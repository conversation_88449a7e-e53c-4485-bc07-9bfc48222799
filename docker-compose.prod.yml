# ================================
# OmniLink 生产环境 Docker Compose
# 版本: 2.0 - 生产安全配置
# 创建日期: 2025-01-07
# ================================

services:
  # PostgreSQL 数据库
  postgres-db:
    image: postgres:16
    container_name: omnilink-postgres-prod
    environment:
      - POSTGRES_DB=omnilink_main
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-bro2fhz12}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/schemas:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - omnilink-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
    # 生产环境内存限制
    mem_limit: 256m
    memswap_limit: 256m
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp
      - /var/run/postgresql

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: omnilink-redis-prod
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-bro2fhz12} --maxmemory 64mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - omnilink-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-bro2fhz12}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    # 生产环境内存限制
    mem_limit: 128m
    memswap_limit: 128m
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp

  # 生产主服务器（安全镜像）
  main-server:
    build:
      context: ./main_server/backend
      dockerfile: Dockerfile
      target: production
    container_name: omnilink-main-server-prod
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:${POSTGRES_PASSWORD:-bro2fhz12}@postgres-db:5432/omnilink_main
      - REDIS_URL=redis://:${REDIS_PASSWORD:-bro2fhz12}@redis:6379
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-omnilink-jwt-secret-key-2024}
      - JWT_ALGORITHM=HS256
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
      - DEBUG=False
      - LOG_LEVEL=WARNING
      - ENVIRONMENT=production
    volumes:
      # 生产环境只挂载日志目录到外部路径
      - ./logs:/app/logs_external:rw
    depends_on:
      postgres-db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - omnilink-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    # 生产环境内存限制
    mem_limit: 512m
    memswap_limit: 512m
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/logs

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  omnilink-network:
    driver: bridge
