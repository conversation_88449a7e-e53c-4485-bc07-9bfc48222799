@echo off
REM ================================
REM OmniLink 单容器集成构建脚本
REM 版本: 1.0
REM 创建日期: 2025-01-10
REM ================================

echo ================================
echo OmniLink 单容器集成构建脚本
echo ================================

REM 检查Docker是否运行
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Docker未运行或未安装，请先启动Docker
    pause
    exit /b 1
)

echo [信息] Docker检查通过

REM 停止并清理现有容器
echo [信息] 停止现有容器...
docker-compose -f docker-compose.single.yml down -v

REM 清理旧镜像（保留基础镜像）
echo [信息] 清理旧的应用镜像...
for /f "tokens=3" %%i in ('docker images --filter "reference=sever*" --format "table {{.Repository}}:{{.Tag}}"') do (
    if not "%%i"=="python:3.11-slim" (
        docker rmi %%i 2>nul
    )
)

REM 拉取必要的基础镜像
echo [信息] 拉取基础镜像...
docker pull python:3.11-slim

REM 构建前端（如果存在）
if exist "main_server\frontend" (
    echo [信息] 构建前端...
    call build-frontend.bat
    if %errorlevel% neq 0 (
        echo [错误] 前端构建失败
        pause
        exit /b 1
    )
)

REM 构建单容器集成镜像
echo [信息] 构建单容器集成镜像...
docker-compose -f docker-compose.single.yml build --no-cache
if %errorlevel% neq 0 (
    echo [错误] 镜像构建失败
    pause
    exit /b 1
)

REM 启动服务
echo [信息] 启动集成服务...
docker-compose -f docker-compose.single.yml up -d
if %errorlevel% neq 0 (
    echo [错误] 服务启动失败
    pause
    exit /b 1
)

REM 等待服务启动
echo [信息] 等待服务启动...
timeout /t 30 /nobreak >nul

REM 检查服务状态
echo [信息] 检查服务状态...
docker-compose -f docker-compose.single.yml ps

REM 显示镜像大小
echo [信息] 镜像大小信息:
docker images --filter "reference=sever*" --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}"

echo ================================
echo 构建完成！
echo ================================
echo Web界面: http://localhost:8000 (对外访问)
echo PostgreSQL: 仅容器内部通信 (安全模式)
echo Redis: 仅容器内部通信 (安全模式)
echo ================================
echo 安全特性: 数据库服务不对外暴露端口
echo ================================
echo 查看日志: docker-compose -f docker-compose.single.yml logs -f
echo 停止服务: docker-compose -f docker-compose.single.yml down
echo ================================

pause
