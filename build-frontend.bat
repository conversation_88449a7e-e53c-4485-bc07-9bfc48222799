@echo off
REM ================================
REM OmniLink 前端构建脚本 (Windows)
REM 版本: 2.0
REM 创建日期: 2025-01-07
REM ================================

echo 🚀 开始构建 OmniLink 前端...

REM 检查Node.js环境
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    exit /b 1
)

where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm 未安装，请先安装 npm
    exit /b 1
)

REM 进入前端目录
cd main_server\frontend

echo 📦 安装前端依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    exit /b 1
)

echo 🔨 构建前端项目...
npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    exit /b 1
)

REM 创建静态文件目录
echo 📁 创建静态文件目录...
if not exist "..\backend\static" mkdir "..\backend\static"

REM 复制构建文件到后端静态目录
echo 📋 复制构建文件...
xcopy /E /Y "dist\*" "..\backend\static\"
if %errorlevel% neq 0 (
    echo ❌ 文件复制失败
    exit /b 1
)

echo ✅ 前端构建完成！
echo 📍 静态文件已复制到: main_server\backend\static\

REM 返回根目录
cd ..\..

echo 🎉 前端构建流程完成！
pause
