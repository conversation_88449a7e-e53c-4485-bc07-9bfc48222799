@echo off
REM ================================
REM OmniLink 单容器快速运行脚本
REM 版本: 1.0
REM 创建日期: 2025-01-10
REM ================================

echo ================================
echo OmniLink 单容器快速运行
echo ================================

REM 检查Docker是否运行
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Docker未运行或未安装，请先启动Docker
    pause
    exit /b 1
)

REM 检查镜像是否存在
docker images sever_omnilink-integrated >nul 2>&1
if %errorlevel% neq 0 (
    echo [信息] 镜像不存在，开始构建...
    call build-single-container.bat
    exit /b %errorlevel%
)

echo [信息] 启动现有服务...
docker-compose -f docker-compose.single.yml up -d

echo [信息] 等待服务启动...
timeout /t 15 /nobreak >nul

echo [信息] 服务状态:
docker-compose -f docker-compose.single.yml ps

echo ================================
echo 服务已启动！
echo ================================
echo Web界面: http://localhost:8000
echo 查看日志: docker-compose -f docker-compose.single.yml logs -f
echo 停止服务: docker-compose -f docker-compose.single.yml down
echo ================================

pause
