-- OmniLink权限系统一次性彻底修复SQL脚本
-- 版本: 1.0
-- 创建日期: 2025-01-12
-- 描述: 修复权限表数据缺失问题，确保所有权限级别用户都能正常使用系统

-- 清空现有权限数据
DELETE FROM permissions;

-- 重置序列
ALTER SEQUENCE permissions_id_seq RESTART WITH 1;

-- 插入完整的权限配置数据
INSERT INTO permissions (name, code, description, category, is_system_permission, is_active, min_required_level, created_at, updated_at) VALUES

-- 基础权限
('查看个人资料', 'profile.view', '查看和编辑个人资料信息', 'basic', true, true, 4, NOW(), NOW()),

-- 申请相关权限
('查看申请', 'application.view', '查看申请列表和详情', 'application', true, true, 3, NOW(), NOW()),
('提交申请', 'application.submit', '提交新的申请', 'application', true, true, 3, NOW(), NOW()),
('处理申请', 'application.process', '审批和处理申请', 'application', true, true, 2, NOW(), NOW()),

-- 用户管理权限
('查看用户', 'user.view', '查看用户列表和信息', 'user_management', true, true, 2, NOW(), NOW()),
('创建用户', 'user.create', '创建新用户', 'user_management', true, true, 2, NOW(), NOW()),
('编辑用户', 'user.edit', '编辑用户信息', 'user_management', true, true, 2, NOW(), NOW()),
('删除用户', 'user.delete', '删除用户', 'user_management', true, true, 1, NOW(), NOW()),

-- 组织管理权限
('查看组织', 'organization.view', '查看组织架构', 'organization_management', true, true, 2, NOW(), NOW()),
('管理组织', 'organization.manage', '创建、编辑、删除组织', 'organization_management', true, true, 1, NOW(), NOW()),

-- 角色管理权限
('查看角色', 'role.view', '查看角色列表', 'role_management', true, true, 1, NOW(), NOW()),
('管理角色', 'role.manage', '创建、编辑、删除角色', 'role_management', true, true, 0, NOW(), NOW()),

-- 设备管理权限
('查看设备', 'device.view', '查看设备列表', 'device_management', true, true, 1, NOW(), NOW()),
('管理设备', 'device.manage', '管理设备配置', 'device_management', true, true, 1, NOW(), NOW()),

-- 系统配置权限
('系统配置', 'system.config', '系统配置管理', 'system', true, true, 0, NOW(), NOW()),

-- 审计日志权限
('查看审计日志', 'audit.view', '查看系统审计日志', 'audit', true, true, 1, NOW(), NOW()),

-- Dashboard相关权限
('查看工作台', 'dashboard.view', '查看工作台统计信息', 'dashboard', true, true, 3, NOW(), NOW()),
('查看统计数据', 'stats.view', '查看各类统计数据', 'dashboard', true, true, 2, NOW(), NOW());

-- 验证插入结果
SELECT 
    '权限插入完成' as status,
    COUNT(*) as total_permissions,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_permissions
FROM permissions;

-- 显示权限级别分布
SELECT 
    min_required_level as permission_level,
    CASE min_required_level
        WHEN 0 THEN '全域管理员'
        WHEN 1 THEN '超级管理员'
        WHEN 2 THEN '管理员'
        WHEN 3 THEN '普通用户'
        WHEN 4 THEN '新用户'
        ELSE '未知'
    END as level_name,
    COUNT(*) as permission_count
FROM permissions 
WHERE is_active = true
GROUP BY min_required_level 
ORDER BY min_required_level;

-- 显示所有权限
SELECT 
    id,
    name,
    code,
    category,
    min_required_level,
    CASE min_required_level
        WHEN 0 THEN '全域管理员'
        WHEN 1 THEN '超级管理员'
        WHEN 2 THEN '管理员'
        WHEN 3 THEN '普通用户'
        WHEN 4 THEN '新用户'
        ELSE '未知'
    END as required_level_name
FROM permissions 
WHERE is_active = true
ORDER BY min_required_level, category, name;
