-- 为ApplicationRequest表添加转交功能相关字段
-- 版本: 1.0
-- 创建日期: 2025-01-12

-- 添加转交相关字段
ALTER TABLE application_requests 
ADD COLUMN IF NOT EXISTS forwarded_from_id INTEGER REFERENCES users(id),
ADD COLUMN IF NOT EXISTS forwarded_to_id INTEGER REFERENCES users(id),
ADD COLUMN IF NOT EXISTS forwarded_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS forward_comment TEXT,
ADD COLUMN IF NOT EXISTS is_forwarded BOOLEAN DEFAULT FALSE;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_application_requests_forwarded_from ON application_requests(forwarded_from_id);
CREATE INDEX IF NOT EXISTS idx_application_requests_forwarded_to ON application_requests(forwarded_to_id);
CREATE INDEX IF NOT EXISTS idx_application_requests_is_forwarded ON application_requests(is_forwarded);

-- 验证字段添加
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'application_requests' 
    AND column_name IN ('forwarded_from_id', 'forwarded_to_id', 'forwarded_at', 'forward_comment', 'is_forwarded')
ORDER BY column_name;
