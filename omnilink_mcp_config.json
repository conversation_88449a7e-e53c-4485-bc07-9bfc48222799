{"mcpServers": {"OmniLink-MongoDB": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-mongodb"], "env": {"MONGODB_URI": "mongodb://localhost:27017/omnilink_analysis"}}, "OmniLink-PostgreSQL": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"POSTGRES_CONNECTION_STRING": "postgresql://postgres:bro2fhz12@localhost:5432/omnilink_main"}}, "OmniLink-SQLite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite", "E:/key/sever/slave_server/data/slave_local.db"]}}}