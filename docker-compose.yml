# ================================
# OmniLink 多容器架构 Docker Compose
# 版本: 3.0 - 多容器分离架构
# 创建日期: 2025-01-13
# ================================

services:
  # PostgreSQL 数据库 (Alpine版本 - 优化镜像大小)
  postgres-db:
    image: postgres:16-alpine
    container_name: omnilink-postgres-1
    environment:
      - POSTGRES_DB=omnilink_main
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=bro2fhz12
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
      - POSTGRES_HOST_AUTH_METHOD=trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./sql/schemas:/docker-entrypoint-initdb.d/01-schemas:ro
      - ./sql/docker_init_complete.sql:/docker-entrypoint-initdb.d/02-init-permissions.sql:ro
      - ./sql/init_permission_levels.sql:/docker-entrypoint-initdb.d/03-permission-levels.sql:ro
      - ./sql/init_permission_data.sql:/docker-entrypoint-initdb.d/04-permission-data.sql:ro
      - ./sql/init_test_users.sql:/docker-entrypoint-initdb.d/05-test-users.sql:ro
      - ./sql/incremental_data_repair.sql:/docker-entrypoint-initdb.d/06-data-repair.sql:ro
    command: ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf", "-c", "hba_file=/etc/postgresql/pg_hba.conf"]
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - omnilink-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    # 内存限制（1GB总内存下的合理分配）
    mem_limit: 256m
    memswap_limit: 256m

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: omnilink-redis-1
    command: redis-server --appendonly yes --requirepass bro2fhz12 --maxmemory 64mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - omnilink-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "bro2fhz12", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    # 内存限制
    mem_limit: 128m
    memswap_limit: 128m

  # 主服务器（集成内网穿透功能）
  main-server:
    build:
      context: ./main_server/backend
      dockerfile: Dockerfile.dev  # 开发模式使用热更新
    container_name: omnilink-main-server-1
    ports:
      - "8000:8000"  # 主服务器API和Web界面
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:bro2fhz12@postgres-db:5432/omnilink_main
      - REDIS_URL=redis://:bro2fhz12@redis:6379
      - JWT_SECRET_KEY=omnilink-jwt-secret-key-2024
      - JWT_ALGORITHM=HS256
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
      - DEBUG=True
      - LOG_LEVEL=INFO
      - ENVIRONMENT=development
      - TUNNEL_ENABLED=true
    volumes:
      # 热更新：挂载本地代码到容器
      - ./main_server/backend:/app:rw
      - ./main_server/logs:/app/logs:rw
      - tunnel_configs:/app/tunnel_configs:rw
    depends_on:
      postgres-db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - omnilink-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    # 内存限制
    mem_limit: 512m
    memswap_limit: 512m

  # 从服务器
  slave-server:
    build:
      context: ./slave_server
      dockerfile: Dockerfile
    container_name: omnilink-slave-server-1
    hostname: slave-server
    ports:
      - "8889:8889"  # 从服务器API接口
      - "7575:7575"  # VirtualHere服务端口
    environment:
      - MASTER_SERVER_URL=http://main-server:8000
      - API_HOST=0.0.0.0
      - API_PORT=8889
      - VH_SERVER_PORT=7575
      - SLAVE_SERVER_ID=slave-001
      - LOG_LEVEL=INFO
      - TZ=Asia/Shanghai
    volumes:
      - ./slave_server/data:/app/data:rw
      - ./slave_server/logs:/app/logs:rw
      - ./slave_server/config:/app/config:rw
      - /dev/bus/usb:/dev/bus/usb:rw  # USB设备访问
    devices:
      - /dev/bus/usb:/dev/bus/usb
    privileged: true  # USB设备访问需要特权模式
    depends_on:
      main-server:
        condition: service_healthy
    networks:
      - omnilink-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8889/api/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    # 内存限制 - 轻量化配置
    mem_limit: 256m
    memswap_limit: 256m



volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  tunnel_configs:
    driver: local

networks:
  omnilink-network:
    driver: bridge
