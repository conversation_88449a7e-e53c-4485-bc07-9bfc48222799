#!/bin/bash
# OmniLink从服务器 v2.0 启动脚本
# 支持开发模式和生产模式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    log_info "Python版本: $python_version"
}

# 检查依赖
check_dependencies() {
    log_info "检查Python依赖..."
    
    if [ -f "requirements_minimal.txt" ]; then
        pip3 install -r requirements_minimal.txt --quiet
        log_info "依赖检查完成"
    else
        log_warn "未找到requirements_minimal.txt文件"
    fi
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p logs
    mkdir -p data
    mkdir -p backups
    mkdir -p temp
    mkdir -p virtualhere/config_templates
    
    log_info "目录创建完成"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f "config/slave_server.ini" ]; then
        log_warn "配置文件不存在，创建默认配置..."
        
        mkdir -p config
        cat > config/slave_server.ini << EOF
[server]
host = 0.0.0.0
port = 8889

[virtualhere]
server_port = 7575
binary_path = /app/vhusbd

[logging]
log_level = INFO
log_file = logs/slave_server.log
EOF
        log_info "默认配置文件已创建"
    fi
}

# 检查VirtualHere二进制文件
check_virtualhere() {
    log_info "检查VirtualHere二进制文件..."
    
    vh_binary="/app/vhusbd"
    if [ ! -f "$vh_binary" ]; then
        log_warn "VirtualHere二进制文件不存在，尝试下载..."
        
        # 检测架构
        arch=$(uname -m)
        if [[ "$arch" == "armv7l" || "$arch" == "aarch64" ]]; then
            if [ -f "virtualhere/vhusbdarmpi3" ]; then
                cp virtualhere/vhusbdarmpi3 "$vh_binary"
                log_info "使用ARM版本VirtualHere"
            else
                log_error "ARM版本VirtualHere文件不存在"
                exit 1
            fi
        else
            # 下载x86_64版本
            wget -O "$vh_binary" https://www.virtualhere.com/sites/default/files/usbserver/vhusbdx86_64
            log_info "已下载x86_64版本VirtualHere"
        fi
        
        chmod +x "$vh_binary"
    fi
    
    log_info "VirtualHere二进制文件检查完成"
}

# 启动服务器
start_server() {
    log_info "启动OmniLink从服务器 v2.0..."
    log_info "=========================================="
    log_info "🚀 极简轻量化架构"
    log_info "🔌 事件驱动USB管理"
    log_info "🌐 aiohttp高性能服务器"
    log_info "📡 智能主从通信"
    log_info "⚙️ 远程管理功能"
    log_info "=========================================="
    
    # 设置环境变量
    export PYTHONPATH="$(pwd):$(pwd)/core"
    export PYTHONUNBUFFERED=1
    
    # 启动主程序
    if [ "$1" = "dev" ]; then
        log_info "开发模式启动..."
        python3 main_minimal.py
    else
        log_info "生产模式启动..."
        python3 main_minimal.py
    fi
}

# 主函数
main() {
    log_info "OmniLink从服务器 v2.0 启动脚本"
    log_info "======================================"
    
    # 检查运行环境
    check_python
    create_directories
    check_config
    check_dependencies
    check_virtualhere
    
    # 启动服务器
    start_server "$1"
}

# 信号处理
trap 'log_info "收到中断信号，正在关闭..."; exit 0' INT TERM

# 执行主函数
main "$@"
