# -*- coding: utf-8 -*-
"""
心跳管理任务
负责与主服务器保持心跳连接
"""

import threading
from typing import Dict, List, Any, Optional
import time
from datetime import datetime
from utils.logger import get_logger
from utils.master_communication import MasterCommunication
from db import ConfigDAO

logger = get_logger('heartbeat')


class HeartbeatManager:
    """心跳管理器"""
    
    def __init__(self, interval: int = 30) -> None:
        """
        初始化心跳管理器

        Args:
            interval: 心跳间隔（秒）
        """
        self.interval = interval
        self.running = False
        self.thread = None
        self.master_comm = MasterCommunication()
    
    def start(self) -> None:
        """启动心跳管理"""
        if self.running:
            logger.warning("心跳管理已在运行中")
            return

        # 从配置加载心跳间隔
        self.interval = ConfigDAO.get_heartbeat_interval()

        self.running = True
        self.thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        self.thread.start()
        logger.info(f"心跳管理已启动，间隔: {self.interval}秒")
    
    def stop(self) -> None:
        """停止心跳管理"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        logger.info("心跳管理已停止")
    
    def register_to_master(self) -> bool:
        """
        向主服务器注册

        Returns:
            bool: 注册是否成功
        """
        return self.master_comm.register_to_master()
    
    def _heartbeat_loop(self) -> None:
        """心跳循环"""
        while self.running:
            try:
                self._send_heartbeat()
                time.sleep(self.interval)
            except Exception as e:
                logger.error(f"心跳发送异常: {e}")
                time.sleep(self.interval)
    
    def _send_heartbeat(self) -> None:
        """发送心跳"""
        try:
            success = self.master_comm.send_heartbeat()
            if not success:
                logger.warning("心跳发送失败")
        except Exception as e:
            logger.error(f"心跳发送异常: {e}")
    
    def is_connected(self) -> None:
        """
        检查是否与主服务器连接

        Returns:
            bool: 连接状态
        """
        return self.master_comm.is_connected()

    def set_master_server_url(self, url) -> Any:
        """
        设置主服务器URL

        Args:
            url: 主服务器URL
        """
        self.master_comm.set_master_server_url(url)
