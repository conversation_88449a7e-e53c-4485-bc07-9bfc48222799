# -*- coding: utf-8 -*-
"""
命令处理器
负责从主服务器获取并执行命令
"""

import threading
from typing import Dict, List, Any, Optional
import time
import json
from datetime import datetime
from utils.logger import get_logger
from utils.master_communication import MasterCommunication
from utils.device_controller import DeviceController
from utils.vh_server_manager import VirtualHereServerManager
from utils.config_manager import ConfigManager
from db import Config<PERSON><PERSON>, CommandDAO, DeviceDAO

logger = get_logger('command_processor')


class CommandProcessor:
    """命令处理器"""
    
    def __init__(self, poll_interval=5) -> Any:
        """
        初始化命令处理器

        Args:
            poll_interval: 轮询间隔（秒）
        """
        self.poll_interval = poll_interval
        self.running = False
        self.thread = None
        self.master_comm = MasterCommunication()

        # 初始化组件
        self.device_controller = DeviceController()
        self.vh_server_manager = VirtualHereServerManager()
        self.config_manager = ConfigManager()
        self.device_dao = DeviceDAO()

        # 支持的命令类型
        self.command_handlers = {
            'device_connect': self._handle_device_connect,
            'device_disconnect': self._handle_device_disconnect,
            'device_reset': self._handle_device_reset,
            'device_rename': self._handle_device_rename,
            'device_set_visibility': self._handle_device_set_visibility,
            'device_set_enabled': self._handle_device_set_enabled,
            'server_restart': self._handle_server_restart,
            'server_start': self._handle_server_start,
            'server_stop': self._handle_server_stop,
            'config_update': self._handle_config_update,
            'system_reboot': self._handle_system_reboot,
            'get_device_list': self._handle_get_device_list,
            'get_system_status': self._handle_get_system_status,
            'force_sync': self._handle_force_sync
        }
    
    def start(self) -> None:
        """启动命令处理器"""
        if self.running:
            logger.warning("命令处理器已在运行中")
            return

        # 从配置加载轮询间隔
        self.poll_interval = ConfigDAO.get_command_poll_interval()

        self.running = True
        self.thread = threading.Thread(target=self._command_loop, daemon=True)
        self.thread.start()
        logger.info(f"命令处理器已启动，轮询间隔: {self.poll_interval}秒")
    
    def stop(self) -> None:
        """停止命令处理器"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        logger.info("命令处理器已停止")
    
    def _fetch_and_execute_commands(self) -> None:
        """获取并执行命令"""
        try:
            # 获取命令
            command = self.master_comm.fetch_commands()
            if command:
                logger.info(f"收到命令: {command.get('command_type')}")

                # 记录命令到数据库
                command_log = CommandDAO.create_command_log(
                    command['command_id'],
                    command['command_type'],
                    command.get('command_params', {})
                )

                # 执行命令
                result = self._execute_command(command)

                # 上报执行结果
                self.master_comm.report_command_result(command['command_id'], result)

                # 更新数据库记录
                if result.get('status'):
                    CommandDAO.mark_command_success(command['command_id'], result.get('result', ''))
                else:
                    CommandDAO.mark_command_failed(command['command_id'], result.get('error', ''))

        except Exception as e:
            logger.error(f"获取或执行命令失败: {e}")
    
    def _command_loop(self) -> None:
        """命令轮询循环"""
        while self.running:
            try:
                self._fetch_and_execute_commands()
                time.sleep(self.poll_interval)
            except Exception as e:
                logger.error(f"命令处理异常: {e}")
                time.sleep(self.poll_interval)
    
    def _fetch_and_execute_commands(self) -> None:
        """获取并执行命令"""
        try:
            if not self.master_comm.is_connected():
                logger.debug("未连接到主服务器，跳过命令轮询")
                return
            
            # 获取命令
            command = self._fetch_command()
            if command:
                logger.info(f"收到命令: {command}")
                
                # 执行命令
                result = self._execute_command(command)
                
                # 上报执行结果
                self._report_command_result(command['command_id'], result)
                
        except Exception as e:
            logger.error(f"获取或执行命令失败: {e}")
    
    def _fetch_command(self) -> None:
        """
        从主服务器获取命令
        
        Returns:
            dict: 命令信息，如果没有命令则返回None
        """
        try:
            headers = {'Authorization': f'Bearer {self.auth_token}'}
            url = f"{self.master_server_url}/api/slave/command"

            # 使用主从通信管理器的重试机制
            def _do_fetch() -> None:
                return requests.get(url, headers=headers, timeout=5)

            response = self.master_comm._execute_with_retry(_do_fetch)

            if response and response.status_code == 200:
                result = response.json()
                if result.get('status') and 'data' in result:
                    return result['data']
            elif response and response.status_code == 204:
                # 没有待执行的命令
                return None
            elif response:
                logger.warning(f"获取命令失败: HTTP {response.status_code}")
            else:
                logger.warning("获取命令失败: 无响应（可能触发了熔断器）")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"获取命令网络请求失败: {e}")
        except Exception as e:
            logger.error(f"获取命令异常: {e}")
        
        return None
    
    def _execute_command(self, command) -> Any:
        """
        执行命令

        Args:
            command: 命令信息

        Returns:
            dict: 执行结果
        """
        try:
            command_type = command.get('command_type')
            command_params = command.get('command_params', {})

            logger.info(f"执行命令: {command_type}")

            # 使用命令处理器映射
            if command_type in self.command_handlers:
                handler = self.command_handlers[command_type]
                return handler(command_params)
            else:
                return {
                    'status': False,
                    'error': f'未知命令类型: {command_type}',
                    'error': 'UNKNOWN_COMMAND_TYPE'
                }
                
        except Exception as e:
            logger.error(f"执行命令异常: {e}")
            return {
                'status': False,
                'result': f'命令执行异常: {str(e)}',
                'error': 'EXECUTION_ERROR'
            }
    
    def _handle_device_connect(self, params) -> Any:
        """处理设备连接命令"""
        # TODO: 实现设备连接逻辑
        device_id = params.get('device_id')
        logger.info(f"连接设备: {device_id}")
        
        return {
            'status': True,
            'result': f'设备 {device_id} 连接成功',
            'error': None
        }
    
    def _handle_device_disconnect(self, params) -> Any:
        """处理设备断开命令"""
        # TODO: 实现设备断开逻辑
        device_id = params.get('device_id')
        logger.info(f"断开设备: {device_id}")
        
        return {
            'status': True,
            'result': f'设备 {device_id} 断开成功',
            'error': None
        }
    
    def _handle_server_restart(self, params) -> Any:
        """处理服务器重启命令"""
        # TODO: 实现服务器重启逻辑
        logger.info("重启服务器")
        
        return {
            'status': True,
            'result': '服务器重启命令已接收',
            'error': None
        }
    
    def _handle_config_update(self, params) -> Any:
        """处理配置更新命令"""
        # TODO: 实现配置更新逻辑
        config_data = params.get('config_data', {})
        logger.info(f"更新配置: {config_data}")
        
        return {
            'status': True,
            'result': '配置更新成功',
            'error': None
        }
    
    def _report_command_result(self, command_id, result) -> Any:
        """
        上报命令执行结果
        
        Args:
            command_id: 命令ID
            result: 执行结果
        """
        try:
            headers = {
                'Authorization': f'Bearer {self.auth_token}',
                'Content-Type': 'application/json'
            }
            url = f"{self.master_server_url}/api/slave/command/{command_id}/result"
            
            response = requests.post(url, json=result, headers=headers, timeout=5)
            
            if response.status_code == 200:
                logger.debug(f"命令结果上报成功: {command_id}")
            else:
                logger.error(f"命令结果上报失败: HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"上报命令结果异常: {e}")
    
    def set_auth_token(self, token) -> Any:
        """
        设置认证令牌
        
        Args:
            token: 认证令牌
        """
        self.auth_token = token
        logger.info("命令处理器认证令牌已更新")

    # 设备控制命令处理器
    def _handle_device_connect(self, params) -> Any:
        """处理设备连接命令"""
        try:
            device_uuid = params.get('device_uuid')
            user_info = params.get('user_info')

            if not device_uuid:
                return {'status': False, 'error': '缺少device_uuid参数'}

            result = self.device_controller.connect_device(device_uuid, user_info)
            return {
                'status': result['success'],
                'result': result.get('message', ''),
                'error': result.get('error', '') if not result['success'] else None
            }
        except Exception as e:
            logger.error(f"处理设备连接命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def _handle_device_disconnect(self, params) -> Any:
        """处理设备断开命令"""
        try:
            device_uuid = params.get('device_uuid')
            user_info = params.get('user_info')

            if not device_uuid:
                return {'status': False, 'error': '缺少device_uuid参数'}

            result = self.device_controller.disconnect_device(device_uuid, user_info)
            return {
                'status': result['success'],
                'result': result.get('message', ''),
                'error': result.get('error', '') if not result['success'] else None
            }
        except Exception as e:
            logger.error(f"处理设备断开命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def _handle_device_reset(self, params) -> Any:
        """处理设备重置命令"""
        try:
            device_uuid = params.get('device_uuid')

            if not device_uuid:
                return {'status': False, 'error': '缺少device_uuid参数'}

            result = self.device_controller.reset_device(device_uuid)
            return {
                'status': result['success'],
                'result': result.get('message', ''),
                'error': result.get('error', '') if not result['success'] else None
            }
        except Exception as e:
            logger.error(f"处理设备重置命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def _handle_device_rename(self, params) -> Any:
        """处理设备重命名命令"""
        try:
            device_uuid = params.get('device_uuid')
            new_name = params.get('new_name')

            if not device_uuid or not new_name:
                return {'status': False, 'error': '缺少device_uuid或new_name参数'}

            result = self.device_controller.rename_device(device_uuid, new_name)
            return {
                'status': result['success'],
                'result': result.get('message', ''),
                'error': result.get('error', '') if not result['success'] else None
            }
        except Exception as e:
            logger.error(f"处理设备重命名命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def _handle_device_set_visibility(self, params) -> Any:
        """处理设备可见性设置命令"""
        try:
            device_uuid = params.get('device_uuid')
            shown = params.get('shown')

            if not device_uuid or shown is None:
                return {'status': False, 'error': '缺少device_uuid或shown参数'}

            success = self.device_dao.set_device_visibility(device_uuid, bool(shown))
            return {
                'status': success,
                'result': f'设备可见性设置为: {shown}' if success else '',
                'error': '设备不存在或设置失败' if not success else None
            }
        except Exception as e:
            logger.error(f"处理设备可见性设置命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def _handle_device_set_enabled(self, params) -> Any:
        """处理设备启用状态设置命令"""
        try:
            device_uuid = params.get('device_uuid')
            enabled = params.get('enabled')

            if not device_uuid or enabled is None:
                return {'status': False, 'error': '缺少device_uuid或enabled参数'}

            success = self.device_dao.set_device_enabled(device_uuid, bool(enabled))
            return {
                'status': success,
                'result': f'设备启用状态设置为: {enabled}' if success else '',
                'error': '设备不存在或设置失败' if not success else None
            }
        except Exception as e:
            logger.error(f"处理设备启用状态设置命令失败: {e}")
            return {'status': False, 'error': str(e)}

    # 服务器管理命令处理器
    def _handle_server_restart(self, params) -> Any:
        """处理服务器重启命令"""
        try:
            success = self.vh_server_manager.restart_server()
            return {
                'status': success,
                'result': 'VirtualHere服务器重启成功' if success else '',
                'error': 'VirtualHere服务器重启失败' if not success else None
            }
        except Exception as e:
            logger.error(f"处理服务器重启命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def _handle_server_start(self, params) -> Any:
        """处理服务器启动命令"""
        try:
            success = self.vh_server_manager.start_server()
            return {
                'status': success,
                'result': 'VirtualHere服务器启动成功' if success else '',
                'error': 'VirtualHere服务器启动失败' if not success else None
            }
        except Exception as e:
            logger.error(f"处理服务器启动命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def _handle_server_stop(self, params) -> Any:
        """处理服务器停止命令"""
        try:
            success = self.vh_server_manager.stop_server()
            return {
                'status': success,
                'result': 'VirtualHere服务器停止成功' if success else '',
                'error': 'VirtualHere服务器停止失败' if not success else None
            }
        except Exception as e:
            logger.error(f"处理服务器停止命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def _handle_config_update(self, params) -> Any:
        """处理配置更新命令"""
        try:
            config_type = params.get('config_type', 'system')
            config_data = params.get('config_data', {})

            if config_type == 'system':
                # 更新系统配置
                for section, values in config_data.items():
                    for key, value in values.items():
                        self.config_manager.set_value(section, key, str(value))

                success = self.config_manager.save_config()
                return {
                    'status': success,
                    'result': '系统配置更新成功' if success else '',
                    'error': '系统配置更新失败' if not success else None
                }

            elif config_type == 'virtualhere':
                # 更新VirtualHere配置
                success = self.vh_server_manager.configure_server(config_data)
                return {
                    'status': success,
                    'result': 'VirtualHere配置更新成功' if success else '',
                    'error': 'VirtualHere配置更新失败' if not success else None
                }

            else:
                return {'status': False, 'error': f'不支持的配置类型: {config_type}'}

        except Exception as e:
            logger.error(f"处理配置更新命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def _handle_system_reboot(self, params) -> Any:
        """处理系统重启命令"""
        try:
            import os

            # 记录重启请求
            logger.warning("收到系统重启命令，将在5秒后重启系统")

            # 延迟重启，给响应时间
            import threading
            def delayed_reboot() -> None:
                time.sleep(5)
                os.system('sudo reboot')

            threading.Thread(target=delayed_reboot, daemon=True).start()

            return {
                'status': True,
                'result': '系统将在5秒后重启'
            }
        except Exception as e:
            logger.error(f"处理系统重启命令失败: {e}")
            return {'status': False, 'error': str(e)}

    # 查询命令处理器
    def _handle_get_device_list(self, params) -> Any:
        """处理获取设备列表命令"""
        try:
            # 获取查询参数
            status_filter = params.get('status')
            include_hidden = params.get('include_hidden', False)

            if status_filter:
                devices = self.device_dao.get_devices_by_status(status_filter)
            else:
                devices = self.device_dao.get_all_devices()

            # 过滤隐藏设备
            if not include_hidden:
                devices = [device for device in devices if device.shown]

            # 转换为字典格式
            device_list = []
            for device in devices:
                device_dict = {
                    'device_uuid': device.device_uuid,
                    'device_address': device.device_address,
                    'vendor': device.vendor,
                    'product': device.product,
                    'status': device.status,
                    'shown': device.shown,
                    'disable': device.disable,
                    'last_seen': device.last_seen.isoformat() if device.last_seen else None
                }
                device_list.append(device_dict)

            return {
                'status': True,
                'result': {
                    'devices': device_list,
                    'total_count': len(device_list)
                }
            }
        except Exception as e:
            logger.error(f"处理获取设备列表命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def _handle_get_system_status(self, params) -> Any:
        """处理获取系统状态命令"""
        try:
            # 获取VirtualHere服务器状态
            vh_status = self.vh_server_manager.get_server_status()

            # 获取设备统计
            device_stats = self.device_dao.get_device_statistics()

            # 获取系统资源使用情况
            import psutil
            import time

            system_info = {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': round((psutil.disk_usage('/').used / psutil.disk_usage('/').total) * 100, 2),
                'uptime_seconds': int(time.time() - psutil.boot_time())
            }

            return {
                'status': True,
                'result': {
                    'virtualhere_status': vh_status,
                    'device_statistics': device_stats,
                    'system_info': system_info,
                    'timestamp': datetime.now().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"处理获取系统状态命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def _handle_force_sync(self, params) -> Any:
        """处理强制同步命令"""
        try:
            # 强制发送所有待上报数据
            success = self.master_comm.force_send_pending_reports()

            # 获取当前设备状态并上报
            devices = self.device_dao.get_all_devices()
            device_list = []
            for device in devices:
                device_dict = {
                    'device_uuid': device.device_uuid,
                    'device_address': device.device_address,
                    'vendor': device.vendor,
                    'product': device.product,
                    'status': device.status,
                    'shown': device.shown,
                    'disable': device.disable,
                    'last_seen': device.last_seen.isoformat() if device.last_seen else None
                }
                device_list.append(device_dict)

            # 上报完整设备状态
            sync_success = self.master_comm.report_device_states_incremental(device_list)

            return {
                'status': success and sync_success,
                'result': f'强制同步完成，上报了 {len(device_list)} 个设备',
                'error': '同步过程中出现错误' if not (success and sync_success) else None
            }
        except Exception as e:
            logger.error(f"处理强制同步命令失败: {e}")
            return {'status': False, 'error': str(e)}

    def get_supported_commands(self) -> List[str]:
        """
        获取支持的命令类型列表

        Returns:
            List[str]: 支持的命令类型
        """
        return list(self.command_handlers.keys())

    def is_command_supported(self, command_type: str) -> bool:
        """
        检查命令类型是否支持

        Args:
            command_type: 命令类型

        Returns:
            bool: 是否支持
        """
        return command_type in self.command_handlers
