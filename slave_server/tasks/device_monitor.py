# -*- coding: utf-8 -*-
"""
设备监控任务
负责定期扫描和监控USB设备状态
"""

import threading
import time
from datetime import datetime
from typing import List, Dict, Optional, Any, Union, Tuple
from utils.logger import get_logger
from utils.vh_client import VirtualHereClient
from utils.device_scanner import DeviceScanner
from utils.device_event_handler import Device<PERSON>ventHand<PERSON>, DeviceEvent, DeviceEventType
from utils.master_communication import MasterCommunication
from db import DeviceDAO

logger = get_logger('device_monitor')


class DeviceMonitor:
    """设备监控器"""
    
    def __init__(self, scan_interval: int = 3) -> None:
        """
        初始化设备监控器

        Args:
            scan_interval: 扫描间隔（秒）
        """
        self.scan_interval = scan_interval
        self.running = False
        self.thread = None
        self.devices_cache = {}
        self.previous_devices = []
        self.vh_client = VirtualHereClient()
        self.device_scanner = DeviceScanner()
        self.event_handler = DeviceEventHandler()
        self.device_dao = DeviceDAO()
        self.master_comm = MasterCommunication()

        # 注册事件监听器
        self._register_event_listeners()
    
    def start(self) -> None:
        """启动设备监控"""
        if self.running:
            logger.warning("设备监控已在运行中")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        logger.info("设备监控已启动")
    
    def stop(self) -> None:
        """停止设备监控"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        logger.info("设备监控已停止")
    
    def _monitor_loop(self) -> None:
        """监控循环（增强版）"""
        while self.running:
            try:
                # 扫描设备
                current_devices = self.device_scanner.scan_usb_devices()

                # 检测设备变化并生成事件
                self.event_handler.detect_device_changes(current_devices, self.previous_devices)

                # 更新设备信息到数据库
                self._update_devices_to_database(current_devices)

                # 增量上报设备状态到主服务器
                if self.master_comm.is_connected():
                    self.master_comm.report_device_states_incremental(current_devices)

                # 更新缓存
                self.previous_devices = current_devices.copy()
                self.devices_cache = {device['device_uuid']: device for device in current_devices}

                time.sleep(self.scan_interval)
            except Exception as e:
                logger.error(f"设备监控异常: {e}")
                time.sleep(self.scan_interval)
    
    def _scan_devices(self) -> None:
        """扫描设备"""
        try:
            # TODO: 实现实际的设备扫描逻辑
            # 这里应该调用VirtualHere客户端获取设备状态
            current_devices = self._get_virtualhere_devices()
            
            # 检查设备变化
            changes = self._detect_changes(current_devices)
            
            if changes:
                logger.info(f"检测到设备变化: {changes}")
                self._handle_device_changes(changes)
            
            # 更新缓存
            self.devices_cache = current_devices
            
        except Exception as e:
            logger.error(f"扫描设备失败: {e}")
    
    def _get_virtualhere_devices(self) -> None:
        """
        从VirtualHere获取设备列表

        Returns:
            dict: 设备字典
        """
        try:
            # 发送VH命令刷新设备状态
            self.vh_client.send_command("LIST")

            # 获取设备状态
            devices_list = self.vh_client.get_client_state()
            devices_dict = {}

            for device in devices_list:
                device_uuid = device.get('device_uuid')
                if device_uuid:
                    # 添加时间戳
                    device['last_seen'] = datetime.now()
                    devices_dict[device_uuid] = device

            return devices_dict

        except Exception as e:
            logger.error(f"获取VirtualHere设备失败: {e}")
            return {}
    
    def _detect_changes(self, current_devices) -> Any:
        """
        检测设备变化
        
        Args:
            current_devices: 当前设备列表
        
        Returns:
            dict: 变化信息
        """
        changes = {
            'added': [],
            'removed': [],
            'modified': []
        }
        
        # 检查新增设备
        for device_id, device in current_devices.items():
            if device_id not in self.devices_cache:
                changes['added'].append(device)
        
        # 检查移除设备
        for device_id, device in self.devices_cache.items():
            if device_id not in current_devices:
                changes['removed'].append(device)
        
        # 检查修改设备
        for device_id, device in current_devices.items():
            if device_id in self.devices_cache:
                old_device = self.devices_cache[device_id]
                if device != old_device:
                    changes['modified'].append({
                        'old': old_device,
                        'new': device
                    })
        
        return changes
    
    def _handle_device_changes(self, changes) -> Any:
        """
        处理设备变化

        Args:
            changes: 变化信息
        """
        try:
            # 处理新增设备
            for device in changes['added']:
                logger.info(f"新增设备: {device.get('device_uuid', 'unknown')}")
                self._add_device_to_database(device)

            # 处理移除设备
            for device in changes['removed']:
                logger.info(f"移除设备: {device.get('device_uuid', 'unknown')}")
                self._update_device_status(device.get('device_uuid'), 'disconnected')

            # 处理修改设备
            for change in changes['modified']:
                old_device = change['old']
                new_device = change['new']
                device_uuid = new_device.get('device_uuid')
                logger.info(f"设备状态变化: {device_uuid}")
                self._update_device_in_database(new_device)

        except Exception as e:
            logger.error(f"处理设备变化失败: {e}")

    def _add_device_to_database(self, device) -> Any:
        """
        添加设备到数据库

        Args:
            device: 设备信息
        """
        try:
            device_data = {
                'device_uuid': device.get('device_uuid'),
                'device_address': device.get('device_address'),
                'device_serial': device.get('device_serial'),
                'connection_uuid': device.get('connection_uuid'),
                'vendor': device.get('vendor'),
                'vendor_id': device.get('vendor_id'),
                'product': device.get('product'),
                'product_id': device.get('product_id'),
                'status': device.get('status', 'available'),
                'last_seen': device.get('last_seen', datetime.now())
            }

            DeviceDAO.create_device(device_data)

        except Exception as e:
            logger.error(f"添加设备到数据库失败: {e}")

    def _update_device_in_database(self, device) -> Any:
        """
        更新数据库中的设备信息

        Args:
            device: 设备信息
        """
        try:
            device_uuid = device.get('device_uuid')
            update_data = {
                'status': device.get('status', 'available'),
                'last_seen': device.get('last_seen', datetime.now())
            }

            DeviceDAO.update_device(device_uuid, update_data)

        except Exception as e:
            logger.error(f"更新设备数据库失败: {e}")

    def _update_device_status(self, device_uuid, status) -> Any:
        """
        更新设备状态

        Args:
            device_uuid: 设备UUID
            status: 新状态
        """
        try:
            DeviceDAO.update_device_status(device_uuid, status)
        except Exception as e:
            logger.error(f"更新设备状态失败: {e}")
    
    def get_device_count(self) -> None:
        """
        获取当前设备数量

        Returns:
            int: 设备数量
        """
        return len(self.devices_cache)

    def _register_event_listeners(self) -> None:
        """注册事件监听器"""
        try:
            # 注册设备连接事件监听器
            self.event_handler.register_listener(
                DeviceEventType.DEVICE_CONNECTED,
                self._on_device_connected
            )

            # 注册设备断开事件监听器
            self.event_handler.register_listener(
                DeviceEventType.DEVICE_DISCONNECTED,
                self._on_device_disconnected
            )

            # 注册设备状态变化事件监听器
            self.event_handler.register_listener(
                DeviceEventType.DEVICE_STATUS_CHANGED,
                self._on_device_status_changed
            )

            logger.info("设备事件监听器注册完成")

        except Exception as e:
            logger.error(f"注册事件监听器失败: {e}")

    def _on_device_connected(self, event: DeviceEvent) -> Any:
        """设备连接事件处理"""
        try:
            logger.info(f"设备已连接: {event.device_info.get('vendor', '')} {event.device_info.get('product', '')} ({event.device_address})")

            # 可以在这里添加设备连接后的处理逻辑
            # 例如：自动配置、通知主服务器等

        except Exception as e:
            logger.error(f"处理设备连接事件失败: {e}")

    def _on_device_disconnected(self, event: DeviceEvent) -> Any:
        """设备断开事件处理"""
        try:
            logger.info(f"设备已断开: {event.device_address}")

            # 可以在这里添加设备断开后的处理逻辑
            # 例如：清理资源、通知主服务器等

        except Exception as e:
            logger.error(f"处理设备断开事件失败: {e}")

    def _on_device_status_changed(self, event: DeviceEvent) -> Any:
        """设备状态变化事件处理"""
        try:
            logger.info(f"设备状态变化: {event.device_address} {event.old_status} -> {event.new_status}")

            # 可以在这里添加状态变化后的处理逻辑

        except Exception as e:
            logger.error(f"处理设备状态变化事件失败: {e}")

    def _update_devices_to_database(self, devices: List[Dict]) -> Any:
        """更新设备信息到数据库"""
        try:
            for device in devices:
                # 检查设备是否已存在
                existing_device = self.device_dao.get_device_by_uuid(device['device_uuid'])

                if existing_device:
                    # 更新现有设备
                    self.device_dao.update_device(device['device_uuid'], device)
                else:
                    # 添加新设备
                    self.device_dao.add_device(device)

        except Exception as e:
            logger.error(f"更新设备信息到数据库失败: {e}")

    def get_event_handler(self) -> DeviceEventHandler:
        """获取事件处理器"""
        return self.event_handler

    def get_device_scanner(self) -> DeviceScanner:
        """获取设备扫描器"""
        return self.device_scanner

    def get_monitor_statistics(self) -> Dict:
        """获取监控统计信息"""
        try:
            return {
                'running': self.running,
                'scan_interval': self.scan_interval,
                'cached_devices': len(self.devices_cache),
                'event_statistics': self.event_handler.get_statistics(),
                'scanner_statistics': {
                    'device_count': self.device_scanner.get_device_count(),
                    'scan_interval': self.device_scanner.scan_interval
                }
            }
        except Exception as e:
            logger.error(f"获取监控统计信息失败: {e}")
            return {}

    def force_report_to_master(self) -> bool:
        """
        强制上报所有待上报数据到主服务器

        Returns:
            bool: 上报是否成功
        """
        try:
            if self.master_comm.is_connected():
                return self.master_comm.force_send_pending_reports()
            else:
                logger.warning("未连接到主服务器，无法上报数据")
                return False
        except Exception as e:
            logger.error(f"强制上报数据失败: {e}")
            return False

    def get_report_status(self) -> Dict:
        """
        获取上报状态信息

        Returns:
            Dict: 上报状态
        """
        try:
            return {
                'connected_to_master': self.master_comm.is_connected(),
                'pending_report_count': self.master_comm.get_pending_report_count(),
                'batch_size': self.master_comm.batch_size,
                'batch_timeout': self.master_comm.batch_timeout,
                'last_batch_time': self.master_comm.last_batch_time
            }
        except Exception as e:
            logger.error(f"获取上报状态失败: {e}")
            return {}

    def set_batch_config(self, batch_size: int = None, batch_timeout: int = None) -> bool:
        """
        设置批量上报配置

        Args:
            batch_size: 批量大小
            batch_timeout: 批量超时时间（秒）

        Returns:
            bool: 设置是否成功
        """
        try:
            self.master_comm.set_batch_config(batch_size, batch_timeout)
            return True
        except Exception as e:
            logger.error(f"设置批量配置失败: {e}")
            return False

    def clear_pending_reports(self) -> None:
        """清空待上报数据队列"""
        try:
            self.master_comm.clear_pending_reports()
            logger.info("待上报数据队列已清空")
        except Exception as e:
            logger.error(f"清空待上报数据队列失败: {e}")

    def get_master_communication(self) -> MasterCommunication:
        """获取主从通信管理器"""
        return self.master_comm
