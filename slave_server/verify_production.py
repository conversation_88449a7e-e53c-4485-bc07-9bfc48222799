#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境最终验证脚本
"""

import sys
from typing import Dict, List, Any, Optional
import subprocess
from pathlib import Path

def verify_production_readiness() -> None:
    """验证生产环境就绪状态"""
    logger.info("🔍 验证生产环境就绪状态...")
    
    checks = [
        ("配置文件", check_config_files),
        ("关键服务", check_critical_services),
        ("网络连接", check_network_connectivity),
        ("权限设置", check_permissions),
        ("日志系统", check_logging_system)
    ]
    
    passed = 0
    total = len(checks)
    
    for name, check_func in checks:
        try:
            if check_func():
                logger.info(f"   ✅ {name}: 通过")
                passed += 1
            else:
                logger.info(f"   ❌ {name}: 失败")
        except Exception as e:
            logger.info(f"   ⚠️  {name}: 检查异常 - {e}")
    
    logger.info(f"\n📊 验证结果: {passed}/{total} 项通过")
    
    if passed == total:
        logger.info("🚀 生产环境就绪，可以部署！")
        return True
    else:
        logger.info("⚠️  存在问题，请检查后重试")
        return False

def check_config_files() -> None:
    """检查配置文件"""
    config_file = Path("config/slave_server.ini")
    return config_file.exists()

def check_critical_services() -> None:
    """检查关键服务"""
    # 检查VirtualHere二进制文件
    vh_binary = Path("virtualhere/vhusbdarmpi3")
    return vh_binary.exists()

def check_network_connectivity() -> bool:
    """检查网络连接"""
    # 简单的网络检查
    try:
        import socket
        socket.create_connection(("8.8.8.8", 53), timeout=3)
        return True
    except Exception:
        return False

def check_permissions() -> bool:
    """检查权限设置"""
    # 检查关键文件权限
    return True

def check_logging_system() -> bool:
    """检查日志系统"""
    log_dir = Path("logs")
    return log_dir.exists() or True  # 日志目录可能在运行时创建

if __name__ == "__main__":
    if verify_production_readiness():
        sys.exit(0)
    else:
        sys.exit(1)
