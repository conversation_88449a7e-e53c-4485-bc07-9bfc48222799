# Prometheus配置文件
global:
  scrape_interval: 15s
  evaluation_interval: 15s

# 规则文件
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # OmniLink从服务器监控
  - job_name: 'omnilink-slave-server'
    static_configs:
      - targets: ['slave-server:8889']
    metrics_path: '/api/system/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s
    params:
      format: ['prometheus']

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # 系统监控（如果有node_exporter）
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# 远程写入配置（可选）
# remote_write:
#   - url: "http://remote-storage:9201/write"

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9201/read"
