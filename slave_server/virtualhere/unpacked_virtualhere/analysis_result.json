{"sections": [{"name": ".text", "virtual_address": 4096, "virtual_size": 7035750, "raw_address": 1024, "raw_size": 7035904, "characteristics": 1610612768}, {"name": ".rdata", "virtual_address": 7041024, "virtual_size": 2830128, "raw_address": 7036928, "raw_size": 2830336, "characteristics": 1073741888}, {"name": ".data", "virtual_address": 9871360, "virtual_size": 421297, "raw_address": 9867264, "raw_size": 148480, "characteristics": 3221225536}, {"name": ".pdata", "virtual_address": 10293248, "virtual_size": 205764, "raw_address": 10015744, "raw_size": 205824, "characteristics": 1073741888}, {"name": ".tls", "virtual_address": 10502144, "virtual_size": 32, "raw_address": 10221568, "raw_size": 512, "characteristics": 3221225536}, {"name": ".rsrc", "virtual_address": 10506240, "virtual_size": 69216, "raw_address": 10222080, "raw_size": 69632, "characteristics": 1073741888}, {"name": ".reloc", "virtual_address": 10575872, "virtual_size": 180456, "raw_address": 10291712, "raw_size": 180736, "characteristics": 1107296320}], "imports": [{"dll": "KERNEL32.DLL", "functions": ["AcquireSRWLockExclusive", "AreFileApisANSI", "AttachConsole", "CloseHandle", "CopyFileW", "CreateDirectoryW", "CreateEventA", "CreateEventW", "CreateFileA", "CreateFileMappingA"]}, {"dll": "ADVAPI32.dll", "functions": ["AdjustTokenPrivileges", "ChangeServiceConfig2W", "ClearEventLogW", "CloseEventLog", "CloseServiceHandle", "ControlService", "CreateServiceW", "CryptAcquireContextA", "CryptGenRandom", "CryptReleaseContext"]}, {"dll": "api-ms-win-crt-convert-l1-1-0.dll", "functions": ["_strtod_l", "_strtoi64_l", "_strtoui64_l", "_wtoi", "_wtol", "atof", "atoi", "mbrtowc", "mbsrtowcs", "mbstowcs"]}, {"dll": "api-ms-win-crt-environment-l1-1-0.dll", "functions": ["__p__environ", "_wgetcwd", "_wgetenv", "_wputenv", "getenv"]}, {"dll": "api-ms-win-crt-filesystem-l1-1-0.dll", "functions": ["_chdrive", "_findclose", "_findfirst64", "_findnext64", "_fullpath", "_getdrive", "_lock_file", "_unlock_file", "_waccess", "_wchmod"]}, {"dll": "api-ms-win-crt-heap-l1-1-0.dll", "functions": ["_aligned_free", "_aligned_malloc", "_set_new_mode", "calloc", "free", "malloc", "realloc"]}, {"dll": "api-ms-win-crt-locale-l1-1-0.dll", "functions": ["___lc_codepage_func", "___mb_cur_max_func", "__pctype_func", "_configthreadlocale", "_create_locale", "_free_locale", "localeconv", "<PERSON><PERSON><PERSON>"]}, {"dll": "api-ms-win-crt-math-l1-1-0.dll", "functions": ["__set<PERSON><PERSON><PERSON>r", "_fdopen", "acosf", "atan2", "atan2f", "ceil", "ceilf", "cos", "cosf", "exp"]}, {"dll": "api-ms-win-crt-multibyte-l1-1-0.dll", "functions": ["_mbtowc_l"]}, {"dll": "api-ms-win-crt-private-l1-1-0.dll", "functions": ["__C_specific_handler", "__intrinsic_setjmpex", "longjmp", "memchr", "memcmp", "memcpy", "memmove", "strchr", "strrchr", "strstr"]}, {"dll": "api-ms-win-crt-runtime-l1-1-0.dll", "functions": ["__p___argc", "__p___argv", "__sys_nerr", "_assert", "_beginthread", "_beginthreadex", "_cexit", "_configure_narrow_argv", "_crt_atexit", "_endthreadex"]}, {"dll": "api-ms-win-crt-stdio-l1-1-0.dll", "functions": ["__acrt_iob_func", "__p__commode", "__p__fmode", "__stdio_common_vfprintf", "__stdio_common_vfwprintf", "__stdio_common_vsprintf", "__stdio_common_vsscanf", "__stdio_common_vswprintf", "__stdio_common_vswscanf", "_close"]}, {"dll": "api-ms-win-crt-string-l1-1-0.dll", "functions": ["_isctype_l", "_iswlower_l", "_strdup", "_stricmp", "_strlwr", "_wcsdup", "_wcsicmp", "isalnum", "isalpha", "isdigit"]}, {"dll": "api-ms-win-crt-time-l1-1-0.dll", "functions": ["__daylight", "__timezone", "__tzname", "_ctime64", "_difftime64", "_gmtime64", "_gmtime64_s", "_localtime64", "_localtime64_s", "_mktime64"]}, {"dll": "api-ms-win-crt-utility-l1-1-0.dll", "functions": ["bsearch", "qsort", "rand", "rand_s", "srand"]}, {"dll": "COMCTL32.dll", "functions": ["CreateUpDownControl", "ImageList_Add", "ImageList_AddMasked", "ImageList_BeginDrag", "ImageList_Copy", "ImageList_Create", "ImageList_Destroy", "ImageList_DragEnter", "ImageList_DragLeave", "ImageList_DragMove"]}, {"dll": "comdlg32.dll", "functions": ["ChooseColorW", "ChooseFontW", "CommDlgExtendedError", "GetOpenFileNameW", "GetSaveFileNameW", "PageSetupDlgW", "PrintDlgW"]}, {"dll": "CRYPT32.dll", "functions": ["CertAddEncodedCertificateToStore", "CertCloseStore", "CertOpenStore"]}, {"dll": "DNSAPI.dll", "functions": ["DnsServiceConstructInstance", "DnsServiceDeRegister", "DnsServiceFreeInstance", "DnsServiceRegister", "DnsServiceRegisterCancel"]}, {"dll": "GDI32.dll", "functions": ["AddFontResourceExW", "Arc", "BitBlt", "CloseEnhMetaFile", "CombineRgn", "CopyEnhMetaFileW", "CreateBitmap", "CreateBitmapIndirect", "CreateCompatibleBitmap", "CreateCompatibleDC"]}, {"dll": "IPHLPAPI.DLL", "functions": ["GetAdaptersAddresses"]}, {"dll": "ole32.dll", "functions": ["CoCreateInstance", "CoInitializeEx", "CoInitializeSecurity", "CoLockObjectExternal", "CoSetProxyBlanket", "CoTaskMemAlloc", "CoTaskMemFree", "CoUninitialize", "DoDragDrop", "OleFlushClipboard"]}, {"dll": "OLEACC.dll", "functions": ["CreateStdAccessibleObject", "LresultFromObject"]}, {"dll": "OLEAUT32.dll", "functions": ["SafeArrayCreate", "SafeArrayDestroy", "SafeArrayGetDim", "SafeArrayGetLBound", "SafeArrayGetUBound", "SafeArrayGetVartype", "SafeArrayLock", "SafeArrayUnlock", "SysAllocString", "SysFreeString"]}, {"dll": "POWRPROF.dll", "functions": ["PowerGetActiveScheme", "PowerReadACValue", "PowerReadDCValue", "PowerWriteACValueIndex", "PowerWriteDCValueIndex"]}, {"dll": "RPCRT4.dll", "functions": ["RpcStringFreeW", "UuidCreate", "UuidToStringW"]}, {"dll": "SETUPAPI.dll", "functions": ["CM_Get_Child", "CM_Get_DevNode_Status", "CM_Get_Device_IDW", "CM_Get_Device_Interface_ListW", "CM_Get_Parent", "CM_Get_Sibling", "CM_Locate_DevNodeW", "SetupCloseInfFile", "SetupDiBuildDriverInfoList", "SetupDiChangeState"]}, {"dll": "SHELL32.dll", "functions": ["CommandLineToArgvW", "DragAcceptFiles", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DragQueryFileW", "DragQueryPoint", "ExtractIconExW", "ExtractIconW", "SHBrowseForFolderW", "SHChangeNotify", "SHDefExtractIconW"]}, {"dll": "SHLWAPI.dll", "functions": ["AssocQueryStringW", "PathFileExistsW", "PathMatchSpecW", "SHAutoComplete", "StrCmpLogicalW"]}, {"dll": "USER32.dll", "functions": ["AdjustWindowRectEx", "AnimateWindow", "AppendMenuW", "BeginDeferWindowPos", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BringWindowToTop", "CallNextHookEx", "CallWindowProcW", "ChangeDisplaySettingsExW", "CheckMenuItem"]}, {"dll": "UxTheme.dll", "functions": ["CloseThemeData", "DrawThemeBackground", "DrawThemeParentBackground", "GetCurrentThemeName", "GetThemeBackgroundContentRect", "GetThemeBackgroundExtent", "GetThemeColor", "GetThemeFont", "GetThemeInt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"dll": "VERSION.dll", "functions": ["GetFileVersionInfoSizeW", "GetFileVersionInfoW", "VerQueryValueW"]}, {"dll": "WINMM.dll", "functions": ["timeBeginPeriod", "timeEndPeriod", "timeGetTime"]}, {"dll": "WINSPOOL.DRV", "functions": ["ClosePrinter", "DocumentPropertiesW", "GetPrinterW", "OpenPrinterW"]}, {"dll": "WS2_32.dll", "functions": ["WSACleanup", "WSAGetLastError", "WSARecvFrom", "WSASend", "WSASendTo", "WSAStartup", "__WSAFDIsSet", "accept", "bind", "closesocket"]}], "exports": [], "resources": [{"type": 3, "id": 1, "lang": 1033, "size": 67624, "offset": 10506656}, {"type": 11, "id": 1, "lang": 1033, "size": 32, "offset": 10506624}, {"type": 14, "id": null, "lang": 1033, "size": 20, "offset": 10574280}, {"type": 16, "id": 1, "lang": 1033, "size": 764, "offset": 10574688}, {"type": 24, "id": 1, "lang": 1033, "size": 381, "offset": 10574304}], "entry_point": 4448, "image_base": 5368709120}