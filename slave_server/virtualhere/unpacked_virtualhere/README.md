# VirtualHere USB Server - 重构项目

## 项目说明
这是通过逆向工程分析VirtualHere USB Server后重构的项目结构。

## 原始文件信息
- 原始文件: vhusbdwin64_original.exe
- 文件大小: 10,483,120 字节
- PE节区数: 7
- 入口点: 0x1160

## 目录结构
- `src/` - 重构的源代码
- `include/` - 头文件
- `sections/` - 提取的PE节区
- `resources/` - 提取的资源文件
- `strings/` - 提取的字符串
- `config/` - 配置文件
- `docs/` - 文档

## 关键修改点
1. `src/main.cpp` 中的 `LicenseManager::validateLicense()` 函数
2. 许可证状态设置: `setLicenseStatus("commercial")`
3. 设备数量限制: `setMaxDevices(999)`

## 构建方法
```bash
mkdir build
cd build
cmake ..
make
```

## 许可证绕过策略
基于逆向工程分析，关键的许可证验证逻辑可能在以下位置：
1. 许可证文件读取和验证
2. 注册表项检查
3. 网络验证（如果有）
4. 设备数量限制检查

通过修改重构的源代码，可以在源码级别绕过这些检查。
