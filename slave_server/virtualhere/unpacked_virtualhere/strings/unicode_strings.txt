# Unicode字符串
# 总数: 6630

0x000464e0: *.zi
0x00046517: *.xr
0x00046c94: UTF-
0x00047b9f: wxMe
0x000482f6: wxPa
0x00048305: anel
0x000483c7: wxPa
0x000483d6: anel
0x00048496: wxFr
0x000484a5: rame
0x00048567: wxFr
0x00048576: rame
0x00048736: wxIc
0x00049a2c: vers
0x00049a3b: sion
0x00049a61: 0.0.
0x00049a73: .0.0
0x0004a246: name
0x0004a31b: name
0x0004ad5e: name
0x0004af60: clas
0x0004b389: clas
0x0004ba84: name
0x0004bc4c: name
0x0004fbf0: name
0x0004febb: clas
0x00051227: name
0x000541e0: size
0x00054235: bitm
0x000543dd: mask
0x000546fd: clas
0x00055d57: size
0x00055e5b: styl
0x00056268: weig
0x000567ae: fami
0x00056a40: face
0x00056df5: sysf
0x00056e04: font
0x00056eb2: inhe
0x00056ec1: erit
0x00057130: inhe
0x0005713f: erit
0x00057231: size
0x00057461: size
0x000574e1: size
0x00057a06: vari
0x00057a15: iant
0x00057eb0: exst
0x00057ebf: tyle
0x0005803f: ownb
0x000581e8: ownf
0x000582c7: enab
0x000582d6: bled
0x0005833c: focu
0x0005834b: used
0x000583af: tool
0x000583be: ltip
0x0005849a: font
0x00058619: help
0x00060727: jpeg
0x00060b83: tpic
0x00065607: GRAY
0x00065633: GREY
0x000659aa: GRAY
0x000659d6: GREY
0x0007e474: .dll
0x0008a7c2: dren
0x0008fa37: .dll
0x00094401: OURS
0x0009fbe3: wxyz
0x0009fd0c: wxyz
0x000a0634: ture
0x000a0ad7: ture
0x000a1127: ture
0x000b24e2: focu
0x000b2652: focu
0x000ba293: .dll
0x000bd0c6: .dll
0x000bd3da: lsEx
0x000dba55: ionX
0x000dbad0: ionY
0x000dbd4f: ionY
0x000f84ec: WXTR
0x000f84fe: RACE
0x00102945: strc
0x00102954: conv
0x00102d85: strc
0x00102d94: conv
0x0010a92e: ager
0x0010b9a0: file
0x0010d6f4: wxbf
0x0016f2a1: rror
0x00177545: i18n
0x00177635: i18n
0x00177733: i18n
0x00177f47: i18n
0x00178032: i18n
0x00178734: i18n
0x00178d3d: i18n
0x00178ff1: i18n
0x0017926d: i18n
0x001795ee: i18n
0x0017988d: i18n
0x00179e7d: i18n
0x0017a0b7: i18n
0x0017a805: i18n
0x0017c3fa: i18n
0x0017c963: i18n
0x0017cc3d: i18n
0x0017d6c4: i18n
0x0017e101: i18n
0x00188ffe: ume{
0x001899da: file
0x001899e9: e://
0x0018f179: UTF-
0x00191353: text
0x00191579: cdat
0x0019173a: comm
0x00191749: ment
0x001931eb: <!--
0x0019b19b: RDER
0x0019b438: NDOW
0x0019b6e0: SIZE
0x0019bb14: ENTS
0x0019bc00: VELY
0x0019bdd6: HELP
0x0019bec2: IDLE
0x001d039c: font
0x001d11e7: modu
0x001d1a8e: modu
0x001d252b: modu
0x001d3826: size
0x001d3ba2: size
0x001d48c2: SIZE
0x001d4a38: size
0x001d4a83: styl
0x001d4c70: unkn
0x001d4c7f: nown
0x001d5740: wxIc
0x001d5bfe: _BOX
0x001d5d74: VELY
0x001d6153: hidd
0x001d61e4: titl
0x001d6248: styl
0x001d6357: size
0x001d64e3: icon
0x001d6ab4: VELY
0x001d6c0b: hidd
0x001d6cda: size
0x001d6d2b: styl
0x001d6f60: wxPa
0x001d6f6f: anel
0x001d7190: NTAL
0x001d7235: wxLE
0x001d728e: wxRI
0x001d729d: IGHT
0x001d72ee: wxTO
0x001d738e: wxNO
0x001d739d: ORTH
0x001d73ee: wxSO
0x001d73fd: OUTH
0x001d744e: wxEA
0x001d74a7: wxWE
0x001d7500: wxAL
0x001d7552: wxGR
0x001d77c0: LEFT
0x001d7832: _TOP
0x001d7edc: spac
0x001d8018: obje
0x001d83a9: size
0x001d84cc: mins
0x001d84db: size
0x001d8abd: orie
0x001d8b88: abel
0x001d8bf1: labe
0x001d8d91: orie
0x001d92d9: rows
0x001d9327: cols
0x001d936a: hgap
0x001d9556: rows
0x001d95a4: cols
0x001d95e7: hgap
0x001d97bb: hgap
0x001d9ae8: izer
0x001d9e36: opti
0x001d9ea4: bord
0x001d9f0d: mins
0x001d9f1c: size
0x001d9fb9: rati
0x001da072: cell
0x001da081: lpos
0x001da594: mode
0x001db28c: cell
0x001db29b: lpos
0x001db3f5: flag
0x001dd185: obje
0x001dd4da: butt
0x001de4e0: _BOX
0x001de64b: NDOW
0x001de90d: VELY
0x001deba3: hidd
0x001dec34: titl
0x001dec98: styl
0x001deda7: size
0x001def33: icon
0x001df270: wxFr
0x001df27f: rame
0x001df5af: VELY
0x001df752: hidd
0x001df821: size
0x001df872: styl
0x001dffb2: hidd
0x001e0081: size
0x001e00d2: styl
0x001e01e4: runn
0x001e01f3: ning
0x001e03b0: ator
0x001e072f: hidd
0x001e0848: size
0x001e089c: styl
0x001e0a88: -end
0x001e0c0e: bitm
0x001e0d49: titl
0x001e0dad: mess
0x001e0dbf: sage
0x001e1716: text
0x001e1782: bitm
0x001e18be: hidd
0x001e1953: valu
0x001e1a0a: size
0x001e1a61: styl
0x001e1bd8: obje
0x001e2621: TTOM
0x001e2ab5: hidd
0x001e2bca: bitm
0x001e2c8f: size
0x001e2ce3: styl
0x001e2e51: defa
0x001e2e60: ault
0x001e35c2: _BOX
0x001e3738: VELY
0x001e3b4e: hidd
0x001e3bdf: titl
0x001e3c8d: size
0x001e3ce4: styl
0x001e3e28: icon
0x001e3ff8: butt
0x001e4007: tons
0x001e49cc: TTOM
0x001e4ab3: TEXT
0x001e4c75: hidd
0x001e4d06: labe
0x001e4db7: size
0x001e4e0e: styl
0x001e4f63: defa
0x001e4f72: ault
0x001e5a59: DAYS
0x001e5ad9: ANGE
0x001e5c69: EEKS
0x001e5e40: hidd
0x001e5f0f: size
0x001e5f60: styl
0x001e63fc: TATE
0x001e6745: hidd
0x001e67d6: labe
0x001e6884: size
0x001e68db: styl
0x001e7194: NGLE
0x001e72ed: ROLL
0x001e7594: cont
0x001e75a3: tent
0x001e7699: hidd
0x001e776b: size
0x001e77bf: styl
0x001e78d7: cont
0x001e7996: cked
0x001e79a5: chec
0x001e8152: cont
0x001e8161: tent
0x001e82c8: hidd
0x001e839b: size
0x001e83ef: styl
0x001e88d0: AULT
0x001e8a35: TTOM
0x001e8b6f: IGHT
0x001e8dc6: hidd
0x001e8e95: size
0x001e8ee6: styl
0x001e9136: book
0x001ea6f3: obje
0x001ea837: hidd
0x001ea8ae: labe
0x001ea9ac: size
0x001eaa03: styl
0x001eb997: CTRL
0x001eba8a: TYLE
0x001ebc04: hidd
0x001ebc95: valu
0x001ebd38: size
0x001ebd8c: styl
0x001eca64: MPLE
0x001ecde2: cont
0x001ecdf1: tent
0x001ecf9a: hidd
0x001ed02c: valu
0x001ed0dd: size
0x001ed134: styl
0x001ed2b7: hint
0x001ed96e: LICK
0x001edb58: hidd
0x001edbec: valu
0x001edca0: size
0x001edcf4: styl
0x001ee010: Ctrl
0x001ee6c7: hidd
0x001ee758: labe
0x001ee7bf: note
0x001ee87b: size
0x001ee8d2: styl
0x001eea62: defa
0x001eea71: ault
0x001ef260: tton
0x001ef48c: NGLE
0x001ef92d: hidd
0x001ef9fc: size
0x001efa50: styl
0x001efd09: hidd
0x001efdd8: size
0x001efe29: styl
0x001f00a0: hidd
0x001f016f: size
0x001f01c0: styl
0x001f176c: AULT
0x001f1af8: hidd
0x001f1bc7: size
0x001f1c18: styl
0x001f25c1: hidd
0x001f272c: size
0x001f2783: styl
0x001f27d3: filt
0x001f2ea7: CTRL
0x001f300c: MALL
0x001f308c: TYLE
0x001f3216: hidd
0x001f32aa: valu
0x001f32f9: mess
0x001f330b: sage
0x001f33c7: size
0x001f341b: styl
0x001f3d8c: hidd
0x001f3eb8: size
0x001f3f0c: styl
0x001f48ca: hidd
0x001f4acf: styl
0x001f4b6f: size
0x001f53f1: MALL
0x001f5471: TYLE
0x001f5678: hidd
0x001f570c: valu
0x001f575b: mess
0x001f576d: sage
0x001f587a: size
0x001f58d1: styl
0x001f5f77: CTRL
0x001f60f1: TYLE
0x001f6289: hidd
0x001f6336: valu
0x001f6466: size
0x001f64bd: styl
0x001f6a60: OOTH
0x001f6c3d: hidd
0x001f6cce: rang
0x001f6d5f: size
0x001f6db3: styl
0x001f6ee5: valu
0x001f7130: wxGa
0x001f713f: auge
0x001f749d: hidd
0x001f756c: size
0x001f75bd: styl
0x001f77d0: wxGr
0x001f8077: EVER
0x001f80f7: AUTO
0x001f82c5: hidd
0x001f8394: size
0x001f83e2: styl
0x001f84eb: bord
0x001f84fa: ders
0x001f8950: ndow
0x001f8d6f: TYLE
0x001f8f9a: cont
0x001f8fa9: tent
0x001f9131: hidd
0x001f9201: size
0x001f9255: styl
0x001f9510: tBox
0x001f9afd: hidd
0x001f9b91: labe
0x001f9c92: size
0x001f9ce6: styl
0x001fa682: hidd
0x001fb461: AULT
0x001fb5c6: TTOM
0x001fb926: hidd
0x001fb9f5: size
0x001fba46: styl
0x001fbcf9: page
0x001fbf74: NGLE
0x001fc0cd: ROLL
0x001fc3d2: cont
0x001fc3e1: tent
0x001fc584: hidd
0x001fc657: size
0x001fc6ab: styl
0x001fcc9d: IGHT
0x001fcd1d: NTRE
0x001fd053: USED
0x001fd194: PORT
0x001fd5f3: DING
0x001fd6de: TUAL
0x001fd753: ULES
0x001fd83e: ADER
0x001fdc75: data
0x001fdd33: font
0x001fde02: stat
0x001fe35a: widt
0x001fe411: imag
0x001fe63f: hidd
0x001fe70e: size
0x001fe75f: styl
0x001fec36: alig
0x001fece3: text
0x001fff00: _BOX
0x0020006b: NDOW
0x0020032d: VELY
0x002007bf: hidd
0x00200853: titl
0x002008bd: styl
0x002009ad: hidd
0x00200a3e: titl
0x00200aa8: styl
0x00200cf1: size
0x00200e85: icon
0x00201873: labe
0x00201904: acce
0x002019c0: cels
0x00201b76: radi
0x00201ce6: help
0x002020e0: styl
0x0020215e: labe
0x002021ef: help
0x002023d8: enab
0x002023e7: bled
0x002026a9: bitm
0x00202715: map2
0x002029b0: enab
0x002029bf: bled
0x00202a2b: chec
0x00202a3a: cked
0x00202da7: wxMe
0x00202e4f: brea
0x00203171: styl
0x002035f1: AULT
0x00203756: TTOM
0x00203c03: hidd
0x00203cd2: size
0x00203d23: styl
0x00203ff9: page
0x00204274: MPLE
0x002044a4: CLES
0x002046ea: cont
0x002046f9: tent
0x00204843: hidd
0x002048d8: valu
0x0020498f: size
0x002049e6: styl
0x00204e00: oBox
0x002054b7: tool
0x002054c9: ltip
0x0020570a: cont
0x00205719: tent
0x002059b0: hidd
0x00205a45: labe
0x00205afc: size
0x00205ba6: styl
0x002067bf: NGLE
0x00206993: hidd
0x00206a27: labe
0x00206ade: size
0x00206b32: styl
0x00206c87: valu
0x0020734c: hidd
0x0020741e: size
0x00207472: styl
0x0020757b: valu
0x00207610: rang
0x00207930: lBar
0x00207cf5: NTRE
0x00207f1d: hidd
0x00207fae: valu
0x0020805c: size
0x002080b3: styl
0x00208220: hint
0x002084a0: Ctrl
0x00208853: obje
0x0020898e: labe
0x00208af5: hidd
0x00208bc7: size
0x00208c1b: styl
0x00208fc6: book
0x00209fcd: BELS
0x0020a212: TTOM
0x0020a348: ERSE
0x0020a523: hidd
0x0020a5b4: valu
0x0020a6dc: size
0x0020a736: styl
0x0020aadc: thum
0x0020ab94: tick
0x0020ac54: selm
0x0020b5a8: hidd
0x0020b677: size
0x0020b6c5: styl
0x0020b7ce: valu
0x0020bad0: tton
0x0020bed0: LEFT
0x0020c1c4: hidd
0x0020c259: valu
0x0020c310: size
0x0020c367: styl
0x0020c665: base
0x0020cc95: hidd
0x0020cd29: valu
0x0020cde0: size
0x0020ce37: styl
0x0020ed86: wxSP
0x0020ed95: P_3D
0x0020ee09: SASH
0x0020eef0: RDER
0x0020efdf: PLIT
0x0020f239: hidd
0x0020f308: size
0x0020f356: styl
0x0020f477: sash
0x0020f486: hpos
0x0020f4e8: mins
0x0020f550: grav
0x0020f55f: vity
0x0020f79a: tion
0x0020fd95: hidd
0x0020fe26: bitm
0x0020fe60: size
0x0020ff77: styl
0x0021064b: hidd
0x002106df: labe
0x00210793: size
0x002107e7: styl
0x00210af0: cBox
0x00210f27: hidd
0x00210ff6: size
0x00211047: styl
0x002112a0: Line
0x00211535: LEFT
0x00211804: TART
0x00211ac7: hidd
0x00211b5b: labe
0x00211c0f: size
0x00211c60: styl
0x00211db5: wrap
0x00211ff0: Text
0x002123f5: _END
0x00212475: TYLE
0x0021265d: hidd
0x002126f0: styl
0x002127ab: fiel
0x00212816: widt
0x00212f70: sBar
0x00213663: NTER
0x002136d8: NTRE
0x00213b75: hidd
0x00213c06: valu
0x00213cb7: size
0x00213d0e: styl
0x00213faf: hint
0x0021452c: TTOM
0x00214613: TEXT
0x0021495d: tton
0x00214a6f: labe
0x00214b1d: size
0x00214b74: styl
0x002151ed: chec
0x002151fc: cked
0x002154d1: bitm
0x00215594: size
0x002155eb: styl
0x00215ae7: chec
0x00215af6: cked
0x00215e4c: AULT
0x00216028: hidd
0x002160fa: size
0x0021614e: styl
0x002167eb: CONS
0x002168d2: LIGN
0x00216b1e: TTOM
0x00216c6e: styl
0x00216d9e: radi
0x00216dfe: togg
0x00217134: labe
0x002171a3: bitm
0x00217237: map2
0x002172be: tool
0x002172d0: ltip
0x002175cd: chec
0x002175dc: cked
0x002177d1: hidd
0x002178a8: size
0x00217a6e: marg
0x00217a7d: gins
0x00217aff: pack
0x00217b0e: king
0x00217c02: obje
0x00217da5: tool
0x00217ef8: spac
0x002184d0: tool
0x0021852b: spac
0x00218820: AULT
0x002188e0: TTOM
0x00218c0e: hidd
0x00218cdd: size
0x00218d2e: styl
0x00218ff9: page
0x0021922d: AULT
0x002192ea: TTOM
0x00219509: page
0x002196dd: dept
0x002199ee: hidd
0x00219ac1: size
0x00219b18: styl
0x0021a86a: IGHT
0x0021a8df: NGLE
0x0021abe2: hidd
0x0021acb1: size
0x0021ad05: styl
0x0021b475: _BOX
0x0021b5eb: VELY
0x0021b933: _TOP
0x0021baad: LEFT
0x0021bdd1: bitm
0x0021c00a: hidd
0x0021c081: exst
0x0021c090: tyle
0x0021c119: titl
0x0021c17d: bitm
0x0021c257: styl
0x0021c663: hidd
0x0021c6e1: bitm
0x0021ca92: Page
0x0022102c: DDEE
0x0022103e: Exec
0x002210dc: topi
0x0022145d: tion
0x00221501: nURL
0x00226d5d: litt
0x0022751e: focu
0x00227a48: focu
0x00227c0b: focu
0x0022801b: focu
0x002281a8: focu
0x0022895a: focu
0x0022ed4f: tiff
0x002305c5: tric
0x00231519: tric
0x00231654: sion
0x00233dc2: RMAT
0x0023a5b6: RMAT
0x0023a639: RMAT
0x002472ee: PngF
0x002473c0: PngZ
0x00247709: epth
0x0024dabd: ionX
0x0024db44: ionY
0x0024dfea: qual
0x0024dffb: lity
0x0025690b: EDIT
0x00279d59: S %p
0x00279ed3: %H:%
0x00288008: true
0x002880e8: fals
0x002885d0: Sund
0x00288615: Mond
0x0028865a: sday
0x00288668: Tues
0x0028871d: Frid
0x002891c0: uary
0x002891ce: Janu
0x00289247: Marc
0x00289286: Apri
0x00289311: June
0x0028934f: July
0x0028938d: Augu
0x0028940e: ober
0x0028941c: Octo
0x002c2ea9: .dll
0x002c67f7: .dll
0x002e5e3b: list
0x002e60e3: list
0x002e6873: acce
0x002e6ce8: acce
0x002e73e9: acce
0x002e774c: acce
0x002e79c6: acce
0x002e7b5f: acce
0x002e7f67: acce
0x002e825a: acce
0x002e8350: acce
0x002e8446: acce
0x002e8537: acce
0x002e87e6: acce
0x002e8948: acce
0x002e8a39: acce
0x002e8b99: acce
0x002e8d20: acce
0x002e8e71: acce
0x002e92ca: acce
0x002e955d: acce
0x002e9887: acce
0x002e9978: acce
0x002e9adc: acce
0x002e9c51: acce
0x002e9db6: acce
0x002e9f9d: acce
0x002ea231: acce
0x002ea489: acce
0x002ea7d5: acce
0x002eaa79: acce
0x002eac1e: acce
0x002eae28: acce
0x002eaf94: acce
0x002eb1cb: acce
0x002eb3f7: acce
0x002eb7ea: acce
0x002ebb5c: acce
0x002ebefa: acce
0x002ec141: acce
0x002ec5fa: acce
0x002ec841: acce
0x002ecd07: acce
0x002ed00b: acce
0x002ed3fa: acce
0x002ed64b: acce
0x002edb1a: acce
0x002edd67: acce
0x002ee220: acce
0x002ee474: acce
0x002ee8a0: acce
0x002eeae9: acce
0x002ef13a: acce
0x002ef384: acce
0x002ef84f: acce
0x002efa89: acce
0x002efe98: acce
0x002f0258: acce
0x002f03f8: long
0x002f04aa: void
0x002f2a12: null
0x002f2b25: null
0x002f2c75: null
0x002f36a9: null
0x002f3919: null
0x002f4149: null
0x002f43ce: null
0x002f4c19: null
0x002f4e89: null
0x002f5729: null
0x002f58da: null
0x002f700a: null
0x002f7cda: null
0x002f83c2: null
0x002f867a: null
0x002f8eaa: null
0x002f8fca: null
0x002f97ac: null
0x002f99fa: null
0x002fa29c: null
0x002fa51a: null
0x002fb0fc: null
0x002fb2bc: null
0x002fb416: null
0x002fb825: null
0x002fb960: null
0x002fba13: null
0x002fbc1c: null
0x002fbd56: null
0x002fbe09: null
0x002fc7c8: long
0x002fc888: doub
0x002fc908: bool
0x002fc978: char
0x002fca68: stri
0x002fcb78: void
0x002fce18: list
0x00301637: file
0x00301aa7: urce
0x00301bba: urce
0x003026a5: name
0x00311093: .dll
0x0033886f: NONE
0x00338881: QWOR
0x00338890: DWOR
0x0033890f: BINA
0x0033899d: LINK
0x0033dbd5: PATH
0x0034378a: Task
0x00343839: Task
0x00343ab1: Rece
0x00347114: .dll
0x00364979: i18n
0x0036fb9b: sage
0x00372804: log.
0x00372813: .txt
0x00374eca: Mess
0x00374ed9: sage
0x00374f3a: Time
0x0037cc5c: Alia
0x0037cc6b: ases
0x00387b95: open
0x00387cd5: prin
0x003a1249: %H:%
0x003a16f5: S %Y
0x003a1a09: S %p
0x003a1dfa: %x %
0x003a1e2e: %X %
0x003bf6e3: .dll
0x003d96cc: obje
0x003d9819: bitm
0x003d9b04: imag
0x003d9d1c: labe
0x003e9379: Mask
0x003ebf95: bool
0x003fdf03: bool
0x003feeb1: bool
0x0040de31: long
0x0040e5d6: stri
0x00411be8: stri
0x00411d8c: bool
0x00411f31: long
0x00412070: long
0x0041535f: stri
0x004154cf: bool
0x00415588: long
0x00426da0: item
0x004278a3: S %p
0x0042848d: GREY
0x00429969: S %p
0x00429e45: es 2
0x00438738: stri
0x0044a97f: grid
0x004583b3: stri
0x004584b3: stri
0x0045987c: bool
0x00459aec: long
0x00459b66: doub
0x00459c46: date
0x00460619: stri
0x0046073a: bool
0x00460833: long
0x0046096f: doub
0x0046c330: ders
0x0046c4ed: rmal
0x0046cd75: ders
0x0046ce94: rmal
0x00478a29: Page
0x00478f1e: Page
0x0049ca28: tton
0x004e1d27: ency
0x005103b2: dial
0x0051619b: 100%
0x00518494: canv
0x00518570: pane
0x00521291: Addr
0x0052139c: lk64
0x00521c88: .dll
0x00521fd9: null
0x0053b19f: shel
0x0053b1ae: ll32
0x0053cce1: .dll
0x0053d479: .dll
0x0055361f: open
0x00553fb1: WX_D
0x00553fc0: DDE#
0x00554e04: show
0x0055501c: Type
0x00555e10: ype\
0x00556082: ype\
0x00556839: Type
0x005568d6: ype\
0x00556d0e: Type
0x00556db3: ype\
0x00556fae: open
0x00557026: prin
0x00557dc6: open
0x00557e7d: open
0x0058fde0: mpr.
0x0058fdef: .dll
0x005936a2: .dll
0x00593774: lsEx
0x005add5f: long
0x005af8b7: long
0x005b02ff: doub
0x005b1581: bool
0x005b3e04: long
0x005b42c2: long
0x005b4be3: doub
0x005b5250: doub
0x005b61f3: bool
0x005b63d0: bool
0x005b784a: long
0x005b7ad2: long
0x005bd3f9: ALIG
0x005bd55c: WIDT
0x005c2f0a: colo
0x005c3833: ight
0x005c3fe4: mily
0x005c6dcf: CONT
0x005c6de1: TENT
0x005c8238: CENT
0x005c83db: STYL
0x005c84a1: ALIG
0x005c87b8: TITL
0x005c89b8: BODY
0x005c8a1f: TEXT
0x005c8b2c: LINK
0x005c8d09: BGCO
0x005c8d18: OLOR
0x005c9098: SUB,
0x005c90a6: ,SUP
0x005c9328: SCRI
0x005c9668: FONT
0x005c97f4: COLO
0x005c98fb: BGCO
0x005c990a: OLOR
0x005c9a56: SIZE
0x005c9bd0: FACE
0x005ca5e7: ,DEL
0x005cc8a5: *.gi
0x005cc8dd: *.GI
0x005cdbb7: AREA
0x005cdd74: WIDT
0x005cde10: HEIG
0x005cdea4: ALIG
0x005cdf66: USEM
0x005ce37a: NAME
0x005ce569: SHAP
0x005ce5dc: COOR
0x005ce782: HREF
0x005ce7f2: TARG
0x005d0909: SIZE
0x005d0959: NOSH
0x005d0968: HADE
0x005d0c5a: NAME
0x005d0d65: HREF
0x005d0f86: TARG
0x005d1a1a: BGCO
0x005d1a29: OLOR
0x005d1aa6: VALI
0x005d1b78: CING
0x005d1c04: DING
0x005d1ca3: BORD
0x005d262f: BGCO
0x005d263e: OLOR
0x005d268c: VALI
0x005d2aa1: WIDT
0x005d2b58: COLS
0x005d2b67: SPAN
0x005d2bc4: ROWS
0x005d2eb4: BGCO
0x005d2ec3: OLOR
0x005d3084: VALI
0x005d3174: NOWR
0x005d45e2: WIDT
0x005d472c: ALIG
0x005d4801: ALIG
0x005d490d: ALIG
0x005d4ab0: BGCO
0x005d4abf: OLOR
0x005d51f8: SPAN
0x005d5878: STYL
0x005d59c9: &amp
0x005d5a32: &gt;
0x0060bbf2: .dll
0x0060f4c8: Unkn
0x0060f4da: nown
0x0062552f: STYL
0x006bee30: *.zip
0x006bee3c: *.xrs
0x006bee4c: c#zip:*.xrc
0x006beebc: )wildcards not supported by wxXmlResource::Unload()
0x006bef24: #zip:
0x006bef30: wxMenu
0x006bef3e: wxMenuBar
0x006bef52: wxToolBar
0x006bef66: wxDialog
0x006bef78: wxPanel
0x006bef88: wxFrame
0x006bef98: wxBitmap
0x006befaa: wxIcon
0x006befb8: _container
0x006bf01c: exrc
0x006bf026: opening file '%s'
0x006bf06c: UTF-8
0x006bf09c: .resource
0x006bf0e6: >version
0x006bf0f8: 0.0.0.0
0x006bf108: %i.%i.%i.%i
0x006bf16e: "must have a valid document
0x006bf1a6: <XML document #%lu>
0x006bf1ce: class
0x006bf1da: object_ref
0x006bf24e: dobject
0x006bf748: subclass
0x006bf7ae: translate
0x006bf9d8: "bitmap
0x006bfa18: You can't access handler data before it was initialized!
0x006bfb30: style
0x006bfb3c: italic
0x006bfb4a: slant
0x006bfb56: normal
0x006bfb7a: "weight
0x006bfbaa: thin
0x006bfbb4: extralight
0x006bfbca: light
0x006bfbd6: medium
0x006bfbe4: semibold
0x006bfbf6: bold
0x006bfc00: extrabold
0x006bfc14: heavy
0x006bfc20: extraheavy
0x006bfc50: underlined
0x006bfc66: strikethrough
0x006bfc82: family
0x006bfc90: default
0x006bfca0: decorative
0x006bfcb6: roman
0x006bfcc2: script
0x006bfcd0: swiss
0x006bfcdc: modern
0x006bfcea: teletype
0x006bfd16: encoding
0x006bfd28: sysfont
0x006bfd38: inherit
0x006bfdac: relativesize
0x006bfe1e: variant
0x006bfe2e: small
0x006bfe3a: mini
0x006bfe44: large
0x006bfe96: exstyle
0x006bfea6: ownbg
0x006bfeb2: ownfg
0x006bfebe: enabled
0x006bfece: focused
0x006bfede: tooltip
0x006bfeee: ownfont
0x006bff1e: swxXmlResourceModule
0x006bffd2: Should be called exactly once
0x006c0038: )invalid wxDateTime
0x006c0060: platform
0x006c0078: wids-range
0x006c0096: insert_at
0x006c00aa: begin
0x006c00c2: ewxSYS_COLOUR_SCROLLBAR
0x006c00f2: wxSYS_COLOUR_BACKGROUND
0x006c0122: wxSYS_COLOUR_DESKTOP
0x006c014c: wxSYS_COLOUR_ACTIVECAPTION
0x006c0182: wxSYS_COLOUR_INACTIVECAPTION
0x006c01bc: wxSYS_COLOUR_MENU
0x006c01e0: wxSYS_COLOUR_WINDOW
0x006c0208: wxSYS_COLOUR_WINDOWFRAME
0x006c023a: wxSYS_COLOUR_MENUTEXT
0x006c0266: wxSYS_COLOUR_WINDOWTEXT
0x006c0296: wxSYS_COLOUR_CAPTIONTEXT
0x006c02c8: wxSYS_COLOUR_ACTIVEBORDER
0x006c02fc: wxSYS_COLOUR_INACTIVEBORDER
0x006c0334: wxSYS_COLOUR_APPWORKSPACE
0x006c0368: wxSYS_COLOUR_HIGHLIGHT
0x006c0396: wxSYS_COLOUR_HIGHLIGHTTEXT
0x006c03cc: wxSYS_COLOUR_BTNFACE
0x006c03f6: wxSYS_COLOUR_3DFACE
0x006c041e: wxSYS_COLOUR_BTNSHADOW
0x006c044c: wxSYS_COLOUR_3DSHADOW
0x006c0478: wxSYS_COLOUR_GRAYTEXT
0x006c04a4: wxSYS_COLOUR_BTNTEXT
0x006c04ce: wxSYS_COLOUR_INACTIVECAPTIONTEXT
0x006c0510: wxSYS_COLOUR_BTNHIGHLIGHT
0x006c0544: wxSYS_COLOUR_BTNHILIGHT
0x006c0574: wxSYS_COLOUR_3DHIGHLIGHT
0x006c05a6: wxSYS_COLOUR_3DHILIGHT
0x006c05d4: wxSYS_COLOUR_3DDKSHADOW
0x006c0604: wxSYS_COLOUR_3DLIGHT
0x006c062e: wxSYS_COLOUR_INFOTEXT
0x006c065a: wxSYS_COLOUR_INFOBK
0x006c0682: wxSYS_COLOUR_LISTBOX
0x006c06ac: wxSYS_COLOUR_HOTLIGHT
0x006c06d8: wxSYS_COLOUR_GRADIENTACTIVECAPTION
0x006c071e: wxSYS_COLOUR_GRADIENTINACTIVECAPTION
0x006c0768: wxSYS_COLOUR_MENUHILIGHT
0x006c079a: wxSYS_COLOUR_MENUBAR
0x006c084c: wxArrayString: index out of bounds
0x006c0892: wxSYS_OEM_FIXED_FONT
0x006c08bc: wxSYS_ANSI_FIXED_FONT
0x006c08e8: wxSYS_ANSI_VAR_FONT
0x006c0910: wxSYS_SYSTEM_FONT
0x006c0934: wxSYS_DEVICE_DEFAULT_FONT
0x006c0968: wxSYS_SYSTEM_FIXED_FONT
0x006c0998: wxSYS_DEFAULT_GUI_FONT
0x006c15f4: sthis is useless to call without any flags
0x006c1652: xInvalid menu string '%s'
0x006c16a2: "unexpected return code from wxMessageDialog
0x006c16fc: %d.%d.%d
0x006c170e: wxWidgets Library (%s port)
0x006c1746: Version %s (Unicode: %s, debug level: %d),
0x006c179c: compiled at %s %s
0x006c17c2: Runtime version of toolkit used is %d.%d.%d.
0x006c1824: tApr 25 2025
0x006c183e: 13:30:59
0x006c1850: wxWidgets
0x006c1864: Copyright (c) 1992-2025 wxWidgets team
0x006c18b6: wxWidgets information
0x006c18ea: p://
0x006c2760: PNG file
0x006c277a: image/png
0x006c279e: JPEG file
0x006c27c2: image/jpeg
0x006c27d8: GIF file
0x006c27f2: image/gif
0x006c2806: PNM file
0x006c2838: image/pnm
0x006c284c: PCX file
0x006c2866: image/pcx
0x006c287a: IFF file
0x006c2894: image/x-iff
0x006c28ac: Windows icon file
0x006c28d8: image/x-ico
0x006c28f0: Windows bitmap file
0x006c2920: image/x-bmp
0x006c2938: Windows cursor file
0x006c2968: image/x-cur
0x006c2980: Windows animated cursor file
0x006c29c2: image/x-ani
0x006c29da: TGA file
0x006c29f4: image/tga
0x006c2a08: XPM file
0x006c2a22: image/xpm
0x006c2aa0: wxMenu
0x006c2aae: wxMenuBar
0x006c2ac2: wxMenuItem
0x006c2c06: )invalid itemid value
0x006c2c52: )A non-stock menu item with an empty label?
0x006c2dee: can't add a NULL submenu
0x006c2e2e: "invalid item in wxMenu::Append()
0x006c2e7a: invalid item in wxMenu::Insert
0x006c2ed4: invalid index in wxMenu::Insert
0x006c2f1e: invalid item in wxMenu::Insert()
0x006c2f68: invalid index in wxMenu::Insert()
0x006c2fb4: invalid item in wxMenu::Remove
0x006c2ff2: removing item not in the menu?
0x006c3038: invalid item in wxMenu::Delete
0x006c3088: failed to delete menu item
0x006c30c4: yinvalid item in wxMenu::Destroy
0x006c3144: wxMenu::FindItemByPosition(): invalid menu index
0x006c31b6: menu can't be attached to NULL menubar
0x006c3210: attaching menu twice?
0x006c324e: detaching unattached menu?
0x006c331c: ewxMenu::Enable: no such item
0x006c3360: dwxMenu::IsEnabled: no such item
0x006c33a6: kwxMenu::Check: no such item
0x006c33e8: dwxMenu::IsChecked: no such item
0x006c3434: wxMenu::SetLabel: no such item
0x006c347c: wxMenu::GetLabel: no such item
0x006c34c6: gwxMenu::SetHelpString: no such item
0x006c351c: gwxMenu::GetHelpString: no such item
0x006c3d46: ubad index in wxMenuBar::GetMenu()
0x006c3d98: "can't append NULL menu
0x006c3dda: can't append menu with empty title
0x006c3e20: can't insert NULL menu
0x006c3e4e: bad index in wxMenuBar::Insert()
0x006c3e96: ebad index in wxMenuBar::Replace()
0x006c3edc: bad index in wxMenuBar::Remove()
0x006c3f1e: menubar already attached!
0x006c3f60: detaching unattached menubar
0x006c3f9a: attempt to enable an item which doesn't exist
0x006c3ff6: attempt to check an item which doesn't exist
0x006c4064: "attempt to check an uncheckable item
0x006c40b0: wxMenuBar::IsChecked(): no such item
0x006c40fa: wxMenuBar::IsEnabled(): no such item
0x006c4144: wxMenuBar::SetLabel(): no such item
0x006c418c: wxMenuBar::GetLabel(): no such item
0x006c41d4: wxMenuBar::SetHelpString(): no such item
0x006c4226: wxMenuBar::GetHelpString(): no such item
0x006c44fc: wxGDIObject
0x006c4980: AQUAMARINE
0x006c4996: BLACK
0x006c49a2: BLUE
0x006c49ac: BLUE VIOLET
0x006c49c4: BROWN
0x006c49d0: CADET BLUE
0x006c49e6: CORAL
0x006c49f2: CORNFLOWER BLUE
0x006c4a12: CYAN
0x006c4a1c: DARK GREY
0x006c4a30: DARK GREEN
0x006c4a46: DARK OLIVE GREEN
0x006c4a68: DARK ORCHID
0x006c4a80: DARK SLATE BLUE
0x006c4aa0: DARK SLATE GREY
0x006c4ac0: DARK TURQUOISE
0x006c4ade: DIM GREY
0x006c4af0: FIREBRICK
0x006c4b04: FOREST GREEN
0x006c4b1e: GOLD
0x006c4b28: GOLDENROD
0x006c4b3c: GREY
0x006c4b46: GREEN
0x006c4b52: GREEN YELLOW
0x006c4b6c: INDIAN RED
0x006c4b82: KHAKI
0x006c4b8e: LIGHT BLUE
0x006c4ba4: LIGHT GREY
0x006c4bba: LIGHT STEEL BLUE
0x006c4bdc: LIME GREEN
0x006c4bf2: LIGHT MAGENTA
0x006c4c0e: MAGENTA
0x006c4c1e: MAROON
0x006c4c2c: MEDIUM AQUAMARINE
0x006c4c50: MEDIUM GREY
0x006c4c68: MEDIUM BLUE
0x006c4c80: MEDIUM FOREST GREEN
0x006c4ca8: MEDIUM GOLDENROD
0x006c4cca: MEDIUM ORCHID
0x006c4ce6: MEDIUM SEA GREEN
0x006c4d08: MEDIUM SLATE BLUE
0x006c4d2c: MEDIUM SPRING GREEN
0x006c4d54: MEDIUM TURQUOISE
0x006c4d76: MEDIUM VIOLET RED
0x006c4d9a: MIDNIGHT BLUE
0x006c4db6: NAVY
0x006c4dc0: ORANGE
0x006c4dce: ORANGE RED
0x006c4de4: ORCHID
0x006c4df2: PALE GREEN
0x006c4e08: PINK
0x006c4e12: PLUM
0x006c4e1c: PURPLE
0x006c4e32: SALMON
0x006c4e40: SEA GREEN
0x006c4e54: SIENNA
0x006c4e62: SKY BLUE
0x006c4e74: SLATE BLUE
0x006c4e8a: SPRING GREEN
0x006c4ea4: STEEL BLUE
0x006c4ec2: THISTLE
0x006c4ed2: TURQUOISE
0x006c4ee6: VIOLET
0x006c4ef4: VIOLET RED
0x006c4f0a: WHEAT
0x006c4f16: WHITE
0x006c4f22: YELLOW
0x006c4f30: YELLOW GREEN
0x006c5286: wxTextCtrl
0x006c529c: wxTextUrlEvent
0x006c52ba: wxTextCtrlBase
0x006c5320: eCan't save textctrl to file without filename.
0x006c53a8: "Must have wxTE_PROCESS_ENTER for wxEVT_TEXT_ENTER to work
0x006c5444: "Position argument out of range.
0x006c6430: wxFrame
0x006c6d7a: Menu item can't be NULL
0x006c6db2: Menu item should be attached to a menu
0x006c6e22: "recreating status bar in wxFrame
0x006c6e8a: "no statusbar to set text for
0x006c6ed4: sno statusbar to set widths for
0x006c6f52: recreating toolbar in wxFrame
0x006c7420: name of predefined format cannot be retrieved
0x006c7500: HTML Format
0x006c752e: wxIEnumFORMATETC::Next
0x006c7562: wxIEnumFORMATETC::Skip
0x006c7594: twxIEnumFORMATETC::Reset
0x006c75ca: ewxIEnumFORMATETC::Clone
0x006c7686: awxIDataObject::GetData
0x006c76b6: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006c772e: GlobalAlloc
0x006c7746: Invalid data size - can't be 0
0x006c778e: ewxIDataObject::GetDataHere
0x006c77cc: awxIDataObject::SetData
0x006c780a: wxIDataObject::QueryGetData: invalid ptr.
0x006c785e: wxIDataObject::QueryGetData: bad lindex %ld
0x006c78b6: wxIDataObject::QueryGetData: bad dwAspect %ld
0x006c7912: wxIDataObject::QueryGetData: %s ok
0x006c7958: wxIDataObject::QueryGetData: %s ok (system data)
0x006c79ba: wxIDataObject::QueryGetData: %s unsupported
0x006c7a12: wxIDataObject::QueryGetData: %s != %s
0x006c7a72: cwxIDataObject::GetCanonicalFormatEtc
0x006c7aca: cwxIDataObject::EnumFormatEtc
0x006c7b88: rGlobalSize
0x006c7ba0: CF_TEXT
0x006c7bb0: CF_BITMAP
0x006c7bc4: CF_SYLK
0x006c7bd4: CF_DIF
0x006c7be2: CF_TIFF
0x006c7bf2: CF_OEMTEXT
0x006c7c08: CF_DIB
0x006c7c16: CF_PALETTE
0x006c7c2c: CF_PENDATA
0x006c7c42: CF_RIFF
0x006c7c52: CF_WAVE
0x006c7c62: CF_UNICODETEXT
0x006c7c80: CF_METAFILEPICT
0x006c7ca0: CF_ENHMETAFILE
0x006c7cbe: CF_LOCALE
0x006c7cd2: CF_HDROP
0x006c7ce4: unknown CF (0x%04x)
0x006c7d14: pasting/dropping invalid bitmap
0x006c7d54: GetObject(HBITMAP)
0x006c7d9e: "wrong HDROP handle
0x006c7dc6: In wxFileDropTarget::OnDrop DragQueryFile returned %d characters, %d expected.
0x006c7eec: unsupported format in wxURLDataObject
0x006c7f40: no data in wxURLDataObject
0x006c7f76: UniformResourceLocator
0x006c81a8: GlobalLock
0x006c81d6: GlobalUnlock
0x006c821c: twxArrayString: index out of bounds
0x006c8266: TYMED_HGLOBAL
0x006c8282: TYMED_FILE
0x006c8298: TYMED_ISTREAM
0x006c82b4: TYMED_ISTORAGE
0x006c82d2: TYMED_GDI
0x006c82e6: TYMED_MFPICT
0x006c8300: TYMED_ENHMF
0x006c8318: type of media format %ld (unknown)
0x006c89ec: wxDialog
0x006c92c8: wxWindowModalDialogEvent
0x006c92fa: wxDialogLayoutAdapter
0x006c9326: wxStandardDialogLayoutAdapter
0x006c9362: wxDialogLayoutAdapterModule
0x006c95da: shouldn't be called twice
0x006cbd60: wxFileDialog
0x006cc764: empty wildcard list
0x006cc7ca: gIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006cc844: IFileDialog::SetFileTypes
0x006cc878: IFileDialog::SetFileTypeIndex
0x006cc8b8: IFileDialog::SetFileName
0x006cc90c: )Can't use both wxFD_SHORTCUT_TOP and BOTTOM
0x006cc992: tGetWindowRect
0x006cca6e: IFileDialogCustomize::AddPushButton
0x006ccbf6: eIFileDialogCustomize::GetControlState
0x006ccc44: IFileDialogCustomize::SetControlState
0x006ccc9a: xIFileDialogCustomize::AddCheckButton
0x006ccdfc: IFileDialogCustomize::GetCheckButtonState
0x006cce5a: IFileDialogCustomize::SetCheckButtonState
0x006ccebe: IFileDialogCustomize::AddRadioButtonList
0x006ccf10: IFileDialogCustomize::AddControlItem
0x006cd076: IFileDialogCustomize::GetSelectedControlItem
0x006cd0d6: "clearing radio buttons not supported
0x006cd122: IFileDialogCustomize::SetSelectedControlItem
0x006cd184: eIFileDialogCustomize::AddComboBox
0x006cd2fc: lIFileDialogCustomize::StartVisualGroup
0x006cd34c: IFileDialogCustomize::AddEditBox
0x006cd38e: IFileDialogCustomize::EndVisualGroup
0x006cd4e0: EIFileDialogCustomize::GetEditBoxText
0x006cd53e: Can't get direct access to initialized pointer
0x006cd59c: IFileDialogCustomize::SetEditBoxText
0x006cd5f2: tIFileDialogCustomize::AddText
0x006cd742: IFileDialogCustomize::SetControlLabel
0x006cd808: )wxString: index out of bounds
0x006cd872: twxArrayString: index out of bounds
0x006cd8ba: kernel32.dll
0x006cd91e: GlobalAlloc
0x006cd936: GlobalLock
0x006cd95c: GlobalUnlock
0x006cd9c0: rGlobalFree
0x006cd9f0: rIFileDialog::Advise
0x006cda26: IFileDialog::QI(IFileDialogCustomize)
0x006cda8e: IFileDialog::GetFileTypeIndex
0x006cdae6: IFileDialog::Unadvise
0x006ce0f4: wxStaticText
0x006d076c: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006d07e4: DestroyWindow
0x006d0800: wxWindow
0x006d0830: can't create wxWindow without parent
0x006d087e: dSetWindowLong(GWL_ID)
0x006d08ba: "can't set focus to invalid window
0x006d0900: SetFocus
0x006d0936: invalid window show effect
0x006d096c: unknown window show effect
0x006d09a2: AnimateWindow
0x006d09ce: ReleaseCapture
0x006d09f6: rSetCursorPos
0x006d0a34: "Unknown touch event mask bit specified
0x006d0aa0: sno HWND in GetScrollPos
0x006d0ae0: SetScrollPos: no HWND
0x006d0b34: invalid window
0x006d0b6a: csubclassing window twice?
0x006d0bb4: invalid HWND in SubclassWin
0x006d0bf8: ninvalid HWND in UnsubclassWin
0x006d0c44: ewxTLWHiddenParent
0x006d0c7a: SetWindowPos
0x006d0c94: msw.window.no-clip-children
0x006d0cd6: eunknown border style
0x006d0d0a: UpdateWindow
0x006d0d30: gDeferWindowPos
0x006d0d6c: MoveWindow
0x006d0db2: "invalid font in GetTextExtent()
0x006d0e5a: eEDIT
0x006d0e92: attempt to add a NULL hwnd to window list ignored
0x006d0ef6: HWND %p already associated with another window (%s)
0x006d0fd4: CreateWindowEx("%s", flags=%08lx, ex=%08lx, title-len=%zu)
0x006d1054: rUnknown WM_POWERBROADCAST(%zd) event
0x006d116c: "MSWOnDrawItem: bad wxMenuItem pointer
0x006d11cc: MSWOnMeasureItem: bad wxMenuItem pointer
0x006d1280: swxBITMAP_STD_COLOURS
0x006d12e8: Xforgot to update wxBITMAP_STD_COLOURS!
0x006d1348: runsupported theme colour
0x006d13a4: tCreateRectRgn
0x006d13c2: GetUpdateRgn
0x006d1416: kSetting erase background hook twice?
0x006d1462: Resetting erase background which was not set?
0x006d14d2: dSetBrushOrgEx(bg brush)
0x006d1520: BeginDeferWindowPos
0x006d1566: PShouldn't be called
0x006d1590: EndDeferWindowPos
0x006d15c0: unexpected WM_SIZE parameter
0x006d15fa: comctl32.dll
0x006d1614: _TrackMouseEvent
0x006d1648: SystemParametersInfo(GETWHEELSCROLLLINES)
0x006d171c: GetMenuItemInfo
0x006d1792: registering handler for the same message twice
0x006d1842: "unregistering non-registered handler?
0x006d1b2e: 4can't use wxGetKeyState() for mouse buttons
0x006d1b98: kSetWindowsHookEx(wxKeyboardHook)
0x006d1bec: RegisterHotKey
0x006d1c1c: UnregisterHotKey
0x006d1c3e: wxIdleWakeUpModule
0x006d1d06: GetWindowRect
0x006d1d22: user32.dll
0x006d1d58: nCan't set layout direction for invalid window
0x006d1db6: Invalid layout direction
0x006d1e08: GetClientRect
0x006d1e60: size of this DC hadn't been set and is unknown
0x006d1ed6: SetWindowsHookEx(WH_GETMESSAGE)
0x006d2ab0: lwxWindowBase
0x006d2ae0: wxWindow
0x006d3120: window-default-variant
0x006d31d4: invalid id value
0x006d320e: flags with 0 value can't be toggled
0x006d32fc: any pushed event handlers must have been removed
0x006d337a: 0children not destroyed
0x006d33d6: child didn't remove itself using RemoveChild()
0x006d345c: "this method only implements centering child windows
0x006d34ec: Unknown border style.
0x006d352c: unexpected window variant
0x006d35f8: "min width/height must be less than max width/height!
0x006d36aa: can't add a NULL child
0x006d36fc: )AddChild() called twice
0x006d3738: dcan't remove a NULL child
0x006d3788: Can't use window as its own parent
0x006d3c30: dwhere has the event handler gone?
0x006d3c94: lwe have invalid explicit bg colour?
0x006d3e4a: we have invalid explicit font?
0x006d3eac: scaret should be created associated to this window
0x006d3f3e: rAdding a window to the same sizer twice?
0x006d3fa8: Adding a window already in a sizer, detach it first!
0x006d4026: sConstraints not satisfied for %s.
0x006d4082: "Must have TLW parent
0x006d40e0: emousecapture
0x006d40fc: CaptureMouse(%p)
0x006d412e: )recursive CaptureMouse call?
0x006d41a2: ReleaseMouse(%p)
0x006d41c4: recursive ReleaseMouse call?
0x006d430a: ?After ReleaseMouse() mouse is captured by %p
0x006d4382: GetPrev/NextSibling() don't work for TLWs!
0x006d43da: "window not a child of its parent?
0x006d4432: MoveBefore/AfterInTabOrder() don't work for TLWs!
0x006d4496: MoveBefore/AfterInTabOrder(): win is not a sibling
0x006d497c: unbalanced wxRecursionGuards!?
0x006d49da: window that captured the mouse didn't process wxEVT_MOUSE_CAPTURE_LOST
0x006d4a98: shouldn't be used unless ShouldPropagate()!
0x006d4bc8: wABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz
0x006d5324: wxTaskBarIcon
0x006d544a: nIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006d5538: "can't be used before the icon is created
0x006d558c: Shell_NotifyIcon(NIM_SETVERSION)
0x006d55ce: Shell_NotifyIcon(NIM_MODIFY)
0x006d5614: Shell_NotifyIcon(NIM_DELETE)
0x006d5664: taskbar icon not initialized
0x006d569e: TaskbarCreated
0x006d56bc: wxTaskBarIconMessage
0x006d646a: must be overridden if called
0x006d6624: all bundle bitmaps must be valid
0x006d66a2: dshould use original bitmap
0x006d6c60: eIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006d6cda: EnableMenuItem
0x006d6d0c: "only checkable items may be checked
0x006d6d62: "menuitem not found in the menu items list?
0x006d6dcc: Menu radio item not part of radio group?
0x006d6e3a: invalid ::CheckMenuRadioItem() parameter(s)
0x006d6e92: CheckMenuRadioItem
0x006d6eb8: CheckMenuItem() failed, item not in the menu?
0x006d6f22: GetMenuItemInfo
0x006d6f42: SetMenuItemInfo
0x006d6f7e: Non unique menu item ID?
0x006d6fbe: MENU
0x006d700e: SystemParametersInfo(SPI_GETNONCLIENTMETRICS)
0x006d7210: wxClipboardEvent
0x006d7232: wxClipboardModule
0x006d7410: wxHyperlinkCtrl
0x006d7430: wxHyperlinkEvent
0x006d74a6: Both URL and label are empty ?
0x006d74f4: Specify exactly one align flag!
0x006d76c4: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006d773c: SetWindowRgn
0x006d7f18: Shaped windows must be created with the wxFRAME_SHAPED style.
0x006d8338: wxTextEntryDialog
0x006d835c: wxPasswordEntryDialog
0x006d9638: wxTLWHiddenParentModule
0x006d96ac: "wxFRAME_FLOAT_ON_PARENT but no parent?
0x006d970a: Failed to create dialog. Incorrect DLGTEMPLATE?
0x006d9778: rCan't create dialog using memory template
0x006d97ce: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006d9846: MoveWindow
0x006da0d4: nGetWindowPlacement
0x006da1b8: nEnableMenuItem(SC_CLOSE)
0x006da1ec: DrawMenuBar
0x006da216: GetSystemMenu()
0x006da242: focus
0x006da24e: wxTLW %p activated.
0x006da276: wxTLW %p deactivated, last focused: %p.
0x006da2ce: DestroyWindow(hidden TLW parent)
0x006da310: UnregisterClass("wxTLWHiddenParent")
0x006da35a: wxTLWHiddenParent
0x006da384: DRegisterClass("wxTLWHiddenParent")
0x006da3cc: CreateWindow(hidden TLW parent)
0x006da790: wxTopLevelWindow
0x006db8ce: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006db946: GetWindowPlacement
0x006dba7e: rxywh
0x006dbaae: SetWindowPlacement
0x006dbad4: SetWindowPlacement (2nd time)
0x006dbce0: wxSystemSettingsModule
0x006dbd66: "failed to get LOGFONT
0x006dbd94: stock font not found
0x006dbe60: "invalid metric
0x006dbf34: unknown system feature
0x006dbf76: tIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006dbff0: SystemParametersInfo(SPI_GETICONTITLELOGFONT
0x006dc11e: sSystemParametersInfo(SPI_GETNONCLIENTMETRICS)
0x006dc2c0: wxOleInitModule
0x006dc2e0: wxApp
0x006dc32e: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006dc3a6: RegisterClass(%s)
0x006dc3e0: sUnregisterClass(%s)
0x006dc650: comctl32.dll
0x006dc66a: InitCommonControlsEx
0x006dc694: InitializeFlatSB
0x006dc8ce: FreeConsole
0x006dc9dc: kernel32.dll
0x006dca36: GetConsoleScreenBufferInfo
0x006dca6c: ReadConsoleOutputCharacterA
0x006dcaaa: cmd.exe
0x006dcaca: yfailed getting history?
0x006dcb9e: 1shouldn't be called if not initialized
0x006dcc1a: should only be called if Init() returned true
0x006dcc76: SetConsoleCursorPosition
0x006dcca8: FillConsoleOutputCharacter
0x006dccde: WriteConsole
0x006dcd26: call to Veto() ignored (can't veto this event)
0x006dcd84: DllGetVersion
0x006dd1a4: wxIcon
0x006dd25c: pIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006dd2d6: CreateIconIndirect
0x006dd412: "must be implemented if used
0x006dd5a2: tIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006dd61c: CreatePopupMenu
0x006dd776: uDestroyMenu
0x006dd7b6: invalid submenu
0x006dd7e8: Inserting non-radio item inside a radio group?
0x006dd846: InsertMenuItem()
0x006dd868: SetMenuInfo(MNS_NOCHECK)
0x006dd89a: InsertMenu[Item]()
0x006dd8da: Removing non radio button from radio group?
0x006dd932: RemoveMenu
0x006dd976: it's really needed?
0x006dd9a8: InsertMenu
0x006dd9be: ModifyMenu
0x006de1c8: can't refresh unattached menubar
0x006de212: CreateMenu
0x006de228: AppendMenu
0x006de27c: doesn't work with unattached menubars
0x006de2e0: invalid menu index
0x006de314: GetMenuItemInfo(menubar)
0x006de354: GetMenuState
0x006de37c: invalid menu index in wxMenuBar::GetMenuLabel
0x006de3f6: "can't append invalid menu to menubar
0x006de4a0: Item already inserted inside another range
0x006de52e: SetMenuItemInfo
0x006de5e0: euninitialized iterator
0x006de768: TaskbarButtonCreated
0x006de792: user32.dll
0x006df122: "failed to create menu bar
0x006df16c: rIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006df1e6: SetMenu
0x006df220: No open menus?
0x006df23e: Unexpected menu event type
0x006df2e8: GetWindowRect
0x006df818: wxImage
0x006df834: "NULL data in wxImage::Create
0x006df880: invalid image
0x006df8da: unable to create image
0x006df91c: unable to create alpha channel
0x006df98a: invalid new image size
0x006df9dc: "invalid old image size
0x006dfa0c: HotSpotX
0x006dfa1e: HotSpotY
0x006dfbbe: "invalid subimage size
0x006dfc36: "invalid size
0x006dfd1e: invalid image coordinates
0x006dfda8: invalid bounding rectangle
0x006dfe1a: "no alpha channel
0x006dfe60: image already has an alpha channel
0x006dfeb2: image already doesn't have an alpha channel
0x006e0068: .FileName
0x006e00f0: .MaxWidth
0x006e0104: MaxHeight
0x006e0118: OriginalWidth
0x006e0134: OriginalHeight
0x006e020e: rAdding duplicate image handler for '%s'
0x006e026c: rInserting duplicate image handler for '%s'
0x006e03bc: 0wxImageHandler
0x006e0432: NULL pointer
0x006e044c: ResolutionX
0x006e0464: ResolutionY
0x006e047c: Resolution
0x006e0492: ResolutionUnit
0x006e04b0: wxImageModule
0x006e06ee: twxArrayString: index out of bounds
0x006e079e: .Loading cursors from resources is not implemented.
0x006e0806: Invalid bitmap resource type.
0x006e0864: mIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006e08de: GetIconInfo
0x006e0a06: eDeleteObject(hDIB)
0x006e0a2e: Windows bitmap file
0x006e0a5e: image/x-bmp
0x006e0e64: wxBitmap
0x006e0e76: wxMask
0x006e0e84: wxBitmapHandler
0x006e0f08: bcan't copy bitmap locked for raw access!
0x006e0f72: deleting bitmap still selected into wxMemoryDC
0x006e0fd8: forgot to call wxBitmap::UngetRawData()!
0x006e102e: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006e10a6: DeleteObject(hbitmap)
0x006e110e: "invalid DIB in CopyFromDIB
0x006e1146: GetObject (@wxBitmapRefData::CopyFromDIB)
0x006e11b0: wxBitmap::CopyFromIconOrCursor - CreateBitmap
0x006e120c: wxBitmap::CopyFromIconOrCursor - BitBlt
0x006e126e: unknown wxBitmapTransparency value
0x006e136a: CreateBitmap
0x006e1398: invalid HDC in wxBitmap::Create()
0x006e13f4: "invalid bitmap size
0x006e141e: CreateCompatibleBitmap
0x006e145a: einvalid HDC in wxBitmap::CreateFromImage()
0x006e14c2: invalid image
0x006e14de: GetObject (@wxBitmap::CreateFromImage)
0x006e152c: Failed to create bitmap: no bitmap handler for type %ld defined.
0x006e1638: Invalid bitmap or bitmap region
0x006e1684: GetSubBitmap error
0x006e16aa: SelectObject(destBitmap)
0x006e16dc: BitBlt
0x006e176e: use wxQuantize if you want to convert color wxBitmap to mono
0x006e1800: "GetRawData() may be called only once
0x006e184c: failed to get DIBSECTION from a DIB?
0x006e1896: incorrect bitmap type in wxBitmap::GetRawData()
0x006e1938: Cannot retrieve the dimensions of the wxMask to copy
0x006e19ca: "can't create mask from invalid or not monochrome bitmap
0x006e1a4a: "invalid bitmap in wxMask::Create
0x006e1a8e: CreateCompatibleDC
0x006e1acc: )bitmap can't be selected in another DC
0x006e1b1c: SelectObject
0x006e1b4e: CreateBitmap(mask)
0x006e1b7c: pCreateBitmapIndirect
0x006e1bbe: "invalid bitmap in wxInvertMask
0x006e1c7e: hDeleteObject(hDIB)
0x006e1cc8: mGetIconInfo
0x006e2000: wxButton
0x006e203a: "button without top level window?
0x006e340c: ShowModal() can't be called twice
0x006e3464: EndModal() called for non modal dialog
0x006e34b2: SCROLLBAR
0x006e350c: Bug in wxWidgets: gripper should be at the bottom of Z-order
0x006e359e: shouldn't be called if we have no gripper
0x006e3630: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006e36a8: GetWindowRect
0x006e3a54: "must have valid parent for a control
0x006e3aa0: BUTTON
0x006e3ab2: Have not yet implemented bitmap button as BS_BITMAP button.
0x006e3b2a: Don't know what kind of button this is: id = %ld
0x006e3b8c: COMBOBOX
0x006e3b9e: EDIT
0x006e3ba8: LISTBOX
0x006e3bb8: SCROLLBAR
0x006e3bcc: MSCTLS_UPDOWN32
0x006e3bec: MSCTLS_TRACKBAR32
0x006e3c10: STATIC
0x006e3c1e: Please make SS_BITMAP statics into owner-draw buttons.
0x006e3c8c: Don't know how to convert from Windows class 
0x006e4058: wxWidgets Debug Alert
0x006e4084: A debugging check in this application has failed.
0x006e4fc2: "no preferred format
0x006e5004: "unsupported format in wxDataObjectComposite
0x006e59d8: wxCommandEvent
0x006e59f6: wxNotifyEvent
0x006e5a12: wxScrollEvent
0x006e5a2e: wxScrollWinEvent
0x006e5a50: wxMouseEvent
0x006e5a6a: wxKeyEvent
0x006e5a80: wxSizeEvent
0x006e5a98: wxPaintEvent
0x006e5ab2: wxNcPaintEvent
0x006e5ad0: wxEraseEvent
0x006e5aea: wxMoveEvent
0x006e5b02: wxFocusEvent
0x006e5b1c: wxChildFocusEvent
0x006e5b40: wxCloseEvent
0x006e5b5a: wxShowEvent
0x006e5b72: wxMaximizeEvent
0x006e5b92: wxIconizeEvent
0x006e5bb0: wxFullScreenEvent
0x006e5bd4: wxMenuEvent
0x006e5bec: wxJoystickEvent
0x006e5c0c: wxDropFilesEvent
0x006e5c2e: wxActivateEvent
0x006e5c4e: wxInitDialogEvent
0x006e5c72: wxSetCursorEvent
0x006e5c94: wxSysColourChangedEvent
0x006e5cc4: wxDisplayChangedEvent
0x006e5cf0: wxDPIChangedEvent
0x006e5d14: wxUpdateUIEvent
0x006e5d34: wxNavigationKeyEvent
0x006e5d5e: wxPaletteChangedEvent
0x006e5d8a: wxQueryNewPaletteEvent
0x006e5db8: wxWindowCreateEvent
0x006e5de0: wxWindowDestroyEvent
0x006e5e0a: wxHelpEvent
0x006e5e22: wxContextMenuEvent
0x006e5e48: wxMouseCaptureChangedEvent
0x006e5e7e: wxMouseCaptureLostEvent
0x006e5eae: wxClipboardTextEvent
0x006e5ed8: wxGestureEvent
0x006e5ef6: wxPanGestureEvent
0x006e5f1a: wxZoomGestureEvent
0x006e5f40: wxRotateGestureEvent
0x006e5f6a: wxTwoFingerTapEvent
0x006e5f92: wxLongPressEvent
0x006e5fb4: wxPressAndTapEvent
0x006e617c: "invalid parameter in wxMouseEvent::ButtonDClick
0x006e61ea: invalid parameter in wxMouseEvent::ButtonDown
0x006e6250: invalid parameter in wxMouseEvent::ButtonUp
0x006e62b0: invalid parameter in wxMouseEvent::Button
0x006e64fe: Null window given to wxEventBlocker
0x006e6568: Don't push other event handlers into a window managed by wxEventBlocker!
0x006e798e: Should be overridden if called
0x006e7dd0: wxFileDialogBase
0x006e7e2a: can't specify both wxFD_SAVE and wxFD_OPEN at once
0x006e7eea: wxFD_MULTIPLE or wxFD_FILE_MUST_EXIST can't be used with wxFD_SAVE
0x006e7fac: wxFD_OVERWRITE_PROMPT can't be used with wxFD_OPEN
0x006e8b84: )wxString: index out of bounds
0x006e8bfc: shouldn't be called twice
0x006e8d76: *.%s
0x006ea87e: wxApp::CreateTraits() failed?
0x006eab6e: Mismatch between the program and library build versions detected.
0x006eabf2: The library used %s,
0x006eac1c: and %s used %s.
0x006eac42: Call stack:
0x006eae4c: p[%02u] 
0x006eae6a: %-40s
0x006eafa0: 0unbalanced wxRecursionGuards!?
0x006eafe0: WXTRACE
0x006eaffa: Do you want to stop the program?
0x006eb03c: You can also choose [Cancel] to suppress further warnings.
0x006eb0b2: wxWidgets Debug Alert
0x006eb0de: %s(%d): assert "%s" failed
0x006eb114:  in 
0x006eb314: "unreachable code
0x006eb6e2: )you should call SetString() first
0x006eb750: "unexpected tokenizer mode
0x006eb7ae: dinvalid index
0x006ebb8a: trying to encode undefined Unicode character
0x006ebd3a: invalid encoding value in wxCSConv ctor
0x006ebd94: estrconv
0x006ebda6: creating conversion for %s
0x006ebddc: encoding "%s" is not supported by this system
0x006ec1da: nUnexpected NUL length %d
0x006ec7fc: bad key type.
0x006ec84c: invalid key type
0x006ec880: node doesn't belong to a list in IndexOf
0x006ec8ea: copying list which owns it's elements is a bad idea
0x006ec968: tlogic error in wxList::DoCopy
0x006ec9f2: need a key for the object to append
0x006eca84: "can't append object with numeric key to this list
0x006ecb36: can't append object with string key to this list
0x006ecba0: need a key for the object to insert
0x006ecc0e: "can't insert before a node from another list
0x006ecc70: invalid index in wxListBase::Item
0x006eccd8: this list is not keyed on the type of this key
0x006ecd46: "detaching NULL wxNodeBase
0x006ecd94: detaching node which is not from this list
0x006ecfb0: wxEvtHandler
0x006ecfca: wxEvent
0x006ecfda: wxIdleEvent
0x006ecff2: wxThreadEvent
0x006ed1be: use ProcessEvent() in main thread
0x006ed3ac: caller should check that we have dynamic events
0x006ed462: can't have both object and void client data
0x006ed4ca: tthis window doesn't have object client data
0x006ed56a: athis window doesn't have void client data
0x006ed90c: wxFileSystemHandler
0x006ed934: filesys.no-mimetypesmanager
0x006ed96c: image/jpeg
0x006ed982: JPEG image (from fallback)
0x006ed9c0: jpeg
0x006ed9d2: JPEG
0x006ed9dc: image/gif
0x006ed9f0: GIF image (from fallback)
0x006eda34: image/png
0x006eda48: PNG image (from fallback)
0x006eda8c: image/bmp
0x006edaa0: windows bitmap image (from fallback)
0x006edafa: text/html
0x006edb0e: HTML document (from fallback)
0x006edb52: html
0x006edb64: HTML
0x006edb6e: file
0x006edb78: file:
0x006edb98: wxFileSystem
0x006edbb2: wxFSFile
0x006edc3e: empty file name in wxFileSystem::FindFileInPath
0x006edc9e: wxFileSystemModule
0x006ee0a0: binary search broken
0x006ee0e4: search parameters ignored for auto sorted array
0x006ee15e: "bad index in wxArrayString::Insert
0x006ee1c8: array size overflow in wxArrayString::Insert
0x006ee240: bad index in wxArrayString::Remove
0x006ee2a6: removing too many elements in wxArrayString::Remove
0x006ee324: "removing inexistent element in wxArrayString::Remove
0x006ee3a4: can't use this method with sorted arrays
0x006ee422: twxArrayString: index out of bounds
0x006ee9ec: Too many flags specified for a single conversion specifier!
0x006eef70: wxMemoryInputStream
0x006ef058: pwxMemoryOutputStream
0x006ef136: "must have buffer to CopyTo
0x006ef418: wxTimerEvent
0x006ef542: No timer implementation for this platform
0x006ef5a6: "uninitialized timer
0x006ef5f6: "wxTimer::Notify() should be overridden.
0x006ef6e0: wxObject
0x006ef720: 1wxClassInfo::Register() reentrance
0x006ef788: 0Class "%s" already in RTTI table - have you used wxIMPLEMENT_DYNAMIC_CLASS() multiple times or linked some object file twice)?
0x006ef8ea: 1wxObject::AllocExclusive() failed.
0x006ef952: CreateRefData() must be overridden if called!
0x006ef9bc: CloneRefData() must be overridden if called!
0x006efb34: "out of memory in wxString::operator+
0x006efb94: Non-ASCII value passed to FromAscii().
0x006efbe4: dout of memory in wxString::Mid
0x006efc28: tout of memory in wxString::Right
0x006efc72: out of memory in wxString::Left
0x006efcca: "wxString::Replace(): invalid parameter
0x006efd40: )invalid base
0x006efd64: NULL output pointer
0x006eff84: wxAnyValueTypeGlobalsManager
0x006effbe: true
0x006effd0: false
0x006f1404: \*.\
0x006f1410: POSIXLY_CORRECT
0x006fba12: "hash table too big?
0x006fba6e: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006fbae6: CreateMutex()
0x006fbb14: CloseHandle(mutex)
0x006fbb44: tWaitForSingleObject() returned WAIT_ABANDONED
0x006fbbb4: impossible return value in wxMutex::Lock
0x006fbc06: WaitForSingleObject(mutex)
0x006fbc44: ReleaseMutex()
0x006fbc74: lCreateSemaphore()
0x006fbcb0: CloseHandle(semaphore)
0x006fbce8: tWaitForSingleObject(semaphore)
0x006fbd2e: ReleaseSemaphore
0x006fbdee: invalid value of thread priority parameter
0x006fbe86: dCreate()ing thread twice?
0x006fbf24: unexpected result of MsgWaitForMultipleObject
0x006fbf80: GetExitCodeThread
0x006fbfc4: xGetThreadContext
0x006fc04e: should only be called from the main thread
0x006fc0a4: GetProcessAffinityMask
0x006fc0d2: bad level %u in wxThread::SetConcurrency()
0x006fc128: SetProcessAffinityMask
0x006fc1c8: "thread may only be started once after Create()
0x006fc23e: wxThread::Wait(): can't wait for detached thread
0x006fc2fc: wxThreadModule
0x006fc3d0: tTlsFree failed.
0x006fc418: )main thread doesn't want to block in wxMutexGuiEnter()!
0x006fc4b2: 0calling wxMutexGuiLeave() without entering it first?
0x006fc546: )only main thread may call wxMutexGuiLeaveOrEnter()!
0x006fc5b0: Failed to wake up main thread: PostThreadMessage(WM_NULL) failed with error 0x%08lx (%s).
0x006fc68e: "wxMutex::Lock(): not initialized
0x006fc6d8: kwxMutex::TryLock(): not initialized
0x006fc722: wxMutex::Unlock(): not initialized
0x006fc768: wxCondition::Wait(): not initialized
0x006fc7ba: wxCondition::Signal(): not initialized
0x006fc810: twxCondition::Broadcast(): not initialized
0x006fc866: wxSemaphore::Wait(): not initialized
0x006fc8b6: twxSemaphore::TryWait(): not initialized
0x006fc908: wxSemaphore::WaitTimeout(): not initialized
0x006fc960: wxSemaphore::Post(): not initialized
0x006fca5e: CloseHandle(thread)
0x006fcc0e: %s: %s
0x006fcd52: Fatal Error
0x006fcf4c: FormatMessage failed with error 0x%lx
0x006fcf98: unknown error 0x%lx
0x006fd6b6: =CHARSET
0x006fd750: wxstd
0x006fd76a: no suitable translation for domain '%s' found
0x006fd7c6: adding '%s' translation for domain '%s' (msgid language '%s')
0x006fd842: not using translations for domain '%s' with msgid language '%s'
0x006fdaee: .Using catalog "%s".
0x006fdc00: swxTranslationsModule
0x006fe124: "Unknown path format
0x006fe182: the file name shouldn't contain the path
0x006fe1f8: )the path shouldn't contain file name nor extension
0x006fe27c: rIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006fe2f6: GetTempPath
0x006fe314: rSHFileOperation
0x006fe33a: ~lnk
0x006fe354: unreachable
0x006fe37c: s\/:"<>|
0x006fe39e: sUnknown wxPATH_XXX style
0x006fe3e4: tempty directory passed to wxFileName::InsertDir()
0x006fe44a: invalid directory component in wxFileName
0x006fe4a4: hUnexpected path format
0x006fe52c: file://
0x006fe53c: file:
0x006fe630: 0GetTempFileName
0x006fe6da: twxArrayString: index out of bounds
0x006fe748: wxString: index out of bounds
0x006fe7cc: \\?\Volume{
0x006fe904: )invalid wxDateTime
0x006fead4: rCan't get direct access to initialized pointer
0x006feb80: dir must be opened before traversing it
0x006febe2: unexpected OnDir() return value
0x006fec22: unexpected OnOpenError() return value
0x006fec82: Eunexpected OnFile() return value
0x006fecd8: "NULL pointer in wxDir::GetAllFiles
0x006fefe8: wxXmlDocument
0x006ff31c:  PUBLIC "
0x006ff334:  SYSTEM
0x006ff3a8: UTF-8
0x006ff438: <?xml version="%s" encoding="%s"?>
0x006ff47e: <!DOCTYPE 
0x006ff5fe: dcdata
0x006ff618: dcomment
0x006ff640:  encoding=
0x006ff656: version=
0x006ff69e: <![CDATA[
0x006ff6f4: &lt;
0x006ff6fe: &gt;
0x006ff708: &amp;
0x006ff714: &#xD;
0x006ff720: &quot;
0x006ff72e: &#x9;
0x006ff73a: &#xA;
0x006ff8f8: Cannot search for directories; only for files
0x006ffb88: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x006ffc00: GetWindowsDirectory
0x006ffc4c: empty file name in wxFindFileInPath
0x006ffcbe: "missing '|' in the wildcard string!
0x006ffd52: wxArrayString: index out of bounds
0x006ffe16: invalid wxDateTime
0x006fff68: wxXmlResourceHandler
0x006fffc6: "SetImpl() must have been called!
0x0070000a: wxCLIP_CHILDREN
0x0070002a: wxSIMPLE_BORDER
0x0070004a: wxBORDER_SIMPLE
0x0070006a: wxSUNKEN_BORDER
0x0070008a: wxBORDER_SUNKEN
0x007000aa: wxDOUBLE_BORDER
0x007000ca: wxBORDER_DOUBLE
0x007000ea: wxBORDER_THEME
0x00700108: wxRAISED_BORDER
0x00700128: wxBORDER_RAISED
0x00700148: wxSTATIC_BORDER
0x00700168: wxBORDER_STATIC
0x00700188: wxNO_BORDER
0x007001a0: wxBORDER_NONE
0x007001bc: wxBORDER_DEFAULT
0x007001de: wxTRANSPARENT_WINDOW
0x00700208: wxWANTS_CHARS
0x00700224: wxTAB_TRAVERSAL
0x00700244: wxNO_FULL_REPAINT_ON_RESIZE
0x0070027c: wxFULL_REPAINT_ON_RESIZE
0x007002ae: wxVSCROLL
0x007002c2: wxHSCROLL
0x007002d6: wxALWAYS_SHOW_SB
0x007002f8: wxWS_EX_BLOCK_EVENTS
0x00700322: wxWS_EX_VALIDATE_RECURSIVELY
0x0070035c: wxWS_EX_TRANSIENT
0x00700380: wxWS_EX_CONTEXTHELP
0x007003a8: wxWS_EX_PROCESS_IDLE
0x007003d2: wxWS_EX_PROCESS_UI_UPDATES
0x00700650: wxDateTimeHolidaysModule
0x007006f8: strftime() failed
0x0070075a: "Invalid month value
0x007007b0: logic error
0x0070080a: eunknown time zone
0x0070083c: unknown calendar
0x0070087a: output parameter must be non-null
0x007008fa: TODO
0x00700904: unsupported calendar
0x00700968: invalid month
0x00700b3c: invalid weekday
0x00700c32: eWET
0x00700c3c: WEST
0x00700c5e: CEST
0x00700c82: Tno last Sunday in March?
0x00700cb6: no first Sunday in April?
0x00700cea: no second Sunday in March?
0x00700d28: Tno last Sunday in October?
0x00700d60: no first Sunday in November?
0x00700d9c: tmktime() failed
0x00700dfc: Invalid time in wxDateTime::Set()
0x00700e46: wxLocaltime_r() failed
0x00700eaa: Invalid date in wxDateTime::Set()
0x00700ef8: time can't be represented in DOS format
0x00700f56: )invalid wxDateTime
0x00700f88: JDN out of range
0x00700fde: )invalid day
0x0070104c: )Add(wxDateSpan) shouldn't modify time
0x007010e0: invalid week number: weeks are counted from 1
0x007011d6: "invalid year day
0x00701254: country support not implemented
0x007012aa: invalid date range in GetHolidaysInRange
0x00701354: yinvalid week day
0x0070153e: date out of range - can't convert to JDN
0x007015d6: )invalid broken down date/time
0x00701674: wxLongLong to long conversion loss of precision
0x00701800: can't allocate less than 1 id
0x00701882: dcan't unreserve less than 1 id
0x007018f8: id count already 0
0x00701930: reserve id being decreased
0x00701972: tid should first be reserved
0x007019f6: "invalid id range
0x00701a42: id already in use or already reserved
0x00701ada: "id already in use or not reserved
0x00701b38: %d-%d
0x00701b44: wxArtProvider
0x00701be6: no wxArtProvider exists
0x00701c30: wxArtProviders stack is empty
0x00701c94: invalid 'client' parameter
0x00701d10: incorrect message box icon flags
0x00701d8e: wxArtProviderModule
0x00701e4e: wxString: index out of bounds
0x0070217e: it's a bad idea to copy this buffer
0x007021de: "can't flush this buffer
0x0070221a: "should have a stream in wxStreamBuffer
0x00702292: should have the stream and the buffer in wxStreamBuffer
0x00702324: NULL data pointer
0x00702358: "can't read from this buffer
0x007023aa: can't write to this buffer
0x007023f6: "can't read from that buffer
0x00702444: "invalid seek mode
0x0070246a: wxStreamBase
0x007024f0: wxInputStream
0x0070261e: Seeking in stream which has data written back to it.
0x00702688: wxOutputStream
0x00702748: wxCountingOutputStream
0x00702820: kwxFilterInputStream
0x007028f8: wxFilterOutputStream
0x007029c8: wxFilterClassFactoryBase
0x007029fa: wxFilterClassFactory
0x00702ae6: wxBufferedInputStream needs buffer
0x00702be4: rwxBufferedOutputStream needs buffer
0x00704510: wxIconBundle
0x0070463e: .#%u
0x00704648: Failed to create icon from resource with id %u.
0x007046a8: Failed to load icon with id %u for group icon resource '%s'.
0x00704776: MMust have valid size if not using FALLBACK_SYSTEM
0x007047e8: "invalid icon
0x00704826: "invalid index
0x007049e0: wxImageList
0x00704a4e: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00704ac6: ImageList_Create()
0x00704b44: invalid image list
0x00704ba8: ImageList_Replace()
0x00704bd8: ImageList_Remove()
0x00704c14: invalid wxDC in wxImageList::Draw
0x00704c58: ImageList_Draw()
0x00704c98: DeleteObject(hDIB)
0x00704cf0: initializing twice?
0x00704e90: wxConfigBase
0x00704ffa: create wxApp before calling this
0x00705048: wxConfig::Read(): NULL parameter
0x0070509a: int overflow in wxConfig::Read
0x007050fe: "float overflow in wxConfig::Read
0x0070517a: float underflow in wxConfig::Read
0x00705420: wxFontEnumCacheCleanupModule
0x0070545a: Ms Shell Dlg
0x00705474: Ms Shell Dlg 2
0x0070564e: GUI code requested a wxFontMapper but we only have a wxFontMapperBase.
0x0070576a: Charsets
0x007057de: "bad pointer in GetAltForEncoding
0x00705844: corrupted config data: string '%s' is not a valid font encoding info
0x00705a56: "wxFontEncoding::GetAltForEncoding(): NULL pointer
0x00705acc: Encodings
0x00705ae0: none
0x007065d6: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0070664e: CreateFont
0x0070666a: DeleteObject(font)
0x00706748: y%d;%s;%ld;%ld;%ld;%ld;%ld;%d;%d;%d;%d;%d;%d;%d;%d;%s
0x00706916: "invalid font
0x007069b2: GetTextMetrics
0x007069d0: wxPrivateFontsListModule
0x00706e9c: %s-%d-%s-%.2f-%d
0x00706ece: wxFont
0x00706f2c: "can't set default encoding to wxFONTENCODING_DEFAULT
0x007070ca: )This should be a non-empty string!
0x00707124: Derived class should have created the wxNativeFontInfo!
0x007071b6: yinvalid font
0x0070740c: Falling back to '%s'
0x0070745e: unknown font weight
0x007074da: unknown font style
0x00707562: ';, 
0x0070756c:  underlined
0x00707584: strikethrough
0x007075ac: hunderlinedstrikethrough
0x007075de: thin
0x007075ee: extra
0x007075fa: ultra
0x00707606: semi
0x00707610: demi
0x0070761a: extralight
0x0070763c: light
0x0070764c: tnormal
0x00707664: medium
0x0070767a: semibold
0x00707696: bold
0x007076a6: extrabold
0x007076c2: dheavy
0x007076d4: yextraheavy
0x007076f8: italic
0x00707720: "NULL output parameter
0x007079f8: wxArrayString: index out of bounds
0x00707b8c: wxModule
0x00707bc8: smodule
0x00707bd8: Registering module %s
0x00707c9c: Module "%s" initialized
0x00707cde: Cleanup module %s
0x00707d28: not initialized module being cleaned up
0x00708058: wxAnimationCtrlXmlHandler
0x007080d8: wxAC_NO_AUTORESIZE
0x007080fe: wxAC_DEFAULT_STYLE
0x00708132: lpos
0x00708154: inactive-bitmap
0x00708180: CwxAnimationCtrl
0x007081a2: wxGenericAnimationCtrl
0x00708412: lCouldn't add two unknown controls to the same container!
0x00708486: wxUnknownWidgetXmlHandler
0x00708508: wxNO_FULL_REPAINT_ON_RESIZE
0x00708562: 'unknown' controls can't be subclassed, use wxXmlResource::AttachUnknownControl
0x0070860a: style
0x00708616: unknown
0x00708daa: g_container
0x00708dfc: shouldn't be called twice
0x00708ed0: wxBitmapXmlHandler
0x00708f4c: CwxBitmap
0x00708f60: wxIconXmlHandler
0x00708fd0: wxIcon
0x00709040: wxDialogXmlHandler
0x007090b0: wxSTAY_ON_TOP
0x007090cc: wxCAPTION
0x007090e0: wxDEFAULT_DIALOG_STYLE
0x0070910e: wxSYSTEM_MENU
0x0070912a: wxRESIZE_BORDER
0x0070914a: wxCLOSE_BOX
0x00709162: wxDIALOG_NO_PARENT
0x00709188: wxTAB_TRAVERSAL
0x007091a8: wxWS_EX_VALIDATE_RECURSIVELY
0x007091e2: wxDIALOG_EX_METAL
0x00709206: wxMAXIMIZE_BOX
0x00709224: wxMINIMIZE_BOX
0x00709242: wxFRAME_SHAPED
0x00709260: wxDIALOG_EX_CONTEXTHELP
0x00709290: hidden
0x0070929e: title
0x007092aa: style
0x007092d2: centered
0x007092e4: wxDialog
0x00709410: wxPanelXmlHandler
0x00709480: wxTAB_TRAVERSAL
0x007094a0: wxWS_EX_VALIDATE_RECURSIVELY
0x007094da: hidden
0x007094f0: style
0x007094fc: wxPanel
0x00709550: tshouldn't be called twice
0x007096e4: wxSizerXmlHandler
0x00709760: wxHORIZONTAL
0x0070977a: wxVERTICAL
0x00709790: wxLEFT
0x0070979e: wxRIGHT
0x007097ae: wxTOP
0x007097ba: wxBOTTOM
0x007097cc: wxNORTH
0x007097dc: wxSOUTH
0x007097ec: wxEAST
0x007097fa: wxWEST
0x00709808: wxALL
0x00709814: wxGROW
0x00709822: wxEXPAND
0x00709834: wxSHAPED
0x00709846: wxSTRETCH_NOT
0x00709862: wxALIGN_CENTER
0x00709880: wxALIGN_CENTRE
0x0070989e: wxALIGN_LEFT
0x007098b8: wxALIGN_TOP
0x007098d0: wxALIGN_RIGHT
0x007098ec: wxALIGN_BOTTOM
0x0070990a: wxALIGN_CENTER_HORIZONTAL
0x0070993e: wxALIGN_CENTRE_HORIZONTAL
0x00709972: wxALIGN_CENTER_VERTICAL
0x007099a2: wxALIGN_CENTRE_VERTICAL
0x007099d2: wxFIXED_MINSIZE
0x007099f2: wxRESERVE_SPACE_EVEN_IF_HIDDEN
0x00709a42: wxEXTEND_LAST_ON_EACH_LINE
0x00709a78: wxREMOVE_LEADING_SPACES
0x00709aa8: sizeritem
0x00709abc: spacer
0x00709aca: wxBoxSizer
0x00709ae0: wxStaticBoxSizer
0x00709b02: wxGridSizer
0x00709b1a: wxFlexGridSizer
0x00709b3a: wxGridBagSizer
0x00709b58: wxWrapSizer
0x00709b8a: object
0x00709b98: object_ref
0x00709c3a: minsize
0x00709c4a: empty_cellsize
0x00709c68: hideitems
0x00709c7c: growablerows
0x00709c96: growablecols
0x00709cb0: orient
0x00709cbe: windowlabel
0x00709cd6: label
0x00709e12: flexibledirection
0x00709e36: wxBOTH
0x00709e5c: nonflexiblegrowmode
0x00709e84: wxFLEX_GROWMODE_NONE
0x00709eae: wxFLEX_GROWMODE_SPECIFIED
0x00709ee2: wxFLEX_GROWMODE_ALL
0x00709f84: ncellpos
0x00709f96: cellspan
0x0070a1ba: option
0x0070a1c8: border
0x0070a1d6: ratio
0x0070a1e2: wxStdDialogButtonSizerXmlHandler
0x0070a270: wxStdDialogButtonSizer
0x0070a31c: rbutton
0x0070a390: invalid value for wxBoxSizer orientation
0x0070a588: wxFrameXmlHandler
0x0070a5f8: wxSTAY_ON_TOP
0x0070a614: wxCAPTION
0x0070a628: wxDEFAULT_DIALOG_STYLE
0x0070a656: wxDEFAULT_FRAME_STYLE
0x0070a682: wxSYSTEM_MENU
0x0070a69e: wxRESIZE_BORDER
0x0070a6be: wxCLOSE_BOX
0x0070a6d6: wxFRAME_NO_TASKBAR
0x0070a6fc: wxFRAME_SHAPED
0x0070a71a: wxFRAME_TOOL_WINDOW
0x0070a742: wxFRAME_FLOAT_ON_PARENT
0x0070a772: wxMAXIMIZE_BOX
0x0070a790: wxMINIMIZE_BOX
0x0070a7ae: wxTAB_TRAVERSAL
0x0070a7ce: wxWS_EX_VALIDATE_RECURSIVELY
0x0070a808: wxFRAME_EX_METAL
0x0070a82a: wxFRAME_EX_CONTEXTHELP
0x0070a858: hidden
0x0070a866: title
0x0070a872: style
0x0070a89a: centered
0x0070a8ac: wxFrame
0x0070a9d0: wxScrolledWindowXmlHandler
0x0070aa50: wxHSCROLL
0x0070aa64: wxVSCROLL
0x0070aa78: wxTAB_TRAVERSAL
0x0070aa98: wxWS_EX_VALIDATE_RECURSIVELY
0x0070aad2: hidden
0x0070aae8: style
0x0070aaf4: scrollrate
0x0070ab0a: wxScrolledWindow
0x0070ab70: tshouldn't be called twice
0x0070ad00: wxActivityIndicatorXmlHandler
0x0070ad88: hidden
0x0070ad9e: style
0x0070adaa: running
0x0070adba: wxActivityIndicator
0x0070af08: wxBannerWindowXmlHandler
0x0070af88: hidden
0x0070af96: direction
0x0070afb2: style
0x0070afbe: gradient-start
0x0070afdc: gradient-end
0x0070b03c: .bitmap
0x0070b0ae: .title
0x0070b0bc: message
0x0070b0cc: wxBannerWindow
0x0070b220: wxBitmapComboBoxXmlHandler
0x0070b2a0: wxCB_SORT
0x0070b2b4: wxCB_READONLY
0x0070b2d0: ownerdrawnitem
0x0070b322: xbitmap
0x0070b33e: Cselection
0x0070b354: hidden
0x0070b362: value
0x0070b376: style
0x0070b382: object
0x0070b390: wxBitmapComboBox
0x0070b4e8: wxBitmapButtonXmlHandler
0x0070b568: wxBU_AUTODRAW
0x0070b584: wxBU_LEFT
0x0070b598: wxBU_RIGHT
0x0070b5ae: wxBU_TOP
0x0070b5c0: wxBU_BOTTOM
0x0070b5d8: wxBU_EXACTFIT
0x0070b600: Chidden
0x0070b614: ebitmap
0x0070b63c: style
0x0070b648: default
0x0070b684: rwxBitmapButton
0x0070b7c8: wxPropertySheetDialogXmlHandler
0x0070b858: wxSTAY_ON_TOP
0x0070b874: wxCAPTION
0x0070b888: wxDEFAULT_DIALOG_STYLE
0x0070b8b6: wxSYSTEM_MENU
0x0070b8d2: wxRESIZE_BORDER
0x0070b8f2: wxCLOSE_BOX
0x0070b90a: wxDIALOG_NO_PARENT
0x0070b930: wxTAB_TRAVERSAL
0x0070b950: wxWS_EX_VALIDATE_RECURSIVELY
0x0070b98a: wxDIALOG_EX_METAL
0x0070b9ae: wxMAXIMIZE_BOX
0x0070b9cc: wxMINIMIZE_BOX
0x0070b9ea: wxFRAME_SHAPED
0x0070ba08: wxDIALOG_EX_CONTEXTHELP
0x0070ba38: propertysheetpage
0x0070ba5c: hidden
0x0070ba6a: title
0x0070ba7e: style
0x0070ba9e: centered
0x0070bab0: buttons
0x0070bac0: wxOK
0x0070baca: wxCANCEL
0x0070badc: wxYES
0x0070bae8: wxNO
0x0070baf2: wxHELP
0x0070bb00: wxNO_DEFAULT
0x0070bb1a: wxPropertySheetDialog
0x0070bd00: wxButtonXmlHandler
0x0070bd70: wxBU_LEFT
0x0070bd84: wxBU_RIGHT
0x0070bd9a: wxBU_TOP
0x0070bdac: wxBU_BOTTOM
0x0070bdc4: wxBU_EXACTFIT
0x0070bde0: wxBU_NOTEXT
0x0070bdf8: hidden
0x0070be06: label
0x0070be1a: style
0x0070be26: default
0x0070be8e: swxButton
0x0070bfb8: wxCalendarCtrlXmlHandler
0x0070c038: wxCAL_SUNDAY_FIRST
0x0070c05e: wxCAL_MONDAY_FIRST
0x0070c084: wxCAL_SHOW_HOLIDAYS
0x0070c0ac: wxCAL_NO_YEAR_CHANGE
0x0070c0d6: wxCAL_NO_MONTH_CHANGE
0x0070c102: wxCAL_SEQUENTIAL_MONTH_SELECTION
0x0070c144: wxCAL_SHOW_SURROUNDING_WEEKS
0x0070c17e: hidden
0x0070c194: style
0x0070c1a0: wxCalendarCtrl
0x0070c2e0: wxCheckBoxXmlHandler
0x0070c358: wxCHK_2STATE
0x0070c372: wxCHK_3STATE
0x0070c38c: wxCHK_ALLOW_3RD_STATE_FOR_USER
0x0070c3ca: wxALIGN_RIGHT
0x0070c3e6: hidden
0x0070c3f4: label
0x0070c408: style
0x0070c47c: wxCheckBox
0x0070c640: wxCheckListBoxXmlHandler
0x0070c6c0: wxLB_SINGLE
0x0070c6d8: wxLB_MULTIPLE
0x0070c6f4: wxLB_EXTENDED
0x0070c710: wxLB_HSCROLL
0x0070c72a: wxLB_ALWAYS_SB
0x0070c748: wxLB_NEEDED_SB
0x0070c766: wxLB_SORT
0x0070c77a: wxCheckListBox
0x0070c798: content
0x0070c7a8: hidden
0x0070c7be: style
0x0070c7ca: item
0x0070c7d4: checked
0x0070c920: wxChoiceXmlHandler
0x0070c990: wxCB_SORT
0x0070c9a4: wxChoice
0x0070c9b6: selection
0x0070c9ca: content
0x0070c9da: hidden
0x0070c9f0: style
0x0070c9fc: item
0x0070cb18: wxChoicebookXmlHandler
0x0070cb98: wxBK_DEFAULT
0x0070cbb2: wxBK_LEFT
0x0070cbc6: wxBK_RIGHT
0x0070cbdc: wxBK_TOP
0x0070cbee: wxBK_BOTTOM
0x0070cc06: wxCHB_DEFAULT
0x0070cc22: wxCHB_LEFT
0x0070cc38: wxCHB_RIGHT
0x0070cc50: wxCHB_TOP
0x0070cc64: wxCHB_BOTTOM
0x0070cc7e: choicebookpage
0x0070cc9c: hidden
0x0070ccb2: style
0x0070ccbe: wxChoicebook
0x0070cd1c: tshouldn't be called twice
0x0070cdfc: Override this function!
0x0070dd18: wxCollapsiblePaneXmlHandler
0x0070dd98: wxCP_NO_TLW_RESIZE
0x0070ddbe: wxCP_DEFAULT_STYLE
0x0070dde4: panewindow
0x0070ddfa: object
0x0070de08: object_ref
0x0070de3c: hidden
0x0070de4a: label
0x0070de70: ypos
0x0070de7a: style
0x0070de86: collapsed
0x0070de9a: wxCollapsiblePane
0x0070df02: tshouldn't be called twice
0x0070ed08: wxColourPickerCtrlXmlHandler
0x0070ed90: wxCLRP_USE_TEXTCTRL
0x0070edb8: wxCLRP_SHOW_LABEL
0x0070eddc: wxCLRP_DEFAULT_STYLE
0x0070ee06: hidden
0x0070ee14: value
0x0070ee28: style
0x0070ee34: wxColourPickerCtrl
0x0070ee9e: tshouldn't be called twice
0x0070fc00: wxComboBoxXmlHandler
0x0070fc78: wxCB_SIMPLE
0x0070fc90: wxCB_SORT
0x0070fca4: wxCB_READONLY
0x0070fcc0: wxCB_DROPDOWN
0x0070fcdc: wxTE_PROCESS_ENTER
0x0070fd02: wxComboBox
0x0070fd18: selection
0x0070fd2c: content
0x0070fd3c: hidden
0x0070fd4a: value
0x0070fd5e: style
0x0070fd6a: item
0x0070fe88: wxComboCtrlXmlHandler
0x0070ff00: wxCB_SORT
0x0070ff14: wxCB_READONLY
0x0070ff30: wxTE_PROCESS_ENTER
0x0070ff56: wxCC_SPECIAL_DCLICK
0x0070ff7e: wxCC_STD_BUTTON
0x0070ff9e: wxComboCtrl
0x0070ffb6: hidden
0x0070ffc4: value
0x0070ffd8: style
0x00710108: wxCommandLinkButtonXmlHandler
0x00710190: hidden
0x0071019e: label
0x007101b2: style
0x007101be: default
0x0071021e: twxCommandLinkButton
0x00710378: wxDataViewXmlHandler
0x007103f0: wxDV_SINGLE
0x00710408: wxDV_MULTIPLE
0x00710424: wxDV_NO_HEADER
0x00710442: wxDV_HORIZ_RULES
0x00710464: wxDV_VERT_RULES
0x00710484: wxDV_ROW_LINES
0x007104a2: wxDV_VARIABLE_LINE_HEIGHT
0x0071050c: hidden
0x00710522: style
0x0071052e: imagelist
0x00711530: wxDateCtrlXmlHandler
0x007115a8: wxDP_DEFAULT
0x007115c2: wxDP_SPIN
0x007115d6: wxDP_DROPDOWN
0x007115f2: wxDP_ALLOWNONE
0x00711610: wxDP_SHOWCENTURY
0x00711632: hidden
0x00711648: style
0x00711654: null-text
0x00711668: wxDatePickerCtrl
0x007117a8: wxGenericDirCtrlXmlHandler
0x00711828: wxDIRCTRL_DIR_ONLY
0x0071184e: wxDIRCTRL_3D_INTERNAL
0x0071187a: wxDIRCTRL_SELECT_FIRST
0x007118a8: wxDIRCTRL_SHOW_FILTERS
0x007118d6: wxDIRCTRL_EDIT_LABELS
0x00711902: wxDIRCTRL_MULTIPLE
0x00711928: hidden
0x00711936: defaultfolder
0x0071195a: style
0x00711966: filter
0x00711974: defaultfilter
0x00711990: wxGenericDirCtrl
0x00711ad0: wxDirPickerCtrlXmlHandler
0x00711b50: wxDIRP_USE_TEXTCTRL
0x00711b78: wxDIRP_DIR_MUST_EXIST
0x00711ba4: wxDIRP_CHANGE_DIR
0x00711bc8: wxDIRP_SMALL
0x00711be2: wxDIRP_DEFAULT_STYLE
0x00711c0c: hidden
0x00711c1a: value
0x00711c26: message
0x00711c3e: style
0x00711c4a: wxDirPickerCtrl
0x00711cae: tshouldn't be called twice
0x00711e48: wxEditableListBoxXmlHandler
0x00711ec8: wxEL_ALLOW_NEW
0x00711ee6: wxEL_ALLOW_EDIT
0x00711f06: wxEL_ALLOW_DELETE
0x00711f2a: wxEL_NO_REORDER
0x00711f5a: xhidden
0x00711f6e: lpos
0x00711f78: style
0x00711ffe: tshouldn't be called twice
0x00712198: wxFileCtrlXmlHandler
0x00712210: wxFC_DEFAULT_STYLE
0x00712236: wxFC_OPEN
0x0071224a: wxFC_SAVE
0x0071225e: wxFC_MULTIPLE
0x0071227a: wxFC_NOSHOWHIDDEN
0x0071229e: hidden
0x007122ac: defaultdirectory
0x007122ce: defaultfilename
0x007122ee: wildcard
0x00712300: style
0x00712314: wxFileCtrl
0x0071236e: tshouldn't be called twice
0x00712500: wxFilePickerCtrlXmlHandler
0x00712580: wxFLP_OPEN
0x00712596: wxFLP_SAVE
0x007125ac: wxFLP_OVERWRITE_PROMPT
0x007125da: wxFLP_FILE_MUST_EXIST
0x00712606: wxFLP_CHANGE_DIR
0x00712628: wxFLP_SMALL
0x00712640: wxFLP_DEFAULT_STYLE
0x00712668: wxFLP_USE_TEXTCTRL
0x0071268e: hidden
0x0071269c: value
0x007126a8: message
0x007126b8: wildcard
0x007126d2: style
0x007126de: wxFilePickerCtrl
0x00712744: tshouldn't be called twice
0x007128e0: wxFontPickerCtrlXmlHandler
0x00712960: wxFNTP_USE_TEXTCTRL
0x00712988: wxFNTP_FONTDESC_AS_LABEL
0x007129ba: wxFNTP_USEFONT_FOR_LABEL
0x007129ec: wxFNTP_DEFAULT_STYLE
0x00712a16: hidden
0x00712a24: value
0x00712a38: style
0x00712a44: wxFontPickerCtrl
0x00712aaa: tshouldn't be called twice
0x00712c48: wxGaugeXmlHandler
0x00712cb8: wxGA_HORIZONTAL
0x00712cd8: wxGA_VERTICAL
0x00712cf4: wxGA_SMOOTH
0x00712d0c: hidden
0x00712d1a: range
0x00712d2e: style
0x00712d3a: value
0x00712d46: wxGauge
0x00712e70: wxGridXmlHandler
0x00712ee0: hidden
0x00712ef6: style
0x00712f02: wxGrid
0x00713888: wxHtmlWindowXmlHandler
0x00713900: wxHW_SCROLLBAR_NEVER
0x0071392a: wxHW_SCROLLBAR_AUTO
0x00713952: wxHW_NO_SELECTION
0x00713976: hidden
0x0071398c: style
0x00713998: borders
0x007139b0: htmlcode
0x007139c2: wxHtmlWindow
0x00713a20: tshouldn't be called twice
0x00713bc0: wxSimpleHtmlListBoxXmlHandler
0x00713c48: wxHLB_DEFAULT_STYLE
0x00713c70: wxHLB_MULTIPLE
0x00713c8e: wxSimpleHtmlListBox
0x00713cb6: selection
0x00713cca: content
0x00713cda: hidden
0x00713cf0: style
0x00713cfc: item
0x00713e28: wxHyperlinkCtrlXmlHandler
0x00713ea8: wxHL_CONTEXTMENU
0x00713eca: wxHL_ALIGN_LEFT
0x00713eea: wxHL_ALIGN_RIGHT
0x00713f0c: wxHL_ALIGN_CENTRE
0x00713f30: wxHL_DEFAULT_STYLE
0x00713f56: hidden
0x00713f64: label
0x00713f80: style
0x00713f8c: wxHyperlinkCtrl
0x007140c8: wxInfoBarXmlHandler
0x0071425c: rhidden
0x007144b8: wxListbookXmlHandler
0x00714538: wxBK_DEFAULT
0x00714552: wxBK_LEFT
0x00714566: wxBK_RIGHT
0x0071457c: wxBK_TOP
0x0071458e: wxBK_BOTTOM
0x007145a6: wxLB_DEFAULT
0x007145c0: wxLB_LEFT
0x007145d4: wxLB_RIGHT
0x007145ea: wxLB_TOP
0x007145fc: wxLB_BOTTOM
0x00714614: listbookpage
0x0071462e: hidden
0x00714644: style
0x00714650: wxListbook
0x007146aa: tshouldn't be called twice
0x0071478a: Override this function!
0x00714900: wxListBoxXmlHandler
0x00714970: wxLB_SINGLE
0x00714988: wxLB_MULTIPLE
0x007149a4: wxLB_EXTENDED
0x007149c0: wxLB_HSCROLL
0x007149da: wxLB_ALWAYS_SB
0x007149f8: wxLB_NEEDED_SB
0x00714a16: wxLB_SORT
0x00714a2a: wxListBox
0x00714a3e: selection
0x00714a52: content
0x00714a62: hidden
0x00714a78: style
0x00714a84: item
0x00714bc0: wxListCtrlXmlHandler
0x00714c38: wxLIST_FORMAT_LEFT
0x00714c5e: wxLIST_FORMAT_RIGHT
0x00714c86: wxLIST_FORMAT_CENTRE
0x00714cb0: wxLIST_MASK_STATE
0x00714cd4: wxLIST_MASK_TEXT
0x00714cf6: wxLIST_MASK_IMAGE
0x00714d1a: wxLIST_MASK_DATA
0x00714d3c: wxLIST_MASK_WIDTH
0x00714d60: wxLIST_MASK_FORMAT
0x00714d86: wxLIST_STATE_FOCUSED
0x00714db0: wxLIST_STATE_SELECTED
0x00714ddc: wxLC_LIST
0x00714df0: wxLC_REPORT
0x00714e08: wxLC_ICON
0x00714e1c: wxLC_SMALL_ICON
0x00714e3c: wxLC_ALIGN_TOP
0x00714e5a: wxLC_ALIGN_LEFT
0x00714e7a: wxLC_AUTOARRANGE
0x00714e9c: wxLC_USER_TEXT
0x00714eba: wxLC_EDIT_LABELS
0x00714edc: wxLC_NO_HEADER
0x00714efa: wxLC_SINGLE_SEL
0x00714f1a: wxLC_SORT_ASCENDING
0x00714f42: wxLC_SORT_DESCENDING
0x00714f6c: wxLC_VIRTUAL
0x00714f86: wxLC_HRULES
0x00714f9e: wxLC_VRULES
0x00714fb6: wxLC_NO_SORT_HEADER
0x0071503e: ealign
0x007150ac: .width
0x007150ba: image
0x007150de: state
0x007150ea: textcolour
0x00715100: textcolor
0x00715114: hidden
0x0071512a: style
0x00715136: imagelist
0x0071514a: imagelist-small
0x007153e8: wxMdiXmlHandler
0x00715450: wxSTAY_ON_TOP
0x0071546c: wxCAPTION
0x00715480: wxDEFAULT_DIALOG_STYLE
0x007154ae: wxDEFAULT_FRAME_STYLE
0x007154da: wxSYSTEM_MENU
0x007154f6: wxRESIZE_BORDER
0x00715516: wxCLOSE_BOX
0x0071552e: wxFRAME_NO_TASKBAR
0x00715554: wxFRAME_SHAPED
0x00715572: wxFRAME_TOOL_WINDOW
0x0071559a: wxFRAME_FLOAT_ON_PARENT
0x007155ca: wxMAXIMIZE_BOX
0x007155e8: wxMINIMIZE_BOX
0x00715606: wxTAB_TRAVERSAL
0x00715626: wxWS_EX_VALIDATE_RECURSIVELY
0x00715660: wxFRAME_EX_METAL
0x00715682: wxHSCROLL
0x00715696: wxVSCROLL
0x007156aa: wxMAXIMIZE
0x007156c0: wxFRAME_NO_WINDOW_MENU
0x007156ee: wxMDIParentFrame
0x00715710: hidden
0x0071571e: title
0x0071572a: style
0x00715786: centered
0x00715798: wxMDIChildFrame
0x00716320: wxMenuXmlHandler
0x00716390: wxMENU_TEAROFF
0x007163ae: wxMenu
0x007163bc: style
0x007163c8: label
0x007163d4: enabled
0x007163e4: separator
0x007163f8: break
0x00716404: accel
0x00716410: extra-accels
0x0071642a: radio
0x00716436: checkable
0x007164c0: lbitmap
0x007164d0: bitmap2
0x007164ee: checked
0x007164fe: wxMenuItem
0x00716514: wxMenuBarXmlHandler
0x00716588: wxMB_DOCKABLE
0x0071660c: rwxMenuBar
0x00716830: wxNotebookXmlHandler
0x007168b0: wxBK_DEFAULT
0x007168ca: wxBK_LEFT
0x007168de: wxBK_RIGHT
0x007168f4: wxBK_TOP
0x00716906: wxBK_BOTTOM
0x0071691e: wxNB_DEFAULT
0x00716938: wxNB_LEFT
0x0071694c: wxNB_RIGHT
0x00716962: wxNB_TOP
0x00716974: wxNB_BOTTOM
0x0071698c: wxNB_FIXEDWIDTH
0x007169ac: wxNB_MULTILINE
0x007169ca: wxNB_NOPAGETHEME
0x007169ec: notebookpage
0x00716a06: hidden
0x00716a1c: style
0x00716a28: wxNotebook
0x00716b48: wxOwnerDrawnComboBoxXmlHandler
0x00716bd0: wxCB_SIMPLE
0x00716be8: wxCB_SORT
0x00716bfc: wxCB_READONLY
0x00716c18: wxCB_DROPDOWN
0x00716c34: wxODCB_STD_CONTROL_PAINT
0x00716c66: wxODCB_DCLICK_CYCLES
0x00716c90: wxTE_PROCESS_ENTER
0x00716cb6: wxOwnerDrawnComboBox
0x00716ce0: selection
0x00716cf4: content
0x00716d04: hidden
0x00716d12: value
0x00716d26: style
0x00716d32: buttonsize
0x00716d48: item
0x00716e80: wxRadioBoxXmlHandler
0x00716ef8: wxRA_SPECIFY_COLS
0x00716f1c: wxRA_HORIZONTAL
0x00716f3c: wxRA_SPECIFY_ROWS
0x00716f60: wxRA_VERTICAL
0x00716f7c: wxRadioBox
0x00716f92: selection
0x00716fa6: content
0x00716fb6: hidden
0x00716fc4: label
0x00716fd8: dimension
0x00716fec: style
0x00716ffc: ltooltip
0x0071700e: helptext
0x00717030: item
0x00717076: wxArrayString: index out of bounds
0x00717208: wxRadioButtonXmlHandler
0x00717280: wxRB_GROUP
0x00717296: wxRB_SINGLE
0x007172ae: hidden
0x007172bc: label
0x007172d0: style
0x007172dc: value
0x007172e8: wxRadioButton
0x00717420: wxScrollBarXmlHandler
0x00717498: wxSB_HORIZONTAL
0x007174b8: wxSB_VERTICAL
0x007174d4: hidden
0x007174ea: style
0x007174f6: value
0x00717502: thumbsize
0x00717516: range
0x00717522: pagesize
0x00717534: wxScrollBar
0x00717660: wxSearchCtrlXmlHandler
0x007176d8: wxTE_PROCESS_ENTER
0x007176fe: wxTE_PROCESS_TAB
0x00717720: wxTE_NOHIDESEL
0x0071773e: wxTE_LEFT
0x00717752: wxTE_CENTRE
0x0071776a: wxTE_RIGHT
0x00717780: wxTE_CAPITALIZE
0x007177a0: hidden
0x007177ae: value
0x007177c2: style
0x007177ce: wxSearchCtrl
0x00717900: wxSimplebookXmlHandler
0x00717978: simplebookpage
0x00717996: object
0x007179a4: object_ref
0x007179ba: label
0x007179c6: selected
0x00717a24: dhidden
0x00717a3c: style
0x00717a48: wxSimplebook
0x00717aa6: tshouldn't be called twice
0x00717b5c: "Override this function!
0x00717c18: "Invalid page
0x007186b0: wxSliderXmlHandler
0x00718720: wxSL_HORIZONTAL
0x00718740: wxSL_VERTICAL
0x0071875c: wxSL_AUTOTICKS
0x0071877a: wxSL_MIN_MAX_LABELS
0x007187a2: wxSL_VALUE_LABEL
0x007187c4: wxSL_LABELS
0x007187dc: wxSL_LEFT
0x007187f0: wxSL_TOP
0x00718802: wxSL_RIGHT
0x00718818: wxSL_BOTTOM
0x00718830: wxSL_BOTH
0x00718844: wxSL_SELRANGE
0x00718860: wxSL_INVERSE
0x0071887a: hidden
0x00718888: value
0x007188ac: style
0x007188b8: tickfreq
0x007188ca: pagesize
0x007188dc: linesize
0x007188ee: thumb
0x007188fa: selmin
0x00718908: selmax
0x00718916: wxSlider
0x00718a40: wxSpinButtonXmlHandler
0x00718ab8: wxSP_HORIZONTAL
0x00718ad8: wxSP_VERTICAL
0x00718af4: wxSP_ARROW_KEYS
0x00718b14: wxSP_WRAP
0x00718b28: hidden
0x00718b3e: style
0x00718b4a: value
0x00718b6e: wxSpinButton
0x00718b88: wxSpinCtrlXmlHandler
0x00718c00: wxSpinCtrl
0x00718c16: wxSpinCtrlDoubleXmlHandler
0x00718ca0: wxSpinCtrlDouble
0x00718cd0: wxALIGN_LEFT
0x00718cea: wxALIGN_CENTER
0x00718d08: wxALIGN_RIGHT
0x00718d24: wxTE_PROCESS_ENTER
0x00718dc6: tshouldn't be called twice
0x0071a900: wxSplitterWindowXmlHandler
0x0071a980: wxSP_3D
0x0071a990: wxSP_3DSASH
0x0071a9a8: wxSP_3DBORDER
0x0071a9c4: wxSP_BORDER
0x0071a9dc: wxSP_NOBORDER
0x0071a9f8: wxSP_PERMIT_UNSPLIT
0x0071aa20: wxSP_LIVE_UPDATE
0x0071aa42: wxSP_NO_XP_THEME
0x0071aa64: hidden
0x0071aa7a: style
0x0071aa86: sashpos
0x0071aa96: minsize
0x0071aaa6: gravity
0x0071aab6: object
0x0071aac4: object_ref
0x0071ab12: orientation
0x0071ab2a: vertical
0x0071ab3c: wxSplitterWindow
0x0071aba2: tshouldn't be called twice
0x0071ad40: wxStaticBitmapXmlHandler
0x0071adc0: hidden
0x0071adce: bitmap
0x0071ade8: Cpos
0x0071adf2: style
0x0071adfe: wxStaticBitmap
0x0071af40: wxStaticBoxXmlHandler
0x0071afb8: hidden
0x0071afc6: label
0x0071afda: style
0x0071afe6: wxStaticBox
0x0071b106: rwxStaticLineXmlHandler
0x0071b180: wxLI_HORIZONTAL
0x0071b1a0: wxLI_VERTICAL
0x0071b1bc: hidden
0x0071b1d2: style
0x0071b1de: wxStaticLine
0x0071b310: wxStaticTextXmlHandler
0x0071b388: wxST_NO_AUTORESIZE
0x0071b3ae: wxALIGN_LEFT
0x0071b3c8: wxALIGN_RIGHT
0x0071b3e4: wxALIGN_CENTER
0x0071b402: wxALIGN_CENTRE
0x0071b420: wxALIGN_CENTER_HORIZONTAL
0x0071b454: wxALIGN_CENTRE_HORIZONTAL
0x0071b488: wxST_ELLIPSIZE_START
0x0071b4b2: wxST_ELLIPSIZE_MIDDLE
0x0071b4de: wxST_ELLIPSIZE_END
0x0071b504: hidden
0x0071b512: label
0x0071b526: style
0x0071b532: wxStaticText
0x0071b668: wxStatusBarXmlHandler
0x0071b6e0: wxSTB_SIZEGRIP
0x0071b6fe: wxSTB_SHOW_TIPS
0x0071b71e: wxSTB_ELLIPSIZE_START
0x0071b74a: wxSTB_ELLIPSIZE_MIDDLE
0x0071b778: wxSTB_ELLIPSIZE_END
0x0071b7a0: wxSTB_DEFAULT_STYLE
0x0071b7c8: wxST_SIZEGRIP
0x0071b7e4: hidden
0x0071b7f2: style
0x0071b7fe: fields
0x0071b80c: widths
0x0071b81a: styles
0x0071b828: wxSB_NORMAL
0x0071b840: wxSB_FLAT
0x0071b854: wxSB_RAISED
0x0071b86c: wxSB_SUNKEN
0x0071b8b0: wxStatusBar
0x0071ba6e: rwxTextCtrlXmlHandler
0x0071bae8: wxTE_NO_VSCROLL
0x0071bb08: wxTE_PROCESS_ENTER
0x0071bb2e: wxTE_PROCESS_TAB
0x0071bb50: wxTE_MULTILINE
0x0071bb6e: wxTE_PASSWORD
0x0071bb8a: wxTE_READONLY
0x0071bba6: wxHSCROLL
0x0071bbba: wxTE_RICH
0x0071bbce: wxTE_RICH2
0x0071bbe4: wxTE_AUTO_URL
0x0071bc00: wxTE_NOHIDESEL
0x0071bc1e: wxTE_LEFT
0x0071bc32: wxTE_CENTER
0x0071bc4a: wxTE_CENTRE
0x0071bc62: wxTE_RIGHT
0x0071bc78: wxTE_DONTWRAP
0x0071bc94: wxTE_CHARWRAP
0x0071bcb0: wxTE_WORDWRAP
0x0071bccc: wxTE_BESTWRAP
0x0071bce8: wxTE_CAPITALIZE
0x0071bd08: wxTE_AUTO_SCROLL
0x0071bd2a: hidden
0x0071bd38: value
0x0071bd4c: style
0x0071bd58: maxlength
0x0071bd6c: forceupper
0x0071bd82: wxTextCtrl
0x0071bea8: wxToggleButtonXmlHandler
0x0071bf38: wxBU_LEFT
0x0071bf4c: wxBU_RIGHT
0x0071bf62: wxBU_TOP
0x0071bf74: wxBU_BOTTOM
0x0071bf8c: wxBU_EXACTFIT
0x0071bfa8: wxBU_NOTEXT
0x0071bfc0: wxBitmapToggleButton
0x0071bfea: wxToggleButton
0x0071c008: label
0x0071c01c: style
0x0071c080: schecked
0x0071c092: bitmap
0x0071c108: wxTimeCtrlXmlHandler
0x0071c180: wxTP_DEFAULT
0x0071c19a: hidden
0x0071c1b0: style
0x0071c1bc: wxTimePickerCtrl
0x0071c2f8: wxToolBarXmlHandler
0x0071c368: wxTB_FLAT
0x0071c37c: wxTB_DOCKABLE
0x0071c398: wxTB_VERTICAL
0x0071c3b4: wxTB_HORIZONTAL
0x0071c3d4: wxTB_TEXT
0x0071c3e8: wxTB_NOICONS
0x0071c402: wxTB_NODIVIDER
0x0071c420: wxTB_NOALIGN
0x0071c43a: wxTB_HORZ_LAYOUT
0x0071c45c: wxTB_HORZ_TEXT
0x0071c47a: wxTB_TOP
0x0071c48c: wxTB_LEFT
0x0071c4a0: wxTB_RIGHT
0x0071c4b6: wxTB_BOTTOM
0x0071c4ce: tool
0x0071c4fe: radio
0x0071c50a: toggle
0x0071c5fc: label
0x0071c608: bitmap
0x0071c624: Cbitmap2
0x0071c636: tooltip
0x0071c646: longhelp
0x0071c658: disabled
0x0071c66a: checked
0x0071c6b2: separator
0x0071c6c6: space
0x0071c6fc: style
0x0071c708: hidden
0x0071c71e: bitmapsize
0x0071c734: margins
0x0071c744: packing
0x0071c754: separation
0x0071c76a: object
0x0071c778: object_ref
0x0071c78e: dontattachtoframe
0x0071c7b2: wxToolBar
0x0071c8e0: wxToolbookXmlHandler
0x0071c960: wxBK_DEFAULT
0x0071c97a: wxBK_TOP
0x0071c98c: wxBK_BOTTOM
0x0071c9a4: wxBK_LEFT
0x0071c9b8: wxBK_RIGHT
0x0071c9ce: wxTBK_BUTTONBAR
0x0071c9ee: wxTBK_HORZ_LAYOUT
0x0071ca12: toolbookpage
0x0071ca2c: hidden
0x0071ca42: style
0x0071ca4e: wxToolbook
0x0071caa8: tshouldn't be called twice
0x0071cb88: Override this function!
0x0071ccf8: wxTreebookXmlHandler
0x0071cd78: wxBK_DEFAULT
0x0071cd92: wxBK_TOP
0x0071cda4: wxBK_BOTTOM
0x0071cdbc: wxBK_LEFT
0x0071cdd0: wxBK_RIGHT
0x0071cde6: wxTreebook
0x0071cdfc: treebookpage
0x0071ce16: hidden
0x0071ce2c: style
0x0071ce46: d1depth
0x0071ceae: tshouldn't be called twice
0x0071cf8e: Override this function!
0x0071d138: wxTreeCtrlXmlHandler
0x0071d1b0: wxTR_EDIT_LABELS
0x0071d1d2: wxTR_NO_BUTTONS
0x0071d1f2: wxTR_HAS_BUTTONS
0x0071d214: wxTR_TWIST_BUTTONS
0x0071d23a: wxTR_NO_LINES
0x0071d256: wxTR_FULL_ROW_HIGHLIGHT
0x0071d286: wxTR_LINES_AT_ROOT
0x0071d2ac: wxTR_HIDE_ROOT
0x0071d2ca: wxTR_ROW_LINES
0x0071d2e8: wxTR_HAS_VARIABLE_ROW_HEIGHT
0x0071d322: wxTR_SINGLE
0x0071d33a: wxTR_MULTIPLE
0x0071d356: wxTR_DEFAULT_STYLE
0x0071d37c: hidden
0x0071d392: style
0x0071d39e: imagelist
0x0071d3b2: wxTreeCtrl
0x0071d4e0: wxWizardXmlHandler
0x0071d550: wxSTAY_ON_TOP
0x0071d56c: wxCAPTION
0x0071d580: wxDEFAULT_DIALOG_STYLE
0x0071d5ae: wxSYSTEM_MENU
0x0071d5ca: wxRESIZE_BORDER
0x0071d5ea: wxCLOSE_BOX
0x0071d602: wxDIALOG_NO_PARENT
0x0071d628: wxTAB_TRAVERSAL
0x0071d648: wxWS_EX_VALIDATE_RECURSIVELY
0x0071d682: wxDIALOG_EX_METAL
0x0071d6a6: wxMAXIMIZE_BOX
0x0071d6c4: wxMINIMIZE_BOX
0x0071d6e2: wxFRAME_SHAPED
0x0071d700: wxDIALOG_EX_CONTEXTHELP
0x0071d730: wxWIZARD_EX_HELPBUTTON
0x0071d75e: wxWIZARD_VALIGN_TOP
0x0071d786: wxWIZARD_VALIGN_CENTRE
0x0071d7b4: wxWIZARD_VALIGN_BOTTOM
0x0071d7e2: wxWIZARD_HALIGN_LEFT
0x0071d80c: wxWIZARD_HALIGN_CENTRE
0x0071d83a: wxWIZARD_HALIGN_RIGHT
0x0071d866: wxWIZARD_TILE
0x0071d882: wxWizard
0x0071d894: hidden
0x0071d8a2: exstyle
0x0071d8b2: title
0x0071d8be: bitmap
0x0071d8d8: Cpos
0x0071d8e2: style
0x0071d91e: gwxWizardPageSimple
0x0071d97a: dwxWizardPage
0x0071d9da: tshouldn't be called twice
0x0071dbd4: no matching wxBeginBusyCursor() for wxEndBusyCursor()
0x0071dc9c: eIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0071dd16: SHAutoComplete
0x0071e8f8: wxURI
0x0071e954: /@?#
0x0071e9f8: \shell\open
0x0071ea10: http\shell\open
0x0071ea30: DDEExec
0x0071ea40: WWW_OpenURL
0x0071ea58: topic
0x0071ea64: application
0x0071ea7c: open
0x0071ea86: file
0x0071ead8: wxMessageDialog
0x0071eb9e: bogus thread id in wxMessageDialog::Hook
0x0071ebf0: STATIC
0x0071ec4c: .EDIT
0x0071ed24: no task dialog?
0x0071ed44: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0071ee00: Too many buttons
0x0071ee58: unexpected return code
0x0071f7e2: tGetWindowRect
0x0071f906: GetClientRect
0x0071f958: sSystemParametersInfo(SPI_GETNONCLIENTMETRICS)
0x0071fa74: failed to initialize wxPlatformInfo
0x0071fabc: Unknown
0x0071facc: Macintosh
0x0071fae0: Windows
0x0071faf0: Unix
0x0071fb60: "invalid OS id
0x0071fc50: invalid port id
0x0071fcc8: /wxUniversal
0x0071fcf6: univ
0x0071fd28: little
0x0071fd46: lwine_get_version
0x0071fd9a: invalid enum value
0x0071fdcc: more than one bit set in enum value
0x0071fe14: Apple Mac OS
0x0071fe2e: Apple Mac OS X
0x0071fe4c: Microsoft Windows NT
0x0071fe76: Linux
0x0071fe82: FreeBSD
0x0071fe92: OpenBSD
0x0071fea2: NetBSD
0x0071feb0: SunOS
0x0071fec4: HPUX
0x0071fece: Other Unix
0x0071fee4: wxBase
0x0071fef2: wxMSW
0x0071fefe: wxMotif
0x0071ff0e: wxGTK
0x0071ff1a: wxDFB
0x0071ff26: wxX11
0x0071ff32: wxMac
0x0071ff3e: wxCocoa
0x0071ff4e: wxQT
0x0071ff58: 32 bit
0x0071ff66: 64 bit
0x0071ff74: Big endian
0x0071ff8a: Little endian
0x0071ffa6: PDP endian
0x0071ffee: focus
0x0071fffa: SetFocus on wxPanel 0x%p.
0x00720046: Setting last focus for a window that is not our child?
0x007200b4: Set last focus to %s(%s)
0x007200e6: No more last focus
0x00720118: sOnFocus on wxPanel 0x%p, name: %s
0x00720174: "wxSetFocusToChild(): invalid window
0x007201be: SetFocusToChild() => last child (0x%p).
0x0072020e: SetFocusToChild() => first child (0x%p).
0x0072125e: "invalid stock item ID
0x00721442: wxNotebook
0x00721daa: Override this function!
0x00721ea0: wxTIFFHandler
0x00721f18: TIFF file
0x00721f34: image/tiff
0x0072200e: Photometric
0x00722026: SamplesPerPixel
0x00722046: BitsPerSample
0x00722062: Compression
0x007220a0: dResolutionUnit
0x007220c0: ResolutionX
0x007220d8: ResolutionY
0x00722124: "unknown image resolution units
0x0072252c: wxBMPHandler
0x0072256e: "invalid pointer in wxBMPHandler::SaveFile
0x007225ea: wxBMP_FORMAT
0x0072263c: "unexpected image resolution units
0x0072286e: .ResolutionUnit
0x0072288e: ResolutionX
0x007228a6: ResolutionY
0x007228be: wxICOHandler
0x00722944: !HotSpotX
0x00722958: HotSpotY
0x00722996: wxCURHandler
0x007229b0: wxANIHandler
0x00722b48: Windows bitmap file
0x00722b78: image/x-bmp
0x00722bc8: .Windows icon file
0x00722bf6: image/x-ico
0x00722c0e: PNG file
0x00722c28: image/png
0x00722c3c: Windows cursor file
0x00722c6c: image/x-cur
0x00722c84: Windows animated cursor file
0x00722cc6: image/x-ani
0x00722e40: wxGIFHandler
0x00722efe: can't save 0-sized file
0x00722f2e: GifComment
0x00722fb0: GIF file
0x00722fca: image/gif
0x0072308c: IFF ILBM file recognized
0x007230c6: PRead %d colors from IFF file.
0x0072310c: YLoadIFF: %s %dx%d, planes=%d (%d cols), comp=%d
0x007231ac: MConverting CMAP from normal ILBM CMAP
0x007231fa: Doubling CMAP for EHB mode
0x00723230: Loaded IFF picture %s
0x00723272: Skipping unknown chunk '%c%c%c%c'
0x007232b6: wxIFFHandler
0x00723356: eIFF: the handler is read-only!!
0x0072340e: IFF file
0x00723420: image/x-iff
0x007235ac: wxPCXHandler
0x007236d8: PCX file
0x007236f2: image/pcx
0x00723794: wxPNGHandler
0x00723844: .PngFormat
0x00723878: 0PngZL
0x00723886: PngZM
0x00723892: PngZS
0x0072389e: PngZB
0x007238aa: PngBitDepth
0x007238d4: unsupported image resolution units
0x0072391a: unknown wxPNG_TYPE_XXX
0x007239b0: PNG file
0x007239ca: image/png
0x00723a0e: ResolutionX
0x00723a26: ResolutionY
0x00723a3e: ResolutionUnit
0x00723b38: wxPNMHandler
0x00723c48: PNM file
0x00723c7a: image/pnm
0x00723d84: wxTGAHandler
0x00723dc2: eTGA: this is not a TGA file.
0x00723dfe: TGA: image format unsupported.
0x00723e3c: TGA: couldn't allocate memory.
0x00723e7a: TGA: couldn't read image data.
0x00723ec2: TGA: couldn't write image data.
0x00723f60: TGA file
0x00723f7a: image/tga
0x00723fd4: wxXPMHandler
0x0072404e: FileName
0x00724060: _xpm
0x0072406a: /* XPM */
0x0072407e: static const char *
0x007240a6: /* XPM */
0x007240ba: static const char *xpm_data
0x007241d8: XPM file
0x007241f2: image/xpm
0x00724250: wxJPEGHandler
0x007242ac: MaxWidth
0x007242be: MaxHeight
0x00724308: ResolutionX
0x00724320: ResolutionY
0x00724338: ResolutionUnit
0x00724356: OriginalWidth
0x00724372: OriginalHeight
0x007243b2: .quality
0x00724428: JPEG file
0x0072444c: image/jpeg
0x007244e6: Unknown accel modifier: '%s'
0x00724520: No accel key found, accel string ignored.
0x00724bc2: Unrecognized accel key '%s', accel string ignored.
0x00724c2e: elogic error: should have key code here
0x00724cbc: unknown keyboard accelerator code
0x00724d30: Invalid key string "%s"
0x0072516c: wxBrush
0x007251d6: "unknown brush style
0x00725204: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0072527c: CreateXXXBrush()
0x00725364: invalid brush
0x00725628: wxCursor
0x007256f0: HotSpotX
0x00725702: HotSpotY
0x00725784: invalid cursor hot spot coordinates
0x007257f2: unknown cursor resource type '%d'
0x00725a00: WXCURSOR_RIGHT_ARROW
0x00725a2a: WXCURSOR_BULLSEYE
0x00725a4e: WXCURSOR_MAGNIFIER
0x00725a74: WXCURSOR_PBRUSH
0x00725a94: WXCURSOR_PENCIL
0x00725ab4: WXCURSOR_PLEFT
0x00725ad2: WXCURSOR_PRIGHT
0x00725af2: WXCURSOR_BLANK
0x00725b6c: "invalid cursor id in wxCursor() ctor
0x00725bb8: WXCURSOR_HAND
0x00725be6: Loading a cursor defined by wxWidgets failed, did you include include/wx/msw/wx.rc file from your resource file?
0x00725cc8: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00725d40: LoadCursor
0x00725d8a: GetIconInfo
0x00725dd0: must be implemented if used
0x00726038: wxPen
0x00726164: "invalid pen
0x007261f6: unknown pen style
0x0072622c: unknown pen join style
0x00726268: eunknown pen cap style
0x00726328: wxDisplayModule
0x00726382: )An invalid index was passed to wxDisplay
0x007263ee: invalid window
0x00726422: invalid wxDisplay object
0x00726660: wxPalette
0x00726850: wxRichEditModule
0x00727488: EDIT
0x00727492: RICHEDIT50W
0x007274aa: RichEdit20W
0x007274c2: RICHEDIT
0x00727566: lRichEdit%d0%c
0x0072759a: DUnknown edit control '%s'.
0x00727676: unexpected wxTextCtrl::m_updatesCount value
0x00727712: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0072778a: SendMessage(EM_SETCHARFORMAT)
0x007277d8: SendMessage(EM_SETPARAFORMAT)
0x00727820: riched32
0x00727832: riched20
0x00727844: msftedit
0x00727980: wrong initial m_updatesCount value
0x00727a0c: Can't set layout direction for invalid window
0x00727a68: Invalid layout direction
0x00727ab4: invalid window
0x00728368: should close or detach the old file first
0x0072840c: invalid parameter
0x0072843e: can't read from closed file
0x00728484: "invalid length
0x007284c8: huge file not supported
0x00728520: ecan't write to closed file
0x00728598: kcan't seek on closed file
0x007285e0: unknown seek mode
0x00728622: wxFFile::Tell(): file is closed!
0x00728692: hwxFFile::Length(): file is closed!
0x007286dc: fwxFFile::Eof(): file is closed!
0x00728722: rwxFFile::Error(): file is closed!
0x00728b48: "No flags for file name auto completion?
0x00728b9e: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00728c16: SHAutoComplete()
0x00728c4e: Must be overridden if can be called
0x00728d08: CoCreateInstance(CLSID_AutoComplete)
0x00728d52: IAutoComplete::Init
0x00728d7a: IAutoComplete::QI(IAutoCompleteDropDown)
0x00729280: %m/%d/%y%Y-%m-%d%I:%M:%S %p
0x007292c0: %H:%M:%S
0x00729480: %a %b %d %H:%M:%S %Y
0x007294aa: %I:%M:%S %p
0x0072ae62: tWednesday
0x0072ae78: Thursday
0x0072ae8a: Saturday
0x0072ae9c: February
0x0072aeae: September
0x0072aec2: November
0x0072aed4: December
0x0072b9a2: all controls must have parents
0x0072ba06: duplicate accel char in control label
0x0072ba5a:       
0x0072bc08: wxControl
0x0072bc42: nIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0072bcbc: DrawText(DT_CALCRECT)
0x0072bce8: DrawText()
0x0072bcfe: DrawFocusRect()
0x0072bd2c: eSendMessage(XX_INITSTORAGE)
0x0072bd7a: mSendMessage(XX_ADD/INSERTSTRING)
0x0072c5ac: size of this DC hadn't been set and is unknown
0x0072c63c: index out of bounds
0x0072c67c: wxArrayString: index out of bounds
0x0072cd8e: twxArrayString: index out of bounds
0x0072d6a2: wxFONTENCODING_DEFAULT doesn't make sense here
0x0072d712: "count pointer must be provided
0x0072d770: "Invalid BOM type
0x0072d794: Unknown BOM type
0x0072d91c: wxStatusBar
0x0072e142: "invalid field number in SetFieldsCount
0x0072e1c0: )field number mismatch
0x0072e208: NULL pointer in SetStatusStyles
0x0072ec3c: "Status bar must have a parent
0x0072ec7a: msctls_statusbar32
0x0072ecfa: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0072ee42: )Status
0x0072f058: wxToolBarToolBase
0x0072f132: can't toggle this tool
0x0072fad2: "invalid position in wxToolBar::InsertTool()
0x0072fb42: "toolbar: can't insert NULL control
0x0072fbaa: control must have toolbar as parent
0x0072fc10: NULL control in toolbar?
0x0072fc72: invalid position in wxToolBar::DeleteToolByPos()
0x0072fce8: NULL tool in wxToolBarTool::UnToggleRadioGroup
0x0072fd4e: invalid tool in wxToolBarTool::UnToggleRadioGroup
0x0072fdc2: ano such tool in wxToolBar::SetToolClientData
0x0072fe2c: no such tool
0x0072fe84: uinvalid tool toolid
0x0072fed4: menu can be only associated with drop down tools
0x0072ffda: )only makes sense for buttons
0x00730128: wxToolBar
0x00730150: ToolbarWindow32
0x007301a2: recreating the toolbar failed
0x00730b3e: lIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00730bb8: TB_DELETEBUTTON
0x00730bd8: msw.remap
0x00730bf2: eTB_DELETEBUTTON failed
0x00730c22: invalid tool button bitmap
0x00730c58: Could not replace the old bitmap
0x00730c9a: Could not add bitmap to toolbar
0x00730cda: unexpected toolbar button kind
0x00730d18: TB_ADDBUTTONS
0x00730d52: TB_SETBUTTONINFO (separator)
0x00730d8c: TB_DELETEBUTTON (separator)
0x00730dc4: TB_INSERTBUTTON (separator)
0x00730e10: drop down message for unknown tool
0x00730e5c: sTB_SETSTATE (stretchable spacer)
0x00730eca: Inconsistent tool state
0x00730f04: enot implemented
0x00730f4e: Can only set bitmap on button tools.
0x00730fb8: tValidateRgn()
0x00730fe6: SetWindowOrgEx(tbar-bg-hdc)
0x00731026: pCreateCompatibleDC
0x0073104e: SelectObject
0x007310ac: )Label can be set for control or button tool only
0x00731140: )this toolbar tool is not a control
0x00731194: tonly makes sense for embedded control tools
0x007311fc: tTB_GETITEMRECT
0x0073123a: DeleteObject(hDIB)
0x00731274: only makes sense for buttons
0x00731780: hinvalid image depth in wxDIB::Create()
0x007317d4: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0073184c: CreateDIBSection
0x00731876: wxDIB::Create(): invalid bitmap
0x007318b6: GetObject(bitmap)
0x007318f8: GetObject(DIBSECTION) unexpectedly failed
0x0073194c: GetDIBits()
0x0073196a: Loading DIB from file
0x007319a4: "wxDIB::Save(): invalid object
0x007319e2: GetObject(hDIB)
0x00731a02: GetDIBColorTable
0x00731a66: BwxDIB::CreateDDB(): invalid object
0x00731ac6: invalid DIB in ConvertToBitmap
0x00731b04: CreateDIBitmap
0x00731b3a: invalid bmp can't be converted to DIB
0x00731bb8: .wxDIB::ConvertFromBitmap() unexpectedly failed
0x00731c24: ewxDIB::CreatePalette(): invalid object
0x00731c80: out of memory
0x00731c9c: CreatePalette
0x00731cc8: invalid wxImage in wxDIB ctor
0x00731d4c: SetDIBColorTable
0x00731d84: "can't convert invalid DIB to wxImage
0x00731dd0: could not allocate data for image
0x00731e40: tGlobalLock
0x00731e68: GlobalUnlock
0x007320c4: wxDCFactoryCleanupModule
0x007320f6: wxWindowDC
0x00732170: wxClientDC
0x007321e8: wxMemoryDC
0x00732260: wxPaintDC
0x007322d8: wxScreenDC
0x00732350: wxPrinterDC
0x007323c8: wxDCImpl
0x007327d4: 0Clipping box size values cannot be negative
0x00732848: invalid window dc
0x007328ac: invalid blit size
0x00732968: LxwxDC
0x00732e6c: can't have both object and void client data
0x00732ed4: tthis window doesn't have object client data
0x00732f74: athis window doesn't have void client data
0x007332c8: wxSizerItem
0x007332e0: wxSizer
0x007332f0: wxGridSizer
0x00733308: wxFlexGridSizer
0x00733328: wxBoxSizer
0x0073333e: wxStaticBoxSizer
0x00733360: wxStdDialogButtonSizer
0x0073367e: NULL window in wxSizerItem::SetWindow()
0x007336ee: "unexpected wxSizerItem::m_kind
0x0073375e: Shaped item, non-zero proportion in wxSizerItem::InformFirstDirection()
0x007337fc: can't set size of uninitialized sizer item
0x00733866: can't show uninitialized sizer item
0x00733a48: Removing NULL sizer
0x00733aa4: "Remove index is out of range
0x00733ae8: Failed to find child node
0x00733b24: Detaching NULL sizer
0x00733b56: Detaching NULL window
0x00733b82: Detach index is out of range
0x00733bcc: Replacing NULL window
0x00733c00: Replacing with NULL window
0x00733c3a: zReplacing NULL sizer
0x00733c6a: zReplacing with NULL sizer
0x00733cbc: "Replace index is out of range
0x00733d02: "Replacing with NULL item
0x00733d8e: Must be overridden if RepositionChildren() is not
0x00733e04: SetMinSize for NULL window
0x00733e3a: SetMinSize for NULL sizer
0x00733e74: mGetItem for NULL window
0x00733ea6: GetItem for NULL sizer
0x00733ef2: "GetItem index is out of range
0x00733f30: IsShown failed to find sizer item
0x00733f74: IsShown index is out of range
0x007342c4: eFailed to find SizerItemList node
0x00734ade: wxStaticBoxSizer needs a static box
0x0073526e: column/row is already not growable
0x00735318: invalid value for wxBoxSizer orientation
0x00735580: BUTTON
0x00735dba: "button without top level window?
0x00735f60: wxODButtonImageData
0x00735f88: wxXPButtonImageData
0x007367e4: shouldn't be called if no image
0x00736824: BUTTON
0x00736b90: size of this DC hadn't been set and is unknown
0x00736c12: eIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00736c8c: SetTextColor
0x00736ca6: SetBkColor
0x00736db2: SetBkMode
0x00736df2: twxArrayString: index out of bounds
0x00737098: STATIC
0x00737adc: nassociated window can't be NULL in wxScrollHelper
0x00737bf2: target window must not be NULL
0x00737ce4: unknown scrollbar visibility
0x00737d1e: wxScrolledWindow
0x00738624: 0unbalanced wxRecursionGuards!?
0x0073869e: shouldn't be called twice
0x007387f0: wxPanel
0x007396fa: shouldn't be called twice
0x0073977e: gSTATIC
0x00739fd0: wxBookCtrlBase
0x0073a036: "unexpected alignment
0x0073a07a: Null page in a control that does not allow null pages?
0x0073a10e: NULL page in wxBookCtrlBase::InsertPage()
0x0073a17a: "invalid page index in wxBookCtrlBase::InsertPage()
0x0073a206: "invalid page index in wxBookCtrlBase::DoRemovePage()
0x0073a294: "invalid page index in wxBookCtrlBase::DoSetSelection()
0x0073a304: wxBookCtrlEvent
0x0073aca4: eOverride this function!
0x0073af70: wxValidator
0x0073b0a2: wxStaticLine
0x0073b0f0: "Registering already registered hook?
0x0073b148: Unregistering not registered hook?
0x0073b1d4: wxDirDialog
0x0073bb68: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0073bbe0: CoCreateInstance(CLSID_FileOpenDialog)
0x0073bc40: "shouldn't be called
0x0073bc6a: IFileDialog::SetOptions
0x0073bc9a: IFileDialog::Show
0x0073bcbe: IFileDialog::QI(IFileOpenDialog)
0x0073bd30: eIFileDialog::SetTitle
0x0073bd5e: shell32.dll
0x0073bda6: hSHCreateItemFromParsingName() not found
0x0073bdf8: /\SHCreateItemFromParsingName("%s")
0x0073be50: IFileDialog::SetFolder
0x0073be88: IFileDialog::AddPlace("%s")
0x0073bed8: IShellItem::GetDisplayName
0x0073bf3c: eSHGetMalloc
0x0073bf5c: hSHGetPathFromIDList
0x0073bff4: IShellItemArray::GetResults
0x0073c02c: IShellItemArray::GetCount
0x0073c060: IShellItemArray::GetItem
0x0073c0c4: rCan't get direct access to initialized pointer
0x0073c13c: IFileDialog::GetResult
0x0073cc42: 0Library already loaded.
0x0073ccae: "Can't load symbol from unloaded library
0x0073cd38: _gcc
0x0073cdac: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0073ce24: FreeLibrary
0x0073ce48: EnumerateLoadedModules
0x0073ce8c: GetModuleHandleEx
0x0073ceb0: GetModuleFromAddress
0x0073cf0a: eGetModuleFileName
0x0073cf34: %d.%d.%d.%d
0x0073cfec: kernel32.dll
0x0073d0b6: twxArrayString: index out of bounds
0x0073d284: wxRadioBox
0x0073d29a: BUTTON
0x0073d2c8: eIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0073d342: CreateWindow(radio btn)
0x0073dc58: "invalid radiobox index
0x0073dcb4: invalid item in wxRadioBox::Enable()
0x0073dd0a: dinvalid item in wxRadioBox::IsItemEnabled()
0x0073dd6a: invalid item in wxRadioBox::Show()
0x0073ddba: ninvalid item in wxRadioBox::IsItemShown()
0x0073de2e: Should have the associated radio box
0x0073dea4: subwindow index out of range
0x0073df0a: twxArrayString: index out of bounds
0x0073df92: GetWindowRect
0x0073e270: ywxSpinCtrl has incorrect buddy HWND!
0x0073e2bc: EDIT
0x0073e2ce: eIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0073e348: CreateWindow(buddy text window)
0x0073e388: wxSpinCtrl "%s": initial width %d is too small, at least %d pixels needed.
0x0073ec42: SetWindowText(buddy)
0x0073ec7a: DestroyWindow
0x0073ecaa: "scrolling what?
0x0073ed32: "Can't set layout direction for invalid window
0x0073eda2: Invalid layout direction
0x0073ede2: tGetClientRect
0x0073f608: only the main thread can process Windows messages
0x0073f8e6: role
0x0073f8f0: IDropTarget::DragEnter
0x0073f92c: tdrop target can't already have a data object
0x0073f988: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0073fa00: ScreenToClient
0x0073fa32: IDropTarget::DragLeave
0x0073fa66: IDropTarget::Drop
0x0073fb0a: CoLockObjectExternal
0x0073fb34: RegisterDragDrop
0x0073fb5e: RevokeDragDrop
0x0073fb82: aIDataObject::SetData()
0x0073fbb2: IDataObject::GetData()
0x0073fc2a: invalid value in ConvertDragEffectToResult
0x0073fc98: tinvalid value in ConvertDragResultToEffect
0x0073fed8: wxToolTipModule
0x0073ff36: invalid width value
0x0073ff5e: tooltips_class32
0x0073ff80: wxToolTip
0x0073ffde: Failed to create the tooltip '%s'
0x00740032: no hwnd for subcontrol?
0x00740074: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x007400ec: GetStockObject(DEFAULT_GUI_FONT)
0x0074012e: CreateCompatibleDC(NULL)
0x00740160: SelectObject(hfont)
0x00740188: GetTextExtentPoint32
0x007403b8: cwxTextMeasure needs a valid wxDC
0x007403fe: nwxTextMeasure needs a valid wxWindow
0x00740516: cMust not be used with non-native wxDCs
0x0074057a: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x007405f2: GetTextExtentPoint32()
0x00740636: sGetTextExtentExPoint
0x007407fe: ewxAcceleratorTable
0x007408e4: wxWindowDCImpl
0x00740cfe: "invalid window in wxWindowDCImpl
0x00740d58: wxWindowDCImpl without a window?
0x00740d9a: wxClientDCImpl
0x00741190: invalid window in wxClientDCImpl
0x007411d2: wxClientDCImpl without a window?
0x00741214: wxPaintDCImpl
0x00741604: lNULL canvas in wxPaintDCImpl ctor
0x007416f6: dwxPaintDCEx
0x00741b4e: wxPaintDCEx requires an existing device context
0x00741d20: wxRegion
0x00741d32: wxRegionIterator
0x00741e64: invalid wxRegion
0x00741e8a: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00741f02: OffsetRgn
0x00741f32: unknown region operation
0x00741f64: CombineRgn
0x00741fd4: "invalid wxRegionIterator
0x007423e0: wxMSWDCImpl
0x007423f8: msimg32
0x00742408: gdi32.dll
0x0074241c: wxGDIDLLsCleanupModule
0x0074244a: GetLayout
0x0074245e: SetLayout
0x00742472: AlphaBlend
0x00742488: GradientFill
0x0074289a: invalid clipping region
0x007428ce: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00742962: nExtSelectClipRgn
0x00742998: nCreatePolygonRgn
0x007429c6: lExtFloodFill
0x007429f4: NULL colour parameter in wxMSWDCImpl::GetPixel
0x00742ad0: "invalid icon in DrawIcon
0x00742b1c: "invalid bitmap in wxMSWDCImpl::DrawBitmap
0x00742b86: bitmap is ok but HBITMAP is NULL?
0x00742bd4: tTextOut
0x00742bf6: tGetObject(hfont)
0x00742c1a: CreateFont
0x00742c36: tSelectObject(font)
0x00742c5e: SelectObject(old font)
0x00742c94: SelectObject(pen)
0x00742cb8: SelectObject(old pen)
0x00742cee: SetBrushOrgEx()
0x00742d0e: SelectObject(brush)
0x00742d36: SelectObject(old brush)
0x00742d7c: "unknown logical function
0x00742dd4: )invalid font in wxMSWDCImpl::GetTextExtent
0x00742e38: unknown mapping mode in SetMapMode
0x00742e92: SetGraphicsMode
0x00742eb2: SetWorldTransform
0x00742eea: GetWorldTransform
0x00742f26: wxMSWDCImpl::Blit(): NULL wxDC pointer
0x00742f74: unsupported logical function
0x00742fae: BitBlt
0x00742fbc: StretchBlt
0x00742fd2: StretchDIBits
0x00743004: 0 width device?
0x0074302e: 0 height device?
0x00743098: eCreateCompatibleBitmap
0x007430d4: eCreateCompatibleDC
0x007430fc: wxDCModule
0x007431ee: SetTextColor
0x00743208: SetBkColor
0x0074321e: SetBkMode
0x00743246: rSetStretchBltMode
0x00743330: AlphaBlt(): invalid bitmap
0x00743374: cAlphaBlt(): invalid HDC
0x007435c0: wxPowerEvent
0x00743e18: wxStaticBox
0x00743e74: tshouldn't be called twice
0x00744696: wIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00744710: SetWindowPos
0x0074525c: wxIndividualLayoutConstraint
0x00745296: wxLayoutConstraints
0x00745528: tole
0x00745532: wxIEnumVARIANT::Next
0x0074555c: list
0x0074556c: wxIEnumVARIANT::Skip
0x0074559a: twxIEnumVARIANT::Reset
0x007455cc: ewxIEnumVARIANT::Clone
0x00745728: taccess
0x00745738: accHitTest
0x0074578e: accLocation
0x007457b0: eaccNavigate for 
0x007457d4: Invalid arg for accNavigate
0x0074580c: wxNAVDIR_DOWN
0x00745828: wxNAVDIR_FIRSTCHILD
0x00745850: wxNAVDIR_LASTCHILD
0x00745876: wxNAVDIR_LEFT
0x00745892: wxNAVDIR_NEXT
0x007458ae: wxNAVDIR_PREVIOUS
0x007458d2: wxNAVDIR_RIGHT
0x007458f0: wxNAVDIR_UP
0x00745908: Unknown NAVDIR symbol
0x00745934: wxAccessible::Navigate failed
0x00745970: Invalid argument passed to wxAccessible::Navigate
0x007459d4: wxAccessible::Navigate found no object in this direction
0x00745a46: Navigate not implemented
0x00745a78: Getting wxIAccessible and calling QueryInterface for Navigate
0x00745af4: No wxIAccessible
0x00745b16: QueryInterface failed
0x00745b42: Called QueryInterface for Navigate
0x00745b88: Returning element id from Navigate
0x00745bce: No object in accNavigate
0x00745c00: Failing Navigate
0x00745c30: get_accChild
0x00745c4a: Invalid arg for get_accChild
0x00745c84: GetChild failed
0x00745ca4: Invalid argument passed to GetChild
0x00745cec: Using standard interface for get_accChild
0x00745d40: QueryInterface failed in get_accChild
0x00745d8c: Not an accessible object
0x00745dce: tget_accChildCount
0x00745df4: Using standard interface for get_accChildCount
0x00745e52: Number of children was %d
0x00745e92: tget_accParent
0x00745eb0: Using standard interface to get the parent.
0x00745f08: About to call QueryInterface
0x00745f42: Failed QueryInterface
0x00745f6e: Returning S_OK for get_accParent
0x00745fc4: accDoDefaultAction
0x00745fea: Invalid arg for accDoDefaultAction
0x00746046: get_accDefaultAction
0x00746070: Invalid arg for get_accDefaultAction
0x007460ce: get_accDescription
0x007460f4: Invalid arg for get_accDescription
0x00746144: pget_accHelp
0x0074615e: Invalid arg for get_accHelp
0x007461a8: get_accHelpTopic
0x007461ca: Invalid arg for get_accHelpTopic
0x00746222: tget_accKeyboardShortcut
0x00746254: Invalid arg for get_accKeyboardShortcut
0x007462ae: eget_accName
0x007462c8: Invalid arg for get_accName
0x0074630a: eget_accRole
0x00746324: Invalid arg for get_accRole
0x0074636a: get_accState
0x00746384: Invalid arg for get_accState
0x007463cc: get_accValue
0x007463e6: Invalid arg for get_accValue
0x00746428: tget_accSelect
0x00746446: Invalid arg for accSelect
0x00746488: get_accFocus
0x007464b4: get_accSelection
0x007464d6: void*
0x007464fa: 1Multiple child objects should be selected
0x00746944: wxContextHelp
0x007469c6: win parameter can't be NULL
0x007469fe: wxContextHelpButton
0x00746a5a: window must not be NULL
0x00746ae6: wxHelpProviderModule
0x00747808: wxVariant
0x0074792c: tlong
0x00747958: wxVariantDataLong::Eq: argument mismatch
0x007479b8: gCould not convert to a long
0x007479f2: double
0x00747a1c: )wxVariantDoubleData::Eq: argument mismatch
0x00747a74: %.14g
0x00747a88: eCould not convert to a double number
0x00747ad4: bool
0x00747af8: )wxVariantDataBool::Eq: argument mismatch
0x00747b58: Unimplemented
0x00747b7a: lCould not convert to a bool
0x00747bb4: char
0x00747bd8: )wxVariantDataChar::Eq: argument mismatch
0x00747c32: rCould not convert to a char
0x00747c6c: string
0x00747c96: )wxVariantDataString::Eq: argument mismatch
0x00747d08: )wxVariantDataWxObjectPtr::Eq: argument mismatch
0x00747d6a: wxObject*
0x00747d82: %s(%p)
0x00747d90: void*
0x00747db8: wxVariantDataVoidPtr::Eq: argument mismatch
0x00747e38: datetime
0x00747e68: )wxVariantDataDateTime::Eq: argument mismatch
0x00747ec4: Invalid
0x00747ede: eCould not convert to a datetime
0x00747f20: wxVariantDataArrayString::Eq: argument mismatch
0x00747f8c: TODO
0x00747f96: arrstring
0x00747faa: longlong
0x0074800a: %lld
0x0074801e: gCould not convert to a long long
0x00748062: ulonglong
0x007480c6: %llu
0x00748148: list
0x0074816c: )wxVariantDataList::Eq: argument mismatch
0x007481dc: Invalid type for == operator
0x0074823c: )Invalid index to Delete
0x0074828c: "Invalid type for array operator
0x007482f0: )Invalid index for array
0x0074832c: Invalid type for GetCount()
0x00748364: true
0x00748376: false
0x00748920: twxArrayString: index out of bounds
0x007489bc: )wxLongLong to long conversion loss of precision
0x00748a46: wxULongLong to long conversion loss of precision
0x00749a70: "New size must be given
0x00749ab0: invalid bitmap data
0x00749af2: "Cannot create mask from palette index of a bitmap without palette
0x00749f90: wxBMPFileHandler
0x00749fb2: wxBMPResourceHandler
0x00749fdc: wxICOFileHandler
0x00749ffe: wxICOResourceHandler
0x0074a028: wxPNGResourceHandler
0x0074a080: "invalid bitmap
0x0074a0bc: Can't load bitmap '%s' from resources! Check .rc file.
0x0074a12a: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0074a1a2: GetObject(HBITMAP)
0x0074a1d2: NULL bitmap in LoadFile
0x0074a20c: NULL bitmap in SaveFile
0x0074a246: iconload
0x0074a258: No large icons found in the file '%s'.
0x0074a2a6: No small icons found in the file '%s'.
0x0074a302: Failed to load icon from the file '%s'
0x0074a350: Returning false from wxICOFileHandler::Load because of the size mismatch: actual (%d, %d), requested (%d, %d)
0x0074a462: )width and height should be either both -1 or not
0x0074a510: wxICON_QUESTION
0x0074a530: wxICON_WARNING
0x0074a54e: wxICON_ERROR
0x0074a568: wxICON_INFORMATION
0x0074a58e: Bitmap in PNG format "%s" not found, check that the resource file contains "RCDATA" resource with this name.
0x0074a668: Couldn't load resource bitmap "%s" as a PNG. Have you registered PNG image handler?
0x0074a998: Windows bitmap file
0x0074a9c8: Windows bitmap resource
0x0074a9f8: ICO icon file
0x0074aa1c: ICO resource
0x0074aa36: Windows PNG resource
0x0074aafe: DeleteObject(hDIB)
0x0074abaa: GetIconInfo
0x0074abfe: "must be implemented if used
0x0074ac42: "wxIconHandler only works with icons
0x0074b15c: TEXT
0x0074b18a: eIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0074b204: GetComputerName
0x0074b224: ws2_32.dll
0x0074b23a: WSAStartup
0x0074b250: gethostname
0x0074b268: gethostbyname
0x0074b284: gethostbyaddr
0x0074b2a0: WSACleanup
0x0074b2b6: username
0x0074b2f0: empty buffer in wxGetUserName
0x0074b330: wxWindows
0x0074b350: UserName
0x0074b362: Unknown User
0x0074b37c: HOME
0x0074b386: HOMEDRIVE
0x0074b39a: HOMEPATH
0x0074b3b0: USERPROFILE
0x0074b3d8: GetDiskFreeSpaceEx
0x0074b42a: dPostMessage(WM_QUIT)
0x0074b456: EnumWindows
0x0074b480: unexpected WaitForSingleObject() return
0x0074b4d0: WaitForSingleObject
0x0074b50a: BringWindowToTop
0x0074b52c: COMSPEC
0x0074b53c: \COMMAND.COM
0x0074b556: %s /c %s
0x0074b568: SeShutdownPrivilege
0x0074b59c: unknown wxShutdown() flag
0x0074b816: "NULL parameter in wxCreateHiddenWindow
0x0074b866: RegisterClass() in wxCreateHiddenWindow
0x0074b8b6: CreateWindow() in wxCreateHiddenWindow
0x0074b944: GetModuleFileName
0x0074b9ba: ntdll.dll
0x0074bc34: DrawStateBitmap: unknown wxDSBStates value
0x0074bd0a: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0074bd82: SetTextColor
0x0074bd9c: SetBkColor
0x0074beb4: rSetBkMode
0x0074bf14: clipboard already opened.
0x0074bf74: .Cannot open clipboard without a main window.
0x0074bff6: "clipboard is not opened
0x0074c078: wxClipboard
0x0074c124: rIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0074c19e: OleSetClipboard(NULL)
0x0074c1ce: hOleFlushClipboard
0x0074c1f4: HTML Format
0x0074c224: data is invalid
0x0074c294: dFailed to set data in wxIDataObject
0x0074d30e: ESysLink
0x0074dc90: wxGraphicsObject
0x0074dd42: aA Null Object cannot be changed
0x0074dd84: wxGraphicsPen
0x0074dda0: wxGraphicsBrush
0x0074ddc0: wxGraphicsFont
0x0074ddde: wxGraphicsBitmap
0x0074de00: wxGraphicsMatrix
0x0074de22: wxGraphicsPath
0x0074de9c: wxGraphicsContext
0x0074e100: 0wxGraphicsRenderer
0x0074e7a0: gdiplus.dll
0x0074e7b8: GdiplusStartup
0x0074e7d6: GdiplusShutdown
0x0074e7f6: GdiplusNotificationHook
0x0074e826: GdiplusNotificationUnhook
0x0074e85a: GdipAlloc
0x0074e86e: GdipFree
0x0074e880: GdipCreatePath
0x0074e89e: GdipCreatePath2
0x0074e8be: GdipCreatePath2I
0x0074e8e0: GdipClonePath
0x0074e8fc: GdipDeletePath
0x0074e91a: GdipResetPath
0x0074e936: GdipGetPointCount
0x0074e95a: GdipGetPathTypes
0x0074e97c: GdipGetPathPoints
0x0074e9a0: GdipGetPathPointsI
0x0074e9c6: GdipGetPathFillMode
0x0074e9ee: GdipSetPathFillMode
0x0074ea16: GdipGetPathData
0x0074ea36: GdipStartPathFigure
0x0074ea5e: GdipClosePathFigure
0x0074ea86: GdipClosePathFigures
0x0074eab0: GdipSetPathMarker
0x0074ead4: GdipClearPathMarkers
0x0074eafe: GdipReversePath
0x0074eb1e: GdipGetPathLastPoint
0x0074eb48: GdipAddPathLine
0x0074eb68: GdipAddPathLine2
0x0074eb8a: GdipAddPathArc
0x0074eba8: GdipAddPathBezier
0x0074ebcc: GdipAddPathBeziers
0x0074ebf2: GdipAddPathCurve
0x0074ec14: GdipAddPathCurve2
0x0074ec38: GdipAddPathCurve3
0x0074ec5c: GdipAddPathClosedCurve
0x0074ec8a: GdipAddPathClosedCurve2
0x0074ecba: GdipAddPathRectangle
0x0074ece4: GdipAddPathRectangles
0x0074ed10: GdipAddPathEllipse
0x0074ed36: GdipAddPathPie
0x0074ed54: GdipAddPathPolygon
0x0074ed7a: GdipAddPathPath
0x0074ed9a: GdipAddPathString
0x0074edbe: GdipAddPathStringI
0x0074ede4: GdipAddPathLineI
0x0074ee06: GdipAddPathLine2I
0x0074ee2a: GdipAddPathArcI
0x0074ee4a: GdipAddPathBezierI
0x0074ee70: GdipAddPathBeziersI
0x0074ee98: GdipAddPathCurveI
0x0074eebc: GdipAddPathCurve2I
0x0074eee2: GdipAddPathCurve3I
0x0074ef08: GdipAddPathClosedCurveI
0x0074ef38: GdipAddPathClosedCurve2I
0x0074ef6a: GdipAddPathRectangleI
0x0074ef96: GdipAddPathRectanglesI
0x0074efc4: GdipAddPathEllipseI
0x0074efec: GdipAddPathPieI
0x0074f00c: GdipAddPathPolygonI
0x0074f034: GdipFlattenPath
0x0074f054: GdipWindingModeOutline
0x0074f082: GdipWidenPath
0x0074f09e: GdipWarpPath
0x0074f0b8: GdipTransformPath
0x0074f0dc: GdipGetPathWorldBounds
0x0074f10a: GdipGetPathWorldBoundsI
0x0074f13a: GdipIsVisiblePathPoint
0x0074f168: GdipIsVisiblePathPointI
0x0074f198: GdipIsOutlineVisiblePathPoint
0x0074f1d4: GdipIsOutlineVisiblePathPointI
0x0074f212: GdipCreatePathIter
0x0074f238: GdipDeletePathIter
0x0074f25e: GdipPathIterNextSubpath
0x0074f28e: GdipPathIterNextSubpathPath
0x0074f2c6: GdipPathIterNextPathType
0x0074f2f8: GdipPathIterNextMarker
0x0074f326: GdipPathIterNextMarkerPath
0x0074f35c: GdipPathIterGetCount
0x0074f386: GdipPathIterGetSubpathCount
0x0074f3be: GdipPathIterIsValid
0x0074f3e6: GdipPathIterHasCurve
0x0074f410: GdipPathIterRewind
0x0074f436: GdipPathIterEnumerate
0x0074f462: GdipPathIterCopyData
0x0074f48c: GdipCreateMatrix
0x0074f4ae: GdipCreateMatrix2
0x0074f4d2: GdipCreateMatrix3
0x0074f4f6: GdipCreateMatrix3I
0x0074f51c: GdipCloneMatrix
0x0074f53c: GdipDeleteMatrix
0x0074f55e: GdipSetMatrixElements
0x0074f58a: GdipMultiplyMatrix
0x0074f5b0: GdipTranslateMatrix
0x0074f5d8: GdipScaleMatrix
0x0074f5f8: GdipRotateMatrix
0x0074f61a: GdipShearMatrix
0x0074f63a: GdipInvertMatrix
0x0074f65c: GdipTransformMatrixPoints
0x0074f690: GdipTransformMatrixPointsI
0x0074f6c6: GdipVectorTransformMatrixPoints
0x0074f706: GdipVectorTransformMatrixPointsI
0x0074f748: GdipGetMatrixElements
0x0074f774: GdipIsMatrixInvertible
0x0074f7a2: GdipIsMatrixIdentity
0x0074f7cc: GdipIsMatrixEqual
0x0074f7f0: GdipCreateRegion
0x0074f812: GdipCreateRegionRect
0x0074f83c: GdipCreateRegionRectI
0x0074f868: GdipCreateRegionPath
0x0074f892: GdipCreateRegionRgnData
0x0074f8c2: GdipCreateRegionHrgn
0x0074f8ec: GdipCloneRegion
0x0074f90c: GdipDeleteRegion
0x0074f92e: GdipSetInfinite
0x0074f94e: GdipSetEmpty
0x0074f968: GdipCombineRegionRect
0x0074f994: GdipCombineRegionRectI
0x0074f9c2: GdipCombineRegionPath
0x0074f9ee: GdipCombineRegionRegion
0x0074fa1e: GdipTranslateRegion
0x0074fa46: GdipTranslateRegionI
0x0074fa70: GdipTransformRegion
0x0074fa98: GdipGetRegionBounds
0x0074fac0: GdipGetRegionBoundsI
0x0074faea: GdipGetRegionHRgn
0x0074fb0e: GdipIsEmptyRegion
0x0074fb32: GdipIsInfiniteRegion
0x0074fb5c: GdipIsEqualRegion
0x0074fb80: GdipGetRegionDataSize
0x0074fbac: GdipGetRegionData
0x0074fbd0: GdipIsVisibleRegionPoint
0x0074fc02: GdipIsVisibleRegionPointI
0x0074fc36: GdipIsVisibleRegionRect
0x0074fc66: GdipIsVisibleRegionRectI
0x0074fc98: GdipGetRegionScansCount
0x0074fcc8: GdipGetRegionScans
0x0074fcee: GdipGetRegionScansI
0x0074fd16: GdipCloneBrush
0x0074fd34: GdipDeleteBrush
0x0074fd54: GdipGetBrushType
0x0074fd76: GdipCreateHatchBrush
0x0074fda0: GdipGetHatchStyle
0x0074fdc4: GdipGetHatchForegroundColor
0x0074fdfc: GdipGetHatchBackgroundColor
0x0074fe34: GdipCreateTexture
0x0074fe58: GdipCreateTexture2
0x0074fe7e: GdipCreateTextureIA
0x0074fea6: GdipCreateTexture2I
0x0074fece: GdipCreateTextureIAI
0x0074fef8: GdipGetTextureTransform
0x0074ff28: GdipSetTextureTransform
0x0074ff58: GdipResetTextureTransform
0x0074ff8c: GdipMultiplyTextureTransform
0x0074ffc6: GdipTranslateTextureTransform
0x00750002: GdipScaleTextureTransform
0x00750036: GdipRotateTextureTransform
0x0075006c: GdipSetTextureWrapMode
0x0075009a: GdipGetTextureWrapMode
0x007500c8: GdipGetTextureImage
0x007500f0: GdipCreateSolidFill
0x00750118: GdipSetSolidFillColor
0x00750144: GdipGetSolidFillColor
0x00750170: GdipCreateLineBrush
0x00750198: GdipCreateLineBrushI
0x007501c2: GdipCreateLineBrushFromRect
0x007501fa: GdipCreateLineBrushFromRectI
0x00750234: GdipCreateLineBrushFromRectWithAngle
0x0075027e: GdipCreateLineBrushFromRectWithAngleI
0x007502ca: GdipSetLineColors
0x007502ee: GdipGetLineColors
0x00750312: GdipGetLineRect
0x00750332: GdipGetLineRectI
0x00750354: GdipSetLineGammaCorrection
0x0075038a: GdipGetLineGammaCorrection
0x007503c0: GdipGetLineBlendCount
0x007503ec: GdipGetLineBlend
0x0075040e: GdipSetLineBlend
0x00750430: GdipGetLinePresetBlendCount
0x00750468: GdipGetLinePresetBlend
0x00750496: GdipSetLinePresetBlend
0x007504c4: GdipSetLineSigmaBlend
0x007504f0: GdipSetLineLinearBlend
0x0075051e: GdipSetLineWrapMode
0x00750546: GdipGetLineWrapMode
0x0075056e: GdipGetLineTransform
0x00750598: GdipSetLineTransform
0x007505c2: GdipResetLineTransform
0x007505f0: GdipMultiplyLineTransform
0x00750624: GdipTranslateLineTransform
0x0075065a: GdipScaleLineTransform
0x00750688: GdipRotateLineTransform
0x007506b8: GdipCreatePathGradient
0x007506e6: GdipCreatePathGradientI
0x00750716: GdipCreatePathGradientFromPath
0x00750754: GdipGetPathGradientCenterColor
0x00750792: GdipSetPathGradientCenterColor
0x007507d0: GdipGetPathGradientSurroundColorsWithCount
0x00750826: GdipSetPathGradientSurroundColorsWithCount
0x0075087c: GdipGetPathGradientPath
0x007508ac: GdipSetPathGradientPath
0x007508dc: GdipGetPathGradientCenterPoint
0x0075091a: GdipGetPathGradientCenterPointI
0x0075095a: GdipSetPathGradientCenterPoint
0x00750998: GdipSetPathGradientCenterPointI
0x007509d8: GdipGetPathGradientRect
0x00750a08: GdipGetPathGradientRectI
0x00750a3a: GdipGetPathGradientPointCount
0x00750a76: GdipGetPathGradientSurroundColorCount
0x00750ac2: GdipSetPathGradientGammaCorrection
0x00750b08: GdipGetPathGradientGammaCorrection
0x00750b4e: GdipGetPathGradientBlendCount
0x00750b8a: GdipGetPathGradientBlend
0x00750bbc: GdipSetPathGradientBlend
0x00750bee: GdipGetPathGradientPresetBlendCount
0x00750c36: GdipGetPathGradientPresetBlend
0x00750c74: GdipSetPathGradientPresetBlend
0x00750cb2: GdipSetPathGradientSigmaBlend
0x00750cee: GdipSetPathGradientLinearBlend
0x00750d2c: GdipGetPathGradientWrapMode
0x00750d64: GdipSetPathGradientWrapMode
0x00750d9c: GdipGetPathGradientTransform
0x00750dd6: GdipSetPathGradientTransform
0x00750e10: GdipResetPathGradientTransform
0x00750e4e: GdipMultiplyPathGradientTransform
0x00750e92: GdipTranslatePathGradientTransform
0x00750ed8: GdipScalePathGradientTransform
0x00750f16: GdipRotatePathGradientTransform
0x00750f56: GdipGetPathGradientFocusScales
0x00750f94: GdipSetPathGradientFocusScales
0x00750fd2: GdipCreatePen1
0x00750ff0: GdipCreatePen2
0x0075100e: GdipClonePen
0x00751028: GdipDeletePen
0x00751044: GdipSetPenWidth
0x00751064: GdipGetPenWidth
0x00751084: GdipSetPenUnit
0x007510a2: GdipGetPenUnit
0x007510c0: GdipSetPenLineCap197819
0x007510f0: GdipSetPenStartCap
0x00751116: GdipSetPenEndCap
0x00751138: GdipSetPenDashCap197819
0x00751168: GdipGetPenStartCap
0x0075118e: GdipGetPenEndCap
0x007511b0: GdipGetPenDashCap197819
0x007511e0: GdipSetPenLineJoin
0x00751206: GdipGetPenLineJoin
0x0075122c: GdipSetPenCustomStartCap
0x0075125e: GdipGetPenCustomStartCap
0x00751290: GdipSetPenCustomEndCap
0x007512be: GdipGetPenCustomEndCap
0x007512ec: GdipSetPenMiterLimit
0x00751316: GdipGetPenMiterLimit
0x00751340: GdipSetPenMode
0x0075135e: GdipGetPenMode
0x0075137c: GdipSetPenTransform
0x007513a4: GdipGetPenTransform
0x007513cc: GdipResetPenTransform
0x007513f8: GdipMultiplyPenTransform
0x0075142a: GdipTranslatePenTransform
0x0075145e: GdipScalePenTransform
0x0075148a: GdipRotatePenTransform
0x007514b8: GdipSetPenColor
0x007514d8: GdipGetPenColor
0x007514f8: GdipSetPenBrushFill
0x00751520: GdipGetPenBrushFill
0x00751548: GdipGetPenFillType
0x0075156e: GdipGetPenDashStyle
0x00751596: GdipSetPenDashStyle
0x007515be: GdipGetPenDashOffset
0x007515e8: GdipSetPenDashOffset
0x00751612: GdipGetPenDashCount
0x0075163a: GdipSetPenDashArray
0x00751662: GdipGetPenDashArray
0x0075168a: GdipGetPenCompoundCount
0x007516ba: GdipSetPenCompoundArray
0x007516ea: GdipGetPenCompoundArray
0x0075171a: GdipCreateCustomLineCap
0x0075174a: GdipDeleteCustomLineCap
0x0075177a: GdipCloneCustomLineCap
0x007517a8: GdipGetCustomLineCapType
0x007517da: GdipSetCustomLineCapStrokeCaps
0x00751818: GdipGetCustomLineCapStrokeCaps
0x00751856: GdipSetCustomLineCapStrokeJoin
0x00751894: GdipGetCustomLineCapStrokeJoin
0x007518d2: GdipSetCustomLineCapBaseCap
0x0075190a: GdipGetCustomLineCapBaseCap
0x00751942: GdipSetCustomLineCapBaseInset
0x0075197e: GdipGetCustomLineCapBaseInset
0x007519ba: GdipSetCustomLineCapWidthScale
0x007519f8: GdipGetCustomLineCapWidthScale
0x00751a36: GdipCreateAdjustableArrowCap
0x00751a70: GdipSetAdjustableArrowCapHeight
0x00751ab0: GdipGetAdjustableArrowCapHeight
0x00751af0: GdipSetAdjustableArrowCapWidth
0x00751b2e: GdipGetAdjustableArrowCapWidth
0x00751b6c: GdipSetAdjustableArrowCapMiddleInset
0x00751bb6: GdipGetAdjustableArrowCapMiddleInset
0x00751c00: GdipSetAdjustableArrowCapFillState
0x00751c46: GdipGetAdjustableArrowCapFillState
0x00751c8c: GdipLoadImageFromStream
0x00751cbc: GdipLoadImageFromFile
0x00751ce8: GdipLoadImageFromStreamICM
0x00751d1e: GdipLoadImageFromFileICM
0x00751d50: GdipCloneImage
0x00751d6e: GdipDisposeImage
0x00751d90: GdipSaveImageToFile
0x00751db8: GdipSaveImageToStream
0x00751de4: GdipSaveAdd
0x00751dfc: GdipSaveAddImage
0x00751e1e: GdipGetImageGraphicsContext
0x00751e56: GdipGetImageBounds
0x00751e7c: GdipGetImageDimension
0x00751ea8: GdipGetImageType
0x00751eca: GdipGetImageWidth
0x00751eee: GdipGetImageHeight
0x00751f14: GdipGetImageHorizontalResolution
0x00751f56: GdipGetImageVerticalResolution
0x00751f94: GdipGetImageFlags
0x00751fb8: GdipGetImageRawFormat
0x00751fe4: GdipGetImagePixelFormat
0x00752014: GdipGetImageThumbnail
0x00752040: GdipGetEncoderParameterListSize
0x00752080: GdipGetEncoderParameterList
0x007520b8: GdipImageGetFrameDimensionsCount
0x007520fa: GdipImageGetFrameDimensionsList
0x0075213a: GdipImageGetFrameCount
0x00752168: GdipImageSelectActiveFrame
0x0075219e: GdipImageRotateFlip
0x007521c6: GdipGetImagePalette
0x007521ee: GdipSetImagePalette
0x00752216: GdipGetImagePaletteSize
0x00752246: GdipGetPropertyCount
0x00752270: GdipGetPropertyIdList
0x0075229c: GdipGetPropertyItemSize
0x007522cc: GdipGetPropertyItem
0x007522f4: GdipGetPropertySize
0x0075231c: GdipGetAllPropertyItems
0x0075234c: GdipRemovePropertyItem
0x0075237a: GdipSetPropertyItem
0x007523a2: GdipImageForceValidation
0x007523d4: GdipCreateBitmapFromStream
0x0075240a: GdipCreateBitmapFromFile
0x0075243c: GdipCreateBitmapFromStreamICM
0x00752478: GdipCreateBitmapFromFileICM
0x007524b0: GdipCreateBitmapFromScan0
0x007524e4: GdipCreateBitmapFromGraphics
0x0075251e: GdipCreateBitmapFromDirectDrawSurface
0x0075256a: GdipCreateBitmapFromGdiDib
0x007525a0: GdipCreateBitmapFromHBITMAP
0x007525d8: GdipCreateHBITMAPFromBitmap
0x00752610: GdipCreateBitmapFromHICON
0x00752644: GdipCreateHICONFromBitmap
0x00752678: GdipCreateBitmapFromResource
0x007526b2: GdipCloneBitmapArea
0x007526da: GdipCloneBitmapAreaI
0x00752704: GdipBitmapLockBits
0x0075272a: GdipBitmapUnlockBits
0x00752754: GdipBitmapGetPixel
0x0075277a: GdipBitmapSetPixel
0x007527a0: GdipBitmapSetResolution
0x007527d0: GdipCreateImageAttributes
0x00752804: GdipCloneImageAttributes
0x00752836: GdipDisposeImageAttributes
0x0075286c: GdipSetImageAttributesToIdentity
0x007528ae: GdipResetImageAttributes
0x007528e0: GdipSetImageAttributesColorMatrix
0x00752924: GdipSetImageAttributesThreshold
0x00752964: GdipSetImageAttributesGamma
0x0075299c: GdipSetImageAttributesNoOp
0x007529d2: GdipSetImageAttributesColorKeys
0x00752a12: GdipSetImageAttributesOutputChannel
0x00752a5a: GdipSetImageAttributesOutputChannelColorProfile
0x00752aba: GdipSetImageAttributesRemapTable
0x00752afc: GdipSetImageAttributesWrapMode
0x00752b3a: GdipGetImageAttributesAdjustedPalette
0x00752b86: GdipFlush
0x00752b9a: GdipCreateFromHDC
0x00752bbe: GdipCreateFromHDC2
0x00752be4: GdipCreateFromHWND
0x00752c0a: GdipCreateFromHWNDICM
0x00752c36: GdipDeleteGraphics
0x00752c5c: GdipGetDC
0x00752c70: GdipReleaseDC
0x00752c8c: GdipSetCompositingMode
0x00752cba: GdipGetCompositingMode
0x00752ce8: GdipSetRenderingOrigin
0x00752d16: GdipGetRenderingOrigin
0x00752d44: GdipSetCompositingQuality
0x00752d78: GdipGetCompositingQuality
0x00752dac: GdipSetSmoothingMode
0x00752dd6: GdipGetSmoothingMode
0x00752e00: GdipSetPixelOffsetMode
0x00752e2e: GdipGetPixelOffsetMode
0x00752e5c: GdipSetTextRenderingHint
0x00752e8e: GdipGetTextRenderingHint
0x00752ec0: GdipSetTextContrast
0x00752ee8: GdipGetTextContrast
0x00752f10: GdipSetInterpolationMode
0x00752f42: GdipGetInterpolationMode
0x00752f74: GdipSetWorldTransform
0x00752fa0: GdipResetWorldTransform
0x00752fd0: GdipMultiplyWorldTransform
0x00753006: GdipTranslateWorldTransform
0x0075303e: GdipScaleWorldTransform
0x0075306e: GdipRotateWorldTransform
0x007530a0: GdipGetWorldTransform
0x007530cc: GdipResetPageTransform
0x007530fa: GdipGetPageUnit
0x0075311a: GdipGetPageScale
0x0075313c: GdipSetPageUnit
0x0075315c: GdipSetPageScale
0x0075317e: GdipGetDpiX
0x00753196: GdipGetDpiY
0x007531ae: GdipTransformPoints
0x007531d6: GdipTransformPointsI
0x00753200: GdipGetNearestColor
0x00753228: GdipDrawLine
0x00753242: GdipDrawLineI
0x0075325e: GdipDrawLines
0x0075327a: GdipDrawLinesI
0x00753298: GdipDrawArc
0x007532b0: GdipDrawArcI
0x007532ca: GdipDrawBezier
0x007532e8: GdipDrawBezierI
0x00753308: GdipDrawBeziers
0x00753328: GdipDrawBeziersI
0x0075334a: GdipDrawRectangle
0x0075336e: GdipDrawRectangleI
0x00753394: GdipDrawRectangles
0x007533ba: GdipDrawRectanglesI
0x007533e2: GdipDrawEllipse
0x00753402: GdipDrawEllipseI
0x00753424: GdipDrawPie
0x0075343c: GdipDrawPieI
0x00753456: GdipDrawPolygon
0x00753476: GdipDrawPolygonI
0x00753498: GdipDrawPath
0x007534b2: GdipDrawCurve
0x007534ce: GdipDrawCurveI
0x007534ec: GdipDrawCurve2
0x0075350a: GdipDrawCurve2I
0x0075352a: GdipDrawCurve3
0x00753548: GdipDrawCurve3I
0x00753568: GdipDrawClosedCurve
0x00753590: GdipDrawClosedCurveI
0x007535ba: GdipDrawClosedCurve2
0x007535e4: GdipDrawClosedCurve2I
0x00753610: GdipGraphicsClear
0x00753634: GdipFillRectangle
0x00753658: GdipFillRectangleI
0x0075367e: GdipFillRectangles
0x007536a4: GdipFillRectanglesI
0x007536cc: GdipFillPolygon
0x007536ec: GdipFillPolygonI
0x0075370e: GdipFillPolygon2
0x00753730: GdipFillPolygon2I
0x00753754: GdipFillEllipse
0x00753774: GdipFillEllipseI
0x00753796: GdipFillPie
0x007537ae: GdipFillPieI
0x007537c8: GdipFillPath
0x007537e2: GdipFillClosedCurve
0x0075380a: GdipFillClosedCurveI
0x00753834: GdipFillClosedCurve2
0x0075385e: GdipFillClosedCurve2I
0x0075388a: GdipFillRegion
0x007538a8: GdipDrawImage
0x007538c4: GdipDrawImageI
0x007538e2: GdipDrawImageRect
0x00753906: GdipDrawImageRectI
0x0075392c: GdipDrawImagePoints
0x00753954: GdipDrawImagePointsI
0x0075397e: GdipDrawImagePointRect
0x007539ac: GdipDrawImagePointRectI
0x007539dc: GdipDrawImageRectRect
0x00753a08: GdipDrawImageRectRectI
0x00753a36: GdipDrawImagePointsRect
0x00753a66: GdipDrawImagePointsRectI
0x00753a98: GdipEnumerateMetafileDestPoint
0x00753ad6: GdipEnumerateMetafileDestPointI
0x00753b16: GdipEnumerateMetafileDestRect
0x00753b52: GdipEnumerateMetafileDestRectI
0x00753b90: GdipEnumerateMetafileDestPoints
0x00753bd0: GdipEnumerateMetafileDestPointsI
0x00753c12: GdipEnumerateMetafileSrcRectDestPoint
0x00753c5e: GdipEnumerateMetafileSrcRectDestPointI
0x00753cac: GdipEnumerateMetafileSrcRectDestRect
0x00753cf6: GdipEnumerateMetafileSrcRectDestRectI
0x00753d42: GdipEnumerateMetafileSrcRectDestPoints
0x00753d90: GdipEnumerateMetafileSrcRectDestPointsI
0x00753de0: GdipPlayMetafileRecord
0x00753e0e: GdipSetClipGraphics
0x00753e36: GdipSetClipRect
0x00753e56: GdipSetClipRectI
0x00753e78: GdipSetClipPath
0x00753e98: GdipSetClipRegion
0x00753ebc: GdipSetClipHrgn
0x00753edc: GdipResetClip
0x00753ef8: GdipTranslateClip
0x00753f1c: GdipTranslateClipI
0x00753f42: GdipGetClip
0x00753f5a: GdipGetClipBounds
0x00753f7e: GdipGetClipBoundsI
0x00753fa4: GdipIsClipEmpty
0x00753fc4: GdipGetVisibleClipBounds
0x00753ff6: GdipGetVisibleClipBoundsI
0x0075402a: GdipIsVisibleClipEmpty
0x00754058: GdipIsVisiblePoint
0x0075407e: GdipIsVisiblePointI
0x007540a6: GdipIsVisibleRect
0x007540ca: GdipIsVisibleRectI
0x007540f0: GdipSaveGraphics
0x00754112: GdipRestoreGraphics
0x0075413a: GdipBeginContainer
0x00754160: GdipBeginContainerI
0x00754188: GdipBeginContainer2
0x007541b0: GdipEndContainer
0x007541d2: GdipGetMetafileHeaderFromEmf
0x0075420c: GdipGetMetafileHeaderFromFile
0x00754248: GdipGetMetafileHeaderFromStream
0x00754288: GdipGetMetafileHeaderFromMetafile
0x007542cc: GdipGetHemfFromMetafile
0x007542fc: GdipCreateStreamOnFile
0x0075432a: GdipCreateMetafileFromWmf
0x0075435e: GdipCreateMetafileFromEmf
0x00754392: GdipCreateMetafileFromFile
0x007543c8: GdipCreateMetafileFromWmfFile
0x00754404: GdipCreateMetafileFromStream
0x0075443e: GdipRecordMetafile
0x00754464: GdipRecordMetafileI
0x0075448c: GdipRecordMetafileFileName
0x007544c2: GdipRecordMetafileFileNameI
0x007544fa: GdipRecordMetafileStream
0x0075452c: GdipRecordMetafileStreamI
0x00754560: GdipSetMetafileDownLevelRasterizationLimit
0x007545b6: GdipGetMetafileDownLevelRasterizationLimit
0x0075460c: GdipGetImageDecodersSize
0x0075463e: GdipGetImageDecoders
0x00754668: GdipGetImageEncodersSize
0x0075469a: GdipGetImageEncoders
0x007546c4: GdipComment
0x007546dc: GdipCreateFontFamilyFromName
0x00754716: GdipDeleteFontFamily
0x00754740: GdipCloneFontFamily
0x00754768: GdipGetGenericFontFamilySansSerif
0x007547ac: GdipGetGenericFontFamilySerif
0x007547e8: GdipGetGenericFontFamilyMonospace
0x0075482c: GdipGetFamilyName
0x00754850: GdipIsStyleAvailable
0x0075487a: GdipGetEmHeight
0x0075489a: GdipGetCellAscent
0x007548be: GdipGetCellDescent
0x007548e4: GdipGetLineSpacing
0x0075490a: GdipCreateFontFromDC
0x00754934: GdipCreateFontFromLogfontA
0x0075496a: GdipCreateFontFromLogfontW
0x007549a0: GdipCreateFont
0x007549be: GdipCloneFont
0x007549da: GdipDeleteFont
0x007549f8: GdipGetFamily
0x00754a14: GdipGetFontStyle
0x00754a36: GdipGetFontSize
0x00754a56: GdipGetFontUnit
0x00754a76: GdipGetFontHeight
0x00754a9a: GdipGetFontHeightGivenDPI
0x00754ace: GdipGetLogFontA
0x00754aee: GdipGetLogFontW
0x00754b0e: GdipNewInstalledFontCollection
0x00754b4c: GdipNewPrivateFontCollection
0x00754b86: GdipDeletePrivateFontCollection
0x00754bc6: GdipGetFontCollectionFamilyCount
0x00754c08: GdipGetFontCollectionFamilyList
0x00754c48: GdipPrivateAddFontFile
0x00754c76: GdipPrivateAddMemoryFont
0x00754ca8: GdipDrawString
0x00754cc6: GdipMeasureString
0x00754cea: GdipMeasureCharacterRanges
0x00754d20: GdipDrawDriverString
0x00754d4a: GdipMeasureDriverString
0x00754d7a: GdipCreateStringFormat
0x00754da8: GdipStringFormatGetGenericDefault
0x00754dec: GdipStringFormatGetGenericTypographic
0x00754e38: GdipDeleteStringFormat
0x00754e66: GdipCloneStringFormat
0x00754e92: GdipSetStringFormatFlags
0x00754ec4: GdipGetStringFormatFlags
0x00754ef6: GdipSetStringFormatAlign
0x00754f28: GdipGetStringFormatAlign
0x00754f5a: GdipSetStringFormatLineAlign
0x00754f94: GdipGetStringFormatLineAlign
0x00754fce: GdipSetStringFormatTrimming
0x00755006: GdipGetStringFormatTrimming
0x0075503e: GdipSetStringFormatHotkeyPrefix
0x0075507e: GdipGetStringFormatHotkeyPrefix
0x007550be: GdipSetStringFormatTabStops
0x007550f6: GdipGetStringFormatTabStops
0x0075512e: GdipGetStringFormatTabStopCount
0x0075516e: GdipSetStringFormatDigitSubstitution
0x007551b8: GdipGetStringFormatDigitSubstitution
0x00755202: GdipGetStringFormatMeasurableCharacterRangeCount
0x00755264: GdipSetStringFormatMeasurableCharacterRanges
0x007552be: GdipCreateCachedBitmap
0x007552ec: GdipDeleteCachedBitmap
0x0075531a: GdipDrawCachedBitmap
0x00755344: GdipSetImageAttributesCachedBackground
0x00755392: GdipTestControl
0x007553b2: wxGdiPlusModule
0x00755460: wxTextValidator
0x00755940: invalid key in wxRegKey::GetStdKeyName
0x00755a6c: invalid key prefix in wxRegKey::ExtractKeyName.
0x00755adc: ynon root hkey passed to wxRegKey::GetStdKeyFromHkey.
0x00755b60: )key should be opened in GetKeyInfo
0x00755cf6: "registry hives can't be renamed
0x00755edc: .advapi32
0x0075604e: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x007560c6: ExpandEnvironmentStrings
0x007561ce: dword:%08x
0x0075624c: .HKEY_CLASSES_ROOT
0x00756272: HKCR
0x0075627c: HKEY_CURRENT_USER
0x007562a0: HKCU
0x007562aa: HKEY_LOCAL_MACHINE
0x007562d0: HKLM
0x007562da: HKEY_USERS
0x007562f8: HKEY_PERFORMANCE_DATA
0x00756324: HKPD
0x0075632e: HKEY_CURRENT_CONFIG
0x00756356: HKCC
0x00756360: HKEY_DYN_DATA
0x0075637c: HKDD
0x0075642c: wxArrayString: index out of bounds
0x00756472: EXPAND_SZ
0x00756486: BINARY
0x00756494: DWORD
0x007564a0: DWORD_BIG_ENDIAN
0x007564c2: MULTI_SZ
0x007564d4: RESOURCE_LIST
0x007564f0: FULL_RESOURCE_DESCRIPTOR
0x00756522: RESOURCE_REQUIREMENTS_LIST
0x00756558: QWORD
0x00756678: %02x
0x007568ea: eIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00756964: CreateEvent(wake)
0x0075699a: eCloseHandle(wake)
0x007569c8: SetEvent(wake)
0x00756f6e: timer can only be started from the main thread
0x00757026: "create wxApp before calling this
0x00757320: "can't reenter a message loop
0x00757370: Use ScheduleExit() on not running loop
0x0075747e: can't call ScheduleExit() if not running
0x007574e0: wxTimerHiddenWindowModule
0x00757576: )bogus timer id
0x0075759e: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00757616: DestroyWindow(wxTimerHiddenWindow)
0x0075765c: UnregisterClass("wxTimerHiddenWindow")
0x007576aa: wxTimerHiddenWindow
0x00757928: wxStatusBarGeneric
0x007581bc: "status bar field count mismatch
0x00758238: "invalid status bar field index
0x00758468: wxThumbBarButton
0x007584ea: wIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00758564: ITaskbarList3::Init
0x0075873e: ITaskbarList3::ThumbBarAddButtons
0x00758782: ITaskbarList3::ThumbBarUpdateButtons
0x0075889c: lCoCreateInstance(wxCLSID_DestinationList)
0x007588f2: Tasks
0x007588fe: Frequent
0x00758910: Recent
0x00758c4e: rCan't get direct access to initialized pointer
0x00758cae: shlwapi.dll
0x00758cc6: SHStrDupW
0x007590c2: "Cannot read XPM from stream of unknown size
0x0075912e: "NULL XPM data
0x0075918e: "XPM colormaps this large not supported.
0x0075ac20: wxColour
0x0075ac3a: ( %d , %d , %d , %
0x0075ac60: [^)] )
0x0075ac6e: ( %d , %d , %d )
0x0075ac98: rgb(%d, %d, %d)
0x0075acb8: rgba(%d, %d, %d, %s)
0x0075ace2: #%02X%02X%02X
0x0075acfe: #%02X%02X%02X%02X
0x0075ad22: rgb(??, ??, ??)
0x0075ad42: #??????
0x0075ad70: Invalid wxColour -> wxString conversion flags
0x0075ade0: NULL output parameter
0x0075bcc8: BUTTON
0x0075bd00: "radio button not a child of its parent?
0x0075bda0: )wxRadioButton::m_isChecked is out of sync?
0x0075c720: LISTBOX
0x0075d0c0: )only one of listbox selection modes can be specified
0x0075d154: Conflicting styles wxLB_ALWAYS_SB and wxLB_NO_SB.
0x0075d1d4: invalid index in wxListBox::EnsureVisible
0x0075d242: "invalid index in wxListBox::SetFirstItem
0x0075d2a4: minvalid index in wxListBox::Delete
0x0075d316: invalid index in wxListBox::SetSelection
0x0075d374: invalid index in wxListBox::Selected
0x0075d3d6: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0075d44e: LB_GETITEMDATA
0x0075d47e: aLB_SETITEMDATA failed
0x0075d4b8: sListBox_GetSelCount failed
0x0075d4f0: ListBox_GetSelItems failed
0x0075d54e: GetSelection() can't be used with multiple-selection listboxes, use GetSelections() instead.
0x0075d610: ginvalid index in wxListBox::GetString
0x0075d666: ginvalid index in wxListBox::SetString
0x0075d6be: tinvalid index in wxListBox::GetItemRect
0x0075d730: DISPLAY
0x0075d788: wxArrayString: index out of bounds
0x0075d7f8: tGetClientRect
0x0075d82e: )index out of bounds
0x0075d894: size of this DC hadn't been set and is unknown
0x0075e4f0: wxListBox
0x0075e58c: "invalid string in wxListBox::SetFirstItem
0x0075e69a: this style flag is ignored by wxChoice, you probably want to use a wxComboBox
0x0075e736: COMBOBOX
0x0075e748: EDIT
0x0075f05e: invalid item index in wxChoice::Delete
0x0075f0b4: ginvalid item index in wxChoice::SetString
0x0075f120: "Invalid index
0x0075f142: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0075f1ba: SendMessage(CB_GETLBTEXT)
0x0075f200: aCB_SETITEMDATA
0x0075f232: aCB_GETITEMDATA
0x0075f278: can't popup/dismiss the list for simple combo box
0x0075f31a: wxArrayString: index out of bounds
0x0075f378: )index out of bounds
0x0075f57c: Slider minimum must be strictly less than the maximum.
0x0075f61c: incompatible slider direction and orientation
0x0075f678: STATIC
0x0075f686: msctls_trackbar32
0x0075ff2c: subwindow index out of range
0x00760220: STATIC
0x00760a56: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00760ace: DeleteObject(hDIB)
0x00760c16: GetWindowRect
0x00760dee: wxStaticBitmap
0x00761624: "unreachable
0x00761658: BUTTON
0x007616de: "event.GetInt() returned an invalid checkbox state
0x0076176e: unexpected Get3StateValue() return value
0x0076209a: eSetting a 2-state checkbox to undetermined state
0x0076210e: DoGet3StateValue() says the 2-state checkbox is in an undetermined/third state
0x007622fa: "should have combo as parent
0x00762338: "should have a parent
0x00762364: EDIT
0x0076238c: read-only combobox doesn't have any edit control
0x007623f8: combobox without edit control?
0x00762446: wCOMBOBOX
0x00762fb4: wxArrayString: index out of bounds
0x00763040: Can't set layout direction for invalid window
0x0076309c: Invalid layout direction
0x007631ac: ScrollBar
0x007631e8: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00763260: GetScrollInfo
0x00763bc8: BUTTON
0x00763bd6: msw.staticbox.optimized-paint
0x00763c3c: Label window can't be null
0x00763c72: msw.staticbox.htclient
0x007644c8: )size of this DC hadn't been set and is unknown
0x00764540: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x007645b8: CreateRectRgn()
0x007646f6: initializing twice?
0x0076472a: SelectClipRgn
0x007649e8: eIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00764a62: CreateUpDownControl
0x00764a9e: "scrolling what?
0x007653d8: wxBitmapButton
0x0076542e: Must have a valid parent
0x00765e66: ()en
0x007661aa: option should have at least one name
0x00766236: Short option contains invalid characters
0x007662c6: Long option contains invalid characters
0x00766326: ptype mismatch in wxCmdLineArg
0x0076638e: Unknown option 
0x007663b4: cunknown command line entry type
0x00766424: )duplicate switch
0x00766450: nduplicate option
0x007664a6: )all parameters after the one with wxCMD_LINE_PARAM_MULTIPLE style will be ignored
0x00766578: a required parameter can't follow an optional one
0x007665f8: text can't be empty
0x0076662c: "NULL pointer in wxCmdLineOption::Found
0x0076669c: invalid param index
0x00766790: unknown option type
0x0076683e: all parameters after the one with wxCMD_LINE_PARAM_MULTIPLE style are ignored
0x00766964: .%s%s
0x00766970: no wxMessageOutput object?
0x007669b4: s[-]
0x007669ce: option with only a long name while long options are disabled
0x00766a48: option without either short or long name
0x00766b10: twxArrayString: index out of bounds
0x00766b7e: wxString: index out of bounds
0x00766c0a: Hkind mismatch in wxCmdLineArg
0x00768282: Message
0x007682d0: CLog message
0x007683ba: wxArrayString: index out of bounds
0x007684a8: invalid value for wxBoxSizer orientation
0x007685ba: shouldn't be called twice
0x00768628: strftime() failed
0x0076865c: log.txt
0x00768a20:         
0x00768c58: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x007697c4: wxRichMessageDialog
0x0076a1d6: shouldn't be called twice
0x0076a2c2: Calling IsChecked() doesn't make sense for a three state checkbox, Use Get3StateValue() instead
0x0076a490: wxGenericMessageDialog
0x0076b18c: "bad buffer type in wxTextBuffer::GetEOL.
0x0076b270: )unknown line terminator
0x0076b3e8: wxChoice
0x0076bcbc: twxArrayString: index out of bounds
0x0076bd16: kwxCheckBox
0x0076bd44: nwxRadioButton
0x0076bd9c: "radio button not a child of its parent?
0x0076be4c: wxFontMapperModule
0x0076bed4: wxAppTraits::CreateFontMapper() failed
0x0076bf22: /wxWindows/FontMapper
0x0076bf84: an absolute path should be given to wxFontMapper::SetConfigPath()
0x0076c032: )should be a relative path
0x0076c068: Charsets
0x0076c09e: corrupted config data: invalid encoding %ld for charset '%s' ignored
0x0076c128: Aliases
0x0076c294: 8859-%u
0x0076c2a4: 8859
0x0076c2ae: WINDOWS
0x0076c30c: wxFontMapper::GetEncoding(): invalid index
0x0076c550: ISO-8859-1
0x0076c566: ISO8859-1
0x0076c57a: iso88591
0x0076c58c: 8859-1
0x0076c59a: iso_8859_1
0x0076c5b0: ISO-8859-2
0x0076c5c6: ISO8859-2
0x0076c5da: iso88592
0x0076c5ec: 8859-2
0x0076c5fa: ISO-8859-3
0x0076c610: ISO8859-3
0x0076c624: iso88593
0x0076c636: 8859-3
0x0076c644: ISO-8859-4
0x0076c65a: ISO8859-4
0x0076c66e: iso88594
0x0076c680: 8859-4
0x0076c68e: ISO-8859-5
0x0076c6a4: ISO8859-5
0x0076c6b8: iso88595
0x0076c6ca: 8859-5
0x0076c6d8: ISO-8859-6
0x0076c6ee: ISO8859-6
0x0076c702: iso88596
0x0076c714: 8859-6
0x0076c722: ISO-8859-7
0x0076c738: ISO8859-7
0x0076c74c: iso88597
0x0076c75e: 8859-7
0x0076c76c: ISO-8859-8
0x0076c782: ISO8859-8
0x0076c796: iso88598
0x0076c7a8: 8859-8
0x0076c7b6: ISO-8859-9
0x0076c7cc: ISO8859-9
0x0076c7e0: iso88599
0x0076c7f2: 8859-9
0x0076c800: ISO-8859-10
0x0076c818: ISO8859-10
0x0076c82e: iso885910
0x0076c842: 8859-10
0x0076c852: ISO-8859-11
0x0076c86a: ISO8859-11
0x0076c880: iso885911
0x0076c894: 8859-11
0x0076c8a4: ISO-8859-12
0x0076c8bc: ISO8859-12
0x0076c8d2: iso885912
0x0076c8e6: 8859-12
0x0076c8f6: ISO-8859-13
0x0076c90e: ISO8859-13
0x0076c924: iso885913
0x0076c938: 8859-13
0x0076c948: ISO-8859-14
0x0076c960: ISO8859-14
0x0076c976: iso885914
0x0076c98a: 8859-14
0x0076c99a: ISO-8859-15
0x0076c9b2: ISO8859-15
0x0076c9c8: iso885915
0x0076c9dc: 8859-15
0x0076c9ec: KOI8-R
0x0076c9fa: KOI8-RU
0x0076ca0a: KOI8-U
0x0076ca18: WINDOWS-866
0x0076ca30: CP866
0x0076ca3c: WINDOWS-874
0x0076ca54: CP874
0x0076ca60: MS874
0x0076ca6c: IBM-874
0x0076ca7c: WINDOWS-932
0x0076ca94: CP932
0x0076caa0: MS932
0x0076caac: IBM-932
0x0076cabc: SJIS
0x0076cac6: SHIFT-JIS
0x0076cada: SHIFT_JIS
0x0076caee: WINDOWS-936
0x0076cb06: CP936
0x0076cb12: MS936
0x0076cb1e: IBM-936
0x0076cb2e: GB2312
0x0076cb4c: WINDOWS-949
0x0076cb64: CP949
0x0076cb70: MS949
0x0076cb7c: IBM-949
0x0076cb8c: EUC-KR
0x0076cb9a: eucKR
0x0076cba6: euc_kr
0x0076cbb4: WINDOWS-950
0x0076cbcc: CP950
0x0076cbd8: MS950
0x0076cbe4: IBM-950
0x0076cbf4: BIG5
0x0076cbfe: BIG-5
0x0076cc0a: BIG-FIVE
0x0076cc1c: WINDOWS-1250
0x0076cc36: CP1250
0x0076cc44: MS1250
0x0076cc52: IBM-1250
0x0076cc64: WINDOWS-1251
0x0076cc7e: CP1251
0x0076cc8c: MS1251
0x0076cc9a: IBM-1251
0x0076ccac: WINDOWS-1252
0x0076ccc6: CP1252
0x0076ccd4: MS1252
0x0076cce2: IBM-1252
0x0076ccf4: WINDOWS-1253
0x0076cd0e: CP1253
0x0076cd1c: MS1253
0x0076cd2a: IBM-1253
0x0076cd3c: WINDOWS-1254
0x0076cd56: CP1254
0x0076cd64: MS1254
0x0076cd72: IBM-1254
0x0076cd84: WINDOWS-1255
0x0076cd9e: CP1255
0x0076cdac: MS1255
0x0076cdba: IBM-1255
0x0076cdcc: WINDOWS-1256
0x0076cde6: CP1256
0x0076cdf4: MS1256
0x0076ce02: IBM-1256
0x0076ce14: WINDOWS-1257
0x0076ce2e: CP1257
0x0076ce3c: MS1257
0x0076ce4a: IBM-1257
0x0076ce5c: WINDOWS-1258
0x0076ce76: CP1258
0x0076ce84: MS1258
0x0076ce92: IBM-1258
0x0076cea4: WINDOWS-1361
0x0076cebe: CP1361
0x0076cecc: MS1361
0x0076ceda: IBM-1361
0x0076ceec: JOHAB
0x0076cef8: WINDOWS-437
0x0076cf10: CP437
0x0076cf1c: MS437
0x0076cf28: IBM-437
0x0076cf38: UTF-7
0x0076cf44: UTF7
0x0076cf4e: UTF-8
0x0076cf5a: UTF8
0x0076cf64: UTF-16BE
0x0076cf76: UTF16BE
0x0076cf86: UCS-2BE
0x0076cf96: UCS2BE
0x0076cfa4: UTF-16LE
0x0076cfb6: UTF16LE
0x0076cfc6: UCS-2LE
0x0076cfd6: UTF-16
0x0076cfe4: UTF16
0x0076cff0: UCS-2
0x0076cffc: UCS2
0x0076d006: UTF-32BE
0x0076d018: UTF32BE
0x0076d028: UCS-4BE
0x0076d038: UCS4BE
0x0076d046: UTF-32LE
0x0076d058: UTF32LE
0x0076d068: UCS-4LE
0x0076d078: UCS4LE
0x0076d086: UTF-32
0x0076d094: UTF32
0x0076d0a0: UCS-4
0x0076d0ac: UCS4
0x0076d0b6: EUC-JP
0x0076d0c4: eucJP
0x0076d0d0: euc_jp
0x0076d0de: IBM-eucJP
0x0076d0f2: US-ASCII
0x0076d104: ASCII
0x0076d114: POSIX
0x0076d120: ANSI_X3.4-1968
0x0076d146: roman8
0x0076d154: ISO-2022-JP
0x0076d16c: MacRoman
0x0076d17e: MacJapanese
0x0076d196: MacChineseTrad
0x0076d1b4: MacKorean
0x0076d1c8: MacArabic
0x0076d1dc: MacHebrew
0x0076d1f0: MacGreek
0x0076d202: MacCyrillic
0x0076d21a: MacDevanagari
0x0076d236: MacGurmukhi
0x0076d24e: MacGujarati
0x0076d266: MacOriya
0x0076d278: MacBengali
0x0076d28e: MacTamil
0x0076d2a0: MacTelugu
0x0076d2b4: MacKannada
0x0076d2ca: MacMalayalam
0x0076d2e4: MacSinhalese
0x0076d2fe: MacBurmese
0x0076d314: MacKhmer
0x0076d326: MacThai
0x0076d336: MacLaotian
0x0076d34c: MacGeorgian
0x0076d364: MacArmenian
0x0076d37c: MacChineseSimp
0x0076d39a: MacTibetan
0x0076d3b0: MacMongolian
0x0076d3ca: MacEthiopic
0x0076d3e2: MacCentralEurRoman
0x0076d408: MacVietnamese
0x0076d424: MacExtArabic
0x0076d43e: MacSymbol
0x0076d452: MacDingbats
0x0076d46a: MacTurkish
0x0076d480: MacCroatian
0x0076d498: MacIcelandic
0x0076d4b2: MacRomanian
0x0076d4ca: MacCeltic
0x0076d4de: MacGaelic
0x0076d4f2: MacKeyboardGlyphs
0x0076f540: SymFromAddr
0x0076f558: SymSetContext
0x0076f574: SymEnumSymbols
0x0076f5b8: Failed to get stack backtrace: %s
0x0076f5fc: SymInitialize
0x0076f618: StackWalk
0x0076f62c: SymCleanup
0x0076f6e4: twxArrayString: index out of bounds
0x0076f8d0:  !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\]^_`abcdefghijklmnopqrstuvwxyz{|}~
0x0076fda8: You cannot convert to unicode if output is const char*!
0x0076fe26: tYou cannot convert from unicode if input is const char*!
0x0076feaa: You must call wxEncodingConverter::Init() before actually converting!
0x0076ff44: tYou cannot convert to 8-bit if output is const wchar_t*!
0x0076ffc8: You cannot convert from 8-bit if input is const wchar_t*!
0x00770062: "Invalid platform specified
0x00771cfc: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00771d74: GetLocaleInfo(LOCALE_SENGLANGUAGE)
0x00771dd4: no locale to set in wxLocale::Init()
0x00771e32: dyou can't call wxLocale::Init more than once
0x00771ede: "Initializing unknown locale doesn't make sense, did you mean to use wxLANGUAGE_DEFAULT perhaps?
0x00771fa0: Unknown language %i.
0x00771fd2: windows-%u
0x00772032: No info for a valid language?
0x0077222c: wxLocaleModule
0x007724a8: Unknown field %%%c in command '%s'.
0x00772510: invalid parameter in GetMimeType
0x00772566: "invalid parameter in GetDescription
0x007725ca: invalid parameter in GetOpenCommand
0x0077262e: invalid parameter in GetPrintCommand
0x00772698: need the icon file
0x007726e4: first MIME type can't contain wildcards
0x0077275e: "extension can't be empty
0x00772792: wxMimeTypeCmnModule
0x0077285c: twxArrayString: index out of bounds
0x00772900: print
0x00773220: (null)
0x00773404: %*.*s
0x00773410: %-*.*s
0x0077341e: %.*s
0x00773428: %*.*S
0x00773434: %-*.*S
0x00773442: %.*S
0x0077344c: (null)
0x00784dfe: "NULL format in wxDateTime::Format
0x00784e44: %04d
0x00784e4e: %03d
0x00784e5e: %02d
0x00784e82: 0logic error in wxDateTime::Format
0x00784ece: %04d-%02d-%02d
0x00784efe: unknown format specifier
0x00784f30: missing format at the end of string
0x007850b2: d%a %b %d %H:%M:%S %Y
0x007850de: %x %X
0x007850ea: %X %x
0x007850f6: %Y-%m-%d
0x00785108: %I:%M:%S %p
0x00785120: %H:%M
0x0078512c: %H:%M:%S
0x0078513e: %d/%m/%Y
0x00785150: %m/%d/%Y
0x00785172: unexpected format end
0x0078536c: X-NULL format in wxTimeSpan::Format
0x007853b4: invalid format character
0x0078542c: invalid wxDateTime
0x0078546a: )invalid broken down date/time
0x007854fc: )wxLongLong to long conversion loss of precision
0x00785964: bad wxFile::Access mode parameter.
0x00785a26: Output string must be non-NULL
0x00785a88: huge file not supported
0x00785b5a: can't seek on closed file
0x00785bbc: "invalid absolute file offset
0x00785bf8: unknown seek origin
0x00785fc6: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0078603e: FindNext
0x00786064: "must wxDir::Open() first
0x007860ac: bad pointer in wxDir::GetNext()
0x0078612a: 'incorrect directory name format in wxGetDirectoryTimes
0x007861b2: aFindClose
0x0078623e: )wxString: index out of bounds
0x007863bc: "Invalid argument
0x0078640a: 2Invalid argument(s)
0x00786434: WXPREFIX
0x00786446: /usr/local
0x0078645c: share
0x0078647e: "output pointer can't be NULL
0x00787709: "#$%&')*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[
0x0078777c: \]^_`abcdefghijklmnopqrstuvwxyz{}~
0x00788340: shell32.dll
0x00788358: SHGetStockIconInfo
0x007883ba: wxArrayString: index out of bounds
0x007e9f10: ////////\\\\\\\\////
0x007e9f40: \\\\
0x007e9f50: wxRegConfig
0x007ea0e6: No application name in wxRegConfig ctor!
0x007ea138: Software\
0x007ea180: 'error in wxRegConfig::SetPath
0x007ea304: "wxRegConfig::Read(): NULL param
0x007ea346: User value for immutable key '%s' ignored.
0x007ea3aa: Can't change immutable entry '%s'.
0x007ea4a8: wxSingleChoiceDialog
0x007ea54a: wxMultiChoiceDialog
0x007ec066: wxArrayString: index out of bounds
0x007ec3ee: bad pointer in wxGetNativeFontEncoding
0x007ec466: unexpected Win32 charset
0x007ec52e: ewxFontData
0x007ec5c8: wxFontDialog
0x007ed128: wxGenericAnimationCtrl
0x007ed9a0: "incompatible animation
0x007eda48: "invalid animation
0x007edf54: lwxAnimation
0x007edf6e: wxAnimationCtrl
0x007ee000: invalid animation
0x007ee07c: Cannot create the static bitmap
0x007ee0c8: Adding duplicate animation handler for '%d' type
0x007ee136: rInserting duplicate animation handler for '%d' type
0x007ee1a0: wxAnimationModule
0x007ef430: wxGBSizerItem
0x007ef44c: wxGridBagSizer
0x007ef528: An item is already at that position
0x007ef792: Invalid cell.
0x007ef7c6: Failed to find item.
0x007ef82e: "Invalid Add form called.
0x007ef868: dPrepend should not be used with wxGridBagSizer.
0x007ef8d2: Insert should not be used with wxGridBagSizer.
0x007efa70: wxWrapSizer
0x007efcaa: invalid value for wxBoxSizer orientation
0x007efdf8: wxActivityIndicator
0x007f061a: "Must be created first
0x007f0670: "Invalid window variant
0x007f06a6: Unknown window variant
0x007f0aa0: MInvalid banner direction
0x007f0af2: Unreachable
0x007f139e: wxBufferedDC already initialised
0x007f140c: twxArrayString: index out of bounds
0x007f15f0: wxBitmapComboBox
0x007f21c4: Invalid wxBitmapComboBox state
0x007f2254: wxBitmapComboBox item index out of bound
0x007f22e8: iInvalid index for wxBitmapComboBox item
0x007f23f2: wxArrayString: index out of bounds
0x007f2488: can't mix different types of client data
0x007f251c: can't insert items in sorted control
0x007f2578: "position out of range
0x007f25ba: need something to insert
0x007f2604: )index out of bounds
0x007f2900: imagelist
0x007f2914: object
0x007f2922: object_ref
0x007f2938: bitmap
0x007f2952: Cimage
0x007f2996: label
0x007f29a2: selected
0x007f2a88: wxPropertySheetDialog
0x007f33fc: tshouldn't be called twice
0x007f34c6: Override this function!
0x007f3570: SysMonthCal32
0x007f358c: _wx_SysMonthCtl32
0x007f35d2: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x007f364a: GetClassInfoEx(SysMonthCal32)
0x007f36cc: eDateTime_SetSystemtime() failed
0x007f376e: MonthCal_SetRange() failed
0x007f37e0: sMonthCal_SetDayState
0x007f40a2: rUnregisterClass
0x007f40de: 1calling ClassRegistrar::Register() twice?
0x007f4134: RegisterClassEx()
0x007f4430: wxDateEvent
0x007f4448: wxCalendarCtrl
0x007f4466: wxCalendarEvent
0x007f581e: bad wxCheckListBox index
0x007f5896: )size of this DC hadn't been set and is unknown
0x007f5a10: wxCheckListBox
0x007f63b0: wxChoicebook
0x007f6ce2: shouldn't be called twice
0x007f6d96: "Override this function!
0x007f6e46: invalid value for wxBoxSizer orientation
0x007f6ec6: )can't insert items in sorted control
0x007f6f24: "position out of range
0x007f6f66: need something to insert
0x007f700e: ewxGenericCollapsiblePane
0x007f7042: wxCollapsiblePaneEvent
0x007f7080: wxCollapsiblePanePane
0x007f7932: shouldn't be called twice
0x007f82d4: wxColourPickerCtrl
0x007f82fa: wxColourPickerEvent
0x007f8bba: shouldn't be called twice
0x007f8ed2: wxComboBox
0x007f8ef8: wxComboCtrlBase
0x007f9a06: no popup interface set for wxComboCtrl
0x007f9a7e: popup window already shown
0x007fb8a0: wxComboCtrl
0x007fc378: COMBOBOX
0x007fc402: cwxBufferedDC already initialised
0x007fc446: Software\Policies\Microsoft\Control Panel
0x007fc49a: Software\Policies\Microsoft\Windows\Control Panel
0x007fc4fe: Control Panel\Desktop
0x007fc52a: UserPreferencesMask
0x007fcea0: ewxCommandLinkButton
0x007fd158: wxDataViewRenderer
0x007fd2b0: wxDataViewCustomRenderer
0x007fd424: wxDataViewTextRenderer
0x007fd588: wxDataViewBitmapRenderer
0x007fd6f0: wxBitmapBundle
0x007fd70e: wxBitmap
0x007fd720: wxIcon
0x007fd72e: wxDataViewToggleRenderer
0x007fd8a0: dwxDataViewProgressRenderer
0x007fda08: %i %%
0x007fda14: wxDataViewIconTextRenderer
0x007fdc8a: Logic error in wxDVC sorting code
0x007fdd34: ?wxDataViewMainWindow
0x007fe4d0: wxDataView
0x007fe5fa: wxDataViewCtrl
0x007feff0: wxdataviewctrlmainwindow
0x007ff2c4: Invalid selection flag
0x007ff2f2: No selection anchor
0x007ffdc8: wxBufferedDC already initialised
0x0080014a: wxLongLong to long conversion loss of precision
0x00802364: Removing non-registered notifier
0x008023a6: string
0x008023b4: long
0x008023be: double
0x008023cc: datetime
0x008023de: bool
0x008023e8: wxDataViewIconText
0x00802508: "invalid index
0x00802668: wxDataViewRendererBase
0x00802948: wxDataViewCtrlBase
0x008032d2: wxDataViewEvent
0x0080342e: wxSpinCtrl
0x008037e8: datectrl
0x00803842: wxDataViewCheckIconText
0x00803872: wxDataViewCheckIconTextRenderer
0x00803b6a: wxDataViewListCtrl
0x00804678: Comparing items with different parent.
0x008046c6: Unreachable
0x008046de: wxDataViewTreeCtrl
0x0080546a: )invalid wxDateTime
0x00805660: wxArrayString: index out of bounds
0x0080594e: ewxBitmapBundle
0x00806298: EXPLORER
0x008062d8: wxDatePickerCtrl
0x00806360: bug in wxDateTimePickerCtrl: m_date not in sync
0x008063ca: eDateTime_SetRange() failed
0x00806c1c: invalid wxDateTime
0x00806dc6: )The number of paths and their human readable names should be equal in number.
0x00806e8c: )Wrong number of icons for available drives.
0x00807826: .*.*
0x00807842: lwxDirFilterListCtrl
0x00807880: wxFileIconsTableModule
0x008078da: creating icons twice
0x0080797e: Eapplication/x-executable
0x008079c6: Eexe
0x008085f6: )wxString: index out of bounds
0x00808660: twxArrayString: index out of bounds
0x008089b4: wxGenericDirCtrl
0x00808a2c: ewxFileDirPickerEvent
0x00808aaa: can't specify both wxFLP_SAVE and wxFLP_OPEN at once
0x00808b3a: wxFLP_FILE_MUST_EXIST can't be used with wxFLP_SAVE
0x00808bc8: wxFLP_OVERWRITE_PROMPT can't be used with wxFLP_OPEN
0x00808c50: twxFilePickerCtrl
0x00808cb0: wxDirPickerCtrl
0x0080a544: tshouldn't be called twice
0x0080a8b0: wxEditableListBox
0x0080ba00: shouldn't be called twice
0x0080bc46: %c%c%c%c
0x0080bc72: %I:%M:%S %p
0x0080bcd8: "unexpected field in wxFileData::GetEntry()
0x0080bd30: MEDIUM GREY
0x0080bd48: wxFileListCtrl
0x0080c6e8: dAttributes 2
0x0080c720: invalid filedata
0x0080c7ba: .wxGenericFileCtrl
0x0080c830: can't specify both wxFC_SAVE and wxFC_OPEN at once
0x0080c8d0: )wxFC_MULTIPLE can't be used with wxFC_SAVE
0x0080c93c: filelist
0x0080c9da: "can't specify directory component to SetFilename
0x0080ca6e: "wxFileDialog: bad wildcard string
0x0080d3f4: wxArrayString: index out of bounds
0x0080d460: wxString: index out of bounds
0x0080d750: invalid wxDateTime
0x0080d7b0: shouldn't be called twice
0x0080d8d0: dcan't mix different types of client data
0x0080dba2: wxFontPickerCtrl
0x0080dbc4: wxFontPickerEvent
0x0080e49a: shouldn't be called twice
0x0080e7b8: msctls_progress32
0x0080f7f8: wxGauge
0x0080fc80: wxGridCellEditorEvtHandler
0x008100c8: Missing default cell attribute
0x00810150: Missing default cell renderer
0x0081019e: Missing default cell editor
0x00810250: wxGridTableBase
0x0081041c: Called grid table class function InsertRows
0x00810474: but your derived table class does not override this function
0x008104fa: Called grid table class function AppendRows
0x00810552: but your derived table class does not override this function
0x008105d8: Called grid table class function DeleteRows
0x00810630: but your derived table class does not override this function
0x008106b6: Called grid table class function InsertCols
0x0081070e: but your derived table class does not override this function
0x00810794: Called grid table class function AppendCols
0x008107ec: but your derived table class does not override this function
0x00810872: Called grid table class function DeleteCols
0x008108ca: but your derived table class does not override this function
0x00810944: string
0x00810952: wxGridStringTable
0x00810b6c: "invalid row or column index in wxGridStringTable
0x00810bda: Called wxGridStringTable::DeleteRows(pos=%lu, N=%lu)
0x00810c44: Pos value is invalid for present table with %lu rows
0x00810cae: Called wxGridStringTable::DeleteCols(pos=%lu, N=%lu)
0x00810d18: Pos value is invalid for present table with %lu cols
0x008116ee: "wxGrid::CreateGrid or wxGrid::SetTable called more than once
0x00811788: Called wxGrid::SetSelectionMode() before calling CreateGrid()
0x00811816: Called wxGrid::GetSelectionMode() before calling CreateGrid()
0x008118a4: "Table pointer must be valid
0x008118de: wxGrid already has a table
0x00811998: SELECT_CELL
0x008119b0: RESIZE_ROW
0x008119c6: RESIZE_COL
0x008119dc: SELECT_ROW
0x008119f2: SELECT_COL
0x00811a08: MOVE_ROW
0x00811a1a: MOVE_COL
0x00811a3e: ewxGrid cursor mode (mouse capture for %s): %s -> %s
0x00811aa8: colLabelWin
0x00811ac0: rowLabelWin
0x00811ad8: gridWin
0x00811eaa: can't enable editing for this cell!
0x0081207e: "invalid row index
0x008120ce: invalid column index
0x00812114: "Cell attributes not allowed
0x00812156: "must have a table
0x0081217c: bool
0x00812186: long
0x00812190: double
0x0081219e: date
0x008121d8: )wxGrid::SetCellSize setting cell size that is already part of another cell
0x00812292: )wxGrid::SetCellSize setting cell size to < 1
0x00812304: eUnknown data type name [%s]
0x0081242c: "invalid cell coords
0x00812456: wxGridEvent
0x008124b8: wxGridSizeEvent
0x00812520: wxGridRangeSelectEvent
0x00812598: wxGridEditorCreatedEvent
0x00812618: choice
0x00815578: twxArrayString: index out of bounds
0x008156b0: wxBufferedDC already initialised
0x008158fc: New size must be positive or -1.
0x00817308: wxGrid
0x00817334: wxHtmlLinkEvent
0x00817354: wxHtmlCellEvent
0x008173d4: can't be called with NULL cell
0x00817418: <html><body></body></html>
0x00817e20: .wxHtmlWindow/Borders
0x00817e4c: wxHtmlWindow/FontFaceFixed
0x00817e82: wxHtmlWindow/FontFaceNormal
0x00817eba: wxHtmlWindow/FontsSize%i
0x00817efc: nwxhtmlselection
0x00817f86: twxHtmlProcessor
0x00817fa8: wxHtmlWindow
0x00817fd8: wxHtmlWinModule
0x00818266: unbalanced wxRecursionGuards!?
0x00818372: tshouldn't be called twice
0x00818888: wxHtmlListBox
0x00819286: wxHtmlParser::Parse() returned NULL?
0x008192e4: this cell should be cached!
0x0081932c: no cell
0x0081933c: no root cell
0x00819368: unexpected root cell's ID
0x0081939c: wxSimpleHtmlListBox
0x00819eda: invalid index in wxSimpleHtmlListBox::SetString
0x00819f42: ginvalid index in wxSimpleHtmlListBox::GetString
0x0081a000: shouldn't be called twice
0x0081a0a2: )index out of bounds
0x0081a0e4: wxArrayString: index out of bounds
0x0081a166: GetSelection() can't be used with wxLB_MULTIPLE
0x0081a550: wxControlWithItems
0x0081a5a8: "invalid index
0x0081a5e8: Must be overridden if DoInsertItemsInLoop() is used
0x0081a676: )can't have both object and void client data
0x0081a730: this window doesn't have object client data
0x0081a824: "this window doesn't have void client data
0x0081a8b8: aunknown client data type
0x0081b2a2: index out of bounds
0x0081b2e2: wxArrayString: index out of bounds
0x0081be36: ewxListbook
0x0081be60: Pages
0x0081c772: shouldn't be called twice
0x0081c826: "Override this function!
0x0081cc78: SysListView32
0x0081ccc2: 1wxListCtrl style should have exactly one mode bit set
0x0081cd40: )can't sort in ascending and descending orders at once
0x0081d6b0: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0081d728: ListView_SetBkColor()
0x0081d754: ListView_SetTextBkColor()
0x0081d7be: "Column position out of bounds
0x0081d826: Column index out of bounds
0x0081d86e: no column with with given order?
0x0081d8e4: wrong number of elements in column orders array
0x0081d9b0: invalid item index in SetItem
0x0081d9ec: ListView_SetItem() failed
0x0081da50: "invalid list control item index in GetItemState()
0x0081dac4: ListView_SetItemState
0x0081dafa: tListView_GetViewRect() failed.
0x0081db3a: not implemented in this mode
0x0081dbc2: "invalid sub item index
0x0081dbf2: invalid item in GetSubItemRect
0x0081dc30: incorrect code in GetItemRect() / GetSubItemRect()
0x0081dcd6: rListView_SetTextColor()
0x0081dd36: ListView_DeleteItem
0x0081dd70: ListView_DeleteColumn
0x0081ddaa: 0no columns should be left
0x0081de6e: )can't be used with virtual controls
0x0081deca: 1Item ID must be set.
0x0081df06: Failed to insert the column '%s' into listview!
0x0081df72: ListView_Scroll(%d, %d) failed
0x0081dfb8: sListView_SortItems() failed
0x0081e006: dScreenToClient(listctrl header)
0x0081e048: ScreenToClient(listctrl)
0x0081e084: yUnknown LVIS_STATEIMAGE state: %u
0x0081e0ca: Ignoring invalid search start position %d in list control with %d items.
0x0081e18a: invalid column index array in OnPaint()
0x0081e1f4: this is for virtual controls only
0x0081e238: ListView_SetItemCount
0x0081e272: ListView_RedrawItems
0x0081e2d0: Init() called twice?
0x0081e34e: )wxLongLong to long conversion loss of precision
0x0081e75a: wxListCtrl
0x0081e770: wxListView
0x0081e786: wxListItem
0x0081e79c: wxListEvent
0x0081fc30: wxMDIParentFrame
0x0081fc52: wxMDIChildFrame
0x0081fc72: wxMDIClientWindow
0x0081fd12: swxMDIFrame
0x00820700: invalid orientation value
0x008207c6: eIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00820840: WM_MDICREATE
0x008211c4: can't deactivate MDI child which wasn't active!
0x00821224: MDICLIENT
0x00821246: CreateWindowEx(MDI client)
0x00821a1a: SendMessage(WM_MDISETMENU)
0x00821a5a: MDI client without parent frame? weird...
0x00821bd4: RemoveMenu
0x00822988: SysTabControl32
0x008229a8: _wx_SysTabCtl32
0x008229e8: eIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00822a62: GetClassInfoEx(SysTabCtl32)
0x00822a9a: msw.notebook.themed-background
0x00822b6e: "notebook page out of range
0x00822bfc: "NULL page in wxNotebook::AdjustPageSize
0x00822c5c: TabCtrl_DeleteItem()
0x00822ca4: sTabCtrl_DeleteAllItems()
0x00822cee: "NULL page in wxNotebook::InsertPage
0x00822d6e: "invalid index in wxNotebook::InsertPage
0x00822ddc: notebook pages must have notebook as parent
0x00822e34: Can't create the notebook page '%s'.
0x00822e9c: Aero
0x00822ea6: NormalColor
0x00822efe: rUnregisterClass
0x00822f3a: 1calling ClassRegistrar::Register() twice?
0x00822f90: RegisterClassEx()
0x00823060: Override this function!
0x00824104: you must subclass wxVListBoxComboPopup for drawing and measuring methods
0x0082420a: "invalid index in wxVListBoxComboPopup::SetSelection
0x00824ebe: invalid index in wxOwnerDrawnComboBox::Delete
0x00824f22: ginvalid index in wxOwnerDrawnComboBox::GetString
0x00824f8e: ginvalid index in wxOwnerDrawnComboBox::SetString
0x00825016: invalid index in wxOwnerDrawnComboBox::Select
0x008250c4: GetSelection() can't be used with wxLB_MULTIPLE
0x00825150: twxArrayString: index out of bounds
0x008251dc: tshouldn't be called twice
0x0082525c: )index out of bounds
0x00825508: wxOwnerDrawnComboBox
0x0082557e: "major radiobox dimension can't be 0
0x008255e6: unexpected wxDirection value
0x00825638: 0logic error in wxRadioBox::GetNextItem()
0x008256ae: Invalid item index
0x00825806: wxArrayString: index out of bounds
0x0082587c: rwxScrollBar
0x008258c8: wxSearchCtrl
0x00827932: tshouldn't be called twice
0x00829230: wxSlider
0x00829290: wxSpinDoubleEvent
0x00829b28: "invalid call to wxSpinCtrl::SetValue
0x00829b8a: ninvalid call to wxSpinCtrl::SetSelection
0x00829bde: wxSpinCtrlDouble
0x0082b8d4: hwxSpinButton
0x0082b966: shouldn't be called twice
0x0082bd90: wxSpinCtrl
0x0082bda6: 0x%04lx
0x0082bdb6: 0x%08lx
0x0082be60: wxSpinButton
0x0082be7a: wxSpinEvent
0x0082bf20: wxSplitterWindow
0x0082bf42: wxSplitterEvent
0x0082c76a: "should have a top level parent!
0x0082c7da: "invalid gravity value
0x0082c83a: )windows in the splitter should have it as parent!
0x0082c8be: cannot split with NULL window(s)
0x0082c948: splitter: attempt to remove a non-existent window
0x0082c9c4: use one of Split() functions instead
0x0082ca18: use Unsplit() functions instead
0x0082ca58: splitter: attempt to replace a non-existent window
0x0082cadc: "splitter: no window to remove
0x0082cba2: shouldn't be called twice
0x0082ce28: wxBitmapToggleButton
0x0082ce52: wxToggleButton
0x0082ce70: BUTTON
0x0082df30: SysDateTimePick32
0x0082df9c: this control requires a valid date
0x0082dff4: Setting the calendar date unexpectedly failed.
0x0082e074: SysDateTimePick32 creation unexpected failed
0x0082e8c8: wxTimePickerCtrl
0x0082f168: wxToolbook
0x0082f1ca: wxToolbook::GetPageImage() not implemented
0x0082fb5a: shouldn't be called twice
0x0082fc00: Override this function!
0x0082fd40: wxTreebook
0x0082fdb4: "Invalid treebook page position
0x0082fe02: )Tree has no root node?
0x0082fe44: Failed to insert treebook page
0x0082fea4: Can't insert sub page when there are no pages
0x0082ff20: "invalid tree item
0x0082ff6a: Internal error in tree insert point calculation
0x0082fffc: "Invalid tree index
0x00830074: Internal error in wxTreebook::DoRemovePage
0x008300f8: Invalid index passed to wxTreebook::DoInternalAddPage
0x008301a2: )Invalid page index
0x00830200: "Invalid page index spacified!
0x0083029c: "Internal problem in wxTreebook!..
0x00830c0a: shouldn't be called twice
0x00830cb0: Override this function!
0x00830dc2: wxTreeCtrl
0x00830dd8: wxTreeEvent
0x00831e8c: SysTreeView32
0x00831ed6: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x008329e4: "can't retrieve virtual root item
0x00832a28: TreeView_GetItem
0x00832a52: mTreeView_SetItem
0x00832a96: invalid tree item
0x00832b18: invalid image index
0x00832b62: failed to change tree items data
0x00832c84: "this only works with single selection controls
0x00832d48: )The item you call GetNextVisible() for must be visible itself!
0x00832dd8: The item you call GetPrevVisible() for must be visible itself!
0x00832ed6: "can't have more than one root in the tree
0x00832f2c: TreeView_InsertItem
0x00832f6a: ttree can have only a single root
0x00832fc4: 0bad index in wxTreeCtrl::InsertItem
0x0083301a: mTreeView_DeleteItem
0x00833054: TreeView_DeleteAllItems
0x008330d2: Unknown flag in wxTreeCtrl::DoExpand
0x00833130: "Can't expand/collapse hidden root node!
0x0083319a: )doesn't make sense, may be you want UnselectAll()?
0x00833260: can't select hidden root item
0x0083329c: TreeView_SelectItem
0x008332d0: ecan't show hidden root item
0x00833314: TreeView_SelectSetFirstVisible
0x008333a4: TreeView_EndEditLabelNow()
0x00833416: sorting tree without data doesn't make sense
0x0083347e: TreeView_SortChildren()
0x008334ae: TreeView_SortChildrenCB()
0x008334ee: cTreeView_SelectDropTarget()
0x00833528: TreeView_SelectDropTarget(0)
0x00833580: unexpected code %d in TVN_ITEMEXPAND message
0x008335e8: starting to drag once again?
0x0083366a: unsupported wxTreeItemIcon value
0x00833a10: wxWizard
0x00833a22: wxWizardPage
0x00833a3c: wxWizardPageSimple
0x00833a62: wxWizardEvent
0x00834c92: You must create the buttons before calling wxWizard::AddBackNextPair
0x00834d5c: wxWizard::SetPageSize after RunWizard
0x00834db0: ewxWizard::FitToPage after RunWizard
0x00834e10: ethis is useless
0x00834e46: "can't run empty wizard
0x00834e7e: rwxWizard::SetBorder after RunWizard
0x00834f22: )unknown button
0x00834f4c: should have a valid current page
0x00834f94: "<Back" button should have been disabled
0x008357f8: shouldn't be called twice
0x00835b4c: wxExecuteModule
0x00835b98: kIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00835c12: CloseHandle(hThread)
0x00835cee: dPeekNamedPipe
0x00835dc4: SetNamedPipeHandleState(PIPE_NOWAIT)
0x00835e28: empty command in wxExecute
0x00835e72: wxExecute() can be called only from the main thread
0x00835eda: WX_DDE#
0x00835efc: invalid WX_DDE command in wxExecute
0x00835f7a: SetHandleInformation(pipeIn)
0x00835fb4: SetHandleInformation(pipeOut)
0x00835ff0: SetHandleInformation(pipeErr)
0x00836030: invalid value of thread priority parameter
0x008360aa: dcan't create a hidden window for wxExecute
0x00836102: ResumeThread in wxExecute
0x00836136: CreateThread in wxExecute
0x0083616a: unexpected WaitForInputIdle() return code
0x008361be: WaitForInputIdle() in wxExecute
0x008361fe: Timeout too small in WaitForInputIdle
0x0083624a: Failed to send DDE request to the process "%s".
0x008362b4: no wxAppTraits in wxExecute()?
0x00836362: eCloseHandle
0x0083638c: CloseHandle(hProcess)
0x00836444: _wxExecute_Internal_Class
0x00836486: dCreateEvent() in wxExecuteThread failed
0x008364d8: GetExitCodeProcess
0x00836524: )process should have terminated
0x00836564: Waiting for the process termination failed!
0x008365e6: "Event can't be created twice
0x00836622: CreateEvent
0x00836642: Failed to set shutdown event in wxExecuteModule
0x008366a2: Failed to stop all wxExecute monitor threads
0x008366fc: UnregisterClass(wxExecClass)
0x00836746: Event must be valid
0x0083676e: SetEvent
0x00836790: Handle must be valid
0x0083b280:         
0x0083b48a: Too many flags specified for a single conversion specifier!
0x0083b5be: "field width must be specified
0x0083b600: (null)
0x0083bac0: wxQuantize
0x0083bbe8: Windows cursor file
0x0083bc18: image/x-cur
0x0083bc30: Windows icon file
0x0083bc5c: image/x-ico
0x0083bc74: Windows bitmap file
0x0083bca4: image/x-bmp
0x0083be80: )Transparency
0x0083be9c: Highlight
0x0083beb0: Unchanged
0x0083bee2: "Unknown wxIMAGE_OPTION_GIF_TRANSPARENCY value
0x0083bf40: GifComment
0x0083dd44: (<Pcw
0x0083fe22: invalid base
0x0083feaa: gunknown EOL mode in wxTextOutputStream
0x00842132: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x008421c8: eEnumDisplaySettings(ENUM_CURRENT_SETTINGS)
0x00842252: at least the width and height must be specified
0x008422c4: unexpected ChangeDisplaySettingsEx() return value
0x00842370: wxDisplayHiddenWindow
0x008423b2: DestroyWindow(wxDisplayHiddenWindow)
0x008423fc: UnregisterClass(wxDisplayHiddenWindow)
0x0084245a: sEnumDisplayMonitors
0x00842496: GetMonitorInfo
0x008424d8: "An invalid index was passed to wxDisplay
0x00842c48: bius
0x00843020: wxCreateRenderer
0x00843448: wxMemoryDCImpl
0x0084385a: "NULL dc in wxMemoryDC ctor
0x008438e2: Bitmap is selected in another wxMemoryDC, delete the first wxMemoryDC or use SelectObject(NULL)
0x008439a6: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00843a1e: SelectObject(memDC, bitmap)
0x00843a68: Couldn't select a bitmap into wxMemoryDC
0x00843c70: wxScreenDCImpl
0x0084422a: wxPrintNativeDataBase
0x008442b0: wxPrintFactoryModule
0x008442da: wxPrinterBase
0x00844360: dialog
0x0084437e: wxPrinter
0x00844400: wxPrintDialogBase
0x00844d00: wxPrintDialog
0x00844d78: wxPageSetupDialogBase
0x00845670: wxPageSetupDialog
0x0084607a: ewxPrintout
0x0084615c: wxPreviewCanvas
0x00846980: wxPreviewControlBar
0x008472b8: ewxPreviewFrame
0x00847c28: canvas
0x00847c36: panel
0x00847c42: wxPrintPreviewBase
0x00847dca: "wxPrintPreviewBase::RenderPage: must use wxPrintPreviewBase::SetCanvas to let me know about the canvas!
0x00847ede: dwxPrintPreview
0x008481bc: tshouldn't be called twice
0x00849e70: wxPrintData
0x00849e88: wxPrintDialogData
0x00849eac: wxPageSetupDialogData
0x00849fd0: )wxThePrintPaperDatabase should not be NULL. Do not create global print dialog data objects.
0x0084a890: SymInitialize
0x0084a8ac: Function SymInitialize() not found.
0x0084a8f6: SymFromAddr
0x0084a90e: Function SymFromAddr() not found.
0x0084a954: SymEnumSymbols
0x0084a972: Function SymEnumSymbols() not found.
0x0084a9be: StackWalk64
0x0084a9d6: Function StackWalk64() not found.
0x0084aa1c: SymFunctionTableAccess64
0x0084aa4e: Function SymFunctionTableAccess64() not found.
0x0084aaae: SymGetModuleBase64
0x0084aad4: Function SymGetModuleBase64() not found.
0x0084ab28: SymGetOptions
0x0084ab44: Function SymGetOptions() not found.
0x0084ab8e: SymSetOptions
0x0084abaa: Function SymSetOptions() not found.
0x0084abf4: SymSetContext
0x0084ac10: Function SymSetContext() not found.
0x0084ac5a: SymGetTypeInfo
0x0084ac78: Function SymGetTypeInfo() not found.
0x0084acc4: SymCleanup
0x0084acda: Function SymCleanup() not found.
0x0084ad1e: MiniDumpWriteDump
0x0084ad42: Function MiniDumpWriteDump() not found.
0x0084ad94: SymGetLineFromAddr
0x0084adba: SymGetLineFromAddr64
0x0084ade4: SymGetLineFromAddrW64
0x0084ae10: EnumerateLoadedModules
0x0084ae3e: EnumerateLoadedModules64
0x0084ae70: EnumerateLoadedModulesW64
0x0084aea4: SymInitializeW
0x0084aec2: SymFromAddrW
0x0084aedc: SymEnumSymbolsW
0x0084aefc: dbghelp.dll
0x0084af16: Please update your dbghelp.dll version, at least version 5.1 is needed!
0x0084afa6: (if you already have a new version, please put it in the same directory where the program is.)
0x0084b066: Please install dbghelp.dll available free of charge from Microsoft to get more detailed crash information!
0x0084b13e: Latest dbghelp.dll is available at http://www.microsoft.com/whdc/ddk/debugging/
0x0084b1e0: dbghelp: %s() failed: %s
0x0084b21e: true
0x0084b228: false
0x0084b234: %#04x
0x0084b240: %#06x
0x0084b270: NULL
0x0084b410: wxBitmap::ConvertToImage doesn't preserve mask?
0x0084b548: wxPopupWindow
0x0084b564: wxPopupTransientWindow
0x0084b5d6: Shouldn't destroy the popup twice.
0x0084c61a: Can't get direct access to initialized BSTR
0x0084c6ac: Attempting to assign already owned BSTR
0x0084c6fc: currency
0x0084c75c: herrorcode
0x0084c7c2: safearray
0x0084c826: SAFEARRAY: %p
0x0084c862: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0084c8da: SafeArrayGetVartype()
0x0084c906: long
0x0084c910: longlong
0x0084c922: ulonglong
0x0084c936: char
0x0084c940: double
0x0084c94e: bool
0x0084c958: string
0x0084c966: datetime
0x0084c978: void*
0x0084c984: list
0x0084c98e: arrstring
0x0084c9b6: tunhandled VT_ARRAY type %x in wxConvertOleToVariant
0x0084ca20: wxAutomationObject::ConvertOleToVariant: [as yet] unhandled reference %X
0x0084cab2: wxAutomationObject::ConvertOleToVariant: Unknown variant value type %X -> %X
0x0084ce9e: Can't be created twice
0x0084cee8: Can only attach a valid array to an uninitialized one
0x0084cf54: SafeArrayGetVarType()
0x0084cf8e: "Attaching array of invalid type
0x0084cfec: Uninitialized array
0x0084d5e0: HHCTRL.OCX
0x0084d6a2: HtmlHelpW
0x0084d6da: wxCHMHelpController
0x0084d702: .htm
0x0084d70e: .chm
0x0084f24c: Unknown interpolation mode
0x0084f2a4: No state to pop
0x0084f2e0: "wxGDIPlusContext::DrawText - no valid font set
0x0084f34c: twxGDIPlusContext::GetTextExtent - no valid font set
0x0084f3ca: swxGDIPlusContext::GetPartialTextExtents - no valid font set
0x0084f45a: GetPartialTextExtents not yet implemented for multichar situations
0x0084f708: wxGDIPlusRenderer
0x0084f7f2: "Invalid bitmap
0x0084f87a: "Invalid bitmap region
0x0084f8a8: wxGDIPlusRendererModule
0x0084fa7c: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0084faf4: GetWindowRect
0x0084fc1a: mGetIconInfo
0x0084fc6e: twxArrayString: index out of bounds
0x0085083c: wxEnhMetaFile
0x00850892: must be implemented if used
0x0085091c: nIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x00850996: CopyEnhMetaFile
0x008509bc: DeleteEnhMetaFile
0x008509ec: "can't play invalid enhanced metafile
0x00850a3e: invalid wxDC in wxEnhMetaFile::Play
0x00850a86: PlayEnhMetaFile
0x00850aac: eGetEnhMetaFileHeader
0x00850aee: can't copy invalid metafile to clipboard
0x00850f10: CreateEnhMetaFile
0x00850f38: einvalid wxEnhMetaFileDC
0x00850f6a: CloseEnhMetaFile
0x00850f8c: wxEnhMetaFileDC
0x00851018: no wxEnhMetaFileDC implementation
0x0085107e: Eunsupported format
0x008510c4: "copying invalid enh metafile
0x00851108: GetWinMetaFileBits() failed
0x00851140: GetWinMetaFileBits
0x00851166: SetMetaFileBitsEx
0x0085119a: pasting invalid enh metafile
0x008511d4: GetMetaFileBitsEx() failed
0x0085120a: GetMetaFileBitsEx
0x0085122e: SetWinMetaFileBits
0x0085174e: rIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x008517c8: GetWindowsDirectory
0x00851888: shell32
0x008518ac: sstdpaths
0x008518c0: Failed to load %s.dll
0x008519d0: eGetModuleFileName
0x0086a6d4: Locale '%s' not supported by OS.
0x0086a716: kernel32.dll
0x0086a812: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0086a88a: GetLocaleInfo
0x0086a9ba: SetThreadPreferredUILanguages
0x0086a9f6: GetLocaleInfoEx
0x0086aa30: Unreachable
0x0086aa5e: sGetUserPreferredUILanguages
0x0086aa98: GetUserDefaultLocaleName
0x0086ae70: wxFileConfig
0x0086b1d2: NULL buffer
0x0086b1f6: gfileconf
0x0086b20a:   Writing String '%s' = '%s' to Group '%s'
0x0086b26e: )can't set value of a group!
0x0086b2a8:   Creating group %s
0x0086b2f8: .  Adding Entry %s
0x0086b31e:   Setting value %s
0x0086b422: RenameEntry(): paths are not supported
0x0086b4be: d    ** Adding Line '%s'
0x0086b4f0:         head: %s
0x0086b512:         tail: %s
0x0086b544:     ** Inserting Line '%s' after '%s'
0x0086b5a0:     ** Removing Line '%s'
0x0086b5fe: changing line for a non-root group?
0x0086b654:   GetGroupLine() for Group '%s'
0x0086b694:     Getting Line item pointer
0x0086b6d0:     checking parent '%s'
0x0086b722: last group must have !NULL associated line
0x0086b78a:   GetLastEntryLine() for Group '%s'
0x0086b7d2: last entry must have !NULL associated line
0x0086b83e: tunexpected for non root group
0x0086b89e: "a non root group must have a corresponding line!
0x0086b902: [%s]
0x0086b920: the root group can't be renamed
0x0086b9be: "deleting non existing group?
0x0086b9fa: Deleting group '%s' from '%s'
0x0086ba36:   (m_pLine) = prev: %p, this %p, next %p
0x0086ba88:   text: '%s'
0x0086baa2: Removing %lu entries
0x0086bacc:     '%s'
0x0086bade: Removing %lu subgroups
0x0086bb0c:   Removing line for group '%s' : '%s'
0x0086bb58:   Removing from group '%s' : '%s'
0x0086bb9c:   Removing last group
0x0086bbc8:   No line entry for Group '%s'?
0x0086bd9c: twxArrayString: index out of bounds
0x0086bf76: @_/-!.*%()
0x0086c0e2: "needs an extension
0x0086c10a: _auto_file
0x0086c120: \shell\
0x0086c130: \command
0x0086c160: "GetAllCommands() needs an extension
0x0086c1ae: Can't get the filetype for extension '%s'.
0x0086c204: \shell
0x0086c212: open
0x0086c260: System
0x0086c26e: WX_DDE#
0x0086c286: Content Type
0x0086c2a4: Extension
0x0086c2e2: "Associate() needs extension
0x0086c31c: Associate() needs non empty extension
0x0086c368: _file
0x0086c374: print
0x0086c3ae: SetCommand() needs an extension and a verb
0x0086c404:  "%1"
0x0086c420: SetDefaultIcon() needs extension
0x0086c47c: File key not found
0x0086c4a2: \DefaultIcon
0x0086c4bc: %s,%d
0x0086c4e8: No file description supplied
0x0086c52e: dRemoveCommand() needs an extension and a verb
0x0086c59c: RemoveMimeType() needs extension
0x0086c5ee: nRemoveDefaultIcon() needs extension
0x0086c648: nRemoveDescription() needs extension
0x0086c6a2: Software\Classes\
0x0086c6da: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0086c85e: MIME\Database\Content Type\
0x0086c8c2: twxArrayString: index out of bounds
0x00873150: wxProcess
0x00873164: wxProcessEvent
0x008731b6: )wxEXEC_SYNC should not be used.
0x008732d6: "unexpected wxProcess::Kill() return code
0x00873354: Invalid process priority value.
0x00877fd8: mpr.dll
0x00877fe8: WNetOpenEnumW
0x00878004: WNetEnumResourceW
0x00878028: WNetCloseEnum
0x008780a2: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0087811a: SHGetFileInfo
0x00878282: wxArrayString: index out of bounds
0x00878354: wxFSIconType::GetIcon(): invalid icon index
0x008783be: wxFS_VOL_ICO_MAX is not valid icon type
0x00878508: wxBufferedDC
0x00878522: wxBufferedPaintDC
0x00878546: wxSharedDCBufferManager
0x008785d0: "no underlying wxDC?
0x00878618: invalid backing store
0x00878774: shared buffer already released
0x008787e8: rcomctl32.dll
0x00878804: InitCommonControlsEx
0x00879554: wxPickerBase
0x008795a8: wxPickerBase's textctrl creation failed
0x0087a1f0: wxGenericColourButton
0x0087a250: "wxGenericColourButton creation failed
0x0087afa0: should be in order
0x0087afe6: mlogic error
0x0087b2d8: wxHeaderCtrlEvent
0x0087daea: wxGenericRendererModule
0x0087dba6: nnot implemented
0x0087dbd0: cthis is probably wrong
0x0087e272: No data in wxDropSource!
0x0087e2a8: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0087e320: DoDragDrop
0x0087e336: Drag & drop operation failed.
0x0087e372: Unexpected success return code %08lx from DoDragDrop.
0x0087e5de: lSysHeader32
0x0087eddc: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0087ee54: Header_Layout
0x0087eef4: Header_DeleteItem
0x0087ef26: Header_DeleteItem()
0x0087efc2: tHeader_InsertItem()
0x0087effa: rHeader_GetOrderArray
0x0087f256: unexpected event type
0x0087fb0a: GetClientRect
0x0087fff8: wxGenericFileButton
0x00880020: wxGenericDirButton
0x00880082: wxGenericFileButton creation failed
0x00881e44: wxFileCtrlEvent
0x00881f86: )Please use GetFiles() to get all files instead of this function
0x00882090: wxGenericFontButton
0x008820fc: wxGenericFontButton creation failed
0x00882144: %s, %d
0x00882cee: einvalid progress value
0x0088304c: datetime
0x00883240: double
0x00883252: %%.%d
0x0088325e: %%%d.
0x0088326a: %%%d.%d
0x0088327a: Invalid wxGridCellFloatRenderer width parameter string '%s ignored
0x00883300: Invalid wxGridCellFloatRenderer precision parameter string '%s ignored
0x008835ce: wxArrayString: index out of bounds
0x00883afc: lThe wxGridCellEditor must be created first!
0x00883c7a: Invalid wxGridCellTextEditor parameter string '%s' ignored
0x00883cf0: wxSpinCtrl
0x00883d3e: this cell doesn't have numeric value
0x00883d92: dInvalid wxGridCellNumberEditor parameter string '%s' ignored
0x00883e0e: double
0x00883e1c: this cell doesn't have float value
0x00883e62: Invalid wxGridCellFloatRenderer width parameter string '%s ignored
0x00883ee8: Invalid wxGridCellFloatRenderer precision parameter string '%s ignored
0x00883fb8: d%%%d.
0x00883fc6: %%.%d
0x00883fd2: %%%d.%d
0x00884024: dinvalid value for a cell with bool editor!
0x00884130: The wxGridCellChoiceEditor must be created first!
0x00884248: The wxGridCellEnumEditor must be Created first!
0x00884358: datectrl
0x008846a8: h%Y-%m-%d
0x00884dd4: wxHtmlCell
0x00884efa: "window interface must be provided
0x00884f58: "Cells are in different trees
0x00884fb4: ]wxHtmlWordCell
0x00885110: wxHtmlContainerCell
0x0088524a: dALIGN
0x00885258: CENTER
0x00885266: LEFT
0x00885270: JUSTIFY
0x00885280: RIGHT
0x0088528c: WIDTH
0x0088529a: wxHtmlColourCell
0x008852d2: wxHtmlFontCell
0x00885304: wxHtmlWidgetCell
0x0088540e: "widget cells can only be placed in wxHtmlWindow
0x0088576a: )wxString: index out of bounds
0x00885b10: wxHtmlWinParser
0x00885bf6: no DC assigned to wxHtmlWinParser!!
0x00885c3e: wxHtmlWinTagHandler
0x00885c66: color
0x00885c72: background-color
0x00885c94: font-size
0x00885cae: font-weight
0x00885cc6: bold
0x00885cd0: normal
0x00885cde: font-style
0x00885cf4: oblique
0x00885d04: italic
0x00885d12: text-decoration
0x00885d32: underline
0x00885d46: font-family
0x00885d5e: wxHtmlTagsModule
0x00885ea6: twxArrayString: index out of bounds
0x0088600c: htmldebug
0x00886020: wxHtmlParser
0x008861c4: kwxHtmlTagHandler
0x008861e8: wxHtmlEntitiesParser
0x00886284: AElig
0x00886290: Aacute
0x0088629e: Acirc
0x008862aa: Agrave
0x008862b8: Alpha
0x008862c4: Aring
0x008862d0: Atilde
0x008862de: Auml
0x008862e8: Beta
0x008862f2: Ccedil
0x00886308: Dagger
0x00886316: Delta
0x0088632a: Eacute
0x00886338: Ecirc
0x00886344: Egrave
0x00886352: Epsilon
0x0088636a: Euml
0x00886374: Gamma
0x00886380: Iacute
0x0088638e: Icirc
0x0088639a: Igrave
0x008863a8: Iota
0x008863b2: Iuml
0x008863bc: Kappa
0x008863c8: Lambda
0x008863dc: Ntilde
0x008863f0: OElig
0x008863fc: Oacute
0x0088640a: Ocirc
0x00886416: Ograve
0x00886424: Omega
0x00886430: Omicron
0x00886440: Oslash
0x0088644e: Otilde
0x0088645c: Ouml
0x00886474: Prime
0x00886490: Scaron
0x0088649e: Sigma
0x008864aa: THORN
0x008864be: Theta
0x008864ca: Uacute
0x008864d8: Ucirc
0x008864e4: Ugrave
0x008864f2: Upsilon
0x00886502: Uuml
0x00886512: Yacute
0x00886520: Yuml
0x0088652a: Zeta
0x00886534: aacute
0x00886542: acirc
0x0088654e: acute
0x0088655a: aelig
0x00886566: agrave
0x00886574: alefsym
0x00886584: alpha
0x008865a8: apos
0x008865b2: aring
0x008865be: asymp
0x008865ca: atilde
0x008865d8: auml
0x008865e2: bdquo
0x008865ee: beta
0x008865f8: brvbar
0x00886606: bull
0x00886618: ccedil
0x00886626: cedil
0x00886632: cent
0x00886644: circ
0x0088664e: clubs
0x0088665a: cong
0x00886664: copy
0x0088666e: crarr
0x00886682: curren
0x00886690: dArr
0x0088669a: dagger
0x008866a8: darr
0x008866ba: delta
0x008866c6: diams
0x008866d2: divide
0x008866e0: eacute
0x008866ee: ecirc
0x008866fa: egrave
0x00886708: empty
0x00886714: emsp
0x0088671e: ensp
0x00886728: epsilon
0x00886738: equiv
0x00886754: euml
0x0088675e: euro
0x00886768: exist
0x00886774: fnof
0x0088677e: forall
0x0088678c: frac12
0x0088679a: frac14
0x008867a8: frac34
0x008867b6: frasl
0x008867c2: gamma
0x008867da: hArr
0x008867e4: harr
0x008867ee: hearts
0x008867fc: hellip
0x0088680a: iacute
0x00886818: icirc
0x00886824: iexcl
0x00886830: igrave
0x0088683e: image
0x0088684a: infin
0x0088685e: iota
0x00886868: iquest
0x00886876: isin
0x00886880: iuml
0x0088688a: kappa
0x00886896: lArr
0x008868a0: lambda
0x008868ae: lang
0x008868b8: laquo
0x008868c4: larr
0x008868ce: lceil
0x008868da: ldquo
0x008868ec: lfloor
0x008868fa: lowast
0x00886918: lsaquo
0x00886926: lsquo
0x00886938: macr
0x00886942: mdash
0x0088694e: micro
0x0088695a: middot
0x00886968: minus
0x0088697a: nabla
0x00886986: nbsp
0x00886990: ndash
0x008869b0: notin
0x008869bc: nsub
0x008869c6: ntilde
0x008869da: oacute
0x008869e8: ocirc
0x008869f4: oelig
0x00886a00: ograve
0x00886a0e: oline
0x00886a1a: omega
0x00886a26: omicron
0x00886a36: oplus
0x00886a48: ordf
0x00886a52: ordm
0x00886a5c: oslash
0x00886a6a: otilde
0x00886a78: otimes
0x00886a86: ouml
0x00886a90: para
0x00886a9a: part
0x00886aa4: permil
0x00886ab2: perp
0x00886ad2: plusmn
0x00886ae0: pound
0x00886aec: prime
0x00886af8: prod
0x00886b02: prop
0x00886b14: quot
0x00886b1e: rArr
0x00886b28: radic
0x00886b34: rang
0x00886b3e: raquo
0x00886b4a: rarr
0x00886b54: rceil
0x00886b60: rdquo
0x00886b6c: real
0x00886b7e: rfloor
0x00886b9c: rsaquo
0x00886baa: rsquo
0x00886bb6: sbquo
0x00886bc2: scaron
0x00886bd0: sdot
0x00886bda: sect
0x00886bec: sigma
0x00886bf8: sigmaf
0x00886c0e: spades
0x00886c24: sube
0x00886c3e: sup1
0x00886c48: sup2
0x00886c52: sup3
0x00886c5c: supe
0x00886c66: szlig
0x00886c7a: there4
0x00886c88: theta
0x00886c94: thetasym
0x00886ca6: thinsp
0x00886cb4: thorn
0x00886cc0: tilde
0x00886ccc: times
0x00886cd8: trade
0x00886ce4: uArr
0x00886cee: uacute
0x00886cfc: uarr
0x00886d06: ucirc
0x00886d12: ugrave
0x00886d28: upsih
0x00886d34: upsilon
0x00886d44: uuml
0x00886d4e: weierp
0x00886d62: yacute
0x00886d78: yuml
0x00886d82: zeta
0x00886d94: zwnj
0x00886d9e: BODY
0x00886da8: HTTP-EQUIV
0x00886dbe: Content-Type
0x00886dd8: CONTENT
0x00886de8: text/html; charset=
0x00886e2e: should be called on the tag start
0x00886f8a: META,BODY
0x00887180: wxHTML_ModuleLayout
0x008872e0: CENTER
0x008872f6: STYLE
0x00887302: PAGE-BREAK-BEFORE:ALWAYS
0x00887334: PAGE-BREAK-INSIDE:AVOID
0x00887364: ALIGN
0x00887370: TITLE
0x0088737c: BACKGROUND
0x00887392: BGCOLOR
0x008873a2: BLOCKQUOTE
0x008873b8: SUB,SUP
0x008873d0: SCRIPT
0x008878f4: wxHTML_ModuleFonts
0x0088797e: COLOR
0x0088798a: BGCOLOR
0x008879c6: twxArrayString: index out of bounds
0x00887a0e: U,STRIKE,DEL
0x00887a28: I,EM,CITE,ADDRESS
0x00887a4c: B,STRONG
0x00887a5e: TT,CODE,KBD,SAMP
0x00887a80: H1,H2,H3,H4,H5,H6
0x00887ac8: BIG,SMALL
0x00888168: *.gif
0x00888174: *.GIF
0x008881a0: CwxHTML_ModuleImage
0x00888308: eIMG,MAP,AREA
0x00888334: WIDTH
0x00888340: HEIGHT
0x0088834e: ALIGN
0x0088835a: TEXTTOP
0x0088836a: CENTER
0x00888378: ABSCENTER
0x0088838c: USEMAP
0x008883aa: AREA
0x008883b4: SHAPE
0x008883c0: COORDS
0x008883ce: POLY
0x008883d8: CIRCLE
0x008883e6: RECT
0x008883f0: TARGET
0x00888800: wxHTML_ModuleList
0x00888888: OL,UL,LI
0x008888a0: %i. 
0x00888b66: lwxHTML_ModuleDefinitionList
0x00888bf0: DL,DT,DD
0x00888cf8: wxHTML_ModulePre
0x00888e38: GREY
0x00888e42: wxHTML_ModuleHLine
0x00888fa0: NOSHADE
0x00889090: wxHTML_ModuleLinks
0x00889118: TARGET
0x00889408: BGCOLOR
0x00889418: VALIGN
0x00889426: CELLSPACING
0x0088943e: CELLPADDING
0x00889456: BORDER
0x0088946c: WIDTH
0x00889478: COLSPAN
0x00889488: ROWSPAN
0x008894a0: BOTTOM
0x008894ae: NOWRAP
0x008894bc: wxHTML_ModuleTables
0x00889548: TABLE,TR,TD,TH
0x00889566: TABLE
0x00889572: ALIGN
0x0088958a: RIGHT
0x00889596: LEFT
0x008895a0: CENTER
0x00889690: wxHTML_ModuleSpans
0x008897c8: wxHTML_ModuleStyleTag
0x00889848: STYLE
0x00889908: wxHtmlFilter
0x00889922: wxHtmlFilterPlainText
0x0088994e: &amp;
0x0088995a: <HTML><BODY><PRE>
0x00889982: </PRE></BODY></HTML>
0x008899ac: wxHtmlFilterImage
0x008899d0: image/
0x008899de: <HTML><BODY><IMG SRC="
0x00889a0c: "></BODY></HTML>
0x00889a2e: wxHtmlFilterHTML
0x00889a50: text/html
0x00889aa8: ; charset=
0x00889abe: wxHtmlFilterModule
0x00889f60: wxVListBox
0x0088a82e: Select() may only be used with multiselection listbox
0x0088a8b0: "Select(): invalid item index
0x0088a8f6: eSelectRange() may only be used with multiselection listbox
0x0088a982: "SelectRange(): invalid item index
0x0088a9d2: lSelectAll may only be used with multiselection listbox
0x0088aa96: wxVListBox::DoSetCurrent(): invalid item index
0x0088ab16: )SendSelectedEvent() shouldn't be called
0x0088abc4: wxVListBox::SetSelection(): invalid item index
0x0088ac30: dGetFirst/NextSelected() may only be used with multiselection listboxes
0x0088adc6: wxBufferedDC already initialised
0x0088af6a: unknown scroll event type?
0x0088afba: target window must not be NULL
0x0088b00e: oRefreshUnits(): empty range
0x0088b084: nRefreshRowsColumns(): empty range
0x0088b0ca: wxVScrolledWindow
0x0088b0ee: wxHScrolledWindow
0x0088b112: wxHVScrolledWindow
0x0088d480: wxDragImage
0x0088d53a: Image list must not be null in BeginDrag.
0x0088d59a: )Window must not be null in BeginDrag.
0x0088d5fa: BeginDrag failed.
0x0088d630: )ImageList_AddIcon failed in BeginDrag.
0x0088d686: gImage list must not be null in EndDrag.
0x0088d6d8: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0088d750: ReleaseCapture
0x0088d774: Image list must not be null in Move.
0x0088d7c4: Image list must not be null in Show.
0x0088d814: Image list must not be null in Hide.
0x0088d9dc: wxDDEServer
0x0088d9f4: wxDDEClient
0x0088da0c: wxDDEConnection
0x0088da2c: wxDDEModule
0x0088da44: Failed to initialize DDE
0x0088dad0: all DDE objects should be deleted by now
0x0088dcf8: Failed to disconnect from DDE server gracefully
0x0088ddb2: "wxDDEServer::Execute() supports only text data
0x0088de12: DDE execute request failed
0x0088de48: DDE data request failed
0x0088de88: 0Buffer too small in wxDDEConnection::Request
0x0088dfd6: Buffer too small in _DDECallback (XTYP_EXECUTE)
0x0088e036: Buffer too small in _DDECallback (XTYP_POKE)
0x0088e090: Buffer too small in _DDECallback (XTYP_ADVDATA)
0x0088e252: DDE not initialized
0x0088e2a8: Failed to free DDE string handle
0x00898abc: "Invalid wxDC
0x00898b5c: SysHeader32
0x00898b84: COMBOBOX
0x00898b96: HEADER
0x00898bb6: TREEVIEW
0x00898bdc: BUTTON
0x00898bea: MENU
0x00898c16: WINDOW
0x00898c46: TASKDIALOG
0x00898c5c: EXPLORER::LISTVIEW;LISTVIEW
0x00898c94: uxtheme.dll
0x00898cd0: EDIT
0x00898cda: PROGRESS
0x008991a0: wxWindowsPrinter
0x008991c2: wxWindowsPrintPreview
0x0089927a: Could not create an abort dialog.
0x008993f2: cPrint abort dialog unexpected disappeared.
0x008995c0: wxWindowsPrintNativeData
0x008996b4: "Paper database wasn't initialized in wxPrintData::ConvertFromNative.
0x00899756: Printing error: 
0x00899778: wxWindowsPrintDialog
0x0089a078: wxWindowsPageSetupDialog
0x0089a99e: tIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0089aa18: GlobalLock
0x0089ab7e: GlobalUnlock
0x0089ab98: GlobalAlloc
0x0089abbc: GlobalFree
0x0089abd2: Unknown
0x0089abe2: CDERR_FINDRESFAILURE
0x0089ac0c: CDERR_INITIALIZATION
0x0089ac36: CDERR_LOADRESFAILURE
0x0089ac60: CDERR_LOADSTRFAILURE
0x0089ac8a: CDERR_LOCKRESFAILURE
0x0089acb4: CDERR_MEMALLOCFAILURE
0x0089ace0: CDERR_MEMLOCKFAILURE
0x0089ad0a: CDERR_NOHINSTANCE
0x0089ad2e: CDERR_NOHOOK
0x0089ad48: CDERR_NOTEMPLATE
0x0089ad6a: CDERR_STRUCTSIZE
0x0089ad8c: PDERR_RETDEFFAILURE
0x0089adb4: PDERR_PRINTERNOTFOUND
0x0089ade0: PDERR_PARSEFAILURE
0x0089ae06: PDERR_NODEVICES
0x0089ae26: PDERR_NODEFAULTPRN
0x0089ae4c: PDERR_LOADDRVFAILURE
0x0089ae76: PDERR_INITFAILURE
0x0089ae9a: PDERR_GETDEVMODEFAIL
0x0089aec4: PDERR_DNDMMISMATCH
0x0089aeea: PDERR_DEFAULTDIFFERENT
0x0089af18: PDERR_CREATEICFAILURE
0x0089b140: wxPrinterDCImpl
0x0089b54c: In file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0089b5c4: StartDoc
0x0089b5e6: CreateDC(printer)
0x0089b622: "invalid bitmap in wxPrinterDC::DrawBitmap
0x0089b6aa: GlobalLock
0x0089b6d0: GlobalUnlock
0x0089b708: GetObject(DIBSECTION)
0x0089b734: StretchDIBits
0x0089b76e: DeleteObject(hDIB)
0x0089b8d4: wxPrintPaperType
0x0089c5aa: wxPrintPaperModule
0x0089c748: kernel32.dll
0x0089c7a8: yIn file %s at line %d: '%s' failed with error 0x%08lx (%s).
0x0089c822: SafeArrayDestroy()
0x0089c85a: Uninitialized safe array
0x0089c8b0: Invalid dimension index
0x0089c8e0: SafeArrayGetLBound()
0x0089c912: dSafeArrayGetUBound()
0x0089c944: SafeArrayLock()
0x0089c96c: SafeArrayUnlock()
0x0089ca98: wxHelpControllerBase
0x0089cc54: unknown open mode in wxTextFile::Open
0x0089ccba: can't read closed file
0x0089cd6e: twxArrayString: index out of bounds
0x0089ced0: NULL input buffer
0x0089d1f0: wxColourData
0x0089d28e: "custom colour index out of range
0x0089d2ea: wxColourDialogEvent
0x0089d410: wxColourDialog
0x0089f112: twxArrayString: index out of bounds
0x0089f212: tshouldn't be called twice
0x0089f344: SCRIPT
0x0089f352: STYLE
0x0089f4ec: "NULL output string argument
0x0089f538: "invalid colour argument
0x0089f56a: black
0x0089f576: silver
0x0089f584: gray
0x0089f58e: white
0x0089f59a: maroon
0x0089f5b0: purple
0x0089f5be: fuchsia
0x0089f5ce: green
0x0089f5da: lime
0x0089f5e4: olive
0x0089f5f0: yellow
0x0089f5fe: navy
0x0089f608: blue
0x0089f612: teal
0x0089f61c: aqua
0x0089f664: twxArrayString: index out of bounds
0x0089f6f8: eSTYLE
0x0089f7ee: 0Could not convert string to UTF8!
0x0089f850: invalid seek mode
0x0089fa40: wxServerBase
0x0089fa5a: wxClientBase
0x0089fa74: wxConnectionBase
0x0089fb82: "Copy constructor of wxConnectionBase not implemented
0x008a00e0: #+3;CScs
0x008a0154: !1Aa
0x008be3da: %s /q
0x008be67a: svhsd_%s
0x008be68c: %s\INF\OEM*.INF
0x008be6ac: DisplayName
0x008be6e8: ynewdev.dll
0x008be776: .%s\vhsd_USB.sys
0x008be798: %s\vhsd_USB.cat
0x008be7b8: %s\vhsd_USB.inf
0x008be7f2: pnputil.exe
0x008be80a: /add-driver "%s"
0x008beb8c: sSOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall
0x008bebf6: vhsd_
0x008bec02: UninstallString
0x008becb6: spnputil.exe /enum-drivers
0x008bed00: f-d %S
0x008beea2: Version
0x008beeb2: DriverVer
0x008beff8: TrustedPublisher
0x008bf27e: s%s%s
0x008c7bba: VS_VERSION_INFO
0x008c7c16: StringFileInfo
0x008c7c3a: 040904E4
0x008c7c52: CompanyName
0x008c7c6c: VirtualHere Pty. Ltd.
0x008c7c9e: FileDescription
0x008c7cc0: VirtualHere USB Filter Utility
0x008c7d06: FileVersion
0x008c7d20: *******
0x008c7d36: InternalName
0x008c7d50: vhfilter
0x008c7d6a: LegalCopyright
0x008c7d88: VirtualHere Pty. Ltd.
0x008c7dba: OriginalFilename
0x008c7ddc: vhfilter.exe
0x008c7dfe: ProductName
0x008c7e18: VirtualHere USB Filter Utility
0x008c7e5e: ProductVersion
0x008c7e7c: *******
0x008c7e92: VarFileInfo
0x008c7eb2: Translation
0x008cb2cc: ROOT\VHUSB3HC
0x008cb520: t\\?\GLOBALROOT\Device\vhfilter
0x008cb676: tSYSTEM\CurrentControlSet\Control\COM Name Arbiter
0x008cb6dc: ComDB
0x008cb6e8: SYSTEM\CurrentControlSet\Control\COM Name Arbiter\Devices
0x008cb914: W\\?\%s
0x008cb938: Error %d getting instanceid from symlink
0x008cb9aa: %s\vhfilter.exe
0x008cb9ca: runas
0x008cb9d6: vhfilter.exe
0x008cb9f0: --install-filter
0x008cc0d4: cfgmgr32.dll
0x008cc15c: %s,%s,%u
0x008cc580: uCR_DEFAULT
0x008cc598: CR_OUT_OF_MEMORY
0x008cc5ba: CR_INVALID_POINTER
0x008cc5e0: CR_INVALID_FLAG
0x008cc600: CR_INVALID_DEVNODE or CR_INVALID_DEVINST
0x008cc652: CR_INVALID_RES_DES
0x008cc678: CR_INVALID_LOG_CONF
0x008cc6a0: CR_INVALID_ARBITRATOR
0x008cc6cc: CR_INVALID_NODELIST
0x008cc6f4: CR_DEVNODE_HAS_REQS or CR_DEVINST_HAS_REQS
0x008cc74a: CR_INVALID_RESOURCEID
0x008cc776: CR_DLVXD_NOT_FOUND
0x008cc79c: CR_NO_SUCH_DEVNODE or CR_NO_SUCH_DEVINST
0x008cc7ee: CR_NO_MORE_LOG_CONF
0x008cc816: CR_NO_MORE_RES_DES
0x008cc83c: CR_ALREADY_SUCH_DEVNODE or CR_ALREADY_SUCH_DEVINST
0x008cc8a2: CR_INVALID_RANGE_LIST
0x008cc8ce: CR_INVALID_RANGE
0x008cc8f0: CR_FAILURE
0x008cc906: CR_NO_SUCH_LOGICAL_DEV
0x008cc934: CR_CREATE_BLOCKED
0x008cc958: CR_NOT_SYSTEM_VM
0x008cc97a: CR_REMOVE_VETOED
0x008cc99c: CR_APM_VETOED
0x008cc9b8: CR_INVALID_LOAD_TYPE
0x008cc9e2: CR_BUFFER_SMALL
0x008cca02: CR_NO_ARBITRATOR
0x008cca24: CR_NO_REGISTRY_HANDLE
0x008cca50: CR_REGISTRY_ERROR
0x008cca74: CR_INVALID_DEVICE_ID
0x008cca9e: CR_INVALID_DATA
0x008ccabe: CR_INVALID_API
0x008ccadc: CR_DEVLOADER_NOT_READY
0x008ccb0a: CR_NEED_RESTART
0x008ccb2a: CR_NO_MORE_HW_PROFILES
0x008ccb58: CR_DEVICE_NOT_THERE
0x008ccb80: CR_NO_SUCH_VALUE
0x008ccba2: CR_WRONG_TYPE
0x008ccbbe: CR_INVALID_PRIORITY
0x008ccbe6: CR_NOT_DISABLEABLE
0x008ccc0c: CR_FREE_RESOURCES
0x008ccc30: CR_QUERY_VETOED
0x008ccc50: CR_CANT_SHARE_IRQ
0x008ccc74: CR_NO_DEPENDENT
0x008ccc94: CR_SAME_RESOURCES
0x008cccb8: CR_NO_SUCH_REGISTRY_KEY
0x008ccce8: CR_INVALID_MACHINENAME
0x008ccd16: CR_REMOTE_COMM_FAILURE
0x008ccd44: CR_MACHINE_UNAVAILABLE
0x008ccd72: CR_NO_CM_SERVICES
0x008ccd96: CR_ACCESS_DENIED
0x008ccdb8: CR_CALL_NOT_IMPLEMENTED
0x008ccde8: CR_INVALID_PROPERTY
0x008cce10: CR_DEVICE_INTERFACE_ACTIVE
0x008cce46: CR_NO_SUCH_DEVICE_INTERFACE
0x008cce7e: CR_INVALID_REFERENCE_STRING
0x008cceb6: CR_INVALID_CONFLICT_LIST
0x008ccee8: CR_INVALID_INDEX
0x008ccf0a: CR_INVALID_STRUCTURE_SIZE
0x008cd306:  SYSTEM\CurrentControlSet\services\eventlog\VirtualHere USB Server
0x008cd38c: CategoryCount
0x008cd3a8: TypesSupported
0x008cd3c6: CategoryMessageFile
0x008cd3ee: EventMessageFile
0x008cd410: ParameterMessageFile
0x008cdae4: %d-%.2d-%.2d %.2d:%.2d:%.2d
0x008d39e2: twxArrayString: index out of bounds
0x008d599c: Please uninstall the service (-b) before uninstalling the drivers
0x008d5a20: There was an error uninstalling the driver, please check the log
0x008d5aa2: The server driver has been successfully uninstalled
0x008d6482: /C %s
0x008d648e: C:\windows\system32\cmd.exe
0x008d6a9c: s.local
0x008d6c92: SOFTWARE\Microsoft\Cryptography
0x008d6cd2: MachineGuid
0x008d6cea: ROOT\CIMV2
0x008d6d08: SELECT * FROM Win32_Bios
0x008d6d3a: SerialNumber
0x008d743c: VirtualHere USB Server
0x008d7556:  -r 
0x008d7560:  -c 
0x008d7570: Server providing virtualized access to USB devices over a network
0x0097e04b:  <[y
0x0097e06b:  =\z
0x00980e39: <NULL>
0x00983711:  !"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\]^_`abcdefghijklmnopqrstuvwxyz{|}~
0x0098518f: VS_VERSION_INFO
0x009851eb: StringFileInfo
0x0098520f: 040904b0
0x00985227: FileDescription
0x00985249: VirtualBox USB Driver
0x0098527b: InternalName
0x00985295: VBoxUSB
0x009852ab: OriginalFilename
0x009852cd: VBoxUSB.sys
0x009852eb: CompanyName
0x00985305: Oracle Corporation
0x00985333: FileVersion
0x0098534d: 5.0.26.108824
0x0098536f: LegalCopyright
0x0098538d: Copyright (C) 2009-2016 Oracle Corporation
0x009853eb: ProductName
0x00985405: Oracle VM VirtualBox
0x00985437: ProductVersion
0x00985455: 5.0.26.108824
0x00985477: PrivateBuild
0x00985491: Private build by michael
0x009854cb: VarFileInfo
0x009854eb: Translation
0x009868d4: RAny use of this Certificate constitutes acceptance of the DigiCert CP/CPS and the Relying Party Agreement which limit liability and are incorporated herein by reference
0x00986f78: RAny use of this Certificate constitutes acceptance of the DigiCert CP/CPS and the Relying Party Agreement which limit liability and are incorporated herein by reference
0x00987650: RAny use of this Certificate constitutes acceptance of the DigiCert CP/CPS and the Relying Party Agreement which limit liability and are incorporated herein by reference
0x00987ed8: 707B4B94146C8A282A4D80F6D87FAC37A0BB2554
0x00987f43: OSAtt
0x00987f56: 2:6.1
0x00987f86: vhsd_usb.inf
0x00987fc5: <<<Obsolete>>
0x00988016: L{DE351A42-8E59-11D0-8C47-00C04FC295EE
0x0098806d: 85E087B78A415EF505F1B81A95504C0C96B90716
0x009880d8: OSAtt
0x009880eb: 2:6.1
0x0098811b: vhsd_usb.sys
0x00988148: L{C689AAB8-8E78-11D0-8C47-00C04FC295EE
0x009881c6: <<<Obsolete>>
0x00988228: 7X64
0x00988247: HWID
0x00988258: usb\vhsd
0x00988f98: RAny use of this Certificate constitutes acceptance of the DigiCert CP/CPS and the Relying Party Agreement which limit liability and are incorporated herein by reference
0x0098963c: RAny use of this Certificate constitutes acceptance of the DigiCert CP/CPS and the Relying Party Agreement which limit liability and are incorporated herein by reference
0x00989d14: RAny use of this Certificate constitutes acceptance of the DigiCert CP/CPS and the Relying Party Agreement which limit liability and are incorporated herein by reference
0x0098b42a: %m/%d/%y
0x0098b45a: %H:%M:%S
0x0099ef38: P&j&
0x009b2d50: 0JaJ
0x009bafc4: 0]w]
0x009d0566: VS_VERSION_INFO
0x009d05c2: StringFileInfo
0x009d05e6: 040904E4
0x009d05fe: CompanyName
0x009d0618: VirtualHere Pty. Ltd.
0x009d064a: FileDescription
0x009d066c: VirtualHere USB Server
0x009d06a2: FileVersion
0x009d06bc: 4.8.1
0x009d06ce: InternalName
0x009d06e8: vhusbdwin64
0x009d0706: LegalCopyright
0x009d0724: VirtualHere Pty. Ltd.
0x009d0756: OriginalFilename
0x009d0778: vhusbdwin64.exe
0x009d079e: ProductName
0x009d07b8: VirtualHere USB Server
0x009d07ee: ProductVersion
0x009d080c: 4.8.1
0x009d081e: VarFileInfo
0x009d083e: Translation
