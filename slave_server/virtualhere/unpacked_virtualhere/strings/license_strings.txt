# 许可证相关字符串
# 总数: 889

0x006b65ea: timer has expired
0x006b6a4b: invalid handshake header
0x006b6bf4: invalid CCS message, len: %d
0x006b6c11: invalid CCS message, content: %02x
0x006b6c92: invalid alert message, len: %d
0x006b76cd: timer expired
0x006b7f32: discarding invalid record (header)
0x006b7f98: discarding invalid record (mac)
0x006b841c: invalid zero-length message type: %d
0x006b8c37: The certificate validity has expired
0x006b8d3c: The CRL is expired
0x006bb87f: CIPHER - Input data contains invalid padding and is rejected
0x006bb91f: CIPHER - The context is invalid. For example, because it was freed
0x006bbc76: ECP - Invalid private or public key
0x006bc056: PK - Invalid key tag or value
0x006bc125: PK - The pubkey tag or value is invalid (only RSA and EC are supported)
0x006bc16d: PK - The algorithm tag or value is invalid
0x006bc448: RSA - Input data contains invalid padding and is rejected
0x006bc701: SSL - An invalid SSL record was received
0x006bcea0: SSL - Session ticket has expired
0x006bd359: SSL - Invalid value in SSL config
0x006bd3df: X509 - The CRT/CRL/CSR format is invalid, e.g. different type expected
0x006bd426: X509 - The CRT/CRL/CSR version element is invalid
0x006bd458: X509 - The serial tag or value is invalid
0x006bd482: X509 - The algorithm tag or value is invalid
0x006bd4af: X509 - The name tag or value is invalid
0x006bd4d7: X509 - The date tag or value is invalid
0x006bd4ff: X509 - The signature tag or value invalid
0x006bd529: X509 - The extension tag or value is invalid
0x006bd683: X509 - Input invalid
0x006bd775: AES - Invalid key length
0x006bd78e: AES - Invalid data input length
0x006bd7ae: AES - Invalid input data
0x006bd8bd: ASN1 - Error when trying to determine the length or invalid length
0x006bd932: ASN1 - Data is invalid. (not used)
0x006bd9d0: BASE64 - Invalid character in input
0x006bda61: BIGNUM - There is an invalid character in the digit string
0x006bdbc2: BLOWFISH - Invalid data input length
0x006bdc31: CAMELLIA - Invalid data input length
0x006bdcfd: CHACHA20 - Invalid input parameter(s)
0x006bdefa: DES - The data input has an invalid length
0x006be372: NET - The context is invalid, eg because it was free()ed
0x006be3d0: NET - Input invalid
0x006be4ac: POLY1305 - Invalid input parameter(s)
0x006be685: XTEA - The data input has an invalid length
0x006bea60: invalid PK
0x006bf0b0: invalid XRC resource, doesn't have root node <resource>
0x006bf7c6: invalid long specification "%s"
0x006bf7e6: invalid float specification "%s"
0x006bface: Invalid direction "%s": must be one of wxLEFT|wxRIGHT|wxTOP|wxBOTTOM.
0x006bfb8a: invalid font weight value "%d"
0x006bfe50: Invalid window variant "%s": must be one of normal|small|mini|large.
0x006c09f7: Invalid font point size
0x006c1b41: wxNO_DEFAULT is invalid without wxNO
0x006c1b94: wxCANCEL_DEFAULT is invalid without wxCANCEL
0x006c700c: removing invalid tracker node
0x006c7052: invalid event handler
0x006c73e0: Couldn't register clipboard format '%s'.
0x006c9627: invalid event handler
0x006c96a5: removing invalid tracker node
0x006d0f95: failed to register window class?
0x006d173c: MSWRegisterMessageHandler
0x006d17f0: MSWUnregisterMessageHandler
0x006d1bdc: RegisterHotKey
0x006d1c0a: UnregisterHotKey
0x006d2024: invalid event handler
0x006d4b09: invalid event handler
0x006d5749: removing invalid tracker node
0x006d7ff3: invalid event handler
0x006d95a6: invalid event handler
0x006da236: OnActivate
0x006da4f1: removing invalid tracker node
0x006da651: invalid event handler
0x006da728: 20wxEventFunctorMethodI14wxEventTypeTagI15wxActivateEventE12wxEvtHandler7wxEventS3_E
0x006dbb44: removing invalid tracker node
0x006dbc16: invalid event handler
0x006dc316: GetRegisteredClassName
0x006dc3ca: UnregisterWindowClasses
0x006dc877: removing invalid tracker node
0x006dce12: invalid event handler
0x006df327: removing invalid tracker node
0x006df487: invalid event handler
0x006dfcf6: invalid bitmap type
0x006e2974: removing invalid tracker node
0x006e37f5: invalid event handler
0x006e4e2e: invalid event handler
0x006e7408: 15wxActivateEvent
0x006e8c5f: invalid event handler
0x006ed73a: invalid IDs range: lower bound > upper bound
0x006ed78a: removing invalid tracker node
0x006ee59f: invalid wxFormatString - not initialized?
0x006ee7b4: requested argument not found - invalid format string?
0x006ef70c: Register
0x006ef89b: invalid ref data count
0x006efde1: Invalid negative precision
0x006f0fcc: (base != 0) && "DW_EH_PE_datarel is invalid with a base of 0"
0x006f16f3: invalid PK context
0x006f599f: invalid state %d
0x006f59d6: configured max major version is invalid, consider using mbedtls_ssl_config_defaults()
0x006f5ebe: invalid curve in ssl configuration
0x006f6f33: invalid state %d
0x006f77ac: ticket is expired
0x006fb6d6: unsupported register
0x006fb6eb: float registers unimplemented
0x006fb7c9: unknown register
0x006fd67c: Invalid message catalog.
0x006fe508: invalid flag specified
0x007009ad: invalid month
0x007051cc: Invalid value %ld for a boolean key "%s" in config file.
0x0070670b: invalid font family
0x00706fab: weight > wxFONTWEIGHT_INVALID
0x00707009: invalid font
0x0070797c: Invalid font point size
0x00707bba: RegisterModules
0x00708e49: invalid event handler
0x007095b5: invalid event handler
0x00709f51: invalid %s index %d: must be less than %d
0x0070abd5: invalid event handler
0x0070cd81: invalid event handler
0x0070df67: invalid event handler
0x0070ef03: invalid event handler
0x0071057e: invalid event handler
0x00711d13: invalid event handler
0x00712063: invalid event handler
0x007123d3: invalid event handler
0x007127a9: invalid event handler
0x00712b0f: invalid event handler
0x00713a85: invalid event handler
0x0071470f: invalid event handler
0x007157f9: removing invalid tracker node
0x00717b0b: invalid event handler
0x00718d79: invalid event handler
0x0071ac07: invalid event handler
0x0071cb0d: invalid event handler
0x0071ce5c: invalid depth
0x0071cf13: invalid event handler
0x0071da3f: invalid event handler
0x00721f94: Invalid TIFF image index.
0x007225c7: BMP: Couldn't save invalid image.
0x00722975: ICO: Invalid icon index.
0x0072366a: PCX: invalid image
0x00727be0: invalid event handler
0x00728dfb: invalid event handler
0x0072baa5: Invalid parameters
0x0072bab8: invalid ellipsize mode
0x0072c7f6: invalid event handler
0x0072cd4c: invalid event handler
0x0072e27a: invalid status bar field index
0x0072ee0e: invalid statusbar field index
0x007313bc: invalid event handler
0x007333d1: invalid flag: not within SIZER_FLAGS_MASK
0x00734475: invalid growable row index
0x007344aa: invalid growable column index
0x00734538: invalid row index
0x007345bd: invalid column index
0x00735e48: removing invalid tracker node
0x00735ec0: invalid event handler
0x007368ea: invalid direction
0x007369dd: invalid event handler
0x00736a4d: invalid image alignment
0x00738701: invalid event handler
0x0073975d: invalid event handler
0x0073ad18: invalid event handler
0x0073b0d8: Register
0x0073b13c: Unregister
0x0073ef10: invalid event handler
0x0073fb00: Register
0x00743ed9: invalid event handler
0x00747617: invalid event handler
0x0074b4f8: wxMSWActivatePID
0x0074ccbe: invalid event handler
0x0074de55: invalid gradient stop position < 0
0x0074de78: invalid gradient stop position > 1
0x0075563e: '%s' is one of the invalid strings
0x00755686: '%s' contains invalid character(s)
0x007556e7: invalid event handler
0x0075834d: invalid event handler
0x007586f9: Invalid index when inserting the button
0x00758818: invalid pos.
0x0075893e: Invalid category.
0x007589ce: Invalid task Item.
0x00758a3c: Invalid category item.
0x00758ad1: Invalid title.
0x00760041: invalid event handler
0x00760c4b: invalid event handler
0x007630e7: invalid event handler
0x00764775: invalid event handler
0x00768607: invalid event handler
0x007686bc: invalid message box return value
0x00768790: removing invalid tracker node
0x0076a223: invalid event handler
0x0076afac: wxNO_DEFAULT is invalid without wxNO
0x0076afff: wxCANCEL_DEFAULT is invalid without wxCANCEL
0x0076b0c7: invalid event handler
0x00785b8e: "ofs != wxInvalidOffset || mode != wxFromStart"
0x00786596: invalid multibyte character
0x00786fdc: not well-formed (invalid token)
0x007870bf: reference to invalid character number
0x007874a5: invalid argument
0x007ea52f: Invalid initial selection
0x007ec0db: invalid event handler
0x007edc48: invalid event handler
0x007efc42: invalid sizer item
0x007f0703: invalid event handler
0x007f147a: invalid event handler
0x007f2654: invalid event handler
0x007f2791: invalid item index
0x007f344b: invalid event handler
0x007f36c1: invalid date
0x007f37bf: invalid day
0x007f40c4: Register
0x007f40cd: m_registered == -1
0x007f428b: invalid event handler
0x007f5959: invalid event handler
0x007f6d45: invalid event handler
0x007f7995: invalid event handler
0x007f8c1d: invalid event handler
0x007fa453: invalid event handler
0x007fc578: invalid event handler
0x007fe57f: invalid item
0x007ff030: invalid item in selection - bad internal state
0x007fffd8: invalid event handler
0x0080002f: invalid column index
0x008002b4: removing invalid tracker node
0x008032ad: Can't make current an invalid item.
0x008056bf: invalid event handler
0x008058e9: removing invalid tracker node
0x008084e5: wxNO_DEFAULT is invalid without wxNO
0x00808538: wxCANCEL_DEFAULT is invalid without wxCANCEL
0x008086c1: invalid event handler
0x0080a5a9: invalid event handler
0x0080ba4d: invalid event handler
0x0080d635: wxNO_DEFAULT is invalid without wxNO
0x0080d688: wxCANCEL_DEFAULT is invalid without wxCANCEL
0x0080d7fd: invalid event handler
0x0080e4fd: invalid event handler
0x00810e08: Invalid right column
0x00810e46: Invalid bottom row
0x0081193f: invalid column index
0x00811c51: invalid index
0x00811c85: invalid row index
0x00815716: invalid event handler
0x0081582f: invalid row/column
0x008183d7: invalid event handler
0x0081a04d: invalid event handler
0x0081a6dd: Invalid index passed to SetClientObject()
0x0081a788: Invalid index passed to GetClientObject()
0x0081a7d7: Invalid index passed to SetClientData()
0x0081a87a: Invalid index passed to GetClientData()
0x0081b450: Invalid infobar button position
0x0081bdc8: invalid event handler
0x0081c7d5: invalid event handler
0x0081dd17: invalid image list
0x0081e162: invalid internal data pointer?
0x0081e564: invalid event handler
0x00821190: HandleMDIActivate
0x00821c0d: removing invalid tracker node
0x00821c78: invalid event handler
0x00822f20: Register
0x00822f29: m_registered == -1
0x008231c7: invalid event handler
0x0082522b: invalid event handler
0x00827981: invalid event handler
0x0082b916: invalid event handler
0x0082cc05: invalid event handler
0x0082f245: Invalid page number
0x0082fbbd: invalid event handler
0x00830c6d: invalid event handler
0x0083188c: invalid event handler
0x00835845: invalid event handler
0x00837a2f: %s: Invalid %stag "%s" (not supported by codec)
0x00837c27: %s: Invalid InkNames value; expecting %d names, found %d
0x00837f6f: Invalid argument to _TIFFMultiplySSize() in %s
0x00838bc9: Invalid vertical YCbCr subsampling
0x00838d1d: Invalid values for YCbCrCoefficients tag
0x00838d46: Invalid values for ReferenceBlackWhite tag
0x00838d95: Invalid value for WhitePoint tag.
0x00838f21: Invalid td_samplesperpixel value
0x00838f42: Invalid YCbCr subsampling (%dx%d)
0x00838f93: %I64u: Invalid strip byte count, strip %lu
0x00838fe3: Invalid YCbCr subsampling
0x00839134: Invalid strip byte count %I64u, strip %lu
0x0083925d: %I64u: Invalid tile byte count, tile %lu
0x008392c4: Invalid buffer size
0x00839ea4: Registering anonymous field with tag %d (0x%x) failed
0x00839f90: Invalid data type for tag %s
0x0083a6cc: Invalid TIFF directory; tags are not sorted in ascending order
0x0083acfb: Invalid type for [Strip|Tile][Offset/ByteCount] tag
0x0083c29b: Invalid attempt to read row data
0x0083c3e5: png_image_begin_read_from_stdio: invalid argument
0x0083c457: png_image_begin_read_from_file: invalid argument
0x0083c4c4: png_image_begin_read_from_memory: invalid argument
0x0083c58b: png_image_finish_read: invalid argument
0x0083c675: invalid memory read
0x0083c853: invalid PNG color type
0x0083ca18: png_read_image: invalid transformations
0x0083d537: Ignoring invalid time value
0x0083d74e: invalid chromaticities
0x0083d78c: invalid end points
0x0083d7c9: invalid sRGB rendering intent
0x0083d8a4: invalid length
0x0083d8c7: invalid rendering intent
0x0083d8fd: invalid signature
0x0083d98e: invalid ICC profile color space
0x0083d9ae: invalid embedded Abstract ICC profile
0x0083db11: Invalid image width in IHDR
0x0083db71: Invalid image height in IHDR
0x0083dbb6: Invalid bit depth in IHDR
0x0083dbd0: Invalid color type in IHDR
0x0083dbeb: Invalid color type/bit depth combination in IHDR
0x0083dcaf: Invalid filter method in IHDR
0x0083dccd: Invalid IHDR data
0x0083ea48: invalid alpha mode
0x0083ea8e: invalid file gamma in png_set_gamma
0x0083eab2: invalid screen gamma in png_set_gamma
0x0083ead8: invalid error action to rgb_to_gray
0x0083eb9a: invalid background gamma type
0x0083ec46: invalid after png_start_read_image or png_read_update_info
0x0083ec81: invalid before the PNG header has been read
0x0083ed00: png_set_filler is invalid for low bit depth gray output
0x0083f3eb: png_image_write_to_memory: invalid argument
0x0083f44e: png_image_write_to_stdio: invalid argument
0x0083f4b2: png_image_write_to_file: invalid argument
0x0083f7aa: Invalid palette size, hIST allocation skipped
0x0083f800: Invalid pCAL equation type
0x0083f81b: Invalid pCAL parameter count
0x0083f838: Invalid format for pCAL parameter
0x0083f8ed: Invalid sCAL unit
0x0083f8ff: Invalid sCAL width
0x0083f912: Invalid sCAL height
0x0083f955: Invalid sCAL width ignored
0x0083f970: Invalid sCAL height ignored
0x0083f98c: Invalid palette length
0x0083f9a3: Invalid palette
0x0083f9b3: Invalid iCCP compression method
0x0083faa0: Ignoring invalid time value
0x0083fb03: png_set_sPLT: invalid sPLT
0x0083fb66: invalid unknown chunk location
0x0083fb85: png_set_keep_unknown_chunks: invalid keep
0x0083fc62: invalid compression buffer size
0x0083fd5c: invalid location in png_set_unknown_chunks
0x00840065: Invalid component ID %d in SOS
0x00840084: Invalid crop request
0x008401ed: Invalid memory pool code %d
0x0084022c: Invalid progressive parameters Ss=%d Se=%d Ah=%d Al=%d
0x00840263: Invalid progressive parameters at scan script entry %d
0x008402b1: Invalid scan script at entry %d
0x0084064c: Invalid color quantization mode change
0x00840830: Invalid JPEG file structure: %s before SOF
0x0084085b: Invalid JPEG file structure: two SOF markers
0x00840888: Invalid JPEG file structure: missing SOS marker
0x008408e2: Invalid JPEG file structure: two SOI markers
0x008411f3: Invalid SOS parameters for sequential JPEG
0x00842a93: invalid font weight "%s"
0x00842acc: invalid font style "%s"
0x00842b12: invalid font size "%s"
0x0084820b: invalid event handler
0x008482bf: removing invalid tracker node
0x0084a277: Invalid markup
0x0084e80c: invalid event handler
0x0084f219: "interpolationMode != wxInterpolationModeInvalid"
0x008658b1:  Demokrat
0x008689e8: Kiswahili (Jamhuri ya Kidemokrasia ya Kongo)
0x0086d1cd: Invalid access!
0x008785b0: Invalid buffer size
0x00879044: invalid event handler
0x00879e78: invalid event handler
0x0087ab2c: invalid event handler
0x0087b14a: invalid column index
0x0087b1b4: invalid order array
0x0087b1ed: invalid position
0x0087b20b: invalid index
0x0087cd92: invalid event handler
0x0087efa4: invalid column header alignment
0x0087fcac: invalid event handler
0x00881bce: invalid event handler
0x008829f4: invalid event handler
0x008831b7: Invalid wxGridCellNumberRenderer parameters "%s"
0x0088338e: Invalid wxGridCellFloatRenderer format parameter string '%s ignored
0x00883f76: Invalid wxGridCellFloatRenderer format parameter string '%s ignored
0x00883fe8: TryActivate
0x008846bc: DoActivate
0x008846c7: Must be overridden if TryActivate() is overridden
0x00884803: invalid event handler
0x008860f9: No HTML tag handlers registered, is your program linked correctly (you might need to use FORCE_WXHTML_MODULES)?
0x0088ae2e: invalid event handler
0x0088d464: invalid event handler
0x0088db70: Failed to register DDE server '%s'
0x0088db93: Failed to unregister DDE server '%s'
0x0088e416: or an invalid instance identifier
0x0088e7b2: an invalid transaction identifier was passed to a DDEML function.
0x0088eb58: 11wxDDEModule
0x0088ede9: TIFFRegisterCODEC
0x0088edfb: No space to register compression scheme %s
0x0088ee26: TIFFUnRegisterCODEC
0x0088ee3a: Cannot remove compression scheme %s; not registered
0x008931c0: Invalid YCbCr subsampling (%dx%d)
0x00894062: invalid window size (libpng)
0x0089408c: invalid
0x008940fd: invalid values
0x008941e3: invalid with alpha channel
0x008941fe: invalid index
0x0089420c: invalid gray level
0x0089421f: invalid color
0x0089424c: invalid parameter count
0x0089427f: invalid data
0x0089428c: invalid unit
0x00894420: invalid chunk type
0x00894588: invalid user transform pixel depth
0x00894908: invalid window size
0x00894949: invalid block type
0x0089495c: invalid stored block lengths
0x0089499d: invalid code lengths set
0x008949b6: invalid bit length repeat
0x008949d0: invalid code -- missing end-of-block
0x008949f5: invalid literal/lengths set
0x00894a11: invalid distances set
0x00894a27: invalid literal/length code
0x00894a43: invalid distance code
0x00894a59: invalid distance too far back
0x00897810: Invalid bit depth for grayscale image
0x00897836: Invalid bit depth for RGB image
0x00897856: Invalid bit depth for paletted image
0x0089787b: Invalid bit depth for grayscale+alpha image
0x008978a7: Invalid bit depth for RGBA image
0x008978c8: Invalid image color type specified
0x008978eb: Invalid compression type specified
0x0089790e: Invalid filter type specified
0x0089792c: Invalid interlace type specified
0x0089794d: Invalid number of colors in palette
0x008979cc: Invalid sRGB rendering intent specified
0x00897a24: ICC profile length invalid (not a multiple of 4)
0x00897a55: iCCP: invalid keyword
0x00897a6b: sPLT: invalid keyword
0x00897a81: Invalid sBIT depth specified
0x00897a9e: Invalid number of transparent colors specified
0x00897b74: Invalid background palette index
0x00897c15: Invalid number of histogram entries specified
0x00897c43: tEXt: invalid keyword
0x00897c6d: zTXt: invalid compression type
0x00897c8c: zTXt: invalid keyword
0x00897ca2: iTXt: invalid keyword
0x00897cb8: iTXt: invalid compression
0x00897d44: pCAL: invalid keyword
0x00897da4: Invalid time specified for tIME chunk
0x0089f261: invalid event handler
0x0089fc18: invalid buffer size
0x008a0030: invalid distance too far back
0x008a004e: invalid distance code
0x008a0064: invalid literal/length code
0x008a22bc: Invalid data for scanline %ld
0x008bd95c: Error, invalid value for property "%s"
0x008bdc6b: Must purchase this server to use OnDeviceKick!
0x008bdd9f: Must purchase this server to use ClientDeauthorization!
0x008bdeb6: Must purchase this server to use onDeviceIgnore!
0x008be02f: Must purchase this server to use onDeviceUnignore!
0x008be148: Must purchase this server to use ClientAuthorization!
0x008be229: BIND MAX_DEVICES_EXCEEDED
0x008be243: BIND_UNAUTHORIZED
0x008befaf: Invalid INF date (%s)
0x008cd6c2: licenseTextBox
0x008cd94e: Please enter the license key for this server
0x008cd97b: Server successfully licensed
0x008cd998: Invalid license
0x008cff09: <label>License</label>
0x008cff96: <object class="wxTextCtrl" name="licenseTextBox">
0x008d3832: There was an error registering this USB Server with Bonjour.
0x008d3b19: removing invalid tracker node
0x008d3cdc: invalid event handler
0x008d44c0: VIRTUALHERE USB SERVER LICENSE FOR NAS DEVICES
0x008d44ef: VirtualHere Pty. Ltd. (Company) hereby gives you a worldwide non-exclusive license to use the software "VirtualHere USB Server for NAS Devices" (Software) according to the following conditions:
0x008d45ba: - Install and use the Software on a NAS device to share a single USB device (with no payment required) over a network for 10 days. To share more than one USB device simultaneously or continue using the software after 10 days, you must purchase a License.
0x008d47ec: VIRTUALHERE DISCLAIMER OF WARRANTY: VIRTUALHERE PTY. LTD IS NOT RESPONSIBLE FOR ANY INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES OF ANY CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF GOODWILL, WORK STOPPAGE, COMPUTER FAILURE OR MALFUNCTION, OR ANY AND ALL OTHER COMMERCIAL DAMAGES OR LOSSES.
0x008d492a: VirtualHere uses the following software libraries which have these licenses:
0x008d4977: LZ4 LICENSE
0x008d4983: https://github.com/Cyan4973/lz4/blob/master/lib/LICENSE
0x008d49bc: MBED-TLS LICENSE
0x008d49cd: https://github.com/ARMmbed/mbedtls/blob/development/LICENSE
0x008d4a10: VIRTUALHERE USB SERVER LICENSE FOR WINDOWS
0x008d4a3b: VirtualHere Pty. Ltd. (Company) hereby gives you a worldwide non-exclusive license to use the software "VirtualHere USB Server for Windows" (Software) according to the following conditions:
0x008d4b02: - Install and use the Software on a Windows machine to share a single USB device (with no payment required) over a network. To share more than one USB device simultaneously from a single server you must purchase a License.
0x008d4bf0: - modify, translate, reverse engineer or decompile the Software except, the USB Server Driver is licensed under GPL (see below);
0x008d4d58: VIRTUALHERE DISCLAIMER OF WARRANTY: VIRTUALHERE PTY. LTD IS NOT RESPONSIBLE FOR ANY INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES OF ANY CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF GOODWILL, WORK STOPPAGE, COMPUTER FAILURE OR MALFUNCTION, OR ANY AND ALL OTHER COMMERCIAL DAMAGES OR LOSSES.
0x008d4e96: VirtualHere uses the following software libraries which have these licenses:
0x008d4ee3: LZ4 LICENSE
0x008d4eef: https://github.com/Cyan4973/lz4/blob/master/lib/LICENSE
0x008d4f28: MBED-TLS LICENSE
0x008d4f39: https://github.com/ARMmbed/mbedtls/blob/development/LICENSE
0x008d4f76: VIRTUALBOX LICENSE:
0x008d4f8a: The VirtualHere USB Server Driver is based upon the VirtualBox USB Stub Driver (VBoxUSB.sys). The source code and full license text for this driver can be found here https://www.virtualbox.org/browser/vbox/trunk
0x008d5170: VIRTUALHERE USB SERVER LICENSE FOR LINUX AND OSX
0x008d51a1: VirtualHere Pty. Ltd. (Company) hereby gives you a worldwide non-exclusive license to use the software "VirtualHere USB Server" (Software) according to the following conditions:
0x008d525c: - Install and use the Software on a computer to share a single USB device (with no payment required) over a network. To share more than one USB device simultaneously from a single server you must purchase a License.
0x008d5467: VIRTUALHERE DISCLAIMER OF WARRANTY: VIRTUALHERE PTY. LTD IS NOT RESPONSIBLE FOR ANY INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES OF ANY CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF GOODWILL, WORK STOPPAGE, COMPUTER FAILURE OR MALFUNCTION, OR ANY AND ALL OTHER COMMERCIAL DAMAGES OR LOSSES.
0x008d55a5: VirtualHere uses the following software libraries which have these licenses:
0x008d55f2: LZ4 LICENSE
0x008d55fe: https://github.com/Cyan4973/lz4/blob/master/lib/LICENSE
0x008d5637: MBED-TLS LICENSE
0x008d5648: https://github.com/ARMmbed/mbedtls/blob/development/LICENSE
0x008d57c7:    -l display license text
0x008d5f00: VirtualHere USB Server Trial Edition
0x008d5f34: Must purchase this server to use onServerRename!
0x008d60f4: Must purchase this server to use %s!
0x008d61fa: invalid vendorId %s
0x008d620e: invalid productId %s
0x008d6223: invalid address %s
0x008d62d7: Invalid format for %s
0x008d632f: The onClientDisconnect event requires a licensed server!
0x008d63d1: The onClientConnect event requires a licensed server!
0x008d67e6: License
0x008d687c: unlicensed,1,MCACDkn0jww6R5WOIjFqU/apAg4Um+mDkU2TBcC7fA1FrA==
0x008d68ec: Failed to create license
0x008d6953: unlicensed
0x008d695e: Invalid License
0x008d6970: Invalid License
0x008d6a2e: Error registering Auto-Find with mDNS, Auto-Find wont work, %s
0x008d6a6d: mDNS failed to register, Auto-Find wont work, %s
0x008d6ba1: Error DEregistering Auto-Find with mDns, %s
0x008d6bcd: Built-in mDNS failed to UNregister, %s
0x008d6d54: unlicensed,
0x008d6e30: max_devices=%d
0x008d6e3f: max_devices=unlimited
0x008d6e55: Server licensed to=%s %s
0x008d7b72: RegisterServiceCtrlHandler (serviceMain) failed, %s
0x008dd104: DnsServiceDeRegister
0x008dd136: DnsServiceRegister
0x008dd14c: DnsServiceRegisterCancel
0x008dd46e: RegisterDragDrop
0x008dd8b6: RegisterEventSourceW
0x008dd8ce: RegisterServiceCtrlHandlerExW
0x008ddd06: _set_invalid_parameter_handler
0x008deb2e: InvalidateRect
0x008ded80: RegisterClassW
0x008ded92: RegisterClipboardFormatW
0x008dedae: RegisterDeviceNotificationW
0x008dedcc: RegisterHotKey
0x008dedde: RegisterWindowMessageW
0x008df068: UnregisterClassW
0x008df07c: UnregisterDeviceNotification
0x008df09c: UnregisterHotKey
0x0097ce31: MAIN_MEDIUMREGISTEREDEVENT
0x0097cee9: MAIN_MACHINEREGISTEREDEVENT
0x0097d0f9: MAIN_GUESTSESSIONREGISTEREDEVENT
0x0097d199: MAIN_GUESTPROCESSREGISTEREDEVENT
0x0097d339: MAIN_GUESTFILEREGISTEREDEVENT
0x00984237: RTStrFormatTypeDeregister
0x00984251: RTStrFormatTypeRegister
0x00984c03: IoRegisterDeviceInterface
0x006c0038: )invalid wxDateTime
0x006c1652: xInvalid menu string '%s'
0x006c2c06: )invalid itemid value
0x006c2e2e: "invalid item in wxMenu::Append()
0x006c2e7a: invalid item in wxMenu::Insert
0x006c2ed4: invalid index in wxMenu::Insert
0x006c2f1e: invalid item in wxMenu::Insert()
0x006c2f68: invalid index in wxMenu::Insert()
0x006c2fb4: invalid item in wxMenu::Remove
0x006c3038: invalid item in wxMenu::Delete
0x006c30c4: yinvalid item in wxMenu::Destroy
0x006c3144: wxMenu::FindItemByPosition(): invalid menu index
0x006c7746: Invalid data size - can't be 0
0x006c780a: wxIDataObject::QueryGetData: invalid ptr.
0x006c7d14: pasting/dropping invalid bitmap
0x006d08ba: "can't set focus to invalid window
0x006d0936: invalid window show effect
0x006d0b34: invalid window
0x006d0bb4: invalid HWND in SubclassWin
0x006d0bf8: ninvalid HWND in UnsubclassWin
0x006d0db2: "invalid font in GetTextExtent()
0x006d1792: registering handler for the same message twice
0x006d1842: "unregistering non-registered handler?
0x006d1bec: RegisterHotKey
0x006d1c1c: UnregisterHotKey
0x006d1d58: nCan't set layout direction for invalid window
0x006d1db6: Invalid layout direction
0x006d31d4: invalid id value
0x006d3c94: lwe have invalid explicit bg colour?
0x006d3e4a: we have invalid explicit font?
0x006d6e3a: invalid ::CheckMenuRadioItem() parameter(s)
0x006da24e: wxTLW %p activated.
0x006da276: wxTLW %p deactivated, last focused: %p.
0x006da310: UnregisterClass("wxTLWHiddenParent")
0x006da384: DRegisterClass("wxTLWHiddenParent")
0x006dbe60: "invalid metric
0x006dc3a6: RegisterClass(%s)
0x006dc3e0: sUnregisterClass(%s)
0x006dd7b6: invalid submenu
0x006de2e0: invalid menu index
0x006de37c: invalid menu index in wxMenuBar::GetMenuLabel
0x006de3f6: "can't append invalid menu to menubar
0x006df880: invalid image
0x006df98a: invalid new image size
0x006df9dc: "invalid old image size
0x006dfbbe: "invalid subimage size
0x006dfc36: "invalid size
0x006dfd1e: invalid image coordinates
0x006dfda8: invalid bounding rectangle
0x006e0806: Invalid bitmap resource type.
0x006e110e: "invalid DIB in CopyFromDIB
0x006e1398: invalid HDC in wxBitmap::Create()
0x006e13f4: "invalid bitmap size
0x006e145a: einvalid HDC in wxBitmap::CreateFromImage()
0x006e14c2: invalid image
0x006e1638: Invalid bitmap or bitmap region
0x006e19ca: "can't create mask from invalid or not monochrome bitmap
0x006e1a4a: "invalid bitmap in wxMask::Create
0x006e1bbe: "invalid bitmap in wxInvertMask
0x006e5c2e: wxActivateEvent
0x006e617c: "invalid parameter in wxMouseEvent::ButtonDClick
0x006e61ea: invalid parameter in wxMouseEvent::ButtonDown
0x006e6250: invalid parameter in wxMouseEvent::ButtonUp
0x006e62b0: invalid parameter in wxMouseEvent::Button
0x006eb7ae: dinvalid index
0x006ebd3a: invalid encoding value in wxCSConv ctor
0x006ec84c: invalid key type
0x006ecc70: invalid index in wxListBase::Item
0x006ef720: 1wxClassInfo::Register() reentrance
0x006efcca: "wxString::Replace(): invalid parameter
0x006efd40: )invalid base
0x006fbdee: invalid value of thread priority parameter
0x006fe44a: invalid directory component in wxFileName
0x006fe904: )invalid wxDateTime
0x006ffe16: invalid wxDateTime
0x0070075a: "Invalid month value
0x00700968: invalid month
0x00700b3c: invalid weekday
0x00700dfc: Invalid time in wxDateTime::Set()
0x00700eaa: Invalid date in wxDateTime::Set()
0x00700f56: )invalid wxDateTime
0x00700fde: )invalid day
0x007010e0: invalid week number: weeks are counted from 1
0x007011d6: "invalid year day
0x007012aa: invalid date range in GetHolidaysInRange
0x00701354: yinvalid week day
0x007015d6: )invalid broken down date/time
0x007019f6: "invalid id range
0x00701c94: invalid 'client' parameter
0x00702444: "invalid seek mode
0x007047e8: "invalid icon
0x00704826: "invalid index
0x00704b44: invalid image list
0x00704c14: invalid wxDC in wxImageList::Draw
0x00706916: "invalid font
0x007071b6: yinvalid font
0x00707bd8: Registering module %s
0x0070a390: invalid value for wxBoxSizer orientation
0x00717c18: "Invalid page
0x0071fb60: "invalid OS id
0x0071fc50: invalid port id
0x0071fd9a: invalid enum value
0x00720174: "wxSetFocusToChild(): invalid window
0x0072125e: "invalid stock item ID
0x0072256e: "invalid pointer in wxBMPHandler::SaveFile
0x00724d30: Invalid key string "%s"
0x00725364: invalid brush
0x00725784: invalid cursor hot spot coordinates
0x00725b6c: "invalid cursor id in wxCursor() ctor
0x00726164: "invalid pen
0x00726382: )An invalid index was passed to wxDisplay
0x007263ee: invalid window
0x00726422: invalid wxDisplay object
0x00727a0c: Can't set layout direction for invalid window
0x00727a68: Invalid layout direction
0x00727ab4: invalid window
0x0072840c: invalid parameter
0x00728484: "invalid length
0x0072d770: "Invalid BOM type
0x0072e142: "invalid field number in SetFieldsCount
0x0072fad2: "invalid position in wxToolBar::InsertTool()
0x0072fc72: invalid position in wxToolBar::DeleteToolByPos()
0x0072fd4e: invalid tool in wxToolBarTool::UnToggleRadioGroup
0x0072fe84: uinvalid tool toolid
0x00730c22: invalid tool button bitmap
0x00731780: hinvalid image depth in wxDIB::Create()
0x00731876: wxDIB::Create(): invalid bitmap
0x007319a4: "wxDIB::Save(): invalid object
0x00731a66: BwxDIB::CreateDDB(): invalid object
0x00731ac6: invalid DIB in ConvertToBitmap
0x00731b3a: invalid bmp can't be converted to DIB
0x00731c24: ewxDIB::CreatePalette(): invalid object
0x00731cc8: invalid wxImage in wxDIB ctor
0x00731d84: "can't convert invalid DIB to wxImage
0x00732848: invalid window dc
0x007328ac: invalid blit size
0x00735318: invalid value for wxBoxSizer orientation
0x0073a17a: "invalid page index in wxBookCtrlBase::InsertPage()
0x0073a206: "invalid page index in wxBookCtrlBase::DoRemovePage()
0x0073a294: "invalid page index in wxBookCtrlBase::DoSetSelection()
0x0073b0f0: "Registering already registered hook?
0x0073b148: Unregistering not registered hook?
0x0073dc58: "invalid radiobox index
0x0073dcb4: invalid item in wxRadioBox::Enable()
0x0073dd0a: dinvalid item in wxRadioBox::IsItemEnabled()
0x0073dd6a: invalid item in wxRadioBox::Show()
0x0073ddba: ninvalid item in wxRadioBox::IsItemShown()
0x0073ed32: "Can't set layout direction for invalid window
0x0073eda2: Invalid layout direction
0x0073fb34: RegisterDragDrop
0x0073fc2a: invalid value in ConvertDragEffectToResult
0x0073fc98: tinvalid value in ConvertDragResultToEffect
0x0073ff36: invalid width value
0x00740cfe: "invalid window in wxWindowDCImpl
0x00741190: invalid window in wxClientDCImpl
0x00741e64: invalid wxRegion
0x00741fd4: "invalid wxRegionIterator
0x0074289a: invalid clipping region
0x00742ad0: "invalid icon in DrawIcon
0x00742b1c: "invalid bitmap in wxMSWDCImpl::DrawBitmap
0x00742dd4: )invalid font in wxMSWDCImpl::GetTextExtent
0x00743330: AlphaBlt(): invalid bitmap
0x00743374: cAlphaBlt(): invalid HDC
0x007457d4: Invalid arg for accNavigate
0x00745970: Invalid argument passed to wxAccessible::Navigate
0x00745c4a: Invalid arg for get_accChild
0x00745ca4: Invalid argument passed to GetChild
0x00745fea: Invalid arg for accDoDefaultAction
0x00746070: Invalid arg for get_accDefaultAction
0x007460f4: Invalid arg for get_accDescription
0x0074615e: Invalid arg for get_accHelp
0x007461ca: Invalid arg for get_accHelpTopic
0x00746254: Invalid arg for get_accKeyboardShortcut
0x007462c8: Invalid arg for get_accName
0x00746324: Invalid arg for get_accRole
0x00746384: Invalid arg for get_accState
0x007463e6: Invalid arg for get_accValue
0x00746446: Invalid arg for accSelect
0x00747ec4: Invalid
0x007481dc: Invalid type for == operator
0x0074823c: )Invalid index to Delete
0x0074828c: "Invalid type for array operator
0x007482f0: )Invalid index for array
0x0074832c: Invalid type for GetCount()
0x00749ab0: invalid bitmap data
0x0074a080: "invalid bitmap
0x0074a668: Couldn't load resource bitmap "%s" as a PNG. Have you registered PNG image handler?
0x0074b866: RegisterClass() in wxCreateHiddenWindow
0x0074c224: data is invalid
0x00755940: invalid key in wxRegKey::GetStdKeyName
0x00755a6c: invalid key prefix in wxRegKey::ExtractKeyName.
0x0075765c: UnregisterClass("wxTimerHiddenWindow")
0x00758238: "invalid status bar field index
0x0075ad70: Invalid wxColour -> wxString conversion flags
0x0075d1d4: invalid index in wxListBox::EnsureVisible
0x0075d242: "invalid index in wxListBox::SetFirstItem
0x0075d2a4: minvalid index in wxListBox::Delete
0x0075d316: invalid index in wxListBox::SetSelection
0x0075d374: invalid index in wxListBox::Selected
0x0075d610: ginvalid index in wxListBox::GetString
0x0075d666: ginvalid index in wxListBox::SetString
0x0075d6be: tinvalid index in wxListBox::GetItemRect
0x0075e58c: "invalid string in wxListBox::SetFirstItem
0x0075f05e: invalid item index in wxChoice::Delete
0x0075f0b4: ginvalid item index in wxChoice::SetString
0x0075f120: "Invalid index
0x007616de: "event.GetInt() returned an invalid checkbox state
0x00763040: Can't set layout direction for invalid window
0x0076309c: Invalid layout direction
0x00766236: Short option contains invalid characters
0x007662c6: Long option contains invalid characters
0x0076669c: invalid param index
0x007684a8: invalid value for wxBoxSizer orientation
0x0076c09e: corrupted config data: invalid encoding %ld for charset '%s' ignored
0x0076c30c: wxFontMapper::GetEncoding(): invalid index
0x00770062: "Invalid platform specified
0x00772510: invalid parameter in GetMimeType
0x00772566: "invalid parameter in GetDescription
0x007725ca: invalid parameter in GetOpenCommand
0x0077262e: invalid parameter in GetPrintCommand
0x007853b4: invalid format character
0x0078542c: invalid wxDateTime
0x0078546a: )invalid broken down date/time
0x00785bbc: "invalid absolute file offset
0x007863bc: "Invalid argument
0x0078640a: 2Invalid argument(s)
0x007eda48: "invalid animation
0x007ee000: invalid animation
0x007ef792: Invalid cell.
0x007ef82e: "Invalid Add form called.
0x007efcaa: invalid value for wxBoxSizer orientation
0x007f0670: "Invalid window variant
0x007f0aa0: MInvalid banner direction
0x007f21c4: Invalid wxBitmapComboBox state
0x007f22e8: iInvalid index for wxBitmapComboBox item
0x007f40a2: rUnregisterClass
0x007f40de: 1calling ClassRegistrar::Register() twice?
0x007f4134: RegisterClassEx()
0x007f6e46: invalid value for wxBoxSizer orientation
0x007ff2c4: Invalid selection flag
0x00802364: Removing non-registered notifier
0x00802508: "invalid index
0x0080546a: )invalid wxDateTime
0x00806c1c: invalid wxDateTime
0x0080c720: invalid filedata
0x0080d750: invalid wxDateTime
0x00810b6c: "invalid row or column index in wxGridStringTable
0x00810c44: Pos value is invalid for present table with %lu rows
0x00810d18: Pos value is invalid for present table with %lu cols
0x0081207e: "invalid row index
0x008120ce: invalid column index
0x0081242c: "invalid cell coords
0x00819eda: invalid index in wxSimpleHtmlListBox::SetString
0x00819f42: ginvalid index in wxSimpleHtmlListBox::GetString
0x0081a5a8: "invalid index
0x0081d9b0: invalid item index in SetItem
0x0081da50: "invalid list control item index in GetItemState()
0x0081dbc2: "invalid sub item index
0x0081dbf2: invalid item in GetSubItemRect
0x0081e0ca: Ignoring invalid search start position %d in list control with %d items.
0x0081e18a: invalid column index array in OnPaint()
0x00820700: invalid orientation value
0x008211c4: can't deactivate MDI child which wasn't active!
0x00822d6e: "invalid index in wxNotebook::InsertPage
0x00822efe: rUnregisterClass
0x00822f3a: 1calling ClassRegistrar::Register() twice?
0x00822f90: RegisterClassEx()
0x0082420a: "invalid index in wxVListBoxComboPopup::SetSelection
0x00824ebe: invalid index in wxOwnerDrawnComboBox::Delete
0x00824f22: ginvalid index in wxOwnerDrawnComboBox::GetString
0x00824f8e: ginvalid index in wxOwnerDrawnComboBox::SetString
0x00825016: invalid index in wxOwnerDrawnComboBox::Select
0x008256ae: Invalid item index
0x00829b28: "invalid call to wxSpinCtrl::SetValue
0x00829b8a: ninvalid call to wxSpinCtrl::SetSelection
0x0082c7da: "invalid gravity value
0x0082fdb4: "Invalid treebook page position
0x0082ff20: "invalid tree item
0x0082fffc: "Invalid tree index
0x008300f8: Invalid index passed to wxTreebook::DoInternalAddPage
0x008301a2: )Invalid page index
0x00830200: "Invalid page index spacified!
0x00832a96: invalid tree item
0x00832b18: invalid image index
0x00835efc: invalid WX_DDE command in wxExecute
0x00836030: invalid value of thread priority parameter
0x008366fc: UnregisterClass(wxExecClass)
0x0083fe22: invalid base
0x008423fc: UnregisterClass(wxDisplayHiddenWindow)
0x008424d8: "An invalid index was passed to wxDisplay
0x0084cf8e: "Attaching array of invalid type
0x0084f7f2: "Invalid bitmap
0x0084f87a: "Invalid bitmap region
0x008509ec: "can't play invalid enhanced metafile
0x00850a3e: invalid wxDC in wxEnhMetaFile::Play
0x00850aee: can't copy invalid metafile to clipboard
0x00850f38: einvalid wxEnhMetaFileDC
0x008510c4: "copying invalid enh metafile
0x0085119a: pasting invalid enh metafile
0x00873354: Invalid process priority value.
0x00878354: wxFSIconType::GetIcon(): invalid icon index
0x00878618: invalid backing store
0x00882cee: einvalid progress value
0x0088327a: Invalid wxGridCellFloatRenderer width parameter string '%s ignored
0x00883300: Invalid wxGridCellFloatRenderer precision parameter string '%s ignored
0x00883c7a: Invalid wxGridCellTextEditor parameter string '%s' ignored
0x00883d92: dInvalid wxGridCellNumberEditor parameter string '%s' ignored
0x00883e62: Invalid wxGridCellFloatRenderer width parameter string '%s ignored
0x00883ee8: Invalid wxGridCellFloatRenderer precision parameter string '%s ignored
0x00884024: dinvalid value for a cell with bool editor!
0x0088a8b0: "Select(): invalid item index
0x0088a982: "SelectRange(): invalid item index
0x0088aa96: wxVListBox::DoSetCurrent(): invalid item index
0x0088abc4: wxVListBox::SetSelection(): invalid item index
0x0088da2c: wxDDEModule
0x00898abc: "Invalid wxDC
0x0089b622: "invalid bitmap in wxPrinterDC::DrawBitmap
0x0089c8b0: Invalid dimension index
0x0089f538: "invalid colour argument
0x0089f850: invalid seek mode
0x008cc5ba: CR_INVALID_POINTER
0x008cc5e0: CR_INVALID_FLAG
0x008cc600: CR_INVALID_DEVNODE or CR_INVALID_DEVINST
0x008cc652: CR_INVALID_RES_DES
0x008cc678: CR_INVALID_LOG_CONF
0x008cc6a0: CR_INVALID_ARBITRATOR
0x008cc6cc: CR_INVALID_NODELIST
0x008cc74a: CR_INVALID_RESOURCEID
0x008cc8a2: CR_INVALID_RANGE_LIST
0x008cc8ce: CR_INVALID_RANGE
0x008cc9b8: CR_INVALID_LOAD_TYPE
0x008cca74: CR_INVALID_DEVICE_ID
0x008cca9e: CR_INVALID_DATA
0x008ccabe: CR_INVALID_API
0x008ccbbe: CR_INVALID_PRIORITY
0x008ccce8: CR_INVALID_MACHINENAME
0x008ccde8: CR_INVALID_PROPERTY
0x008cce7e: CR_INVALID_REFERENCE_STRING
0x008cceb6: CR_INVALID_CONFLICT_LIST
0x008ccee8: CR_INVALID_INDEX
0x008ccf0a: CR_INVALID_STRUCTURE_SIZE
