version: '3.8'

# 开发环境配置
# 使用方法: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  # 从服务器开发配置
  slave-server:
    # 开发环境变量
    environment:
      - FLASK_ENV=development
      - LOG_LEVEL=DEBUG
      - ENABLE_PERFORMANCE_MONITOR=true
      - PYTHONUNBUFFERED=1
    
    # 开发端口映射
    ports:
      - "8889:8889"
      - "7575:7575"
      - "5678:5678"    # 调试端口
    
    # 开发数据卷（代码热重载）
    volumes:
      - .:/app:rw
      - slave_dev_data:/app/data
      - slave_dev_logs:/app/logs
      - /dev/bus/usb:/dev/bus/usb:rw
    
    # 开发启动命令
    command: >
      sh -c "
        echo 'Starting in development mode...' &&
        python -m pip install --no-cache-dir debugpy &&
        python -m debugpy --listen 0.0.0.0:5678 --wait-for-client main.py
      "
    
    # 开发健康检查（更宽松）
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8889/api/system/health"]
      interval: 60s
      timeout: 15s
      retries: 5
      start_period: 60s
    
    # 开发资源限制（更宽松）
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 256M

  # Redis开发配置
  redis:
    # 开发端口映射（暴露到主机）
    ports:
      - "6379:6379"
    
    # 开发配置（更详细的日志）
    command: redis-server --appendonly yes --maxmemory 128mb --maxmemory-policy allkeys-lru --loglevel verbose

  # 监控服务开发配置
  monitoring:
    # 开发配置文件
    volumes:
      - ./monitoring/prometheus.dev.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_dev_data:/prometheus
    
    # 开发启动参数（更短的保留时间）
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=3d'
      - '--web.enable-lifecycle'
      - '--log.level=debug'

  # Grafana开发配置
  grafana:
    # 开发环境变量
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=true
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      - GF_LOG_LEVEL=debug
      - GF_FEATURE_TOGGLES_ENABLE=ngalert
    
    # 开发数据卷
    volumes:
      - grafana_dev_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:rw
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:rw

  # 开发工具服务
  dev-tools:
    image: python:3.11-slim
    container_name: omnilink-dev-tools
    hostname: dev-tools
    restart: "no"
    
    # 工作目录
    working_dir: /app
    
    # 数据卷
    volumes:
      - .:/app:rw
    
    # 开发工具命令
    command: >
      sh -c "
        echo 'Installing development tools...' &&
        pip install --no-cache-dir pytest pytest-cov black flake8 mypy &&
        echo 'Development tools ready. Use: docker-compose exec dev-tools bash' &&
        tail -f /dev/null
      "
    
    # 环境变量
    environment:
      - PYTHONPATH=/app
    
    # 标签
    labels:
      - "com.omnilink.service=dev-tools"
      - "com.omnilink.description=开发工具容器"

  # 数据库管理工具
  adminer:
    image: adminer:latest
    container_name: omnilink-adminer
    hostname: adminer
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "8080:8080"
    
    # 环境变量
    environment:
      - ADMINER_DEFAULT_SERVER=slave-server
    
    # 依赖服务
    depends_on:
      - slave-server
    
    # 标签
    labels:
      - "com.omnilink.service=adminer"
      - "com.omnilink.description=数据库管理工具"

# 开发数据卷
volumes:
  slave_dev_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./dev-data
  
  slave_dev_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./dev-logs
  
  prometheus_dev_data:
    driver: local
  
  grafana_dev_data:
    driver: local
