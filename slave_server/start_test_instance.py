#!/usr/bin/env python3
"""
从服务器测试实例启动脚本
用于多实例测试
"""

import sys
import os
import asyncio
import signal
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from main_minimal import OmniLinkSlaveServer

def main():
    if len(sys.argv) != 2:
        print("用法: python start_test_instance.py <config_file>")
        print("示例: python start_test_instance.py config_test_1.ini")
        sys.exit(1)
    
    config_file = sys.argv[1]
    if not os.path.exists(config_file):
        print(f"配置文件不存在: {config_file}")
        sys.exit(1)
    
    print(f"启动从服务器测试实例，配置文件: {config_file}")
    
    # 创建服务器实例
    server = OmniLinkSlaveServer()
    
    # 加载指定配置文件
    server._load_config_from_file(config_file)
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在关闭服务器...")
        asyncio.create_task(server.shutdown())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动服务器
    try:
        asyncio.run(server.run())
    except KeyboardInterrupt:
        print("服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
