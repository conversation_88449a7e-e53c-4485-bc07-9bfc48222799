[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py *_test.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 最小版本要求
minversion = 6.0

# 添加选项
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=.
    --cov-report=html
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --durations=10

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    api: API测试
    slow: 慢速测试
    smoke: 冒烟测试
    regression: 回归测试

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 测试发现
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    node_modules

# 覆盖率配置
[coverage:run]
source = .
omit = 
    tests/*
    venv/*
    .venv/*
    */venv/*
    */.venv/*
    setup.py
    */migrations/*
    */static/*
    */templates/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    if TYPE_CHECKING:

[coverage:html]
directory = htmlcov
