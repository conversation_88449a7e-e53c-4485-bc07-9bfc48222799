#!/usr/bin/env python3
"""
硬件指纹生成模块
基于硬件特征生成稳定的唯一标识符
支持Windows、Linux、macOS跨平台
"""

import hashlib
import platform
import subprocess
import logging
import uuid
from typing import Optional, Dict, Any
import json

logger = logging.getLogger(__name__)

class HardwareFingerprint:
    """硬件指纹生成器"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self._cache = {}
        
    def generate_uuid(self) -> str:
        """生成基于硬件特征的UUID"""
        try:
            # 收集硬件信息
            hardware_info = self._collect_hardware_info()
            
            # 生成指纹字符串
            fingerprint_data = self._create_fingerprint_string(hardware_info)
            
            # 生成UUID
            hardware_uuid = self._generate_uuid_from_fingerprint(fingerprint_data)
            
            logger.info(f"硬件UUID生成成功: {hardware_uuid}")
            return hardware_uuid
            
        except Exception as e:
            logger.error(f"生成硬件UUID失败: {e}")
            # 降级方案：使用MAC地址生成UUID
            return self._generate_fallback_uuid()
    
    def _collect_hardware_info(self) -> Dict[str, Any]:
        """收集硬件信息"""
        info = {
            'cpu_info': self._get_cpu_info(),
            'motherboard_info': self._get_motherboard_info(),
            'mac_address': self._get_primary_mac_address(),
            'system_info': {
                'platform': platform.platform(),
                'machine': platform.machine(),
                'processor': platform.processor()
            }
        }
        
        # 过滤空值
        return {k: v for k, v in info.items() if v}
    
    def _get_cpu_info(self) -> Optional[str]:
        """获取CPU信息"""
        try:
            if self.system == 'windows':
                return self._get_cpu_info_windows()
            elif self.system == 'linux':
                return self._get_cpu_info_linux()
            elif self.system == 'darwin':  # macOS
                return self._get_cpu_info_macos()
            else:
                return None
        except Exception as e:
            logger.debug(f"获取CPU信息失败: {e}")
            return None
    
    def _get_cpu_info_windows(self) -> Optional[str]:
        """Windows CPU信息"""
        try:
            cmd = ['wmic', 'cpu', 'get', 'ProcessorId', '/value']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'ProcessorId=' in line:
                        cpu_id = line.split('=')[1].strip()
                        if cpu_id:
                            return cpu_id
            
            # 备选方案：使用PowerShell
            cmd = ['powershell', '-Command', 
                   'Get-WmiObject -Class Win32_Processor | Select-Object -ExpandProperty ProcessorId']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                return result.stdout.strip()
                
        except Exception as e:
            logger.debug(f"Windows CPU信息获取失败: {e}")
        
        return None
    
    def _get_cpu_info_linux(self) -> Optional[str]:
        """Linux CPU信息"""
        try:
            # 尝试从/proc/cpuinfo获取
            with open('/proc/cpuinfo', 'r') as f:
                for line in f:
                    if 'serial' in line.lower():
                        return line.split(':')[1].strip()
            
            # 备选方案：使用dmidecode
            cmd = ['dmidecode', '-t', 'processor', '-s', 'processor-serial-number']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                serial = result.stdout.strip()
                if serial and serial != 'Not Specified':
                    return serial
                    
        except Exception as e:
            logger.debug(f"Linux CPU信息获取失败: {e}")
        
        return None
    
    def _get_cpu_info_macos(self) -> Optional[str]:
        """macOS CPU信息"""
        try:
            cmd = ['system_profiler', 'SPHardwareDataType']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'Serial Number' in line:
                        return line.split(':')[1].strip()
                        
        except Exception as e:
            logger.debug(f"macOS CPU信息获取失败: {e}")
        
        return None
    
    def _get_motherboard_info(self) -> Optional[str]:
        """获取主板信息"""
        try:
            if self.system == 'windows':
                return self._get_motherboard_info_windows()
            elif self.system == 'linux':
                return self._get_motherboard_info_linux()
            elif self.system == 'darwin':
                return self._get_motherboard_info_macos()
            else:
                return None
        except Exception as e:
            logger.debug(f"获取主板信息失败: {e}")
            return None
    
    def _get_motherboard_info_windows(self) -> Optional[str]:
        """Windows主板信息"""
        try:
            cmd = ['wmic', 'baseboard', 'get', 'SerialNumber', '/value']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        serial = line.split('=')[1].strip()
                        if serial:
                            return serial
                            
        except Exception as e:
            logger.debug(f"Windows主板信息获取失败: {e}")
        
        return None
    
    def _get_motherboard_info_linux(self) -> Optional[str]:
        """Linux主板信息"""
        try:
            cmd = ['dmidecode', '-t', 'baseboard', '-s', 'baseboard-serial-number']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                serial = result.stdout.strip()
                if serial and serial != 'Not Specified':
                    return serial
                    
        except Exception as e:
            logger.debug(f"Linux主板信息获取失败: {e}")
        
        return None
    
    def _get_motherboard_info_macos(self) -> Optional[str]:
        """macOS主板信息"""
        try:
            cmd = ['system_profiler', 'SPHardwareDataType']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'Hardware UUID' in line:
                        return line.split(':')[1].strip()
                        
        except Exception as e:
            logger.debug(f"macOS主板信息获取失败: {e}")
        
        return None
    
    def _get_primary_mac_address(self) -> Optional[str]:
        """获取主网卡MAC地址"""
        try:
            import psutil
            
            # 获取所有网络接口
            interfaces = psutil.net_if_addrs()
            
            # 优先选择有效的以太网接口
            for interface_name, addresses in interfaces.items():
                # 跳过回环接口
                if 'loopback' in interface_name.lower() or 'lo' == interface_name.lower():
                    continue
                
                for addr in addresses:
                    if addr.family == psutil.AF_LINK:  # MAC地址
                        mac = addr.address
                        if mac and mac != '00:00:00:00:00:00':
                            return mac.replace(':', '').replace('-', '').upper()
            
            # 降级方案：使用uuid.getnode()
            mac = uuid.getnode()
            return f"{mac:012x}".upper()
            
        except ImportError:
            # psutil不可用时的降级方案
            try:
                mac = uuid.getnode()
                return f"{mac:012x}".upper()
            except Exception as e:
                logger.debug(f"获取MAC地址失败: {e}")
                return None
        except Exception as e:
            logger.debug(f"获取MAC地址失败: {e}")
            return None
    
    def _create_fingerprint_string(self, hardware_info: Dict[str, Any]) -> str:
        """创建指纹字符串"""
        # 按优先级排序硬件信息
        priority_keys = ['cpu_info', 'motherboard_info', 'mac_address']
        
        fingerprint_parts = []
        
        for key in priority_keys:
            if key in hardware_info and hardware_info[key]:
                fingerprint_parts.append(f"{key}:{hardware_info[key]}")
        
        # 添加系统信息作为补充
        if 'system_info' in hardware_info:
            system_info = hardware_info['system_info']
            fingerprint_parts.append(f"platform:{system_info.get('platform', '')}")
            fingerprint_parts.append(f"machine:{system_info.get('machine', '')}")
        
        fingerprint_string = "|".join(fingerprint_parts)
        logger.debug(f"硬件指纹字符串: {fingerprint_string}")
        
        return fingerprint_string
    
    def _generate_uuid_from_fingerprint(self, fingerprint_data: str) -> str:
        """从指纹数据生成UUID"""
        # 使用SHA256生成稳定的哈希
        hash_object = hashlib.sha256(fingerprint_data.encode('utf-8'))
        hash_hex = hash_object.hexdigest()
        
        # 将哈希转换为UUID格式
        uuid_string = f"{hash_hex[:8]}-{hash_hex[8:12]}-{hash_hex[12:16]}-{hash_hex[16:20]}-{hash_hex[20:32]}"
        
        return uuid_string
    
    def _generate_fallback_uuid(self) -> str:
        """生成降级UUID"""
        try:
            # 使用MAC地址生成UUID
            mac = self._get_primary_mac_address()
            if mac:
                # 基于MAC地址生成稳定的UUID
                namespace = uuid.UUID('6ba7b810-9dad-11d1-80b4-00c04fd430c8')  # DNS namespace
                return str(uuid.uuid5(namespace, mac))
            else:
                # 最后的降级方案：生成随机UUID
                return str(uuid.uuid4())
                
        except Exception as e:
            logger.error(f"生成降级UUID失败: {e}")
            return str(uuid.uuid4())
    
    def get_hardware_info_summary(self) -> Dict[str, Any]:
        """获取硬件信息摘要（用于调试）"""
        try:
            return self._collect_hardware_info()
        except Exception as e:
            logger.error(f"获取硬件信息摘要失败: {e}")
            return {}

# 全局实例
hardware_fingerprint = HardwareFingerprint()

def generate_hardware_uuid() -> str:
    """生成硬件UUID的便捷函数"""
    return hardware_fingerprint.generate_uuid()

def get_hardware_summary() -> Dict[str, Any]:
    """获取硬件信息摘要的便捷函数"""
    return hardware_fingerprint.get_hardware_info_summary()
