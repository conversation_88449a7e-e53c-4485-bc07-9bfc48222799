#!/usr/bin/env python3
"""
端口位置码数据库管理模块
使用SQLite数据库确保数据持久化，防止断电重启数据丢失
支持端口绑定、设备配置、历史记录等数据的完整管理
"""

import sqlite3
import logging
import os
import threading
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from .config_manager import SlaveConfigManager

logger = logging.getLogger(__name__)

@dataclass
class PortLocationRecord:
    """端口位置记录"""
    location_code: str
    server_uuid: str
    controller_id: str
    hub_id: str
    port_number: int
    hub_path: str = ""
    created_at: str = ""
    last_seen: str = ""
    is_active: bool = True

@dataclass
class DeviceBindingRecord:
    """设备绑定记录"""
    binding_id: int
    location_code: str
    device_signature: str
    custom_name: str = ""
    notes: str = ""
    config_data: str = ""  # JSON字符串
    binding_time: str = ""
    last_verified: str = ""
    is_bound: bool = True
    binding_confidence: float = 1.0

@dataclass
class DeviceHistoryRecord:
    """设备历史记录"""
    history_id: int
    device_signature: str
    location_code: str
    action: str  # BIND, UNBIND, MOVE, UPDATE
    timestamp: str
    details: str = ""  # JSON字符串

class PortLocationDatabase:
    """端口位置码数据库管理器"""
    
    def __init__(self, server_uuid: str):
        self.server_uuid = server_uuid
        self.config_manager = SlaveConfigManager()
        
        # 数据库文件路径
        self.db_path = os.path.join(
            self.config_manager.config_dir,
            "port_locations.db"
        )
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 初始化数据库
        self._initialize_database()
        
        logger.info(f"端口位置码数据库初始化完成: {self.db_path}")
    
    def _initialize_database(self):
        """初始化数据库表结构"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 创建端口位置表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS port_locations (
                        location_code TEXT PRIMARY KEY,
                        server_uuid TEXT NOT NULL,
                        controller_id TEXT NOT NULL,
                        hub_id TEXT NOT NULL,
                        port_number INTEGER NOT NULL,
                        hub_path TEXT DEFAULT '',
                        created_at TEXT NOT NULL,
                        last_seen TEXT NOT NULL,
                        is_active BOOLEAN DEFAULT 1
                    )
                ''')
                
                # 创建设备绑定表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS device_bindings (
                        binding_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        location_code TEXT NOT NULL,
                        device_signature TEXT NOT NULL,
                        custom_name TEXT DEFAULT '',
                        notes TEXT DEFAULT '',
                        config_data TEXT DEFAULT '{}',
                        binding_time TEXT NOT NULL,
                        last_verified TEXT NOT NULL,
                        is_bound BOOLEAN DEFAULT 1,
                        binding_confidence REAL DEFAULT 1.0,
                        FOREIGN KEY (location_code) REFERENCES port_locations (location_code),
                        UNIQUE(location_code, device_signature)
                    )
                ''')
                
                # 创建设备历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS device_history (
                        history_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        device_signature TEXT NOT NULL,
                        location_code TEXT NOT NULL,
                        action TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        details TEXT DEFAULT '{}'
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_port_server ON port_locations(server_uuid)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_binding_location ON device_bindings(location_code)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_binding_device ON device_bindings(device_signature)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_history_device ON device_history(device_signature)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_history_timestamp ON device_history(timestamp)')
                
                conn.commit()
                conn.close()
                
                logger.info("数据库表结构初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def insert_port_location(self, location_code: str, controller_id: str, hub_id: str,
                           port_number: int, hub_path: str = "") -> bool:
        """插入端口位置记录"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO port_locations 
                    (location_code, server_uuid, controller_id, hub_id, port_number, 
                     hub_path, created_at, last_seen, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
                ''', (location_code, self.server_uuid, controller_id, hub_id, 
                      port_number, hub_path, now, now))
                
                conn.commit()
                conn.close()
                
                logger.debug(f"端口位置记录已插入: {location_code}")
                return True
                
        except Exception as e:
            logger.error(f"插入端口位置记录失败: {e}")
            return False
    
    def get_port_location(self, location_code: str) -> Optional[PortLocationRecord]:
        """获取端口位置记录"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM port_locations WHERE location_code = ? AND is_active = 1
                ''', (location_code,))
                
                row = cursor.fetchone()
                conn.close()
                
                if row:
                    return PortLocationRecord(
                        location_code=row[0],
                        server_uuid=row[1],
                        controller_id=row[2],
                        hub_id=row[3],
                        port_number=row[4],
                        hub_path=row[5],
                        created_at=row[6],
                        last_seen=row[7],
                        is_active=bool(row[8])
                    )
                return None
                
        except Exception as e:
            logger.error(f"获取端口位置记录失败: {e}")
            return None
    
    def bind_device_to_port(self, location_code: str, device_signature: str,
                          custom_name: str = "", notes: str = "", 
                          config_data: Dict[str, Any] = None) -> bool:
        """绑定设备到端口"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                config_json = json.dumps(config_data or {})
                
                # 检查端口是否已被其他设备绑定
                cursor.execute('''
                    SELECT device_signature FROM device_bindings 
                    WHERE location_code = ? AND is_bound = 1
                ''', (location_code,))
                
                existing = cursor.fetchone()
                if existing and existing[0] != device_signature:
                    logger.warning(f"端口已被其他设备绑定: {location_code} -> {existing[0]}")
                    conn.close()
                    return False
                
                # 插入或更新绑定记录
                cursor.execute('''
                    INSERT OR REPLACE INTO device_bindings
                    (location_code, device_signature, custom_name, notes, config_data,
                     binding_time, last_verified, is_bound, binding_confidence)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1, 1.0)
                ''', (location_code, device_signature, custom_name, notes, 
                      config_json, now, now))
                
                # 记录历史
                self._add_history_record(cursor, device_signature, location_code, 
                                       "BIND", now, {"custom_name": custom_name, "notes": notes})
                
                conn.commit()
                conn.close()
                
                logger.info(f"设备绑定成功: {device_signature} -> {location_code}")
                return True
                
        except Exception as e:
            logger.error(f"设备绑定失败: {e}")
            return False
    
    def unbind_device_from_port(self, device_signature: str) -> bool:
        """解除设备与端口的绑定"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 获取当前绑定信息
                cursor.execute('''
                    SELECT location_code FROM device_bindings 
                    WHERE device_signature = ? AND is_bound = 1
                ''', (device_signature,))
                
                row = cursor.fetchone()
                if not row:
                    logger.debug(f"设备未绑定: {device_signature}")
                    conn.close()
                    return True
                
                location_code = row[0]
                now = datetime.now().isoformat()
                
                # 更新绑定状态
                cursor.execute('''
                    UPDATE device_bindings SET is_bound = 0, last_verified = ?
                    WHERE device_signature = ? AND is_bound = 1
                ''', (now, device_signature))
                
                # 记录历史
                self._add_history_record(cursor, device_signature, location_code, 
                                       "UNBIND", now, {})
                
                conn.commit()
                conn.close()
                
                logger.info(f"设备解绑成功: {device_signature} 从 {location_code}")
                return True
                
        except Exception as e:
            logger.error(f"设备解绑失败: {e}")
            return False
    
    def get_device_binding(self, device_signature: str) -> Optional[DeviceBindingRecord]:
        """获取设备绑定信息"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM device_bindings 
                    WHERE device_signature = ? AND is_bound = 1
                ''', (device_signature,))
                
                row = cursor.fetchone()
                conn.close()
                
                if row:
                    return DeviceBindingRecord(
                        binding_id=row[0],
                        location_code=row[1],
                        device_signature=row[2],
                        custom_name=row[3],
                        notes=row[4],
                        config_data=row[5],
                        binding_time=row[6],
                        last_verified=row[7],
                        is_bound=bool(row[8]),
                        binding_confidence=row[9]
                    )
                return None
                
        except Exception as e:
            logger.error(f"获取设备绑定信息失败: {e}")
            return None
    
    def get_port_binding(self, location_code: str) -> Optional[DeviceBindingRecord]:
        """获取端口绑定信息"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM device_bindings 
                    WHERE location_code = ? AND is_bound = 1
                ''', (location_code,))
                
                row = cursor.fetchone()
                conn.close()
                
                if row:
                    return DeviceBindingRecord(
                        binding_id=row[0],
                        location_code=row[1],
                        device_signature=row[2],
                        custom_name=row[3],
                        notes=row[4],
                        config_data=row[5],
                        binding_time=row[6],
                        last_verified=row[7],
                        is_bound=bool(row[8]),
                        binding_confidence=row[9]
                    )
                return None
                
        except Exception as e:
            logger.error(f"获取端口绑定信息失败: {e}")
            return None
    
    def get_all_port_locations(self) -> List[PortLocationRecord]:
        """获取所有端口位置记录"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM port_locations 
                    WHERE server_uuid = ? AND is_active = 1
                    ORDER BY controller_id, hub_id, port_number
                ''', (self.server_uuid,))
                
                rows = cursor.fetchall()
                conn.close()
                
                records = []
                for row in rows:
                    records.append(PortLocationRecord(
                        location_code=row[0],
                        server_uuid=row[1],
                        controller_id=row[2],
                        hub_id=row[3],
                        port_number=row[4],
                        hub_path=row[5],
                        created_at=row[6],
                        last_seen=row[7],
                        is_active=bool(row[8])
                    ))
                
                return records
                
        except Exception as e:
            logger.error(f"获取所有端口位置记录失败: {e}")
            return []
    
    def get_all_device_bindings(self) -> List[DeviceBindingRecord]:
        """获取所有设备绑定记录"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM device_bindings 
                    WHERE is_bound = 1
                    ORDER BY binding_time DESC
                ''')
                
                rows = cursor.fetchall()
                conn.close()
                
                records = []
                for row in rows:
                    records.append(DeviceBindingRecord(
                        binding_id=row[0],
                        location_code=row[1],
                        device_signature=row[2],
                        custom_name=row[3],
                        notes=row[4],
                        config_data=row[5],
                        binding_time=row[6],
                        last_verified=row[7],
                        is_bound=bool(row[8]),
                        binding_confidence=row[9]
                    ))
                
                return records
                
        except Exception as e:
            logger.error(f"获取所有设备绑定记录失败: {e}")
            return []
    
    def _add_history_record(self, cursor, device_signature: str, location_code: str,
                          action: str, timestamp: str, details: Dict[str, Any]):
        """添加历史记录"""
        try:
            details_json = json.dumps(details)
            cursor.execute('''
                INSERT INTO device_history 
                (device_signature, location_code, action, timestamp, details)
                VALUES (?, ?, ?, ?, ?)
            ''', (device_signature, location_code, action, timestamp, details_json))
            
        except Exception as e:
            logger.error(f"添加历史记录失败: {e}")
    
    def cleanup_old_records(self, days: int = 30):
        """清理旧记录"""
        try:
            with self._lock:
                from datetime import timedelta
                cutoff_time = (datetime.now() - timedelta(days=days)).isoformat()
                
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 清理旧的历史记录
                cursor.execute('''
                    DELETE FROM device_history WHERE timestamp < ?
                ''', (cutoff_time,))
                
                # 清理未绑定的旧端口位置
                cursor.execute('''
                    DELETE FROM port_locations 
                    WHERE last_seen < ? AND location_code NOT IN (
                        SELECT DISTINCT location_code FROM device_bindings WHERE is_bound = 1
                    )
                ''', (cutoff_time,))
                
                conn.commit()
                conn.close()
                
                logger.info(f"清理 {days} 天前的旧记录完成")
                
        except Exception as e:
            logger.error(f"清理旧记录失败: {e}")

# 全局实例
port_database: Optional[PortLocationDatabase] = None

def initialize_port_database(server_uuid: str) -> PortLocationDatabase:
    """初始化端口位置码数据库"""
    global port_database
    port_database = PortLocationDatabase(server_uuid)
    return port_database

def get_port_database() -> Optional[PortLocationDatabase]:
    """获取端口位置码数据库实例"""
    return port_database
