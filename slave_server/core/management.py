#!/usr/bin/env python3
"""
从服务器管理功能模块
支持代码升级、配置推送、VirtualHere版本管理、远程重启
"""

import asyncio
import os
import shutil
import tempfile
import zipfile
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import hashlib
import subprocess
import json

logger = logging.getLogger(__name__)

class ManagementModule:
    """从服务器管理模块"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.app_dir = Path('/app')
        self.backup_dir = self.app_dir / 'backups'
        self.temp_dir = self.app_dir / 'temp'
        
        # 确保目录存在
        self.backup_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)
        
        # 依赖注入
        self.vh_manager = None
        self.server = None
    
    def set_dependencies(self, vh_manager, server):
        """设置依赖组件"""
        self.vh_manager = vh_manager
        self.server = server
    
    async def update_code(self, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新服务器代码"""
        try:
            update_type = update_data.get('type', 'file')
            
            if update_type == 'file':
                return await self._update_from_files(update_data)
            elif update_type == 'git':
                return await self._update_from_git(update_data)
            elif update_type == 'zip':
                return await self._update_from_zip(update_data)
            else:
                return {
                    'status': 'error',
                    'message': f'不支持的更新类型: {update_type}'
                }
                
        except Exception as e:
            logger.error(f"代码更新失败: {e}")
            return {
                'status': 'error',
                'message': f'代码更新失败: {str(e)}'
            }
    
    async def _update_from_files(self, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """从文件更新代码"""
        try:
            files = update_data.get('files', {})
            if not files:
                return {'status': 'error', 'message': '没有提供更新文件'}
            
            # 创建备份
            backup_id = await self._create_backup()
            
            updated_files = []
            for file_path, file_content in files.items():
                try:
                    # 安全检查：确保文件路径在应用目录内
                    full_path = self.app_dir / file_path
                    if not str(full_path).startswith(str(self.app_dir)):
                        logger.warning(f"拒绝更新路径外文件: {file_path}")
                        continue
                    
                    # 确保目录存在
                    full_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 写入文件
                    if isinstance(file_content, str):
                        with open(full_path, 'w', encoding='utf-8') as f:
                            f.write(file_content)
                    else:
                        with open(full_path, 'wb') as f:
                            f.write(file_content)
                    
                    updated_files.append(file_path)
                    logger.info(f"已更新文件: {file_path}")
                    
                except Exception as e:
                    logger.error(f"更新文件 {file_path} 失败: {e}")
            
            return {
                'status': 'success',
                'message': f'成功更新 {len(updated_files)} 个文件',
                'updated_files': updated_files,
                'backup_id': backup_id
            }
            
        except Exception as e:
            logger.error(f"文件更新失败: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def _update_from_git(self, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """从Git仓库更新代码"""
        try:
            repo_url = update_data.get('repo_url')
            branch = update_data.get('branch', 'main')
            
            if not repo_url:
                return {'status': 'error', 'message': '没有提供Git仓库URL'}
            
            # 创建备份
            backup_id = await self._create_backup()
            
            # 创建临时目录
            temp_repo_dir = self.temp_dir / f'git_update_{asyncio.get_event_loop().time()}'
            temp_repo_dir.mkdir(exist_ok=True)
            
            try:
                # 克隆仓库
                cmd = ['git', 'clone', '-b', branch, '--depth', '1', repo_url, str(temp_repo_dir)]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                
                if result.returncode != 0:
                    return {
                        'status': 'error',
                        'message': f'Git克隆失败: {result.stderr}'
                    }
                
                # 复制文件到应用目录
                updated_files = []
                for item in temp_repo_dir.rglob('*'):
                    if item.is_file() and not item.name.startswith('.git'):
                        rel_path = item.relative_to(temp_repo_dir)
                        dest_path = self.app_dir / rel_path
                        
                        # 确保目录存在
                        dest_path.parent.mkdir(parents=True, exist_ok=True)
                        
                        # 复制文件
                        shutil.copy2(item, dest_path)
                        updated_files.append(str(rel_path))
                
                return {
                    'status': 'success',
                    'message': f'从Git成功更新 {len(updated_files)} 个文件',
                    'updated_files': updated_files,
                    'backup_id': backup_id,
                    'repo_url': repo_url,
                    'branch': branch
                }
                
            finally:
                # 清理临时目录
                shutil.rmtree(temp_repo_dir, ignore_errors=True)
                
        except Exception as e:
            logger.error(f"Git更新失败: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def _update_from_zip(self, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """从ZIP文件更新代码"""
        try:
            zip_data = update_data.get('zip_data')
            if not zip_data:
                return {'status': 'error', 'message': '没有提供ZIP数据'}
            
            # 创建备份
            backup_id = await self._create_backup()
            
            # 创建临时ZIP文件
            temp_zip_path = self.temp_dir / f'update_{asyncio.get_event_loop().time()}.zip'
            
            try:
                # 写入ZIP数据
                with open(temp_zip_path, 'wb') as f:
                    f.write(zip_data)
                
                # 解压ZIP文件
                updated_files = []
                with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
                    for file_info in zip_ref.infolist():
                        if not file_info.is_dir():
                            # 安全检查
                            if '..' in file_info.filename or file_info.filename.startswith('/'):
                                logger.warning(f"跳过不安全的文件路径: {file_info.filename}")
                                continue
                            
                            # 解压文件
                            dest_path = self.app_dir / file_info.filename
                            dest_path.parent.mkdir(parents=True, exist_ok=True)
                            
                            with zip_ref.open(file_info) as source, open(dest_path, 'wb') as target:
                                shutil.copyfileobj(source, target)
                            
                            updated_files.append(file_info.filename)
                
                return {
                    'status': 'success',
                    'message': f'从ZIP成功更新 {len(updated_files)} 个文件',
                    'updated_files': updated_files,
                    'backup_id': backup_id
                }
                
            finally:
                # 清理临时文件
                temp_zip_path.unlink(missing_ok=True)
                
        except Exception as e:
            logger.error(f"ZIP更新失败: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def update_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新配置文件"""
        try:
            config_type = config_data.get('type', 'ini')
            config_content = config_data.get('content')
            config_file = config_data.get('file', 'config/slave_server.ini')
            
            if not config_content:
                return {'status': 'error', 'message': '没有提供配置内容'}
            
            # 配置文件路径
            config_path = self.app_dir / config_file
            
            # 备份现有配置
            backup_path = None
            if config_path.exists():
                backup_path = config_path.with_suffix(f'.backup.{int(asyncio.get_event_loop().time())}')
                shutil.copy2(config_path, backup_path)
            
            # 确保目录存在
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入新配置
            if config_type == 'json':
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config_content, f, indent=2, ensure_ascii=False)
            else:
                with open(config_path, 'w', encoding='utf-8') as f:
                    f.write(config_content)
            
            logger.info(f"配置文件已更新: {config_file}")
            
            return {
                'status': 'success',
                'message': f'配置文件 {config_file} 更新成功',
                'backup_path': str(backup_path) if backup_path else None
            }
            
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def update_vh_version(self, version_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新VirtualHere版本"""
        try:
            if not self.vh_manager:
                return {'status': 'error', 'message': 'VirtualHere管理器未初始化'}
            
            binary_data = version_data.get('binary_data')
            version = version_data.get('version', 'unknown')
            
            if not binary_data:
                return {'status': 'error', 'message': '没有提供二进制数据'}
            
            # 创建临时文件
            temp_binary_path = self.temp_dir / f'vhusbd_{version}_{int(asyncio.get_event_loop().time())}'
            
            try:
                # 写入二进制数据
                with open(temp_binary_path, 'wb') as f:
                    f.write(binary_data)
                
                # 设置执行权限
                os.chmod(temp_binary_path, 0o755)
                
                # 更新VirtualHere二进制文件
                success = await self.vh_manager.update_binary(str(temp_binary_path))
                
                if success:
                    return {
                        'status': 'success',
                        'message': f'VirtualHere版本 {version} 更新成功',
                        'version': version
                    }
                else:
                    return {
                        'status': 'error',
                        'message': 'VirtualHere二进制文件更新失败'
                    }
                    
            finally:
                # 清理临时文件
                temp_binary_path.unlink(missing_ok=True)
                
        except Exception as e:
            logger.error(f"更新VirtualHere版本失败: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def restart_server(self) -> Dict[str, Any]:
        """重启服务器"""
        try:
            logger.info("收到重启服务器请求")
            
            # 延迟重启，给客户端时间接收响应
            asyncio.create_task(self._delayed_restart())
            
            return {
                'status': 'success',
                'message': '服务器将在3秒后重启'
            }
            
        except Exception as e:
            logger.error(f"重启服务器失败: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def _delayed_restart(self):
        """延迟重启"""
        try:
            await asyncio.sleep(3)  # 等待3秒
            
            # 在容器环境中，退出进程会触发容器重启
            logger.info("正在重启服务器...")
            os._exit(0)
            
        except Exception as e:
            logger.error(f"延迟重启失败: {e}")
    
    async def _create_backup(self) -> str:
        """创建代码备份"""
        try:
            backup_id = f"backup_{int(asyncio.get_event_loop().time())}"
            backup_path = self.backup_dir / backup_id
            
            # 创建备份目录
            backup_path.mkdir(exist_ok=True)
            
            # 备份关键文件
            important_files = [
                'main.py',
                'core/',
                'config/',
                'requirements.txt'
            ]
            
            for item in important_files:
                source_path = self.app_dir / item
                if source_path.exists():
                    dest_path = backup_path / item
                    if source_path.is_dir():
                        shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
                    else:
                        dest_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(source_path, dest_path)
            
            logger.info(f"代码备份已创建: {backup_id}")
            return backup_id
            
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return f"backup_failed_{int(asyncio.get_event_loop().time())}"
    
    def get_status(self) -> Dict[str, Any]:
        """获取管理模块状态"""
        try:
            # 统计备份数量
            backup_count = len(list(self.backup_dir.glob('backup_*'))) if self.backup_dir.exists() else 0
            
            # 获取磁盘使用情况
            app_size = sum(f.stat().st_size for f in self.app_dir.rglob('*') if f.is_file())
            
            return {
                'backup_count': backup_count,
                'app_size_mb': app_size / 1024 / 1024,
                'backup_dir': str(self.backup_dir),
                'temp_dir': str(self.temp_dir)
            }
            
        except Exception as e:
            logger.error(f"获取管理状态失败: {e}")
            return {'error': str(e)}
