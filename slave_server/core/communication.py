#!/usr/bin/env python3
"""
主服务器通信模块
负责与主服务器的注册、心跳、状态同步
保持100%API兼容性
"""

import asyncio
import aiohttp
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
import socket
from .hardware_fingerprint import generate_hardware_uuid, get_hardware_summary
from .config_manager import SlaveConfigManager
from .config_sync import initialize_config_sync, get_config_sync_manager
from .port_database import initialize_port_database
from .smart_heartbeat import SmartHeartbeatManager, HeartbeatConfig, HeartbeatStrategy

logger = logging.getLogger(__name__)

class MasterCommunicator:
    """主服务器通信器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.master_url = config.get('master_url', 'http://192.168.1.100:8000')
        self.server_name = config.get('server_name', 'OmniLink-Slave-Server')
        self.server_port = config.get('server_port', 8889)
        self.vh_port = config.get('vh_port', 7575)

        # 配置管理器
        self.config_manager = SlaveConfigManager()

        # 硬件UUID管理
        self.hardware_uuid: Optional[str] = None
        self.hardware_info: Optional[Dict[str, Any]] = None

        # 配置同步管理器
        self.config_sync_manager = None

        # 状态管理
        self.registered = False
        self.current_ip: Optional[str] = None  # 当前成功注册的IP地址
        self.last_heartbeat: Optional[datetime] = None
        self.heartbeat_interval = config.get('heartbeat_interval', 30)  # 秒
        self.connection_timeout = config.get('connection_timeout', 10)  # 秒
        self.retry_count = config.get('retry_count', 3)  # 重试次数
        self.heartbeat_task: Optional[asyncio.Task] = None

        # 智能心跳管理器
        heartbeat_config = HeartbeatConfig(
            base_interval=self.heartbeat_interval,
            min_interval=15,
            max_interval=120,
            batch_size=50,
            strategy=HeartbeatStrategy.SMART
        )
        self.smart_heartbeat = SmartHeartbeatManager(
            server_id=self.hardware_uuid,
            config=heartbeat_config
        )
        
        # HTTP会话
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 依赖注入
        self.usb_manager = None
        self.vh_manager = None
    
    def set_dependencies(self, usb_manager, vh_manager):
        """设置依赖组件"""
        self.usb_manager = usb_manager
        self.vh_manager = vh_manager

    async def initialize_hardware_uuid(self):
        """初始化硬件UUID"""
        try:
            # 从配置文件加载UUID
            stored_uuid = self.config_manager.get_hardware_uuid()

            if stored_uuid:
                self.hardware_uuid = stored_uuid
                logger.info(f"从配置文件加载硬件UUID: {stored_uuid}")
            else:
                # 生成新的硬件UUID
                self.hardware_uuid = generate_hardware_uuid()
                self.config_manager.set_hardware_uuid(self.hardware_uuid)
                logger.info(f"生成新的硬件UUID: {self.hardware_uuid}")

            # 获取硬件信息摘要
            self.hardware_info = get_hardware_summary()
            self.config_manager.set_hardware_fingerprint_data(self.hardware_info)

            # 更新配置管理器中的主服务器URL
            self.config_manager.set_master_url(self.master_url)
            self.config_manager.set_server_port(self.server_port)

            # 从配置文件加载服务器名称（如果有）
            stored_name = self.config_manager.get_server_name()
            if stored_name:
                self.server_name = stored_name
                logger.info(f"从配置文件加载服务器名称: {stored_name}")

        except Exception as e:
            logger.error(f"初始化硬件UUID失败: {e}")
            # 降级方案：使用临时UUID
            import uuid
            self.hardware_uuid = str(uuid.uuid4())
            logger.warning(f"使用临时UUID: {self.hardware_uuid}")

    async def _initialize_config_sync(self):
        """初始化配置同步管理器"""
        try:
            if self.hardware_uuid and self.master_url:
                self.config_sync_manager = initialize_config_sync(self.master_url, self.hardware_uuid)
                await self.config_sync_manager.start()

                # 执行一次初始同步
                await self.config_sync_manager.force_sync()

                logger.info("配置同步管理器初始化成功")
            else:
                logger.warning("无法初始化配置同步：缺少UUID或主服务器URL")

        except Exception as e:
            logger.error(f"初始化配置同步管理器失败: {e}")

    def _initialize_port_database(self):
        """初始化端口位置码数据库"""
        try:
            if self.hardware_uuid:
                port_db = initialize_port_database(self.hardware_uuid)
                logger.info("端口位置码数据库初始化成功")
                return port_db
            else:
                logger.warning("无法初始化端口数据库：缺少硬件UUID")
                return None

        except Exception as e:
            logger.error(f"初始化端口位置码数据库失败: {e}")
            return None
    
    async def start(self):
        """启动通信服务"""
        try:
            # 初始化硬件UUID
            await self.initialize_hardware_uuid()

            # 创建优化的HTTP会话
            timeout = aiohttp.ClientTimeout(
                total=self.connection_timeout,
                connect=10.0,
                sock_read=self.connection_timeout
            )

            # 优化连接器配置
            connector = aiohttp.TCPConnector(
                limit=20,  # 连接池大小
                limit_per_host=10,  # 每个主机的连接数
                keepalive_timeout=30,  # 保持连接时间
                enable_cleanup_closed=True,
                ttl_dns_cache=300
            )

            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                raise_for_status=False
            )

            # 尝试注册到主服务器
            await self.register_to_master()

            # 启动智能心跳管理器
            await self.smart_heartbeat.start(self.send_heartbeat)
            logger.info(f"智能心跳已启动，策略: {self.smart_heartbeat.config.strategy.value}")

            logger.info("主服务器通信已启动")

        except Exception as e:
            logger.error(f"启动主服务器通信失败: {e}")
    
    async def stop(self):
        """停止通信服务"""
        try:
            # 停止智能心跳管理器
            await self.smart_heartbeat.stop()

            # 停止配置同步
            if self.config_sync_manager:
                await self.config_sync_manager.stop()

            # 停止传统心跳任务（如果存在）
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass

            # 关闭HTTP会话
            if self.session:
                await self.session.close()

            logger.info("主服务器通信已停止")

        except Exception as e:
            logger.error(f"停止主服务器通信失败: {e}")

    async def sync_devices_to_master(self, devices: List[Dict[str, Any]]) -> bool:
        """向主服务器同步设备清单"""
        try:
            if not self.config_sync_manager:
                logger.warning("配置同步管理器未初始化，无法同步设备")
                return False

            # 格式化设备信息
            formatted_devices = []
            for device in devices:
                formatted_device = self.config_sync_manager.format_device_for_sync(device)
                if formatted_device:
                    formatted_devices.append(formatted_device)

            # 执行同步
            if formatted_devices:
                success = await self.config_sync_manager.sync_devices_to_master(formatted_devices)
                if success:
                    logger.info(f"成功同步 {len(formatted_devices)} 个设备到主服务器")
                return success
            else:
                logger.warning("没有有效的设备需要同步")
                return True

        except Exception as e:
            logger.error(f"同步设备到主服务器失败: {e}")
            return False

    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        if self.config_sync_manager:
            return self.config_sync_manager.get_sync_status()
        else:
            return {
                'last_sync_time': None,
                'sync_interval': 0,
                'config_version': 0,
                'is_running': False,
                'master_url': self.master_url,
                'hardware_uuid': self.hardware_uuid
            }
    
    async def register_to_master(self) -> bool:
        """向主服务器注册（智能IP分配和冲突处理）"""
        # 尝试不同的IP地址，直到找到可用的
        candidate_ips = [
            '*********',  # 首选IP
            '*********',  # 备选IP
            '*********',  # 备选IP
            '*********',  # 备选IP
            f'127.0.0.{100 + self.server_port % 100}'  # 基于端口的动态IP
        ]

        for ip_candidate in candidate_ips:
            for attempt in range(self.retry_count):
                try:
                    # 构建注册数据（包含UUID和硬件信息）
                    data = {
                        'server_name': f"{self.server_name}-v2-{self.server_port}",
                        'server_ip': ip_candidate,
                        'server_port': self.server_port,
                        'vh_port': self.vh_port,
                        'hardware_uuid': self.hardware_uuid,
                        'hardware_info': self.hardware_info,
                        'description': 'OmniLink Lightweight Slave Server v2.0 (Native)',
                        'version': '2.0'
                    }

                    # 发送注册请求
                    url = f"{self.master_url}/api/v1/slave/register"
                    headers = {'Content-Type': 'application/json; charset=utf-8'}

                    logger.info(f"尝试注册到主服务器 (IP: {ip_candidate}, 第{attempt + 1}次): {url}")

                    async with self.session.post(url, json=data, headers=headers) as response:
                        if response.status == 200:
                            result = await response.json()
                            self.registered = True
                            self.current_ip = ip_candidate  # 记录成功的IP

                            # 保存注册成功的配置信息
                            self.config_manager.set_last_known_ip(ip_candidate)

                            # 只在配置版本真正变更时才更新
                            server_config_version = result.get('config_version')
                            local_config_version = self.config_manager.get_config_version()
                            if server_config_version and server_config_version != local_config_version:
                                self.config_manager.set_config_version(server_config_version)
                                logger.info(f"配置版本更新: {local_config_version} -> {server_config_version}")
                            else:
                                logger.debug(f"配置版本无变化: {local_config_version}")

                            # 初始化配置同步管理器
                            await self._initialize_config_sync()

                            # 初始化端口位置码数据库
                            self._initialize_port_database()

                            logger.info(f"从服务器注册成功 (IP: {ip_candidate}): {result.get('message', '成功')}")
                            logger.info(f"硬件UUID: {self.hardware_uuid}")
                            return True
                        elif response.status == 409:  # IP地址冲突
                            error_text = await response.text()
                            logger.warning(f"IP地址 {ip_candidate} 已被占用: {error_text}")
                            break  # 尝试下一个IP
                        else:
                            error_text = await response.text()
                            logger.warning(f"注册失败 (IP: {ip_candidate}, 第{attempt + 1}次): HTTP {response.status} - {error_text}")

                except Exception as e:
                    logger.warning(f"注册尝试失败 (IP: {ip_candidate}, 第{attempt + 1}次): {e}")

                    if attempt < self.retry_count - 1:
                        wait_time = (attempt + 1) * 2
                        logger.info(f"等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)

        logger.error(f"所有IP地址都已被占用或注册失败，尝试的IP: {candidate_ips}")
        self.registered = False
        return False
    
    async def send_heartbeat(self) -> bool:
        """发送心跳到主服务器"""
        try:
            logger.debug("开始发送心跳")

            # 检查session是否可用
            if not self.session or self.session.closed:
                logger.error("HTTP会话未初始化或已关闭")
                return False

            # 获取系统信息
            logger.debug("获取系统信息")
            system_info = await self._get_system_info()
            logger.debug(f"系统信息: {system_info}")
            
            # 获取设备数量
            device_count = self.usb_manager.get_device_count() if self.usb_manager else 0
            
            # 获取VirtualHere状态
            vh_status = 'running' if self.vh_manager and await self.vh_manager.is_running() else 'stopped'
            
            # 构建心跳数据（与注册数据保持一致）
            data = {
                'timestamp': datetime.now().isoformat(),
                'status': 'online',
                'device_count': device_count,
                'vh_status': vh_status,
                'system_info': system_info,
                'server_name': f"{self.server_name}-v2-{self.server_port}",
                'server_port': self.server_port,
                'vh_port': self.vh_port,
                'server_ip': self.current_ip  # 使用当前注册成功的IP
            }
            
            # 发送心跳请求
            url = f"{self.master_url}/api/v1/slave/heartbeat"
            headers = {'Content-Type': 'application/json; charset=utf-8'}

            # 获取当前IP用于调试
            current_ip = await self._get_local_ip()
            logger.debug(f"发送心跳到: {url}")
            logger.debug(f"当前IP地址: {current_ip}")
            logger.debug(f"心跳数据: {data}")

            async with self.session.post(url, json=data, headers=headers) as response:
                if response.status == 200:
                    self.last_heartbeat = datetime.now()
                    logger.debug("心跳发送成功")
                    return True
                elif response.status == 404:
                    # 从服务器未注册，尝试重新注册（这是正常流程）
                    logger.debug("从服务器未注册，尝试重新注册")
                    if await self.register_to_master():
                        self.last_heartbeat = datetime.now()
                        logger.debug("重新注册成功，心跳将在下次定时发送")
                        return True
                    else:
                        logger.error("重新注册失败")
                        return False
                else:
                    error_text = await response.text()
                    logger.error(f"心跳发送失败: HTTP {response.status}")
                    logger.error(f"错误详情: {error_text}")
                    logger.error(f"请求URL: {url}")
                    logger.error(f"请求数据: {json.dumps(data, indent=2)}")
                    return False
                    
        except Exception as e:
            import traceback
            logger.error(f"发送心跳失败: {e}")
            logger.error(f"心跳URL: {self.master_url}/api/v1/slave/heartbeat")
            logger.error(f"详细错误信息: {traceback.format_exc()}")

            # 检查是否是连接问题
            if "Connection" in str(e) or "timeout" in str(e).lower():
                logger.error("网络连接问题，检查主服务器是否可访问")
            elif "JSON" in str(e):
                logger.error("JSON序列化问题，检查心跳数据格式")

            return False
    
    async def _heartbeat_worker(self):
        """优化的心跳工作任务"""
        logger.info("心跳工作任务已启动")
        consecutive_failures = 0
        max_failures = 5
        base_retry_delay = 5

        while True:
            try:
                logger.debug(f"心跳循环开始，间隔: {self.heartbeat_interval}秒")
                success = await self.send_heartbeat()

                if success:
                    consecutive_failures = 0  # 重置失败计数
                    logger.debug(f"心跳发送成功，等待 {self.heartbeat_interval} 秒")
                    await asyncio.sleep(self.heartbeat_interval)
                else:
                    consecutive_failures += 1
                    retry_delay = min(base_retry_delay * (2 ** consecutive_failures), 60)

                    if consecutive_failures >= max_failures:
                        logger.error(f"心跳连续失败 {consecutive_failures} 次，尝试重新初始化连接")
                        await self._reinitialize_connection()
                        consecutive_failures = 0
                        retry_delay = base_retry_delay

                    logger.warning(f"心跳发送失败，{retry_delay}秒后重试 (失败次数: {consecutive_failures})")
                    await asyncio.sleep(retry_delay)

            except asyncio.CancelledError:
                logger.info("心跳任务已取消")
                break
            except Exception as e:
                consecutive_failures += 1
                logger.error(f"心跳任务异常: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")

                # 异常时的退避策略
                retry_delay = min(base_retry_delay * consecutive_failures, 30)
                await asyncio.sleep(retry_delay)
    
    async def _reinitialize_connection(self):
        """重新初始化连接"""
        try:
            logger.info("重新初始化HTTP连接")

            # 关闭现有会话
            if self.session and not self.session.closed:
                await self.session.close()
                await asyncio.sleep(1)  # 等待连接完全关闭

            # 创建新的HTTP会话
            timeout = aiohttp.ClientTimeout(
                total=self.connection_timeout,
                connect=10.0,
                sock_read=self.connection_timeout
            )

            connector = aiohttp.TCPConnector(
                limit=20,
                limit_per_host=10,
                keepalive_timeout=30,
                enable_cleanup_closed=True,
                ttl_dns_cache=300
            )

            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                raise_for_status=False
            )

            # 重置注册状态
            self.registered = False

            logger.info("HTTP连接重新初始化完成")

        except Exception as e:
            logger.error(f"重新初始化连接失败: {e}")

    async def _get_local_ip(self) -> str:
        """获取当前注册成功的IP地址"""
        # 如果已经注册成功，返回当前IP
        if self.current_ip:
            return self.current_ip

        # 如果还没有注册，返回默认IP
        return '*********'
    
    async def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            import psutil
            
            # 获取CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # 获取内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 获取磁盘信息
            try:
                import platform
                if platform.system() == 'Windows':
                    disk = psutil.disk_usage('C:')
                else:
                    disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100
            except Exception as disk_error:
                logger.debug(f"获取磁盘信息失败: {disk_error}")
                disk_percent = 0
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent
            }
            
        except ImportError:
            logger.warning("psutil未安装，无法获取系统信息")
            return {
                'cpu_percent': 0.0,
                'memory_percent': 0.0,
                'disk_percent': 0.0
            }
        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            return {
                'cpu_percent': 0.0,
                'memory_percent': 0.0,
                'disk_percent': 0.0
            }
    
    def get_status(self) -> Dict[str, Any]:
        """获取通信状态"""
        return {
            'registered': self.registered,
            'master_url': self.master_url,
            'last_heartbeat': self.last_heartbeat.isoformat() if self.last_heartbeat else None,
            'heartbeat_interval': self.heartbeat_interval,
            'session_active': self.session is not None and not self.session.closed
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试与主服务器的连接"""
        try:
            url = f"{self.master_url}/api/v1/slave/stats"  # 使用一个简单的API进行测试
            
            start_time = asyncio.get_event_loop().time()
            async with self.session.get(url) as response:
                end_time = asyncio.get_event_loop().time()
                
                return {
                    'success': True,
                    'status_code': response.status,
                    'response_time_ms': (end_time - start_time) * 1000,
                    'master_url': self.master_url
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'master_url': self.master_url
            }
    
    async def update_config(self, new_config: Dict[str, Any]):
        """更新通信配置"""
        try:
            # 更新配置
            old_master_url = self.master_url
            self.config.update(new_config)
            self.master_url = new_config.get('master_url', self.master_url)
            
            # 如果主服务器URL发生变化，需要重新注册
            if old_master_url != self.master_url:
                logger.info(f"主服务器URL已更改: {old_master_url} -> {self.master_url}")
                self.registered = False
                await self.register_to_master()
            
            logger.info("通信配置已更新")
            
        except Exception as e:
            logger.error(f"更新通信配置失败: {e}")
