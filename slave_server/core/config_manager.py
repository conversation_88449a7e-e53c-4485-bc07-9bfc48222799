#!/usr/bin/env python3
"""
从服务器配置管理器
负责本地配置文件的读写、UUID管理、设备配置缓存
"""

import json
import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
import threading

logger = logging.getLogger(__name__)

class SlaveConfigManager:
    """从服务器配置管理器"""
    
    def __init__(self, config_dir: str = None):
        # 配置目录
        if config_dir:
            self.config_dir = Path(config_dir)
        else:
            self.config_dir = Path(__file__).parent.parent / "config"
        
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置文件路径
        self.config_file = self.config_dir / "slave_config.json"
        self.device_cache_file = self.config_dir / "device_cache.json"
        
        # 线程锁
        self._lock = threading.Lock()
        
        # 配置数据
        self._config = {}
        self._device_cache = {}
        
        # 加载配置
        self._load_config()
        self._load_device_cache()
        
        logger.info(f"配置管理器初始化完成，配置目录: {self.config_dir}")
    
    def _load_config(self):
        """加载主配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                logger.info("主配置文件加载成功")
            else:
                # 创建默认配置
                self._config = self._create_default_config()
                self._save_config()
                logger.info("创建默认配置文件")
        except Exception as e:
            logger.error(f"加载主配置文件失败: {e}")
            self._config = self._create_default_config()
    
    def _load_device_cache(self):
        """加载设备缓存文件"""
        try:
            if self.device_cache_file.exists():
                with open(self.device_cache_file, 'r', encoding='utf-8') as f:
                    self._device_cache = json.load(f)
                logger.info("设备缓存文件加载成功")
            else:
                self._device_cache = {}
        except Exception as e:
            logger.error(f"加载设备缓存文件失败: {e}")
            self._device_cache = {}
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        return {
            "server_info": {
                "hardware_uuid": None,
                "server_name": None,
                "description": "OmniLink从服务器",
                "version": "2.0",
                "created_at": datetime.now().isoformat()
            },
            "network": {
                "server_host": "0.0.0.0",
                "server_port": 8890,
                "master_url": None,
                "last_known_ip": None
            },
            "virtualhere": {
                "vh_port": 7575,
                "binary_path": None,
                "auto_start": True
            },
            "sync": {
                "last_sync_time": None,
                "config_version": 1,
                "sync_interval": 300  # 5分钟
            },
            "hardware": {
                "fingerprint_data": None,
                "last_hardware_scan": None
            }
        }
    
    def _save_config(self):
        """保存主配置文件"""
        try:
            with self._lock:
                # 更新最后修改时间
                self._config.setdefault("meta", {})["last_updated"] = datetime.now().isoformat()
                
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(self._config, f, indent=2, ensure_ascii=False)
                logger.debug("主配置文件保存成功")
        except Exception as e:
            logger.error(f"保存主配置文件失败: {e}")
    
    def _save_device_cache(self):
        """保存设备缓存文件"""
        try:
            with self._lock:
                # 添加元数据
                cache_data = {
                    "meta": {
                        "last_updated": datetime.now().isoformat(),
                        "device_count": len(self._device_cache)
                    },
                    "devices": self._device_cache
                }
                
                with open(self.device_cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, indent=2, ensure_ascii=False)
                logger.debug("设备缓存文件保存成功")
        except Exception as e:
            logger.error(f"保存设备缓存文件失败: {e}")
    
    # ==================== 硬件UUID管理 ====================
    
    def get_hardware_uuid(self) -> Optional[str]:
        """获取硬件UUID"""
        return self._config.get("server_info", {}).get("hardware_uuid")
    
    def set_hardware_uuid(self, uuid: str):
        """设置硬件UUID"""
        self._config.setdefault("server_info", {})["hardware_uuid"] = uuid
        self._save_config()
        logger.info(f"硬件UUID已设置: {uuid}")
    
    def get_server_name(self) -> Optional[str]:
        """获取服务器名称"""
        return self._config.get("server_info", {}).get("server_name")
    
    def set_server_name(self, name: str):
        """设置服务器名称"""
        self._config.setdefault("server_info", {})["server_name"] = name
        self._save_config()
        logger.info(f"服务器名称已设置: {name}")
    
    # ==================== 网络配置管理 ====================
    
    def get_master_url(self) -> Optional[str]:
        """获取主服务器URL"""
        return self._config.get("network", {}).get("master_url")
    
    def set_master_url(self, url: str):
        """设置主服务器URL"""
        self._config.setdefault("network", {})["master_url"] = url
        self._save_config()
        logger.info(f"主服务器URL已设置: {url}")
    
    def get_server_port(self) -> int:
        """获取服务器端口"""
        return self._config.get("network", {}).get("server_port", 8890)
    
    def set_server_port(self, port: int):
        """设置服务器端口"""
        self._config.setdefault("network", {})["server_port"] = port
        self._save_config()
        logger.info(f"服务器端口已设置: {port}")
    
    def get_last_known_ip(self) -> Optional[str]:
        """获取最后已知IP"""
        return self._config.get("network", {}).get("last_known_ip")
    
    def set_last_known_ip(self, ip: str):
        """设置最后已知IP"""
        self._config.setdefault("network", {})["last_known_ip"] = ip
        self._save_config()
    
    # ==================== 同步配置管理 ====================
    
    def get_config_version(self) -> int:
        """获取配置版本"""
        return self._config.get("sync", {}).get("config_version", 1)
    
    def set_config_version(self, version: int):
        """设置配置版本"""
        self._config.setdefault("sync", {})["config_version"] = version
        self._config.setdefault("sync", {})["last_sync_time"] = datetime.now().isoformat()
        self._save_config()
        logger.info(f"配置版本已更新: {version}")
    
    def get_last_sync_time(self) -> Optional[str]:
        """获取最后同步时间"""
        return self._config.get("sync", {}).get("last_sync_time")
    
    # ==================== 设备缓存管理 ====================
    
    def get_device_config(self, hardware_signature: str) -> Optional[Dict[str, Any]]:
        """获取设备配置"""
        return self._device_cache.get(hardware_signature)
    
    def set_device_config(self, hardware_signature: str, config: Dict[str, Any]):
        """设置设备配置"""
        self._device_cache[hardware_signature] = {
            **config,
            "last_updated": datetime.now().isoformat()
        }
        self._save_device_cache()
        logger.debug(f"设备配置已缓存: {hardware_signature}")
    
    def remove_device_config(self, hardware_signature: str):
        """移除设备配置"""
        if hardware_signature in self._device_cache:
            del self._device_cache[hardware_signature]
            self._save_device_cache()
            logger.debug(f"设备配置已移除: {hardware_signature}")
    
    def get_all_device_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有设备配置"""
        return self._device_cache.copy()
    
    def update_device_configs(self, configs: Dict[str, Dict[str, Any]]):
        """批量更新设备配置"""
        self._device_cache.update(configs)
        self._save_device_cache()
        logger.info(f"批量更新设备配置: {len(configs)} 个设备")
    
    def clear_device_cache(self):
        """清空设备缓存"""
        self._device_cache.clear()
        self._save_device_cache()
        logger.info("设备缓存已清空")
    
    # ==================== 硬件信息管理 ====================
    
    def get_hardware_fingerprint_data(self) -> Optional[Dict[str, Any]]:
        """获取硬件指纹数据"""
        return self._config.get("hardware", {}).get("fingerprint_data")
    
    def set_hardware_fingerprint_data(self, data: Dict[str, Any]):
        """设置硬件指纹数据"""
        self._config.setdefault("hardware", {})["fingerprint_data"] = data
        self._config.setdefault("hardware", {})["last_hardware_scan"] = datetime.now().isoformat()
        self._save_config()
        logger.debug("硬件指纹数据已保存")
    
    # 配置导出导入功能已移除（生产环境不需要）
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "hardware_uuid": self.get_hardware_uuid(),
            "server_name": self.get_server_name(),
            "master_url": self.get_master_url(),
            "server_port": self.get_server_port(),
            "config_version": self.get_config_version(),
            "last_sync_time": self.get_last_sync_time(),
            "device_count": len(self._device_cache),
            "config_file_exists": self.config_file.exists(),
            "device_cache_file_exists": self.device_cache_file.exists()
        }
