#!/usr/bin/env python3
"""
轻量级HTTP服务器
基于aiohttp实现，保持Flask API兼容性
专注于高性能和低资源占用
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from aiohttp import web, ClientSession
import aiohttp_cors

logger = logging.getLogger(__name__)

class LightweightServer:
    """轻量级HTTP服务器"""
    
    def __init__(self, host='0.0.0.0', port=8889):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.runner = None
        self.site = None
        
        # 依赖注入
        self.usb_manager = None
        self.vh_manager = None
        self.communicator = None
        self.management = None
        
        self._setup_cors()
        self._setup_routes()
    
    def _setup_cors(self):
        """设置CORS"""
        self.cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
    
    def _setup_routes(self):
        """设置路由"""
        # 健康检查
        self.app.router.add_get('/api/system/health', self.health_check)
        self.app.router.add_get('/api/system/status', self.system_status)
        
        # 设备管理
        self.app.router.add_get('/api/devices', self.get_devices)
        
        # VirtualHere控制
        self.app.router.add_post('/api/virtualhere/start', self.start_vh)
        self.app.router.add_post('/api/virtualhere/stop', self.stop_vh)
        
        # 从服务器管理API
        self.app.router.add_get('/api/management/status', self.management_status)
        self.app.router.add_post('/api/management/update', self.update_server)
        self.app.router.add_post('/api/management/config', self.update_config)
        self.app.router.add_post('/api/management/vh_version', self.update_vh_version)
        self.app.router.add_post('/api/management/restart', self.restart_server)

        # 端口位置码测试接口
        self.app.router.add_get('/api/ports', self.get_port_locations)
        self.app.router.add_post('/api/ports/{location_code}/bind', self.bind_device_to_port)
        self.app.router.add_delete('/api/ports/{location_code}/bind', self.unbind_device_from_port)

        # Web界面
        self.app.router.add_get('/', self.web_interface)
    
    def set_dependencies(self, usb_manager, vh_manager, communicator, management):
        """设置依赖组件"""
        self.usb_manager = usb_manager
        self.vh_manager = vh_manager
        self.communicator = communicator
        self.management = management
    
    async def health_check(self, request):
        """健康检查"""
        try:
            vh_status = 'running' if self.vh_manager and await self.vh_manager.is_running() else 'stopped'
            device_count = len(self.usb_manager.get_devices()) if self.usb_manager else 0
            
            return web.json_response({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'vh_status': vh_status,
                'device_count': device_count
            })
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)
    
    async def system_status(self, request):
        """系统状态"""
        try:
            import psutil
            
            vh_status = 'running' if self.vh_manager and await self.vh_manager.is_running() else 'stopped'
            device_count = len(self.usb_manager.get_devices()) if self.usb_manager else 0
            last_heartbeat = self.communicator.last_heartbeat if self.communicator else None
            
            return web.json_response({
                'status': 'online',
                'vh_status': vh_status,
                'device_count': device_count,
                'last_heartbeat': last_heartbeat.isoformat() if last_heartbeat else None,
                'system_info': {
                    'cpu_percent': psutil.cpu_percent(),
                    'memory_percent': psutil.virtual_memory().percent,
                    'disk_percent': psutil.disk_usage('/').percent if hasattr(psutil, 'disk_usage') else 0
                },
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)
    
    async def get_devices(self, request):
        """获取设备列表"""
        try:
            devices = self.usb_manager.get_devices() if self.usb_manager else []
            return web.json_response({
                'devices': devices,
                'count': len(devices),
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            logger.error(f"获取设备列表失败: {e}")
            return web.json_response({
                'devices': [],
                'count': 0,
                'error': str(e)
            }, status=500)
    
    async def start_vh(self, request):
        """启动VirtualHere"""
        try:
            if self.vh_manager:
                success = await self.vh_manager.start()
                if success:
                    return web.json_response({'status': 'success', 'message': 'VirtualHere已启动'})
                else:
                    return web.json_response({'status': 'error', 'message': 'VirtualHere启动失败'}, status=500)
            else:
                return web.json_response({'status': 'error', 'message': 'VirtualHere管理器未初始化'}, status=500)
        except Exception as e:
            logger.error(f"启动VirtualHere失败: {e}")
            return web.json_response({'status': 'error', 'message': str(e)}, status=500)
    
    async def stop_vh(self, request):
        """停止VirtualHere"""
        try:
            if self.vh_manager:
                success = await self.vh_manager.stop()
                if success:
                    return web.json_response({'status': 'success', 'message': 'VirtualHere已停止'})
                else:
                    return web.json_response({'status': 'error', 'message': 'VirtualHere停止失败'}, status=500)
            else:
                return web.json_response({'status': 'error', 'message': 'VirtualHere管理器未初始化'}, status=500)
        except Exception as e:
            logger.error(f"停止VirtualHere失败: {e}")
            return web.json_response({'status': 'error', 'message': str(e)}, status=500)
    
    async def management_status(self, request):
        """获取管理模块状态"""
        try:
            logger.info(f"管理状态请求 - 管理模块状态: {self.management is not None}")
            if self.management:
                status = self.management.get_status()
                return web.json_response({
                    'status': 'success',
                    'management': status,
                    'available_apis': [
                        '/api/management/update',
                        '/api/management/config',
                        '/api/management/vh_version',
                        '/api/management/restart'
                    ]
                })
            else:
                logger.error("管理模块未初始化")
                return web.json_response({'status': 'error', 'message': '管理模块未初始化'}, status=500)
        except Exception as e:
            logger.error(f"获取管理状态失败: {e}")
            return web.json_response({'status': 'error', 'message': str(e)}, status=500)

    async def update_server(self, request):
        """更新服务器代码"""
        try:
            data = await request.json()
            if self.management:
                result = await self.management.update_code(data)
                return web.json_response(result)
            else:
                return web.json_response({'status': 'error', 'message': '管理模块未初始化'}, status=500)
        except Exception as e:
            logger.error(f"更新服务器失败: {e}")
            return web.json_response({'status': 'error', 'message': str(e)}, status=500)
    
    async def update_config(self, request):
        """更新配置文件"""
        try:
            data = await request.json()
            if self.management:
                result = await self.management.update_config(data)
                return web.json_response(result)
            else:
                return web.json_response({'status': 'error', 'message': '管理模块未初始化'}, status=500)
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return web.json_response({'status': 'error', 'message': str(e)}, status=500)
    
    async def update_vh_version(self, request):
        """更新VirtualHere版本"""
        try:
            data = await request.json()
            if self.management:
                result = await self.management.update_vh_version(data)
                return web.json_response(result)
            else:
                return web.json_response({'status': 'error', 'message': '管理模块未初始化'}, status=500)
        except Exception as e:
            logger.error(f"更新VirtualHere版本失败: {e}")
            return web.json_response({'status': 'error', 'message': str(e)}, status=500)
    
    async def restart_server(self, request):
        """重启服务器"""
        try:
            if self.management:
                result = await self.management.restart_server()
                return web.json_response(result)
            else:
                return web.json_response({'status': 'error', 'message': '管理模块未初始化'}, status=500)
        except Exception as e:
            logger.error(f"重启服务器失败: {e}")
            return web.json_response({'status': 'error', 'message': str(e)}, status=500)
    
    async def web_interface(self, request):
        """简易Web界面"""
        try:
            vh_status = 'running' if self.vh_manager and await self.vh_manager.is_running() else 'stopped'
            devices = self.usb_manager.get_devices() if self.usb_manager else []
            last_heartbeat = self.communicator.last_heartbeat if self.communicator else None
            
            import psutil
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            disk_percent = psutil.disk_usage('/').percent if hasattr(psutil, 'disk_usage') else 0
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>OmniLink从服务器 v2.0</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                    .container {{ max-width: 1200px; margin: 0 auto; }}
                    .status {{ padding: 15px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                    .online {{ background: linear-gradient(135deg, #d4edda, #c3e6cb); color: #155724; }}
                    .offline {{ background: linear-gradient(135deg, #f8d7da, #f5c6cb); color: #721c24; }}
                    table {{ border-collapse: collapse; width: 100%; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                    th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                    th {{ background: linear-gradient(135deg, #007bff, #0056b3); color: white; }}
                    .metric {{ display: inline-block; margin: 10px; padding: 10px 15px; background: white; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }}
                    .header {{ text-align: center; color: #333; margin-bottom: 30px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🚀 OmniLink从服务器 v2.0</h1>
                        <p>轻量化高性能USB设备代理服务</p>
                    </div>
                    
                    <div class="status online">
                        <h3>📊 服务状态: 在线运行</h3>
                        <div class="metric">VirtualHere: {'🟢 运行中' if vh_status == 'running' else '🔴 已停止'}</div>
                        <div class="metric">设备数量: {len(devices)}</div>
                        <div class="metric">最后心跳: {last_heartbeat.strftime('%H:%M:%S') if last_heartbeat else '未发送'}</div>
                    </div>

                    <h3>🔌 USB设备列表</h3>
                    <table>
                        <tr><th>厂商ID</th><th>产品ID</th><th>总线</th><th>地址</th><th>描述</th></tr>
                        {''.join([f"<tr><td>{d.get('vendor_id', 'N/A'):04x}</td><td>{d.get('product_id', 'N/A'):04x}</td><td>{d.get('bus', 'N/A')}</td><td>{d.get('address', 'N/A')}</td><td>{d.get('description', 'Unknown Device')}</td></tr>" for d in devices])}
                    </table>

                    <h3>💻 系统信息</h3>
                    <div class="status online">
                        <div class="metric">CPU使用率: {cpu_percent:.1f}%</div>
                        <div class="metric">内存使用率: {memory_percent:.1f}%</div>
                        <div class="metric">磁盘使用率: {disk_percent:.1f}%</div>
                    </div>

                    <script>
                        setTimeout(function(){{ location.reload(); }}, 30000);
                    </script>
                </div>
            </body>
            </html>
            """
            return web.Response(text=html, content_type='text/html')
        except Exception as e:
            logger.error(f"生成Web界面失败: {e}")
            return web.Response(text=f"Error: {e}", status=500)
    
    async def start(self):
        """启动服务器"""
        try:
            # 打印所有路由信息用于调试
            logger.info("注册的路由列表:")
            for route in list(self.app.router.routes()):
                logger.info(f"  {route.method} {route.resource.canonical}")
                self.cors.add(route)

            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            self.site = web.TCPSite(self.runner, self.host, self.port)
            await self.site.start()
            logger.info(f"轻量级HTTP服务器已启动: http://{self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"启动HTTP服务器失败: {e}")
            return False
    
    async def stop(self):
        """停止服务器"""
        try:
            if self.site:
                await self.site.stop()
            if self.runner:
                await self.runner.cleanup()
            logger.info("HTTP服务器已停止")
            return True
        except Exception as e:
            logger.error(f"停止HTTP服务器失败: {e}")
            return False

    async def get_port_locations(self, request):
        """获取所有端口位置码"""
        try:
            from .port_database import get_port_database

            port_db = get_port_database()
            if not port_db:
                return web.json_response({
                    "error": "端口位置码数据库未初始化"
                }, status=500)

            # 获取所有端口位置
            locations = port_db.get_all_port_locations()

            # 获取所有设备绑定
            bindings = port_db.get_all_device_bindings()
            binding_map = {b.location_code: b for b in bindings}

            # 构建响应数据
            port_data = []
            for location in locations:
                binding = binding_map.get(location.location_code)
                port_info = {
                    "location_code": location.location_code,
                    "controller_id": location.controller_id,
                    "hub_id": location.hub_id,
                    "port_number": location.port_number,
                    "hub_path": location.hub_path,
                    "created_at": location.created_at,
                    "last_seen": location.last_seen,
                    "is_active": location.is_active,
                    "is_bound": binding is not None,
                    "binding_info": None
                }

                if binding:
                    port_info["binding_info"] = {
                        "device_signature": binding.device_signature,
                        "custom_name": binding.custom_name,
                        "notes": binding.notes,
                        "binding_time": binding.binding_time,
                        "binding_confidence": binding.binding_confidence
                    }

                port_data.append(port_info)

            return web.json_response({
                "success": True,
                "ports": port_data,
                "total_ports": len(port_data),
                "bound_ports": len([p for p in port_data if p["is_bound"]])
            })

        except Exception as e:
            logger.error(f"获取端口位置码失败: {e}")
            return web.json_response({
                "error": f"获取端口位置码失败: {str(e)}"
            }, status=500)

    async def bind_device_to_port(self, request):
        """绑定设备到端口（端口独占模式）"""
        try:
            location_code = request.match_info['location_code']
            data = await request.json()

            custom_name = data.get('custom_name', '')
            notes = data.get('notes', '')
            device_signature = data.get('device_signature', f'test_device_{location_code}')

            from .port_database import get_port_database

            port_db = get_port_database()
            if not port_db:
                return web.json_response({
                    "error": "端口位置码数据库未初始化"
                }, status=500)

            # 检查端口是否存在
            location_info = port_db.get_port_location(location_code)
            if not location_info:
                return web.json_response({
                    "error": f"端口位置码不存在: {location_code}"
                }, status=404)

            # 执行绑定
            success = port_db.bind_device_to_port(
                location_code=location_code,
                device_signature=device_signature,
                custom_name=custom_name,
                notes=notes,
                config_data={"binding_type": "manual", "test_mode": True}
            )

            if success:
                logger.info(f"设备绑定成功: {device_signature} -> {location_code}")
                return web.json_response({
                    "success": True,
                    "message": "设备绑定成功",
                    "location_code": location_code,
                    "device_signature": device_signature,
                    "custom_name": custom_name,
                    "notes": notes
                })
            else:
                return web.json_response({
                    "error": "设备绑定失败，端口可能已被占用"
                }, status=400)

        except Exception as e:
            logger.error(f"设备绑定失败: {e}")
            return web.json_response({
                "error": f"设备绑定失败: {str(e)}"
            }, status=500)

    async def unbind_device_from_port(self, request):
        """解除设备与端口的绑定"""
        try:
            location_code = request.match_info['location_code']

            from .port_database import get_port_database

            port_db = get_port_database()
            if not port_db:
                return web.json_response({
                    "error": "端口位置码数据库未初始化"
                }, status=500)

            # 获取当前绑定信息
            binding = port_db.get_port_binding(location_code)
            if not binding:
                return web.json_response({
                    "error": f"端口未绑定设备: {location_code}"
                }, status=404)

            # 执行解绑
            success = port_db.unbind_device_from_port(binding.device_signature)

            if success:
                logger.info(f"设备解绑成功: {binding.device_signature} 从 {location_code}")
                return web.json_response({
                    "success": True,
                    "message": "设备解绑成功",
                    "location_code": location_code,
                    "device_signature": binding.device_signature
                })
            else:
                return web.json_response({
                    "error": "设备解绑失败"
                }, status=500)

        except Exception as e:
            logger.error(f"设备解绑失败: {e}")
            return web.json_response({
                "error": f"设备解绑失败: {str(e)}"
            }, status=500)
