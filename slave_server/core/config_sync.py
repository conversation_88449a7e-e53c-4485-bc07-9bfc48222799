#!/usr/bin/env python3
"""
从服务器配置同步模块
负责与主服务器同步设备配置和权限信息
"""

import asyncio
import aiohttp
import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from config_manager import SlaveConfigManager

logger = logging.getLogger(__name__)

class ConfigSyncManager:
    """配置同步管理器"""
    
    def __init__(self, master_url: str, hardware_uuid: str):
        self.master_url = master_url.rstrip('/')
        self.hardware_uuid = hardware_uuid
        self.config_manager = SlaveConfigManager()
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 同步状态
        self.last_sync_time: Optional[datetime] = None
        self.sync_interval = 300  # 5分钟
        self.sync_task: Optional[asyncio.Task] = None
        
        logger.info(f"配置同步管理器初始化: {master_url}")
    
    async def start(self):
        """启动配置同步服务"""
        try:
            # 创建HTTP会话
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # 启动定期同步任务
            self.sync_task = asyncio.create_task(self._sync_loop())
            
            logger.info("配置同步服务已启动")
            
        except Exception as e:
            logger.error(f"启动配置同步服务失败: {e}")
    
    async def stop(self):
        """停止配置同步服务"""
        try:
            # 停止同步任务
            if self.sync_task:
                self.sync_task.cancel()
                try:
                    await self.sync_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭HTTP会话
            if self.session:
                await self.session.close()
            
            logger.info("配置同步服务已停止")
            
        except Exception as e:
            logger.error(f"停止配置同步服务失败: {e}")
    
    async def _sync_loop(self):
        """定期同步循环"""
        while True:
            try:
                await asyncio.sleep(self.sync_interval)
                await self.sync_config_from_master()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"配置同步循环错误: {e}")
                await asyncio.sleep(60)  # 错误后等待1分钟再重试
    
    async def sync_config_from_master(self) -> bool:
        """从主服务器同步配置"""
        try:
            if not self.session:
                logger.warning("HTTP会话未初始化，跳过配置同步")
                return False
            
            # 获取配置API URL
            config_url = f"{self.master_url}/api/v1/slave/{self.hardware_uuid}/config"
            
            async with self.session.get(config_url) as response:
                if response.status == 200:
                    config_data = await response.json()
                    
                    # 处理配置数据
                    await self._process_master_config(config_data)
                    
                    self.last_sync_time = datetime.now()
                    logger.info(f"配置同步成功，版本: {config_data.get('config_version', 'unknown')}")
                    return True
                    
                elif response.status == 404:
                    logger.warning("从服务器在主服务器中未找到，可能需要重新注册")
                    return False
                    
                else:
                    logger.error(f"获取配置失败，状态码: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"从主服务器同步配置失败: {e}")
            return False
    
    async def _process_master_config(self, config_data: Dict[str, Any]):
        """处理主服务器配置数据"""
        try:
            config_version = config_data.get('config_version', 1)
            devices_config = config_data.get('devices', [])
            
            # 检查配置版本
            local_version = self.config_manager.get_config_version()
            if config_version <= local_version:
                logger.debug(f"配置版本无变化: {config_version}")
                return
            
            # 更新设备配置
            device_configs = {}
            for device_config in devices_config:
                hardware_signature = device_config.get('hardware_signature')
                if hardware_signature:
                    device_configs[hardware_signature] = {
                        'custom_name': device_config.get('custom_name'),
                        'notes': device_config.get('notes'),
                        'config_data': device_config.get('config_data', {}),
                        'device_group': device_config.get('device_group'),
                        'permissions': device_config.get('permissions', {}),
                        'last_updated': datetime.now().isoformat()
                    }
            
            # 批量更新设备配置
            if device_configs:
                self.config_manager.update_device_configs(device_configs)
                logger.info(f"更新了 {len(device_configs)} 个设备配置")
            
            # 更新配置版本
            self.config_manager.set_config_version(config_version)
            
        except Exception as e:
            logger.error(f"处理主服务器配置数据失败: {e}")
    
    async def sync_devices_to_master(self, devices: List[Dict[str, Any]]) -> bool:
        """向主服务器同步设备清单"""
        try:
            if not self.session:
                logger.warning("HTTP会话未初始化，跳过设备同步")
                return False
            
            # 构建同步请求
            sync_request = {
                'hardware_uuid': self.hardware_uuid,
                'devices': devices,
                'sync_timestamp': datetime.now().isoformat()
            }
            
            # 发送同步请求
            sync_url = f"{self.master_url}/api/v1/slave/{self.hardware_uuid}/sync-devices"
            
            async with self.session.post(sync_url, json=sync_request) as response:
                if response.status == 200:
                    result = await response.json()
                    sync_results = result.get('sync_results', {})
                    
                    logger.info(f"设备同步成功: 创建 {sync_results.get('created', 0)}, "
                              f"更新 {sync_results.get('updated', 0)}, "
                              f"未变更 {sync_results.get('unchanged', 0)}")
                    
                    # 更新本地配置版本
                    new_version = result.get('config_version')
                    if new_version:
                        self.config_manager.set_config_version(new_version)
                    
                    return True
                    
                else:
                    logger.error(f"设备同步失败，状态码: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"向主服务器同步设备失败: {e}")
            return False
    
    def format_device_for_sync(self, device: Dict[str, Any]) -> Dict[str, Any]:
        """格式化设备信息用于同步"""
        try:
            return {
                'hardware_signature': device.get('hardware_signature', ''),
                'physical_port': device.get('physical_port', ''),
                'vendor_id': device.get('vendor_id', ''),
                'product_id': device.get('product_id', ''),
                'serial_number': device.get('serial_number'),
                'description': device.get('description', ''),
                'device_path': device.get('device_path'),
                'custom_name': device.get('custom_name'),
                'notes': device.get('notes'),
                'config_data': device.get('config_data')
            }
        except Exception as e:
            logger.error(f"格式化设备信息失败: {e}")
            return {}
    
    async def force_sync(self) -> bool:
        """强制执行一次完整同步"""
        try:
            logger.info("开始强制配置同步...")
            
            # 1. 从主服务器获取最新配置
            config_success = await self.sync_config_from_master()
            
            # 2. 如果有USB管理器，同步设备清单
            # 这里需要从外部传入设备列表，或者通过回调获取
            
            if config_success:
                logger.info("强制配置同步完成")
                return True
            else:
                logger.warning("强制配置同步部分失败")
                return False
                
        except Exception as e:
            logger.error(f"强制配置同步失败: {e}")
            return False
    
    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        return {
            'last_sync_time': self.last_sync_time.isoformat() if self.last_sync_time else None,
            'sync_interval': self.sync_interval,
            'config_version': self.config_manager.get_config_version(),
            'is_running': self.sync_task is not None and not self.sync_task.done(),
            'master_url': self.master_url,
            'hardware_uuid': self.hardware_uuid
        }
    
    def set_sync_interval(self, interval: int):
        """设置同步间隔（秒）"""
        if interval >= 60:  # 最小1分钟
            self.sync_interval = interval
            logger.info(f"同步间隔已设置为: {interval} 秒")
        else:
            logger.warning("同步间隔不能小于60秒")

# 全局实例（需要在初始化时设置）
config_sync_manager: Optional[ConfigSyncManager] = None

def initialize_config_sync(master_url: str, hardware_uuid: str) -> ConfigSyncManager:
    """初始化配置同步管理器"""
    global config_sync_manager
    config_sync_manager = ConfigSyncManager(master_url, hardware_uuid)
    return config_sync_manager

def get_config_sync_manager() -> Optional[ConfigSyncManager]:
    """获取配置同步管理器实例"""
    return config_sync_manager
