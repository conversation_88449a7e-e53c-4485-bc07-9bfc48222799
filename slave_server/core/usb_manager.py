#!/usr/bin/env python3
"""
事件驱动USB设备管理器
实现高性能USB设备监控和管理
支持热插拔事件检测，响应时间<50ms
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, asdict
from datetime import datetime
from .device_signature import create_device_identity, detect_device_movement
from .config_manager import SlaveConfigManager
from .usb_topology import get_current_usb_topology
from .port_location import get_port_location_manager, initialize_port_location_manager
from .port_database import get_port_database

logger = logging.getLogger(__name__)

@dataclass
class USBDevice:
    """USB设备数据结构"""
    vendor_id: int
    product_id: int
    bus: int
    address: int
    description: str
    serial_number: Optional[str] = None
    device_path: Optional[str] = None
    status: str = "online"
    detected_at: Optional[datetime] = None
    # 新增设备签名字段
    hardware_signature: str = ""
    physical_port: str = ""
    custom_name: str = ""
    notes: str = ""
    config_data: Optional[Dict[str, Any]] = None
    # 端口位置码字段
    port_location_code: str = ""
    port_bound: bool = False
    binding_time: Optional[str] = None
    
    def __post_init__(self):
        if self.detected_at is None:
            self.detected_at = datetime.now()
        if self.config_data is None:
            self.config_data = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，兼容原有API"""
        return {
            'vendor_id': self.vendor_id,
            'product_id': self.product_id,
            'bus': self.bus,
            'address': self.address,
            'description': self.description,
            'serial_number': self.serial_number,
            'device_path': self.device_path,
            'status': self.status,
            'detected_at': self.detected_at.isoformat() if self.detected_at else None,
            'hardware_signature': self.hardware_signature,
            'physical_port': self.physical_port,
            'custom_name': self.custom_name,
            'notes': self.notes,
            'config_data': self.config_data,
            'port_location_code': self.port_location_code,
            'port_bound': self.port_bound,
            'binding_time': self.binding_time
        }

class EventDrivenUSBManager:
    """事件驱动USB设备管理器"""

    def __init__(self):
        self.devices: Dict[str, USBDevice] = {}
        self.event_queue = asyncio.Queue(maxsize=1000)
        self.callbacks: List[Callable] = []
        self.monitoring = False
        self.scan_interval = 30.0  # 大幅降低扫描频率，减少资源占用
        self._device_states = {}      # 存储设备状态信息
        self._state_change_callbacks = []  # 状态变化回调函数
        self._last_device_snapshot = {}  # 上次设备快照
        self._last_scan_time = 0
        self._scan_cache = {}

        # 配置管理器
        self.config_manager = SlaveConfigManager()

        # 设备身份缓存
        self._device_identities: Dict[str, Dict[str, str]] = {}
        self._device_movements: List[Dict[str, Any]] = []

        # 端口位置码管理器
        self.port_location_manager = None
        self._server_uuid = None
        
    def _generate_device_id(self, device: USBDevice) -> str:
        """生成设备唯一ID"""
        return f"{device.vendor_id:04x}:{device.product_id:04x}:{device.bus}:{device.address}"

    def _enhance_device_with_signature(self, device: USBDevice) -> USBDevice:
        """为设备添加硬件签名和配置信息"""
        try:
            # 构建设备信息字典
            device_info = {
                'vendor_id': f"{device.vendor_id:04x}",
                'product_id': f"{device.product_id:04x}",
                'serial_number': device.serial_number or '',
                'description': device.description,
                'manufacturer': '',  # 可以从描述中提取
                'bus': str(device.bus),
                'address': str(device.address),
                'device_path': device.device_path or '',
                'device_id': self._generate_device_id(device)
            }

            # 生成设备身份信息
            identity = create_device_identity(device_info)

            # 更新设备签名字段
            device.hardware_signature = identity['hardware_signature']
            device.physical_port = identity['physical_port']

            # 检测设备类型和是否为真实硬件设备
            device_classification = self._classify_device(device)
            device.device_type = device_classification['type']
            device.is_real_hardware = device_classification['is_real_hardware']
            device.auto_bind_eligible = device_classification['auto_bind_eligible']

            # 生成自动设备名称（如果没有自定义名称）
            if not device.custom_name and device.auto_bind_eligible:
                device.auto_generated_name = self._generate_device_name(device, device_classification)

            # 生成端口位置码
            if self.port_location_manager:
                device.port_location_code = self._generate_device_location_code(device)

                # 检查是否已有绑定配置
                existing_location = self.port_location_manager.get_device_location(device.hardware_signature)
                if existing_location:
                    device.port_location_code = existing_location
                    device.port_bound = True

            # 从配置管理器加载设备配置
            cached_config = self.config_manager.get_device_config(device.hardware_signature)
            if cached_config:
                device.custom_name = cached_config.get('custom_name', '')
                device.notes = cached_config.get('notes', '')
                device.config_data = cached_config.get('config_data', {})
                logger.debug(f"从缓存加载设备配置: {device.hardware_signature}")

            # 检测设备移动
            old_identity = self._device_identities.get(device.hardware_signature)
            if old_identity:
                movement_info = detect_device_movement(old_identity, identity)
                if movement_info['is_moved']:
                    self._device_movements.append({
                        'hardware_signature': device.hardware_signature,
                        'movement_info': movement_info,
                        'timestamp': datetime.now().isoformat()
                    })
                    logger.info(f"检测到设备移动: {device.hardware_signature} -> {device.physical_port}")

            # 更新身份缓存
            self._device_identities[device.hardware_signature] = identity

            # 设置设备状态和最后发现时间
            device.status = "online" if device.is_real_hardware else "idle"
            device.last_seen = datetime.now().isoformat()

            # 确保端口位置码存在
            if not hasattr(device, 'port_location_code') or not device.port_location_code:
                server_name = getattr(self, 'server_name', 'OmniLink-Slave')
                device.port_location_code = f"{server_name}-B{device.bus}P{device.address}"

            return device

        except Exception as e:
            logger.error(f"增强设备签名失败: {e}")
            # 降级方案：使用基本信息
            device.hardware_signature = f"fallback_{hash(str(device.vendor_id) + str(device.product_id))}_{device.bus}_{device.address}"[-16:]
            device.physical_port = f"BUS:{device.bus}|ADDR:{device.address}"
            return device

    def _classify_device(self, device: USBDevice) -> Dict[str, Any]:
        """分类USB设备，识别真实硬件设备"""
        try:
            vendor_id = device.vendor_id
            product_id = device.product_id
            description = (device.description or "").lower()

            logger.info(f"开始分类设备: VID=0x{vendor_id:04X}, PID=0x{product_id:04X}, 描述='{device.description}'")

            # 设备分类规则
            classification = {
                'type': 'unknown',
                'is_real_hardware': True,
                'auto_bind_eligible': True,
                'confidence': 0.5,
                'reason': 'default_classification'
            }

            # 扩展的虚拟设备和系统设备排除规则
            virtual_indicators = [
                # 基础虚拟设备
                'virtual', 'composite', 'root', 'generic', 'standard',
                'microsoft', 'windows', 'system', 'internal',

                # USB复合设备和子设备
                'usb composite device', 'composite device', 'usb device',
                # 注意：不包含'shared device'和'usbip shared device'，因为这可能是真实的USB设备通过USBIP共享

                # 系统内置设备
                'built-in', 'integrated', 'onboard', 'chipset',
                'host controller', 'root hub', 'usb root hub',

                # 虚拟化相关
                'vmware', 'virtualbox', 'hyper-v', 'qemu', 'kvm',
                'virtual machine', 'vm guest', 'guest additions',

                # 无用的通用设备
                'unknown device', 'unspecified', 'other device',
                'usb input device', 'hid-compliant', 'human interface',

                # 中文系统设备
                '复合设备', '通用', '标准', '系统', '内置', '虚拟'
            ]

            # 检查设备描述是否包含虚拟设备标识
            description_lower = description.lower()
            is_virtual_device = any(indicator.lower() in description_lower for indicator in virtual_indicators)

            # 排除特定的无用VID（系统厂商）
            system_vendors = {
                0x1d6b,  # Linux Foundation (USB hubs)
                0x0424,  # Standard Microsystems Corp (USB hubs)
                0x8087,  # Intel Corp (内置USB控制器)
                0x80ee,  # VirtualBox (虚拟设备)
                0x0e0f,  # VMware (虚拟设备)
            }

            is_system_vendor = vendor_id in system_vendors

            # 如果是虚拟设备或系统厂商设备，标记但继续分类
            if is_virtual_device or is_system_vendor:
                classification.update({
                    'is_real_hardware': False,
                    'auto_bind_eligible': False,
                    'reason': 'virtual_or_system_device' if is_virtual_device else 'system_vendor_device'
                })
                # 继续处理以确定具体的设备类型

            # HUB设备检测
            if 'hub' in description or vendor_id in [0x1d6b, 0x0424]:  # Linux Foundation, SMSC
                classification.update({
                    'type': 'hub',
                    'is_real_hardware': True,  # Hub是真实硬件，但不适合自动绑定
                    'auto_bind_eligible': False,
                    'reason': 'usb_hub_device'
                })
                return classification  # Hub设备可以直接返回，因为类型已确定

            # 扩展的加密锁和安全设备检测（修复重复键问题）
            encryption_vendors = {
                # 主流加密锁厂商
                0x096e: 'feitian_group',      # 飞天诚信（包含Rockey系列、广联达合作、CA锁）
                0x0471: 'philips',            # 飞利浦（部分加密锁）
                0x1234: 'generic_lock',       # 通用加密锁厂商
                0x0a89: 'aktiv_group',        # Aktiv（包含Aktiv Co.、博威、银行U盾）
                0x20a0: 'clay_logic',         # Clay Logic
                0x1bc0: 'senseshield_group',  # 深思数盾（包含SenseShield、新点软件）
                0x1a2c: 'china_iwncomm_group', # 西电捷通（包含广联达、数字证书设备）
                0x0557: 'aten',               # ATEN（部分加密设备）

                # 其他知名加密锁厂商
                0x0416: 'winbond',            # 华邦电子
                0x04e6: 'sct',                # SCT Microsystems
                0x072f: 'advanced_card',      # Advanced Card Systems
                0x0b97: 'o2micro',            # O2 Micro
                0x0dc3: 'athena',             # Athena Smartcard Solutions
                0x17ef: 'lenovo_security',    # 联想安全设备
                0x1f3a: 'eutron',             # Eutron
            }

            # 检查是否为加密锁或安全设备
            if vendor_id in encryption_vendors:
                vendor_name = encryption_vendors[vendor_id]
                logger.info(f"检测到加密锁设备: VID=0x{vendor_id:04X}, 厂商={vendor_name}, 描述={description}")
                classification.update({
                    'type': 'encryption_key',
                    'confidence': 0.9,
                    'reason': f'known_encryption_vendor_{vendor_name}',
                    'is_real_hardware': True,
                    'auto_bind_eligible': True
                })
                return classification

            # 通过设备描述识别加密锁
            encryption_keywords = [
                'senseshield', 'rockey', 'dongle', 'hasp', 'sentinel',
                'safenet', 'aladdin', 'rainbow', 'wibu', 'codemeter',
                'glodon', 'newpoint', 'bowei', '广联达', '新点', '博威',
                'ca lock', 'ukey', 'digital certificate', '数字证书',
                'encryption', 'security key', '加密锁', '安全锁'
            ]

            if any(keyword in description for keyword in encryption_keywords):
                classification.update({
                    'type': 'encryption_key',
                    'confidence': 0.8,
                    'reason': 'encryption_keyword_detected'
                })
                return classification

            # 打印机和扫描仪检测
            printer_vendors = {
                0x03f0: 'hp',           # HP
                0x04e8: 'samsung',      # Samsung
                0x04a9: 'canon',        # Canon
                0x04b8: 'epson',        # Epson
                0x0924: 'xerox',        # Xerox
                0x04f9: 'brother',      # Brother
                0x04da: 'panasonic',    # Panasonic
            }

            printer_keywords = [
                'printer', 'print', 'scanner', 'scan', 'multifunction',
                'laserjet', 'inkjet', 'deskjet', 'officejet', 'photosmart',
                '打印机', '扫描仪', '一体机', '激光', '喷墨'
            ]

            if (vendor_id in printer_vendors or
                any(keyword in description for keyword in printer_keywords)):
                classification.update({
                    'type': 'printer_scanner',
                    'confidence': 0.9,
                    'reason': 'printer_scanner_detected'
                })
                return classification

            # 存储设备检测（扩展版本）
            storage_vendors = {
                0x0781: 'sandisk',      # SanDisk
                0x058f: 'alcor',        # Alcor Micro
                0x090c: 'silicon_motion', # Silicon Motion
                0x13fe: 'kingston',     # Kingston
                0x0951: 'kingston_dt',  # Kingston DataTraveler
                0x8564: 'transcend',    # Transcend
                0x0930: 'toshiba',      # Toshiba
                0x0bc2: 'seagate',      # Seagate
                0x1058: 'wd',           # Western Digital
            }

            storage_keywords = [
                'storage', 'disk', 'flash', 'memory', 'drive', 'usb drive',
                'mass storage', 'removable', 'portable', 'thumb drive',
                '存储', '硬盘', '闪存', 'u盘', '移动硬盘'
            ]

            if (vendor_id in storage_vendors or
                any(keyword in description for keyword in storage_keywords)):
                classification.update({
                    'type': 'storage',
                    'confidence': 0.8,
                    'reason': 'storage_device_detected'
                })
                return classification

            # 输入设备检测
            if any(keyword in description for keyword in ['mouse', 'keyboard', 'hid']):
                classification.update({
                    'type': 'input',
                    'confidence': 0.8,
                    'reason': 'input_device_keywords'
                })
                return classification

            # 通信设备检测
            if any(keyword in description for keyword in ['serial', 'comm', 'modem', 'bluetooth']):
                classification.update({
                    'type': 'communication',
                    'confidence': 0.7,
                    'reason': 'communication_device_keywords'
                })
                return classification

            # 默认为未知设备，但保持现有的is_real_hardware和auto_bind_eligible设置
            if classification['type'] == 'unknown':
                if is_virtual_device or is_system_vendor:
                    classification.update({
                        'type': 'virtual' if is_virtual_device else 'system',
                        'confidence': 0.8,
                        'reason': classification.get('reason', 'virtual_or_system_device')
                    })
                else:
                    classification.update({
                        'type': 'unknown',
                        'confidence': 0.6,
                        'reason': 'unknown_hardware_device'
                    })

            return classification

        except Exception as e:
            logger.debug(f"设备分类失败: {e}")
            return {
                'type': 'unknown',
                'is_real_hardware': True,
                'auto_bind_eligible': True,
                'confidence': 0.1,
                'reason': 'classification_error'
            }

    def _generate_device_name(self, device: USBDevice, classification: Dict[str, Any]) -> str:
        """生成设备自动命名"""
        try:
            # 获取从服务器名称（使用统一命名函数）
            server_name = getattr(self, 'server_name', None)
            if not server_name:
                # 如果没有设置服务器名称，使用统一命名函数生成
                server_name = self._generate_unified_server_name()

            # 生成端口地址
            port_address = f"Bus{device.bus}Port{device.address}"

            # 根据设备类型生成类型标识
            type_mapping = {
                'encryption_key': 'EncryptionKey',
                'storage': 'Storage',
                'input': 'Input',
                'communication': 'Comm',
                'printer_scanner': 'Printer',
                'hub': 'Hub',
                'hardware': 'Device',
                'unknown': 'Device'
            }

            device_type_label = type_mapping.get(classification['type'], 'Device')

            # 生成基础名称
            base_name = f"{server_name}-{port_address}-{device_type_label}"

            # 确保名称唯一性
            unique_name = self._ensure_name_uniqueness(base_name, device.hardware_signature)

            return unique_name

        except Exception as e:
            logger.debug(f"生成设备名称失败: {e}")
            return f"Device-{device.hardware_signature[:8]}"

    def _ensure_name_uniqueness(self, base_name: str, hardware_signature: str) -> str:
        """确保设备名称唯一性"""
        try:
            # 检查当前设备列表中是否有重名
            existing_names = set()
            for existing_device in self.devices:
                if existing_device.hardware_signature != hardware_signature:
                    if hasattr(existing_device, 'auto_generated_name') and existing_device.auto_generated_name:
                        existing_names.add(existing_device.auto_generated_name)
                    if existing_device.custom_name:
                        existing_names.add(existing_device.custom_name)

            # 如果没有重名，直接返回
            if base_name not in existing_names:
                return base_name

            # 如果有重名，添加序号
            counter = 1
            while f"{base_name}-{counter:02d}" in existing_names:
                counter += 1

            return f"{base_name}-{counter:02d}"

        except Exception as e:
            logger.debug(f"确保名称唯一性失败: {e}")
            return f"{base_name}-{hardware_signature[:4]}"

    def save_device_config(self, hardware_signature: str, config: Dict[str, Any]):
        """保存设备配置到本地缓存"""
        try:
            self.config_manager.set_device_config(hardware_signature, config)
            logger.debug(f"设备配置已保存: {hardware_signature}")
        except Exception as e:
            logger.error(f"保存设备配置失败: {e}")

    def get_device_movements(self) -> List[Dict[str, Any]]:
        """获取设备移动记录"""
        return self._device_movements.copy()

    def clear_device_movements(self):
        """清空设备移动记录"""
        self._device_movements.clear()

    def initialize_port_location_system(self, server_uuid: str):
        """初始化端口位置码系统"""
        try:
            self._server_uuid = server_uuid
            self.port_location_manager = initialize_port_location_manager(server_uuid)

            # 连接数据库
            port_db = get_port_database()
            if port_db:
                self.port_location_manager.set_database(port_db)

            # 获取当前USB拓扑并更新位置码
            topology = get_current_usb_topology(force_refresh=True)
            location_mapping = self.port_location_manager.update_topology_locations(topology)

            logger.info(f"端口位置码系统初始化完成，生成 {len(location_mapping)} 个位置码")
            return True

        except Exception as e:
            logger.error(f"端口位置码系统初始化失败: {e}")
            return False

    def bind_device_to_port(self, device_id: str, custom_name: str = "", notes: str = "") -> bool:
        """将设备绑定到当前端口（端口独占模式）"""
        try:
            if not self.port_location_manager:
                logger.warning("端口位置码管理器未初始化")
                return False

            device = self.devices.get(device_id)
            if not device:
                logger.warning(f"设备不存在: {device_id}")
                return False

            # 生成或获取端口位置码
            if not device.port_location_code:
                # 基于设备的USB拓扑信息生成位置码
                location_code = self._generate_device_location_code(device)
                device.port_location_code = location_code

            # 执行端口绑定
            success = self.port_location_manager.bind_device_to_location(
                device.port_location_code,
                device.hardware_signature,
                confidence=1.0
            )

            if success:
                device.port_bound = True
                device.binding_time = datetime.now().isoformat()
                device.custom_name = custom_name
                device.notes = notes

                # 保存设备配置
                self.save_device_config(device.hardware_signature, {
                    'custom_name': custom_name,
                    'notes': notes,
                    'port_location_code': device.port_location_code,
                    'port_bound': True,
                    'binding_time': device.binding_time
                })

                logger.info(f"设备端口绑定成功: {device_id} -> {device.port_location_code}")
                return True
            else:
                logger.error(f"设备端口绑定失败: {device_id}")
                return False

        except Exception as e:
            logger.error(f"设备端口绑定异常: {e}")
            return False

    def unbind_device_from_port(self, device_id: str) -> bool:
        """解除设备与端口的绑定"""
        try:
            if not self.port_location_manager:
                logger.warning("端口位置码管理器未初始化")
                return False

            device = self.devices.get(device_id)
            if not device:
                logger.warning(f"设备不存在: {device_id}")
                return False

            # 解除绑定
            success = self.port_location_manager.unbind_device_from_location(device.hardware_signature)

            if success:
                device.port_bound = False
                device.binding_time = None
                device.custom_name = ""
                device.notes = ""

                # 清除设备配置
                self.save_device_config(device.hardware_signature, {
                    'custom_name': '',
                    'notes': '',
                    'port_location_code': device.port_location_code,
                    'port_bound': False,
                    'binding_time': None
                })

                logger.info(f"设备端口解绑成功: {device_id}")
                return True
            else:
                logger.error(f"设备端口解绑失败: {device_id}")
                return False

        except Exception as e:
            logger.error(f"设备端口解绑异常: {e}")
            return False

    def _generate_device_location_code(self, device: USBDevice) -> str:
        """为设备生成端口位置码"""
        try:
            if not self.port_location_manager:
                return f"UNKNOWN-P{device.address:02d}"

            # 基于设备的总线和地址信息推断位置
            controller_id = f"CTRL{device.bus - 1}" if device.bus > 0 else "CTRL0"
            hub_id = f"HUB{device.bus - 1}" if device.bus > 0 else "HUB0"
            port_number = device.address

            return self.port_location_manager.generate_location_code(
                controller_id, hub_id, port_number
            )

        except Exception as e:
            logger.error(f"生成设备位置码失败: {e}")
            return f"ERROR-P{device.address:02d}"
    
    async def start(self):
        """启动USB监控"""
        if self.monitoring:
            logger.warning("USB监控已在运行")
            return
            
        self.monitoring = True
        
        # 启动监控任务
        asyncio.create_task(self._monitor_devices())
        asyncio.create_task(self._process_events())
        
        logger.info("事件驱动USB设备监控已启动")
    
    async def stop(self):
        """停止USB监控"""
        self.monitoring = False
        logger.info("USB设备监控已停止")
    
    async def _monitor_devices(self):
        """监控USB设备变化"""
        while self.monitoring:
            try:
                start_time = time.time()
                current_devices = await self._scan_devices()
                await self._detect_changes(current_devices)
                
                # 动态调整扫描间隔
                scan_duration = time.time() - start_time
                sleep_time = max(0.5, self.scan_interval - scan_duration)
                await asyncio.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"USB设备监控错误: {e}")
                await asyncio.sleep(5)  # 错误时延长等待时间
    
    async def _scan_devices(self) -> List[USBDevice]:
        """扫描当前USB设备"""
        devices = []
        current_time = time.time()
        
        # 缓存机制：如果距离上次扫描时间很短，返回缓存结果
        if current_time - self._last_scan_time < 1.0 and self._scan_cache:
            return self._scan_cache.get('devices', [])
        
        try:
            # 智能USB设备扫描，支持多种backend
            import usb.core

            # 尝试多种backend，按优先级排序
            backends_to_try = []

            # 1. 尝试libusb1 backend
            try:
                import usb.backend.libusb1
                backend = usb.backend.libusb1.get_backend()
                if backend:
                    backends_to_try.append(('libusb1', backend))
            except:
                pass

            # 2. 尝试libusb0 backend
            try:
                import usb.backend.libusb0
                backend = usb.backend.libusb0.get_backend()
                if backend:
                    backends_to_try.append(('libusb0', backend))
            except:
                pass

            # 3. 尝试openusb backend
            try:
                import usb.backend.openusb
                backend = usb.backend.openusb.get_backend()
                if backend:
                    backends_to_try.append(('openusb', backend))
            except:
                pass

            # 4. 最后尝试默认backend
            backends_to_try.append(('default', None))

            # 尝试每个backend
            for backend_name, backend in backends_to_try:
                try:
                    logger.debug(f"尝试USB backend: {backend_name}")
                    device_list = list(usb.core.find(find_all=True, backend=backend))

                    for device in device_list:
                        try:
                            # 获取设备描述
                            description = f"USB Device {device.idVendor:04x}:{device.idProduct:04x}"

                            # 尝试获取序列号
                            serial_number = None
                            try:
                                if hasattr(device, 'serial_number') and device.serial_number:
                                    serial_number = device.serial_number
                            except:
                                pass  # 某些设备无法读取序列号

                            usb_device = USBDevice(
                                vendor_id=device.idVendor,
                                product_id=device.idProduct,
                                bus=device.bus,
                                address=device.address,
                                description=description,
                                serial_number=serial_number
                            )
                            # 增强设备签名和配置
                            usb_device = self._enhance_device_with_signature(usb_device)
                            devices.append(usb_device)

                        except Exception as e:
                            logger.debug(f"处理USB设备时出错: {e}")
                            continue

                    # 如果成功找到设备或backend工作正常，记录并跳出
                    if devices or device_list is not None:
                        logger.info(f"USB扫描成功，使用backend: {backend_name}，找到 {len(devices)} 个设备")
                        break

                except Exception as e:
                    logger.debug(f"Backend {backend_name} 失败: {e}")
                    continue

            # 如果所有backend都失败，记录一次警告
            if not devices and not hasattr(self, '_backend_warning_logged'):
                logger.warning("所有USB backend都不可用，将使用Windows WMI作为备选方案")
                self._backend_warning_logged = True
                    
        except ImportError:
            logger.info("pyusb未安装，使用Windows WMI扫描USB设备")
            devices = await self._scan_devices_wmi()
            if not devices:
                logger.info("当前系统无USB设备或WMI不可用")
                # 不使用模拟数据，返回空列表
        except Exception as e:
            if "No backend available" in str(e):
                logger.warning("USB backend不可用，无法扫描USB设备")
                devices = []
            else:
                logger.error(f"扫描USB设备失败: {e}")
        
        # 检测设备变化
        device_dict = {device.hardware_signature: device for device in devices}
        self._detect_device_changes(device_dict)

        # 更新缓存
        self._last_scan_time = current_time
        self._scan_cache = {'devices': devices, 'timestamp': current_time}

        return devices

    async def _scan_devices_wmi(self) -> List[USBDevice]:
        """使用Windows WMI扫描USB设备（优化版，支持WSL）"""
        devices = []
        try:
            import subprocess
            import re
            import platform

            # 检测运行环境
            is_wsl = False
            try:
                with open('/proc/version', 'r') as f:
                    if 'microsoft' in f.read().lower():
                        is_wsl = True
            except:
                pass

            # 根据环境选择不同的命令
            if is_wsl:
                # WSL环境：通过powershell.exe调用Windows PowerShell
                cmd = [
                    'powershell.exe', '-Command',
                    '''
                    Get-PnpDevice -Class USB | Where-Object { $_.InstanceId -like "*VID_*" -and $_.InstanceId -like "*PID_*" } |
                    Select-Object FriendlyName, InstanceId, Status |
                    ConvertTo-Json
                    '''
                ]
            elif platform.system() == 'Windows':
                # 原生Windows环境
                cmd = [
                    'powershell', '-Command',
                    '''
                    Get-WmiObject -Class Win32_PnPEntity |
                    Where-Object { $_.DeviceID -like "*USB*" -and $_.DeviceID -like "*VID_*" -and $_.DeviceID -like "*PID_*" } |
                    Select-Object DeviceID, Name, Description, Manufacturer |
                    ConvertTo-Json
                    '''
                ]
            else:
                return devices

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)

            if result.returncode == 0 and result.stdout.strip():
                import json
                try:
                    wmi_devices = json.loads(result.stdout)
                    if not isinstance(wmi_devices, list):
                        wmi_devices = [wmi_devices] if wmi_devices else []

                    for i, wmi_device in enumerate(wmi_devices):
                        if is_wsl:
                            # WSL环境：处理Get-PnpDevice的输出格式
                            device_id = wmi_device.get('InstanceId', '')
                            name = wmi_device.get('FriendlyName', '')
                            status = wmi_device.get('Status', '')

                            # 跳过非正常状态的设备
                            if status not in ['OK', 'Unknown']:
                                continue

                            device_desc = name or 'Unknown USB Device'
                        else:
                            # 原生Windows环境：处理Win32_PnPEntity的输出格式
                            device_id = wmi_device.get('DeviceID', '')
                            name = wmi_device.get('Name', '')
                            description = wmi_device.get('Description', '')
                            manufacturer = wmi_device.get('Manufacturer', '')

                            # 优先使用Name，然后Description
                            device_desc = name or description or 'Unknown USB Device'
                            if manufacturer and manufacturer not in device_desc:
                                device_desc = f"{manufacturer} {device_desc}"

                        # 从DeviceID/InstanceId中提取VID和PID
                        vid_match = re.search(r'VID_([0-9A-F]{4})', device_id, re.IGNORECASE)
                        pid_match = re.search(r'PID_([0-9A-F]{4})', device_id, re.IGNORECASE)

                        if vid_match and pid_match:
                            vendor_id = int(vid_match.group(1), 16)
                            product_id = int(pid_match.group(1), 16)

                            usb_device = USBDevice(
                                vendor_id=vendor_id,
                                product_id=product_id,
                                bus=1,  # WMI不提供总线信息，使用默认值
                                address=i + 1,  # 使用索引作为地址
                                description=device_desc,
                                device_path=device_id
                            )
                            # 增强设备签名和配置
                            usb_device = self._enhance_device_with_signature(usb_device)
                            devices.append(usb_device)

                except json.JSONDecodeError as e:
                    logger.debug(f"解析WMI JSON失败: {e}")
                except Exception as e:
                    logger.debug(f"处理WMI设备数据失败: {e}")

            if devices:
                logger.info(f"WMI扫描成功，找到 {len(devices)} 个USB设备")
            else:
                logger.debug("WMI扫描未找到USB设备")

        except Exception as e:
            logger.debug(f"WMI扫描失败: {e}")

        return devices

    async def _detect_changes(self, current_devices: List[USBDevice]):
        """检测设备变化"""
        # 构建当前设备ID集合
        current_device_ids = set()
        current_device_map = {}
        
        for device in current_devices:
            device_id = self._generate_device_id(device)
            current_device_ids.add(device_id)
            current_device_map[device_id] = device
        
        existing_device_ids = set(self.devices.keys())
        
        # 检测新增设备
        added_devices = current_device_ids - existing_device_ids
        for device_id in added_devices:
            device = current_device_map[device_id]
            await self.event_queue.put(('device_added', device))
        
        # 检测移除设备
        removed_devices = existing_device_ids - current_device_ids
        for device_id in removed_devices:
            device = self.devices[device_id]
            await self.event_queue.put(('device_removed', device))
    
    async def _process_events(self):
        """处理USB事件"""
        while self.monitoring:
            try:
                # 使用超时避免阻塞
                event_type, device = await asyncio.wait_for(
                    self.event_queue.get(), timeout=1.0
                )
                
                if event_type == 'device_added':
                    await self._on_device_added(device)
                elif event_type == 'device_removed':
                    await self._on_device_removed(device)
                    
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"处理USB事件错误: {e}")
    
    async def _on_device_added(self, device: USBDevice):
        """设备添加处理"""
        device_id = self._generate_device_id(device)
        self.devices[device_id] = device
        
        logger.info(f"检测到新USB设备: {device.description} (VID:{device.vendor_id:04x} PID:{device.product_id:04x})")
        
        # 通知回调函数
        await self._notify_callbacks('device_added', device)
    
    async def _on_device_removed(self, device: USBDevice):
        """设备移除处理"""
        device_id = self._generate_device_id(device)
        if device_id in self.devices:
            del self.devices[device_id]
            
        logger.info(f"USB设备已移除: {device.description} (VID:{device.vendor_id:04x} PID:{device.product_id:04x})")
        
        # 通知回调函数
        await self._notify_callbacks('device_removed', device)
    
    async def _notify_callbacks(self, event_type: str, device: USBDevice):
        """通知回调函数"""
        for callback in self.callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event_type, device)
                else:
                    callback(event_type, device)
            except Exception as e:
                logger.error(f"回调函数执行错误: {e}")
    
    def add_callback(self, callback: Callable):
        """添加设备变化回调"""
        self.callbacks.append(callback)
        logger.debug(f"已添加USB设备变化回调: {callback.__name__}")
    
    def remove_callback(self, callback: Callable):
        """移除设备变化回调"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
            logger.debug(f"已移除USB设备变化回调: {callback.__name__}")
    
    def get_devices(self) -> List[Dict[str, Any]]:
        """获取当前设备列表（兼容原有API格式）"""
        return [device.to_dict() for device in self.devices.values()]
    
    def get_device_count(self) -> int:
        """获取设备数量"""
        return len(self.devices)
    
    def get_device_by_id(self, device_id: str) -> Optional[USBDevice]:
        """根据ID获取设备"""
        return self.devices.get(device_id)
    
    async def refresh_devices(self):
        """手动刷新设备列表"""
        try:
            current_devices = await self._scan_devices()
            await self._detect_changes(current_devices)
            logger.debug("手动刷新USB设备列表完成")
        except Exception as e:
            logger.error(f"手动刷新设备列表失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_devices': len(self.devices),
            'monitoring_status': 'running' if self.monitoring else 'stopped',
            'event_queue_size': self.event_queue.qsize(),
            'callback_count': len(self.callbacks),
            'last_scan_time': self._last_scan_time,
            'scan_interval': self.scan_interval
        }

    def add_state_change_callback(self, callback: Callable):
        """添加设备状态变化回调函数"""
        self._state_change_callbacks.append(callback)

    def remove_state_change_callback(self, callback: Callable):
        """移除设备状态变化回调函数"""
        if callback in self._state_change_callbacks:
            self._state_change_callbacks.remove(callback)

    def _detect_device_changes(self, current_devices: Dict[str, USBDevice]):
        """检测设备变化并触发相应事件"""
        try:
            current_signatures = set(current_devices.keys())
            last_signatures = set(self._last_device_snapshot.keys())

            # 检测新增设备
            added_devices = current_signatures - last_signatures
            for signature in added_devices:
                device = current_devices[signature]
                if getattr(device, 'auto_bind_eligible', True):
                    self._trigger_device_event('device_added', device)
                    logger.info(f"检测到新设备: {getattr(device, 'auto_generated_name', '') or device.hardware_signature}")

            # 检测移除设备
            removed_devices = last_signatures - current_signatures
            for signature in removed_devices:
                device = self._last_device_snapshot[signature]
                if getattr(device, 'auto_bind_eligible', True):
                    self._trigger_device_event('device_removed', device)
                    logger.info(f"检测到设备移除: {getattr(device, 'auto_generated_name', '') or device.hardware_signature}")

            # 检测设备状态变化
            for signature in current_signatures & last_signatures:
                current_device = current_devices[signature]
                last_device = self._last_device_snapshot[signature]

                if self._has_device_state_changed(current_device, last_device):
                    self._trigger_device_event('device_changed', current_device)
                    logger.info(f"检测到设备状态变化: {getattr(current_device, 'auto_generated_name', '') or current_device.hardware_signature}")

            # 更新设备快照
            self._last_device_snapshot = current_devices.copy()

        except Exception as e:
            logger.error(f"检测设备变化失败: {e}")

    def _has_device_state_changed(self, current: USBDevice, last: USBDevice) -> bool:
        """检查设备状态是否发生变化"""
        try:
            # 检查关键状态字段
            state_fields = ['status', 'bus', 'address', 'device_path']

            for field in state_fields:
                if getattr(current, field, None) != getattr(last, field, None):
                    return True

            return False

        except Exception as e:
            logger.debug(f"检查设备状态变化失败: {e}")
            return False

    def _trigger_device_event(self, event_type: str, device: USBDevice):
        """触发设备事件"""
        try:
            event_data = {
                'type': event_type,
                'device': device,
                'timestamp': datetime.now().isoformat(),
                'server_id': getattr(self, 'server_id', 'unknown')
            }

            # 调用所有注册的回调函数
            for callback in self._state_change_callbacks:
                try:
                    callback(event_data)
                except Exception as e:
                    logger.error(f"设备状态变化回调执行失败: {e}")

            # 将事件加入队列
            try:
                self.event_queue.put_nowait(event_data)
            except asyncio.QueueFull:
                logger.warning("事件队列已满，丢弃旧事件")
                try:
                    self.event_queue.get_nowait()  # 移除最旧的事件
                    self.event_queue.put_nowait(event_data)  # 添加新事件
                except asyncio.QueueEmpty:
                    pass

        except Exception as e:
            logger.error(f"触发设备事件失败: {e}")

    def get_device_state_summary(self) -> Dict[str, Any]:
        """获取设备状态摘要"""
        try:
            total_devices = len(self.devices)
            real_hardware_devices = sum(1 for device in self.devices.values()
                                      if getattr(device, 'is_real_hardware', True))
            auto_bind_eligible = sum(1 for device in self.devices.values()
                                   if getattr(device, 'auto_bind_eligible', True))

            device_types = {}
            for device in self.devices.values():
                device_type = getattr(device, 'device_type', 'unknown')
                device_types[device_type] = device_types.get(device_type, 0) + 1

            return {
                'total_devices': total_devices,
                'real_hardware_devices': real_hardware_devices,
                'auto_bind_eligible': auto_bind_eligible,
                'device_types': device_types,
                'last_scan_time': self._last_scan_time,
                'monitoring_active': self.monitoring
            }

        except Exception as e:
            logger.error(f"获取设备状态摘要失败: {e}")
            return {
                'total_devices': 0,
                'real_hardware_devices': 0,
                'auto_bind_eligible': 0,
                'device_types': {},
                'last_scan_time': None,
                'monitoring_active': False
            }

    def _generate_unified_server_name(self):
        """
        生成统一的服务器名称
        与main.py中的generate_unique_server_name()保持一致
        """
        import uuid
        import time
        import hashlib
        import socket

        try:
            # 方案1：尝试获取硬件UUID（最稳定的标识）
            hardware_uuid = None
            try:
                from core.hardware_fingerprint import HardwareFingerprint
                fingerprint = HardwareFingerprint()
                hardware_uuid = fingerprint.generate_uuid()[:8]  # 取前8位作为标识
            except Exception:
                pass

            # 方案2：基于MAC地址生成唯一标识
            mac = uuid.getnode()
            mac_suffix = f"{mac:012x}"[-6:]  # 取MAC地址后6位

            # 方案3：基于主机名增加可识别性
            hostname = socket.gethostname()
            hostname_hash = hashlib.md5(hostname.encode()).hexdigest()[:4]

            # 方案4：基于端口号增加可识别性（默认端口）
            port_suffix = 8890

            # 方案5：基于时间戳确保唯一性
            timestamp = int(time.time() * 1000) % 100000  # 取时间戳后5位

            # 组合生成唯一名称（优先使用硬件UUID）
            if hardware_uuid:
                unique_name = f"OmniLink-Slave-{hardware_uuid}-{hostname_hash}-{port_suffix}"
            else:
                unique_name = f"OmniLink-Slave-{mac_suffix}-{hostname_hash}-{port_suffix}-{timestamp}"

            logger.info(f"USB管理器生成统一服务器名称: {unique_name}")
            return unique_name

        except Exception as e:
            # 降级方案：使用时间戳和随机数
            import random
            fallback_id = f"{int(time.time() % 10000)}-{random.randint(1000, 9999)}"
            fallback_name = f"OmniLink-Slave-{fallback_id}"
            logger.warning(f"USB管理器名称生成失败，使用降级方案: {fallback_name}, 错误: {e}")
            return fallback_name
