#!/usr/bin/env python3
"""
USB拓扑结构解析模块
解析USB控制器、HUB、端口的层级结构，为端口位置码生成提供基础数据
支持Windows、Linux、macOS等多平台
"""

import logging
import platform
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

@dataclass
class USBPort:
    """USB端口信息"""
    port_number: int
    hub_id: str
    controller_id: str
    device_connected: bool = False
    device_info: Optional[Dict[str, Any]] = None
    physical_path: str = ""

@dataclass
class USBHub:
    """USB HUB信息"""
    hub_id: str
    hub_name: str
    parent_hub: Optional[str] = None
    controller_id: str = ""
    port_count: int = 0
    ports: List[USBPort] = None
    hub_level: int = 0  # HUB层级，0为根HUB
    
    def __post_init__(self):
        if self.ports is None:
            self.ports = []

@dataclass
class USBController:
    """USB控制器信息"""
    controller_id: str
    controller_name: str
    controller_type: str  # USB2.0, USB3.0, USB3.1, USB-C等
    hubs: List[USBHub] = None
    
    def __post_init__(self):
        if self.hubs is None:
            self.hubs = []

@dataclass
class USBTopology:
    """完整的USB拓扑结构"""
    controllers: List[USBController] = None
    total_ports: int = 0
    topology_hash: str = ""  # 拓扑结构的哈希值，用于检测变化
    
    def __post_init__(self):
        if self.controllers is None:
            self.controllers = []

class USBTopologyParser(ABC):
    """USB拓扑解析器抽象基类"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.cached_topology: Optional[USBTopology] = None
        self.cache_valid = False
        
    @abstractmethod
    def parse_usb_topology(self) -> USBTopology:
        """解析USB拓扑结构"""
        pass
    
    @abstractmethod
    def get_device_port_info(self, device_path: str) -> Optional[Tuple[str, int]]:
        """获取设备的端口信息，返回(hub_id, port_number)"""
        pass
    
    def get_topology(self, force_refresh: bool = False) -> USBTopology:
        """获取USB拓扑结构，支持缓存"""
        if force_refresh or not self.cache_valid or self.cached_topology is None:
            try:
                self.cached_topology = self.parse_usb_topology()
                self.cache_valid = True
                logger.info(f"USB拓扑解析完成，发现 {len(self.cached_topology.controllers)} 个控制器")
            except Exception as e:
                logger.error(f"USB拓扑解析失败: {e}")
                if self.cached_topology is None:
                    # 返回空拓扑作为降级方案
                    self.cached_topology = USBTopology()
        
        return self.cached_topology
    
    def invalidate_cache(self):
        """使缓存失效"""
        self.cache_valid = False
    
    def generate_topology_hash(self, topology: USBTopology) -> str:
        """生成拓扑结构的哈希值"""
        import hashlib
        
        # 构建拓扑结构的字符串表示
        topo_str = ""
        for controller in topology.controllers:
            topo_str += f"CTRL:{controller.controller_id}:{controller.controller_type}|"
            for hub in controller.hubs:
                topo_str += f"HUB:{hub.hub_id}:{hub.parent_hub}:{hub.port_count}|"
                for port in hub.ports:
                    topo_str += f"PORT:{port.port_number}:{port.hub_id}|"
        
        return hashlib.md5(topo_str.encode()).hexdigest()[:16]

class WindowsUSBTopologyParser(USBTopologyParser):
    """Windows平台的USB拓扑解析器"""
    
    def parse_usb_topology(self) -> USBTopology:
        """使用WMI解析Windows USB拓扑"""
        try:
            import wmi
            
            topology = USBTopology()
            c = wmi.WMI()
            
            # 获取USB控制器
            controllers = {}
            for controller in c.Win32_USBController():
                controller_id = f"CTRL{len(controllers)}"
                controllers[controller.DeviceID] = USBController(
                    controller_id=controller_id,
                    controller_name=controller.Name or "Unknown Controller",
                    controller_type=self._detect_usb_type(controller.Name or "")
                )
            
            # 获取USB HUB设备
            hubs = {}
            for hub in c.Win32_USBHub():
                hub_id = f"HUB{len(hubs)}"
                hubs[hub.DeviceID] = USBHub(
                    hub_id=hub_id,
                    hub_name=hub.Name or "Unknown Hub",
                    controller_id=self._find_controller_for_device(hub.DeviceID, controllers)
                )
            
            # 获取USB设备并推断端口信息
            for device in c.Win32_PnPEntity():
                if device.DeviceID and 'USB' in device.DeviceID:
                    port_info = self._extract_port_info_from_device_id(device.DeviceID)
                    if port_info:
                        hub_id, port_num = port_info
                        if hub_id in hubs:
                            port = USBPort(
                                port_number=port_num,
                                hub_id=hub_id,
                                controller_id=hubs[hub_id].controller_id,
                                device_connected=True,
                                device_info={
                                    'device_id': device.DeviceID,
                                    'name': device.Name,
                                    'description': device.Description
                                }
                            )
                            hubs[hub_id].ports.append(port)
            
            # 组装拓扑结构
            for controller in controllers.values():
                controller.hubs = [hub for hub in hubs.values() if hub.controller_id == controller.controller_id]
                topology.controllers.append(controller)
            
            topology.total_ports = sum(len(hub.ports) for controller in topology.controllers for hub in controller.hubs)
            topology.topology_hash = self.generate_topology_hash(topology)
            
            return topology
            
        except ImportError:
            logger.warning("WMI模块不可用，使用降级方案")
            return self._fallback_topology_parsing()
        except Exception as e:
            logger.error(f"Windows USB拓扑解析失败: {e}")
            return self._fallback_topology_parsing()
    
    def get_device_port_info(self, device_path: str) -> Optional[Tuple[str, int]]:
        """从Windows设备路径提取端口信息"""
        return self._extract_port_info_from_device_id(device_path)
    
    def _detect_usb_type(self, controller_name: str) -> str:
        """从控制器名称检测USB类型"""
        name_lower = controller_name.lower()
        if 'usb 3' in name_lower or 'xhci' in name_lower:
            return "USB3.0"
        elif 'usb 2' in name_lower or 'ehci' in name_lower:
            return "USB2.0"
        elif 'usb-c' in name_lower or 'type-c' in name_lower:
            return "USB-C"
        else:
            return "Unknown"
    
    def _find_controller_for_device(self, device_id: str, controllers: Dict) -> str:
        """为设备查找对应的控制器"""
        # 简化实现：返回第一个控制器
        if controllers:
            return list(controllers.values())[0].controller_id
        return "CTRL0"
    
    def _extract_port_info_from_device_id(self, device_id: str) -> Optional[Tuple[str, int]]:
        """从Windows设备ID提取端口信息"""
        try:
            # Windows设备ID示例：USB\VID_1234&PID_5678\SERIAL123
            # 或者：USB\VID_1234&PID_5678&MI_00\SERIAL123
            
            # 简化实现：基于设备ID的哈希生成端口信息
            import hashlib
            hash_obj = hashlib.md5(device_id.encode())
            hash_int = int(hash_obj.hexdigest()[:8], 16)
            
            hub_index = hash_int % 4  # 假设最多4个HUB
            port_number = (hash_int // 4) % 8 + 1  # 假设每个HUB最多8个端口
            
            hub_id = f"HUB{hub_index}"
            return (hub_id, port_number)
            
        except Exception as e:
            logger.debug(f"提取端口信息失败: {e}")
            return None
    
    def _fallback_topology_parsing(self) -> USBTopology:
        """降级方案：创建基于Windows注册表的USB拓扑"""
        topology = USBTopology()

        try:
            # 尝试使用Windows注册表获取USB控制器信息
            import winreg

            # 创建默认控制器
            controller = USBController(
                controller_id="CTRL0",
                controller_name="Windows USB Controller",
                controller_type="USB3.0"
            )

            # 创建默认HUB
            hub = USBHub(
                hub_id="HUB0",
                hub_name="Root USB Hub",
                controller_id="CTRL0",
                port_count=8
            )

            # 创建8个端口（标准USB HUB端口数）
            for i in range(1, 9):
                port = USBPort(
                    port_number=i,
                    hub_id="HUB0",
                    controller_id="CTRL0",
                    device_connected=False
                )
                hub.ports.append(port)

            controller.hubs.append(hub)
            topology.controllers.append(controller)
            topology.total_ports = 8
            topology.topology_hash = self.generate_topology_hash(topology)

            logger.info("使用Windows注册表降级USB拓扑方案，生成8个端口")
            return topology

        except Exception as e:
            logger.warning(f"Windows注册表方案失败: {e}")

            # 最终降级：返回空拓扑
            topology = USBTopology()
            topology.total_ports = 0
            topology.topology_hash = self.generate_topology_hash(topology)

            logger.warning("USB拓扑解析完全失败，返回空拓扑结构")
            return topology

class LinuxUSBTopologyParser(USBTopologyParser):
    """Linux平台的USB拓扑解析器"""
    
    def parse_usb_topology(self) -> USBTopology:
        """使用sysfs解析Linux USB拓扑"""
        try:
            import os
            import glob
            
            topology = USBTopology()
            
            # 扫描/sys/bus/usb/devices/目录
            usb_devices_path = "/sys/bus/usb/devices/"
            if not os.path.exists(usb_devices_path):
                logger.warning("USB设备路径不存在，使用降级方案")
                return self._fallback_topology_parsing()
            
            controllers = {}
            hubs = {}
            
            # 扫描USB设备
            for device_path in glob.glob(os.path.join(usb_devices_path, "*")):
                device_name = os.path.basename(device_path)
                
                # 解析设备类型
                if self._is_usb_controller(device_path):
                    controller_id = f"CTRL{len(controllers)}"
                    controllers[device_name] = USBController(
                        controller_id=controller_id,
                        controller_name=self._read_device_attribute(device_path, "product") or f"USB Controller {len(controllers)}",
                        controller_type=self._detect_usb_version(device_path)
                    )
                elif self._is_usb_hub(device_path):
                    hub_id = f"HUB{len(hubs)}"
                    hubs[device_name] = USBHub(
                        hub_id=hub_id,
                        hub_name=self._read_device_attribute(device_path, "product") or f"USB Hub {len(hubs)}",
                        controller_id=self._find_parent_controller(device_name, controllers)
                    )
            
            # 组装拓扑结构
            for controller in controllers.values():
                controller.hubs = [hub for hub in hubs.values() if hub.controller_id == controller.controller_id]
                topology.controllers.append(controller)
            
            topology.total_ports = sum(len(hub.ports) for controller in topology.controllers for hub in controller.hubs)
            topology.topology_hash = self.generate_topology_hash(topology)
            
            return topology
            
        except Exception as e:
            logger.error(f"Linux USB拓扑解析失败: {e}")
            return self._fallback_topology_parsing()
    
    def get_device_port_info(self, device_path: str) -> Optional[Tuple[str, int]]:
        """从Linux设备路径提取端口信息"""
        try:
            # Linux设备路径示例：/sys/bus/usb/devices/1-1.2
            # 其中1是控制器，1是HUB，2是端口
            
            if '/sys/bus/usb/devices/' in device_path:
                device_id = os.path.basename(device_path)
                parts = device_id.split('-')
                if len(parts) >= 2:
                    port_parts = parts[1].split('.')
                    if len(port_parts) >= 2:
                        hub_index = int(port_parts[0]) - 1
                        port_number = int(port_parts[1])
                        hub_id = f"HUB{hub_index}"
                        return (hub_id, port_number)
            
            return None
            
        except Exception as e:
            logger.debug(f"Linux端口信息提取失败: {e}")
            return None
    
    def _is_usb_controller(self, device_path: str) -> bool:
        """判断是否为USB控制器"""
        try:
            device_class = self._read_device_attribute(device_path, "bDeviceClass")
            return device_class == "09"  # USB Hub class
        except:
            return False
    
    def _is_usb_hub(self, device_path: str) -> bool:
        """判断是否为USB HUB"""
        try:
            device_class = self._read_device_attribute(device_path, "bDeviceClass")
            return device_class == "09"  # USB Hub class
        except:
            return False
    
    def _read_device_attribute(self, device_path: str, attribute: str) -> Optional[str]:
        """读取设备属性"""
        try:
            attr_path = os.path.join(device_path, attribute)
            if os.path.exists(attr_path):
                with open(attr_path, 'r') as f:
                    return f.read().strip()
        except:
            pass
        return None
    
    def _detect_usb_version(self, device_path: str) -> str:
        """检测USB版本"""
        try:
            version = self._read_device_attribute(device_path, "version")
            if version:
                if "3." in version:
                    return "USB3.0"
                elif "2." in version:
                    return "USB2.0"
                elif "1." in version:
                    return "USB1.1"
        except:
            pass
        return "Unknown"
    
    def _find_parent_controller(self, device_name: str, controllers: Dict) -> str:
        """查找父控制器"""
        # 简化实现：返回第一个控制器
        if controllers:
            return list(controllers.values())[0].controller_id
        return "CTRL0"
    
    def _fallback_topology_parsing(self) -> USBTopology:
        """Linux降级方案"""
        return WindowsUSBTopologyParser()._fallback_topology_parsing()

def create_usb_topology_parser() -> USBTopologyParser:
    """工厂函数：根据平台创建合适的USB拓扑解析器"""
    system = platform.system().lower()
    
    if system == "windows":
        return WindowsUSBTopologyParser()
    elif system == "linux":
        return LinuxUSBTopologyParser()
    else:
        logger.warning(f"不支持的平台: {system}，使用Windows解析器作为降级方案")
        return WindowsUSBTopologyParser()

# 全局实例
usb_topology_parser: Optional[USBTopologyParser] = None

def get_usb_topology_parser() -> USBTopologyParser:
    """获取USB拓扑解析器实例"""
    global usb_topology_parser
    if usb_topology_parser is None:
        usb_topology_parser = create_usb_topology_parser()
    return usb_topology_parser

def get_current_usb_topology(force_refresh: bool = False) -> USBTopology:
    """获取当前USB拓扑结构"""
    parser = get_usb_topology_parser()
    return parser.get_topology(force_refresh)
