#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniLink智能心跳管理器
实施分批心跳策略，减少网络负担和服务器压力
"""

import asyncio
import logging
import time
import random
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from enum import Enum
import hashlib

logger = logging.getLogger(__name__)

class HeartbeatStrategy(Enum):
    """心跳策略"""
    FIXED = "fixed"  # 固定间隔
    ADAPTIVE = "adaptive"  # 自适应间隔
    BATCH = "batch"  # 分批发送
    SMART = "smart"  # 智能策略

@dataclass
class HeartbeatConfig:
    """心跳配置"""
    base_interval: int = 30  # 基础间隔（秒）
    min_interval: int = 15   # 最小间隔
    max_interval: int = 120  # 最大间隔
    batch_size: int = 50     # 批次大小
    jitter_factor: float = 0.1  # 抖动因子
    strategy: HeartbeatStrategy = HeartbeatStrategy.SMART

class SmartHeartbeatManager:
    """智能心跳管理器"""
    
    def __init__(self, server_id: str, config: Optional[HeartbeatConfig] = None):
        self.server_id = server_id
        self.config = config or HeartbeatConfig()
        
        # 状态管理
        self.is_running = False
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.last_heartbeat_time = 0.0
        self.consecutive_failures = 0
        self.success_rate = 1.0
        
        # 批次管理
        self.batch_id = self._calculate_batch_id()
        self.batch_offset = self._calculate_batch_offset()
        
        # 统计信息
        self.stats = {
            'total_heartbeats': 0,
            'successful_heartbeats': 0,
            'failed_heartbeats': 0,
            'avg_response_time': 0.0,
            'last_success_time': 0.0
        }
        
        logger.info(f"智能心跳管理器初始化: {server_id}, 批次ID: {self.batch_id}, 偏移: {self.batch_offset}秒")
    
    def _calculate_batch_id(self) -> int:
        """计算批次ID"""
        # 基于服务器ID的哈希值计算批次ID
        hash_value = int(hashlib.md5(self.server_id.encode()).hexdigest(), 16)
        return hash_value % self.config.batch_size
    
    def _calculate_batch_offset(self) -> float:
        """计算批次内的偏移时间"""
        # 在批次间隔内均匀分布
        batch_interval = self.config.base_interval / self.config.batch_size
        return self.batch_id * batch_interval
    
    def _calculate_jitter(self) -> float:
        """计算抖动时间"""
        max_jitter = self.config.base_interval * self.config.jitter_factor
        return random.uniform(-max_jitter, max_jitter)
    
    def _calculate_adaptive_interval(self) -> float:
        """计算自适应间隔"""
        base = self.config.base_interval
        
        # 根据成功率调整间隔
        if self.success_rate > 0.95:
            # 高成功率时可以适当延长间隔
            interval = min(base * 1.2, self.config.max_interval)
        elif self.success_rate < 0.8:
            # 低成功率时缩短间隔
            interval = max(base * 0.8, self.config.min_interval)
        else:
            interval = base
        
        # 根据连续失败次数调整
        if self.consecutive_failures > 0:
            # 失败时使用指数退避，但有上限
            backoff_factor = min(2 ** self.consecutive_failures, 8)
            interval = min(interval * backoff_factor, self.config.max_interval)
        
        return interval
    
    def _calculate_next_heartbeat_time(self) -> float:
        """计算下次心跳时间"""
        current_time = time.time()
        
        if self.config.strategy == HeartbeatStrategy.FIXED:
            interval = self.config.base_interval
        elif self.config.strategy == HeartbeatStrategy.ADAPTIVE:
            interval = self._calculate_adaptive_interval()
        elif self.config.strategy == HeartbeatStrategy.BATCH:
            interval = self.config.base_interval + self.batch_offset
        else:  # SMART
            # 智能策略：结合自适应和分批
            base_interval = self._calculate_adaptive_interval()
            interval = base_interval + self.batch_offset
        
        # 添加抖动
        jitter = self._calculate_jitter()
        final_interval = max(interval + jitter, self.config.min_interval)
        
        return current_time + final_interval
    
    async def start(self, heartbeat_func):
        """启动智能心跳"""
        if self.is_running:
            logger.warning("智能心跳已在运行")
            return
        
        self.is_running = True
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop(heartbeat_func))
        logger.info(f"智能心跳已启动，策略: {self.config.strategy.value}")
    
    async def stop(self):
        """停止智能心跳"""
        self.is_running = False
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        logger.info("智能心跳已停止")
    
    async def _heartbeat_loop(self, heartbeat_func):
        """智能心跳循环"""
        # 初始延迟，避免启动时的雷鸣群体效应
        initial_delay = random.uniform(0, self.config.base_interval * 0.1)
        await asyncio.sleep(initial_delay)
        
        while self.is_running:
            try:
                # 计算下次心跳时间
                next_heartbeat_time = self._calculate_next_heartbeat_time()
                current_time = time.time()
                sleep_duration = max(next_heartbeat_time - current_time, 1.0)
                
                logger.debug(f"下次心跳将在 {sleep_duration:.1f} 秒后发送")
                await asyncio.sleep(sleep_duration)
                
                if not self.is_running:
                    break
                
                # 发送心跳
                await self._send_heartbeat_with_stats(heartbeat_func)
                
            except asyncio.CancelledError:
                logger.info("心跳循环已取消")
                break
            except Exception as e:
                logger.error(f"心跳循环异常: {e}")
                await asyncio.sleep(5)  # 异常时短暂等待
    
    async def _send_heartbeat_with_stats(self, heartbeat_func):
        """发送心跳并更新统计信息"""
        start_time = time.time()
        self.stats['total_heartbeats'] += 1
        
        try:
            success = await heartbeat_func()
            response_time = time.time() - start_time
            
            if success:
                self.stats['successful_heartbeats'] += 1
                self.stats['last_success_time'] = time.time()
                self.consecutive_failures = 0
                
                # 更新平均响应时间
                total_success = self.stats['successful_heartbeats']
                current_avg = self.stats['avg_response_time']
                self.stats['avg_response_time'] = (
                    (current_avg * (total_success - 1) + response_time) / total_success
                )
                
                logger.debug(f"心跳发送成功，响应时间: {response_time:.3f}秒")
            else:
                self.stats['failed_heartbeats'] += 1
                self.consecutive_failures += 1
                logger.warning(f"心跳发送失败，连续失败次数: {self.consecutive_failures}")
            
            # 更新成功率
            self.success_rate = (
                self.stats['successful_heartbeats'] / max(self.stats['total_heartbeats'], 1)
            )
            
            self.last_heartbeat_time = time.time()
            
        except Exception as e:
            self.stats['failed_heartbeats'] += 1
            self.consecutive_failures += 1
            logger.error(f"心跳发送异常: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        current_time = time.time()
        return {
            **self.stats,
            'success_rate': self.success_rate,
            'consecutive_failures': self.consecutive_failures,
            'time_since_last_heartbeat': current_time - self.last_heartbeat_time,
            'time_since_last_success': current_time - self.stats['last_success_time'],
            'batch_id': self.batch_id,
            'batch_offset': self.batch_offset,
            'strategy': self.config.strategy.value
        }
    
    def update_config(self, new_config: HeartbeatConfig):
        """更新配置"""
        old_strategy = self.config.strategy
        self.config = new_config
        
        if old_strategy != new_config.strategy:
            logger.info(f"心跳策略已更新: {old_strategy.value} -> {new_config.strategy.value}")
    
    def force_heartbeat(self) -> bool:
        """强制立即发送心跳"""
        if self.heartbeat_task and not self.heartbeat_task.done():
            # 取消当前任务并立即重新开始
            self.heartbeat_task.cancel()
            return True
        return False
