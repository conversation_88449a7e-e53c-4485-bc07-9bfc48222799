#!/usr/bin/env python3
"""
设备物理签名生成模块
为USB设备生成基于硬件特征的唯一标识
支持设备物理位置识别和配置持久化
"""

import hashlib
import logging
import re
from typing import Dict, Any, Optional, Tuple
import platform

logger = logging.getLogger(__name__)

class DeviceSignatureGenerator:
    """设备物理签名生成器"""
    
    def __init__(self):
        self.system = platform.system().lower()
        
    def generate_hardware_signature(self, device_info: Dict[str, Any]) -> str:
        """生成设备硬件签名"""
        try:
            # 收集设备硬件特征
            signature_components = []
            
            # 1. VID和PID（必需）
            vid = device_info.get('vendor_id', '').strip()
            pid = device_info.get('product_id', '').strip()
            if vid and pid:
                signature_components.append(f"VID:{vid}")
                signature_components.append(f"PID:{pid}")
            
            # 2. 序列号（如果有）
            serial = device_info.get('serial_number', '').strip()
            if serial and serial.lower() not in ['', 'none', 'unknown', 'n/a']:
                signature_components.append(f"SN:{serial}")
            
            # 3. 设备描述（作为补充标识）
            description = device_info.get('description', '').strip()
            if description:
                # 清理描述，移除动态部分
                clean_desc = self._clean_device_description(description)
                if clean_desc:
                    signature_components.append(f"DESC:{clean_desc}")
            
            # 4. 制造商信息（如果有）
            manufacturer = device_info.get('manufacturer', '').strip()
            if manufacturer and manufacturer.lower() not in ['', 'unknown', 'n/a']:
                signature_components.append(f"MFG:{manufacturer}")
            
            # 生成签名字符串
            signature_string = "|".join(signature_components)
            
            if not signature_string:
                # 降级方案：使用设备ID
                device_id = device_info.get('device_id', '')
                if device_id:
                    signature_string = f"DEVICE_ID:{device_id}"
                else:
                    # 最后的降级方案：使用时间戳
                    import time
                    signature_string = f"FALLBACK:{int(time.time())}"
            
            # 生成SHA256哈希
            hash_object = hashlib.sha256(signature_string.encode('utf-8'))
            hardware_signature = hash_object.hexdigest()[:16]  # 取前16位
            
            logger.debug(f"设备硬件签名生成: {signature_string} -> {hardware_signature}")
            return hardware_signature
            
        except Exception as e:
            logger.error(f"生成设备硬件签名失败: {e}")
            # 紧急降级方案
            import uuid
            return str(uuid.uuid4()).replace('-', '')[:16]
    
    def generate_physical_port(self, device_info: Dict[str, Any]) -> str:
        """生成设备物理端口标识"""
        try:
            # 收集物理位置信息
            port_components = []
            
            # 1. USB总线号
            bus = device_info.get('bus', '')
            if bus:
                port_components.append(f"BUS:{bus}")
            
            # 2. USB设备地址
            address = device_info.get('address', '')
            if address:
                port_components.append(f"ADDR:{address}")
            
            # 3. USB端口路径（如果有）
            port_path = device_info.get('port_path', '')
            if port_path:
                port_components.append(f"PATH:{port_path}")
            
            # 4. 父设备信息（如果有）
            parent_id = device_info.get('parent_id', '')
            if parent_id:
                port_components.append(f"PARENT:{parent_id}")
            
            # 5. 设备路径（Windows特有）
            device_path = device_info.get('device_path', '')
            if device_path:
                # 提取关键的路径信息
                clean_path = self._extract_device_path_key(device_path)
                if clean_path:
                    port_components.append(f"DEVPATH:{clean_path}")
            
            # 生成物理端口标识
            if port_components:
                physical_port = "|".join(port_components)
            else:
                # 降级方案：使用总线和地址
                physical_port = f"BUS:{bus or 'unknown'}|ADDR:{address or 'unknown'}"
            
            logger.debug(f"设备物理端口生成: {physical_port}")
            return physical_port
            
        except Exception as e:
            logger.error(f"生成设备物理端口失败: {e}")
            return f"UNKNOWN:{device_info.get('device_id', 'fallback')}"
    
    def _clean_device_description(self, description: str) -> str:
        """清理设备描述，移除动态部分"""
        try:
            # 移除常见的动态部分
            clean_desc = description
            
            # 移除序列号模式
            clean_desc = re.sub(r'\b[A-F0-9]{8,}\b', '', clean_desc)
            clean_desc = re.sub(r'\b\d{10,}\b', '', clean_desc)
            
            # 移除版本号模式
            clean_desc = re.sub(r'\bv?\d+\.\d+(\.\d+)?\b', '', clean_desc, flags=re.IGNORECASE)
            
            # 移除括号内的动态信息
            clean_desc = re.sub(r'\([^)]*\d{4,}[^)]*\)', '', clean_desc)
            
            # 清理多余的空格
            clean_desc = re.sub(r'\s+', ' ', clean_desc).strip()
            
            # 如果清理后太短，返回原始描述的前50个字符
            if len(clean_desc) < 10:
                clean_desc = description[:50]
            
            return clean_desc
            
        except Exception as e:
            logger.debug(f"清理设备描述失败: {e}")
            return description[:50]
    
    def _extract_device_path_key(self, device_path: str) -> str:
        """提取设备路径的关键信息"""
        try:
            # Windows设备路径示例：\\?\usb#vid_1234&pid_5678#serial#{guid}
            if '\\\\?\\' in device_path:
                # 提取USB部分
                usb_part = device_path.split('\\\\?\\')[-1]
                if 'usb#' in usb_part.lower():
                    # 提取VID&PID部分
                    parts = usb_part.split('#')
                    if len(parts) >= 2:
                        vid_pid_part = parts[1]
                        return vid_pid_part
            
            # Linux设备路径示例：/dev/bus/usb/001/002
            if '/dev/bus/usb/' in device_path:
                # 提取总线和设备号
                parts = device_path.split('/')
                if len(parts) >= 2:
                    bus_dev = f"{parts[-2]}-{parts[-1]}"
                    return bus_dev
            
            # 通用降级方案：取路径的最后部分
            return device_path.split('\\')[-1].split('/')[-1]
            
        except Exception as e:
            logger.debug(f"提取设备路径关键信息失败: {e}")
            return device_path[-20:]  # 取最后20个字符
    
    def create_device_identity(self, device_info: Dict[str, Any]) -> Dict[str, str]:
        """创建设备完整身份信息"""
        try:
            identity = {
                'hardware_signature': self.generate_hardware_signature(device_info),
                'physical_port': self.generate_physical_port(device_info),
                'device_id': device_info.get('device_id', ''),
                'vendor_id': device_info.get('vendor_id', ''),
                'product_id': device_info.get('product_id', ''),
                'serial_number': device_info.get('serial_number', ''),
                'description': device_info.get('description', ''),
                'manufacturer': device_info.get('manufacturer', '')
            }
            
            logger.debug(f"设备身份信息创建完成: {identity['hardware_signature']}")
            return identity
            
        except Exception as e:
            logger.error(f"创建设备身份信息失败: {e}")
            return {
                'hardware_signature': 'error_' + str(hash(str(device_info)))[-8:],
                'physical_port': 'unknown',
                'device_id': device_info.get('device_id', ''),
                'vendor_id': '',
                'product_id': '',
                'serial_number': '',
                'description': '',
                'manufacturer': ''
            }
    
    def detect_device_movement(self, old_identity: Dict[str, str], new_identity: Dict[str, str]) -> Dict[str, Any]:
        """检测设备物理位置变更"""
        try:
            movement_info = {
                'is_moved': False,
                'is_same_device': False,
                'changes': [],
                'confidence': 0.0
            }
            
            # 检查是否是同一个设备（基于硬件签名）
            if old_identity.get('hardware_signature') == new_identity.get('hardware_signature'):
                movement_info['is_same_device'] = True
                
                # 检查物理位置是否变更
                if old_identity.get('physical_port') != new_identity.get('physical_port'):
                    movement_info['is_moved'] = True
                    movement_info['changes'].append({
                        'field': 'physical_port',
                        'old_value': old_identity.get('physical_port'),
                        'new_value': new_identity.get('physical_port')
                    })
                
                # 计算置信度
                matching_fields = 0
                total_fields = 0
                
                for field in ['vendor_id', 'product_id', 'serial_number', 'description']:
                    total_fields += 1
                    if old_identity.get(field) == new_identity.get(field):
                        matching_fields += 1
                
                movement_info['confidence'] = matching_fields / total_fields if total_fields > 0 else 0.0
            
            return movement_info
            
        except Exception as e:
            logger.error(f"检测设备移动失败: {e}")
            return {
                'is_moved': False,
                'is_same_device': False,
                'changes': [],
                'confidence': 0.0
            }

# 全局实例
device_signature_generator = DeviceSignatureGenerator()

def generate_device_signature(device_info: Dict[str, Any]) -> str:
    """生成设备硬件签名的便捷函数"""
    return device_signature_generator.generate_hardware_signature(device_info)

def generate_device_physical_port(device_info: Dict[str, Any]) -> str:
    """生成设备物理端口的便捷函数"""
    return device_signature_generator.generate_physical_port(device_info)

def create_device_identity(device_info: Dict[str, Any]) -> Dict[str, str]:
    """创建设备完整身份信息的便捷函数"""
    return device_signature_generator.create_device_identity(device_info)

def detect_device_movement(old_identity: Dict[str, str], new_identity: Dict[str, str]) -> Dict[str, Any]:
    """检测设备物理位置变更的便捷函数"""
    return device_signature_generator.detect_device_movement(old_identity, new_identity)
