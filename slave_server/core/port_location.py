#!/usr/bin/env python3
"""
USB端口位置码生成和管理模块
生成格式为{UUID前8位}-{控制器}-{HUB路径}-{端口}的唯一位置码
支持位置码持久化、缓存和设备绑定管理
"""

import json
import logging
import os
import threading
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from .usb_topology import USBTopology, USBController, USBHub, USBPort, get_current_usb_topology
from .config_manager import SlaveConfigManager
from .port_database import get_port_database

logger = logging.getLogger(__name__)

@dataclass
class PortLocationCode:
    """端口位置码信息"""
    location_code: str
    server_uuid: str
    controller_id: str
    hub_id: str
    port_number: int
    hub_path: str = ""  # HUB层级路径，如"HUB0_HUB1"
    created_at: str = ""
    last_seen: str = ""
    device_bound: bool = False
    bound_device_info: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.last_seen:
            self.last_seen = datetime.now().isoformat()

@dataclass
class DeviceLocationBinding:
    """设备与位置的绑定信息"""
    location_code: str
    device_signature: str
    binding_time: str
    binding_confidence: float = 1.0
    last_verified: str = ""
    movement_history: List[str] = None
    
    def __post_init__(self):
        if self.movement_history is None:
            self.movement_history = []
        if not self.last_verified:
            self.last_verified = datetime.now().isoformat()

class PortLocationManager:
    """端口位置码管理器"""
    
    def __init__(self, server_uuid: str):
        self.server_uuid = server_uuid
        self.server_prefix = server_uuid[:8] if server_uuid else "unknown"
        self.config_manager = SlaveConfigManager()

        # 数据库管理器（延迟初始化）
        self.port_db = None
        
        # 位置码缓存
        self._location_codes: Dict[str, PortLocationCode] = {}
        self._device_bindings: Dict[str, DeviceLocationBinding] = {}
        self._port_to_location: Dict[Tuple[str, str, int], str] = {}  # (controller, hub, port) -> location_code
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 配置文件路径
        self.location_cache_file = os.path.join(
            self.config_manager.config_dir, 
            "port_locations.json"
        )
        self.binding_cache_file = os.path.join(
            self.config_manager.config_dir,
            "device_bindings.json"
        )
        
        # 加载缓存数据
        self._load_cached_data()
        
        logger.info(f"端口位置码管理器初始化完成，服务器前缀: {self.server_prefix}")

    def set_database(self, port_db):
        """设置数据库实例"""
        self.port_db = port_db
        logger.info("端口位置码数据库已连接")
    
    def generate_location_code(self, controller_id: str, hub_id: str, port_number: int, 
                             hub_path: str = "") -> str:
        """生成端口位置码"""
        try:
            # 构建位置码组件
            components = [self.server_prefix, controller_id]
            
            # 添加HUB路径（如果有多级HUB）
            if hub_path:
                components.append(hub_path.replace("_", "-"))
            else:
                components.append(hub_id)
            
            # 添加端口号
            components.append(f"P{port_number:02d}")
            
            location_code = "-".join(components)
            
            logger.debug(f"生成位置码: {location_code} (控制器:{controller_id}, HUB:{hub_id}, 端口:{port_number})")
            return location_code
            
        except Exception as e:
            logger.error(f"生成位置码失败: {e}")
            # 降级方案：使用简化格式
            return f"{self.server_prefix}-FALLBACK-P{port_number:02d}"
    
    def register_port_location(self, controller_id: str, hub_id: str, port_number: int,
                             hub_path: str = "", device_info: Optional[Dict[str, Any]] = None) -> str:
        """注册端口位置，返回位置码"""
        with self._lock:
            try:
                # 检查是否已存在位置码
                port_key = (controller_id, hub_id, port_number)
                existing_location = self._port_to_location.get(port_key)
                
                if existing_location and existing_location in self._location_codes:
                    # 更新现有位置码信息
                    location_info = self._location_codes[existing_location]
                    location_info.last_seen = datetime.now().isoformat()
                    
                    if device_info:
                        location_info.device_bound = True
                        location_info.bound_device_info = device_info
                    
                    logger.debug(f"更新现有位置码: {existing_location}")
                    return existing_location
                
                # 生成新的位置码
                location_code = self.generate_location_code(controller_id, hub_id, port_number, hub_path)
                
                # 创建位置码信息
                location_info = PortLocationCode(
                    location_code=location_code,
                    server_uuid=self.server_uuid,
                    controller_id=controller_id,
                    hub_id=hub_id,
                    port_number=port_number,
                    hub_path=hub_path,
                    device_bound=device_info is not None,
                    bound_device_info=device_info
                )
                
                # 缓存位置码信息
                self._location_codes[location_code] = location_info
                self._port_to_location[port_key] = location_code

                # 保存到数据库
                if self.port_db:
                    success = self.port_db.insert_port_location(
                        location_code, controller_id, hub_id, port_number, hub_path
                    )
                    logger.debug(f"数据库保存位置码: {location_code} -> {success}")
                else:
                    logger.warning("数据库未连接，无法保存位置码")

                # 保存到文件（备份）
                self._save_location_cache()

                logger.info(f"注册新位置码: {location_code}")
                return location_code
                
            except Exception as e:
                logger.error(f"注册端口位置失败: {e}")
                # 返回降级位置码
                return f"{self.server_prefix}-ERROR-P{port_number:02d}"
    
    def bind_device_to_location(self, location_code: str, device_signature: str,
                              confidence: float = 1.0) -> bool:
        """将设备绑定到位置码"""
        with self._lock:
            try:
                if location_code not in self._location_codes:
                    logger.warning(f"位置码不存在: {location_code}")
                    return False
                
                # 检查设备是否已绑定到其他位置
                existing_binding = self._device_bindings.get(device_signature)
                if existing_binding and existing_binding.location_code != location_code:
                    # 设备移动到新位置
                    logger.info(f"检测到设备移动: {device_signature} 从 {existing_binding.location_code} 到 {location_code}")
                    existing_binding.movement_history.append(
                        f"{datetime.now().isoformat()}:MOVED_FROM:{existing_binding.location_code}"
                    )
                
                # 创建或更新绑定
                binding = DeviceLocationBinding(
                    location_code=location_code,
                    device_signature=device_signature,
                    binding_time=datetime.now().isoformat(),
                    binding_confidence=confidence
                )
                
                self._device_bindings[device_signature] = binding

                # 更新位置码的设备绑定状态
                location_info = self._location_codes[location_code]
                location_info.device_bound = True
                location_info.last_seen = datetime.now().isoformat()

                # 保存到数据库
                if self.port_db:
                    self.port_db.bind_device_to_port(
                        location_code, device_signature, "", "", {}
                    )

                # 保存缓存（备份）
                self._save_binding_cache()
                self._save_location_cache()

                logger.info(f"设备绑定成功: {device_signature} -> {location_code}")
                return True
                
            except Exception as e:
                logger.error(f"设备绑定失败: {e}")
                return False
    
    def unbind_device_from_location(self, device_signature: str) -> bool:
        """解除设备与位置的绑定"""
        with self._lock:
            try:
                binding = self._device_bindings.get(device_signature)
                if not binding:
                    logger.debug(f"设备未绑定: {device_signature}")
                    return True
                
                location_code = binding.location_code
                
                # 移除绑定
                del self._device_bindings[device_signature]
                
                # 更新位置码状态
                if location_code in self._location_codes:
                    location_info = self._location_codes[location_code]
                    location_info.device_bound = False
                    location_info.bound_device_info = None
                
                # 保存缓存
                self._save_binding_cache()
                self._save_location_cache()
                
                logger.info(f"设备解绑成功: {device_signature} 从 {location_code}")
                return True
                
            except Exception as e:
                logger.error(f"设备解绑失败: {e}")
                return False
    
    def get_device_location(self, device_signature: str) -> Optional[str]:
        """获取设备的当前位置码"""
        binding = self._device_bindings.get(device_signature)
        return binding.location_code if binding else None
    
    def get_location_device(self, location_code: str) -> Optional[str]:
        """获取位置上绑定的设备签名"""
        for device_sig, binding in self._device_bindings.items():
            if binding.location_code == location_code:
                return device_sig
        return None
    
    def get_all_locations(self) -> List[PortLocationCode]:
        """获取所有位置码"""
        with self._lock:
            return list(self._location_codes.values())
    
    def get_location_info(self, location_code: str) -> Optional[PortLocationCode]:
        """获取位置码详细信息"""
        return self._location_codes.get(location_code)
    
    def update_topology_locations(self, topology: USBTopology) -> Dict[str, str]:
        """根据USB拓扑更新位置码，返回位置码映射"""
        with self._lock:
            location_mapping = {}
            
            try:
                for controller in topology.controllers:
                    for hub in controller.hubs:
                        # 构建HUB路径
                        hub_path = self._build_hub_path(hub, topology)
                        
                        for port in hub.ports:
                            location_code = self.register_port_location(
                                controller_id=controller.controller_id,
                                hub_id=hub.hub_id,
                                port_number=port.port_number,
                                hub_path=hub_path,
                                device_info=port.device_info if port.device_connected else None
                            )
                            
                            port_key = f"{controller.controller_id}:{hub.hub_id}:{port.port_number}"
                            location_mapping[port_key] = location_code
                
                logger.info(f"拓扑位置码更新完成，共 {len(location_mapping)} 个位置")
                return location_mapping
                
            except Exception as e:
                logger.error(f"拓扑位置码更新失败: {e}")
                return {}
    
    def _build_hub_path(self, hub: USBHub, topology: USBTopology) -> str:
        """构建HUB的层级路径"""
        try:
            path_components = [hub.hub_id]
            
            # 查找父HUB
            current_hub = hub
            while current_hub.parent_hub:
                for controller in topology.controllers:
                    for h in controller.hubs:
                        if h.hub_id == current_hub.parent_hub:
                            path_components.insert(0, h.hub_id)
                            current_hub = h
                            break
                    else:
                        continue
                    break
                else:
                    break
            
            return "_".join(path_components) if len(path_components) > 1 else ""
            
        except Exception as e:
            logger.debug(f"构建HUB路径失败: {e}")
            return ""
    
    def _load_cached_data(self):
        """加载缓存的位置码和绑定数据"""
        try:
            # 加载位置码缓存
            if os.path.exists(self.location_cache_file):
                with open(self.location_cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for location_code, info in data.items():
                        self._location_codes[location_code] = PortLocationCode(**info)
                        
                        # 重建端口映射
                        port_key = (info['controller_id'], info['hub_id'], info['port_number'])
                        self._port_to_location[port_key] = location_code
                
                logger.info(f"加载位置码缓存: {len(self._location_codes)} 个位置")
            
            # 加载绑定缓存
            if os.path.exists(self.binding_cache_file):
                with open(self.binding_cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for device_sig, binding_info in data.items():
                        self._device_bindings[device_sig] = DeviceLocationBinding(**binding_info)
                
                logger.info(f"加载绑定缓存: {len(self._device_bindings)} 个绑定")
                
        except Exception as e:
            logger.error(f"加载缓存数据失败: {e}")
    
    def _save_location_cache(self):
        """保存位置码缓存"""
        try:
            data = {}
            for location_code, info in self._location_codes.items():
                data[location_code] = asdict(info)
            
            with open(self.location_cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存位置码缓存失败: {e}")
    
    def _save_binding_cache(self):
        """保存绑定缓存"""
        try:
            data = {}
            for device_sig, binding in self._device_bindings.items():
                data[device_sig] = asdict(binding)
            
            with open(self.binding_cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存绑定缓存失败: {e}")
    
    def cleanup_stale_locations(self, max_age_days: int = 30):
        """清理过期的位置码"""
        with self._lock:
            try:
                from datetime import timedelta
                cutoff_time = datetime.now() - timedelta(days=max_age_days)
                
                stale_locations = []
                for location_code, info in self._location_codes.items():
                    last_seen = datetime.fromisoformat(info.last_seen)
                    if last_seen < cutoff_time and not info.device_bound:
                        stale_locations.append(location_code)
                
                for location_code in stale_locations:
                    del self._location_codes[location_code]
                    # 清理端口映射
                    for port_key, loc_code in list(self._port_to_location.items()):
                        if loc_code == location_code:
                            del self._port_to_location[port_key]
                
                if stale_locations:
                    self._save_location_cache()
                    logger.info(f"清理过期位置码: {len(stale_locations)} 个")
                
            except Exception as e:
                logger.error(f"清理过期位置码失败: {e}")

# 全局实例
port_location_manager: Optional[PortLocationManager] = None

def initialize_port_location_manager(server_uuid: str) -> PortLocationManager:
    """初始化端口位置码管理器"""
    global port_location_manager
    port_location_manager = PortLocationManager(server_uuid)
    return port_location_manager

def get_port_location_manager() -> Optional[PortLocationManager]:
    """获取端口位置码管理器实例"""
    return port_location_manager

def generate_port_location_code(server_uuid: str, controller_id: str, hub_id: str, 
                              port_number: int, hub_path: str = "") -> str:
    """便捷函数：生成端口位置码"""
    manager = get_port_location_manager()
    if manager:
        return manager.generate_location_code(controller_id, hub_id, port_number, hub_path)
    else:
        # 降级方案
        server_prefix = server_uuid[:8] if server_uuid else "unknown"
        return f"{server_prefix}-{controller_id}-{hub_id}-P{port_number:02d}"
