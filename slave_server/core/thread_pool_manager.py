#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniLink从服务器线程池管理器
用于解决"cannot join current thread"异常和线程资源泄漏问题
"""

import asyncio
import threading
import logging
import time
import queue
import weakref
from typing import Optional, Callable, Any, Dict, List
from concurrent.futures import ThreadPoolExecutor, Future
from dataclasses import dataclass
from enum import Enum
import signal
import sys

logger = logging.getLogger(__name__)

class ThreadPoolType(Enum):
    """线程池类型"""
    HEARTBEAT = "heartbeat"
    BEHAVIOR = "behavior"
    DEVICE_MONITOR = "device_monitor"
    COMMUNICATION = "communication"

@dataclass
class ThreadPoolConfig:
    """线程池配置"""
    max_workers: int = 4
    thread_name_prefix: str = "OmniLink"
    daemon: bool = True
    timeout: float = 30.0
    queue_size: int = 100

class SafeThreadPoolManager:
    """安全的线程池管理器"""
    
    def __init__(self):
        self._pools: Dict[ThreadPoolType, ThreadPoolExecutor] = {}
        self._active_futures: Dict[ThreadPoolType, List[Future]] = {}
        self._shutdown_event = threading.Event()
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'created_threads': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'active_tasks': 0,
            'pool_restarts': 0
        }
        
        # 注册信号处理器
        self._register_signal_handlers()
        
        logger.info("安全线程池管理器已初始化")
    
    def _register_signal_handlers(self):
        """注册信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，开始优雅关闭线程池")
            self.shutdown_all()
            sys.exit(0)
        
        try:
            signal.signal(signal.SIGTERM, signal_handler)
            signal.signal(signal.SIGINT, signal_handler)
        except Exception as e:
            logger.warning(f"注册信号处理器失败: {e}")
    
    def create_pool(self, pool_type: ThreadPoolType, config: Optional[ThreadPoolConfig] = None) -> bool:
        """创建线程池"""
        if config is None:
            config = self._get_default_config(pool_type)
        
        with self._lock:
            try:
                # 如果池已存在，先关闭
                if pool_type in self._pools:
                    self._shutdown_pool(pool_type)
                
                # 创建新的线程池
                pool = ThreadPoolExecutor(
                    max_workers=config.max_workers,
                    thread_name_prefix=f"{config.thread_name_prefix}-{pool_type.value}"
                )
                
                self._pools[pool_type] = pool
                self._active_futures[pool_type] = []
                
                logger.info(f"线程池 {pool_type.value} 已创建，最大工作线程数: {config.max_workers}")
                return True
                
            except Exception as e:
                logger.error(f"创建线程池 {pool_type.value} 失败: {e}")
                return False
    
    def _get_default_config(self, pool_type: ThreadPoolType) -> ThreadPoolConfig:
        """获取默认配置"""
        configs = {
            ThreadPoolType.HEARTBEAT: ThreadPoolConfig(
                max_workers=2,
                thread_name_prefix="Heartbeat",
                timeout=10.0
            ),
            ThreadPoolType.BEHAVIOR: ThreadPoolConfig(
                max_workers=4,
                thread_name_prefix="Behavior",
                timeout=30.0
            ),
            ThreadPoolType.DEVICE_MONITOR: ThreadPoolConfig(
                max_workers=2,
                thread_name_prefix="DeviceMonitor",
                timeout=15.0
            ),
            ThreadPoolType.COMMUNICATION: ThreadPoolConfig(
                max_workers=3,
                thread_name_prefix="Communication",
                timeout=20.0
            )
        }
        return configs.get(pool_type, ThreadPoolConfig())
    
    def submit_task(self, pool_type: ThreadPoolType, func: Callable, *args, **kwargs) -> Optional[Future]:
        """提交任务到线程池"""
        with self._lock:
            if self._shutdown_event.is_set():
                logger.warning("线程池管理器已关闭，拒绝新任务")
                return None
            
            if pool_type not in self._pools:
                logger.warning(f"线程池 {pool_type.value} 不存在，尝试创建")
                if not self.create_pool(pool_type):
                    return None
            
            try:
                pool = self._pools[pool_type]
                future = pool.submit(self._safe_task_wrapper, func, *args, **kwargs)
                
                # 添加到活跃任务列表
                self._active_futures[pool_type].append(future)
                self.stats['active_tasks'] += 1
                
                # 添加完成回调
                future.add_done_callback(lambda f: self._task_completed(pool_type, f))
                
                logger.debug(f"任务已提交到线程池 {pool_type.value}")
                return future
                
            except Exception as e:
                logger.error(f"提交任务到线程池 {pool_type.value} 失败: {e}")
                return None
    
    def _safe_task_wrapper(self, func: Callable, *args, **kwargs) -> Any:
        """安全的任务包装器"""
        try:
            self.stats['created_threads'] += 1
            result = func(*args, **kwargs)
            self.stats['completed_tasks'] += 1
            return result
        except Exception as e:
            self.stats['failed_tasks'] += 1
            logger.error(f"任务执行失败: {e}")
            raise
        finally:
            self.stats['active_tasks'] = max(0, self.stats['active_tasks'] - 1)
    
    def _task_completed(self, pool_type: ThreadPoolType, future: Future):
        """任务完成回调"""
        with self._lock:
            try:
                if pool_type in self._active_futures:
                    if future in self._active_futures[pool_type]:
                        self._active_futures[pool_type].remove(future)
                
                # 检查任务结果
                if future.exception():
                    logger.error(f"线程池 {pool_type.value} 任务异常: {future.exception()}")
                else:
                    logger.debug(f"线程池 {pool_type.value} 任务完成")
                    
            except Exception as e:
                logger.error(f"处理任务完成回调失败: {e}")
    
    def wait_for_completion(self, pool_type: ThreadPoolType, timeout: float = 30.0) -> bool:
        """等待指定线程池的所有任务完成"""
        with self._lock:
            if pool_type not in self._active_futures:
                return True
            
            futures = self._active_futures[pool_type].copy()
        
        try:
            # 等待所有任务完成
            for future in futures:
                try:
                    future.result(timeout=timeout)
                except Exception as e:
                    logger.warning(f"等待任务完成时出现异常: {e}")
            
            logger.info(f"线程池 {pool_type.value} 所有任务已完成")
            return True
            
        except Exception as e:
            logger.error(f"等待线程池 {pool_type.value} 任务完成失败: {e}")
            return False
    
    def _shutdown_pool(self, pool_type: ThreadPoolType, wait: bool = True, timeout: float = 30.0):
        """关闭指定线程池"""
        if pool_type not in self._pools:
            return
        
        try:
            pool = self._pools[pool_type]
            
            # 停止接受新任务
            pool.shutdown(wait=False)
            
            if wait:
                # 等待现有任务完成
                if not pool.shutdown(wait=True, timeout=timeout):
                    logger.warning(f"线程池 {pool_type.value} 在超时时间内未能完全关闭")
            
            # 清理资源
            del self._pools[pool_type]
            if pool_type in self._active_futures:
                del self._active_futures[pool_type]
            
            logger.info(f"线程池 {pool_type.value} 已关闭")
            
        except Exception as e:
            logger.error(f"关闭线程池 {pool_type.value} 失败: {e}")
    
    def restart_pool(self, pool_type: ThreadPoolType) -> bool:
        """重启线程池"""
        logger.info(f"重启线程池 {pool_type.value}")
        
        with self._lock:
            # 关闭现有池
            self._shutdown_pool(pool_type, wait=True, timeout=10.0)
            
            # 创建新池
            success = self.create_pool(pool_type)
            if success:
                self.stats['pool_restarts'] += 1
            
            return success
    
    def shutdown_all(self, timeout: float = 30.0):
        """关闭所有线程池"""
        logger.info("开始关闭所有线程池")
        
        self._shutdown_event.set()
        
        with self._lock:
            pool_types = list(self._pools.keys())
        
        for pool_type in pool_types:
            self._shutdown_pool(pool_type, wait=True, timeout=timeout)
        
        logger.info("所有线程池已关闭")
    
    def get_pool_status(self, pool_type: ThreadPoolType) -> Dict[str, Any]:
        """获取线程池状态"""
        with self._lock:
            if pool_type not in self._pools:
                return {"status": "not_exists"}
            
            pool = self._pools[pool_type]
            active_futures = self._active_futures.get(pool_type, [])
            
            return {
                "status": "running",
                "max_workers": pool._max_workers,
                "active_tasks": len(active_futures),
                "pending_tasks": len([f for f in active_futures if not f.done()]),
                "completed_tasks": len([f for f in active_futures if f.done()])
            }
    
    def get_global_stats(self) -> Dict[str, Any]:
        """获取全局统计信息"""
        return {
            **self.stats,
            "active_pools": len(self._pools),
            "total_active_tasks": sum(len(futures) for futures in self._active_futures.values())
        }

# 全局线程池管理器实例
_global_thread_manager: Optional[SafeThreadPoolManager] = None

def get_thread_manager() -> SafeThreadPoolManager:
    """获取全局线程池管理器"""
    global _global_thread_manager
    if _global_thread_manager is None:
        _global_thread_manager = SafeThreadPoolManager()
    return _global_thread_manager

def shutdown_thread_manager():
    """关闭全局线程池管理器"""
    global _global_thread_manager
    if _global_thread_manager:
        _global_thread_manager.shutdown_all()
        _global_thread_manager = None
