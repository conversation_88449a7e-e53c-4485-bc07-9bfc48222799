
# 在utils/vh_server_manager.py中添加的架构检测方法

def _detect_architecture(self) -> str:
    """
    检测系统架构并返回推荐的二进制文件路径
    
    Returns:
        str: 推荐的VirtualHere二进制文件路径
    """
    import platform
    
    machine = platform.machine().lower()
    
    if 'arm' in machine or 'aarch' in machine:
        # ARM架构，优先使用本地ARM版本
        local_arm_server = Path(__file__).parent.parent / 'virtualhere/vhusbdarmpi3'
        if local_arm_server.exists():
            logger.info(f"使用本地ARM二进制文件: {local_arm_server}")
            return str(local_arm_server)
        else:
            logger.warning("本地ARM二进制文件不存在，使用默认ARM路径")
            return './vhusbd_arm'
    else:
        # x86架构
        logger.info("检测到x86架构，使用标准二进制文件")
        return './vhusbd'

def _get_architecture_info(self) -> Dict[str, Any]:
    """
    获取详细的架构信息
    
    Returns:
        Dict: 架构信息
    """
    import platform
    
    return {
        'machine': platform.machine(),
        'processor': platform.processor(),
        'architecture': platform.architecture(),
        'is_arm': 'arm' in platform.machine().lower() or 'aarch' in platform.machine().lower()
    }
