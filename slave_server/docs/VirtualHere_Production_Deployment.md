# VirtualHere生产环境部署指南

## 概述

本文档详细说明了如何在生产环境中正确部署和配置VirtualHere USB服务器，确保USB设备能够被正确检测、暴露和远程访问。

## 支持的Linux发行版

- **Ubuntu** 18.04+ / 20.04+ / 22.04+
- **CentOS** 7+ / 8+
- **Debian** 9+ / 10+ / 11+
- **RHEL** 7+ / 8+
- **SUSE Linux Enterprise** 12+ / 15+

## 前置要求

### 系统要求
- Linux内核版本 3.10+
- 至少512MB可用内存
- 至少100MB可用磁盘空间
- 网络连接（用于客户端访问）

### 权限要求
- 非root用户账户（推荐）
- sudo权限（用于配置系统权限）
- USB设备访问权限

### 网络要求
- 开放TCP端口7575（默认）
- 防火墙配置允许VirtualHere通信
- 如需Bonjour发现，开放UDP端口5353

## 快速部署

### 1. 自动配置脚本

```bash
# 进入从服务器目录
cd /path/to/slave_server

# 运行权限配置脚本
chmod +x scripts/setup_virtualhere_permissions.sh
./scripts/setup_virtualhere_permissions.sh

# 重新登录以应用组权限
newgrp virtualhere

# 启动VirtualHere服务
sudo systemctl start virtualhere
sudo systemctl enable virtualhere
```

### 2. 验证部署

```bash
# 检查服务状态
sudo systemctl status virtualhere

# 检查端口监听
netstat -tlnp | grep 7575
# 或
ss -tlnp | grep 7575

# 查看服务日志
sudo journalctl -u virtualhere -f
```

## 详细配置步骤

### 1. 用户组配置

```bash
# 创建virtualhere用户组
sudo groupadd virtualhere

# 将用户添加到必要的组
sudo usermod -a -G virtualhere $USER
sudo usermod -a -G dialout $USER
sudo usermod -a -G plugdev $USER
```

### 2. udev规则配置

创建文件 `/etc/udev/rules.d/99-virtualhere-usb.rules`：

```bash
# VirtualHere USB设备访问规则
SUBSYSTEM=="usb", GROUP="virtualhere", MODE="0664"
SUBSYSTEM=="usb_device", GROUP="virtualhere", MODE="0664"

# USB Hub设备
SUBSYSTEM=="usb", ATTR{bDeviceClass}=="09", GROUP="virtualhere", MODE="0664"

# 加密锁设备（常见厂商）
SUBSYSTEM=="usb", ATTR{idVendor}=="096e", GROUP="virtualhere", MODE="0664"  # SenseShield
SUBSYSTEM=="usb", ATTR{idVendor}=="0471", GROUP="virtualhere", MODE="0664"  # Rockey
SUBSYSTEM=="usb", ATTR{idVendor}=="0529", GROUP="virtualhere", MODE="0664"  # HASP/Sentinel
SUBSYSTEM=="usb", ATTR{idVendor}=="1a86", GROUP="virtualhere", MODE="0664"  # CH341
SUBSYSTEM=="usb", ATTR{idVendor}=="10c4", GROUP="virtualhere", MODE="0664"  # CP210x

# 设备节点权限
KERNEL=="ttyUSB*", GROUP="virtualhere", MODE="0664"
KERNEL=="ttyACM*", GROUP="virtualhere", MODE="0664"
KERNEL=="hidraw*", GROUP="virtualhere", MODE="0664"

# 动态权限设置
ACTION=="add", SUBSYSTEM=="usb", RUN+="/bin/chmod 664 /dev/bus/usb/%b/%k"
ACTION=="add", SUBSYSTEM=="usb", RUN+="/bin/chgrp virtualhere /dev/bus/usb/%b/%k"
```

重新加载udev规则：
```bash
sudo udevadm control --reload-rules
sudo udevadm trigger
```

### 3. systemd服务配置

创建文件 `/etc/systemd/system/virtualhere.service`：

```ini
[Unit]
Description=VirtualHere USB Server
After=network.target
Wants=network.target

[Service]
Type=forking
User=omnilink
Group=virtualhere
WorkingDirectory=/path/to/slave_server
ExecStart=/path/to/slave_server/virtualhere/chake/online/VirtualHere/VirtualHere/Server/4.3.3/vhusbdx86_64 -b -c /path/to/slave_server/virtualhere_config.ini
ExecReload=/bin/kill -HUP $MAINPID
KillMode=process
Restart=on-failure
RestartSec=5
TimeoutStartSec=30
TimeoutStopSec=30

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/path/to/slave_server

# 允许访问USB设备
DeviceAllow=/dev/bus/usb/ rw
DeviceAllow=char-usb_device rw

[Install]
WantedBy=multi-user.target
```

### 4. VirtualHere配置文件

创建 `virtualhere_config.ini`：

```ini
# VirtualHere服务器配置文件

# 服务器名称
ServerName=OmniLink-USB-Server

# TCP端口
TCPPort=7575

# 启用Bonjour广播
UseAVAHI=1

# 压缩限制（字节）
CompressionLimit=384

# 自动附加到内核
AutoAttachToKernel=1

# 声明端口（Linux）
ClaimPorts=0

# 日志级别（0=无，1=错误，2=警告，3=信息，4=调试）
LogLevel=2

# 忽略的设备（可选）
# IgnoredDevices=424/ec00,1d6b/*

# 允许的设备（可选，留空表示允许所有）
# AllowedDevices=096e/*,0471/*,0529/*
```

## 防火墙配置

### Ubuntu/Debian (ufw)
```bash
sudo ufw allow 7575/tcp
sudo ufw allow 5353/udp  # Bonjour发现（可选）
```

### CentOS/RHEL (firewalld)
```bash
sudo firewall-cmd --permanent --add-port=7575/tcp
sudo firewall-cmd --permanent --add-port=5353/udp  # Bonjour发现（可选）
sudo firewall-cmd --reload
```

### CentOS/RHEL (iptables)
```bash
sudo iptables -A INPUT -p tcp --dport 7575 -j ACCEPT
sudo iptables -A INPUT -p udp --dport 5353 -j ACCEPT  # Bonjour发现（可选）
sudo service iptables save
```

## 故障排除

### 1. 权限问题

**症状**: "Permission denied" 错误

**解决方案**:
```bash
# 检查用户组
groups $USER

# 确保用户在virtualhere组中
sudo usermod -a -G virtualhere $USER

# 重新登录或执行
newgrp virtualhere

# 检查USB设备权限
ls -la /dev/bus/usb/
```

### 2. 端口监听问题

**症状**: VirtualHere启动但不监听端口

**解决方案**:
```bash
# 检查端口占用
netstat -tlnp | grep 7575
lsof -i :7575

# 检查配置文件
cat virtualhere_config.ini

# 手动启动测试
./virtualhere/chake/online/VirtualHere/VirtualHere/Server/4.3.3/vhusbdx86_64 -h
```

### 3. USB设备检测问题

**症状**: USB设备未被检测到

**解决方案**:
```bash
# 检查USB设备
lsusb
lsusb -t

# 检查udev规则
udevadm test /sys/bus/usb/devices/1-1

# 重新加载udev规则
sudo udevadm control --reload-rules
sudo udevadm trigger
```

### 4. 服务启动失败

**症状**: systemd服务启动失败

**解决方案**:
```bash
# 查看详细错误
sudo systemctl status virtualhere -l
sudo journalctl -u virtualhere -n 50

# 检查服务文件
sudo systemctl cat virtualhere

# 手动启动测试
sudo -u omnilink /path/to/vhusbdx86_64 -b -c /path/to/virtualhere_config.ini
```

## 性能优化

### 1. 内核参数优化

在 `/etc/sysctl.conf` 中添加：
```
# USB性能优化
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
```

应用设置：
```bash
sudo sysctl -p
```

### 2. USB驱动优化

```bash
# 增加USB缓冲区大小
echo 'SUBSYSTEM=="usb", ATTR{bMaxPacketSize0}=="64", ATTR{../speed}=="480", RUN+="/bin/sh -c '\''echo 32768 > /sys/module/usbcore/parameters/usbfs_memory_mb'\''"' | sudo tee -a /etc/udev/rules.d/99-usb-performance.rules
```

### 3. 网络优化

```bash
# 优化TCP参数
echo 'net.ipv4.tcp_window_scaling = 1' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 65536 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 16777216' | sudo tee -a /etc/sysctl.conf
```

## 监控和维护

### 1. 日志监控

```bash
# 实时查看日志
sudo journalctl -u virtualhere -f

# 查看错误日志
sudo journalctl -u virtualhere -p err

# 日志轮转配置
sudo systemctl edit virtualhere
```

### 2. 性能监控

```bash
# 监控USB设备状态
watch -n 5 'lsusb && echo "---" && netstat -tlnp | grep 7575'

# 监控系统资源
top -p $(pgrep vhusbdx86_64)
```

### 3. 自动重启配置

在systemd服务中已配置自动重启，可以调整参数：
```ini
Restart=on-failure
RestartSec=5
StartLimitBurst=3
StartLimitIntervalSec=60
```

## 安全考虑

### 1. 网络安全

- 使用防火墙限制访问源IP
- 考虑使用VPN连接
- 定期更新VirtualHere版本

### 2. 系统安全

- 定期更新系统补丁
- 使用非特权用户运行服务
- 限制USB设备访问权限

### 3. 审计日志

```bash
# 启用USB设备插拔日志
echo 'SUBSYSTEM=="usb", ACTION=="add", RUN+="/usr/bin/logger USB device added: %k"' | sudo tee -a /etc/udev/rules.d/99-usb-audit.rules
echo 'SUBSYSTEM=="usb", ACTION=="remove", RUN+="/usr/bin/logger USB device removed: %k"' | sudo tee -a /etc/udev/rules.d/99-usb-audit.rules
```

## 常见问题FAQ

**Q: VirtualHere在WSL中无法运行？**
A: WSL不支持直接访问USB设备，需要在真实Linux环境中运行。

**Q: 某些加密锁无法识别？**
A: 可能需要特定的驱动程序，请联系加密锁厂商获取Linux驱动。

**Q: 客户端连接超时？**
A: 检查网络连接和防火墙设置，确保7575端口开放。

**Q: 设备在客户端显示但无法使用？**
A: 可能是驱动问题，确保客户端安装了正确的设备驱动。

## 联系支持

如遇到问题，请提供以下信息：
- Linux发行版和版本
- VirtualHere版本
- 错误日志
- USB设备信息 (lsusb输出)
- 网络配置信息
