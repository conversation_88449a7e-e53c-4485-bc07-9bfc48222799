# OmniLink从服务器环境变量配置模板
# 复制此文件为 .env 并修改相应的值

# SERVER 配置
# server.host (类型: str)
SLAVE_SERVER_HOST=0.0.0.0

# server.port (类型: int)
SLAVE_SERVER_PORT=8889

# server.debug (类型: bool)
FLASK_DEBUG=False

# server.workers (类型: int)
FLASK_WORKERS=4

# DATABASE 配置
# database.db_file (类型: str)
DB_FILE=data/slave_server.db

# database.backup_enabled (类型: bool)
DB_BACKUP_ENABLED=True

# database.backup_interval (类型: int)
DB_BACKUP_INTERVAL=3600

# VIRTUALHERE 配置
# virtualhere.server_port (类型: int)
VH_SERVER_PORT=7575

# virtualhere.binary_path (类型: str)
VH_BINARY_PATH=/app/vhusbd

# virtualhere.auto_start (类型: bool)
VH_AUTO_START=True

# virtualhere.max_devices (类型: int)
VH_MAX_DEVICES=50

# MASTER 配置
# master.server_url (类型: str)
MASTER_SERVER_URL=

# master.auth_token (类型: str)
MASTER_AUTH_TOKEN=

# master.heartbeat_interval (类型: int)
MASTER_HEARTBEAT_INTERVAL=30

# master.retry_count (类型: int)
MASTER_RETRY_COUNT=3

# master.timeout (类型: int)
MASTER_TIMEOUT=10

# LOGGING 配置
# logging.level (类型: str)
LOG_LEVEL=INFO

# logging.file (类型: str)
LOG_FILE=logs/slave_server.log

# logging.max_size (类型: int)
LOG_MAX_SIZE=10485760

# logging.backup_count (类型: int)
LOG_BACKUP_COUNT=5

# MONITORING 配置
# monitoring.enabled (类型: bool)
ENABLE_MONITORING=True

# monitoring.performance_enabled (类型: bool)
ENABLE_PERFORMANCE_MONITOR=True

# monitoring.health_check_interval (类型: int)
HEALTH_CHECK_INTERVAL=60

# monitoring.metrics_port (类型: int)
METRICS_PORT=9100

# SECURITY 配置
# security.api_key (类型: str)
API_KEY=

# security.cors_enabled (类型: bool)
CORS_ENABLED=True

# security.cors_origins (类型: str)
CORS_ORIGINS=*

# security.rate_limit (类型: str)
RATE_LIMIT=100/hour

# CACHE 配置
# cache.redis_url (类型: str)
REDIS_URL=redis://redis:6379/0

# cache.enabled (类型: bool)
CACHE_ENABLED=True

# cache.ttl (类型: int)
CACHE_TTL=300

# PERFORMANCE 配置
# performance.sample_interval (类型: int)
PERF_SAMPLE_INTERVAL=5

# performance.history_size (类型: int)
PERF_HISTORY_SIZE=720

# performance.cpu_threshold (类型: float)
PERF_CPU_THRESHOLD=80.0

# performance.memory_threshold (类型: float)
PERF_MEMORY_THRESHOLD=80.0

# DEVELOPMENT 配置
# development.debug_mode (类型: bool)
DEBUG_MODE=False

# development.hot_reload (类型: bool)
HOT_RELOAD=False

# development.profiling (类型: bool)
ENABLE_PROFILING=False

FLASK_ENV=production