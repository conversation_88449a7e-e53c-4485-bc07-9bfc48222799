# -*- coding: utf-8 -*-
"""
设备管理API服务
提供设备相关的RESTful接口
"""

from flask import Blueprint, request, jsonify, Response
from typing import Dict, Any, Optional, Union, List, Tuple
from utils.logger import get_logger
from utils.device_controller import Device<PERSON>ontroller
from utils.device_event_handler import DeviceEventHandler
from utils.security_manager import require_auth
from utils.performance_monitor import monitor_api_performance
from db.device_dao import DeviceDAO

logger = get_logger('device_service')
device_bp = Blueprint('device', __name__, url_prefix='/api/devices')

# 初始化组件
event_handler = DeviceEventHandler()
device_controller = DeviceController(event_handler)
device_dao = DeviceDAO()


def _device_to_dict(device) -> Dict[str, Any]:
    """将设备对象转换为字典"""
    return {
        'device_uuid': device.device_uuid,
        'device_address': device.device_address,
        'vendor': device.vendor,
        'vendor_id': device.vendor_id,
        'product': device.product,
        'product_id': device.product_id,
        'device_serial': device.device_serial,
        'nick_name': device.nick_name,
        'status': device.status,
        'server_name': device.server_name,
        'hostname': device.hostname,
        'shown': device.shown,
        'disable': device.disable,
        'last_seen': device.last_seen.isoformat() if device.last_seen else None
    }


@device_bp.route('/', methods=['GET'])
@require_auth
@monitor_api_performance
def get_devices() -> Union[Response, Tuple[Response, int]]:
    """
    获取设备列表

    Query Parameters:
        page: 页码（默认1）
        page_size: 每页大小（默认20）
        status: 设备状态过滤
        server: 服务器名称过滤
        search: 搜索关键词

    Returns:
        JSON: 设备列表和统计信息
    """
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        status = request.args.get('status')
        server = request.args.get('server')
        search = request.args.get('search')

        # 根据参数获取设备
        if search:
            devices = device_dao.search_devices(search)
            # 转换为分页格式
            total_count = len(devices)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_devices = devices[start_idx:end_idx]

            result = {
                'devices': [_device_to_dict(device) for device in paginated_devices],
                'total_count': total_count,
                'page': page,
                'page_size': page_size,
                'total_pages': (total_count + page_size - 1) // page_size,
                'has_next': end_idx < total_count,
                'has_prev': page > 1
            }
        elif status:
            devices = device_dao.get_devices_by_status(status)
            result = {
                'devices': [_device_to_dict(device) for device in devices],
                'total_count': len(devices)
            }
        elif server:
            devices = device_dao.get_devices_by_server(server)
            result = {
                'devices': [_device_to_dict(device) for device in devices],
                'total_count': len(devices)
            }
        else:
            result = device_dao.get_devices_paginated(page, page_size)
            result['devices'] = [_device_to_dict(device) for device in result['devices']]

        return jsonify({
            'status': True,
            'message': '获取设备列表成功',
            'data': result
        })

    except Exception as e:
        logger.error(f"获取设备列表失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取设备列表失败: {str(e)}'
        }), 500


@device_bp.route('/<device_uuid>', methods=['GET'])
def get_device(device_uuid: str) -> Union[Response, tuple]:
    """
    获取特定设备信息

    Args:
        device_uuid: 设备UUID

    Returns:
        JSON: 设备详细信息
    """
    try:
        # 获取设备状态（包含数据库和VH实时信息）
        result = device_controller.get_device_status(device_uuid)

        if result['success']:
            return jsonify({
                'status': True,
                'message': '获取设备信息成功',
                'data': result
            })
        else:
            return jsonify({
                'status': False,
                'message': result['error']
            }), 404

    except Exception as e:
        logger.error(f"获取设备信息失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取设备信息失败: {str(e)}'
        }), 500


@device_bp.route('/<device_uuid>/connect', methods=['POST'])
def connect_device(device_uuid: str) -> Union[Response, tuple]:
    """
    连接设备

    Args:
        device_uuid: 设备UUID

    Request Body:
        user_info: 用户信息（可选）

    Returns:
        JSON: 连接结果
    """
    try:
        # 获取请求数据
        data = request.get_json() or {}
        user_info = data.get('user_info')

        # 执行设备连接
        result = device_controller.connect_device(device_uuid, user_info)

        return jsonify({
            'status': result['success'],
            'message': result.get('message', result.get('error', '未知错误')),
            'data': result if result['success'] else None
        }), 200 if result['success'] else 400

    except Exception as e:
        logger.error(f"连接设备失败: {e}")
        return jsonify({
            'status': False,
            'message': f'连接设备失败: {str(e)}'
        }), 500


@device_bp.route('/<device_uuid>/disconnect', methods=['POST'])
def disconnect_device(device_uuid: str) -> Union[Response, tuple]:
    """
    断开设备

    Args:
        device_uuid: 设备UUID

    Request Body:
        user_info: 用户信息（可选）

    Returns:
        JSON: 断开结果
    """
    try:
        # 获取请求数据
        data = request.get_json() or {}
        user_info = data.get('user_info')

        # 执行设备断开
        result = device_controller.disconnect_device(device_uuid, user_info)

        return jsonify({
            'status': result['success'],
            'message': result.get('message', result.get('error', '未知错误')),
            'data': result if result['success'] else None
        }), 200 if result['success'] else 400

    except Exception as e:
        logger.error(f"断开设备失败: {e}")
        return jsonify({
            'status': False,
            'message': f'断开设备失败: {str(e)}'
        }), 500


@device_bp.route('/<device_uuid>/reset', methods=['POST'])
def reset_device(device_uuid: str) -> Union[Response, tuple]:
    """
    重置设备

    Args:
        device_uuid: 设备UUID

    Returns:
        JSON: 重置结果
    """
    try:
        # 执行设备重置
        result = device_controller.reset_device(device_uuid)

        return jsonify({
            'status': result['success'],
            'message': result.get('message', result.get('error', '未知错误')),
            'data': result if result['success'] else None
        }), 200 if result['success'] else 400

    except Exception as e:
        logger.error(f"重置设备失败: {e}")
        return jsonify({
            'status': False,
            'message': f'重置设备失败: {str(e)}'
        }), 500


@device_bp.route('/<device_uuid>/rename', methods=['POST'])
def rename_device(device_uuid: str) -> Union[Response, tuple]:
    """
    重命名设备

    Args:
        device_uuid: 设备UUID

    Request Body:
        new_name: 新名称

    Returns:
        JSON: 重命名结果
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'new_name' not in data:
            return jsonify({
                'status': False,
                'message': '缺少新名称参数'
            }), 400

        new_name = data['new_name']
        if not new_name or not new_name.strip():
            return jsonify({
                'status': False,
                'message': '新名称不能为空'
            }), 400

        # 执行设备重命名
        result = device_controller.rename_device(device_uuid, new_name.strip())

        return jsonify({
            'status': result['success'],
            'message': result.get('message', result.get('error', '未知错误')),
            'data': result if result['success'] else None
        }), 200 if result['success'] else 400

    except Exception as e:
        logger.error(f"重命名设备失败: {e}")
        return jsonify({
            'status': False,
            'message': f'重命名设备失败: {str(e)}'
        }), 500


@device_bp.route('/statistics', methods=['GET'])
def get_device_statistics() -> Union[Response, tuple]:
    """
    获取设备统计信息

    Returns:
        JSON: 设备统计信息
    """
    try:
        stats = device_dao.get_device_statistics()

        return jsonify({
            'status': True,
            'message': '获取设备统计信息成功',
            'data': stats
        })

    except Exception as e:
        logger.error(f"获取设备统计信息失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取设备统计信息失败: {str(e)}'
        }), 500


@device_bp.route('/<device_uuid>/visibility', methods=['PUT'])
def set_device_visibility(device_uuid: str) -> Union[Response, tuple]:
    """
    设置设备可见性

    Args:
        device_uuid: 设备UUID

    Request Body:
        shown: 是否显示（布尔值）

    Returns:
        JSON: 设置结果
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'shown' not in data:
            return jsonify({
                'status': False,
                'message': '缺少shown参数'
            }), 400

        shown = bool(data['shown'])

        # 设置设备可见性
        success = device_dao.set_device_visibility(device_uuid, shown)

        if success:
            return jsonify({
                'status': True,
                'message': f'设备可见性设置成功',
                'data': {'device_uuid': device_uuid, 'shown': shown}
            })
        else:
            return jsonify({
                'status': False,
                'message': '设备不存在或设置失败'
            }), 404

    except Exception as e:
        logger.error(f"设置设备可见性失败: {e}")
        return jsonify({
            'status': False,
            'message': f'设置设备可见性失败: {str(e)}'
        }), 500


@device_bp.route('/<device_uuid>/enabled', methods=['PUT'])
def set_device_enabled(device_uuid: str) -> Union[Response, tuple]:
    """
    设置设备启用状态

    Args:
        device_uuid: 设备UUID

    Request Body:
        enabled: 是否启用（布尔值）

    Returns:
        JSON: 设置结果
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'enabled' not in data:
            return jsonify({
                'status': False,
                'message': '缺少enabled参数'
            }), 400

        enabled = bool(data['enabled'])

        # 设置设备启用状态
        success = device_dao.set_device_enabled(device_uuid, enabled)

        if success:
            return jsonify({
                'status': True,
                'message': f'设备启用状态设置成功',
                'data': {'device_uuid': device_uuid, 'enabled': enabled}
            })
        else:
            return jsonify({
                'status': False,
                'message': '设备不存在或设置失败'
            }), 404

    except Exception as e:
        logger.error(f"设置设备启用状态失败: {e}")
        return jsonify({
            'status': False,
            'message': f'设置设备启用状态失败: {str(e)}'
        }), 500
