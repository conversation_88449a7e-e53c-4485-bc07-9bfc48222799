# -*- coding: utf-8 -*-
"""
服务器管理API服务
提供服务器状态和管理相关的RESTful接口
"""

from flask import Blueprint, request, jsonify
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import psutil
from utils.logger import get_logger
from utils.health_checker import get_system_status, global_health_checker
from utils.error_handler import global_error_handler

logger = get_logger('server_service')
server_bp = Blueprint('server', __name__)


@server_bp.route('/status', methods=['GET'])
def get_server_status() -> Union[Dict[str, Any], Tuple[Dict[str, Any], int]]:
    """
    获取服务器状态信息

    Returns:
        JSON: 服务器状态详情
    """
    try:
        # 获取完整系统状态（包含健康检查）
        system_status = get_system_status()

        return jsonify({
            'status': True,
            'message': '获取服务器状态成功',
            'data': system_status
        })

    except Exception as e:
        logger.error(f"获取服务器状态失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取服务器状态失败: {str(e)}'
        }), 500


@server_bp.route('/health', methods=['GET'])
def get_health_status() -> None:
    """
    获取健康检查状态

    Returns:
        JSON: 健康检查结果
    """
    try:
        health_results = global_health_checker.run_all_checks()

        # 根据健康状态设置HTTP状态码
        http_status = 200
        if health_results['overall_status'] == 'critical':
            http_status = 503
        elif health_results['overall_status'] == 'warning':
            http_status = 200

        return jsonify({
            'status': health_results['overall_status'] != 'critical',
            'message': f"健康检查完成 - {health_results['overall_status']}",
            'data': health_results
        }), http_status

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            'status': False,
            'message': f'健康检查失败: {str(e)}'
        }), 500


@server_bp.route('/errors', methods=['GET'])
def get_error_stats() -> None:
    """
    获取错误统计信息

    Returns:
        JSON: 错误统计
    """
    try:
        error_stats = global_error_handler.get_error_stats()

        return jsonify({
            'status': True,
            'message': '获取错误统计成功',
            'data': error_stats
        })

    except Exception as e:
        logger.error(f"获取错误统计失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取错误统计失败: {str(e)}'
        }), 500


@server_bp.route('/info', methods=['GET'])
def get_server_info() -> None:
    """
    获取服务器基本信息
    
    Returns:
        JSON: 服务器基本信息
    """
    try:
        import platform
        import socket
        
        server_info = {
            'hostname': socket.gethostname(),
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'architecture': platform.architecture()[0],
            'processor': platform.processor(),
            'ip_address': socket.gethostbyname(socket.gethostname()),
            'version': '1.0.0'
        }
        
        return jsonify({
            'status': True,
            'message': '获取服务器信息成功',
            'data': server_info
        })
        
    except Exception as e:
        logger.error(f"获取服务器信息失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取服务器信息失败: {str(e)}'
        }), 500


@server_bp.route('/restart', methods=['POST'])
def restart_server() -> None:
    """
    重启服务器
    
    Returns:
        JSON: 重启结果
    """
    try:
        # TODO: 实现服务器重启逻辑
        logger.info("收到服务器重启请求")
        
        return jsonify({
            'status': True,
            'message': '服务器重启请求已接收'
        })
        
    except Exception as e:
        logger.error(f"重启服务器失败: {e}")
        return jsonify({
            'status': False,
            'message': f'重启服务器失败: {str(e)}'
        }), 500
