# -*- coding: utf-8 -*-
"""
配置管理API服务
提供运行时配置查看和修改的RESTful接口
"""

from flask import Blueprint, request, jsonify
from typing import Dict, List, Any, Optional, Union, Tuple
from utils.logger import get_logger
from utils.config_manager import ConfigManager
from utils.vh_server_manager import VirtualHereServerManager

logger = get_logger('config_service')

# 创建蓝图
config_bp = Blueprint('config', __name__, url_prefix='/api/config')

# 初始化组件
config_manager = ConfigManager()
vh_server_manager = VirtualHereServerManager()


@config_bp.route('/', methods=['GET'])
def get_all_config() -> None:
    """
    获取所有配置
    
    Returns:
        JSON: 所有配置信息
    """
    try:
        # 获取系统配置
        system_config = config_manager.get_all_config()
        
        # 获取VirtualHere配置
        vh_config = vh_server_manager.get_current_config()
        
        return jsonify({
            'status': True,
            'message': '获取配置成功',
            'data': {
                'system_config': system_config,
                'virtualhere_config': vh_config,
                'config_file_path': config_manager.config_file,
                'vh_config_file_path': vh_server_manager.config_file
            }
        })
        
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取配置失败: {str(e)}'
        }), 500


@config_bp.route('/system', methods=['GET'])
def get_system_config() -> None:
    """
    获取系统配置
    
    Returns:
        JSON: 系统配置信息
    """
    try:
        config = config_manager.get_all_config()
        
        return jsonify({
            'status': True,
            'message': '获取系统配置成功',
            'data': config
        })
        
    except Exception as e:
        logger.error(f"获取系统配置失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取系统配置失败: {str(e)}'
        }), 500


@config_bp.route('/system/<section>', methods=['GET'])
def get_system_config_section(section) -> Any:
    """
    获取系统配置的特定节
    
    Args:
        section: 配置节名称
    
    Returns:
        JSON: 配置节信息
    """
    try:
        if section == 'database':
            config = config_manager.get_database_config()
        elif section == 'virtualhere':
            config = config_manager.get_virtualhere_config()
        elif section == 'logging':
            config = config_manager.get_logging_config()
        elif section == 'server':
            config = config_manager.get_server_config()
        else:
            return jsonify({
                'status': False,
                'message': f'不支持的配置节: {section}'
            }), 400
        
        return jsonify({
            'status': True,
            'message': f'获取{section}配置成功',
            'data': config
        })
        
    except Exception as e:
        logger.error(f"获取配置节失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取配置节失败: {str(e)}'
        }), 500


@config_bp.route('/system/<section>/<key>', methods=['GET'])
def get_system_config_value(section, key) -> Any:
    """
    获取系统配置的特定值
    
    Args:
        section: 配置节名称
        key: 配置键名
    
    Returns:
        JSON: 配置值
    """
    try:
        value = config_manager.get_value(section, key)
        
        if value is not None:
            return jsonify({
                'status': True,
                'message': f'获取配置值成功',
                'data': {
                    'section': section,
                    'key': key,
                    'value': value
                }
            })
        else:
            return jsonify({
                'status': False,
                'message': f'配置项不存在: {section}.{key}'
            }), 404
        
    except Exception as e:
        logger.error(f"获取配置值失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取配置值失败: {str(e)}'
        }), 500


@config_bp.route('/system/<section>/<key>', methods=['PUT'])
def set_system_config_value(section, key) -> Any:
    """
    设置系统配置值
    
    Args:
        section: 配置节名称
        key: 配置键名
    
    Request Body:
        value: 新的配置值
    
    Returns:
        JSON: 设置结果
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'value' not in data:
            return jsonify({
                'status': False,
                'message': '缺少value参数'
            }), 400
        
        new_value = data['value']
        
        # 验证配置项是否可修改
        if not _is_config_modifiable(section, key):
            return jsonify({
                'status': False,
                'message': f'配置项 {section}.{key} 不允许运行时修改'
            }), 403
        
        # 获取旧值
        old_value = config_manager.get_value(section, key)
        
        # 设置新值
        config_manager.set_value(section, key, str(new_value))
        
        # 保存配置
        if config_manager.save_config():
            # 检查是否需要重启服务
            restart_required = _check_restart_required(section, key)
            
            return jsonify({
                'status': True,
                'message': f'配置设置成功',
                'data': {
                    'section': section,
                    'key': key,
                    'old_value': old_value,
                    'new_value': new_value,
                    'restart_required': restart_required
                }
            })
        else:
            return jsonify({
                'status': False,
                'message': '配置保存失败'
            }), 500
        
    except Exception as e:
        logger.error(f"设置配置值失败: {e}")
        return jsonify({
            'status': False,
            'message': f'设置配置值失败: {str(e)}'
        }), 500


@config_bp.route('/virtualhere', methods=['GET'])
def get_virtualhere_config() -> None:
    """
    获取VirtualHere配置
    
    Returns:
        JSON: VirtualHere配置信息
    """
    try:
        config = vh_server_manager.get_current_config()
        
        return jsonify({
            'status': True,
            'message': '获取VirtualHere配置成功',
            'data': config
        })
        
    except Exception as e:
        logger.error(f"获取VirtualHere配置失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取VirtualHere配置失败: {str(e)}'
        }), 500


@config_bp.route('/virtualhere', methods=['PUT'])
def update_virtualhere_config() -> None:
    """
    更新VirtualHere配置
    
    Request Body:
        配置项键值对
    
    Returns:
        JSON: 更新结果
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                'status': False,
                'message': '缺少配置数据'
            }), 400
        
        # 获取当前配置
        current_config = vh_server_manager.get_current_config()
        
        # 更新配置
        updated_config = current_config.copy()
        updated_config.update(data)
        
        # 应用配置
        if vh_server_manager.configure_server(updated_config):
            return jsonify({
                'status': True,
                'message': 'VirtualHere配置更新成功',
                'data': {
                    'old_config': current_config,
                    'new_config': updated_config,
                    'restart_required': True
                }
            })
        else:
            return jsonify({
                'status': False,
                'message': 'VirtualHere配置更新失败'
            }), 500
        
    except Exception as e:
        logger.error(f"更新VirtualHere配置失败: {e}")
        return jsonify({
            'status': False,
            'message': f'更新VirtualHere配置失败: {str(e)}'
        }), 500


@config_bp.route('/virtualhere/<key>', methods=['PUT'])
def update_virtualhere_config_item(key) -> Any:
    """
    更新VirtualHere配置项
    
    Args:
        key: 配置键名
    
    Request Body:
        value: 新的配置值
    
    Returns:
        JSON: 更新结果
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'value' not in data:
            return jsonify({
                'status': False,
                'message': '缺少value参数'
            }), 400
        
        new_value = data['value']
        
        # 更新单个配置项
        if vh_server_manager.update_config_item(key, str(new_value)):
            return jsonify({
                'status': True,
                'message': f'VirtualHere配置项 {key} 更新成功',
                'data': {
                    'key': key,
                    'value': new_value,
                    'restart_required': True
                }
            })
        else:
            return jsonify({
                'status': False,
                'message': f'VirtualHere配置项 {key} 更新失败'
            }), 500
        
    except Exception as e:
        logger.error(f"更新VirtualHere配置项失败: {e}")
        return jsonify({
            'status': False,
            'message': f'更新VirtualHere配置项失败: {str(e)}'
        }), 500


@config_bp.route('/reload', methods=['POST'])
def reload_config() -> None:
    """
    重新加载配置
    
    Returns:
        JSON: 重新加载结果
    """
    try:
        # 重新加载系统配置
        config_manager.load_config()
        
        return jsonify({
            'status': True,
            'message': '配置重新加载成功'
        })
        
    except Exception as e:
        logger.error(f"重新加载配置失败: {e}")
        return jsonify({
            'status': False,
            'message': f'重新加载配置失败: {str(e)}'
        }), 500


@config_bp.route('/backup', methods=['POST'])
def backup_config() -> None:
    """
    备份配置
    
    Returns:
        JSON: 备份结果
    """
    try:
        # 备份系统配置
        backup_file = config_manager.backup_config()
        
        # 备份VirtualHere配置
        vh_server_manager._backup_config()
        
        return jsonify({
            'status': True,
            'message': '配置备份成功',
            'data': {
                'backup_file': backup_file,
                'timestamp': config_manager._get_timestamp()
            }
        })
        
    except Exception as e:
        logger.error(f"备份配置失败: {e}")
        return jsonify({
            'status': False,
            'message': f'备份配置失败: {str(e)}'
        }), 500


@config_bp.route('/env', methods=['GET'])
def get_env_config() -> None:
    """
    获取环境变量配置

    Returns:
        JSON: 环境变量配置信息
    """
    try:
        env_config = config_manager.get_env_config()
        validation_result = config_manager.validate_env_config()

        return jsonify({
            'status': True,
            'message': '获取环境变量配置成功',
            'data': {
                'env_config': env_config,
                'validation': validation_result
            }
        })

    except Exception as e:
        logger.error(f"获取环境变量配置失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取环境变量配置失败: {str(e)}'
        }), 500


@config_bp.route('/env/<section>', methods=['GET'])
def get_env_section_config(section) -> Any:
    """
    获取环境变量的特定节配置

    Args:
        section: 配置节名称

    Returns:
        JSON: 配置节信息
    """
    try:
        section_config = config_manager.get_env_section_config(section)

        return jsonify({
            'status': True,
            'message': f'获取{section}环境变量配置成功',
            'data': section_config
        })

    except Exception as e:
        logger.error(f"获取环境变量配置节失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取环境变量配置节失败: {str(e)}'
        }), 500


@config_bp.route('/env/validate', methods=['GET'])
def validate_env_config() -> None:
    """
    验证环境变量配置

    Returns:
        JSON: 验证结果
    """
    try:
        validation_result = config_manager.validate_env_config()

        return jsonify({
            'status': validation_result['valid'],
            'message': '环境变量配置验证完成',
            'data': validation_result
        })

    except Exception as e:
        logger.error(f"验证环境变量配置失败: {e}")
        return jsonify({
            'status': False,
            'message': f'验证环境变量配置失败: {str(e)}'
        }), 500


@config_bp.route('/env/template', methods=['GET'])
def export_env_template() -> None:
    """
    导出环境变量模板

    Query Parameters:
        format: 返回格式 (json/text)

    Returns:
        JSON/Text: 环境变量模板
    """
    try:
        result_format = request.args.get('format', 'json')

        if result_format == 'text':
            # 返回模板文件内容
            template_path = '.env.template'
            if os.path.exists(template_path):
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()
                return template_content, 200, {'Content-Type': 'text/plain; charset=utf-8'}
            else:
                # 生成模板
                config_manager.export_env_template(template_path)
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()
                return template_content, 200, {'Content-Type': 'text/plain; charset=utf-8'}
        else:
            # JSON格式返回配置映射
            env_config = config_manager.get_env_config()
            return jsonify({
                'status': True,
                'message': '环境变量模板导出成功',
                'data': {
                    'template_file': '.env.template',
                    'env_config': env_config
                }
            })

    except Exception as e:
        logger.error(f"导出环境变量模板失败: {e}")
        return jsonify({
            'status': False,
            'message': f'导出环境变量模板失败: {str(e)}'
        }), 500


@config_bp.route('/effective', methods=['GET'])
def get_effective_config() -> None:
    """
    获取有效配置（文件配置 + 环境变量覆盖）

    Returns:
        JSON: 有效配置信息
    """
    try:
        effective_config = config_manager.get_effective_config()

        return jsonify({
            'status': True,
            'message': '获取有效配置成功',
            'data': effective_config
        })

    except Exception as e:
        logger.error(f"获取有效配置失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取有效配置失败: {str(e)}'
        }), 500


def _is_config_modifiable(section: str, key: str) -> bool:
    """
    检查配置项是否可以运行时修改
    
    Args:
        section: 配置节
        key: 配置键
    
    Returns:
        bool: 是否可修改
    """
    # 定义不可修改的配置项
    readonly_configs = {
        'database': ['db_file'],  # 数据库文件路径不可修改
        'logging': ['log_file'],  # 日志文件路径不可修改
    }
    
    if section in readonly_configs:
        return key not in readonly_configs[section]
    
    return True


def _check_restart_required(section: str, key: str) -> bool:
    """
    检查配置修改是否需要重启服务
    
    Args:
        section: 配置节
        key: 配置键
    
    Returns:
        bool: 是否需要重启
    """
    # 定义需要重启的配置项
    restart_required_configs = {
        'server': ['port', 'host'],
        'virtualhere': ['server_port'],
        'database': ['db_file']
    }
    
    if section in restart_required_configs:
        return key in restart_required_configs[section]
    
    return False
