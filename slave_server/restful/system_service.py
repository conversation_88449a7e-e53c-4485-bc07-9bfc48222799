# -*- coding: utf-8 -*-
"""
系统服务REST API
提供系统状态、健康检查等接口
"""

import os
from typing import Dict, List, Any, Optional, Union, Tuple
import time
import psutil
from datetime import datetime
from flask import Blueprint, request, jsonify
from utils.logger import get_logger
from utils.vh_server_manager import VirtualHereServerManager
from utils.vh_server_monitor import VirtualHereServerMonitor
from utils.config_manager import ConfigManager
from utils.performance_monitor import get_performance_monitor
from utils.system_monitor import get_system_monitor
from utils.security_manager import require_auth
from utils.error_handler import global_error_handler, ErrorCategory, ErrorSeverity
from utils.logger import global_log_manager, LogFormat
from db.device_dao import DeviceDAO

logger = get_logger('system_service')

# 创建蓝图
system_bp = Blueprint('system', __name__, url_prefix='/api/system')

# 初始化组件
vh_server_manager = VirtualHereServerManager()
config_manager = ConfigManager()
device_dao = DeviceDAO()


@system_bp.route('/status', methods=['GET'])
def get_system_status() -> None:
    """
    获取系统状态
    
    Returns:
        JSON: 系统状态信息
    """
    try:
        # 获取系统基本信息
        system_info = {
            'hostname': os.uname().nodename if hasattr(os, 'uname') else 'unknown',
            'platform': os.name,
            'uptime': _get_system_uptime(),
            'timestamp': datetime.now().isoformat()
        }
        
        # 获取VirtualHere服务器状态
        vh_status = vh_server_manager.get_server_status()
        
        # 获取设备统计
        device_stats = device_dao.get_device_statistics()
        
        # 获取系统资源使用情况
        resource_usage = _get_resource_usage()
        
        return jsonify({
            'status': True,
            'message': '获取系统状态成功',
            'data': {
                'system_info': system_info,
                'virtualhere_status': vh_status,
                'device_statistics': device_stats,
                'resource_usage': resource_usage
            }
        })
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取系统状态失败: {str(e)}'
        }), 500


@system_bp.route('/health', methods=['GET'])
def health_check() -> None:
    """
    健康检查
    
    Returns:
        JSON: 健康检查结果
    """
    try:
        health_status = {
            'overall': 'healthy',
            'checks': {},
            'timestamp': datetime.now().isoformat()
        }
        
        # 检查VirtualHere服务器
        vh_status = vh_server_manager.get_server_status()
        health_status['checks']['virtualhere_server'] = {
            'status': 'healthy' if vh_status['running'] else 'unhealthy',
            'details': vh_status
        }
        
        # 检查数据库连接
        try:
            device_count = device_dao.get_device_count()
            health_status['checks']['database'] = {
                'status': 'healthy',
                'details': {'device_count': device_count}
            }
        except Exception as e:
            health_status['checks']['database'] = {
                'status': 'unhealthy',
                'details': {'error': str(e)}
            }
        
        # 检查系统资源
        resource_usage = _get_resource_usage()
        cpu_healthy = resource_usage['cpu_percent'] < 90
        memory_healthy = resource_usage['memory_percent'] < 90
        disk_healthy = resource_usage['disk_percent'] < 90
        
        health_status['checks']['system_resources'] = {
            'status': 'healthy' if (cpu_healthy and memory_healthy and disk_healthy) else 'warning',
            'details': {
                'cpu_healthy': cpu_healthy,
                'memory_healthy': memory_healthy,
                'disk_healthy': disk_healthy,
                'usage': resource_usage
            }
        }
        
        # 检查配置文件
        try:
            config = config_manager.get_all_config()
            health_status['checks']['configuration'] = {
                'status': 'healthy',
                'details': {'config_loaded': len(config) > 0}
            }
        except Exception as e:
            health_status['checks']['configuration'] = {
                'status': 'unhealthy',
                'details': {'error': str(e)}
            }
        
        # 确定整体健康状态
        unhealthy_checks = [check for check in health_status['checks'].values() 
                           if check['status'] == 'unhealthy']
        warning_checks = [check for check in health_status['checks'].values() 
                         if check['status'] == 'warning']
        
        if unhealthy_checks:
            health_status['overall'] = 'unhealthy'
        elif warning_checks:
            health_status['overall'] = 'warning'
        
        # 根据健康状态返回适当的HTTP状态码
        status_code = 200
        if health_status['overall'] == 'warning':
            status_code = 200  # 警告仍然返回200
        elif health_status['overall'] == 'unhealthy':
            status_code = 503  # 服务不可用
        
        return jsonify({
            'status': health_status['overall'] != 'unhealthy',
            'message': f'健康检查完成 - {health_status["overall"]}',
            'data': health_status
        }), status_code
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            'status': False,
            'message': f'健康检查失败: {str(e)}'
        }), 500


@system_bp.route('/config', methods=['GET'])
def get_system_config() -> None:
    """
    获取系统配置
    
    Returns:
        JSON: 系统配置信息
    """
    try:
        # 获取所有配置
        config = config_manager.get_all_config()
        
        # 获取VirtualHere配置
        vh_config = vh_server_manager.get_current_config()
        
        return jsonify({
            'status': True,
            'message': '获取系统配置成功',
            'data': {
                'system_config': config,
                'virtualhere_config': vh_config
            }
        })
        
    except Exception as e:
        logger.error(f"获取系统配置失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取系统配置失败: {str(e)}'
        }), 500


@system_bp.route('/logs', methods=['GET'])
def get_system_logs() -> None:
    """
    获取系统日志
    
    Query Parameters:
        lines: 日志行数（默认100）
        level: 日志级别过滤
    
    Returns:
        JSON: 系统日志
    """
    try:
        lines = int(request.args.get('lines', 100))
        level = request.args.get('level')
        
        # 获取VirtualHere服务器日志
        vh_logs = vh_server_manager.get_server_logs(lines)
        
        # 这里可以添加应用程序日志获取逻辑
        app_logs = "应用程序日志获取功能待实现"
        
        return jsonify({
            'status': True,
            'message': '获取系统日志成功',
            'data': {
                'virtualhere_logs': vh_logs,
                'application_logs': app_logs,
                'lines_requested': lines,
                'level_filter': level
            }
        })
        
    except Exception as e:
        logger.error(f"获取系统日志失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取系统日志失败: {str(e)}'
        }), 500


@system_bp.route('/restart', methods=['POST'])
def restart_services() -> None:
    """
    重启服务
    
    Request Body:
        service: 要重启的服务名称（可选，默认重启VirtualHere）
    
    Returns:
        JSON: 重启结果
    """
    try:
        data = request.get_json() or {}
        service = data.get('service', 'virtualhere')
        
        if service == 'virtualhere':
            # 重启VirtualHere服务器
            success = vh_server_manager.restart_server()
            
            if success:
                return jsonify({
                    'status': True,
                    'message': 'VirtualHere服务器重启成功'
                })
            else:
                return jsonify({
                    'status': False,
                    'message': 'VirtualHere服务器重启失败'
                }), 500
        else:
            return jsonify({
                'status': False,
                'message': f'不支持的服务: {service}'
            }), 400
        
    except Exception as e:
        logger.error(f"重启服务失败: {e}")
        return jsonify({
            'status': False,
            'message': f'重启服务失败: {str(e)}'
        }), 500


@system_bp.route('/performance', methods=['GET'])
def get_performance_data() -> None:
    """
    获取性能数据

    Query Parameters:
        type: 数据类型 (current/history/summary)
        duration: 历史数据时长（分钟，默认60）

    Returns:
        JSON: 性能数据
    """
    try:
        data_type = request.args.get('type', 'current')
        duration = int(request.args.get('duration', 60))

        if data_type == 'current':
            # 获取当前性能数据
            performance_data = global_performance_monitor.get_current_performance()

        elif data_type == 'history':
            # 获取历史性能数据
            performance_data = global_performance_monitor.get_performance_history(duration)

        elif data_type == 'summary':
            # 获取性能摘要
            performance_data = global_performance_monitor.get_performance_summary(duration)

        else:
            return jsonify({
                'status': False,
                'message': f'不支持的数据类型: {data_type}'
            }), 400

        return jsonify({
            'status': True,
            'message': f'获取{data_type}性能数据成功',
            'data': performance_data
        })

    except Exception as e:
        logger.error(f"获取性能数据失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取性能数据失败: {str(e)}'
        }), 500


@system_bp.route('/performance/statistics', methods=['GET'])
def get_performance_statistics() -> None:
    """
    获取性能监控统计信息

    Returns:
        JSON: 性能监控统计
    """
    try:
        statistics = global_performance_monitor.get_statistics()

        return jsonify({
            'status': True,
            'message': '获取性能监控统计成功',
            'data': statistics
        })

    except Exception as e:
        logger.error(f"获取性能监控统计失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取性能监控统计失败: {str(e)}'
        }), 500


@system_bp.route('/performance/control', methods=['POST'])
def control_performance_monitor() -> None:
    """
    控制性能监控器

    Request Body:
        action: 操作类型 (start/stop/clear/config)
        sample_interval: 采样间隔（仅config操作）

    Returns:
        JSON: 操作结果
    """
    try:
        data = request.get_json() or {}
        action = data.get('action')

        if not action:
            return jsonify({
                'status': False,
                'message': '缺少action参数'
            }), 400

        if action == 'start':
            global_performance_monitor.start()
            message = '性能监控已启动'

        elif action == 'stop':
            global_performance_monitor.stop()
            message = '性能监控已停止'

        elif action == 'clear':
            global_performance_monitor.clear_history()
            message = '性能监控历史数据已清除'

        elif action == 'config':
            sample_interval = data.get('sample_interval')
            if sample_interval:
                global_performance_monitor.set_sample_interval(int(sample_interval))
                message = f'采样间隔已更新为 {sample_interval} 秒'
            else:
                return jsonify({
                    'status': False,
                    'message': '缺少sample_interval参数'
                }), 400

        else:
            return jsonify({
                'status': False,
                'message': f'不支持的操作: {action}'
            }), 400

        return jsonify({
            'status': True,
            'message': message
        })

    except Exception as e:
        logger.error(f"控制性能监控器失败: {e}")
        return jsonify({
            'status': False,
            'message': f'控制性能监控器失败: {str(e)}'
        }), 500


@system_bp.route('/diagnostic', methods=['GET'])
def run_diagnostic() -> None:
    """
    运行系统诊断

    Query Parameters:
        type: 诊断类型 (full/specific)
        item: 特定诊断项目名称（仅type=specific时）
        format: 返回格式 (json/text)

    Returns:
        JSON/Text: 诊断结果
    """
    try:
        diagnostic_type = request.args.get('type', 'full')
        item_name = request.args.get('item')
        result_format = request.args.get('format', 'json')

        if diagnostic_type == 'full':
            # 运行完整诊断
            diagnostic_result = global_self_diagnostic.run_full_diagnostic()

        elif diagnostic_type == 'specific':
            # 运行特定诊断
            if not item_name:
                return jsonify({
                    'status': False,
                    'message': '缺少item参数'
                }), 400

            diagnostic_result = global_self_diagnostic.run_specific_diagnostic(item_name)

        else:
            return jsonify({
                'status': False,
                'message': f'不支持的诊断类型: {diagnostic_type}'
            }), 400

        # 根据格式返回结果
        if result_format == 'text':
            if diagnostic_type == 'full':
                report_text = global_self_diagnostic.generate_diagnostic_report()
                return report_text, 200, {'Content-Type': 'text/plain; charset=utf-8'}
            else:
                # 简单的文本格式
                status_symbol = "✓" if diagnostic_result['status'] == 'pass' else ("⚠" if diagnostic_result['status'] == 'warning' else "✗")
                text_result = f"{status_symbol} {diagnostic_result.get('item_name', 'Unknown')}: {diagnostic_result['message']}"
                return text_result, 200, {'Content-Type': 'text/plain; charset=utf-8'}
        else:
            return jsonify({
                'status': True,
                'message': f'{diagnostic_type}诊断完成',
                'data': diagnostic_result
            })

    except Exception as e:
        logger.error(f"运行系统诊断失败: {e}")
        return jsonify({
            'status': False,
            'message': f'运行系统诊断失败: {str(e)}'
        }), 500


@system_bp.route('/diagnostic/items', methods=['GET'])
def get_diagnostic_items() -> None:
    """
    获取可用的诊断项目列表

    Returns:
        JSON: 诊断项目列表
    """
    try:
        items = global_self_diagnostic.get_diagnostic_items()

        return jsonify({
            'status': True,
            'message': '获取诊断项目列表成功',
            'data': {
                'items': items,
                'total_count': len(items)
            }
        })

    except Exception as e:
        logger.error(f"获取诊断项目列表失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取诊断项目列表失败: {str(e)}'
        }), 500


@system_bp.route('/errors', methods=['GET'])
def get_error_information() -> None:
    """
    获取错误信息

    Query Parameters:
        type: 信息类型 (statistics/history/patterns)
        limit: 历史记录数量限制（默认100）
        category: 错误类别过滤
        severity: 严重程度过滤

    Returns:
        JSON: 错误信息
    """
    try:
        info_type = request.args.get('type', 'statistics')
        limit = int(request.args.get('limit', 100))
        category_str = request.args.get('category')
        severity_str = request.args.get('severity')

        # 转换过滤参数
        category = None
        if category_str:
            try:
                category = ErrorCategory(category_str)
            except ValueError:
                return jsonify({
                    'status': False,
                    'message': f'无效的错误类别: {category_str}'
                }), 400

        severity = None
        if severity_str:
            try:
                severity = ErrorSeverity(severity_str)
            except ValueError:
                return jsonify({
                    'status': False,
                    'message': f'无效的严重程度: {severity_str}'
                }), 400

        if info_type == 'statistics':
            # 获取错误统计
            error_data = global_error_handler.get_error_statistics()

        elif info_type == 'history':
            # 获取错误历史
            error_data = global_error_handler.get_error_history(limit, category, severity)

        elif info_type == 'patterns':
            # 获取错误模式分析
            error_data = global_error_handler.analyze_error_patterns()

        else:
            return jsonify({
                'status': False,
                'message': f'不支持的信息类型: {info_type}'
            }), 400

        return jsonify({
            'status': True,
            'message': f'获取错误{info_type}成功',
            'data': error_data
        })

    except Exception as e:
        logger.error(f"获取错误信息失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取错误信息失败: {str(e)}'
        }), 500


@system_bp.route('/errors/clear', methods=['POST'])
def clear_error_history() -> None:
    """
    清理错误历史

    Request Body:
        older_than_hours: 清理多少小时前的错误（默认24）

    Returns:
        JSON: 清理结果
    """
    try:
        data = request.get_json() or {}
        older_than_hours = data.get('older_than_hours', 24)

        global_error_handler.clear_error_history(older_than_hours)

        return jsonify({
            'status': True,
            'message': f'错误历史清理完成（{older_than_hours}小时前）'
        })

    except Exception as e:
        logger.error(f"清理错误历史失败: {e}")
        return jsonify({
            'status': False,
            'message': f'清理错误历史失败: {str(e)}'
        }), 500


@system_bp.route('/errors/categories', methods=['GET'])
def get_error_categories() -> None:
    """
    获取错误类别和严重程度列表

    Returns:
        JSON: 错误分类信息
    """
    try:
        categories = [category.value for category in ErrorCategory]
        severities = [severity.value for severity in ErrorSeverity]

        return jsonify({
            'status': True,
            'message': '获取错误分类信息成功',
            'data': {
                'categories': categories,
                'severities': severities
            }
        })

    except Exception as e:
        logger.error(f"获取错误分类信息失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取错误分类信息失败: {str(e)}'
        }), 500


@system_bp.route('/logs', methods=['GET'])
def get_log_information() -> None:
    """
    获取日志信息

    Query Parameters:
        type: 信息类型 (info/content)
        logger_name: 日志记录器名称（获取内容时）
        lines: 日志行数（默认100）
        level: 日志级别过滤

    Returns:
        JSON/Text: 日志信息
    """
    try:
        info_type = request.args.get('type', 'info')
        logger_name = request.args.get('logger_name')
        lines = int(request.args.get('lines', 100))
        level_filter = request.args.get('level')

        if info_type == 'info':
            # 获取日志记录器信息
            logger_info = global_log_manager.get_logger_info()

            return jsonify({
                'status': True,
                'message': '获取日志信息成功',
                'data': {
                    'loggers': logger_info,
                    'available_formats': [fmt.value for fmt in LogFormat]
                }
            })

        elif info_type == 'content':
            # 获取日志文件内容
            if not logger_name:
                return jsonify({
                    'status': False,
                    'message': '获取日志内容需要指定logger_name参数'
                }), 400

            # 构建日志文件路径
            log_file = f"logs/{logger_name}.log"

            if not os.path.exists(log_file):
                return jsonify({
                    'status': False,
                    'message': f'日志文件不存在: {log_file}'
                }), 404

            # 读取日志文件
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    all_lines = f.readlines()

                # 获取最后N行
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

                # 级别过滤
                if level_filter:
                    filtered_lines = []
                    for line in recent_lines:
                        if level_filter.upper() in line:
                            filtered_lines.append(line)
                    recent_lines = filtered_lines

                log_content = ''.join(recent_lines)

                return jsonify({
                    'status': True,
                    'message': f'获取日志内容成功',
                    'data': {
                        'logger_name': logger_name,
                        'lines_requested': lines,
                        'lines_returned': len(recent_lines),
                        'level_filter': level_filter,
                        'content': log_content
                    }
                })

            except Exception as e:
                return jsonify({
                    'status': False,
                    'message': f'读取日志文件失败: {str(e)}'
                }), 500

        else:
            return jsonify({
                'status': False,
                'message': f'不支持的信息类型: {info_type}'
            }), 400

    except Exception as e:
        logger.error(f"获取日志信息失败: {e}")
        return jsonify({
            'status': False,
            'message': f'获取日志信息失败: {str(e)}'
        }), 500


@system_bp.route('/logs/config', methods=['POST'])
def update_log_config() -> None:
    """
    更新日志配置

    Request Body:
        logger_name: 日志记录器名称
        level: 日志级别
        format: 日志格式
        max_bytes: 最大文件大小
        backup_count: 备份文件数量

    Returns:
        JSON: 更新结果
    """
    try:
        data = request.get_json() or {}
        logger_name = data.get('logger_name')

        if not logger_name:
            return jsonify({
                'status': False,
                'message': '缺少logger_name参数'
            }), 400

        # 构建配置
        config = {}

        if 'level' in data:
            level_str = data['level'].upper()
            if hasattr(logging, level_str):
                config['level'] = getattr(logging, level_str)
            else:
                return jsonify({
                    'status': False,
                    'message': f'无效的日志级别: {data["level"]}'
                }), 400

        if 'format' in data:
            try:
                config['format'] = LogFormat(data['format'])
            except ValueError:
                return jsonify({
                    'status': False,
                    'message': f'无效的日志格式: {data["format"]}'
                }), 400

        if 'max_bytes' in data:
            config['max_bytes'] = int(data['max_bytes'])

        if 'backup_count' in data:
            config['backup_count'] = int(data['backup_count'])

        # 更新日志配置
        global_log_manager.update_logger_config(logger_name, config)

        return jsonify({
            'status': True,
            'message': f'日志配置更新成功: {logger_name}',
            'data': {
                'logger_name': logger_name,
                'updated_config': config
            }
        })

    except Exception as e:
        logger.error(f"更新日志配置失败: {e}")
        return jsonify({
            'status': False,
            'message': f'更新日志配置失败: {str(e)}'
        }), 500


@system_bp.route('/logs/rotate', methods=['POST'])
def rotate_logs() -> None:
    """
    手动轮转日志

    Request Body:
        logger_name: 日志记录器名称（可选，不指定则轮转所有）

    Returns:
        JSON: 轮转结果
    """
    try:
        data = request.get_json() or {}
        logger_name = data.get('logger_name')

        rotated_loggers = []

        if logger_name:
            # 轮转指定日志记录器
            logger_obj = logging.getLogger(logger_name)
            for handler in logger_obj.handlers:
                if isinstance(handler, logging.handlers.RotatingFileHandler):
                    handler.doRollover()
                    rotated_loggers.append(logger_name)
        else:
            # 轮转所有日志记录器
            for name, logger_obj in global_log_manager.loggers.items():
                for handler in logger_obj.handlers:
                    if isinstance(handler, logging.handlers.RotatingFileHandler):
                        handler.doRollover()
                        rotated_loggers.append(name)

        return jsonify({
            'status': True,
            'message': f'日志轮转完成',
            'data': {
                'rotated_loggers': rotated_loggers,
                'count': len(rotated_loggers)
            }
        })

    except Exception as e:
        logger.error(f"日志轮转失败: {e}")
        return jsonify({
            'status': False,
            'message': f'日志轮转失败: {str(e)}'
        }), 500


def _get_system_uptime() -> None:
    """获取系统运行时间"""
    try:
        boot_time = psutil.boot_time()
        uptime_seconds = time.time() - boot_time
        
        days = int(uptime_seconds // 86400)
        hours = int((uptime_seconds % 86400) // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        
        return {
            'seconds': int(uptime_seconds),
            'formatted': f"{days}天 {hours}小时 {minutes}分钟"
        }
    except Exception:
        return {'seconds': 0, 'formatted': '未知'}


def _get_resource_usage() -> None:
    """获取系统资源使用情况"""
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        
        # 网络统计
        network = psutil.net_io_counters()
        
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_total_gb': round(memory.total / (1024**3), 2),
            'memory_used_gb': round(memory.used / (1024**3), 2),
            'disk_percent': round((disk.used / disk.total) * 100, 2),
            'disk_total_gb': round(disk.total / (1024**3), 2),
            'disk_used_gb': round(disk.used / (1024**3), 2),
            'network_bytes_sent': network.bytes_sent,
            'network_bytes_recv': network.bytes_recv
        }
    except Exception as e:
        logger.error(f"获取资源使用情况失败: {e}")
        return {
            'cpu_percent': 0,
            'memory_percent': 0,
            'disk_percent': 0,
            'error': str(e)
        }

@system_bp.route('/control', methods=['POST'])
def control_system():
    """
    远程控制系统

    Returns:
        JSON: 控制结果
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "message": "缺少请求数据"
            }), 400

        action = data.get('action', '').lower()

        if not action:
            return jsonify({
                "success": False,
                "message": "缺少控制动作参数"
            }), 400

        logger.info(f"收到远程控制命令: {action}")

        if action == "restart":
            # 重启VirtualHere服务器
            success = vh_server_manager.restart_server()
            if success:
                return jsonify({
                    "success": True,
                    "message": "VirtualHere服务器重启成功",
                    "action": action,
                    "timestamp": datetime.now().isoformat()
                })
            else:
                return jsonify({
                    "success": False,
                    "message": "VirtualHere服务器重启失败"
                }), 500

        elif action == "stop":
            # 停止VirtualHere服务器
            success = vh_server_manager.stop_server()
            if success:
                return jsonify({
                    "success": True,
                    "message": "VirtualHere服务器停止成功",
                    "action": action,
                    "timestamp": datetime.now().isoformat()
                })
            else:
                return jsonify({
                    "success": False,
                    "message": "VirtualHere服务器停止失败"
                }), 500

        elif action == "start":
            # 启动VirtualHere服务器
            success = vh_server_manager.start_server()
            if success:
                return jsonify({
                    "success": True,
                    "message": "VirtualHere服务器启动成功",
                    "action": action,
                    "timestamp": datetime.now().isoformat()
                })
            else:
                return jsonify({
                    "success": False,
                    "message": "VirtualHere服务器启动失败"
                }), 500

        elif action == "refresh":
            # 刷新状态（强制发送心跳）
            return jsonify({
                "success": True,
                "message": "状态刷新成功",
                "action": action,
                "timestamp": datetime.now().isoformat()
            })

        else:
            return jsonify({
                "success": False,
                "message": f"不支持的控制动作: {action}"
            }), 400

    except Exception as e:
        logger.error(f"远程控制失败: {e}")
        return jsonify({
            "success": False,
            "message": f"远程控制失败: {str(e)}"
        }), 500
