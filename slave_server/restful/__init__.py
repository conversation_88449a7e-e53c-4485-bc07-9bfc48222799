# -*- coding: utf-8 -*-
"""
RESTful API模块初始化文件
"""

from flask import Blueprint
from typing import Dict, List, Any, Optional
from .device_service import device_bp
from .server_service import server_bp
from .system_service import system_bp
from .config_service import config_bp
from .command_service import command_bp


def register_blueprints(app) -> Any:
    """
    注册所有蓝图到Flask应用
    
    Args:
        app: Flask应用实例
    """
    # 注册设备管理API
    app.register_bluelogger.info(device_bp)

    # 注册服务器管理API
    app.register_bluelogger.info(server_bp, url_prefix='/api/server')

    # 注册系统状态API
    app.register_bluelogger.info(system_bp)

    # 注册配置管理API
    app.register_bluelogger.info(config_bp)

    # 注册命令服务API
    app.register_bluelogger.info(command_bp)
    
    # 注册健康检查端点
    @app.route('/health')
    def health_check() -> None:
        """健康检查端点"""
        from datetime import datetime
from utils.logger import get_logger

logger = get_logger('__init__')
