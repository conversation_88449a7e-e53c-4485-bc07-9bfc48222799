# -*- coding: utf-8 -*-
"""
命令接收服务
处理来自主服务器的命令
"""

from flask import Blueprint, request, jsonify
from typing import Dict, Any, Union, Tuple
from utils.logger import get_logger
from utils.vh_server_manager import VirtualHereServerManager
from tasks.device_monitor import DeviceMonitor

logger = get_logger('command_service')

# 创建Blueprint
command_bp = Blueprint('command', __name__, url_prefix='/api/command')

# 初始化管理器
vh_manager = VirtualHereServerManager()
device_monitor = DeviceMonitor()


@command_bp.route('/execute', methods=['POST'])
def execute_command() -> Union[Dict[str, Any], Tuple[Dict[str, Any], int]]:
    """
    执行来自主服务器的命令
    
    Returns:
        JSON响应
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '缺少请求数据'
            }), 400
        
        command_type = data.get('type')
        command_params = data.get('params', {})
        
        logger.info(f"收到命令执行请求: {command_type}")
        
        # 根据命令类型执行相应操作
        result = _execute_command_by_type(command_type, command_params)
        
        return jsonify({
            'success': True,
            'result': result,
            'message': '命令执行成功'
        })
        
    except Exception as e:
        logger.error(f"命令执行失败: {e}")
        return jsonify({
            'success': False,
            'message': f'命令执行失败: {str(e)}'
        }), 500


@command_bp.route('/status', methods=['GET'])
def get_command_status() -> Union[Dict[str, Any], Tuple[Dict[str, Any], int]]:
    """
    获取命令执行状态
    
    Returns:
        JSON响应
    """
    try:
        return jsonify({
            'success': True,
            'status': {
                'command_service_running': True,
                'vh_server_status': vh_manager.get_server_status(),
                'device_monitor_status': device_monitor.is_running()
            }
        })
        
    except Exception as e:
        logger.error(f"获取命令状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取状态失败: {str(e)}'
        }), 500


def _execute_command_by_type(command_type: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    根据命令类型执行相应操作
    
    Args:
        command_type: 命令类型
        params: 命令参数
        
    Returns:
        Dict: 执行结果
    """
    if command_type == 'restart_vh_server':
        # 重启VirtualHere服务器
        success = vh_manager.restart_server()
        return {
            'action': 'restart_vh_server',
            'success': success,
            'status': vh_manager.get_server_status()
        }
    
    elif command_type == 'refresh_devices':
        # 刷新设备列表
        devices = device_monitor.discover_devices()
        return {
            'action': 'refresh_devices',
            'success': True,
            'devices': devices
        }
    
    elif command_type == 'update_config':
        # 更新配置
        config_section = params.get('section')
        config_data = params.get('data', {})
        
        # 这里应该调用配置管理器更新配置
        # 暂时返回成功状态
        return {
            'action': 'update_config',
            'success': True,
            'section': config_section,
            'updated_keys': list(config_data.keys())
        }
    
    elif command_type == 'get_system_info':
        # 获取系统信息
        import platform
        import psutil
        
        return {
            'action': 'get_system_info',
            'success': True,
            'system_info': {
                'platform': platform.platform(),
                'architecture': platform.machine(),
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total,
                'disk_usage': psutil.disk_usage('/').percent
            }
        }
    
    else:
        raise ValueError(f"不支持的命令类型: {command_type}")
