# VirtualHere深度逆向工程指南

## 第一部分：VirtualHere破解尝试总结

### 1. 许可证时间戳生成和修改

#### 实施步骤：
1. **分析参考许可证**：发现6个时间戳位置（2014、2018、2022、2003、2029年）
2. **生成时间戳许可证**：创建5个未来时间戳许可证（2035年、2038年过期）
3. **应用配置**：将`timestamp_test_1.ini`复制为`config.ini`

#### 测试结果：
- ✅ **许可证格式验证通过** - 从"Invalid License"变为正常启动
- ✅ **时间戳验证通过** - 消除了"许可证已过期"错误
- ❌ **设备限制未改变** - 仍显示`max_devices=1`

#### 失败原因：
只解决了许可证验证问题，但没有影响设备数量限制逻辑

### 2. 字符串替换（unlicensed → commercial）

#### 实施步骤：
```python
# 替换所有unlicensed字符串为commercial
data[pos:pos+10] = b'commercial'
# 修改商业版配置：commercial,1, → commercial,99,
```

#### 测试结果：
- ❌ **完全无效** - 日志仍显示`Server licensed to=unlicensed max_devices=1`
- ❌ **字符串替换被忽略** - 程序可能有多重验证机制

#### 失败原因：
程序可能有校验和验证或从其他位置读取许可证状态

### 3. 设备限制常量修改

#### 实施步骤：
```python
# 修改unlicensed配置
b'unlicensed,1,' → b'unlicensed,99'
# 修改32位常量
b'\x01\x00\x00\x00' → b'\x63\x00\x00\x00'  # 1 → 99
```

#### 测试结果：
- ❌ **显示未改变** - 仍显示`max_devices=1`
- ❌ **实际限制未改变** - 仍然出现设备绑定错误

#### 失败原因：
修改的可能是诱饵/蜜罐数据，真正的限制逻辑在其他位置

### 4. 错误字符串破坏

#### 实施步骤：
```python
# 破坏错误字符串
b'BIND MAX_DEVICES_EXCEEDED' → b'BIND_DEVICES_OK_______'
```

#### 测试结果：
- ✅ **错误字符串修改成功** - 日志显示`BIND_DEVICES_OK_______DED`
- ❌ **限制逻辑完全未受影响** - 设备绑定仍然失败
- ⚠️ **程序自我修复** - 后续测试中错误字符串恢复原样

#### 失败原因：
错误报告与限制执行是分离的，破坏报告不影响实际限制

### 5. 暴力修改所有相关常量

#### 实施步骤：
```python
# 修改所有设备相关的常量1为99
# 8位、16位、32位常量全面修改
# 总计修改41个位置
```

#### 测试结果：
- ❌ **完全无效** - `max_devices=1`保持不变
- ❌ **程序行为无任何改变**

#### 失败原因：
VirtualHere具有强大的反篡改保护机制

## 第二部分：深度逆向工程计划

### 1. 使用Ghidra进行静态分析

#### 导入和初始设置：
```bash
1. 启动Ghidra：双击ghidraRun.bat
2. 创建新项目：File → New Project → Non-Shared Project
3. 导入文件：File → Import File → 选择vhusbdwin64_original.exe
4. 分析选项：Analysis → Auto Analyze → 使用默认设置
```

#### 关键分析目标：

**A. 定位许可证验证函数**
```
搜索目标：
- 字符串："unlicensed", "max_devices", "Server licensed to"
- 函数：包含许可证解析逻辑的函数
- 交叉引用：追踪字符串的使用位置
```

**B. 追踪设备限制设置逻辑**
```
分析重点：
1. 找到设置max_devices变量的代码
2. 分析许可证类型到设备数量的映射
3. 识别设备计数检查函数
4. 定位BIND MAX_DEVICES_EXCEEDED错误的触发条件
```

**C. 重点分析的代码段**
```
优先级1：程序启动时的许可证初始化
优先级2：设备绑定时的限制检查
优先级3：许可证验证和解析函数
优先级4：错误处理和报告机制
```

### 2. 使用x64dbg进行动态调试

#### 基本调试设置：
```bash
1. 启动x64dbg
2. 打开文件：File → Open → vhusbdwin64_original.exe
3. 设置工作目录：确保config.ini在正确位置
```

#### 关键断点设置：

**A. 许可证验证断点**
```
目标API：
- CreateFileA/CreateFileW (配置文件读取)
- ReadFile (许可证内容读取)
- 字符串比较函数 (strcmp, strncmp)
```

**B. 设备限制检查断点**
```
策略：
1. 在"BIND MAX_DEVICES_EXCEEDED"字符串上设置内存断点
2. 在设备绑定函数入口设置断点
3. 监控设备计数变量的修改
```

**C. 内存监控重点**
```
观察目标：
- 许可证状态变量
- 设备数量限制变量
- 当前绑定设备计数
- 设备绑定函数的参数和返回值
```

#### 实时修改策略：
```
1. 定位设备限制变量在内存中的位置
2. 在运行时修改该变量的值
3. 观察程序行为变化
4. 记录有效的修改位置和数值
```

### 3. 使用HxD进行精确二进制修改

#### 基于分析结果的修改策略：

**A. 静态分析指导的修改**
```
1. 根据Ghidra分析结果定位关键函数
2. 找到设备限制检查的汇编指令
3. 计算精确的文件偏移地址
4. 使用HxD进行字节级修改
```

**B. 动态调试验证的修改**
```
1. 根据x64dbg调试结果确认内存地址
2. 将内存地址转换为文件偏移
3. 修改关键的比较指令或常量
4. 验证修改效果
```

**C. 可能的修改目标**
```
指令修改：
- JE → JMP (跳过设备限制检查)
- CMP EAX, 1 → CMP EAX, 0 (修改比较值)
- MOV [mem], 1 → MOV [mem], 99 (修改设备限制常量)

常量修改：
- 设备限制常量：1 → 99
- 许可证验证返回值修改
- 错误代码修改
```

## 第三部分：执行优先级和时间安排

### 任务清单（按重要性排序）

| 优先级 | 任务 | 预期时间 | 难度 | 关键产出 |
|--------|------|----------|------|----------|
| 🔴 高 | Ghidra静态分析 | 2-3小时 | 中等 | 许可证验证函数位置 |
| 🔴 高 | x64dbg动态调试 | 1-2小时 | 中等 | 设备限制变量地址 |
| 🟡 中 | 内存实时修改测试 | 30分钟 | 简单 | 有效修改方案 |
| 🟡 中 | HxD精确二进制修改 | 30分钟 | 简单 | 最终破解文件 |
| 🟢 低 | 验证和优化 | 30分钟 | 简单 | 稳定的解决方案 |

### 详细执行计划

#### 阶段1：静态分析（2-3小时）
```
步骤1：Ghidra环境准备 (15分钟)
步骤2：程序导入和自动分析 (30分钟)
步骤3：字符串引用分析 (45分钟)
步骤4：函数调用关系分析 (60分钟)
步骤5：关键代码段识别 (30分钟)
```

#### 阶段2：动态调试（1-2小时）
```
步骤1：x64dbg环境设置 (15分钟)
步骤2：关键断点设置 (30分钟)
步骤3：程序执行和观察 (45分钟)
步骤4：内存地址确认 (30分钟)
```

#### 阶段3：实施修改（1小时）
```
步骤1：内存实时修改测试 (30分钟)
步骤2：二进制文件修改 (20分钟)
步骤3：效果验证 (10分钟)
```

### 可能的技术障碍和解决方案

#### 障碍1：代码混淆和保护
**解决方案**：
- 使用Ghidra的反编译功能
- 分析程序的控制流图
- 识别关键的API调用

#### 障碍2：动态反调试检测
**解决方案**：
- 使用x64dbg的反反调试插件
- 修改调试器检测代码
- 使用进程注入技术

#### 障碍3：多层验证机制
**解决方案**：
- 逐层分析每个验证点
- 使用多点修改策略
- 结合静态和动态分析

#### 障碍4：程序自我修复
**解决方案**：
- 找到自我修复的触发条件
- 禁用完整性检查
- 修改校验和计算

### 成功指标

#### 最终目标：
- ✅ 服务器日志显示：`Server licensed to=unlicensed max_devices=99`
- ✅ 客户端显示：`最大设备数：99 (unlicensed)`
- ✅ 能够同时绑定多个USB设备
- ✅ 不再出现`BIND MAX_DEVICES_EXCEEDED`错误

#### 中间里程碑：
1. 成功定位许可证验证函数
2. 找到设备限制设置的确切位置
3. 实现内存中的实时修改
4. 创建稳定的二进制修改版本

---

**注意**：本文档仅用于技术研究和学习目的。在实际使用中，建议购买正版许可证以支持软件开发者。
