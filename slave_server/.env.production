# OmniLink从服务器生产环境配置
# 请根据实际部署环境修改以下配置值

# 应用基础配置
FLASK_ENV=production
DEBUG=false
SECRET_KEY=your-production-secret-key-here

# 数据库配置
DATABASE_PATH=data/slave_server.db
DATABASE_POOL_SIZE=20
DATABASE_TIMEOUT=30

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/slave_server.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# 安全配置
TOKEN_EXPIRY=3600
REFRESH_TOKEN_EXPIRY=604800
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=300
PASSWORD_MIN_LENGTH=8

# 缓存配置
CACHE_DEFAULT_TTL=3600
CACHE_MAX_SIZE=10000
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 监控配置
MONITOR_CHECK_INTERVAL=60
MONITOR_HISTORY_SIZE=1440
PERFORMANCE_MONITORING=true
SYSTEM_MONITORING=true

# 备份配置
BACKUP_MAX_COUNT=30
BACKUP_INTERVAL=3600
BACKUP_COMPRESSION=true

# VirtualHere配置
VH_SERVER_PORT=7575
VH_CLIENT_TIMEOUT=30
VH_RETRY_COUNT=3
VH_RETRY_DELAY=5

# 主服务器通信配置
MASTER_SERVER_URL=http://master-server:8080
HEARTBEAT_INTERVAL=30
HEARTBEAT_TIMEOUT=10
REGISTRATION_TIMEOUT=30

# 设备监控配置
DEVICE_SCAN_INTERVAL=3
DEVICE_TIMEOUT=30
DEVICE_RETRY_COUNT=3

# 网络配置
API_HOST=0.0.0.0
API_PORT=8081
API_WORKERS=4
API_TIMEOUT=30

# 性能配置
MAX_CONNECTIONS=100
CONNECTION_TIMEOUT=30
REQUEST_TIMEOUT=30
RESPONSE_TIMEOUT=30
