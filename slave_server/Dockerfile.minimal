# OmniLink从服务器 v2.0 - 极简轻量化Docker镜像
# 基于alpine Linux，专注USB设备代理功能
# 目标：镜像大小<100MB，内存占用<128MB

FROM python:3.11-alpine

# 设置维护者信息
LABEL maintainer="OmniLink Team"
LABEL description="OmniLink Minimal Slave Server v2.0 for USB device proxy"
LABEL version="2.0.0"

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app \
    AIOHTTP_NO_EXTENSIONS=1

# 安装系统依赖 - 极简化安装
RUN apk add --no-cache --virtual .build-deps \
    # 编译工具（临时）
    gcc \
    musl-dev \
    linux-headers \
    # USB设备支持
    && apk add --no-cache \
    libusb \
    libusb-dev \
    curl \
    # 清理编译依赖在同一层
    && rm -rf /var/cache/apk/*

# 创建应用用户（非root用户）
RUN addgroup -g 1000 omnilink && \
    adduser -D -s /bin/sh -u 1000 -G omnilink omnilink

# 复制requirements文件并安装Python依赖
COPY requirements_minimal.txt /app/
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements_minimal.txt && \
    # 清理编译工具
    apk del .build-deps && \
    rm -rf /var/cache/apk/* /tmp/* /var/tmp/*

# 复制核心代码
COPY core/ /app/core/
COPY main_minimal.py /app/
COPY config/ /app/config/

# 复制VirtualHere文件
COPY virtualhere/ /app/virtualhere/

# 根据架构选择合适的二进制文件并应用优化
RUN if [ "$(uname -m)" = "armv7l" ] || [ "$(uname -m)" = "aarch64" ]; then \
        echo "检测到ARM架构，使用本地ARM版本"; \
        cp /app/virtualhere/vhusbdarmpi3 /app/vhusbd; \
    else \
        echo "检测到x86架构，下载对应版本"; \
        wget -O /app/vhusbd https://www.virtualhere.com/sites/default/files/usbserver/vhusbdx86_64; \
    fi && \
    chmod +x /app/vhusbd

# 创建必要的目录并设置权限
RUN mkdir -p /app/logs /app/data /app/config /app/backups /app/temp && \
    chown -R omnilink:omnilink /app && \
    chmod -R 755 /app

# 创建优化的配置文件
RUN echo "[server]" > /app/config/slave_server.ini && \
    echo "host = 0.0.0.0" >> /app/config/slave_server.ini && \
    echo "port = 8889" >> /app/config/slave_server.ini && \
    echo "" >> /app/config/slave_server.ini && \
    echo "[virtualhere]" >> /app/config/slave_server.ini && \
    echo "server_port = 7575" >> /app/config/slave_server.ini && \
    echo "binary_path = /app/vhusbd" >> /app/config/slave_server.ini && \
    echo "" >> /app/config/slave_server.ini && \
    echo "[logging]" >> /app/config/slave_server.ini && \
    echo "log_level = INFO" >> /app/config/slave_server.ini && \
    echo "log_file = logs/slave_server.log" >> /app/config/slave_server.ini && \
    chown omnilink:omnilink /app/config/slave_server.ini

# 暴露端口
EXPOSE 8889 7575

# 健康检查 - 优化为轻量级检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8889/api/system/health || exit 1

# 切换到非root用户
USER omnilink

# 启动命令 - 使用新的极简主程序
CMD ["python", "main_minimal.py"]
