[{"test": "Environment Check", "passed": false, "details": "Running in WSL - VirtualHere requires real Linux environment", "timestamp": 1753955855.4325309}, {"test": "Binary Availability", "passed": true, "details": "VirtualHere binary working: virtualhere/chake/online/VirtualHere/VirtualHere/Server/4.3.3/vhusbdx86_64", "timestamp": 1753955855.455385}, {"test": "VirtualHere Group", "passed": false, "details": "virtualhere group not found", "timestamp": 1753955855.4572663}, {"test": "USB Device Detection", "passed": false, "details": "lsusb command failed", "timestamp": 1753955855.4611075}, {"test": "Systemd Service", "passed": false, "details": "systemd service file not found", "timestamp": 1753955855.4612308}, {"test": "Network Connectivity", "passed": true, "details": "Found 1 network interfaces", "timestamp": 1753955855.4642599}, {"test": "VirtualHere Startup", "passed": false, "details": "Startup failed: Permission denied, did you run with sudo?\n", "timestamp": 1753955858.479217}]