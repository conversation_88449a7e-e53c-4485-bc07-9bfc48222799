# -*- coding: utf-8 -*-
"""
错误处理和异常恢复模块
提供全局异常处理、自动重连、故障恢复功能
"""

import time
import threading
import traceback
import sys
import os
from enum import Enum
from typing import Callable, Any, Optional, Dict, List, Union, Tuple
from functools import wraps
from datetime import datetime, timedelta
from utils.logger import get_logger

logger = get_logger('error_handler')


class ErrorCategory(Enum):
    """错误分类"""
    NETWORK = "network"              # 网络相关错误
    DATABASE = "database"            # 数据库相关错误
    VIRTUALHERE = "virtualhere"      # VirtualHere相关错误
    SYSTEM = "system"                # 系统相关错误
    CONFIGURATION = "configuration"  # 配置相关错误
    PERMISSION = "permission"        # 权限相关错误
    RESOURCE = "resource"            # 资源相关错误
    VALIDATION = "validation"        # 验证相关错误
    UNKNOWN = "unknown"              # 未知错误


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"          # 低级错误，不影响核心功能
    MEDIUM = "medium"    # 中级错误，影响部分功能
    HIGH = "high"        # 高级错误，影响核心功能
    CRITICAL = "critical" # 严重错误，系统无法正常运行


class ErrorAction(Enum):
    """错误处理动作"""
    IGNORE = "ignore"        # 忽略错误
    LOG = "log"              # 仅记录日志
    RETRY = "retry"          # 重试操作
    RESTART = "restart"      # 重启服务
    ALERT = "alert"          # 发送告警
    SHUTDOWN = "shutdown"    # 关闭系统


class ErrorInfo:
    """错误信息类"""

    def __init__(self, error: Exception, context: str = "", category: ErrorCategory = ErrorCategory.UNKNOWN,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM, action: ErrorAction = ErrorAction.LOG) -> Any:
        self.error = error
        self.context = context
        self.category = category
        self.severity = severity
        self.action = action
        self.timestamp = datetime.now()
        self.error_id = f"{category.value}_{int(time.time())}"
        self.traceback = traceback.format_exc()
        self.retry_count = 0
        self.resolved = False


class ErrorHandler:
    """全局错误处理器"""

    def __init__(self) -> None:
        """初始化错误处理器"""
        self.error_count = 0
        self.last_error_time = 0
        self.recovery_callbacks = []
        self.error_history: List[ErrorInfo] = []
        self.error_patterns: Dict[str, Dict] = {}
        self.max_history_size = 1000
        self.error_rules = self._init_error_rules()
        self.lock = threading.Lock()
    
    def register_recovery_callback(self, callback: Callable) -> Any:
        """
        注册恢复回调函数
        
        Args:
            callback: 恢复回调函数
        """
        self.recovery_callbacks.append(callback)
        logger.info(f"注册恢复回调: {callback.__name__}")

    def _init_error_rules(self) -> Dict[str, Dict]:
        """初始化错误处理规则"""
        return {
            # 网络错误规则
            'ConnectionError': {
                'category': ErrorCategory.NETWORK,
                'severity': ErrorSeverity.HIGH,
                'action': ErrorAction.RETRY,
                'max_retries': 3,
                'retry_delay': 5
            },
            'TimeoutError': {
                'category': ErrorCategory.NETWORK,
                'severity': ErrorSeverity.MEDIUM,
                'action': ErrorAction.RETRY,
                'max_retries': 2,
                'retry_delay': 3
            },
            'requests.exceptions.ConnectionError': {
                'category': ErrorCategory.NETWORK,
                'severity': ErrorSeverity.HIGH,
                'action': ErrorAction.RETRY,
                'max_retries': 3,
                'retry_delay': 5
            },

            # 数据库错误规则
            'peewee.OperationalError': {
                'category': ErrorCategory.DATABASE,
                'severity': ErrorSeverity.HIGH,
                'action': ErrorAction.RESTART,
                'max_retries': 2,
                'retry_delay': 10
            },
            'sqlite3.OperationalError': {
                'category': ErrorCategory.DATABASE,
                'severity': ErrorSeverity.HIGH,
                'action': ErrorAction.RESTART,
                'max_retries': 1,
                'retry_delay': 5
            },

            # VirtualHere错误规则
            'VirtualHereError': {
                'category': ErrorCategory.VIRTUALHERE,
                'severity': ErrorSeverity.HIGH,
                'action': ErrorAction.RESTART,
                'max_retries': 2,
                'retry_delay': 10
            },

            # 系统错误规则
            'OSError': {
                'category': ErrorCategory.SYSTEM,
                'severity': ErrorSeverity.HIGH,
                'action': ErrorAction.ALERT,
                'max_retries': 1,
                'retry_delay': 5
            },
            'PermissionError': {
                'category': ErrorCategory.PERMISSION,
                'severity': ErrorSeverity.HIGH,
                'action': ErrorAction.ALERT,
                'max_retries': 0,
                'retry_delay': 0
            },

            # 资源错误规则
            'MemoryError': {
                'category': ErrorCategory.RESOURCE,
                'severity': ErrorSeverity.CRITICAL,
                'action': ErrorAction.SHUTDOWN,
                'max_retries': 0,
                'retry_delay': 0
            },
            'DiskSpaceError': {
                'category': ErrorCategory.RESOURCE,
                'severity': ErrorSeverity.HIGH,
                'action': ErrorAction.ALERT,
                'max_retries': 0,
                'retry_delay': 0
            },

            # 配置错误规则
            'ConfigurationError': {
                'category': ErrorCategory.CONFIGURATION,
                'severity': ErrorSeverity.HIGH,
                'action': ErrorAction.ALERT,
                'max_retries': 0,
                'retry_delay': 0
            },

            # 验证错误规则
            'ValidationError': {
                'category': ErrorCategory.VALIDATION,
                'severity': ErrorSeverity.LOW,
                'action': ErrorAction.LOG,
                'max_retries': 0,
                'retry_delay': 0
            }
        }

    def classify_error(self, error: Exception, context: str = "") -> ErrorInfo:
        """
        分类错误

        Args:
            error: 异常对象
            context: 错误上下文

        Returns:
            ErrorInfo: 错误信息对象
        """
        error_type = type(error).__name__
        error_module = type(error).__module__
        full_error_type = f"{error_module}.{error_type}" if error_module != 'builtins' else error_type

        # 查找匹配的错误规则
        rule = None
        for pattern in [full_error_type, error_type]:
            if pattern in self.error_rules:
                rule = self.error_rules[pattern]
                break

        # 如果没有找到规则，使用默认规则
        if not rule:
            rule = {
                'category': ErrorCategory.UNKNOWN,
                'severity': ErrorSeverity.MEDIUM,
                'action': ErrorAction.LOG,
                'max_retries': 0,
                'retry_delay': 0
            }

        # 创建错误信息对象
        error_info = ErrorInfo(
            error=error,
            context=context,
            category=rule['category'],
            severity=rule['severity'],
            action=rule['action']
        )

        # 添加规则信息
        error_info.max_retries = rule.get('max_retries', 0)
        error_info.retry_delay = rule.get('retry_delay', 0)

        return error_info

    def handle_error_info(self, error_info: ErrorInfo) -> bool:
        """
        处理错误信息

        Args:
            error_info: 错误信息对象

        Returns:
            bool: 是否成功处理
        """
        try:
            with self.lock:
                # 添加到错误历史
                self.error_history.append(error_info)
                if len(self.error_history) > self.max_history_size:
                    self.error_history.pop(0)

                # 更新错误统计
                self.error_count += 1
                self.last_error_time = time.time()

            # 记录错误日志
            self._log_error(error_info)

            # 执行错误处理动作
            return self._execute_error_action(error_info)

        except Exception as e:
            logger.error(f"处理错误信息失败: {e}")
            return False

    def _log_error(self, error_info: ErrorInfo) -> Any:
        """记录错误日志"""
        log_message = (
            f"[{error_info.error_id}] {error_info.category.value.upper()} ERROR "
            f"({error_info.severity.value}): {str(error_info.error)}"
        )

        if error_info.context:
            log_message += f" | Context: {error_info.context}"

        # 根据严重程度选择日志级别
        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
            logger.critical(f"Traceback: {error_info.traceback}")
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
            logger.debug(f"Traceback: {error_info.traceback}")
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)

    def _execute_error_action(self, error_info: ErrorInfo) -> bool:
        """执行错误处理动作"""
        try:
            if error_info.action == ErrorAction.IGNORE:
                return True

            elif error_info.action == ErrorAction.LOG:
                # 已在_log_error中处理
                return True

            elif error_info.action == ErrorAction.RETRY:
                return self._handle_retry(error_info)

            elif error_info.action == ErrorAction.RESTART:
                return self._handle_restart(error_info)

            elif error_info.action == ErrorAction.ALERT:
                return self._handle_alert(error_info)

            elif error_info.action == ErrorAction.SHUTDOWN:
                return self._handle_shutdown(error_info)

            else:
                logger.warning(f"未知的错误处理动作: {error_info.action}")
                return False

        except Exception as e:
            logger.error(f"执行错误处理动作失败: {e}")
            return False

    def _handle_retry(self, error_info: ErrorInfo) -> bool:
        """处理重试动作"""
        if error_info.retry_count >= error_info.max_retries:
            logger.warning(f"错误 {error_info.error_id} 已达到最大重试次数")
            return False

        error_info.retry_count += 1
        logger.info(f"错误 {error_info.error_id} 将在 {error_info.retry_delay} 秒后重试 "
                   f"({error_info.retry_count}/{error_info.max_retries})")

        # 这里可以添加具体的重试逻辑
        return True

    def _handle_restart(self, error_info: ErrorInfo) -> bool:
        """处理重启动作"""
        logger.warning(f"错误 {error_info.error_id} 触发重启动作")

        # 执行恢复回调
        for callback in self.recovery_callbacks:
            try:
                callback()
                logger.info(f"执行恢复回调成功: {callback.__name__}")
            except Exception as e:
                logger.error(f"执行恢复回调失败 {callback.__name__}: {e}")

        return True

    def _handle_alert(self, error_info: ErrorInfo) -> bool:
        """处理告警动作"""
        logger.warning(f"错误 {error_info.error_id} 触发告警")

        # 这里可以添加告警通知逻辑（邮件、短信、webhook等）
        alert_message = (
            f"OmniLink从服务器告警\n"
            f"错误ID: {error_info.error_id}\n"
            f"类别: {error_info.category.value}\n"
            f"严重程度: {error_info.severity.value}\n"
            f"错误: {str(error_info.error)}\n"
            f"上下文: {error_info.context}\n"
            f"时间: {error_info.timestamp}"
        )

        logger.critical(f"ALERT: {alert_message}")
        return True

    def _handle_shutdown(self, error_info: ErrorInfo) -> bool:
        """处理关闭动作"""
        logger.critical(f"错误 {error_info.error_id} 触发系统关闭")

        # 这里可以添加优雅关闭逻辑
        try:
            # 保存重要数据
            # 通知其他组件
            # 执行清理操作

            logger.critical("系统即将关闭...")
            # os._exit(1)  # 强制退出

        except Exception as e:
            logger.error(f"执行关闭操作失败: {e}")

        return True

    def get_error_statistics(self) -> Dict[str, Any]:
        """
        获取错误统计信息

        Returns:
            Dict: 错误统计信息
        """
        with self.lock:
            now = datetime.now()
            last_hour = now - timedelta(hours=1)
            last_day = now - timedelta(days=1)

            # 按时间段统计
            recent_errors = [e for e in self.error_history if e.timestamp > last_hour]
            daily_errors = [e for e in self.error_history if e.timestamp > last_day]

            # 按类别统计
            category_stats = {}
            for category in ErrorCategory:
                category_errors = [e for e in self.error_history if e.category == category]
                category_stats[category.value] = len(category_errors)

            # 按严重程度统计
            severity_stats = {}
            for severity in ErrorSeverity:
                severity_errors = [e for e in self.error_history if e.severity == severity]
                severity_stats[severity.value] = len(severity_errors)

            # 最常见的错误
            error_types = {}
            for error_info in self.error_history:
                error_type = type(error_info.error).__name__
                error_types[error_type] = error_types.get(error_type, 0) + 1

            top_errors = sorted(error_types.items(), key=lambda x: x[1], reverse=True)[:5]

            return {
                'total_errors': len(self.error_history),
                'recent_errors_1h': len(recent_errors),
                'recent_errors_24h': len(daily_errors),
                'category_distribution': category_stats,
                'severity_distribution': severity_stats,
                'top_error_types': top_errors,
                'last_error_time': self.last_error_time,
                'error_rate_1h': len(recent_errors) / 60 if recent_errors else 0,  # 每分钟错误数
                'error_rate_24h': len(daily_errors) / 1440 if daily_errors else 0  # 每分钟错误数
            }

    def get_error_history(self, limit: int = 100, category: Optional[ErrorCategory] = None,
                         severity: Optional[ErrorSeverity] = None) -> List[Dict[str, Any]]:
        """
        获取错误历史

        Args:
            limit: 返回数量限制
            category: 错误类别过滤
            severity: 严重程度过滤

        Returns:
            List: 错误历史列表
        """
        with self.lock:
            filtered_errors = self.error_history

            # 按类别过滤
            if category:
                filtered_errors = [e for e in filtered_errors if e.category == category]

            # 按严重程度过滤
            if severity:
                filtered_errors = [e for e in filtered_errors if e.severity == severity]

            # 按时间倒序排列并限制数量
            filtered_errors = sorted(filtered_errors, key=lambda x: x.timestamp, reverse=True)[:limit]

            # 转换为字典格式
            return [
                {
                    'error_id': error_info.error_id,
                    'error_type': type(error_info.error).__name__,
                    'error_message': str(error_info.error),
                    'context': error_info.context,
                    'category': error_info.category.value,
                    'severity': error_info.severity.value,
                    'action': error_info.action.value,
                    'timestamp': error_info.timestamp.isoformat(),
                    'retry_count': error_info.retry_count,
                    'resolved': error_info.resolved
                }
                for error_info in filtered_errors
            ]

    def clear_error_history(self, older_than_hours: int = 24) -> Any:
        """
        清理错误历史

        Args:
            older_than_hours: 清理多少小时前的错误
        """
        with self.lock:
            cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
            original_count = len(self.error_history)

            self.error_history = [e for e in self.error_history if e.timestamp > cutoff_time]

            cleared_count = original_count - len(self.error_history)
            logger.info(f"清理了 {cleared_count} 条错误历史记录")

    def add_error_rule(self, error_pattern: str, category: ErrorCategory, severity: ErrorSeverity,
                      action: ErrorAction, max_retries: int = 0, retry_delay: int = 0) -> Any:
        """
        添加错误处理规则

        Args:
            error_pattern: 错误模式（异常类名）
            category: 错误类别
            severity: 严重程度
            action: 处理动作
            max_retries: 最大重试次数
            retry_delay: 重试延迟
        """
        self.error_rules[error_pattern] = {
            'category': category,
            'severity': severity,
            'action': action,
            'max_retries': max_retries,
            'retry_delay': retry_delay
        }

        logger.info(f"添加错误处理规则: {error_pattern} -> {category.value}/{severity.value}/{action.value}")

    def analyze_error_patterns(self) -> Dict[str, Any]:
        """
        分析错误模式

        Returns:
            Dict: 错误模式分析结果
        """
        with self.lock:
            if not self.error_history:
                return {'patterns': [], 'recommendations': []}

            # 分析错误频率模式
            error_frequency = {}
            for error_info in self.error_history:
                error_type = type(error_info.error).__name__
                hour = error_info.timestamp.hour
                key = f"{error_type}_{hour}"
                error_frequency[key] = error_frequency.get(key, 0) + 1

            # 分析错误集中时间段
            hour_distribution = {}
            for error_info in self.error_history:
                hour = error_info.timestamp.hour
                hour_distribution[hour] = hour_distribution.get(hour, 0) + 1

            peak_hours = sorted(hour_distribution.items(), key=lambda x: x[1], reverse=True)[:3]

            # 生成建议
            recommendations = []

            # 检查是否有频繁的网络错误
            network_errors = [e for e in self.error_history if e.category == ErrorCategory.NETWORK]
            if len(network_errors) > len(self.error_history) * 0.3:
                recommendations.append("网络错误频率较高，建议检查网络连接稳定性")

            # 检查是否有频繁的数据库错误
            db_errors = [e for e in self.error_history if e.category == ErrorCategory.DATABASE]
            if len(db_errors) > 5:
                recommendations.append("数据库错误较多，建议检查数据库配置和连接")

            # 检查是否有严重错误
            critical_errors = [e for e in self.error_history if e.severity == ErrorSeverity.CRITICAL]
            if critical_errors:
                recommendations.append(f"发现 {len(critical_errors)} 个严重错误，需要立即处理")

            return {
                'patterns': {
                    'error_frequency': error_frequency,
                    'peak_hours': peak_hours,
                    'hour_distribution': hour_distribution
                },
                'recommendations': recommendations,
                'analysis_time': datetime.now().isoformat()
            }
    
    def handle_error(self, error: Exception, context: str = "") -> bool:
        """
        处理错误（使用新的分类系统）

        Args:
            error: 异常对象
            context: 错误上下文

        Returns:
            bool: 是否成功处理
        """
        # 分类错误
        error_info = self.classify_error(error, context)

        # 处理错误信息
        return self.handle_error_info(error_info)
    
    def _trigger_recovery(self, error: Exception, context: str) -> Any:
        """
        触发恢复机制
        
        Args:
            error: 异常对象
            context: 错误上下文
        """
        for callback in self.recovery_callbacks:
            try:
                callback(error, context)
            except Exception as e:
                logger.error(f"恢复回调执行失败: {e}")
    
    def get_error_stats(self) -> dict:
        """
        获取错误统计
        
        Returns:
            dict: 错误统计信息
        """
        return {
            'error_count': self.error_count,
            'last_error_time': self.last_error_time,
            'recovery_callbacks_count': len(self.recovery_callbacks)
        }


# 全局错误处理器实例
global_error_handler = ErrorHandler()


def retry_on_error(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0) -> Any:
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间
        backoff: 退避倍数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            current_delay = delay
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        logger.warning(f"{func.__name__} 执行失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                        time.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logger.error(f"{func.__name__} 最终失败: {e}")
                        global_error_handler.handle_error(e, func.__name__)
            
            raise last_exception
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default_return=None, context: str = "", **kwargs) -> Any:
    """
    安全执行函数
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        default_return: 默认返回值
        context: 执行上下文
        **kwargs: 函数关键字参数
    
    Returns:
        函数执行结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        global_error_handler.handle_error(e, context or func.__name__)
        return default_return


class AutoRecovery:
    """自动恢复管理器"""
    
    def __init__(self) -> None:
        """初始化自动恢复管理器"""
        self.recovery_tasks = {}
        self.running = False
        self.thread = None
    
    def register_task(self, name: str, check_func: Callable, recovery_func: Callable, interval: int = 30) -> Any:
        """
        注册恢复任务
        
        Args:
            name: 任务名称
            check_func: 检查函数，返回True表示正常
            recovery_func: 恢复函数
            interval: 检查间隔（秒）
        """
        self.recovery_tasks[name] = {
            'check_func': check_func,
            'recovery_func': recovery_func,
            'interval': interval,
            'last_check': 0,
            'failure_count': 0
        }
        logger.info(f"注册自动恢复任务: {name}")
    
    def start(self) -> None:
        """启动自动恢复"""
        if self.running:
            logger.warning("自动恢复已在运行中")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._recovery_loop, daemon=True)
        self.thread.start()
        logger.info("自动恢复已启动")
    
    def stop(self) -> None:
        """停止自动恢复"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        logger.info("自动恢复已停止")
    
    def _recovery_loop(self) -> None:
        """恢复循环"""
        while self.running:
            try:
                current_time = time.time()
                
                for name, task in self.recovery_tasks.items():
                    if current_time - task['last_check'] >= task['interval']:
                        self._check_and_recover(name, task)
                        task['last_check'] = current_time
                
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                logger.error(f"自动恢复循环异常: {e}")
                time.sleep(5)
    
    def _check_and_recover(self, name: str, task: dict) -> Any:
        """
        检查并恢复任务
        
        Args:
            name: 任务名称
            task: 任务配置
        """
        try:
            # 执行检查
            is_healthy = safe_execute(
                task['check_func'],
                default_return=False,
                context=f"检查任务_{name}"
            )
            
            if not is_healthy:
                task['failure_count'] += 1
                logger.warning(f"任务 {name} 检查失败，尝试恢复 (失败次数: {task['failure_count']})")
                
                # 执行恢复
                recovery_success = safe_execute(
                    task['recovery_func'],
                    default_return=False,
                    context=f"恢复任务_{name}"
                )
                
                if recovery_success:
                    logger.info(f"任务 {name} 恢复成功")
                    task['failure_count'] = 0
                else:
                    logger.error(f"任务 {name} 恢复失败")
            else:
                # 重置失败计数
                if task['failure_count'] > 0:
                    logger.info(f"任务 {name} 已恢复正常")
                    task['failure_count'] = 0
                
        except Exception as e:
            logger.error(f"检查恢复任务 {name} 异常: {e}")


class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60) -> Any:
        """
        初始化熔断器
        
        Args:
            failure_threshold: 失败阈值
            recovery_timeout: 恢复超时时间（秒）
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        通过熔断器调用函数
        
        Args:
            func: 要调用的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
        
        Returns:
            函数执行结果
        """
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
                logger.info(f"熔断器 {func.__name__} 进入半开状态")
            else:
                raise Exception(f"熔断器开启，拒绝调用 {func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            
            if self.state == 'HALF_OPEN':
                self.state = 'CLOSED'
                self.failure_count = 0
                logger.info(f"熔断器 {func.__name__} 恢复正常")
            
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = 'OPEN'
                logger.warning(f"熔断器 {func.__name__} 开启")
            
            raise e
    
    def reset(self) -> None:
        """重置熔断器"""
        self.state = 'CLOSED'
        self.failure_count = 0
        self.last_failure_time = 0


# 全局自动恢复管理器
global_auto_recovery = AutoRecovery()


def setup_error_handling() -> None:
    """设置错误处理"""
    # 注册恢复回调
    def log_error_callback(error: Exception, context: str) -> Any:
        logger.error(f"全局错误处理: {context} - {error}")
    
    global_error_handler.register_recovery_callback(log_error_callback)
    
    # 启动自动恢复
    global_auto_recovery.start()
    
    logger.info("错误处理系统已初始化")
