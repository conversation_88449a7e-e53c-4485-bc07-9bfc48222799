# -*- coding: utf-8 -*-
"""
设备扫描器
负责发现和识别USB设备
"""

import os
import subprocess
import xml.etree.ElementTree as ET
import time
import hashlib
from typing import List, Dict, Optional
from utils.logger import get_logger
from utils.vh_client import VirtualHereClient

logger = get_logger('device_scanner')


class DeviceScanner:
    """设备扫描器"""
    
    def __init__(self) -> None:
        """初始化设备扫描器"""
        self.vh_client_pipe_write = '/tmp/vhclient'
        self.vh_client_pipe_read = '/tmp/vhclient_response'
        self.vh_client = VirtualHereClient()
        self.scan_interval = 3
        self.last_scan_time = 0
        self.cached_devices = []
        self.device_cache = {}  # 设备缓存，用于检测变化
    
    def scan_usb_devices(self) -> List[Dict]:
        """
        扫描USB设备（增强版）

        Returns:
            List[Dict]: 设备信息列表
        """
        try:
            current_time = time.time()

            # 检查是否需要重新扫描
            if current_time - self.last_scan_time < self.scan_interval:
                logger.debug("使用缓存的设备列表")
                return self.cached_devices

            # 首先尝试通过VirtualHere客户端获取设备
            vh_devices = self._scan_virtualhere_devices()
            if vh_devices:
                logger.info(f"通过VirtualHere发现 {len(vh_devices)} 个设备")
                devices = vh_devices
            else:
                # 如果VirtualHere不可用，尝试系统级扫描
                system_devices = self._scan_system_devices()
                logger.info(f"通过系统扫描发现 {len(system_devices)} 个设备")
                devices = system_devices

            # 处理设备信息并生成UUID
            processed_devices = []
            for device in devices:
                processed_device = self._process_device_info(device)
                if processed_device:
                    processed_devices.append(processed_device)

            # 更新缓存
            self.cached_devices = processed_devices
            self.last_scan_time = current_time

            # 检测设备变化
            self._detect_device_changes(processed_devices)

            return processed_devices

        except Exception as e:
            logger.error(f"设备扫描失败: {e}")
            return []
    
    def _scan_virtualhere_devices(self) -> List[Dict]:
        """
        通过VirtualHere客户端扫描设备
        
        Returns:
            List[Dict]: 设备信息列表
        """
        try:
            # 发送GET CLIENT STATE命令
            response = self._send_vh_command("GET CLIENT STATE")
            if not response:
                return []
            
            # 解析XML响应
            devices = self._parse_vh_response(response)
            return devices
            
        except Exception as e:
            logger.error(f"VirtualHere设备扫描失败: {e}")
            return []
    
    def _send_vh_command(self, command: str) -> Optional[str]:
        """
        向VirtualHere客户端发送命令
        基于官方客户端API文档实现

        Args:
            command: 命令字符串

        Returns:
            str: 响应内容，失败返回None
        """
        try:
            # 检查管道是否存在
            if not os.path.exists(self.vh_client_pipe_write):
                logger.warning("VirtualHere客户端写入管道不存在")
                return None

            # 使用os.open和os.write进行管道通信（推荐方式）
            write_pipe = os.open(self.vh_client_pipe_write, os.O_SYNC | os.O_CREAT | os.O_RDWR)

            # 写入命令
            os.write(write_pipe, command.encode())
            os.write(write_pipe, b"\n")

            # 读取响应
            if os.path.exists(self.vh_client_pipe_read):
                read_pipe = os.open(self.vh_client_pipe_read, os.O_RDONLY)

                result = ""
                while True:
                    temp = os.read(read_pipe, 2048)
                    if len(temp) == 0:
                        break
                    result += temp.decode().replace('\x01', '')

                os.close(read_pipe)
                os.close(write_pipe)

                return result.strip()
            else:
                os.close(write_pipe)
                logger.warning("VirtualHere客户端读取管道不存在")
                return None

        except Exception as e:
            logger.error(f"VirtualHere命令发送失败: {e}")
            return None
    
    def _parse_vh_response(self, response: str) -> List[Dict]:
        """
        解析VirtualHere响应
        
        Args:
            response: XML响应内容
        
        Returns:
            List[Dict]: 解析后的设备列表
        """
        devices = []
        
        try:
            # 清理响应内容
            response = response.replace('\x01', '').strip()
            
            if not response or not response.startswith('<'):
                logger.warning("VirtualHere响应格式无效")
                return devices
            
            # 解析XML
            root = ET.fromstring(response)
            
            # 查找设备节点
            for device_node in root.findall('.//device'):
                device_info = self._extract_device_info(device_node)
                if device_info:
                    devices.append(device_info)
            
            # 查找服务器节点下的设备
            for server_node in root.findall('.//server'):
                for device_node in server_node.findall('.//device'):
                    device_info = self._extract_device_info(device_node)
                    if device_info:
                        devices.append(device_info)
            
        except ET.ParseError as e:
            logger.error(f"XML解析失败: {e}")
        except Exception as e:
            logger.error(f"VirtualHere响应解析失败: {e}")
        
        return devices
    
    def _extract_device_info(self, device_node) -> Optional[Dict]:
        """
        从XML节点提取设备信息
        
        Args:
            device_node: XML设备节点
        
        Returns:
            Dict: 设备信息字典，失败返回None
        """
        try:
            device_info = {
                'device_address': device_node.get('address', ''),
                'vendor': device_node.get('vendor', ''),
                'vendor_id': device_node.get('vendorid', ''),
                'product': device_node.get('product', ''),
                'product_id': device_node.get('productid', ''),
                'device_serial': device_node.get('serial', ''),
                'connection_uuid': device_node.get('connectionuuid', ''),
                'status': 'available'
            }
            
            # 验证必要字段
            if not device_info['device_address']:
                return None
            
            return device_info
            
        except Exception as e:
            logger.error(f"提取设备信息失败: {e}")
            return None
    
    def _scan_system_devices(self) -> List[Dict]:
        """
        通过系统命令扫描USB设备
        
        Returns:
            List[Dict]: 设备信息列表
        """
        devices = []
        
        try:
            # 尝试使用lsusb命令
            result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                devices.extend(self._parse_lsusb_output(result.stdout))
            
        except subprocess.TimeoutExpired:
            logger.warning("lsusb命令执行超时")
        except FileNotFoundError:
            logger.warning("lsusb命令不可用")
        except Exception as e:
            logger.error(f"系统设备扫描失败: {e}")
        
        return devices
    
    def _parse_lsusb_output(self, output: str) -> List[Dict]:
        """
        解析lsusb命令输出
        
        Args:
            output: lsusb命令输出
        
        Returns:
            List[Dict]: 设备信息列表
        """
        devices = []
        
        try:
            for line in output.strip().split('\n'):
                if not line.strip():
                    continue
                
                # 解析lsusb输出格式: Bus 001 Device 002: ID 1234:5678 Vendor Product
                parts = line.split(':', 2)
                if len(parts) < 2:
                    continue
                
                bus_device = parts[0].strip()
                id_info = parts[1].strip()
                
                # 提取总线和设备号
                bus_match = bus_device.split()
                if len(bus_match) < 4:
                    continue
                
                bus_num = bus_match[1]
                device_num = bus_match[3]
                
                # 提取供应商和产品ID
                if id_info.startswith('ID '):
                    id_part = id_info[3:].split()[0]
                    if ':' in id_part:
                        vendor_id, product_id = id_part.split(':', 1)
                        
                        device_info = {
                            'device_address': f"{bus_num}.{device_num}",
                            'vendor': '',
                            'vendor_id': vendor_id,
                            'product': '',
                            'product_id': product_id,
                            'device_serial': '',
                            'connection_uuid': '',
                            'status': 'available'
                        }
                        
                        devices.append(device_info)
            
        except Exception as e:
            logger.error(f"解析lsusb输出失败: {e}")
        
        return devices
    
    def get_device_details(self, device_address: str) -> Optional[Dict]:
        """
        获取设备详细信息
        
        Args:
            device_address: 设备地址
        
        Returns:
            Dict: 设备详细信息，失败返回None
        """
        try:
            # 发送DEVICE INFO命令
            command = f"DEVICE INFO,{device_address}"
            response = self._send_vh_command(command)
            
            if response:
                # 解析响应获取详细信息
                return self._parse_device_details(response)
            
        except Exception as e:
            logger.error(f"获取设备详细信息失败: {e}")
        
        return None
    
    def _parse_device_details(self, response: str) -> Optional[Dict]:
        """
        解析设备详细信息响应
        
        Args:
            response: 响应内容
        
        Returns:
            Dict: 设备详细信息
        """
        try:
            # 这里可以根据实际的VirtualHere响应格式进行解析
            # 目前返回基本信息
            return {
                'details': response.strip(),
                'timestamp': __import__('datetime').datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"解析设备详细信息失败: {e}")
            return None

    def _process_device_info(self, device: Dict) -> Optional[Dict]:
        """
        处理设备信息，生成UUID和标准化字段

        Args:
            device: 原始设备信息

        Returns:
            Dict: 处理后的设备信息
        """
        try:
            # 生成device_uuid（基于旧版VHClientSender算法）
            device_serial = device.get('device_serial', '')
            device_address = device.get('device_address', '')
            vendor_id = device.get('vendor_id', '')
            product_id = device.get('product_id', '')

            if not device_serial or len(device_serial) == 0:
                # 没有序列号，使用vendor_id、product_id和address
                device_uuid = self._str_to_md5(f'{vendor_id}_{product_id}_{device_address}')
            else:
                # 有序列号，使用序列号和address
                device_uuid = self._str_to_md5(f'{device_serial}_{device_address}')

            # 标准化设备信息
            processed_device = {
                'device_uuid': device_uuid,
                'device_address': device_address,
                'vendor': device.get('vendor', ''),
                'vendor_id': vendor_id,
                'product': device.get('product', ''),
                'product_id': product_id,
                'device_serial': device_serial,
                'connection_uuid': device.get('connection_uuid', ''),
                'status': device.get('status', 'available'),
                'scan_time': time.time(),
                'server_name': 'localhost',  # 本地服务器
                'hostname': os.uname().nodename if hasattr(os, 'uname') else 'unknown'
            }

            # 验证必需字段
            if not device_address:
                logger.warning(f"设备缺少地址信息: {device}")
                return None

            return processed_device

        except Exception as e:
            logger.error(f"处理设备信息失败: {e}")
            return None

    def _str_to_md5(self, text: str) -> str:
        """
        字符串转MD5（与旧版VHClientSender保持一致）

        Args:
            text: 输入字符串

        Returns:
            str: MD5哈希值
        """
        return hashlib.md5(text.encode()).hexdigest()

    def _detect_device_changes(self, current_devices: List[Dict]) -> Any:
        """
        检测设备变化

        Args:
            current_devices: 当前设备列表
        """
        try:
            current_uuids = {device['device_uuid'] for device in current_devices}
            cached_uuids = set(self.device_cache.keys())

            # 检测新增设备
            new_devices = current_uuids - cached_uuids
            if new_devices:
                logger.info(f"检测到新增设备: {len(new_devices)} 个")
                for device in current_devices:
                    if device['device_uuid'] in new_devices:
                        logger.info(f"新增设备: {device['vendor']} {device['product']} ({device['device_address']})")

            # 检测移除设备
            removed_devices = cached_uuids - current_uuids
            if removed_devices:
                logger.info(f"检测到移除设备: {len(removed_devices)} 个")
                for uuid in removed_devices:
                    if uuid in self.device_cache:
                        device = self.device_cache[uuid]
                        logger.info(f"移除设备: {device.get('vendor', '')} {device.get('product', '')} ({device.get('device_address', '')})")

            # 更新设备缓存
            self.device_cache = {device['device_uuid']: device for device in current_devices}

        except Exception as e:
            logger.error(f"检测设备变化失败: {e}")

    def get_device_count(self) -> int:
        """
        获取设备数量

        Returns:
            int: 设备数量
        """
        return len(self.cached_devices)

    def clear_cache(self) -> None:
        """清除设备缓存"""
        self.cached_devices = []
        self.device_cache = {}
        self.last_scan_time = 0
        logger.debug("设备缓存已清除")

    def get_device_by_uuid(self, device_uuid: str) -> Optional[Dict]:
        """
        根据UUID获取设备信息

        Args:
            device_uuid: 设备UUID

        Returns:
            Dict: 设备信息，未找到返回None
        """
        for device in self.cached_devices:
            if device.get('device_uuid') == device_uuid:
                return device
        return None

    def get_device_by_address(self, device_address: str) -> Optional[Dict]:
        """
        根据地址获取设备信息

        Args:
            device_address: 设备地址

        Returns:
            Dict: 设备信息，未找到返回None
        """
        for device in self.cached_devices:
            if device.get('device_address') == device_address:
                return device
        return None

    def set_scan_interval(self, interval: int) -> Any:
        """
        设置扫描间隔

        Args:
            interval: 扫描间隔（秒）
        """
        if interval < 1:
            logger.warning(f"扫描间隔过短 ({interval}秒)，已调整为1秒")
            interval = 1

        self.scan_interval = interval
        logger.info(f"扫描间隔已更新为 {interval} 秒")
