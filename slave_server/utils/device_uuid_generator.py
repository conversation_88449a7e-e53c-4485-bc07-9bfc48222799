# -*- coding: utf-8 -*-
"""
设备UUID生成器
根据设备信息生成唯一标识符
"""

import hashlib
from typing import Dict, Optional
from utils.logger import get_logger

logger = get_logger('device_uuid_generator')


class DeviceUUIDGenerator:
    """设备UUID生成器"""
    
    @staticmethod
    def generate_device_uuid(device_info: Dict) -> str:
        """
        根据设备信息生成唯一标识
        
        Args:
            device_info: 设备信息字典
        
        Returns:
            str: 设备UUID
        """
        try:
            device_serial = device_info.get('device_serial', '').strip()
            device_address = device_info.get('device_address', '').strip()
            vendor_id = device_info.get('vendor_id', '').strip()
            product_id = device_info.get('product_id', '').strip()
            
            # 策略1：如果有序列号，使用序列号+地址
            if device_serial:
                uuid_source = f"{device_serial}_{device_address}"
                logger.debug(f"使用序列号生成UUID: {uuid_source}")
            else:
                # 策略2：没有序列号，使用供应商ID+产品ID+地址
                uuid_source = f"{vendor_id}_{product_id}_{device_address}"
                logger.debug(f"使用供应商产品ID生成UUID: {uuid_source}")
            
            # 生成MD5哈希
            device_uuid = DeviceUUIDGenerator._str_to_md5(uuid_source)
            
            logger.info(f"设备UUID生成成功: {device_uuid} (来源: {uuid_source})")
            return device_uuid
            
        except Exception as e:
            logger.error(f"生成设备UUID失败: {e}")
            # 返回基于地址的备用UUID
            return DeviceUUIDGenerator._str_to_md5(device_info.get('device_address', 'unknown'))
    
    @staticmethod
    def _str_to_md5(text: str) -> str:
        """
        将字符串转换为MD5哈希
        
        Args:
            text: 输入字符串
        
        Returns:
            str: MD5哈希值
        """
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    @staticmethod
    def validate_device_uuid(device_uuid: str) -> bool:
        """
        验证设备UUID格式
        
        Args:
            device_uuid: 设备UUID
        
        Returns:
            bool: 是否有效
        """
        if not device_uuid:
            return False
        
        # MD5哈希应该是32位十六进制字符串
        if len(device_uuid) != 32:
            return False
        
        try:
            int(device_uuid, 16)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def generate_connection_uuid(device_info: Dict) -> str:
        """
        生成连接UUID
        
        Args:
            device_info: 设备信息字典
        
        Returns:
            str: 连接UUID
        """
        try:
            # 使用设备地址和当前时间戳生成连接UUID
            import time
            timestamp = str(int(time.time()))
            device_address = device_info.get('device_address', '')
            
            connection_source = f"{device_address}_{timestamp}"
            connection_uuid = DeviceUUIDGenerator._str_to_md5(connection_source)
            
            logger.debug(f"连接UUID生成: {connection_uuid}")
            return connection_uuid
            
        except Exception as e:
            logger.error(f"生成连接UUID失败: {e}")
            return DeviceUUIDGenerator._str_to_md5(f"conn_{device_info.get('device_address', 'unknown')}")
    
    @staticmethod
    def is_same_device(device1: Dict, device2: Dict) -> bool:
        """
        判断两个设备信息是否为同一设备
        
        Args:
            device1: 设备1信息
            device2: 设备2信息
        
        Returns:
            bool: 是否为同一设备
        """
        try:
            uuid1 = DeviceUUIDGenerator.generate_device_uuid(device1)
            uuid2 = DeviceUUIDGenerator.generate_device_uuid(device2)
            
            return uuid1 == uuid2
            
        except Exception as e:
            logger.error(f"设备比较失败: {e}")
            return False
    
    @staticmethod
    def extract_device_identifier(device_info: Dict) -> Dict[str, str]:
        """
        提取设备标识信息
        
        Args:
            device_info: 设备信息字典
        
        Returns:
            Dict: 设备标识信息
        """
        return {
            'device_uuid': DeviceUUIDGenerator.generate_device_uuid(device_info),
            'connection_uuid': DeviceUUIDGenerator.generate_connection_uuid(device_info),
            'device_address': device_info.get('device_address', ''),
            'device_serial': device_info.get('device_serial', ''),
            'vendor_id': device_info.get('vendor_id', ''),
            'product_id': device_info.get('product_id', '')
        }
