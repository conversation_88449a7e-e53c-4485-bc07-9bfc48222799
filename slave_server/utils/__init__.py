# -*- coding: utf-8 -*-
"""
工具模块初始化文件
"""

try:
    __version__ = "1.0.0"
    __author__ = "OmniLink Team"

    # 导入核心工具模块
    from .logger import get_logger
    from .config_manager import Config<PERSON>anager
    from .security_manager import get_security_manager
    from .system_monitor import get_system_monitor
    from .performance_monitor import get_performance_monitor
    from .cache_manager import get_cache_manager
    from .backup_manager import get_backup_manager

    __all__ = [
        'get_logger',
        'ConfigManager',
        'get_security_manager',
        'get_system_monitor',
        'get_performance_monitor',
        'get_cache_manager',
        'get_backup_manager'
    ]

except ImportError as e:
    # 处理导入错误
    import logging
    logging.warning(f"工具模块导入警告: {e}")
    __all__ = []
