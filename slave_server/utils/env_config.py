# -*- coding: utf-8 -*-
"""
环境变量配置模块
支持从环境变量读取配置，便于容器化部署
"""

import os
from typing import Any, Dict, Optional, Union, List
from utils.logger import get_logger

logger = get_logger('env_config')


class EnvConfig:
    """环境变量配置管理器"""
    
    def __init__(self) -> None:
        """初始化环境变量配置管理器"""
        self.env_prefix = 'OMNILINK_'
        self.config_mapping = {
            # 服务器配置
            'server.host': ('SLAVE_SERVER_HOST', str, '0.0.0.0'),
            'server.port': ('SLAVE_SERVER_PORT', int, 8889),
            'server.debug': ('FLASK_DEBUG', bool, False),
            'server.workers': ('FLASK_WORKERS', int, 4),
            
            # 数据库配置
            'database.db_file': ('DB_FILE', str, 'data/slave_server.db'),
            'database.backup_enabled': ('DB_BACKUP_ENABLED', bool, True),
            'database.backup_interval': ('DB_BACKUP_INTERVAL', int, 3600),
            
            # VirtualHere配置
            'virtualhere.server_port': ('VH_SERVER_PORT', int, 7575),
            'virtualhere.binary_path': ('VH_BINARY_PATH', str, '/app/vhusbd'),
            'virtualhere.auto_start': ('VH_AUTO_START', bool, True),
            'virtualhere.max_devices': ('VH_MAX_DEVICES', int, 50),
            
            # 主从通信配置
            'master.server_url': ('MASTER_SERVER_URL', str, ''),
            'master.auth_token': ('MASTER_AUTH_TOKEN', str, ''),
            'master.heartbeat_interval': ('MASTER_HEARTBEAT_INTERVAL', int, 30),
            'master.retry_count': ('MASTER_RETRY_COUNT', int, 3),
            'master.timeout': ('MASTER_TIMEOUT', int, 10),
            
            # 日志配置
            'logging.level': ('LOG_LEVEL', str, 'INFO'),
            'logging.file': ('LOG_FILE', str, 'logs/slave_server.log'),
            'logging.max_size': ('LOG_MAX_SIZE', int, 10485760),  # 10MB
            'logging.backup_count': ('LOG_BACKUP_COUNT', int, 5),
            
            # 监控配置
            'monitoring.enabled': ('ENABLE_MONITORING', bool, True),
            'monitoring.performance_enabled': ('ENABLE_PERFORMANCE_MONITOR', bool, True),
            'monitoring.health_check_interval': ('HEALTH_CHECK_INTERVAL', int, 60),
            'monitoring.metrics_port': ('METRICS_PORT', int, 9100),
            
            # 安全配置
            'security.api_key': ('API_KEY', str, ''),
            'security.cors_enabled': ('CORS_ENABLED', bool, True),
            'security.cors_origins': ('CORS_ORIGINS', str, '*'),
            'security.rate_limit': ('RATE_LIMIT', str, '100/hour'),
            
            # 缓存配置
            'cache.redis_url': ('REDIS_URL', str, 'redis://redis:6379/0'),
            'cache.enabled': ('CACHE_ENABLED', bool, True),
            'cache.ttl': ('CACHE_TTL', int, 300),
            
            # 性能配置
            'performance.sample_interval': ('PERF_SAMPLE_INTERVAL', int, 5),
            'performance.history_size': ('PERF_HISTORY_SIZE', int, 720),
            'performance.cpu_threshold': ('PERF_CPU_THRESHOLD', float, 80.0),
            'performance.memory_threshold': ('PERF_MEMORY_THRESHOLD', float, 80.0),
            
            # 开发配置
            'development.debug_mode': ('DEBUG_MODE', bool, False),
            'development.hot_reload': ('HOT_RELOAD', bool, False),
            'development.profiling': ('ENABLE_PROFILING', bool, False),
        }
    
    def get_value(self, config_key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            config_key: 配置键（如 'server.host'）
            default: 默认值
        
        Returns:
            配置值
        """
        try:
            if config_key not in self.config_mapping:
                logger.warning(f"未知的配置键: {config_key}")
                return default
            
            env_var, value_type, default_value = self.config_mapping[config_key]
            
            # 从环境变量获取值
            env_value = os.getenv(env_var)
            
            if env_value is None:
                # 尝试带前缀的环境变量
                prefixed_var = f"{self.env_prefix}{env_var}"
                env_value = os.getenv(prefixed_var)
            
            if env_value is None:
                return default if default is not None else default_value
            
            # 类型转换
            return self._convert_value(env_value, value_type)
            
        except Exception as e:
            logger.error(f"获取配置值失败 {config_key}: {e}")
            return default
    
    def _convert_value(self, value: str, value_type: type) -> Any:
        """
        转换环境变量值到指定类型
        
        Args:
            value: 环境变量值（字符串）
            value_type: 目标类型
        
        Returns:
            转换后的值
        """
        try:
            if value_type == bool:
                return value.lower() in ('true', '1', 'yes', 'on', 'enabled')
            elif value_type == int:
                return int(value)
            elif value_type == float:
                return float(value)
            elif value_type == list:
                return [item.strip() for item in value.split(',') if item.strip()]
            else:
                return str(value)
                
        except (ValueError, TypeError) as e:
            logger.error(f"类型转换失败 {value} -> {value_type}: {e}")
            raise
    
    def get_all_config(self) -> Dict[str, Any]:
        """
        获取所有配置
        
        Returns:
            Dict: 所有配置的字典
        """
        config = {}
        
        for config_key in self.config_mapping:
            try:
                config[config_key] = self.get_value(config_key)
            except Exception as e:
                logger.error(f"获取配置 {config_key} 失败: {e}")
                config[config_key] = None
        
        return config
    
    def get_section_config(self, section: str) -> Dict[str, Any]:
        """
        获取指定节的配置
        
        Args:
            section: 配置节名称（如 'server'）
        
        Returns:
            Dict: 该节的配置
        """
        section_config = {}
        section_prefix = f"{section}."
        
        for config_key in self.config_mapping:
            if config_key.startswith(section_prefix):
                key_name = config_key[len(section_prefix):]
                section_config[key_name] = self.get_value(config_key)
        
        return section_config
    
    def set_env_prefix(self, prefix: str) -> Any:
        """
        设置环境变量前缀
        
        Args:
            prefix: 前缀字符串
        """
        self.env_prefix = prefix.rstrip('_') + '_'
        logger.info(f"环境变量前缀已设置为: {self.env_prefix}")
    
    def validate_config(self) -> Dict[str, List[str]]:
        """
        验证配置
        
        Returns:
            Dict: 验证结果，包含错误和警告
        """
        errors = []
        warnings = []
        
        try:
            # 验证必需配置
            required_configs = [
                'server.host',
                'server.port',
                'database.db_file',
                'virtualhere.binary_path'
            ]
            
            for config_key in required_configs:
                value = self.get_value(config_key)
                if not value:
                    errors.append(f"必需配置缺失: {config_key}")
            
            # 验证端口范围
            port_configs = ['server.port', 'virtualhere.server_port', 'monitoring.metrics_port']
            for config_key in port_configs:
                port = self.get_value(config_key)
                if port and (port < 1 or port > 65535):
                    errors.append(f"端口范围无效 {config_key}: {port}")
            
            # 验证文件路径
            file_configs = ['database.db_file', 'logging.file']
            for config_key in file_configs:
                file_path = self.get_value(config_key)
                if file_path:
                    dir_path = os.path.dirname(file_path)
                    if dir_path and not os.path.exists(dir_path):
                        warnings.append(f"目录不存在 {config_key}: {dir_path}")
            
            # 验证VirtualHere二进制文件
            vh_binary = self.get_value('virtualhere.binary_path')
            if vh_binary and not os.path.exists(vh_binary):
                warnings.append(f"VirtualHere二进制文件不存在: {vh_binary}")
            
            # 验证主服务器URL
            master_url = self.get_value('master.server_url')
            if master_url:
                if not master_url.startswith(('http://', 'https://')):
                    warnings.append(f"主服务器URL格式可能不正确: {master_url}")
            
        except Exception as e:
            errors.append(f"配置验证异常: {str(e)}")
        
        return {
            'errors': errors,
            'warnings': warnings,
            'valid': len(errors) == 0
        }
    
    def export_env_template(self, file_path: str = '.env.template') -> Any:
        """
        导出环境变量模板文件
        
        Args:
            file_path: 模板文件路径
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("# OmniLink从服务器环境变量配置模板\n")
                f.write("# 复制此文件为 .env 并修改相应的值\n\n")
                
                current_section = ""
                for config_key, (env_var, value_type, default_value) in self.config_mapping.items():
                    section = config_key.split('.')[0]
                    
                    if section != current_section:
                        f.write(f"\n# {section.upper()} 配置\n")
                        current_section = section
                    
                    # 写入注释和默认值
                    f.write(f"# {config_key} (类型: {value_type.__name__})\n")
                    f.write(f"{env_var}={default_value}\n\n")
            
            logger.info(f"环境变量模板已导出到: {file_path}")
            
        except Exception as e:
            logger.error(f"导出环境变量模板失败: {e}")
            raise
    
    def load_from_file(self, file_path: str = '.env') -> Any:
        """
        从文件加载环境变量
        
        Args:
            file_path: 环境变量文件路径
        """
        try:
            if not os.path.exists(file_path):
                logger.warning(f"环境变量文件不存在: {file_path}")
                return
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过注释和空行
                    if not line or line.startswith('#'):
                        continue
                    
                    # 解析键值对
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # 设置环境变量
                        os.environ[key] = value
                    else:
                        logger.warning(f"环境变量文件格式错误 {file_path}:{line_num}: {line}")
            
            logger.info(f"环境变量已从文件加载: {file_path}")
            
        except Exception as e:
            logger.error(f"加载环境变量文件失败: {e}")
            raise


# 全局环境配置实例
global_env_config = EnvConfig()
