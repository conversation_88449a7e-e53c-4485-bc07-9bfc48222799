# -*- coding: utf-8 -*-
"""
认证装饰器
"""

from functools import wraps
from flask import request, jsonify
from typing import Any, Callable

def require_auth(f: Callable) -> Callable:
    """
    API认证装饰器
    
    Args:
        f: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(f)
    def decorated_function(*args: Any, **kwargs: Any) -> Any:
        # 检查Authorization头
        auth_header = request.headers.get('Authorization')
        
        if not auth_header:
            return jsonify({
                'success': False,
                'message': '缺少认证信息'
            }), 401
        
        # 简单的Bearer Token验证
        if not auth_header.startswith('Bearer '):
            return jsonify({
                'success': False,
                'message': '认证格式错误'
            }), 401
        
        token = auth_header.split(' ')[1]
        
        # 这里应该验证token的有效性
        # 暂时允许所有非空token通过
        if not token:
            return jsonify({
                'success': False,
                'message': '无效的认证令牌'
            }), 401
        
        return f(*args, **kwargs)
    
    return decorated_function
