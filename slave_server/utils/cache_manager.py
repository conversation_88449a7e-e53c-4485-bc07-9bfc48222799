# -*- coding: utf-8 -*-
"""
缓存管理模块
提供内存缓存、Redis缓存、缓存策略管理功能
"""

import time
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
from functools import wraps
from utils.logger import get_logger

logger = get_logger('cache_manager')


class MemoryCache:
    """内存缓存"""
    
    def __init__(self, default_ttl: int = 3600, max_size: int = 10000) -> None:
        """
        初始化内存缓存
        
        Args:
            default_ttl: 默认过期时间（秒）
            max_size: 最大缓存条目数
        """
        self.default_ttl = default_ttl
        self.max_size = max_size
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
        
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值或None
        """
        with self.lock:
            if key not in self.cache:
                return None
            
            entry = self.cache[key]
            
            # 检查是否过期
            if entry['expires_at'] < time.time():
                self._remove_key(key)
                return None
            
            # 更新访问时间
            self.access_times[key] = time.time()
            return entry['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒）
        """
        with self.lock:
            if ttl is None:
                ttl = self.default_ttl
            
            # 检查缓存大小限制
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            expires_at = time.time() + ttl
            self.cache[key] = {
                'value': value,
                'expires_at': expires_at,
                'created_at': time.time()
            }
            self.access_times[key] = time.time()
    
    def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        with self.lock:
            if key in self.cache:
                self._remove_key(key)
                return True
            return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def exists(self, key: str) -> bool:
        """
        检查缓存键是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        return self.get(key) is not None
    
    def _remove_key(self, key: str) -> None:
        """移除缓存键"""
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
    
    def _evict_lru(self) -> None:
        """移除最近最少使用的缓存项"""
        if not self.access_times:
            return
        
        # 找到最久未访问的键
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        self._remove_key(lru_key)
    
    def cleanup_expired(self) -> int:
        """
        清理过期缓存
        
        Returns:
            清理的缓存项数量
        """
        with self.lock:
            current_time = time.time()
            expired_keys = []
            
            for key, entry in self.cache.items():
                if entry['expires_at'] < current_time:
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_key(key)
            
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息
        """
        with self.lock:
            current_time = time.time()
            expired_count = sum(
                1 for entry in self.cache.values()
                if entry['expires_at'] < current_time
            )
            
            return {
                'total_keys': len(self.cache),
                'expired_keys': expired_count,
                'valid_keys': len(self.cache) - expired_count,
                'max_size': self.max_size,
                'usage_ratio': len(self.cache) / self.max_size,
                'default_ttl': self.default_ttl
            }


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, enable_redis: bool = False, redis_config: Optional[Dict[str, Any]] = None) -> None:
        """
        初始化缓存管理器
        
        Args:
            enable_redis: 是否启用Redis缓存
            redis_config: Redis配置
        """
        self.memory_cache = MemoryCache()
        self.enable_redis = enable_redis
        self.redis_client = None
        
        if enable_redis:
            try:
                import redis
                config = redis_config or {}
                self.redis_client = redis.Redis(
                    host=config.get('host', 'localhost'),
                    port=config.get('port', 6379),
                    db=config.get('db', 0),
                    password=config.get('password'),
                    decode_responses=True
                )
                # 测试连接
                self.redis_client.ping()
                logger.info("Redis缓存已启用")
            except Exception as e:
                logger.warning(f"Redis连接失败，使用内存缓存: {e}")
                self.enable_redis = False
                self.redis_client = None
    
    def get(self, key: str, use_redis: bool = True) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            use_redis: 是否使用Redis
            
        Returns:
            缓存值或None
        """
        # 先尝试内存缓存
        value = self.memory_cache.get(key)
        if value is not None:
            return value
        
        # 再尝试Redis缓存
        if use_redis and self.enable_redis and self.redis_client:
            try:
                redis_value = self.redis_client.get(key)
                if redis_value is not None:
                    # 尝试JSON反序列化
                    try:
                        value = json.loads(redis_value)
                    except json.JSONDecodeError:
                        value = redis_value
                    
                    # 回写到内存缓存
                    self.memory_cache.set(key, value)
                    return value
            except Exception as e:
                logger.warning(f"Redis获取失败: {e}")
        
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, 
           use_redis: bool = True) -> None:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒）
            use_redis: 是否使用Redis
        """
        # 设置内存缓存
        self.memory_cache.set(key, value, ttl)
        
        # 设置Redis缓存
        if use_redis and self.enable_redis and self.redis_client:
            try:
                # JSON序列化
                if isinstance(value, (dict, list)):
                    redis_value = json.dumps(value, ensure_ascii=False)
                else:
                    redis_value = str(value)
                
                if ttl:
                    self.redis_client.setex(key, ttl, redis_value)
                else:
                    self.redis_client.set(key, redis_value)
            except Exception as e:
                logger.warning(f"Redis设置失败: {e}")
    
    def delete(self, key: str, use_redis: bool = True) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            use_redis: 是否使用Redis
            
        Returns:
            是否删除成功
        """
        # 删除内存缓存
        memory_deleted = self.memory_cache.delete(key)
        
        # 删除Redis缓存
        redis_deleted = False
        if use_redis and self.enable_redis and self.redis_client:
            try:
                redis_deleted = bool(self.redis_client.delete(key))
            except Exception as e:
                logger.warning(f"Redis删除失败: {e}")
        
        return memory_deleted or redis_deleted
    
    def clear(self, use_redis: bool = True) -> None:
        """
        清空所有缓存
        
        Args:
            use_redis: 是否清空Redis
        """
        # 清空内存缓存
        self.memory_cache.clear()
        
        # 清空Redis缓存
        if use_redis and self.enable_redis and self.redis_client:
            try:
                self.redis_client.flushdb()
            except Exception as e:
                logger.warning(f"Redis清空失败: {e}")
    
    def exists(self, key: str, use_redis: bool = True) -> bool:
        """
        检查缓存键是否存在
        
        Args:
            key: 缓存键
            use_redis: 是否检查Redis
            
        Returns:
            是否存在
        """
        # 检查内存缓存
        if self.memory_cache.exists(key):
            return True
        
        # 检查Redis缓存
        if use_redis and self.enable_redis and self.redis_client:
            try:
                return bool(self.redis_client.exists(key))
            except Exception as e:
                logger.warning(f"Redis检查失败: {e}")
        
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息
        """
        stats = {
            'memory_cache': self.memory_cache.get_stats(),
            'redis_enabled': self.enable_redis,
            'redis_connected': self.redis_client is not None
        }
        
        if self.enable_redis and self.redis_client:
            try:
                redis_info = self.redis_client.info()
                stats['redis_stats'] = {
                    'used_memory': redis_info.get('used_memory', 0),
                    'used_memory_human': redis_info.get('used_memory_human', '0B'),
                    'connected_clients': redis_info.get('connected_clients', 0),
                    'total_commands_processed': redis_info.get('total_commands_processed', 0)
                }
            except Exception as e:
                logger.warning(f"获取Redis统计失败: {e}")
                stats['redis_stats'] = {'error': str(e)}
        
        return stats


# 全局缓存管理器实例
cache_manager = CacheManager()


def get_cache_manager() -> CacheManager:
    """获取缓存管理器实例"""
    return cache_manager


def cache_result(key_prefix: str = "", ttl: int = 3600, use_redis: bool = True):
    """
    缓存函数结果装饰器
    
    Args:
        key_prefix: 缓存键前缀
        ttl: 过期时间（秒）
        use_redis: 是否使用Redis
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            func_name = func.__name__
            args_str = str(args) + str(sorted(kwargs.items()))
            cache_key = f"{key_prefix}:{func_name}:{hash(args_str)}"
            
            # 尝试从缓存获取
            cached_result = cache_manager.get(cache_key, use_redis=use_redis)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl=ttl, use_redis=use_redis)
            
            return result
        
        return wrapper
    return decorator


def invalidate_cache(pattern: str = "*") -> int:
    """
    清除匹配模式的缓存
    
    Args:
        pattern: 缓存键模式
        
    Returns:
        清除的缓存项数量
    """
    # 这里简化实现，实际应该支持模式匹配
    cache_manager.clear()
    return 1
