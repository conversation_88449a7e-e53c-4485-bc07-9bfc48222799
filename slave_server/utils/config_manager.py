# -*- coding: utf-8 -*-
"""
配置管理模块
提供配置文件的加载、验证和管理功能
"""

import os
import configparser
import json
from typing import Dict, Any, Optional, List, Union, Tuple
from utils.logger import get_logger
from utils.env_config import global_env_config

logger = get_logger('config_manager')


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径，如果为None则使用默认路径
        """
        if config_file is None:
            config_file = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                'config',
                'slave_server.ini'
            )
        
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.env_config = global_env_config
        self._load_config()
        self._load_env_overrides()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            # 确保配置目录存在
            config_dir = os.path.dirname(self.config_file)
            os.makedirs(config_dir, exist_ok=True)
            
            # 如果配置文件不存在，创建默认配置
            if not os.path.exists(self.config_file):
                self._create_default_config()
            
            # 读取配置文件
            self.config.read(self.config_file, encoding='utf-8')
            logger.info(f"配置文件加载成功: {self.config_file}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self) -> None:
        """创建默认配置文件"""
        try:
            # 主服务器配置
            self.config.add_section('master')
            self.config.set('master', 'url', 'http://localhost:8888')
            self.config.set('master', 'register_endpoint', '/api/slave/register')
            self.config.set('master', 'heartbeat_endpoint', '/api/slave/heartbeat')
            self.config.set('master', 'devices_endpoint', '/api/slave/devices')
            self.config.set('master', 'command_endpoint', '/api/slave/command')
            
            # 从服务器配置
            self.config.add_section('server')
            self.config.set('server', 'host', '0.0.0.0')
            self.config.set('server', 'port', '8889')
            self.config.set('server', 'uuid', 'auto')
            self.config.set('server', 'hostname', 'auto')
            
            # VirtualHere配置
            self.config.add_section('virtualhere')
            self.config.set('virtualhere', 'server_port', '7575')
            self.config.set('virtualhere', 'client_pipe_write', '/tmp/vhclient')
            self.config.set('virtualhere', 'client_pipe_read', '/tmp/vhclient_response')
            
            # 日志配置
            self.config.add_section('logging')
            self.config.set('logging', 'level', 'INFO')
            self.config.set('logging', 'file', 'logs/slave_server.log')
            self.config.set('logging', 'max_size', '10485760')  # 10MB
            self.config.set('logging', 'backup_count', '5')
            
            # 任务配置
            self.config.add_section('tasks')
            self.config.set('tasks', 'device_scan_interval', '3')
            self.config.set('tasks', 'heartbeat_interval', '30')
            self.config.set('tasks', 'command_poll_interval', '5')
            
            # 保存配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            
            logger.info(f"默认配置文件已创建: {self.config_file}")
            
        except Exception as e:
            logger.error(f"创建默认配置文件失败: {e}")
            raise
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取完整配置
        
        Returns:
            dict: 配置字典
        """
        config_dict = {}
        for section in self.config.sections():
            config_dict[section] = dict(self.config.items(section))
        return config_dict
    
    def get_section(self, section: str) -> Dict[str, str]:
        """
        获取指定节的配置
        
        Args:
            section: 节名称
        
        Returns:
            dict: 节配置字典
        """
        if self.config.has_section(section):
            return dict(self.config.items(section))
        return {}
    
    def get_master_config(self) -> Dict[str, str]:
        """获取主服务器配置"""
        return self.get_section('master')
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取从服务器配置"""
        config = self.get_section('server')
        
        # 处理特殊值
        if config.get('port'):
            config['port'] = int(config['port'])
        
        # 处理自动值
        if config.get('uuid') == 'auto':
            config['uuid'] = self._generate_server_uuid()
        
        if config.get('hostname') == 'auto':
            import socket
            config['hostname'] = socket.gethostname()
        
        return config
    
    def get_virtualhere_config(self) -> Dict[str, Any]:
        """获取VirtualHere配置"""
        config = self.get_section('virtualhere')

        # 处理端口号
        if config.get('server_port'):
            config['server_port'] = int(config['server_port'])

        return config
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        config = self.get_section('logging')
        
        # 处理数值
        if config.get('max_size'):
            config['max_size'] = int(config['max_size'])
        if config.get('backup_count'):
            config['backup_count'] = int(config['backup_count'])
        
        return config
    
    def get_tasks_config(self) -> Dict[str, Any]:
        """获取任务配置"""
        config = self.get_section('tasks')
        
        # 处理数值
        for key in ['device_scan_interval', 'heartbeat_interval', 'command_poll_interval']:
            if config.get(key):
                config[key] = int(config[key])
        
        return config
    
    def get_value(self, section: str, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            section: 节名称
            key: 键名称
            default: 默认值
        
        Returns:
            配置值
        """
        try:
            return self.config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return default
    
    def set_value(self, section: str, key: str, value: str) -> None:
        """
        设置配置值

        Args:
            section: 节名称
            key: 键名称
            value: 值
        """
        if not self.config.has_section(section):
            self.config.add_section(section)
        
        self.config.set(section, key, str(value))
    
    def save_config(self) -> bool:
        """
        保存配置到文件

        Returns:
            bool: 保存是否成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            logger.info("配置文件保存成功")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def reload_config(self) -> None:
        """重新加载配置文件"""
        self._load_config()
        logger.info("配置文件重新加载完成")
    
    def _generate_server_uuid(self) -> str:
        """
        生成服务器UUID
        
        Returns:
            str: 服务器UUID
        """
        import uuid
        import socket
        
        # 基于主机名和MAC地址生成UUID
        hostname = socket.gethostname()
        mac = uuid.getnode()
        server_uuid = str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{hostname}-{mac}"))
        
        return server_uuid

    def backup_config(self) -> str:
        """
        备份配置文件

        Returns:
            str: 备份文件路径
        """
        try:
            import shutil
            from datetime import datetime

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"{self.config_file}.backup_{timestamp}"

            shutil.copy2(self.config_file, backup_file)
            logger.info(f"配置文件备份成功: {backup_file}")

            return backup_file

        except Exception as e:
            logger.error(f"备份配置文件失败: {e}")
            raise

    def _get_timestamp(self) -> str:
        """
        获取当前时间戳

        Returns:
            str: 时间戳字符串
        """
        from datetime import datetime
        return datetime.now().isoformat()

    def load_config(self) -> None:
        """重新加载配置文件（别名方法）"""
        self._load_config()
        self._load_env_overrides()
        logger.info("配置文件重新加载完成")

    def _load_env_overrides(self) -> None:
        """加载环境变量覆盖配置"""
        try:
            # 从环境变量获取配置并覆盖文件配置
            env_overrides = {
                'server': {
                    'host': self.env_config.get_value('server.host'),
                    'port': str(self.env_config.get_value('server.port')),
                    'debug': str(self.env_config.get_value('server.debug')).lower()
                },
                'database': {
                    'db_file': self.env_config.get_value('database.db_file')
                },
                'virtualhere': {
                    'server_port': str(self.env_config.get_value('virtualhere.server_port')),
                    'binary_path': self.env_config.get_value('virtualhere.binary_path'),
                    'auto_start': str(self.env_config.get_value('virtualhere.auto_start')).lower()
                },
                'logging': {
                    'log_level': self.env_config.get_value('logging.level'),
                    'log_file': self.env_config.get_value('logging.file')
                },
                'master': {
                    'server_url': self.env_config.get_value('master.server_url'),
                    'auth_token': self.env_config.get_value('master.auth_token'),
                    'heartbeat_interval': str(self.env_config.get_value('master.heartbeat_interval')),
                    'timeout': str(self.env_config.get_value('master.timeout'))
                }
            }

            # 应用环境变量覆盖
            for section_name, section_config in env_overrides.items():
                if not self.config.has_section(section_name):
                    self.config.add_section(section_name)

                for key, value in section_config.items():
                    if value is not None and value != '':
                        self.config.set(section_name, key, str(value))

            logger.debug("环境变量配置覆盖已应用")

        except Exception as e:
            logger.error(f"加载环境变量覆盖失败: {e}")

    def get_env_config(self) -> Dict[str, Any]:
        """
        获取环境变量配置

        Returns:
            Dict: 环境变量配置
        """
        return self.env_config.get_all_config()

    def get_env_section_config(self, section: str) -> Dict[str, Any]:
        """
        获取环境变量中指定节的配置

        Args:
            section: 配置节名称

        Returns:
            Dict: 该节的环境变量配置
        """
        return self.env_config.get_section_config(section)

    def validate_env_config(self) -> Dict[str, Any]:
        """
        验证环境变量配置

        Returns:
            Dict: 验证结果
        """
        return self.env_config.validate_config()

    def export_env_template(self, file_path: str = '.env.template') -> None:
        """
        导出环境变量模板

        Args:
            file_path: 模板文件路径
        """
        self.env_config.export_env_template(file_path)

    def load_env_file(self, file_path: str = '.env') -> None:
        """
        从文件加载环境变量

        Args:
            file_path: 环境变量文件路径
        """
        self.env_config.load_from_file(file_path)
        self._load_env_overrides()  # 重新应用环境变量覆盖

    def get_effective_config(self) -> Dict[str, Any]:
        """
        获取有效配置（文件配置 + 环境变量覆盖）

        Returns:
            Dict: 有效配置
        """
        effective_config = {}

        # 获取文件配置
        for section_name in self.config.sections():
            effective_config[section_name] = dict(self.config.items(section_name))

        # 添加环境变量信息
        effective_config['_env_config'] = self.env_config.get_all_config()
        effective_config['_env_validation'] = self.env_config.validate_config()

        return effective_config
