# -*- coding: utf-8 -*-
"""
主从通信模块
负责与主服务器的通信，包括注册、心跳、状态上报等
"""

import requests
import socket
import platform
import time
import json
import hashlib
from typing import Dict, Optional, List, Set, Any, Union, Tuple
from datetime import datetime, timedelta
from utils.logger import get_logger
from db import ConfigDAO

logger = get_logger('master_communication')


class MasterCommunication:
    """主从通信管理器"""
    
    def __init__(self) -> None:
        """初始化主从通信管理器"""
        self.master_server_url = None
        self.auth_token = None
        self.server_uuid = None
        self.session = requests.Session()
        self.session.timeout = 10

        # 重试和错误恢复配置
        self.max_retries = 3
        self.retry_delay = 5  # 秒
        self.connection_timeout = 10
        self.read_timeout = 30
        self.backoff_factor = 2  # 指数退避因子
        self.max_retry_delay = 60  # 最大重试延迟

        # 错误恢复状态
        self.consecutive_failures = 0
        self.last_failure_time = 0
        self.circuit_breaker_threshold = 5  # 连续失败阈值
        self.circuit_breaker_timeout = 300  # 熔断器超时时间（秒）
        self.is_circuit_open = False

        # 增量上报相关
        self.last_device_states = {}  # 上次上报的设备状态
        self.device_state_hashes = {}  # 设备状态哈希值
        self.pending_reports = []  # 待上报的数据队列
        self.batch_size = 50  # 批量处理大小
        self.batch_timeout = 30  # 批量超时时间（秒）
        self.last_batch_time = time.time()

        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置"""
        try:
            self.master_server_url = ConfigDAO.get_master_server_url()
            self.auth_token = ConfigDAO.get_auth_token()
            self.server_uuid = ConfigDAO.get_server_uuid()
            
            # 如果没有server_uuid，生成一个
            if not self.server_uuid:
                self.server_uuid = self._generate_server_uuid()
                ConfigDAO.set_config_value('server_uuid', self.server_uuid)
                
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
    
    def _generate_server_uuid(self) -> str:
        """
        生成服务器UUID
        统一使用硬件指纹生成稳定的UUID

        Returns:
            str: 服务器UUID
        """
        import uuid
        import hashlib

        try:
            # 优先使用硬件指纹生成稳定UUID
            from core.hardware_fingerprint import HardwareFingerprint
            fingerprint = HardwareFingerprint()
            return fingerprint.generate_uuid()
        except Exception as e:
            logger.warning(f"硬件指纹生成失败，使用降级方案: {e}")

            # 降级方案：基于主机名和MAC地址生成UUID
            hostname = socket.gethostname()
            mac = uuid.getnode()
            uuid_source = f"{hostname}-{mac}-{int(time.time())}"

            return hashlib.md5(uuid_source.encode()).hexdigest()
    
    def register_to_master(self) -> bool:
        """
        向主服务器注册从服务器
        
        Returns:
            bool: 注册是否成功
        """
        try:
            logger.info("正在向主服务器注册...")
            
            # 收集服务器信息
            server_info = self._collect_server_info()
            
            # 发送注册请求（使用重试机制）
            def _do_register() -> None:
                url = f"{self.master_server_url}/api/slave/register"
                return self.session.post(
                    url,
                    json=server_info,
                    timeout=(self.connection_timeout, self.read_timeout)
                )

            response = self._execute_with_retry(_do_register)

            if response and response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    # 保存认证令牌
                    token = result.get('data', {}).get('token')
                    if token:
                        self.auth_token = token
                        ConfigDAO.set_auth_token(token)
                        logger.info("从服务器注册成功")
                        return True
                    else:
                        logger.error("注册响应中缺少认证令牌")
                        return False
                else:
                    logger.error(f"注册失败: {result.get('message')}")
                    return False
            elif response:
                logger.error(f"注册请求失败: HTTP {response.status_code}")
                return False
            else:
                logger.error("注册请求失败: 无响应（可能触发了熔断器）")
                return False
                
        except Exception as e:
            logger.error(f"注册到主服务器失败: {e}")
            return False
    
    def _collect_server_info(self) -> Dict:
        """
        收集服务器信息
        
        Returns:
            Dict: 服务器信息字典
        """
        try:
            return {
                "server_uuid": self.server_uuid,
                "hostname": socket.gethostname(),
                "ip": self._get_local_ip(),
                "port": 8889,  # 从服务器端口
                "system_info": {
                    "os": platform.system(),
                    "platform": platform.platform(),
                    "python_version": platform.python_version(),
                    "architecture": platform.architecture()[0],
                    "version": "1.0.0"
                }
            }
        except Exception as e:
            logger.error(f"收集服务器信息失败: {e}")
            return {}
    
    def _get_local_ip(self) -> str:
        """
        获取本地IP地址
        
        Returns:
            str: IP地址
        """
        try:
            # 连接到一个远程地址来获取本地IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                return s.getsockname()[0]
        except Exception:
            try:
                return socket.gethostbyname(socket.gethostname())
            except Exception:
                return "127.0.0.1"
    
    def send_heartbeat(self) -> bool:
        """
        发送心跳到主服务器
        
        Returns:
            bool: 心跳是否成功
        """
        try:
            if not self.auth_token:
                logger.warning("未找到认证令牌，无法发送心跳")
                return False
            
            # 准备心跳数据
            heartbeat_data = {
                "server_uuid": self.server_uuid,
                "timestamp": int(time.time()),
                "status": "running"
            }
            
            # 发送心跳请求
            url = f"{self.master_server_url}/api/slave/heartbeat"
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            response = self.session.post(url, json=heartbeat_data, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    logger.debug("心跳发送成功")
                    return True
                else:
                    logger.warning(f"心跳响应异常: {result.get('message')}")
                    return False
            else:
                logger.error(f"心跳发送失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"发送心跳失败: {e}")
            return False
    
    def report_devices(self, devices: List[Dict]) -> bool:
        """
        向主服务器上报设备状态
        
        Args:
            devices: 设备列表
        
        Returns:
            bool: 上报是否成功
        """
        try:
            if not self.auth_token:
                logger.warning("未找到认证令牌，无法上报设备状态")
                return False
            
            # 准备设备数据
            device_data = {
                "server_uuid": self.server_uuid,
                "devices": devices
            }
            
            # 发送设备状态请求
            url = f"{self.master_server_url}/api/slave/devices"
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            response = self.session.post(url, json=device_data, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    logger.debug(f"设备状态上报成功，设备数量: {len(devices)}")
                    return True
                else:
                    logger.warning(f"设备状态上报响应异常: {result.get('message')}")
                    return False
            else:
                logger.error(f"设备状态上报失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"上报设备状态失败: {e}")
            return False
    
    def fetch_commands(self) -> Optional[Dict]:
        """
        从主服务器获取命令
        
        Returns:
            Dict: 命令信息，无命令返回None
        """
        try:
            if not self.auth_token:
                logger.warning("未找到认证令牌，无法获取命令")
                return None
            
            # 获取命令
            url = f"{self.master_server_url}/api/slave/command"
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            response = self.session.get(url, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') and 'data' in result:
                    return result['data']
                else:
                    return None
            elif response.status_code == 204:
                # 没有待执行的命令
                return None
            else:
                logger.warning(f"获取命令失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取命令失败: {e}")
            return None
    
    def report_command_result(self, command_id: str, result: Dict) -> bool:
        """
        上报命令执行结果
        
        Args:
            command_id: 命令ID
            result: 执行结果
        
        Returns:
            bool: 上报是否成功
        """
        try:
            if not self.auth_token:
                logger.warning("未找到认证令牌，无法上报命令结果")
                return False
            
            # 上报命令结果
            url = f"{self.master_server_url}/api/slave/command/{command_id}/result"
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            response = self.session.post(url, json=result, headers=headers)
            
            if response.status_code == 200:
                response_result = response.json()
                if response_result.get('status'):
                    logger.debug(f"命令结果上报成功: {command_id}")
                    return True
                else:
                    logger.warning(f"命令结果上报响应异常: {response_result.get('message')}")
                    return False
            else:
                logger.error(f"命令结果上报失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"上报命令结果失败: {e}")
            return False
    
    def is_connected(self) -> bool:
        """
        检查与主服务器的连接状态
        
        Returns:
            bool: 是否连接
        """
        return self.auth_token is not None
    
    def set_master_server_url(self, url: str) -> Any:
        """
        设置主服务器URL
        
        Args:
            url: 主服务器URL
        """
        self.master_server_url = url
        ConfigDAO.set_config_value('master_server_url', url)
        logger.info(f"主服务器URL已更新: {url}")
    
    def disconnect(self) -> None:
        """断开与主服务器的连接"""
        self.auth_token = None
        ConfigDAO.set_auth_token('')
        logger.info("已断开与主服务器的连接")

    def report_device_states_incremental(self, current_devices: List[Dict]) -> bool:
        """
        增量上报设备状态

        Args:
            current_devices: 当前设备列表

        Returns:
            bool: 上报是否成功
        """
        try:
            # 计算设备状态变化
            changes = self._calculate_device_changes(current_devices)

            if not changes['added'] and not changes['removed'] and not changes['modified']:
                logger.debug("设备状态无变化，跳过上报")
                return True

            # 添加到待上报队列
            report_data = {
                'type': 'device_state_change',
                'timestamp': datetime.now().isoformat(),
                'changes': changes
            }

            self.pending_reports.append(report_data)

            # 检查是否需要批量发送
            if self._should_send_batch():
                return self._send_batch_reports()

            return True

        except Exception as e:
            logger.error(f"增量上报设备状态失败: {e}")
            return False

    def _calculate_device_changes(self, current_devices: List[Dict]) -> Dict:
        """
        计算设备状态变化

        Args:
            current_devices: 当前设备列表

        Returns:
            Dict: 变化信息
        """
        changes = {
            'added': [],
            'removed': [],
            'modified': []
        }

        # 构建当前设备状态映射
        current_device_map = {device['device_uuid']: device for device in current_devices}
        current_uuids = set(current_device_map.keys())
        last_uuids = set(self.last_device_states.keys())

        # 检测新增设备
        added_uuids = current_uuids - last_uuids
        for uuid in added_uuids:
            device = current_device_map[uuid]
            changes['added'].append(device)
            logger.debug(f"检测到新增设备: {uuid}")

        # 检测移除设备
        removed_uuids = last_uuids - current_uuids
        for uuid in removed_uuids:
            changes['removed'].append({'device_uuid': uuid})
            logger.debug(f"检测到移除设备: {uuid}")

        # 检测修改设备
        common_uuids = current_uuids & last_uuids
        for uuid in common_uuids:
            current_device = current_device_map[uuid]
            last_device = self.last_device_states[uuid]

            # 计算设备状态哈希
            current_hash = self._calculate_device_hash(current_device)
            last_hash = self.device_state_hashes.get(uuid, '')

            if current_hash != last_hash:
                changes['modified'].append({
                    'device_uuid': uuid,
                    'old_state': last_device,
                    'new_state': current_device
                })
                logger.debug(f"检测到设备状态变化: {uuid}")

        # 更新缓存
        self.last_device_states = current_device_map.copy()
        self.device_state_hashes = {
            uuid: self._calculate_device_hash(device)
            for uuid, device in current_device_map.items()
        }

        return changes

    def _calculate_device_hash(self, device: Dict) -> str:
        """
        计算设备状态哈希值

        Args:
            device: 设备信息

        Returns:
            str: 哈希值
        """
        # 选择关键字段计算哈希
        key_fields = ['status', 'shown', 'disable', 'nick_name']
        hash_data = {field: device.get(field) for field in key_fields}
        hash_string = json.dumps(hash_data, sort_keys=True)
        return hashlib.md5(hash_string.encode()).hexdigest()

    def _should_send_batch(self) -> bool:
        """
        检查是否应该发送批量数据

        Returns:
            bool: 是否应该发送
        """
        current_time = time.time()

        # 检查队列大小
        if len(self.pending_reports) >= self.batch_size:
            return True

        # 检查超时
        if current_time - self.last_batch_time >= self.batch_timeout:
            return len(self.pending_reports) > 0

        return False

    def _send_batch_reports(self) -> bool:
        """
        发送批量上报数据

        Returns:
            bool: 发送是否成功
        """
        try:
            if not self.pending_reports:
                return True

            logger.info(f"发送批量上报数据，共 {len(self.pending_reports)} 条")

            # 准备批量数据
            batch_data = {
                'server_uuid': self.server_uuid,
                'batch_timestamp': datetime.now().isoformat(),
                'reports': self.pending_reports.copy()
            }

            # 发送到主服务器（使用重试机制）
            def _do_batch_report() -> None:
                url = f"{self.master_server_url}/api/slave/batch_report"
                headers = {'Authorization': f'Bearer {self.auth_token}'}
                return self.session.post(
                    url,
                    json=batch_data,
                    headers=headers,
                    timeout=(self.connection_timeout, self.read_timeout)
                )

            response = self._execute_with_retry(_do_batch_report)

            if response and response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    # 清空待上报队列
                    self.pending_reports.clear()
                    self.last_batch_time = time.time()
                    logger.info("批量上报成功")
                    return True
                else:
                    logger.error(f"批量上报失败: {result.get('message')}")
                    return False
            elif response:
                logger.error(f"批量上报请求失败: HTTP {response.status_code}")
                return False
            else:
                logger.error("批量上报请求失败: 无响应（可能触发了熔断器）")
                return False

        except Exception as e:
            logger.error(f"发送批量上报数据失败: {e}")
            return False

    def force_send_pending_reports(self) -> bool:
        """
        强制发送所有待上报数据

        Returns:
            bool: 发送是否成功
        """
        if self.pending_reports:
            return self._send_batch_reports()
        return True

    def get_pending_report_count(self) -> int:
        """
        获取待上报数据数量

        Returns:
            int: 待上报数据数量
        """
        return len(self.pending_reports)

    def clear_pending_reports(self) -> None:
        """清空待上报数据队列"""
        self.pending_reports.clear()
        logger.info("待上报数据队列已清空")

    def set_batch_config(self, batch_size: int = None, batch_timeout: int = None) -> Any:
        """
        设置批量处理配置

        Args:
            batch_size: 批量大小
            batch_timeout: 批量超时时间（秒）
        """
        if batch_size is not None:
            self.batch_size = max(1, batch_size)
            logger.info(f"批量大小已更新为: {self.batch_size}")

        if batch_timeout is not None:
            self.batch_timeout = max(5, batch_timeout)
            logger.info(f"批量超时时间已更新为: {self.batch_timeout}秒")

    def _execute_with_retry(self, func, *args, **kwargs) -> Any:
        """
        带重试机制的请求执行

        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            执行结果
        """
        # 检查熔断器状态
        if self._is_circuit_breaker_open():
            logger.warning("熔断器已开启，跳过请求")
            return None

        last_exception = None

        for attempt in range(self.max_retries + 1):
            try:
                # 执行请求
                result = func(*args, **kwargs)

                # 成功时重置失败计数
                self._reset_failure_count()
                return result

            except requests.exceptions.ConnectionError as e:
                last_exception = e
                logger.warning(f"连接错误 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}")

            except requests.exceptions.Timeout as e:
                last_exception = e
                logger.warning(f"请求超时 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}")

            except requests.exceptions.RequestException as e:
                last_exception = e
                logger.warning(f"请求异常 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}")

            except Exception as e:
                last_exception = e
                logger.error(f"未知异常 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}")

            # 如果不是最后一次尝试，等待后重试
            if attempt < self.max_retries:
                delay = self._calculate_retry_delay(attempt)
                logger.info(f"等待 {delay} 秒后重试...")
                time.sleep(delay)

        # 所有重试都失败，记录失败并可能触发熔断器
        self._record_failure()
        logger.error(f"请求失败，已重试 {self.max_retries} 次: {last_exception}")
        return None

    def _calculate_retry_delay(self, attempt: int) -> float:
        """
        计算重试延迟时间（指数退避）

        Args:
            attempt: 当前尝试次数

        Returns:
            float: 延迟时间（秒）
        """
        delay = self.retry_delay * (self.backoff_factor ** attempt)
        return min(delay, self.max_retry_delay)

    def _is_circuit_breaker_open(self) -> bool:
        """
        检查熔断器是否开启

        Returns:
            bool: 熔断器是否开启
        """
        if not self.is_circuit_open:
            return False

        # 检查熔断器是否应该关闭
        current_time = time.time()
        if current_time - self.last_failure_time > self.circuit_breaker_timeout:
            logger.info("熔断器超时，尝试恢复连接")
            self.is_circuit_open = False
            self.consecutive_failures = 0
            return False

        return True

    def _record_failure(self) -> None:
        """记录失败并检查是否需要开启熔断器"""
        self.consecutive_failures += 1
        self.last_failure_time = time.time()

        if self.consecutive_failures >= self.circuit_breaker_threshold:
            if not self.is_circuit_open:
                logger.warning(f"连续失败 {self.consecutive_failures} 次，开启熔断器")
                self.is_circuit_open = True

    def _reset_failure_count(self) -> None:
        """重置失败计数"""
        if self.consecutive_failures > 0:
            logger.info("请求成功，重置失败计数")
            self.consecutive_failures = 0
            self.is_circuit_open = False

    def set_retry_config(self, max_retries: int = None, retry_delay: float = None,
                        backoff_factor: float = None, max_retry_delay: float = None) -> Any:
        """
        设置重试配置

        Args:
            max_retries: 最大重试次数
            retry_delay: 基础重试延迟
            backoff_factor: 指数退避因子
            max_retry_delay: 最大重试延迟
        """
        if max_retries is not None:
            self.max_retries = max(0, max_retries)
            logger.info(f"最大重试次数已更新为: {self.max_retries}")

        if retry_delay is not None:
            self.retry_delay = max(1, retry_delay)
            logger.info(f"基础重试延迟已更新为: {self.retry_delay}秒")

        if backoff_factor is not None:
            self.backoff_factor = max(1, backoff_factor)
            logger.info(f"指数退避因子已更新为: {self.backoff_factor}")

        if max_retry_delay is not None:
            self.max_retry_delay = max(5, max_retry_delay)
            logger.info(f"最大重试延迟已更新为: {self.max_retry_delay}秒")

    def set_circuit_breaker_config(self, threshold: int = None, timeout: int = None) -> Any:
        """
        设置熔断器配置

        Args:
            threshold: 连续失败阈值
            timeout: 熔断器超时时间（秒）
        """
        if threshold is not None:
            self.circuit_breaker_threshold = max(1, threshold)
            logger.info(f"熔断器阈值已更新为: {self.circuit_breaker_threshold}")

        if timeout is not None:
            self.circuit_breaker_timeout = max(60, timeout)
            logger.info(f"熔断器超时时间已更新为: {self.circuit_breaker_timeout}秒")

    def get_connection_status(self) -> Dict:
        """
        获取连接状态信息

        Returns:
            Dict: 连接状态
        """
        return {
            'connected': self.is_connected(),
            'consecutive_failures': self.consecutive_failures,
            'circuit_breaker_open': self.is_circuit_open,
            'last_failure_time': self.last_failure_time,
            'pending_reports': len(self.pending_reports),
            'retry_config': {
                'max_retries': self.max_retries,
                'retry_delay': self.retry_delay,
                'backoff_factor': self.backoff_factor,
                'max_retry_delay': self.max_retry_delay
            },
            'circuit_breaker_config': {
                'threshold': self.circuit_breaker_threshold,
                'timeout': self.circuit_breaker_timeout
            }
        }

    def reset_circuit_breaker(self) -> None:
        """手动重置熔断器"""
        self.is_circuit_open = False
        self.consecutive_failures = 0
        self.last_failure_time = 0
        logger.info("熔断器已手动重置")

    def send_full_data_sync(self, device_details: List[Dict]) -> bool:
        """
        发送完整数据同步到主服务器

        Args:
            device_details: 设备详细信息列表

        Returns:
            bool: 同步是否成功
        """
        try:
            if not self.master_server_url:
                logger.error("主服务器URL未配置")
                return False

            # 准备同步数据
            sync_data = {
                'sync_type': 'full_data',
                'timestamp': datetime.now().isoformat(),
                'device_details': device_details,
                'device_count_summary': len(device_details),
                'usb_topology': {
                    'hub_count': 0,  # 可以根据实际情况计算
                    'total_ports': 0,
                    'occupied_ports': len(device_details),
                    'free_ports': 0
                }
            }

            # 发送数据同步请求
            url = f"{self.master_server_url}/api/v1/slave/data-sync"
            headers = {'Content-Type': 'application/json'}

            response = self.session.post(
                url,
                json=sync_data,
                headers=headers,
                timeout=(self.connection_timeout, self.read_timeout)
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    logger.info(f"完整数据同步成功，已上报 {len(device_details)} 个设备")
                    return True
                else:
                    logger.error(f"数据同步响应异常: {result.get('message')}")
                    return False
            else:
                logger.error(f"数据同步请求失败: HTTP {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"发送完整数据同步失败: {e}")
            return False
