# -*- coding: utf-8 -*-
"""
INI文件工具模块
基于旧版VHServer的IniFileUtils实现
提供配置文件读取和字符串解析功能
"""

import os
import configparser
from typing import Dict, Any, Callable, Optional
from utils.logger import get_logger

logger = get_logger('ini_file_utils')


def read_properties(file_path: str) -> Dict[str, str]:
    """
    读取properties格式的配置文件
    
    Args:
        file_path: 配置文件路径
    
    Returns:
        Dict[str, str]: 配置字典
    """
    try:
        if not os.path.exists(file_path):
            logger.warning(f"配置文件不存在: {file_path}")
            return {}
        
        config = configparser.ConfigParser()
        config.read(file_path, encoding='utf-8')
        
        # 将所有section的配置合并到一个字典中
        result = {}
        for section in config.sections():
            for key, value in config.items(section):
                result[key] = value
        
        return result
        
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")
        return {}


def read_properties_str(content: str, separator: str = ":", convert: Optional[Callable] = None) -> Dict[str, str]:
    """
    解析字符串格式的配置内容
    基于旧版VHClientSender中的实现
    
    Args:
        content: 配置内容字符串
        separator: 分隔符，默认为冒号
        convert: 键名转换函数
    
    Returns:
        Dict[str, str]: 解析后的配置字典
    """
    try:
        result = {}
        
        if not content or not content.strip():
            return result
        
        lines = content.strip().split('\n')
        for line in lines:
            line = line.strip()
            if not line or separator not in line:
                continue
            
            # 分割键值对
            parts = line.split(separator, 1)
            if len(parts) != 2:
                continue
            
            key = parts[0].strip()
            value = parts[1].strip()
            
            # 应用键名转换函数
            if convert and callable(convert):
                key = convert(key)
            
            result[key] = value
        
        return result
        
    except Exception as e:
        logger.error(f"解析配置字符串失败: {e}")
        return {}


def hump_convert(name: str) -> str:
    """
    将字符串转换为驼峰命名格式
    基于旧版程序的实现
    
    Args:
        name: 原始字符串
    
    Returns:
        str: 驼峰格式字符串
    """
    try:
        if not name:
            return ""
        
        # 移除特殊字符并分割单词
        words = []
        current_word = ""
        
        for char in name:
            if char.isalnum():
                current_word += char
            else:
                if current_word:
                    words.append(current_word.lower())
                    current_word = ""
        
        if current_word:
            words.append(current_word.lower())
        
        if not words:
            return ""
        
        # 第一个单词小写，后续单词首字母大写
        result = words[0]
        for word in words[1:]:
            if word:
                result += word.capitalize()
        
        return result
        
    except Exception as e:
        logger.error(f"驼峰转换失败: {e}")
        return name


def write_properties(file_path: str, properties: Dict[str, str], section: str = 'DEFAULT') -> bool:
    """
    写入properties格式的配置文件
    
    Args:
        file_path: 配置文件路径
        properties: 配置字典
        section: 配置节名称
    
    Returns:
        bool: 写入是否成功
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        config = configparser.ConfigParser()
        
        # 如果文件存在，先读取现有配置
        if os.path.exists(file_path):
            config.read(file_path, encoding='utf-8')
        
        # 确保section存在
        if not config.has_section(section):
            config.add_section(section)
        
        # 写入配置
        for key, value in properties.items():
            config.set(section, key, str(value))
        
        # 保存文件
        with open(file_path, 'w', encoding='utf-8') as f:
            config.write(f)
        
        logger.info(f"配置文件写入成功: {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"写入配置文件失败: {e}")
        return False


def merge_properties(base_props: Dict[str, str], override_props: Dict[str, str]) -> Dict[str, str]:
    """
    合并配置字典
    
    Args:
        base_props: 基础配置
        override_props: 覆盖配置
    
    Returns:
        Dict[str, str]: 合并后的配置
    """
    try:
        result = base_props.copy()
        result.update(override_props)
        return result
    except Exception as e:
        logger.error(f"合并配置失败: {e}")
        return base_props


def validate_properties(properties: Dict[str, str], required_keys: list) -> bool:
    """
    验证配置是否包含必需的键
    
    Args:
        properties: 配置字典
        required_keys: 必需的键列表
    
    Returns:
        bool: 验证是否通过
    """
    try:
        missing_keys = []
        for key in required_keys:
            if key not in properties or not properties[key]:
                missing_keys.append(key)
        
        if missing_keys:
            logger.error(f"配置缺少必需的键: {missing_keys}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"配置验证失败: {e}")
        return False


def get_property_with_default(properties: Dict[str, str], key: str, default: str = "") -> str:
    """
    获取配置值，如果不存在则返回默认值
    
    Args:
        properties: 配置字典
        key: 配置键
        default: 默认值
    
    Returns:
        str: 配置值
    """
    return properties.get(key, default)


def convert_property_type(value: str, target_type: type) -> Any:
    """
    转换配置值类型
    
    Args:
        value: 配置值字符串
        target_type: 目标类型
    
    Returns:
        Any: 转换后的值
    """
    try:
        if target_type == bool:
            return value.lower() in ('true', '1', 'yes', 'on')
        elif target_type == int:
            return int(value)
        elif target_type == float:
            return float(value)
        else:
            return str(value)
    except Exception as e:
        logger.error(f"类型转换失败: {e}")
        return value
