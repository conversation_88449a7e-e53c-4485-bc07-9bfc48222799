# -*- coding: utf-8 -*-
"""
设备控制器模块
负责执行设备控制命令（连接/断开/重置等）
"""

import time
from typing import Dict, Any, Optional, List, Union, Tuple
from utils.logger import get_logger
from utils.vh_client import VirtualHereClient
from utils.device_event_handler import DeviceEventHandler, DeviceEvent, DeviceEventType
from db.device_dao import DeviceDAO

logger = get_logger('device_controller')


class DeviceController:
    """设备控制器"""
    
    def __init__(self, event_handler: Optional[DeviceEventHandler] = None) -> None:
        """
        初始化设备控制器
        
        Args:
            event_handler: 事件处理器
        """
        self.vh_client = VirtualHereClient()
        self.device_dao = DeviceDAO()
        self.event_handler = event_handler
        self.command_timeout = 30  # 命令超时时间（秒）
    
    def connect_device(self, device_uuid: str, user_info: Optional[Dict] = None) -> Dict[str, Any]:
        """
        连接设备
        
        Args:
            device_uuid: 设备UUID
            user_info: 用户信息
        
        Returns:
            Dict: 操作结果
        """
        try:
            logger.info(f"开始连接设备: {device_uuid}")
            
            # 获取设备信息
            device = self.device_dao.get_device_by_uuid(device_uuid)
            if not device:
                return {
                    'success': False,
                    'error': '设备不存在',
                    'device_uuid': device_uuid
                }
            
            # 检查设备状态
            if device.status == 'in_use':
                return {
                    'success': False,
                    'error': '设备正在使用中',
                    'device_uuid': device_uuid
                }
            
            if device.disable:
                return {
                    'success': False,
                    'error': '设备已被禁用',
                    'device_uuid': device_uuid
                }
            
            # 执行连接命令
            command = f"USE,{device.device_address}"
            response = self.vh_client.send_command(command)
            
            if response and "OK" in response:
                # 更新设备状态
                self.device_dao.update_device_status(device_uuid, 'in_use')
                
                # 发出设备使用事件
                if self.event_handler:
                    event = DeviceEvent(
                        event_type=DeviceEventType.DEVICE_USED,
                        device_uuid=device_uuid,
                        device_address=device.device_address,
                        device_info=self._device_to_dict(device),
                        timestamp=time.time(),
                        old_status='available',
                        new_status='in_use',
                        user_info=user_info
                    )
                    self.event_handler.emit_event(event)
                
                logger.info(f"设备连接成功: {device_uuid}")
                return {
                    'success': True,
                    'message': '设备连接成功',
                    'device_uuid': device_uuid,
                    'device_address': device.device_address
                }
            else:
                logger.error(f"设备连接失败: {device_uuid}, 响应: {response}")
                return {
                    'success': False,
                    'error': f'VirtualHere连接失败: {response}',
                    'device_uuid': device_uuid
                }
                
        except Exception as e:
            logger.error(f"连接设备异常: {e}")
            return {
                'success': False,
                'error': f'连接设备异常: {str(e)}',
                'device_uuid': device_uuid
            }
    
    def disconnect_device(self, device_uuid: str, user_info: Optional[Dict] = None) -> Dict[str, Any]:
        """
        断开设备连接
        
        Args:
            device_uuid: 设备UUID
            user_info: 用户信息
        
        Returns:
            Dict: 操作结果
        """
        try:
            logger.info(f"开始断开设备: {device_uuid}")
            
            # 获取设备信息
            device = self.device_dao.get_device_by_uuid(device_uuid)
            if not device:
                return {
                    'success': False,
                    'error': '设备不存在',
                    'device_uuid': device_uuid
                }
            
            # 执行断开命令
            command = f"STOP USING,{device.device_address}"
            response = self.vh_client.send_command(command)
            
            if response and "OK" in response:
                # 更新设备状态
                self.device_dao.update_device_status(device_uuid, 'available')
                
                # 发出设备释放事件
                if self.event_handler:
                    event = DeviceEvent(
                        event_type=DeviceEventType.DEVICE_RELEASED,
                        device_uuid=device_uuid,
                        device_address=device.device_address,
                        device_info=self._device_to_dict(device),
                        timestamp=time.time(),
                        old_status='in_use',
                        new_status='available',
                        user_info=user_info
                    )
                    self.event_handler.emit_event(event)
                
                logger.info(f"设备断开成功: {device_uuid}")
                return {
                    'success': True,
                    'message': '设备断开成功',
                    'device_uuid': device_uuid,
                    'device_address': device.device_address
                }
            else:
                logger.error(f"设备断开失败: {device_uuid}, 响应: {response}")
                return {
                    'success': False,
                    'error': f'VirtualHere断开失败: {response}',
                    'device_uuid': device_uuid
                }
                
        except Exception as e:
            logger.error(f"断开设备异常: {e}")
            return {
                'success': False,
                'error': f'断开设备异常: {str(e)}',
                'device_uuid': device_uuid
            }
    
    def reset_device(self, device_uuid: str) -> Dict[str, Any]:
        """
        重置设备
        
        Args:
            device_uuid: 设备UUID
        
        Returns:
            Dict: 操作结果
        """
        try:
            logger.info(f"开始重置设备: {device_uuid}")
            
            # 获取设备信息
            device = self.device_dao.get_device_by_uuid(device_uuid)
            if not device:
                return {
                    'success': False,
                    'error': '设备不存在',
                    'device_uuid': device_uuid
                }
            
            # 先断开设备（如果正在使用）
            if device.status == 'in_use':
                disconnect_result = self.disconnect_device(device_uuid)
                if not disconnect_result['success']:
                    return disconnect_result
                
                # 等待断开完成
                time.sleep(1)
            
            # 执行重置命令
            command = f"DEVICE RESET,{device.device_address}"
            response = self.vh_client.send_command(command)
            
            if response and "OK" in response:
                # 更新设备状态
                self.device_dao.update_device_status(device_uuid, 'available')
                
                logger.info(f"设备重置成功: {device_uuid}")
                return {
                    'success': True,
                    'message': '设备重置成功',
                    'device_uuid': device_uuid,
                    'device_address': device.device_address
                }
            else:
                logger.error(f"设备重置失败: {device_uuid}, 响应: {response}")
                return {
                    'success': False,
                    'error': f'VirtualHere重置失败: {response}',
                    'device_uuid': device_uuid
                }
                
        except Exception as e:
            logger.error(f"重置设备异常: {e}")
            return {
                'success': False,
                'error': f'重置设备异常: {str(e)}',
                'device_uuid': device_uuid
            }
    
    def rename_device(self, device_uuid: str, new_name: str) -> Dict[str, Any]:
        """
        重命名设备
        
        Args:
            device_uuid: 设备UUID
            new_name: 新名称
        
        Returns:
            Dict: 操作结果
        """
        try:
            logger.info(f"开始重命名设备: {device_uuid} -> {new_name}")
            
            # 获取设备信息
            device = self.device_dao.get_device_by_uuid(device_uuid)
            if not device:
                return {
                    'success': False,
                    'error': '设备不存在',
                    'device_uuid': device_uuid
                }
            
            # 执行重命名命令
            response = self.vh_client.rename_device(device.device_address, new_name)
            
            if response and "OK" in response:
                # 更新数据库中的设备名称
                self.device_dao.update_device(device_uuid, {'nick_name': new_name})
                
                logger.info(f"设备重命名成功: {device_uuid}")
                return {
                    'success': True,
                    'message': '设备重命名成功',
                    'device_uuid': device_uuid,
                    'old_name': device.nick_name,
                    'new_name': new_name
                }
            else:
                logger.error(f"设备重命名失败: {device_uuid}, 响应: {response}")
                return {
                    'success': False,
                    'error': f'VirtualHere重命名失败: {response}',
                    'device_uuid': device_uuid
                }
                
        except Exception as e:
            logger.error(f"重命名设备异常: {e}")
            return {
                'success': False,
                'error': f'重命名设备异常: {str(e)}',
                'device_uuid': device_uuid
            }
    
    def get_device_status(self, device_uuid: str) -> Dict[str, Any]:
        """
        获取设备状态
        
        Args:
            device_uuid: 设备UUID
        
        Returns:
            Dict: 设备状态信息
        """
        try:
            # 从数据库获取设备信息
            device = self.device_dao.get_device_by_uuid(device_uuid)
            if not device:
                return {
                    'success': False,
                    'error': '设备不存在',
                    'device_uuid': device_uuid
                }
            
            # 从VirtualHere获取实时状态
            vh_devices = self.vh_client.get_client_state()
            vh_device = None
            for vh_dev in vh_devices:
                if vh_dev.get('device_uuid') == device_uuid:
                    vh_device = vh_dev
                    break
            
            return {
                'success': True,
                'device_uuid': device_uuid,
                'database_info': self._device_to_dict(device),
                'virtualhere_info': vh_device,
                'is_online': vh_device is not None
            }
            
        except Exception as e:
            logger.error(f"获取设备状态异常: {e}")
            return {
                'success': False,
                'error': f'获取设备状态异常: {str(e)}',
                'device_uuid': device_uuid
            }
    
    def _device_to_dict(self, device) -> Dict[str, Any]:
        """
        将设备对象转换为字典
        
        Args:
            device: 设备对象
        
        Returns:
            Dict: 设备字典
        """
        return {
            'device_uuid': device.device_uuid,
            'device_address': device.device_address,
            'vendor': device.vendor,
            'vendor_id': device.vendor_id,
            'product': device.product,
            'product_id': device.product_id,
            'device_serial': device.device_serial,
            'nick_name': device.nick_name,
            'status': device.status,
            'server_name': device.server_name,
            'hostname': device.hostname,
            'shown': device.shown,
            'disable': device.disable,
            'last_seen': device.last_seen.isoformat() if device.last_seen else None
        }
