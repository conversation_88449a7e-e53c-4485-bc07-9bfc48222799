# -*- coding: utf-8 -*-
"""
VirtualHere服务器管理模块
负责VirtualHere服务器的启动、停止和监控
"""

import os
import subprocess
import time
import psutil
import socket
from typing import Optional, Dict, List, Any, Union, Tuple, ClassVar
from utils.logger import get_logger
from utils.config_manager import ConfigManager

logger = get_logger('vh_server_manager')


class VirtualHereServerManager:
    """VirtualHere服务器管理器"""
    
    def __init__(self) -> None:
        """初始化VH服务器管理器"""
        self.config_manager = ConfigManager()
        vh_config = self.config_manager.get_virtualhere_config()

        # 从配置获取VirtualHere设置
        self.service_name = "vhusbd"
        self.process_name = "vhusbd"
        self.default_port = vh_config.get('server_port', 7575)

        # 智能选择二进制文件路径
        configured_path = vh_config.get('binary_path', 'auto')
        if configured_path == 'auto':
            self.binary_path = self._detect_architecture()
        else:
            self.binary_path = configured_path

        self.config_file = vh_config.get('config_file', 'config/vhusbd.conf')
        self.pid_file = vh_config.get('pid_file', 'data/vhusbd.pid')

        # 记录架构信息
        arch_info = self._get_architecture_info()
        logger.info(f"VirtualHere服务器管理器初始化完成")
        logger.info(f"系统架构: {arch_info['machine']} ({'ARM' if arch_info['is_arm'] else 'x86'})")
        logger.info(f"二进制路径: {self.binary_path}")
        logger.info(f"配置文件: {self.config_file}")
        logger.info(f"默认端口: {self.default_port}")

    def _detect_architecture(self) -> str:
        """
        检测系统架构并返回推荐的二进制文件路径

        Returns:
            str: 推荐的VirtualHere二进制文件路径
        """
        import platform
        from pathlib import Path

        machine = platform.machine().lower()

        if 'arm' in machine or 'aarch' in machine:
            # ARM架构，优先使用本地ARM版本
            local_arm_server = Path(__file__).parent.parent / 'virtualhere/vhusbdarmpi3'
            if local_arm_server.exists():
                logger.info(f"使用本地ARM二进制文件: {local_arm_server}")
                return str(local_arm_server)
            else:
                logger.warning("本地ARM二进制文件不存在，使用默认ARM路径")
                return './vhusbd_arm'
        else:
            # x86架构
            logger.info("检测到x86架构，使用标准二进制文件")
            return './vhusbd'

    def _get_architecture_info(self) -> Dict[str, Any]:
        """
        获取详细的架构信息

        Returns:
            Dict: 架构信息
        """
        import platform

        return {
            'machine': platform.machine(),
            'processor': platform.processor(),
            'architecture': platform.architecture(),
            'is_arm': 'arm' in platform.machine().lower() or 'aarch' in platform.machine().lower()
        }

    def start_server(self) -> bool:
        """
        启动VirtualHere服务器
        
        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info("正在启动VirtualHere服务器...")

            # 检查端口是否被占用
            if self._is_port_in_use(self.default_port):
                logger.warning(f"端口 {self.default_port} 已被占用，尝试停止现有服务")
                self.stop_server()
                time.sleep(2)

            # 确保配置文件存在
            self._ensure_config_file()

            # 使用systemctl启动服务
            result = subprocess.run(
                ["systemctl", "start", self.service_name],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                # 等待服务启动
                time.sleep(3)

                # 验证服务状态
                if self.is_server_running():
                    logger.info("VirtualHere服务器启动成功")
                    return True
                else:
                    logger.error("VirtualHere服务器启动失败：服务未运行")
                    return False
            else:
                logger.error(f"VirtualHere服务器启动失败: {result.stderr}")
                # 尝试直接启动二进制文件
                return self._start_binary_directly()
                
        except subprocess.TimeoutExpired:
            logger.error("VirtualHere服务器启动超时")
            return False
        except Exception as e:
            logger.error(f"启动VirtualHere服务器异常: {e}")
            return False
    
    def stop_server(self) -> bool:
        """
        停止VirtualHere服务器
        
        Returns:
            bool: 停止是否成功
        """
        try:
            logger.info("正在停止VirtualHere服务器...")
            
            # 使用systemctl停止服务
            result = subprocess.run(
                ["systemctl", "stop", self.service_name],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                # 等待服务停止
                time.sleep(2)

                # 验证服务状态
                if not self.is_server_running():
                    logger.info("VirtualHere服务器停止成功")
                    return True
                else:
                    logger.warning("VirtualHere服务器可能未完全停止，尝试强制终止")
                    return self._force_kill_server()
            else:
                logger.error(f"VirtualHere服务器停止失败: {result.stderr}")
                # 尝试强制终止
                return self._force_kill_server()
                
        except subprocess.TimeoutExpired:
            logger.error("VirtualHere服务器停止超时")
            return False
        except Exception as e:
            logger.error(f"停止VirtualHere服务器异常: {e}")
            return False
    
    def restart_server(self) -> bool:
        """
        重启VirtualHere服务器
        
        Returns:
            bool: 重启是否成功
        """
        try:
            logger.info("正在重启VirtualHere服务器...")
            
            # 先停止服务
            if not self.stop_server():
                logger.warning("停止服务失败，尝试强制重启")
            
            # 等待一段时间
            time.sleep(1)
            
            # 启动服务
            return self.start_server()
            
        except Exception as e:
            logger.error(f"重启VirtualHere服务器异常: {e}")
            return False
    
    def is_server_running(self) -> bool:
        """
        检查VirtualHere服务器是否运行
        
        Returns:
            bool: 是否运行
        """
        try:
            # 方法1：检查systemctl状态
            result = subprocess.run(
                ["systemctl", "is-active", self.service_name],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0 and result.stdout.strip() == "active":
                return True
            
            # 方法2：检查进程
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if self.process_name in proc.info['name']:
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"检查VH服务器状态失败: {e}")
            return False
    
    def get_server_status(self) -> Dict[str, Any]:
        """
        获取VirtualHere服务器详细状态
        
        Returns:
            Dict: 服务器状态信息
        """
        try:
            status_info = {
                'running': self.is_server_running(),
                'service_status': 'unknown',
                'process_info': None,
                'port_status': 'unknown',
                'config_exists': os.path.exists(self.config_file)
            }
            
            # 获取systemctl状态
            try:
                result = subprocess.run(
                    ["systemctl", "status", self.service_name],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                status_info['service_status'] = 'active' if result.returncode == 0 else 'inactive'
            except Exception as e:
                pass
            
            # 获取进程信息
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info']):
                try:
                    if self.process_name in proc.info['name']:
                        status_info['process_info'] = {
                            'pid': proc.info['pid'],
                            'cpu_percent': proc.info['cpu_percent'],
                            'memory_mb': proc.info['memory_info'].rss / 1024 / 1024
                        }
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 检查端口状态
            status_info['port_status'] = self._check_port_status(self.default_port)
            
            return status_info
            
        except Exception as e:
            logger.error(f"获取VH服务器状态失败: {e}")
            return {'running': False, 'error': str(e)}
    
    def _check_port_status(self, port: int) -> str:
        """
        检查端口状态
        
        Args:
            port: 端口号
        
        Returns:
            str: 端口状态
        """
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                    return 'listening'
            return 'not_listening'
        except Exception as e:
            logger.error(f"检查端口状态失败: {e}")
            return 'unknown'
    
    def configure_server(self, config: Dict[str, Any]) -> bool:
        """
        配置VirtualHere服务器（增强版）

        Args:
            config: 配置字典

        Returns:
            bool: 配置是否成功
        """
        try:
            logger.info("正在配置VirtualHere服务器...")

            # 验证配置参数
            if not self._validate_config(config):
                logger.error("配置参数验证失败")
                return False

            # 备份现有配置
            self._backup_config()

            # 生成配置文件内容
            config_content = self._generate_config_content(config)

            # 写入配置文件
            with open(self.config_file, 'w') as f:
                f.write(config_content)

            logger.info(f"VirtualHere配置文件已更新: {self.config_file}")

            # 更新内部配置
            if 'tcp_port' in config:
                self.default_port = int(config['tcp_port'])
                # 同步更新配置管理器
                self.config_manager.set_value('virtualhere', 'server_port', str(self.default_port))
                self.config_manager.save_config()

            # 如果服务正在运行，重启以应用配置
            if self.is_server_running():
                logger.info("重启VirtualHere服务以应用新配置")
                return self.restart_server()

            return True

        except Exception as e:
            logger.error(f"配置VirtualHere服务器失败: {e}")
            # 尝试恢复备份配置
            self._restore_config_backup()
            return False
    
    def _generate_config_content(self, config: Dict[str, Any]) -> str:
        """
        生成配置文件内容（增强版）

        Args:
            config: 配置字典

        Returns:
            str: 配置文件内容
        """
        import datetime

        # 获取当前时间戳
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 基本配置模板
        config_template = f"""# VirtualHere USB Server Configuration
# Generated by OmniLink Slave Server
# Created: {timestamp}

# Server settings
ServerName={config.get('server_name', 'OmniLink USB Hub')}
TCPPort={config.get('tcp_port', self.default_port)}

# Device settings
AutoFind={config.get('auto_find', 'true')}
AutoUse={config.get('auto_use', 'false')}

# Security settings
RequireAuth={config.get('require_auth', 'false')}

# Performance settings
"""

        # 添加可选配置项
        optional_configs = {
            'max_devices': 'MaxDevices',
            'interface': 'Interface',
            'log_level': 'LogLevel',
            'timeout': 'Timeout',
            'buffer_size': 'BufferSize',
            'compression': 'Compression',
            'encryption': 'Encryption'
        }

        for key, config_key in optional_configs.items():
            if key in config and config[key]:
                config_template += f"{config_key}={config[key]}\n"

        # 添加局域网优化配置（针对新架构）
        config_template += """
# LAN optimization settings (for local master-slave architecture)
LocalNetwork=true
FastMode=true
"""

        # 添加自定义配置项
        if 'custom_configs' in config and isinstance(config['custom_configs'], dict):
            config_template += "\n# Custom settings\n"
            for key, value in config['custom_configs'].items():
                config_template += f"{key}={value}\n"

        return config_template
    
    def get_server_logs(self, lines: int = 50) -> str:
        """
        获取VirtualHere服务器日志
        
        Args:
            lines: 日志行数
        
        Returns:
            str: 日志内容
        """
        try:
            result = subprocess.run(
                ["journalctl", "-u", self.service_name, "-n", str(lines), "--no-pager"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return result.stdout
            else:
                return f"获取日志失败: {result.stderr}"
                
        except Exception as e:
            logger.error(f"获取VH服务器日志失败: {e}")
            return f"获取日志异常: {str(e)}"

    def _is_port_in_use(self, port: int) -> bool:
        """
        检查端口是否被占用

        Args:
            port: 端口号

        Returns:
            bool: 端口是否被占用
        """
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                result = sock.connect_ex(('localhost', port))
                return result == 0
        except Exception as e:
            logger.error(f"检查端口占用失败: {e}")
            return False

    def _ensure_config_file(self):
        """确保配置文件存在"""
        try:
            if not os.path.exists(self.config_file):
                logger.info(f"配置文件不存在，创建默认配置: {self.config_file}")

                # 确保目录存在
                config_dir = os.path.dirname(self.config_file)
                os.makedirs(config_dir, exist_ok=True)

                # 创建默认配置
                default_config = {
                    'server_name': 'OmniLink USB Hub',
                    'tcp_port': self.default_port,
                    'auto_find': 'true',
                    'auto_use': 'false',
                    'require_auth': 'false'
                }

                self.configure_server(default_config)
        except Exception as e:
            logger.error(f"确保配置文件存在失败: {e}")

    def _start_binary_directly(self) -> bool:
        """
        直接启动VirtualHere二进制文件

        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info("尝试直接启动VirtualHere二进制文件")

            if not os.path.exists(self.binary_path):
                logger.error(f"VirtualHere二进制文件不存在: {self.binary_path}")
                return False

            # 确保二进制文件可执行
            os.chmod(self.binary_path, 0o755)

            # 启动进程
            process = subprocess.Popen(
                [self.binary_path],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                start_new_session=True
            )

            # 等待启动
            time.sleep(3)

            # 检查进程是否还在运行
            if process.poll() is None and self.is_server_running():
                logger.info("VirtualHere二进制文件启动成功")
                return True
            else:
                logger.error("VirtualHere二进制文件启动失败")
                return False

        except Exception as e:
            logger.error(f"直接启动VirtualHere二进制文件失败: {e}")
            return False

    def _force_kill_server(self) -> bool:
        """
        强制终止VirtualHere服务器

        Returns:
            bool: 终止是否成功
        """
        try:
            logger.info("强制终止VirtualHere服务器进程")

            killed_count = 0
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if self.process_name in proc.info['name']:
                        proc.kill()
                        killed_count += 1
                        logger.info(f"终止进程: PID {proc.info['pid']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if killed_count > 0:
                time.sleep(2)
                logger.info(f"强制终止了 {killed_count} 个VirtualHere进程")
                return not self.is_server_running()
            else:
                logger.info("没有找到需要终止的VirtualHere进程")
                return True

        except Exception as e:
            logger.error(f"强制终止VirtualHere服务器失败: {e}")
            return False

    def enable_service(self) -> bool:
        """
        启用VirtualHere服务（开机自启）

        Returns:
            bool: 启用是否成功
        """
        try:
            logger.info("启用VirtualHere服务开机自启")

            result = subprocess.run(
                ["systemctl", "enable", self.service_name],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                logger.info("VirtualHere服务开机自启启用成功")
                return True
            else:
                logger.error(f"启用VirtualHere服务开机自启失败: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"启用VirtualHere服务开机自启异常: {e}")
            return False

    def disable_service(self) -> bool:
        """
        禁用VirtualHere服务（开机自启）

        Returns:
            bool: 禁用是否成功
        """
        try:
            logger.info("禁用VirtualHere服务开机自启")

            result = subprocess.run(
                ["systemctl", "disable", self.service_name],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                logger.info("VirtualHere服务开机自启禁用成功")
                return True
            else:
                logger.error(f"禁用VirtualHere服务开机自启失败: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"禁用VirtualHere服务开机自启异常: {e}")
            return False

    def _validate_config(self, config: Dict) -> bool:
        """
        验证配置参数

        Args:
            config: 配置字典

        Returns:
            bool: 验证是否通过
        """
        try:
            # 验证端口号
            if 'tcp_port' in config:
                port = int(config['tcp_port'])
                if not (1 <= port <= 65535):
                    logger.error(f"无效的端口号: {port}")
                    return False

                # 检查端口是否被其他服务占用（除了当前VH服务）
                if self._is_port_in_use(port) and not self.is_server_running():
                    logger.error(f"端口 {port} 已被其他服务占用")
                    return False

            # 验证服务器名称
            if 'server_name' in config:
                name = config['server_name']
                if not name or len(name.strip()) == 0:
                    logger.error("服务器名称不能为空")
                    return False
                if len(name) > 64:
                    logger.error("服务器名称过长（最大64字符）")
                    return False

            # 验证布尔值配置
            bool_configs = ['auto_find', 'auto_use', 'require_auth']
            for key in bool_configs:
                if key in config:
                    value = str(config[key]).lower()
                    if value not in ['true', 'false', '1', '0', 'yes', 'no']:
                        logger.error(f"无效的布尔值配置 {key}: {config[key]}")
                        return False

            # 验证数值配置
            if 'max_devices' in config:
                try:
                    max_devices = int(config['max_devices'])
                    if max_devices < 0:
                        logger.error("最大设备数不能为负数")
                        return False
                except ValueError:
                    logger.error(f"无效的最大设备数: {config['max_devices']}")
                    return False

            return True

        except Exception as e:
            logger.error(f"配置验证异常: {e}")
            return False

    def _backup_config(self):
        """备份当前配置文件"""
        try:
            if os.path.exists(self.config_file):
                backup_file = f"{self.config_file}.backup"
                import shutil
                shutil.copy2(self.config_file, backup_file)
                logger.debug(f"配置文件已备份: {backup_file}")
        except Exception as e:
            logger.warning(f"备份配置文件失败: {e}")

    def _restore_config_backup(self):
        """恢复配置文件备份"""
        try:
            backup_file = f"{self.config_file}.backup"
            if os.path.exists(backup_file):
                import shutil
                shutil.copy2(backup_file, self.config_file)
                logger.info("配置文件已从备份恢复")
                return True
            return False
        except Exception as e:
            logger.error(f"恢复配置文件备份失败: {e}")
            return False

    def get_current_config(self) -> Dict[str, Any]:
        """
        获取当前配置

        Returns:
            Dict: 当前配置字典
        """
        try:
            if not os.path.exists(self.config_file):
                return {}

            config = {}
            with open(self.config_file, 'r') as f:
                content = f.read()

            # 解析配置文件内容
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()

            return config

        except Exception as e:
            logger.error(f"获取当前配置失败: {e}")
            return {}

    def update_config_item(self, key: str, value: str) -> bool:
        """
        更新单个配置项

        Args:
            key: 配置键
            value: 配置值

        Returns:
            bool: 更新是否成功
        """
        try:
            current_config = self.get_current_config()
            current_config[key] = value

            # 验证更新后的配置
            if not self._validate_config(current_config):
                return False

            return self.configure_server(current_config)

        except Exception as e:
            logger.error(f"更新配置项失败: {e}")
            return False

    def get_default_config(self) -> Dict[str, str]:
        """
        获取默认配置

        Returns:
            Dict: 默认配置字典
        """
        return {
            'server_name': 'OmniLink USB Hub',
            'tcp_port': str(self.default_port),
            'auto_find': 'true',
            'auto_use': 'false',
            'require_auth': 'false',
            'max_devices': '0',  # 0表示无限制
            'interface': 'all'
        }

    def reset_to_default_config(self) -> bool:
        """
        重置为默认配置

        Returns:
            bool: 重置是否成功
        """
        try:
            logger.info("重置VirtualHere服务器为默认配置")
            default_config = self.get_default_config()
            return self.configure_server(default_config)
        except Exception as e:
            logger.error(f"重置默认配置失败: {e}")
            return False
