# -*- coding: utf-8 -*-
"""
服务健康检查模块
监控各个服务组件的健康状态
"""

import os
import time
import psutil
from typing import Dict, List, Callable, Any, Optional, Union, Tuple
from utils.logger import get_logger
from utils.error_handler import global_auto_recovery
from utils.vh_server_manager import VirtualHereServerManager
from utils.config_manager import ConfigManager
from utils.master_communication import MasterCommunication
from db import database
from db.device_dao import DeviceDAO

logger = get_logger('health_checker')


class HealthChecker:
    """健康检查器"""
    
    def __init__(self) -> None:
        """初始化健康检查器"""
        self.checks = {}
        self.last_check_results = {}

        # 初始化组件
        self.vh_server_manager = VirtualHereServerManager()
        self.config_manager = ConfigManager()
        self.master_comm = MasterCommunication()
        self.device_dao = DeviceDAO()

        # 健康检查配置
        self.thresholds = {
            'cpu_warning': 80.0,
            'cpu_critical': 95.0,
            'memory_warning': 80.0,
            'memory_critical': 95.0,
            'disk_warning': 85.0,
            'disk_critical': 95.0,
            'temperature_warning': 70.0,
            'temperature_critical': 85.0
        }

        # 注册默认检查
        self._register_default_checks()
    
    def register_check(self, name: str, check_func: Callable, critical: bool = False) -> Any:
        """
        注册健康检查
        
        Args:
            name: 检查名称
            check_func: 检查函数，返回(bool, str)
            critical: 是否为关键检查
        """
        self.checks[name] = {
            'func': check_func,
            'critical': critical
        }
        logger.info(f"注册健康检查: {name} (关键: {critical})")
    
    def run_all_checks(self) -> Dict:
        """
        运行所有健康检查
        
        Returns:
            Dict: 检查结果
        """
        results = {
            'overall_status': 'healthy',
            'timestamp': time.time(),
            'checks': {},
            'critical_failures': [],
            'warnings': []
        }
        
        for name, check_config in self.checks.items():
            try:
                is_healthy, message = check_config['func']()
                
                check_result = {
                    'status': 'healthy' if is_healthy else 'unhealthy',
                    'message': message,
                    'critical': check_config['critical']
                }
                
                results['checks'][name] = check_result
                
                if not is_healthy:
                    if check_config['critical']:
                        results['critical_failures'].append(name)
                        results['overall_status'] = 'critical'
                    else:
                        results['warnings'].append(name)
                        if results['overall_status'] == 'healthy':
                            results['overall_status'] = 'warning'
                
            except Exception as e:
                logger.error(f"健康检查 {name} 执行失败: {e}")
                results['checks'][name] = {
                    'status': 'error',
                    'message': f'检查执行失败: {str(e)}',
                    'critical': check_config['critical']
                }
                
                if check_config['critical']:
                    results['critical_failures'].append(name)
                    results['overall_status'] = 'critical'
        
        self.last_check_results = results
        return results
    
    def get_last_results(self) -> Dict:
        """获取最后一次检查结果"""
        return self.last_check_results


# 全局健康检查器
global_health_checker = HealthChecker()


def check_database_health() -> tuple:
    """检查数据库健康状态"""
    try:
        # 尝试连接数据库
        database.connect()
        
        # 执行简单查询
        cursor = database.execute_sql("SELECT 1")
        result = cursor.fetchone()
        
        database.close()
        
        if result and result[0] == 1:
            return True, "数据库连接正常"
        else:
            return False, "数据库查询异常"
            
    except Exception as e:
        return False, f"数据库连接失败: {str(e)}"


def check_disk_space() -> tuple:
    """检查磁盘空间"""
    try:
        disk_usage = psutil.disk_usage('/')
        free_percent = (disk_usage.free / disk_usage.total) * 100
        
        if free_percent < 5:
            return False, f"磁盘空间严重不足: {free_percent:.1f}%"
        elif free_percent < 10:
            return False, f"磁盘空间不足: {free_percent:.1f}%"
        else:
            return True, f"磁盘空间充足: {free_percent:.1f}%"
            
    except Exception as e:
        return False, f"磁盘空间检查失败: {str(e)}"


def check_memory_usage() -> tuple:
    """检查内存使用率"""
    try:
        memory = psutil.virtual_memory()
        
        if memory.percent > 90:
            return False, f"内存使用率过高: {memory.percent:.1f}%"
        elif memory.percent > 80:
            return False, f"内存使用率较高: {memory.percent:.1f}%"
        else:
            return True, f"内存使用正常: {memory.percent:.1f}%"
            
    except Exception as e:
        return False, f"内存检查失败: {str(e)}"


def check_virtualhere_pipes() -> tuple:
    """检查VirtualHere管道"""
    try:
        write_pipe = '/tmp/vhclient'
        read_pipe = '/tmp/vhclient_response'
        
        write_exists = os.path.exists(write_pipe)
        read_exists = os.path.exists(read_pipe)
        
        if write_exists and read_exists:
            return True, "VirtualHere管道正常"
        elif write_exists or read_exists:
            return False, f"VirtualHere管道部分缺失 (写入: {write_exists}, 读取: {read_exists})"
        else:
            return False, "VirtualHere管道不存在"
            
    except Exception as e:
        return False, f"VirtualHere管道检查失败: {str(e)}"


def check_log_directory() -> tuple:
    """检查日志目录"""
    try:
        log_dir = '/app/logs'
        
        if not os.path.exists(log_dir):
            return False, "日志目录不存在"
        
        if not os.access(log_dir, os.W_OK):
            return False, "日志目录无写入权限"
        
        return True, "日志目录正常"
        
    except Exception as e:
        return False, f"日志目录检查失败: {str(e)}"


def check_config_files() -> tuple:
    """检查配置文件"""
    try:
        config_dir = '/app/config'
        
        if not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)
        
        return True, "配置目录正常"
        
    except Exception as e:
        return False, f"配置目录检查失败: {str(e)}"


def setup_health_checks() -> None:
    """设置健康检查"""
    # 注册关键健康检查
    global_health_checker.register_check('database', check_database_health, critical=True)
    global_health_checker.register_check('disk_space', check_disk_space, critical=True)
    global_health_checker.register_check('memory_usage', check_memory_usage, critical=False)
    global_health_checker.register_check('log_directory', check_log_directory, critical=True)
    global_health_checker.register_check('config_files', check_config_files, critical=True)
    
    # 注册非关键健康检查
    global_health_checker.register_check('virtualhere_pipes', check_virtualhere_pipes, critical=False)
    
    # 注册自动恢复任务
    def database_recovery() -> None:
        """数据库恢复"""
        try:
            if database.is_closed():
                database.connect()
            return True
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            return False
    
    def log_directory_recovery() -> None:
        """日志目录恢复"""
        try:
            log_dir = '/app/logs'
            os.makedirs(log_dir, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"日志目录恢复失败: {e}")
            return False
    
    def config_directory_recovery() -> None:
        """配置目录恢复"""
        try:
            config_dir = '/app/config'
            os.makedirs(config_dir, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"配置目录恢复失败: {e}")
            return False
    
    # 注册自动恢复任务
    global_auto_recovery.register_task(
        'database_health',
        lambda: check_database_health()[0],
        database_recovery,
        interval=30
    )
    
    global_auto_recovery.register_task(
        'log_directory_health',
        lambda: check_log_directory()[0],
        log_directory_recovery,
        interval=60
    )
    
    global_auto_recovery.register_task(
        'config_directory_health',
        lambda: check_config_files()[0],
        config_directory_recovery,
        interval=60
    )
    
    logger.info("健康检查系统已初始化")


def get_system_status() -> Dict:
    """获取系统状态"""
    try:
        # 运行健康检查
        health_results = global_health_checker.run_all_checks()
        
        # 获取系统资源信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            'health': health_results,
            'resources': {
                'cpu_percent': cpu_percent,
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100
                }
            },
            'timestamp': time.time()
        }

    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return {
            'health': {'overall_status': 'error', 'message': str(e)},
            'resources': {},
            'timestamp': time.time()
        }

    def _register_default_checks(self) -> None:
        """注册默认的健康检查"""
        # VirtualHere服务器检查
        self.register_check('virtualhere_server', self._check_virtualhere_server, critical=True)

        # 数据库检查
        self.register_check('database', self._check_database, critical=True)

        # 主从通信检查
        self.register_check('master_communication', self._check_master_communication, critical=False)

        # 系统资源检查
        self.register_check('system_resources', self._check_system_resources, critical=False)

        # 磁盘空间检查
        self.register_check('disk_space', self._check_disk_space, critical=False)

        # 配置检查
        self.register_check('configuration', self._check_configuration, critical=False)

    def _check_virtualhere_server(self) -> tuple:
        """检查VirtualHere服务器状态"""
        try:
            vh_status = self.vh_server_manager.get_server_status()

            if vh_status['running']:
                if vh_status.get('port_status') == 'listening':
                    return True, 'VirtualHere服务器运行正常'
                else:
                    return False, 'VirtualHere服务器运行但端口状态异常'
            else:
                return False, 'VirtualHere服务器未运行'

        except Exception as e:
            return False, f'VirtualHere服务器检查失败: {str(e)}'

    def _check_database(self) -> tuple:
        """检查数据库状态"""
        try:
            # 尝试查询设备数量
            device_count = self.device_dao.get_device_count()

            # 检查数据库文件
            db_file = self.config_manager.get_database_config().get('db_file', 'data/slave_server.db')
            if os.path.exists(db_file):
                return True, f'数据库连接正常，设备数量: {device_count}'
            else:
                return False, '数据库文件不存在'

        except Exception as e:
            return False, f'数据库检查失败: {str(e)}'

    def _check_master_communication(self) -> tuple:
        """检查主从通信状态"""
        try:
            conn_status = self.master_comm.get_connection_status()

            if conn_status['connected']:
                if conn_status['circuit_breaker_open']:
                    return False, '主从通信连接正常但熔断器已开启'
                else:
                    return True, '主从通信连接正常'
            else:
                return False, '主从通信连接断开'

        except Exception as e:
            return False, f'主从通信检查失败: {str(e)}'

    def _check_system_resources(self) -> tuple:
        """检查系统资源使用情况"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # 检查阈值
            issues = []
            if cpu_percent >= self.thresholds['cpu_critical']:
                issues.append(f'CPU使用率过高: {cpu_percent:.1f}%')
            elif cpu_percent >= self.thresholds['cpu_warning']:
                issues.append(f'CPU使用率较高: {cpu_percent:.1f}%')

            if memory_percent >= self.thresholds['memory_critical']:
                issues.append(f'内存使用率过高: {memory_percent:.1f}%')
            elif memory_percent >= self.thresholds['memory_warning']:
                issues.append(f'内存使用率较高: {memory_percent:.1f}%')

            if issues:
                return False, '; '.join(issues)
            else:
                return True, f'系统资源使用正常 (CPU: {cpu_percent:.1f}%, 内存: {memory_percent:.1f}%)'

        except Exception as e:
            return False, f'系统资源检查失败: {str(e)}'

    def _check_disk_space(self) -> tuple:
        """检查磁盘空间"""
        try:
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100

            if disk_percent >= self.thresholds['disk_critical']:
                return False, f'磁盘空间严重不足: {disk_percent:.1f}%'
            elif disk_percent >= self.thresholds['disk_warning']:
                return False, f'磁盘空间不足: {disk_percent:.1f}%'
            else:
                return True, f'磁盘空间充足: {disk_percent:.1f}%'

        except Exception as e:
            return False, f'磁盘空间检查失败: {str(e)}'

    def _check_configuration(self) -> tuple:
        """检查配置状态"""
        try:
            # 检查配置文件是否存在
            config_file = self.config_manager.config_file
            if not os.path.exists(config_file):
                return False, '配置文件不存在'

            # 检查关键配置项
            try:
                config = self.config_manager.get_all_config()
                if not config:
                    return False, '配置文件为空或无法读取'

                return True, '配置文件正常'

            except Exception as e:
                return False, f'配置文件读取失败: {str(e)}'

        except Exception as e:
            return False, f'配置检查失败: {str(e)}'

    def set_thresholds(self, thresholds: Dict[str, float]) -> Any:
        """
        设置健康检查阈值

        Args:
            thresholds: 阈值字典
        """
        self.thresholds.update(thresholds)
        logger.info(f"健康检查阈值已更新: {thresholds}")

    def get_detailed_status(self) -> Dict:
        """
        获取详细的系统状态信息

        Returns:
            Dict: 详细状态信息
        """
        try:
            # 运行健康检查
            health_results = self.run_all_checks()

            # 获取详细的系统信息
            system_info = {
                'cpu': {
                    'percent': psutil.cpu_percent(interval=1),
                    'count': psutil.cpu_count(),
                    'freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                },
                'memory': psutil.virtual_memory()._asdict(),
                'disk': psutil.disk_usage('/')._asdict(),
                'network': psutil.net_io_counters()._asdict(),
                'boot_time': psutil.boot_time(),
                'uptime': time.time() - psutil.boot_time()
            }

            # 获取组件状态
            component_status = {
                'virtualhere_server': self.vh_server_manager.get_server_status(),
                'master_communication': self.master_comm.get_connection_status(),
                'device_statistics': self.device_dao.get_device_statistics()
            }

            return {
                'health': health_results,
                'system_info': system_info,
                'component_status': component_status,
                'thresholds': self.thresholds,
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"获取详细状态失败: {e}")
            return {
                'health': {'overall_status': 'error', 'message': str(e)},
                'timestamp': time.time()
            }
