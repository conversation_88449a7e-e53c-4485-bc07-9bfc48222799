# -*- coding: utf-8 -*-
"""
备份恢复管理模块
提供数据库备份、配置备份、自动恢复功能
"""

import os
import shutil
import gzip
import json
import time
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
from utils.logger import get_logger
from utils.config_manager import ConfigManager

logger = get_logger('backup_manager')


class BackupManager:
    """备份管理器"""
    
    def __init__(self, backup_dir: str = "backups") -> None:
        """
        初始化备份管理器
        
        Args:
            backup_dir: 备份目录
        """
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
        
        # 备份配置
        self.max_backups = 30  # 最多保留30个备份
        self.auto_backup_interval = 3600  # 1小时自动备份一次
        self.compression_enabled = True
        
        # 自动备份线程
        self.auto_backup_thread: Optional[threading.Thread] = None
        self.auto_backup_running = False
        
        # 配置管理器
        self.config_manager = ConfigManager()
        
    def start_auto_backup(self) -> None:
        """启动自动备份"""
        if self.auto_backup_running:
            logger.warning("自动备份已在运行")
            return
        
        self.auto_backup_running = True
        self.auto_backup_thread = threading.Thread(target=self._auto_backup_loop, daemon=True)
        self.auto_backup_thread.start()
        logger.info("自动备份已启动")
    
    def stop_auto_backup(self) -> None:
        """停止自动备份"""
        self.auto_backup_running = False
        if self.auto_backup_thread and self.auto_backup_thread.is_alive():
            self.auto_backup_thread.join(timeout=5)
        logger.info("自动备份已停止")
    
    def _auto_backup_loop(self) -> None:
        """自动备份循环"""
        while self.auto_backup_running:
            try:
                self.create_full_backup()
                self.cleanup_old_backups()
                time.sleep(self.auto_backup_interval)
            except Exception as e:
                logger.error(f"自动备份失败: {e}")
                time.sleep(60)  # 出错后等待1分钟再重试
    
    def create_full_backup(self) -> str:
        """
        创建完整备份
        
        Returns:
            备份文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"full_backup_{timestamp}"
        backup_path = self.backup_dir / backup_name
        backup_path.mkdir(exist_ok=True)
        
        try:
            # 备份数据库
            db_backup_path = self._backup_database(backup_path)
            logger.info(f"数据库备份完成: {db_backup_path}")
            
            # 备份配置文件
            config_backup_path = self._backup_configuration(backup_path)
            logger.info(f"配置备份完成: {config_backup_path}")
            
            # 备份日志文件
            log_backup_path = self._backup_logs(backup_path)
            logger.info(f"日志备份完成: {log_backup_path}")
            
            # 创建备份元数据
            metadata = {
                'backup_name': backup_name,
                'timestamp': timestamp,
                'backup_type': 'full',
                'components': {
                    'database': str(db_backup_path.relative_to(backup_path)),
                    'configuration': str(config_backup_path.relative_to(backup_path)),
                    'logs': str(log_backup_path.relative_to(backup_path))
                },
                'size_bytes': self._calculate_directory_size(backup_path),
                'created_at': datetime.now().isoformat()
            }
            
            metadata_file = backup_path / 'backup_metadata.json'
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            # 压缩备份（如果启用）
            if self.compression_enabled:
                compressed_path = self._compress_backup(backup_path)
                shutil.rmtree(backup_path)  # 删除未压缩的目录
                backup_path = compressed_path
            
            logger.info(f"完整备份创建成功: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"创建完整备份失败: {e}")
            # 清理失败的备份
            if backup_path.exists():
                shutil.rmtree(backup_path)
            raise
    
    def _backup_database(self, backup_path: Path) -> Path:
        """
        备份数据库
        
        Args:
            backup_path: 备份目录
            
        Returns:
            数据库备份文件路径
        """
        db_backup_dir = backup_path / 'database'
        db_backup_dir.mkdir(exist_ok=True)
        
        # 获取数据库文件路径
        db_file = Path('data/slave_server.db')
        if db_file.exists():
            # 复制数据库文件
            backup_db_file = db_backup_dir / 'slave_server.db'
            shutil.copy2(db_file, backup_db_file)
            
            # 创建数据库转储（如果可能）
            try:
                self._create_database_dump(db_backup_dir)
            except Exception as e:
                logger.warning(f"创建数据库转储失败: {e}")
        
        return db_backup_dir
    
    def _create_database_dump(self, backup_dir: Path) -> None:
        """
        创建数据库转储
        
        Args:
            backup_dir: 备份目录
        """
        try:
            from db import database
            from db.models import Device, Config, CommandLog
            
            # 导出设备数据
            devices_data = []
            for device in Device.select():
                devices_data.append({
                    'device_uuid': device.device_uuid,
                    'device_name': device.device_name,
                    'device_type': device.device_type,
                    'vendor_id': device.vendor_id,
                    'product_id': device.product_id,
                    'serial_number': device.serial_number,
                    'status': device.status,
                    'server_name': device.server_name,
                    'created_at': device.created_at.isoformat() if device.created_at else None,
                    'updated_at': device.updated_at.isoformat() if device.updated_at else None
                })
            
            # 导出配置数据
            configs_data = []
            for config in Config.select():
                configs_data.append({
                    'key': config.key,
                    'value': config.value,
                    'description': config.description,
                    'created_at': config.created_at.isoformat() if config.created_at else None,
                    'updated_at': config.updated_at.isoformat() if config.updated_at else None
                })
            
            # 导出命令日志数据（最近1000条）
            command_logs_data = []
            for log in CommandLog.select().order_by(CommandLog.executed_at.desc()).limit(1000):
                command_logs_data.append({
                    'command_type': log.command_type,
                    'command_params': log.command_params,
                    'execution_result': log.execution_result,
                    'success': log.success,
                    'error_message': log.error_message,
                    'executed_at': log.executed_at.isoformat() if log.executed_at else None
                })
            
            # 保存数据转储
            dump_data = {
                'export_timestamp': datetime.now().isoformat(),
                'devices': devices_data,
                'configs': configs_data,
                'command_logs': command_logs_data
            }
            
            dump_file = backup_dir / 'database_dump.json'
            with open(dump_file, 'w', encoding='utf-8') as f:
                json.dump(dump_data, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"创建数据库转储失败: {e}")
            raise
    
    def _backup_configuration(self, backup_path: Path) -> Path:
        """
        备份配置文件
        
        Args:
            backup_path: 备份目录
            
        Returns:
            配置备份目录路径
        """
        config_backup_dir = backup_path / 'configuration'
        config_backup_dir.mkdir(exist_ok=True)
        
        # 备份配置目录
        config_dir = Path('config')
        if config_dir.exists():
            for config_file in config_dir.glob('*.ini*'):
                shutil.copy2(config_file, config_backup_dir)
        
        # 备份环境变量文件
        env_file = Path('.env')
        if env_file.exists():
            shutil.copy2(env_file, config_backup_dir)
        
        return config_backup_dir
    
    def _backup_logs(self, backup_path: Path) -> Path:
        """
        备份日志文件
        
        Args:
            backup_path: 备份目录
            
        Returns:
            日志备份目录路径
        """
        log_backup_dir = backup_path / 'logs'
        log_backup_dir.mkdir(exist_ok=True)
        
        # 备份最近的日志文件
        logs_dir = Path('logs')
        if logs_dir.exists():
            # 只备份最近7天的日志
            cutoff_time = time.time() - (7 * 24 * 3600)
            
            for log_file in logs_dir.glob('*.log*'):
                if log_file.stat().st_mtime > cutoff_time:
                    shutil.copy2(log_file, log_backup_dir)
        
        return log_backup_dir
    
    def _compress_backup(self, backup_path: Path) -> Path:
        """
        压缩备份目录
        
        Args:
            backup_path: 备份目录
            
        Returns:
            压缩文件路径
        """
        compressed_file = backup_path.with_suffix('.tar.gz')
        
        import tarfile
        with tarfile.open(compressed_file, 'w:gz') as tar:
            tar.add(backup_path, arcname=backup_path.name)
        
        return compressed_file
    
    def _calculate_directory_size(self, directory: Path) -> int:
        """
        计算目录大小
        
        Args:
            directory: 目录路径
            
        Returns:
            目录大小（字节）
        """
        total_size = 0
        for file_path in directory.rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        return total_size
    
    def cleanup_old_backups(self) -> int:
        """
        清理旧备份
        
        Returns:
            清理的备份数量
        """
        try:
            # 获取所有备份文件
            backup_files = []
            for item in self.backup_dir.iterdir():
                if item.name.startswith('full_backup_'):
                    backup_files.append(item)
            
            # 按修改时间排序
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 删除超出限制的备份
            deleted_count = 0
            for backup_file in backup_files[self.max_backups:]:
                try:
                    if backup_file.is_dir():
                        shutil.rmtree(backup_file)
                    else:
                        backup_file.unlink()
                    deleted_count += 1
                    logger.info(f"删除旧备份: {backup_file}")
                except Exception as e:
                    logger.error(f"删除备份失败 {backup_file}: {e}")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理旧备份失败: {e}")
            return 0
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """
        列出所有备份
        
        Returns:
            备份列表
        """
        backups = []
        
        try:
            for item in self.backup_dir.iterdir():
                if item.name.startswith('full_backup_'):
                    backup_info = {
                        'name': item.name,
                        'path': str(item),
                        'size': self._calculate_directory_size(item) if item.is_dir() else item.stat().st_size,
                        'created_at': datetime.fromtimestamp(item.stat().st_mtime).isoformat(),
                        'type': 'directory' if item.is_dir() else 'compressed'
                    }
                    
                    # 尝试读取元数据
                    metadata_file = item / 'backup_metadata.json' if item.is_dir() else None
                    if metadata_file and metadata_file.exists():
                        try:
                            with open(metadata_file, 'r', encoding='utf-8') as f:
                                metadata = json.load(f)
                                backup_info.update(metadata)
                        except Exception:
                            pass
                    
                    backups.append(backup_info)
            
            # 按创建时间排序
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            
        except Exception as e:
            logger.error(f"列出备份失败: {e}")
        
        return backups
    
    def restore_backup(self, backup_name: str) -> bool:
        """
        恢复备份
        
        Args:
            backup_name: 备份名称
            
        Returns:
            是否恢复成功
        """
        try:
            backup_path = self.backup_dir / backup_name
            
            if not backup_path.exists():
                logger.error(f"备份不存在: {backup_name}")
                return False
            
            # 如果是压缩文件，先解压
            if backup_path.is_file() and backup_path.suffix == '.gz':
                extracted_path = self._extract_backup(backup_path)
                backup_path = extracted_path
            
            # 恢复数据库
            db_backup_dir = backup_path / 'database'
            if db_backup_dir.exists():
                self._restore_database(db_backup_dir)
                logger.info("数据库恢复完成")
            
            # 恢复配置
            config_backup_dir = backup_path / 'configuration'
            if config_backup_dir.exists():
                self._restore_configuration(config_backup_dir)
                logger.info("配置恢复完成")
            
            logger.info(f"备份恢复成功: {backup_name}")
            return True
            
        except Exception as e:
            logger.error(f"恢复备份失败: {e}")
            return False
    
    def _extract_backup(self, compressed_file: Path) -> Path:
        """
        解压备份文件
        
        Args:
            compressed_file: 压缩文件路径
            
        Returns:
            解压后的目录路径
        """
        import tarfile
        extract_dir = compressed_file.parent / compressed_file.stem.replace('.tar', '')
        
        with tarfile.open(compressed_file, 'r:gz') as tar:
            tar.extractall(compressed_file.parent)
        
        return extract_dir
    
    def _restore_database(self, db_backup_dir: Path) -> None:
        """
        恢复数据库
        
        Args:
            db_backup_dir: 数据库备份目录
        """
        # 恢复数据库文件
        backup_db_file = db_backup_dir / 'slave_server.db'
        if backup_db_file.exists():
            target_db_file = Path('data/slave_server.db')
            target_db_file.parent.mkdir(exist_ok=True)
            shutil.copy2(backup_db_file, target_db_file)
    
    def _restore_configuration(self, config_backup_dir: Path) -> None:
        """
        恢复配置文件
        
        Args:
            config_backup_dir: 配置备份目录
        """
        config_dir = Path('config')
        config_dir.mkdir(exist_ok=True)
        
        for config_file in config_backup_dir.glob('*.ini*'):
            shutil.copy2(config_file, config_dir)
        
        # 恢复环境变量文件
        env_backup = config_backup_dir / '.env'
        if env_backup.exists():
            shutil.copy2(env_backup, Path('.env'))


# 全局备份管理器实例
backup_manager = BackupManager()


def get_backup_manager() -> BackupManager:
    """获取备份管理器实例"""
    return backup_manager


def start_auto_backup() -> None:
    """启动自动备份"""
    backup_manager.start_auto_backup()


def stop_auto_backup() -> None:
    """停止自动备份"""
    backup_manager.stop_auto_backup()


def create_backup() -> str:
    """创建备份"""
    return backup_manager.create_full_backup()


def list_backups() -> List[Dict[str, Any]]:
    """列出备份"""
    return backup_manager.list_backups()


def restore_backup(backup_name: str) -> bool:
    """恢复备份"""
    return backup_manager.restore_backup(backup_name)
