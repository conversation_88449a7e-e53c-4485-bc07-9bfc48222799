# -*- coding: utf-8 -*-
"""
安全管理模块
提供认证、授权、加密、安全审计功能
"""

import os
import jwt
import hashlib
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
from functools import wraps
from flask import request, jsonify, g
from utils.logger import get_logger
from utils.cache_manager import get_cache_manager

logger = get_logger('security_manager')
cache_manager = get_cache_manager()


class SecurityManager:
    """安全管理器"""
    
    def __init__(self, secret_key: Optional[str] = None) -> None:
        """
        初始化安全管理器
        
        Args:
            secret_key: JWT密钥
        """
        self.secret_key = secret_key or os.environ.get('SECRET_KEY') or secrets.token_hex(32)
        self.algorithm = 'HS256'
        self.token_expiry = 3600  # 1小时
        self.refresh_token_expiry = 86400 * 7  # 7天
        
        # 安全配置
        self.max_login_attempts = 5
        self.lockout_duration = 300  # 5分钟
        self.password_min_length = 8
        
    def generate_token(self, user_id: str, user_data: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
        """
        生成访问令牌和刷新令牌
        
        Args:
            user_id: 用户ID
            user_data: 用户数据
            
        Returns:
            包含访问令牌和刷新令牌的字典
        """
        now = datetime.utcnow()
        
        # 访问令牌
        access_payload = {
            'user_id': user_id,
            'user_data': user_data or {},
            'iat': now,
            'exp': now + timedelta(seconds=self.token_expiry),
            'type': 'access'
        }
        access_token = jwt.encode(access_payload, self.secret_key, algorithm=self.algorithm)
        
        # 刷新令牌
        refresh_payload = {
            'user_id': user_id,
            'iat': now,
            'exp': now + timedelta(seconds=self.refresh_token_expiry),
            'type': 'refresh'
        }
        refresh_token = jwt.encode(refresh_payload, self.secret_key, algorithm=self.algorithm)
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'expires_in': self.token_expiry,
            'token_type': 'Bearer'
        }
    
    def verify_token(self, token: str, token_type: str = 'access') -> Optional[Dict[str, Any]]:
        """
        验证令牌
        
        Args:
            token: JWT令牌
            token_type: 令牌类型（access/refresh）
            
        Returns:
            解码后的令牌数据或None
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # 检查令牌类型
            if payload.get('type') != token_type:
                return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("令牌已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"无效令牌: {e}")
            return None
    
    def refresh_access_token(self, refresh_token: str) -> Optional[Dict[str, str]]:
        """
        刷新访问令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            新的令牌对或None
        """
        payload = self.verify_token(refresh_token, token_type='refresh')
        if not payload:
            return None
        
        user_id = payload.get('user_id')
        if not user_id:
            return None
        
        # 生成新的令牌对
        return self.generate_token(user_id)
    
    def hash_password(self, password: str, salt: Optional[str] = None) -> Tuple[str, str]:
        """
        哈希密码
        
        Args:
            password: 明文密码
            salt: 盐值（可选）
            
        Returns:
            (哈希值, 盐值)
        """
        if salt is None:
            salt = secrets.token_hex(16)
        
        # 使用PBKDF2进行哈希
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 迭代次数
        )
        
        return password_hash.hex(), salt
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """
        验证密码
        
        Args:
            password: 明文密码
            password_hash: 存储的哈希值
            salt: 盐值
            
        Returns:
            密码是否正确
        """
        computed_hash, _ = self.hash_password(password, salt)
        return secrets.compare_digest(computed_hash, password_hash)
    
    def validate_password_strength(self, password: str) -> Tuple[bool, List[str]]:
        """
        验证密码强度
        
        Args:
            password: 密码
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        if len(password) < self.password_min_length:
            errors.append(f"密码长度至少{self.password_min_length}位")
        
        if not any(c.isupper() for c in password):
            errors.append("密码必须包含大写字母")
        
        if not any(c.islower() for c in password):
            errors.append("密码必须包含小写字母")
        
        if not any(c.isdigit() for c in password):
            errors.append("密码必须包含数字")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            errors.append("密码必须包含特殊字符")
        
        return len(errors) == 0, errors
    
    def check_login_attempts(self, identifier: str) -> bool:
        """
        检查登录尝试次数
        
        Args:
            identifier: 用户标识（IP地址或用户名）
            
        Returns:
            是否允许登录
        """
        cache_key = f"login_attempts:{identifier}"
        attempts_data = cache_manager.get(cache_key)
        
        if not attempts_data:
            return True
        
        attempts = attempts_data.get('count', 0)
        last_attempt = attempts_data.get('last_attempt', 0)
        
        # 检查是否在锁定期内
        if attempts >= self.max_login_attempts:
            if time.time() - last_attempt < self.lockout_duration:
                return False
            else:
                # 锁定期已过，重置计数
                cache_manager.delete(cache_key)
                return True
        
        return True
    
    def record_login_attempt(self, identifier: str, success: bool) -> None:
        """
        记录登录尝试
        
        Args:
            identifier: 用户标识
            success: 是否成功
        """
        cache_key = f"login_attempts:{identifier}"
        
        if success:
            # 登录成功，清除记录
            cache_manager.delete(cache_key)
        else:
            # 登录失败，增加计数
            attempts_data = cache_manager.get(cache_key) or {'count': 0, 'last_attempt': 0}
            attempts_data['count'] += 1
            attempts_data['last_attempt'] = time.time()
            
            # 缓存到锁定期结束
            cache_manager.set(cache_key, attempts_data, ttl=self.lockout_duration)
    
    def generate_api_key(self, user_id: str, name: str = "default") -> str:
        """
        生成API密钥
        
        Args:
            user_id: 用户ID
            name: 密钥名称
            
        Returns:
            API密钥
        """
        timestamp = str(int(time.time()))
        data = f"{user_id}:{name}:{timestamp}"
        api_key = hashlib.sha256(data.encode()).hexdigest()
        
        # 存储API密钥信息
        cache_key = f"api_key:{api_key}"
        key_data = {
            'user_id': user_id,
            'name': name,
            'created_at': timestamp,
            'last_used': None
        }
        cache_manager.set(cache_key, key_data, ttl=86400 * 365)  # 1年有效期
        
        return api_key
    
    def verify_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """
        验证API密钥
        
        Args:
            api_key: API密钥
            
        Returns:
            密钥信息或None
        """
        cache_key = f"api_key:{api_key}"
        key_data = cache_manager.get(cache_key)
        
        if key_data:
            # 更新最后使用时间
            key_data['last_used'] = str(int(time.time()))
            cache_manager.set(cache_key, key_data, ttl=86400 * 365)
            
        return key_data
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """
        加密敏感数据
        
        Args:
            data: 明文数据
            
        Returns:
            加密后的数据
        """
        # 简单的加密实现，生产环境应使用更强的加密
        import base64
        encoded = base64.b64encode(data.encode('utf-8')).decode('utf-8')
        return encoded
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """
        解密敏感数据
        
        Args:
            encrypted_data: 加密数据
            
        Returns:
            明文数据
        """
        # 简单的解密实现
        import base64
        try:
            decoded = base64.b64decode(encrypted_data.encode('utf-8')).decode('utf-8')
            return decoded
        except Exception:
            return ""
    
    def audit_log(self, user_id: str, action: str, resource: str, 
                 details: Optional[Dict[str, Any]] = None) -> None:
        """
        记录安全审计日志
        
        Args:
            user_id: 用户ID
            action: 操作类型
            resource: 资源
            details: 详细信息
        """
        audit_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'user_id': user_id,
            'action': action,
            'resource': resource,
            'details': details or {},
            'ip_address': request.remote_addr if request else None,
            'user_agent': request.headers.get('User-Agent') if request else None
        }
        
        # 记录到日志
        logger.info(f"安全审计: {audit_entry}")
        
        # 可以扩展到数据库存储
        # audit_dao.create_audit_log(audit_entry)


# 全局安全管理器实例
security_manager = SecurityManager()


def get_security_manager() -> SecurityManager:
    """获取安全管理器实例"""
    return security_manager


def require_auth(func):
    """
    认证装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 检查Authorization头
        auth_header = request.headers.get('Authorization')
        
        if not auth_header:
            return jsonify({
                'success': False,
                'message': '缺少认证信息'
            }), 401
        
        # 解析Bearer Token
        try:
            scheme, token = auth_header.split(' ', 1)
            if scheme.lower() != 'bearer':
                raise ValueError("Invalid scheme")
        except ValueError:
            return jsonify({
                'success': False,
                'message': '认证格式错误'
            }), 401
        
        # 验证令牌
        payload = security_manager.verify_token(token)
        if not payload:
            return jsonify({
                'success': False,
                'message': '无效的认证令牌'
            }), 401
        
        # 将用户信息存储到请求上下文
        g.current_user = payload
        
        return func(*args, **kwargs)
    
    return wrapper


def require_api_key(func):
    """
    API密钥认证装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 检查API密钥
        api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
        
        if not api_key:
            return jsonify({
                'success': False,
                'message': '缺少API密钥'
            }), 401
        
        # 验证API密钥
        key_data = security_manager.verify_api_key(api_key)
        if not key_data:
            return jsonify({
                'success': False,
                'message': '无效的API密钥'
            }), 401
        
        # 将密钥信息存储到请求上下文
        g.api_key_data = key_data
        
        return func(*args, **kwargs)
    
    return wrapper
