# -*- coding: utf-8 -*-
"""
日志管理模块
提供统一的日志配置和管理功能
"""

import os
import json
import logging
import logging.handlers
import threading
from datetime import datetime
from typing import Dict, Any, Optional, Union, List, Tuple
from enum import Enum


class LogFormat(Enum):
    """日志格式类型"""
    STANDARD = "standard"      # 标准格式
    STRUCTURED = "structured"  # 结构化JSON格式
    DETAILED = "detailed"      # 详细格式


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""

    def __init__(self, include_extra: bool = True) -> None:
        super().__init__()
        self.include_extra = include_extra

    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
            'process': record.process
        }

        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)

        # 添加额外字段
        if self.include_extra:
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                              'filename', 'module', 'lineno', 'funcName', 'created',
                              'msecs', 'relativeCreated', 'thread', 'threadName',
                              'processName', 'process', 'getMessage', 'exc_info',
                              'exc_text', 'stack_info']:
                    log_data['extra'] = log_data.get('extra', {})
                    log_data['extra'][key] = value

        return json.dumps(log_data, ensure_ascii=False, default=str)


class DetailedFormatter(logging.Formatter):
    """详细日志格式化器"""

    def __init__(self) -> None:
        super().__init__(
            fmt='%(asctime)s | %(levelname)-8s | %(name)-20s | %(module)-15s:%(lineno)-4d | '
                '%(funcName)-20s | %(threadName)-10s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )


class LogManager:
    """日志管理器"""

    def __init__(self) -> None:
        self.loggers: Dict[str, logging.Logger] = {}
        self.lock = threading.Lock()
        self.default_config = {
            'level': logging.INFO,
            'format': LogFormat.STANDARD,
            'max_bytes': 10 * 1024 * 1024,  # 10MB
            'backup_count': 5,
            'console_output': True,
            'file_output': True
        }

    def get_logger(self, name: str, config: Optional[Dict[str, Any]] = None) -> logging.Logger:
        """
        获取或创建日志记录器

        Args:
            name: 日志记录器名称
            config: 日志配置

        Returns:
            logging.Logger: 配置好的日志记录器
        """
        with self.lock:
            if name in self.loggers:
                return self.loggers[name]

            # 合并配置
            effective_config = self.default_config.copy()
            if config:
                effective_config.update(config)

            # 创建日志记录器
            logger = self._create_logger(name, effective_config)
            self.loggers[name] = logger

            return logger

    def _create_logger(self, name: str, config: Dict[str, Any]) -> logging.Logger:
        """创建日志记录器"""
        logger = logging.getLogger(name)
        logger.setLevel(config['level'])

        # 清除现有处理器
        logger.handlers.clear()

        # 创建格式化器
        formatter = self._create_formatter(config['format'])

        # 添加控制台处理器
        if config['console_output']:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(config['level'])
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        # 添加文件处理器
        if config['file_output']:
            file_handler = self._create_file_handler(name, config)
            file_handler.setLevel(config['level'])
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        return logger

    def _create_formatter(self, format_type: LogFormat) -> logging.Formatter:
        """创建格式化器"""
        if format_type == LogFormat.STRUCTURED:
            return StructuredFormatter()
        elif format_type == LogFormat.DETAILED:
            return DetailedFormatter()
        else:
            return logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )

    def _create_file_handler(self, name: str, config: Dict[str, Any]) -> logging.Handler:
        """创建文件处理器"""
        # 确定日志文件路径
        log_dir = config.get('log_dir', 'logs')
        os.makedirs(log_dir, exist_ok=True)

        log_file = os.path.join(log_dir, f'{name}.log')

        # 创建轮转文件处理器
        handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=config['max_bytes'],
            backupCount=config['backup_count'],
            encoding='utf-8'
        )

        return handler

    def update_logger_config(self, name: str, config: Dict[str, Any]) -> Any:
        """更新日志记录器配置"""
        with self.lock:
            if name in self.loggers:
                # 重新创建日志记录器
                effective_config = self.default_config.copy()
                effective_config.update(config)

                new_logger = self._create_logger(name, effective_config)
                self.loggers[name] = new_logger

    def set_default_config(self, config: Dict[str, Any]) -> Any:
        """设置默认配置"""
        self.default_config.update(config)

    def get_logger_info(self) -> Dict[str, Any]:
        """获取日志记录器信息"""
        with self.lock:
            logger_info = {}

            for name, logger in self.loggers.items():
                handlers_info = []
                for handler in logger.handlers:
                    handler_info = {
                        'type': type(handler).__name__,
                        'level': logging.getLevelName(handler.level),
                        'formatter': type(handler.formatter).__name__ if handler.formatter else None
                    }

                    # 添加文件处理器特定信息
                    if isinstance(handler, logging.handlers.RotatingFileHandler):
                        handler_info.update({
                            'filename': handler.baseFilename,
                            'max_bytes': handler.maxBytes,
                            'backup_count': handler.backupCount
                        })

                    handlers_info.append(handler_info)

                logger_info[name] = {
                    'level': logging.getLevelName(logger.level),
                    'handlers': handlers_info,
                    'effective_level': logging.getLevelName(logger.getEffectiveLevel())
                }

            return logger_info


# 全局日志管理器
global_log_manager = LogManager()


def setup_logger(name, log_file=None, level=logging.INFO) -> Any:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径，如果为None则使用默认路径
        level: 日志级别
    
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file is None:
        log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f'{name}.log')
    
    # 使用RotatingFileHandler实现日志轮转
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str, config: Optional[Dict[str, Any]] = None) -> logging.Logger:
    """
    获取已配置的日志记录器（使用新的日志管理器）

    Args:
        name: 日志记录器名称
        config: 日志配置（可选）

    Returns:
        logging.Logger: 日志记录器
    """
    return global_log_manager.get_logger(name, config)


def get_structured_logger(name: str, **kwargs) -> logging.Logger:
    """
    获取结构化日志记录器

    Args:
        name: 日志记录器名称
        **kwargs: 其他配置参数

    Returns:
        logging.Logger: 结构化日志记录器
    """
    config = {'format': LogFormat.STRUCTURED}
    config.update(kwargs)
    return global_log_manager.get_logger(name, config)


def get_detailed_logger(name: str, **kwargs) -> logging.Logger:
    """
    获取详细日志记录器

    Args:
        name: 日志记录器名称
        **kwargs: 其他配置参数

    Returns:
        logging.Logger: 详细日志记录器
    """
    config = {'format': LogFormat.DETAILED}
    config.update(kwargs)
    return global_log_manager.get_logger(name, config)


def configure_logging_from_env() -> None:
    """从环境变量配置日志"""
    try:
        from utils.env_config import global_env_config

        # 获取日志配置
        log_level_str = global_env_config.get_value('logging.level', 'INFO')
        log_file = global_env_config.get_value('logging.file', 'logs/slave_server.log')
        max_size = global_env_config.get_value('logging.max_size', 10485760)
        backup_count = global_env_config.get_value('logging.backup_count', 5)

        # 转换日志级别
        log_level = getattr(logging, log_level_str.upper(), logging.INFO)

        # 设置默认配置
        config = {
            'level': log_level,
            'max_bytes': max_size,
            'backup_count': backup_count,
            'log_dir': os.path.dirname(log_file)
        }

        global_log_manager.set_default_config(config)

    except Exception as e:
        # 如果配置失败，使用默认配置
        # 在日志系统初始化阶段，使用标准错误输出
        import sys
        sys.stderr.write(f"从环境变量配置日志失败: {e}\n")


def add_structured_fields(logger: logging.Logger, **fields) -> Any:
    """
    为日志记录器添加结构化字段

    Args:
        logger: 日志记录器
        **fields: 要添加的字段
    """
    # 创建一个适配器来添加额外字段
    class StructuredAdapter(logging.LoggerAdapter):
        def process(self, msg, kwargs) -> Any:
            # 添加额外字段到extra中
            if 'extra' not in kwargs:
                kwargs['extra'] = {}
            kwargs['extra'].update(self.extra)
            return msg, kwargs

    return StructuredAdapter(logger, fields)


def setup_application_logger() -> None:
    """
    设置应用程序日志记录器
    从配置文件读取日志配置
    """
    try:
        # 导入配置管理器
        from utils.config_manager import ConfigManager

        config_manager = ConfigManager()
        log_config = config_manager.get_logging_config()

        # 获取日志配置
        log_level = getattr(logging, log_config.get('level', 'INFO').upper())
        log_file = log_config.get('file', 'logs/slave_server.log')
        max_size = log_config.get('max_size', 10*1024*1024)
        backup_count = log_config.get('backup_count', 5)

        # 设置根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)

        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

        # 文件处理器
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)

        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

        # 记录日志系统启动信息
        logger = logging.getLogger('logger')
        logger.info(f"日志系统初始化完成 - 级别: {log_config.get('level', 'INFO')}, 文件: {log_file}")

    except Exception as e:
        # 如果配置加载失败，使用默认配置
        import sys
        sys.stderr.write(f"日志配置加载失败，使用默认配置: {e}\n")
        setup_logger('default')


def cleanup_old_logs(log_dir='logs', days=30) -> Any:
    """
    清理旧日志文件

    Args:
        log_dir: 日志目录
        days: 保留天数
    """
    try:
        import glob
        from datetime import datetime, timedelta

        if not os.path.exists(log_dir):
            return

        cutoff_time = datetime.now() - timedelta(days=days)

        # 查找所有日志文件
        log_files = glob.glob(os.path.join(log_dir, '*.log*'))

        cleaned_count = 0
        for log_file in log_files:
            try:
                file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                if file_time < cutoff_time:
                    os.remove(log_file)
                    cleaned_count += 1
            except Exception as e:
                # 使用标准错误输出，避免循环日志问题
                import sys
                sys.stderr.write(f"清理日志文件失败 {log_file}: {e}\n")

        if cleaned_count > 0:
            logger = logging.getLogger('logger')
            logger.info(f"清理旧日志文件完成，删除 {cleaned_count} 个文件")

    except Exception as e:
        # 使用标准错误输出，避免循环日志问题
        import sys
        sys.stderr.write(f"清理旧日志文件异常: {e}\n")


def get_log_statistics(log_dir='logs') -> Any:
    """
    获取日志统计信息

    Args:
        log_dir: 日志目录

    Returns:
        dict: 日志统计信息
    """
    try:
        import glob

        if not os.path.exists(log_dir):
            return {'total_files': 0, 'total_size': 0}

        log_files = glob.glob(os.path.join(log_dir, '*.log*'))
        total_size = sum(os.path.getsize(f) for f in log_files if os.path.exists(f))

        return {
            'total_files': len(log_files),
            'total_size': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2)
        }

    except Exception as e:
        # 使用标准错误输出，避免循环日志问题
        import sys
        sys.stderr.write(f"获取日志统计失败: {e}\n")
        return {'total_files': 0, 'total_size': 0}
