# -*- coding: utf-8 -*-
"""
设备事件处理模块
负责处理设备状态变化事件和事件分发
"""

import time
import threading
from enum import Enum
from typing import Dict, List, Callable, Any, Optional, Union, Tuple
from dataclasses import dataclass
from utils.logger import get_logger

logger = get_logger('device_event_handler')


class DeviceEventType(Enum):
    """设备事件类型"""
    DEVICE_CONNECTED = "device_connected"
    DEVICE_DISCONNECTED = "device_disconnected"
    DEVICE_STATUS_CHANGED = "device_status_changed"
    DEVICE_ERROR = "device_error"
    DEVICE_USED = "device_used"
    DEVICE_RELEASED = "device_released"


@dataclass
class DeviceEvent:
    """设备事件数据类"""
    event_type: DeviceEventType
    device_uuid: str
    device_address: str
    device_info: Dict[str, Any]
    timestamp: float
    old_status: Optional[str] = None
    new_status: Optional[str] = None
    error_message: Optional[str] = None
    user_info: Optional[Dict] = None


class DeviceEventHandler:
    """设备事件处理器"""
    
    def __init__(self) -> None:
        """初始化设备事件处理器"""
        self.event_listeners = {}  # 事件监听器
        self.device_states = {}    # 设备状态缓存
        self.event_queue = []      # 事件队列
        self.processing = False    # 是否正在处理事件
        self.lock = threading.RLock()
        self.max_queue_size = 1000
        self.event_history = []    # 事件历史
        self.max_history_size = 500
    
    def register_listener(self, event_type: DeviceEventType, callback: Callable[[DeviceEvent], None]) -> Any:
        """
        注册事件监听器
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        try:
            with self.lock:
                if event_type not in self.event_listeners:
                    self.event_listeners[event_type] = []
                
                self.event_listeners[event_type].append(callback)
                logger.info(f"注册事件监听器: {event_type.value}")
                
        except Exception as e:
            logger.error(f"注册事件监听器失败: {e}")
    
    def unregister_listener(self, event_type: DeviceEventType, callback: Callable[[DeviceEvent], None]) -> Any:
        """
        取消注册事件监听器
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        try:
            with self.lock:
                if event_type in self.event_listeners:
                    if callback in self.event_listeners[event_type]:
                        self.event_listeners[event_type].remove(callback)
                        logger.info(f"取消注册事件监听器: {event_type.value}")
                        
        except Exception as e:
            logger.error(f"取消注册事件监听器失败: {e}")
    
    def emit_event(self, event: DeviceEvent) -> Any:
        """
        发出事件
        
        Args:
            event: 设备事件
        """
        try:
            with self.lock:
                # 添加到事件队列
                if len(self.event_queue) >= self.max_queue_size:
                    # 队列满了，移除最旧的事件
                    self.event_queue.pop(0)
                    logger.warning("事件队列已满，移除最旧事件")
                
                self.event_queue.append(event)
                
                # 添加到历史记录
                self._add_to_history(event)
                
                # 立即处理事件
                self._process_event(event)
                
        except Exception as e:
            logger.error(f"发出事件失败: {e}")
    
    def _process_event(self, event: DeviceEvent) -> Any:
        """
        处理单个事件
        
        Args:
            event: 设备事件
        """
        try:
            # 更新设备状态
            self._update_device_state(event)
            
            # 调用事件监听器
            if event.event_type in self.event_listeners:
                for callback in self.event_listeners[event.event_type]:
                    try:
                        callback(event)
                    except Exception as e:
                        logger.error(f"事件监听器回调失败: {e}")
            
            logger.debug(f"处理事件: {event.event_type.value} - {event.device_address}")
            
        except Exception as e:
            logger.error(f"处理事件失败: {e}")
    
    def _update_device_state(self, event: DeviceEvent) -> Any:
        """
        更新设备状态
        
        Args:
            event: 设备事件
        """
        try:
            device_uuid = event.device_uuid
            
            if event.event_type == DeviceEventType.DEVICE_CONNECTED:
                self.device_states[device_uuid] = {
                    'status': 'available',
                    'last_seen': event.timestamp,
                    'device_info': event.device_info
                }
            elif event.event_type == DeviceEventType.DEVICE_DISCONNECTED:
                if device_uuid in self.device_states:
                    del self.device_states[device_uuid]
            elif event.event_type == DeviceEventType.DEVICE_STATUS_CHANGED:
                if device_uuid in self.device_states:
                    self.device_states[device_uuid]['status'] = event.new_status
                    self.device_states[device_uuid]['last_seen'] = event.timestamp
            elif event.event_type == DeviceEventType.DEVICE_USED:
                if device_uuid in self.device_states:
                    self.device_states[device_uuid]['status'] = 'in_use'
                    self.device_states[device_uuid]['last_seen'] = event.timestamp
                    self.device_states[device_uuid]['user_info'] = event.user_info
            elif event.event_type == DeviceEventType.DEVICE_RELEASED:
                if device_uuid in self.device_states:
                    self.device_states[device_uuid]['status'] = 'available'
                    self.device_states[device_uuid]['last_seen'] = event.timestamp
                    if 'user_info' in self.device_states[device_uuid]:
                        del self.device_states[device_uuid]['user_info']
            
        except Exception as e:
            logger.error(f"更新设备状态失败: {e}")
    
    def _add_to_history(self, event: DeviceEvent) -> Any:
        """
        添加事件到历史记录
        
        Args:
            event: 设备事件
        """
        try:
            if len(self.event_history) >= self.max_history_size:
                self.event_history.pop(0)
            
            self.event_history.append({
                'event_type': event.event_type.value,
                'device_uuid': event.device_uuid,
                'device_address': event.device_address,
                'timestamp': event.timestamp,
                'old_status': event.old_status,
                'new_status': event.new_status,
                'error_message': event.error_message
            })
            
        except Exception as e:
            logger.error(f"添加事件历史失败: {e}")
    
    def detect_device_changes(self, current_devices: List[Dict], previous_devices: List[Dict]) -> Any:
        """
        检测设备变化并生成事件
        
        Args:
            current_devices: 当前设备列表
            previous_devices: 之前的设备列表
        """
        try:
            current_uuids = {device['device_uuid']: device for device in current_devices}
            previous_uuids = {device['device_uuid']: device for device in previous_devices}
            
            # 检测新连接的设备
            new_devices = set(current_uuids.keys()) - set(previous_uuids.keys())
            for device_uuid in new_devices:
                device = current_uuids[device_uuid]
                event = DeviceEvent(
                    event_type=DeviceEventType.DEVICE_CONNECTED,
                    device_uuid=device_uuid,
                    device_address=device['device_address'],
                    device_info=device,
                    timestamp=time.time(),
                    new_status='available'
                )
                self.emit_event(event)
            
            # 检测断开连接的设备
            removed_devices = set(previous_uuids.keys()) - set(current_uuids.keys())
            for device_uuid in removed_devices:
                device = previous_uuids[device_uuid]
                event = DeviceEvent(
                    event_type=DeviceEventType.DEVICE_DISCONNECTED,
                    device_uuid=device_uuid,
                    device_address=device['device_address'],
                    device_info=device,
                    timestamp=time.time(),
                    old_status=device.get('status', 'unknown')
                )
                self.emit_event(event)
            
            # 检测状态变化的设备
            for device_uuid in current_uuids.keys() & previous_uuids.keys():
                current_device = current_uuids[device_uuid]
                previous_device = previous_uuids[device_uuid]
                
                current_status = current_device.get('status', 'available')
                previous_status = previous_device.get('status', 'available')
                
                if current_status != previous_status:
                    event = DeviceEvent(
                        event_type=DeviceEventType.DEVICE_STATUS_CHANGED,
                        device_uuid=device_uuid,
                        device_address=current_device['device_address'],
                        device_info=current_device,
                        timestamp=time.time(),
                        old_status=previous_status,
                        new_status=current_status
                    )
                    self.emit_event(event)
            
        except Exception as e:
            logger.error(f"检测设备变化失败: {e}")
    
    def get_device_state(self, device_uuid: str) -> Optional[Dict]:
        """
        获取设备状态
        
        Args:
            device_uuid: 设备UUID
        
        Returns:
            Dict: 设备状态，未找到返回None
        """
        with self.lock:
            return self.device_states.get(device_uuid)
    
    def get_all_device_states(self) -> Dict[str, Dict]:
        """
        获取所有设备状态
        
        Returns:
            Dict: 所有设备状态
        """
        with self.lock:
            return self.device_states.copy()
    
    def get_event_history(self, limit: int = 100) -> List[Dict]:
        """
        获取事件历史
        
        Args:
            limit: 返回的事件数量限制
        
        Returns:
            List[Dict]: 事件历史列表
        """
        with self.lock:
            return self.event_history[-limit:] if limit > 0 else self.event_history.copy()
    
    def clear_event_history(self) -> None:
        """清除事件历史"""
        with self.lock:
            self.event_history.clear()
            logger.info("事件历史已清除")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取事件统计信息
        
        Returns:
            Dict: 统计信息
        """
        with self.lock:
            stats = {
                'total_devices': len(self.device_states),
                'total_events': len(self.event_history),
                'queue_size': len(self.event_queue),
                'listener_count': sum(len(listeners) for listeners in self.event_listeners.values()),
                'event_types': {}
            }
            
            # 统计各类型事件数量
            for event in self.event_history:
                event_type = event['event_type']
                stats['event_types'][event_type] = stats['event_types'].get(event_type, 0) + 1
            
            return stats
