# -*- coding: utf-8 -*-
"""
增强的VirtualHere客户端
基于VirtualHere官方API文档实现的高级客户端功能
参考: https://www.virtualhere.com/client_api
"""

import os
import time
import json
import threading
from typing import Dict, List, Any, Optional, Callable
from utils.logger import get_logger
from utils.vh_client import VirtualHereClient

logger = get_logger('vh_client_enhanced')


class VirtualHereEnhancedClient(VirtualHereClient):
    """增强的VirtualHere客户端"""
    
    def __init__(self, pipe_path: str = None) -> Any:
        """
        初始化增强客户端
        
        Args:
            pipe_path: 命名管道路径
        """
        super().__init__(pipe_path)
        self.event_callbacks = {}
        self.monitoring_active = False
        self.monitor_thread = None
        
    def register_event_callback(self, event_type: str, callback: Callable) -> Any:
        """
        注册事件回调函数
        
        Args:
            event_type: 事件类型 (DEVICE_ATTACHED, DEVICE_DETACHED, etc.)
            callback: 回调函数
        """
        if event_type not in self.event_callbacks:
            self.event_callbacks[event_type] = []
        self.event_callbacks[event_type].append(callback)
        logger.info(f"注册事件回调: {event_type}")
    
    def start_event_monitoring(self) -> None:
        """启动事件监控"""
        if self.monitoring_active:
            logger.warning("事件监控已在运行")
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._event_monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("事件监控已启动")
    
    def stop_event_monitoring(self) -> None:
        """停止事件监控"""
        self.monitoring_active = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        logger.info("事件监控已停止")
    
    def _event_monitor_loop(self) -> None:
        """事件监控循环"""
        last_state = {}
        
        while self.monitoring_active:
            try:
                current_state = self.get_client_state()
                
                # 检测设备变化
                self._detect_device_changes(last_state, current_state)
                
                last_state = current_state
                time.sleep(2)  # 2秒检查一次
                
            except Exception as e:
                logger.error(f"事件监控异常: {e}")
                time.sleep(5)
    
    def _detect_device_changes(self, old_state: List[Dict], new_state: List[Dict]) -> Any:
        """检测设备状态变化"""
        old_devices = {dev.get('device_address'): dev for dev in old_state}
        new_devices = {dev.get('device_address'): dev for dev in new_state}
        
        # 检测新连接的设备
        for addr, device in new_devices.items():
            if addr not in old_devices:
                self._trigger_event('DEVICE_ATTACHED', device)
        
        # 检测断开的设备
        for addr, device in old_devices.items():
            if addr not in new_devices:
                self._trigger_event('DEVICE_DETACHED', device)
        
        # 检测状态变化的设备
        for addr, new_device in new_devices.items():
            if addr in old_devices:
                old_device = old_devices[addr]
                if old_device.get('in_use') != new_device.get('in_use'):
                    event_type = 'DEVICE_USED' if new_device.get('in_use') else 'DEVICE_RELEASED'
                    self._trigger_event(event_type, new_device)
    
    def _trigger_event(self, event_type: str, device_data: Dict) -> Any:
        """触发事件回调"""
        if event_type in self.event_callbacks:
            for callback in self.event_callbacks[event_type]:
                try:
                    callback(event_type, device_data)
                except Exception as e:
                    logger.error(f"事件回调执行失败 {event_type}: {e}")
    
    def get_server_info(self) -> Dict[str, Any]:
        """
        获取服务器详细信息
        基于VirtualHere API: LIST
        """
        try:
            response = self.send_command("LIST")
            if response:
                return self._parse_server_info(response)
            return {}
        except Exception as e:
            logger.error(f"获取服务器信息失败: {e}")
            return {}
    
    def _parse_server_info(self, response: str) -> Dict[str, Any]:
        """解析服务器信息响应"""
        info = {
            'server_name': 'Unknown',
            'version': 'Unknown',
            'devices': [],
            'clients': []
        }
        
        lines = response.strip().split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if line.startswith('VirtualHere'):
                info['server_name'] = line
            elif 'version' in line.lower():
                info['version'] = line
            elif line.startswith('Device'):
                current_section = 'devices'
                device_info = self._parse_device_line(line)
                if device_info:
                    info['devices'].append(device_info)
            elif line.startswith('Client'):
                current_section = 'clients'
                client_info = self._parse_client_line(line)
                if client_info:
                    info['clients'].append(client_info)
        
        return info
    
    def _parse_device_line(self, line: str) -> Optional[Dict[str, Any]]:
        """解析设备行信息"""
        try:
            # 示例: Device 1: Kingston DataTraveler 3.0 (1234:5678) [Available]
            parts = line.split(':')
            if len(parts) >= 2:
                device_id = parts[0].split()[-1]
                device_info = ':'.join(parts[1:]).strip()
                
                return {
                    'device_id': device_id,
                    'description': device_info,
                    'available': '[Available]' in device_info,
                    'in_use': '[In use by' in device_info
                }
        except Exception as e:
            logger.error(f"解析设备行失败: {e}")
        
        return None
    
    def _parse_client_line(self, line: str) -> Optional[Dict[str, Any]]:
        """解析客户端行信息"""
        try:
            # 示例: Client *************:12345 connected
            parts = line.split()
            if len(parts) >= 2:
                client_address = parts[1]
                status = ' '.join(parts[2:]) if len(parts) > 2 else 'unknown'
                
                return {
                    'address': client_address,
                    'status': status,
                    'connected': 'connected' in status.lower()
                }
        except Exception as e:
            logger.error(f"解析客户端行失败: {e}")
        
        return None
    
    def set_device_nickname(self, device_address: str, nickname: str) -> bool:
        """
        设置设备昵称
        基于VirtualHere API: RENAME
        
        Args:
            device_address: 设备地址
            nickname: 新昵称
        
        Returns:
            bool: 设置是否成功
        """
        try:
            command = f"RENAME,{device_address},{nickname}"
            response = self.send_command(command)
            
            if response and "OK" in response:
                logger.info(f"设备昵称设置成功: {device_address} -> {nickname}")
                return True
            else:
                logger.error(f"设备昵称设置失败: {response}")
                return False
                
        except Exception as e:
            logger.error(f"设置设备昵称异常: {e}")
            return False
    
    def get_device_authorization(self, device_address: str) -> Dict[str, Any]:
        """
        获取设备授权信息
        基于VirtualHere授权机制
        
        Args:
            device_address: 设备地址
        
        Returns:
            Dict: 授权信息
        """
        try:
            command = f"GET_AUTH,{device_address}"
            response = self.send_command(command)
            
            if response:
                return self._parse_authorization_response(response)
            
            return {'authorized': False, 'reason': 'No response'}
            
        except Exception as e:
            logger.error(f"获取设备授权信息异常: {e}")
            return {'authorized': False, 'reason': str(e)}
    
    def _parse_authorization_response(self, response: str) -> Dict[str, Any]:
        """解析授权响应"""
        auth_info = {
            'authorized': False,
            'user': None,
            'permissions': [],
            'restrictions': []
        }
        
        if "AUTHORIZED" in response.upper():
            auth_info['authorized'] = True
            
            # 解析用户信息
            if "USER:" in response:
                user_part = response.split("USER:")[1].split()[0]
                auth_info['user'] = user_part
        
        return auth_info
    
    def enable_device_sharing(self, device_address: str, enable: bool = True) -> bool:
        """
        启用/禁用设备共享
        
        Args:
            device_address: 设备地址
            enable: 是否启用
        
        Returns:
            bool: 操作是否成功
        """
        try:
            command = f"SHARE,{device_address},{'1' if enable else '0'}"
            response = self.send_command(command)
            
            if response and "OK" in response:
                action = "启用" if enable else "禁用"
                logger.info(f"设备共享{action}成功: {device_address}")
                return True
            else:
                logger.error(f"设备共享操作失败: {response}")
                return False
                
        except Exception as e:
            logger.error(f"设备共享操作异常: {e}")
            return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            Dict: 性能统计
        """
        try:
            response = self.send_command("STATS")
            if response:
                return self._parse_stats_response(response)
            return {}
        except Exception as e:
            logger.error(f"获取性能统计失败: {e}")
            return {}
    
    def _parse_stats_response(self, response: str) -> Dict[str, Any]:
        """解析统计响应"""
        stats = {
            'uptime': 0,
            'connections': 0,
            'data_transferred': 0,
            'active_devices': 0
        }
        
        lines = response.strip().split('\n')
        for line in lines:
            line = line.strip().lower()
            if 'uptime' in line:
                # 解析运行时间
                pass
            elif 'connection' in line:
                # 解析连接数
                pass
            elif 'data' in line:
                # 解析数据传输量
                pass
        
        return stats
