# -*- coding: utf-8 -*-
"""
性能监控模块
负责监控系统性能指标和资源使用统计
"""

import time
import threading
import psutil
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
from collections import deque
from utils.logger import get_logger

logger = get_logger('performance_monitor')


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, sample_interval: int = 5, history_size: int = 720) -> None:
        """
        初始化性能监控器
        
        Args:
            sample_interval: 采样间隔（秒）
            history_size: 历史数据保留数量（默认720个，1小时数据）
        """
        self.sample_interval = sample_interval
        self.history_size = history_size
        self.running = False
        self.thread = None
        
        # 性能数据历史记录
        self.cpu_history = deque(maxlen=history_size)
        self.memory_history = deque(maxlen=history_size)
        self.disk_history = deque(maxlen=history_size)
        self.network_history = deque(maxlen=history_size)
        self.process_history = deque(maxlen=history_size)
        
        # 网络计数器基线
        self.last_network_counters = None
        
        # 统计数据
        self.statistics = {
            'start_time': time.time(),
            'total_samples': 0,
            'cpu_peaks': [],
            'memory_peaks': [],
            'disk_peaks': [],
            'network_peaks': []
        }
    
    def start(self) -> None:
        """启动性能监控"""
        if self.running:
            logger.warning("性能监控已在运行中")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        logger.info("性能监控已启动")
    
    def stop(self) -> None:
        """停止性能监控"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        logger.info("性能监控已停止")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self.running:
            try:
                self._collect_performance_data()
                time.sleep(self.sample_interval)
            except Exception as e:
                logger.error(f"性能监控异常: {e}")
                time.sleep(self.sample_interval)
    
    def _collect_performance_data(self) -> None:
        """收集性能数据"""
        try:
            timestamp = time.time()
            
            # CPU数据
            cpu_data = self._collect_cpu_data(timestamp)
            self.cpu_history.append(cpu_data)
            
            # 内存数据
            memory_data = self._collect_memory_data(timestamp)
            self.memory_history.append(memory_data)
            
            # 磁盘数据
            disk_data = self._collect_disk_data(timestamp)
            self.disk_history.append(disk_data)
            
            # 网络数据
            network_data = self._collect_network_data(timestamp)
            if network_data:
                self.network_history.append(network_data)
            
            # 进程数据
            process_data = self._collect_process_data(timestamp)
            self.process_history.append(process_data)
            
            # 更新统计
            self.statistics['total_samples'] += 1
            self._update_peak_statistics(cpu_data, memory_data, disk_data, network_data)
            
        except Exception as e:
            logger.error(f"收集性能数据失败: {e}")
    
    def _collect_cpu_data(self, timestamp: float) -> Dict:
        """收集CPU数据"""
        try:
            cpu_percent = psutil.cpu_percent(interval=None)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # 获取每个CPU核心的使用率
            cpu_per_core = psutil.cpu_percent(interval=None, percpu=True)
            
            # 获取CPU时间
            cpu_times = psutil.cpu_times()
            
            return {
                'timestamp': timestamp,
                'cpu_percent': cpu_percent,
                'cpu_count': cpu_count,
                'cpu_freq_current': cpu_freq.current if cpu_freq else None,
                'cpu_freq_min': cpu_freq.min if cpu_freq else None,
                'cpu_freq_max': cpu_freq.max if cpu_freq else None,
                'cpu_per_core': cpu_per_core,
                'cpu_times': {
                    'user': cpu_times.user,
                    'system': cpu_times.system,
                    'idle': cpu_times.idle,
                    'iowait': getattr(cpu_times, 'iowait', 0)
                }
            }
        except Exception as e:
            logger.error(f"收集CPU数据失败: {e}")
            return {'timestamp': timestamp, 'error': str(e)}
    
    def _collect_memory_data(self, timestamp: float) -> Dict:
        """收集内存数据"""
        try:
            virtual_memory = psutil.virtual_memory()
            swap_memory = psutil.swap_memory()
            
            return {
                'timestamp': timestamp,
                'virtual': {
                    'total': virtual_memory.total,
                    'available': virtual_memory.available,
                    'used': virtual_memory.used,
                    'free': virtual_memory.free,
                    'percent': virtual_memory.percent,
                    'active': getattr(virtual_memory, 'active', 0),
                    'inactive': getattr(virtual_memory, 'inactive', 0),
                    'buffers': getattr(virtual_memory, 'buffers', 0),
                    'cached': getattr(virtual_memory, 'cached', 0)
                },
                'swap': {
                    'total': swap_memory.total,
                    'used': swap_memory.used,
                    'free': swap_memory.free,
                    'percent': swap_memory.percent,
                    'sin': swap_memory.sin,
                    'sout': swap_memory.sout
                }
            }
        except Exception as e:
            logger.error(f"收集内存数据失败: {e}")
            return {'timestamp': timestamp, 'error': str(e)}
    
    def _collect_disk_data(self, timestamp: float) -> Dict:
        """收集磁盘数据"""
        try:
            # 磁盘使用情况
            disk_usage = psutil.disk_usage('/')
            
            # 磁盘IO统计
            disk_io = psutil.disk_io_counters()
            
            return {
                'timestamp': timestamp,
                'usage': {
                    'total': disk_usage.total,
                    'used': disk_usage.used,
                    'free': disk_usage.free,
                    'percent': (disk_usage.used / disk_usage.total) * 100
                },
                'io': {
                    'read_count': disk_io.read_count if disk_io else 0,
                    'write_count': disk_io.write_count if disk_io else 0,
                    'read_bytes': disk_io.read_bytes if disk_io else 0,
                    'write_bytes': disk_io.write_bytes if disk_io else 0,
                    'read_time': disk_io.read_time if disk_io else 0,
                    'write_time': disk_io.write_time if disk_io else 0
                }
            }
        except Exception as e:
            logger.error(f"收集磁盘数据失败: {e}")
            return {'timestamp': timestamp, 'error': str(e)}
    
    def _collect_network_data(self, timestamp: float) -> Optional[Dict]:
        """收集网络数据"""
        try:
            current_counters = psutil.net_io_counters()
            
            if self.last_network_counters is None:
                self.last_network_counters = current_counters
                return None
            
            # 计算速率
            time_delta = timestamp - getattr(self, '_last_network_timestamp', timestamp)
            if time_delta <= 0:
                time_delta = self.sample_interval
            
            bytes_sent_rate = (current_counters.bytes_sent - self.last_network_counters.bytes_sent) / time_delta
            bytes_recv_rate = (current_counters.bytes_recv - self.last_network_counters.bytes_recv) / time_delta
            packets_sent_rate = (current_counters.packets_sent - self.last_network_counters.packets_sent) / time_delta
            packets_recv_rate = (current_counters.packets_recv - self.last_network_counters.packets_recv) / time_delta
            
            data = {
                'timestamp': timestamp,
                'counters': {
                    'bytes_sent': current_counters.bytes_sent,
                    'bytes_recv': current_counters.bytes_recv,
                    'packets_sent': current_counters.packets_sent,
                    'packets_recv': current_counters.packets_recv,
                    'errin': current_counters.errin,
                    'errout': current_counters.errout,
                    'dropin': current_counters.dropin,
                    'dropout': current_counters.dropout
                },
                'rates': {
                    'bytes_sent_per_sec': bytes_sent_rate,
                    'bytes_recv_per_sec': bytes_recv_rate,
                    'packets_sent_per_sec': packets_sent_rate,
                    'packets_recv_per_sec': packets_recv_rate
                }
            }
            
            self.last_network_counters = current_counters
            self._last_network_timestamp = timestamp
            
            return data
            
        except Exception as e:
            logger.error(f"收集网络数据失败: {e}")
            return {'timestamp': timestamp, 'error': str(e)}
    
    def _collect_process_data(self, timestamp: float) -> Dict:
        """收集进程数据"""
        try:
            # 进程总数
            process_count = len(psutil.pids())
            
            # 查找VirtualHere相关进程
            vh_processes = []
            python_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'cmdline']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower()
                    
                    # VirtualHere进程
                    if 'vhusbd' in proc_name or 'virtualhere' in proc_name:
                        vh_processes.append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'cpu_percent': proc_info['cpu_percent'],
                            'memory_mb': proc_info['memory_info'].rss / 1024 / 1024 if proc_info['memory_info'] else 0
                        })
                    
                    # Python进程（可能是本应用）
                    elif 'python' in proc_name:
                        cmdline = proc_info.get('cmdline', [])
                        if cmdline and any('slave_server' in arg or 'main.py' in arg for arg in cmdline):
                            python_processes.append({
                                'pid': proc_info['pid'],
                                'name': proc_info['name'],
                                'cpu_percent': proc_info['cpu_percent'],
                                'memory_mb': proc_info['memory_info'].rss / 1024 / 1024 if proc_info['memory_info'] else 0,
                                'cmdline': ' '.join(cmdline[:3])  # 只保留前3个参数
                            })
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return {
                'timestamp': timestamp,
                'total_processes': process_count,
                'virtualhere_processes': vh_processes,
                'python_processes': python_processes
            }
            
        except Exception as e:
            logger.error(f"收集进程数据失败: {e}")
            return {'timestamp': timestamp, 'error': str(e)}
    
    def _update_peak_statistics(self, cpu_data: Dict, memory_data: Dict, disk_data: Dict, network_data: Optional[Dict]) -> Any:
        """更新峰值统计"""
        try:
            # CPU峰值
            if 'cpu_percent' in cpu_data and cpu_data['cpu_percent'] > 90:
                self.statistics['cpu_peaks'].append({
                    'timestamp': cpu_data['timestamp'],
                    'value': cpu_data['cpu_percent']
                })
                # 只保留最近的100个峰值
                if len(self.statistics['cpu_peaks']) > 100:
                    self.statistics['cpu_peaks'].pop(0)
            
            # 内存峰值
            if 'virtual' in memory_data and memory_data['virtual']['percent'] > 90:
                self.statistics['memory_peaks'].append({
                    'timestamp': memory_data['timestamp'],
                    'value': memory_data['virtual']['percent']
                })
                if len(self.statistics['memory_peaks']) > 100:
                    self.statistics['memory_peaks'].pop(0)
            
            # 磁盘峰值
            if 'usage' in disk_data and disk_data['usage']['percent'] > 90:
                self.statistics['disk_peaks'].append({
                    'timestamp': disk_data['timestamp'],
                    'value': disk_data['usage']['percent']
                })
                if len(self.statistics['disk_peaks']) > 100:
                    self.statistics['disk_peaks'].pop(0)
            
            # 网络峰值（高流量）
            if network_data and 'rates' in network_data:
                total_rate = network_data['rates']['bytes_sent_per_sec'] + network_data['rates']['bytes_recv_per_sec']
                if total_rate > 10 * 1024 * 1024:  # 10MB/s
                    self.statistics['network_peaks'].append({
                        'timestamp': network_data['timestamp'],
                        'value': total_rate
                    })
                    if len(self.statistics['network_peaks']) > 100:
                        self.statistics['network_peaks'].pop(0)
                        
        except Exception as e:
            logger.error(f"更新峰值统计失败: {e}")

    def get_current_performance(self) -> Dict[str, Any]:
        """
        获取当前性能数据

        Returns:
            Dict: 当前性能数据
        """
        try:
            timestamp = time.time()

            return {
                'cpu': self._collect_cpu_data(timestamp),
                'memory': self._collect_memory_data(timestamp),
                'disk': self._collect_disk_data(timestamp),
                'network': self._collect_network_data(timestamp),
                'process': self._collect_process_data(timestamp),
                'timestamp': timestamp
            }
        except Exception as e:
            logger.error(f"获取当前性能数据失败: {e}")
            return {'error': str(e), 'timestamp': time.time()}

    def get_performance_history(self, duration_minutes: int = 60) -> Dict[str, List]:
        """
        获取性能历史数据

        Args:
            duration_minutes: 历史数据时长（分钟）

        Returns:
            Dict: 历史性能数据
        """
        try:
            cutoff_time = time.time() - (duration_minutes * 60)

            # 过滤历史数据
            cpu_history = [data for data in self.cpu_history if data.get('timestamp', 0) > cutoff_time]
            memory_history = [data for data in self.memory_history if data.get('timestamp', 0) > cutoff_time]
            disk_history = [data for data in self.disk_history if data.get('timestamp', 0) > cutoff_time]
            network_history = [data for data in self.network_history if data.get('timestamp', 0) > cutoff_time]
            process_history = [data for data in self.process_history if data.get('timestamp', 0) > cutoff_time]

            return {
                'cpu': cpu_history,
                'memory': memory_history,
                'disk': disk_history,
                'network': network_history,
                'process': process_history,
                'duration_minutes': duration_minutes,
                'sample_count': len(cpu_history)
            }
        except Exception as e:
            logger.error(f"获取性能历史数据失败: {e}")
            return {'error': str(e)}

    def get_performance_summary(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """
        获取性能摘要统计

        Args:
            duration_minutes: 统计时长（分钟）

        Returns:
            Dict: 性能摘要
        """
        try:
            history = self.get_performance_history(duration_minutes)

            if 'error' in history:
                return history

            summary = {
                'duration_minutes': duration_minutes,
                'sample_count': history['sample_count'],
                'cpu': self._calculate_cpu_summary(history['cpu']),
                'memory': self._calculate_memory_summary(history['memory']),
                'disk': self._calculate_disk_summary(history['disk']),
                'network': self._calculate_network_summary(history['network']),
                'process': self._calculate_process_summary(history['process'])
            }

            return summary

        except Exception as e:
            logger.error(f"获取性能摘要失败: {e}")
            return {'error': str(e)}

    def _calculate_cpu_summary(self, cpu_data: List[Dict]) -> Dict:
        """计算CPU摘要统计"""
        if not cpu_data:
            return {}

        cpu_percentages = [data.get('cpu_percent', 0) for data in cpu_data if 'cpu_percent' in data]

        if not cpu_percentages:
            return {}

        return {
            'avg': sum(cpu_percentages) / len(cpu_percentages),
            'min': min(cpu_percentages),
            'max': max(cpu_percentages),
            'samples': len(cpu_percentages)
        }

    def _calculate_memory_summary(self, memory_data: List[Dict]) -> Dict:
        """计算内存摘要统计"""
        if not memory_data:
            return {}

        memory_percentages = [data.get('virtual', {}).get('percent', 0) for data in memory_data if 'virtual' in data]

        if not memory_percentages:
            return {}

        return {
            'avg': sum(memory_percentages) / len(memory_percentages),
            'min': min(memory_percentages),
            'max': max(memory_percentages),
            'samples': len(memory_percentages)
        }

    def _calculate_disk_summary(self, disk_data: List[Dict]) -> Dict:
        """计算磁盘摘要统计"""
        if not disk_data:
            return {}

        disk_percentages = [data.get('usage', {}).get('percent', 0) for data in disk_data if 'usage' in data]

        if not disk_percentages:
            return {}

        return {
            'avg': sum(disk_percentages) / len(disk_percentages),
            'min': min(disk_percentages),
            'max': max(disk_percentages),
            'samples': len(disk_percentages)
        }

    def _calculate_network_summary(self, network_data: List[Dict]) -> Dict:
        """计算网络摘要统计"""
        if not network_data:
            return {}

        sent_rates = [data.get('rates', {}).get('bytes_sent_per_sec', 0) for data in network_data if 'rates' in data]
        recv_rates = [data.get('rates', {}).get('bytes_recv_per_sec', 0) for data in network_data if 'rates' in data]

        if not sent_rates or not recv_rates:
            return {}

        return {
            'sent': {
                'avg_mbps': (sum(sent_rates) / len(sent_rates)) / (1024 * 1024),
                'max_mbps': max(sent_rates) / (1024 * 1024),
                'total_mb': sum(sent_rates) * self.sample_interval / (1024 * 1024)
            },
            'recv': {
                'avg_mbps': (sum(recv_rates) / len(recv_rates)) / (1024 * 1024),
                'max_mbps': max(recv_rates) / (1024 * 1024),
                'total_mb': sum(recv_rates) * self.sample_interval / (1024 * 1024)
            },
            'samples': len(sent_rates)
        }

    def _calculate_process_summary(self, process_data: List[Dict]) -> Dict:
        """计算进程摘要统计"""
        if not process_data:
            return {}

        vh_process_counts = [len(data.get('virtualhere_processes', [])) for data in process_data]
        python_process_counts = [len(data.get('python_processes', [])) for data in process_data]
        total_process_counts = [data.get('total_processes', 0) for data in process_data]

        return {
            'virtualhere_processes': {
                'avg': sum(vh_process_counts) / len(vh_process_counts) if vh_process_counts else 0,
                'max': max(vh_process_counts) if vh_process_counts else 0
            },
            'python_processes': {
                'avg': sum(python_process_counts) / len(python_process_counts) if python_process_counts else 0,
                'max': max(python_process_counts) if python_process_counts else 0
            },
            'total_processes': {
                'avg': sum(total_process_counts) / len(total_process_counts) if total_process_counts else 0,
                'max': max(total_process_counts) if total_process_counts else 0
            },
            'samples': len(process_data)
        }

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取监控统计信息

        Returns:
            Dict: 统计信息
        """
        return {
            'monitor_status': {
                'running': self.running,
                'sample_interval': self.sample_interval,
                'history_size': self.history_size,
                'start_time': self.statistics['start_time'],
                'uptime_seconds': time.time() - self.statistics['start_time'],
                'total_samples': self.statistics['total_samples']
            },
            'data_counts': {
                'cpu_samples': len(self.cpu_history),
                'memory_samples': len(self.memory_history),
                'disk_samples': len(self.disk_history),
                'network_samples': len(self.network_history),
                'process_samples': len(self.process_history)
            },
            'peaks': {
                'cpu_peaks': len(self.statistics['cpu_peaks']),
                'memory_peaks': len(self.statistics['memory_peaks']),
                'disk_peaks': len(self.statistics['disk_peaks']),
                'network_peaks': len(self.statistics['network_peaks'])
            }
        }

    def clear_history(self) -> None:
        """清除历史数据"""
        self.cpu_history.clear()
        self.memory_history.clear()
        self.disk_history.clear()
        self.network_history.clear()
        self.process_history.clear()

        # 重置统计
        self.statistics = {
            'start_time': time.time(),
            'total_samples': 0,
            'cpu_peaks': [],
            'memory_peaks': [],
            'disk_peaks': [],
            'network_peaks': []
        }

        logger.info("性能监控历史数据已清除")

    def set_sample_interval(self, interval: int) -> Any:
        """
        设置采样间隔

        Args:
            interval: 采样间隔（秒）
        """
        if interval < 1:
            logger.warning(f"采样间隔过短 ({interval}秒)，已调整为1秒")
            interval = 1

        self.sample_interval = interval
        logger.info(f"采样间隔已更新为 {interval} 秒")


# 全局性能监控器实例
global_performance_monitor = PerformanceMonitor()
