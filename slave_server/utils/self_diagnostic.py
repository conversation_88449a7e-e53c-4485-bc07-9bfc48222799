# -*- coding: utf-8 -*-
"""
自诊断模块
提供系统故障排查和诊断信息
"""

import os
import time
import subprocess
import platform
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from utils.logger import get_logger
from utils.vh_server_manager import VirtualHereServerManager
from utils.config_manager import ConfigManager
from utils.master_communication import MasterCommunication
from utils.health_checker import HealthChecker
from utils.performance_monitor import global_performance_monitor
from db.device_dao import DeviceDAO

logger = get_logger('self_diagnostic')


class SelfDiagnostic:
    """自诊断器"""
    
    def __init__(self):
        """初始化自诊断器"""
        self.vh_server_manager = VirtualHereServerManager()
        self.config_manager = ConfigManager()
        self.master_comm = MasterCommunication()
        self.health_checker = HealthChecker()
        self.device_dao = DeviceDAO()
        
        # 诊断项目
        self.diagnostic_items = {
            'system_info': self._diagnose_system_info,
            'virtualhere_server': self._diagnose_virtualhere_server,
            'database': self._diagnose_database,
            'master_communication': self._diagnose_master_communication,
            'network_connectivity': self._diagnose_network_connectivity,
            'file_permissions': self._diagnose_file_permissions,
            'disk_space': self._diagnose_disk_space,
            'process_status': self._diagnose_process_status,
            'configuration': self._diagnose_configuration,
            'log_analysis': self._diagnose_log_analysis
        }
    
    def run_full_diagnostic(self) -> Dict[str, Any]:
        """
        运行完整的自诊断
        
        Returns:
            Dict: 诊断结果
        """
        try:
            logger.info("开始运行完整自诊断")
            
            diagnostic_report = {
                'timestamp': datetime.now().isoformat(),
                'diagnostic_version': '1.0.0',
                'system_info': {
                    'hostname': platform.node(),
                    'platform': platform.system(),
                    'architecture': platform.machine(),
                    'python_version': platform.python_version()
                },
                'results': {},
                'summary': {
                    'total_checks': 0,
                    'passed_checks': 0,
                    'failed_checks': 0,
                    'warning_checks': 0,
                    'overall_status': 'unknown'
                },
                'recommendations': []
            }
            
            # 运行所有诊断项目
            for item_name, diagnostic_func in self.diagnostic_items.items():
                try:
                    logger.debug(f"运行诊断项目: {item_name}")
                    result = diagnostic_func()
                    diagnostic_report['results'][item_name] = result
                    diagnostic_report['summary']['total_checks'] += 1
                    
                    # 统计结果
                    status = result.get('status', 'unknown')
                    if status == 'pass':
                        diagnostic_report['summary']['passed_checks'] += 1
                    elif status == 'fail':
                        diagnostic_report['summary']['failed_checks'] += 1
                    elif status == 'warning':
                        diagnostic_report['summary']['warning_checks'] += 1
                    
                    # 收集建议
                    if 'recommendations' in result:
                        diagnostic_report['recommendations'].extend(result['recommendations'])
                        
                except Exception as e:
                    logger.error(f"诊断项目 {item_name} 失败: {e}")
                    diagnostic_report['results'][item_name] = {
                        'status': 'fail',
                        'message': f'诊断失败: {str(e)}',
                        'timestamp': datetime.now().isoformat()
                    }
                    diagnostic_report['summary']['total_checks'] += 1
                    diagnostic_report['summary']['failed_checks'] += 1
            
            # 确定整体状态
            if diagnostic_report['summary']['failed_checks'] > 0:
                diagnostic_report['summary']['overall_status'] = 'fail'
            elif diagnostic_report['summary']['warning_checks'] > 0:
                diagnostic_report['summary']['overall_status'] = 'warning'
            else:
                diagnostic_report['summary']['overall_status'] = 'pass'
            
            logger.info(f"自诊断完成，整体状态: {diagnostic_report['summary']['overall_status']}")
            return diagnostic_report
            
        except Exception as e:
            logger.error(f"运行完整自诊断失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'summary': {'overall_status': 'fail'}
            }
    
    def run_specific_diagnostic(self, item_name: str) -> Dict[str, Any]:
        """
        运行特定的诊断项目
        
        Args:
            item_name: 诊断项目名称
        
        Returns:
            Dict: 诊断结果
        """
        try:
            if item_name not in self.diagnostic_items:
                return {
                    'status': 'fail',
                    'message': f'未知的诊断项目: {item_name}',
                    'timestamp': datetime.now().isoformat()
                }
            
            diagnostic_func = self.diagnostic_items[item_name]
            result = diagnostic_func()
            result['item_name'] = item_name
            
            return result
            
        except Exception as e:
            logger.error(f"运行诊断项目 {item_name} 失败: {e}")
            return {
                'status': 'fail',
                'message': f'诊断失败: {str(e)}',
                'timestamp': datetime.now().isoformat(),
                'item_name': item_name
            }
    
    def _diagnose_system_info(self) -> Dict[str, Any]:
        """诊断系统信息"""
        try:
            import psutil
            
            # 获取系统信息
            boot_time = psutil.boot_time()
            uptime = time.time() - boot_time
            
            # 检查系统负载
            load_avg = os.getloadavg() if hasattr(os, 'getloadavg') else (0, 0, 0)
            cpu_count = psutil.cpu_count()
            
            issues = []
            recommendations = []
            
            # 检查系统运行时间
            if uptime < 300:  # 5分钟
                issues.append(f"系统刚启动，运行时间仅 {uptime:.0f} 秒")
                recommendations.append("系统刚启动，某些服务可能还在初始化中")
            
            # 检查系统负载
            if load_avg[0] > cpu_count * 2:
                issues.append(f"系统负载过高: {load_avg[0]:.2f} (CPU核心数: {cpu_count})")
                recommendations.append("系统负载过高，建议检查CPU密集型进程")
            
            status = 'warning' if issues else 'pass'
            message = '; '.join(issues) if issues else '系统信息正常'
            
            return {
                'status': status,
                'message': message,
                'details': {
                    'platform': platform.system(),
                    'architecture': platform.machine(),
                    'python_version': platform.python_version(),
                    'uptime_seconds': uptime,
                    'load_average': load_avg,
                    'cpu_count': cpu_count
                },
                'recommendations': recommendations,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'fail',
                'message': f'系统信息诊断失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def _diagnose_virtualhere_server(self) -> Dict[str, Any]:
        """诊断VirtualHere服务器"""
        try:
            vh_status = self.vh_server_manager.get_server_status()
            
            issues = []
            recommendations = []
            
            if not vh_status['running']:
                issues.append("VirtualHere服务器未运行")
                recommendations.append("尝试启动VirtualHere服务器")
            else:
                # 检查端口状态
                if vh_status.get('port_status') != 'listening':
                    issues.append("VirtualHere服务器端口未监听")
                    recommendations.append("检查端口配置和防火墙设置")
                
                # 检查进程资源使用
                if 'process_info' in vh_status and vh_status['process_info']:
                    process_info = vh_status['process_info']
                    if process_info.get('cpu_percent', 0) > 50:
                        issues.append(f"VirtualHere服务器CPU使用率过高: {process_info['cpu_percent']:.1f}%")
                        recommendations.append("检查VirtualHere服务器负载和设备连接数")
                    
                    if process_info.get('memory_mb', 0) > 200:
                        issues.append(f"VirtualHere服务器内存使用过高: {process_info['memory_mb']:.1f}MB")
                        recommendations.append("检查VirtualHere服务器内存泄漏")
            
            # 检查配置文件
            config_file = self.vh_server_manager.config_file
            if not os.path.exists(config_file):
                issues.append("VirtualHere配置文件不存在")
                recommendations.append("创建VirtualHere配置文件")
            
            status = 'fail' if not vh_status['running'] else ('warning' if issues else 'pass')
            message = '; '.join(issues) if issues else 'VirtualHere服务器状态正常'
            
            return {
                'status': status,
                'message': message,
                'details': vh_status,
                'recommendations': recommendations,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'fail',
                'message': f'VirtualHere服务器诊断失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def _diagnose_database(self) -> Dict[str, Any]:
        """诊断数据库"""
        try:
            issues = []
            recommendations = []
            
            # 检查数据库连接
            try:
                device_count = self.device_dao.get_device_count()
                device_stats = self.device_dao.get_device_statistics()
            except Exception as e:
                issues.append(f"数据库连接失败: {str(e)}")
                recommendations.append("检查数据库文件权限和路径")
                return {
                    'status': 'fail',
                    'message': '; '.join(issues),
                    'recommendations': recommendations,
                    'timestamp': datetime.now().isoformat()
                }
            
            # 检查数据库文件
            db_config = self.config_manager.get_database_config()
            db_file = db_config.get('db_file', 'data/slave_server.db')
            
            if not os.path.exists(db_file):
                issues.append("数据库文件不存在")
                recommendations.append("初始化数据库文件")
            else:
                # 检查文件大小
                file_size = os.path.getsize(db_file)
                if file_size == 0:
                    issues.append("数据库文件为空")
                    recommendations.append("重新初始化数据库")
                elif file_size > 100 * 1024 * 1024:  # 100MB
                    issues.append(f"数据库文件过大: {file_size / 1024 / 1024:.1f}MB")
                    recommendations.append("考虑清理历史数据或优化数据库")
            
            status = 'fail' if issues else 'pass'
            message = '; '.join(issues) if issues else f'数据库状态正常，设备数量: {device_count}'
            
            return {
                'status': status,
                'message': message,
                'details': {
                    'device_count': device_count,
                    'device_statistics': device_stats,
                    'db_file': db_file,
                    'file_size_bytes': os.path.getsize(db_file) if os.path.exists(db_file) else 0
                },
                'recommendations': recommendations,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'fail',
                'message': f'数据库诊断失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }

    def _diagnose_master_communication(self) -> Dict[str, Any]:
        """诊断主从通信"""
        try:
            conn_status = self.master_comm.get_connection_status()

            issues = []
            recommendations = []

            if not conn_status['connected']:
                issues.append("主从通信连接断开")
                recommendations.append("检查主服务器地址和网络连接")

            if conn_status['circuit_breaker_open']:
                issues.append("熔断器已开启")
                recommendations.append("检查网络稳定性，等待熔断器自动恢复或手动重置")

            if conn_status['consecutive_failures'] > 0:
                issues.append(f"连续失败次数: {conn_status['consecutive_failures']}")
                recommendations.append("检查网络连接和主服务器状态")

            if conn_status['pending_reports'] > 100:
                issues.append(f"待上报数据过多: {conn_status['pending_reports']}")
                recommendations.append("检查网络连接，考虑清理待上报数据")

            status = 'fail' if not conn_status['connected'] else ('warning' if issues else 'pass')
            message = '; '.join(issues) if issues else '主从通信状态正常'

            return {
                'status': status,
                'message': message,
                'details': conn_status,
                'recommendations': recommendations,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            return {
                'status': 'fail',
                'message': f'主从通信诊断失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }

    def _diagnose_network_connectivity(self) -> Dict[str, Any]:
        """诊断网络连接"""
        try:
            issues = []
            recommendations = []
            connectivity_results = {}

            # 测试本地网络接口
            try:
                import psutil
                network_interfaces = psutil.net_if_addrs()
                active_interfaces = []

                for interface, addresses in network_interfaces.items():
                    for addr in addresses:
                        if addr.family == 2 and not addr.address.startswith('127.'):  # IPv4, not localhost
                            active_interfaces.append({
                                'interface': interface,
                                'address': addr.address
                            })

                connectivity_results['network_interfaces'] = active_interfaces

                if not active_interfaces:
                    issues.append("没有活动的网络接口")
                    recommendations.append("检查网络配置和网线连接")

            except Exception as e:
                issues.append(f"网络接口检查失败: {str(e)}")

            # 测试DNS解析
            try:
                import socket
                socket.gethostbyname('www.google.com')
                connectivity_results['dns_resolution'] = True
            except Exception as e:
                connectivity_results['dns_resolution'] = False
                issues.append(f"DNS解析失败: {str(e)}")
                recommendations.append("检查DNS配置")

            # 测试主服务器连接
            if hasattr(self.master_comm, 'master_server_url') and self.master_comm.master_server_url:
                try:
                    import urllib.parse
                    parsed_url = urllib.parse.urlparse(self.master_comm.master_server_url)
                    host = parsed_url.hostname
                    port = parsed_url.port or 80

                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    result = sock.connect_ex((host, port))
                    sock.close()

                    connectivity_results['master_server_reachable'] = (result == 0)

                    if result != 0:
                        issues.append(f"无法连接到主服务器 {host}:{port}")
                        recommendations.append("检查主服务器地址和端口配置")

                except Exception as e:
                    connectivity_results['master_server_reachable'] = False
                    issues.append(f"主服务器连接测试失败: {str(e)}")

            status = 'fail' if issues else 'pass'
            message = '; '.join(issues) if issues else '网络连接正常'

            return {
                'status': status,
                'message': message,
                'details': connectivity_results,
                'recommendations': recommendations,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            return {
                'status': 'fail',
                'message': f'网络连接诊断失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }

    def _diagnose_file_permissions(self) -> Dict[str, Any]:
        """诊断文件权限"""
        try:
            issues = []
            recommendations = []
            permission_results = {}

            # 检查关键目录和文件的权限
            critical_paths = [
                ('data', 'directory'),
                ('logs', 'directory'),
                ('config/slave_server.ini', 'file'),
                (self.vh_server_manager.config_file, 'file'),
                (self.vh_server_manager.binary_path, 'file')
            ]

            for path, path_type in critical_paths:
                try:
                    if not os.path.exists(path):
                        permission_results[path] = {'exists': False}
                        if path_type == 'directory':
                            issues.append(f"关键目录不存在: {path}")
                            recommendations.append(f"创建目录: {path}")
                        continue

                    # 检查权限
                    readable = os.access(path, os.R_OK)
                    writable = os.access(path, os.W_OK)
                    executable = os.access(path, os.X_OK)

                    permission_results[path] = {
                        'exists': True,
                        'readable': readable,
                        'writable': writable,
                        'executable': executable
                    }

                    # 检查必需权限
                    if path_type == 'directory':
                        if not (readable and writable and executable):
                            issues.append(f"目录权限不足: {path}")
                            recommendations.append(f"修复目录权限: chmod 755 {path}")
                    elif path_type == 'file':
                        if path.endswith('.ini') or path.endswith('.conf'):
                            if not (readable and writable):
                                issues.append(f"配置文件权限不足: {path}")
                                recommendations.append(f"修复文件权限: chmod 644 {path}")
                        elif 'vhusbd' in path or 'server' in path:
                            if not (readable and executable):
                                issues.append(f"可执行文件权限不足: {path}")
                                recommendations.append(f"修复执行权限: chmod 755 {path}")

                except Exception as e:
                    permission_results[path] = {'error': str(e)}
                    issues.append(f"检查 {path} 权限失败: {str(e)}")

            status = 'fail' if issues else 'pass'
            message = '; '.join(issues) if issues else '文件权限正常'

            return {
                'status': status,
                'message': message,
                'details': permission_results,
                'recommendations': recommendations,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            return {
                'status': 'fail',
                'message': f'文件权限诊断失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }

    def get_diagnostic_items(self) -> List[str]:
        """
        获取可用的诊断项目列表

        Returns:
            List[str]: 诊断项目名称列表
        """
        return list(self.diagnostic_items.keys())

    def generate_diagnostic_report(self, include_performance: bool = True) -> str:
        """
        生成诊断报告文本

        Args:
            include_performance: 是否包含性能数据

        Returns:
            str: 诊断报告文本
        """
        try:
            diagnostic_result = self.run_full_diagnostic()

            report_lines = []
            report_lines.append("=" * 60)
            report_lines.append("OmniLink 从服务器自诊断报告")
            report_lines.append("=" * 60)
            report_lines.append(f"生成时间: {diagnostic_result['timestamp']}")
            report_lines.append(f"系统信息: {diagnostic_result['system_info']}")
            report_lines.append("")

            # 摘要
            summary = diagnostic_result['summary']
            report_lines.append("诊断摘要:")
            report_lines.append(f"  总检查项: {summary['total_checks']}")
            report_lines.append(f"  通过: {summary['passed_checks']}")
            report_lines.append(f"  警告: {summary['warning_checks']}")
            report_lines.append(f"  失败: {summary['failed_checks']}")
            report_lines.append(f"  整体状态: {summary['overall_status']}")
            report_lines.append("")

            # 详细结果
            report_lines.append("详细诊断结果:")
            for item_name, result in diagnostic_result['results'].items():
                status_symbol = "✓" if result['status'] == 'pass' else ("⚠" if result['status'] == 'warning' else "✗")
                report_lines.append(f"  {status_symbol} {item_name}: {result['message']}")

            report_lines.append("")

            # 建议
            if diagnostic_result['recommendations']:
                report_lines.append("建议:")
                for i, recommendation in enumerate(diagnostic_result['recommendations'], 1):
                    report_lines.append(f"  {i}. {recommendation}")
                report_lines.append("")

            # 性能数据
            if include_performance:
                try:
                    perf_data = global_performance_monitor.get_current_performance()
                    if 'error' not in perf_data:
                        report_lines.append("当前性能状态:")
                        if 'cpu' in perf_data:
                            report_lines.append(f"  CPU使用率: {perf_data['cpu'].get('cpu_percent', 0):.1f}%")
                        if 'memory' in perf_data:
                            memory_percent = perf_data['memory'].get('virtual', {}).get('percent', 0)
                            report_lines.append(f"  内存使用率: {memory_percent:.1f}%")
                        if 'disk' in perf_data:
                            disk_percent = perf_data['disk'].get('usage', {}).get('percent', 0)
                            report_lines.append(f"  磁盘使用率: {disk_percent:.1f}%")
                        report_lines.append("")
                except Exception:
                    pass

            report_lines.append("=" * 60)

            return "\n".join(report_lines)

        except Exception as e:
            logger.error(f"生成诊断报告失败: {e}")
            return f"生成诊断报告失败: {str(e)}"


# 全局自诊断器实例
global_self_diagnostic = SelfDiagnostic()
