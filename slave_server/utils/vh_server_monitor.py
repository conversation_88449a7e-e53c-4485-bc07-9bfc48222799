# -*- coding: utf-8 -*-
"""
VirtualHere服务器监控模块
负责监控VirtualHere服务器进程状态并自动恢复
"""

import os
import time
import threading
import psutil
from typing import Dict, Any, Optional, Callable
from utils.logger import get_logger
from utils.vh_server_manager import VirtualHereServerManager

logger = get_logger('vh_server_monitor')


class VirtualHereServerMonitor:
    """VirtualHere服务器监控器"""
    
    def __init__(self, check_interval: int = 30, auto_recover: bool = True) -> Any:
        """
        初始化VirtualHere服务器监控器
        
        Args:
            check_interval: 检查间隔（秒）
            auto_recover: 是否自动恢复
        """
        self.check_interval = check_interval
        self.auto_recover = auto_recover
        self.server_manager = VirtualHereServerManager()
        self.running = False
        self.thread = None
        self.recovery_count = 0
        self.last_recovery_time = 0
        self.max_recovery_attempts = 5
        self.recovery_reset_time = 3600  # 1小时后重置恢复计数
        self.status_callback = None
        self.recovery_callback = None
    
    def start(self) -> None:
        """启动监控"""
        if self.running:
            logger.warning("VirtualHere服务器监控已在运行中")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        logger.info("VirtualHere服务器监控已启动")
    
    def stop(self) -> None:
        """停止监控"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        logger.info("VirtualHere服务器监控已停止")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self.running:
            try:
                self._check_server_status()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"服务器监控异常: {e}")
                time.sleep(self.check_interval)
    
    def _check_server_status(self) -> None:
        """检查服务器状态"""
        try:
            # 获取服务器状态
            status = self.server_manager.get_server_status()
            
            # 调用状态回调
            if self.status_callback:
                self.status_callback(status)
            
            # 检查服务器是否运行
            if not status['running']:
                logger.warning("VirtualHere服务器未运行，检查是否需要恢复")
                self._handle_server_down()
            else:
                # 检查服务器是否健康
                if not self._is_server_healthy(status):
                    logger.warning("VirtualHere服务器运行状态异常，检查是否需要恢复")
                    self._handle_server_unhealthy(status)
                else:
                    logger.debug("VirtualHere服务器运行正常")
                    
        except Exception as e:
            logger.error(f"检查服务器状态失败: {e}")
    
    def _is_server_healthy(self, status: Dict) -> bool:
        """
        检查服务器是否健康
        
        Args:
            status: 服务器状态
        
        Returns:
            bool: 是否健康
        """
        # 检查进程状态
        if not status['running']:
            return False
        
        # 检查端口状态
        if status['port_status'] != 'listening':
            return False
        
        # 检查进程资源使用
        if 'process_info' in status and status['process_info']:
            process_info = status['process_info']
            
            # CPU使用率过高
            if process_info.get('cpu_percent', 0) > 90:
                return False
            
            # 内存使用过高（超过500MB）
            if process_info.get('memory_mb', 0) > 500:
                return False
        
        return True
    
    def _handle_server_down(self) -> None:
        """处理服务器停止状态"""
        if not self.auto_recover:
            logger.info("自动恢复已禁用，不执行恢复操作")
            return
        
        # 检查恢复频率限制
        current_time = time.time()
        if current_time - self.last_recovery_time > self.recovery_reset_time:
            # 超过重置时间，重置计数
            self.recovery_count = 0
        
        # 检查恢复次数限制
        if self.recovery_count >= self.max_recovery_attempts:
            logger.warning(f"恢复尝试次数已达上限({self.max_recovery_attempts})，不再自动恢复")
            return
        
        logger.info(f"尝试恢复VirtualHere服务器 (尝试 {self.recovery_count + 1}/{self.max_recovery_attempts})")
        
        # 执行恢复
        success = self.server_manager.restart_server()
        
        # 更新恢复计数和时间
        self.recovery_count += 1
        self.last_recovery_time = current_time
        
        # 调用恢复回调
        if self.recovery_callback:
            self.recovery_callback(success)
        
        if success:
            logger.info("VirtualHere服务器恢复成功")
        else:
            logger.error("VirtualHere服务器恢复失败")
    
    def _handle_server_unhealthy(self, status: Dict) -> Any:
        """
        处理服务器不健康状态
        
        Args:
            status: 服务器状态
        """
        if not self.auto_recover:
            logger.info("自动恢复已禁用，不执行恢复操作")
            return
        
        logger.info("VirtualHere服务器状态不健康，尝试重启")
        
        # 执行恢复
        success = self.server_manager.restart_server()
        
        # 调用恢复回调
        if self.recovery_callback:
            self.recovery_callback(success)
        
        if success:
            logger.info("VirtualHere服务器重启成功")
        else:
            logger.error("VirtualHere服务器重启失败")
    
    def set_status_callback(self, callback: Callable[[Dict], None]) -> Any:
        """
        设置状态回调函数
        
        Args:
            callback: 回调函数，接收状态字典参数
        """
        self.status_callback = callback
    
    def set_recovery_callback(self, callback: Callable[[bool], None]) -> Any:
        """
        设置恢复回调函数
        
        Args:
            callback: 回调函数，接收恢复结果参数
        """
        self.recovery_callback = callback
    
    def get_monitor_status(self) -> Dict[str, Any]:
        """
        获取监控器状态
        
        Returns:
            Dict: 监控器状态
        """
        return {
            'running': self.running,
            'check_interval': self.check_interval,
            'auto_recover': self.auto_recover,
            'recovery_count': self.recovery_count,
            'last_recovery_time': self.last_recovery_time,
            'max_recovery_attempts': self.max_recovery_attempts,
            'recovery_reset_time': self.recovery_reset_time,
            'server_status': self.server_manager.get_server_status() if self.running else None
        }
    
    def reset_recovery_count(self) -> None:
        """重置恢复计数"""
        self.recovery_count = 0
        logger.info("恢复计数已重置")
    
    def set_check_interval(self, interval: int) -> Any:
        """
        设置检查间隔
        
        Args:
            interval: 检查间隔（秒）
        """
        if interval < 5:
            logger.warning(f"检查间隔过短 ({interval}秒)，已调整为5秒")
            interval = 5
        
        self.check_interval = interval
        logger.info(f"检查间隔已更新为 {interval} 秒")
    
    def set_auto_recover(self, auto_recover: bool) -> Any:
        """
        设置是否自动恢复
        
        Args:
            auto_recover: 是否自动恢复
        """
        self.auto_recover = auto_recover
        logger.info(f"自动恢复已{'启用' if auto_recover else '禁用'}")
    
    def set_max_recovery_attempts(self, max_attempts: int) -> Any:
        """
        设置最大恢复尝试次数
        
        Args:
            max_attempts: 最大尝试次数
        """
        if max_attempts < 1:
            logger.warning("最大恢复尝试次数不能小于1，已调整为1")
            max_attempts = 1
        
        self.max_recovery_attempts = max_attempts
        logger.info(f"最大恢复尝试次数已更新为 {max_attempts}")
    
    def set_recovery_reset_time(self, reset_time: int) -> Any:
        """
        设置恢复计数重置时间
        
        Args:
            reset_time: 重置时间（秒）
        """
        if reset_time < 60:
            logger.warning(f"恢复计数重置时间过短 ({reset_time}秒)，已调整为60秒")
            reset_time = 60
        
        self.recovery_reset_time = reset_time
        logger.info(f"恢复计数重置时间已更新为 {reset_time} 秒")
