# -*- coding: utf-8 -*-
"""
VirtualHere客户端通信模块
基于官方客户端API实现
"""

import os
import time
import threading
import xmltodict
import hashlib
from typing import List, Dict, Optional, Any, Union, Tuple
from utils.logger import get_logger

logger = get_logger('vh_client')

# 全局锁，确保命名管道线程安全
lock = threading.RLock()
# 缓存XML响应
cached_xml = ""


class VirtualHereClient:
    """VirtualHere客户端通信类"""
    
    def __init__(self, write_pipe: str = '/tmp/vhclient', read_pipe: str = '/tmp/vhclient_response') -> None:
        """
        初始化VirtualHere客户端
        
        Args:
            write_pipe: 写入管道路径
            read_pipe: 读取管道路径
        """
        self.write_pipe = write_pipe
        self.read_pipe = read_pipe
        self.retry_count = 3
        self.retry_delay = 1
    
    def send_command(self, command: str) -> Optional[str]:
        """
        发送命令到VirtualHere客户端
        
        Args:
            command: 命令字符串
        
        Returns:
            str: 响应内容，失败返回None
        """
        for attempt in range(self.retry_count):
            try:
                response = self._send_command_once(command)
                if response is not None:
                    return response
                
                if attempt < self.retry_count - 1:
                    logger.warning(f"命令发送失败，{self.retry_delay}秒后重试 (尝试 {attempt + 1}/{self.retry_count})")
                    time.sleep(self.retry_delay)
                    
            except Exception as e:
                logger.error(f"发送命令异常 (尝试 {attempt + 1}/{self.retry_count}): {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
        
        logger.error(f"命令发送最终失败: {command}")
        return None
    
    def _send_command_once(self, command: str) -> Optional[str]:
        """
        单次发送命令（基于旧版VHClientSender实现）

        Args:
            command: 命令字符串

        Returns:
            str: 响应内容，失败返回None
        """
        try:
            lock.acquire()
            logger.debug(f"发送VH命令: {command}")

            # 检查管道是否存在
            if not os.path.exists(self.write_pipe):
                logger.warning(f"写入管道不存在: {self.write_pipe}")
                return None

            # 基于旧版VHClientSender的实现方式
            write_fd = os.open(self.write_pipe, os.O_SYNC | os.O_CREAT | os.O_RDWR)
            os.write(write_fd, command.encode())
            os.write(write_fd, b"\n")

            if not os.path.exists(self.read_pipe):
                os.close(write_fd)
                logger.warning(f"读取管道不存在: {self.read_pipe}")
                return None

            read_fd = os.open(self.read_pipe, os.O_RDONLY)
            result = ""
            while True:
                temp = os.read(read_fd, 2048)
                if len(temp) == 0:
                    break
                result += temp.decode().replace('\x01', '')

            os.close(read_fd)
            os.close(write_fd)

            logger.debug(f"VH命令响应: {result[:100]}...")
            return result.strip()

        except Exception as e:
            logger.error(f"VH命令执行失败: {e}")
            return None
        finally:
            lock.release()
    
    def get_client_state(self) -> List[Dict]:
        """
        获取客户端状态和设备列表（使用缓存）

        Returns:
            List[Dict]: 设备信息列表
        """
        global cached_xml
        try:
            # 如果没有缓存，直接获取
            if not cached_xml:
                response = self.send_command("GET CLIENT STATE")
                if response:
                    cached_xml = response

            devices = self._parse_client_state_response(cached_xml)
            logger.debug(f"获取到 {len(devices)} 个设备")
            return devices

        except Exception as e:
            logger.error(f"获取客户端状态失败: {e}")
            return []

    def start_background_cache(self):
        """
        启动后台缓存线程（参考项目方式）
        """
        def _background_cache():
            global cached_xml
            while True:
                try:
                    response = self.send_command("GET CLIENT STATE")
                    if response:
                        cached_xml = response
                    time.sleep(3)  # 每3秒更新一次
                except Exception as e:
                    logger.warning(f"后台缓存更新异常: {e}")
                    time.sleep(3)

        cache_thread = threading.Thread(target=_background_cache, daemon=True)
        cache_thread.start()
        logger.info("VirtualHere后台缓存线程已启动")
    
    def get_device_info(self, device_address: str) -> Optional[Dict]:
        """
        获取特定设备信息
        
        Args:
            device_address: 设备地址
        
        Returns:
            Dict: 设备信息，失败返回None
        """
        try:
            command = f"DEVICE INFO,{device_address}"
            response = self.send_command(command)
            
            if response:
                return self._parse_device_info_response(response)
            
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
        
        return None
    
    def add_server(self, server_address: str) -> bool:
        """
        添加VirtualHere服务器
        
        Args:
            server_address: 服务器地址
        
        Returns:
            bool: 是否成功
        """
        try:
            command = f"MANUAL HUB ADD,{server_address}"
            response = self.send_command(command)
            
            # 检查响应是否表示成功
            if response and "OK" in response.upper():
                logger.info(f"服务器添加成功: {server_address}")
                return True
            else:
                logger.warning(f"服务器添加失败: {server_address}, 响应: {response}")
                return False
                
        except Exception as e:
            logger.error(f"添加服务器失败: {e}")
            return False
    
    def remove_all_servers(self) -> bool:
        """
        移除所有服务器
        
        Returns:
            bool: 是否成功
        """
        try:
            response = self.send_command("MANUAL HUB REMOVE ALL")
            
            if response and "OK" in response.upper():
                logger.info("所有服务器移除成功")
                return True
            else:
                logger.warning(f"移除服务器失败, 响应: {response}")
                return False
                
        except Exception as e:
            logger.error(f"移除服务器失败: {e}")
            return False
    
    def _parse_client_state_response(self, response: str) -> List[Dict]:
        """
        解析客户端状态响应（基于参考项目实现）

        Args:
            response: XML响应内容

        Returns:
            List[Dict]: 设备信息列表
        """
        if not response or len(response) == 0:
            return []

        try:
            # 使用xmltodict解析XML（参考项目方式）
            parsed_dict = xmltodict.parse(response, encoding='utf-8')

            if not parsed_dict.get("state"):
                return []

            if "server" not in parsed_dict["state"]:
                logger.warning("VirtualHere服务器未启动或无连接")
                return []

            servers = parsed_dict["state"]["server"]
            if not isinstance(servers, list):
                servers = [servers]

            devices = []
            for server in servers:
                connection = server.get("connection", {})
                hostname = connection.get("@hostname")
                server_name = connection.get("@serverName")

                if "device" in server:
                    server_devices = server["device"]
                    if not isinstance(server_devices, list):
                        server_devices = [server_devices]

                    for device in server_devices:
                        device_info = self._generate_device_info(device, hostname, server_name)
                        if device_info:
                            devices.append(device_info)

            return devices

        except Exception as e:
            logger.error(f"解析客户端状态失败: {e}")
            return []
    
    def _generate_device_info(self, device, hostname, server_name) -> Optional[Dict]:
        """
        生成设备信息（基于旧版VHClientSender实现）

        Args:
            device: 设备XML节点数据
            hostname: 主机名
            server_name: 服务器名称

        Returns:
            Dict: 设备信息字典
        """
        try:
            # 提取设备信息（与旧版VHClientSender保持一致）
            device_serial = device.get("@deviceSerial", "")
            device_address = device.get("@address", "")
            vendor_id = device.get("@idVendor", "")
            product_id = device.get("@idProduct", "")

            # 生成device_uuid（与旧版VHClientSender算法完全一致）
            if not device_serial or len(device_serial) == 0:
                # 没有序列号，使用vendor_id、product_id和address
                device_uuid = self._str_to_md5(f'{vendor_id}_{product_id}_{device_address}')
            else:
                # 有序列号，使用序列号和address
                device_uuid = self._str_to_md5(f'{device_serial}_{device_address}')

            return {
                "device_uuid": device_uuid,
                "server_name": server_name,
                "hostname": hostname,
                "connection_uuid": device.get("@connectionUUID", ""),
                "device_address": device_address,
                "device_serial": device_serial,
                "nick_name": device.get("@nickname", ""),
                "product": device.get("@product", ""),
                "product_id": product_id,
                "vendor": device.get("@vendor", ""),
                "vendor_id": vendor_id,
                "status": "available"
            }

        except Exception as e:
            logger.error(f"生成设备信息失败: {e}")
            return None

    def _str_to_md5(self, text: str) -> str:
        """
        字符串转MD5哈希

        Args:
            text: 输入字符串

        Returns:
            str: MD5哈希值
        """
        import hashlib
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def _parse_device_info_response(self, response: str) -> Optional[Dict]:
        """
        解析设备信息响应
        
        Args:
            response: 响应内容
        
        Returns:
            Dict: 设备详细信息
        """
        try:
            return {
                'details': response.strip(),
                'timestamp': time.time()
            }
        except Exception as e:
            logger.error(f"解析设备信息响应失败: {e}")
            return None
    
    def is_client_running(self) -> bool:
        """
        检查VirtualHere客户端是否运行

        Returns:
            bool: 是否运行
        """
        try:
            return os.path.exists(self.write_pipe) and os.path.exists(self.read_pipe)
        except Exception as e:
            logger.error(f"检查客户端状态失败: {e}")
            return False

    def rename_device(self, device_address: str, new_name: str) -> str:
        """
        重命名设备（基于旧版实现）

        Args:
            device_address: 设备地址
            new_name: 新名称

        Returns:
            str: 执行结果
        """
        try:
            # 注意：旧版注释说在某些情况下服务器改名后调用GET CLIENT INFO会获取不到东西
            # 这里保留原始实现但添加警告
            logger.warning("设备重命名功能可能在某些情况下导致设备信息获取异常")
            command = f'DEVICE RENAME,{device_address},"{new_name}"'
            return self.send_command(command) or ""
        except Exception as e:
            logger.error(f"重命名设备失败: {e}")
            return ""

    def stop_using_all(self) -> str:
        """
        停止使用所有设备

        Returns:
            str: 执行结果
        """
        try:
            return self.send_command("STOP USING ALL") or ""
        except Exception as e:
            logger.error(f"停止使用所有设备失败: {e}")
            return ""

    def list_licenses(self) -> str:
        """
        列出服务器许可证

        Returns:
            str: 许可证信息
        """
        try:
            return self.send_command("LIST LICENSES") or ""
        except Exception as e:
            logger.error(f"获取许可证信息失败: {e}")
            return ""

    def license_server(self, license_code: str) -> bool:
        """
        为服务器授权

        Args:
            license_code: 许可证代码

        Returns:
            bool: 授权是否成功
        """
        try:
            self.send_command(f"LICENSE SERVER,{license_code}")

            # 验证授权结果
            license_info = self.list_licenses()
            if license_info:
                license_parts = license_info.split(",")
                if len(license_parts) >= 3 and license_parts[2].strip() != "unlimited devices":
                    logger.error("授权失败，请确认授权码正确")
                    return False
                else:
                    logger.info("服务器授权成功")
                    return True
            return False

        except Exception as e:
            logger.error(f"服务器授权失败: {e}")
            return False

    def rename_server(self, server_name: str, port: int = 7575) -> str:
        """
        重命名服务器

        Args:
            server_name: 新服务器名称
            port: 服务器端口

        Returns:
            str: 执行结果
        """
        try:
            command = f"SERVER RENAME,127.0.0.1:{port},{server_name}"
            return self.send_command(command) or ""
        except Exception as e:
            logger.error(f"重命名服务器失败: {e}")
            return ""
