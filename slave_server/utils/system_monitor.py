# -*- coding: utf-8 -*-
"""
系统监控模块
提供系统资源监控、性能指标收集、健康检查功能
"""

import os
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
from utils.logger import get_logger

logger = get_logger('system_monitor')


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, check_interval: int = 60) -> None:
        """
        初始化系统监控器
        
        Args:
            check_interval: 检查间隔（秒）
        """
        self.check_interval = check_interval
        self.is_running = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.metrics_history: List[Dict[str, Any]] = []
        self.max_history_size = 1440  # 24小时的数据（每分钟一次）
        
    def start(self) -> None:
        """启动监控"""
        if self.is_running:
            logger.warning("系统监控已在运行")
            return
        
        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("系统监控已启动")
    
    def stop(self) -> None:
        """停止监控"""
        self.is_running = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        logger.info("系统监控已停止")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self.is_running:
            try:
                metrics = self.collect_metrics()
                self._store_metrics(metrics)
                self._check_alerts(metrics)
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(self.check_interval)
    
    def collect_metrics(self) -> Dict[str, Any]:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            # 网络统计
            network = psutil.net_io_counters()
            
            # 进程信息
            process_count = len(psutil.pids())
            
            # 系统负载（Linux/Unix）
            load_avg = None
            try:
                load_avg = os.getloadavg()
            except (OSError, AttributeError):
                # Windows系统不支持getloadavg
                pass
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count,
                    'load_avg': load_avg
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used,
                    'free': memory.free
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                },
                'processes': {
                    'count': process_count
                }
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            return {}
    
    def _store_metrics(self, metrics: Dict[str, Any]) -> None:
        """存储指标数据"""
        if not metrics:
            return
        
        self.metrics_history.append(metrics)
        
        # 保持历史数据大小限制
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history = self.metrics_history[-self.max_history_size:]
    
    def _check_alerts(self, metrics: Dict[str, Any]) -> None:
        """检查告警条件"""
        if not metrics:
            return
        
        # CPU使用率告警
        cpu_percent = metrics.get('cpu', {}).get('percent', 0)
        if cpu_percent > 90:
            logger.warning(f"CPU使用率过高: {cpu_percent:.1f}%")
        
        # 内存使用率告警
        memory_percent = metrics.get('memory', {}).get('percent', 0)
        if memory_percent > 90:
            logger.warning(f"内存使用率过高: {memory_percent:.1f}%")
        
        # 磁盘使用率告警
        disk_percent = metrics.get('disk', {}).get('percent', 0)
        if disk_percent > 90:
            logger.warning(f"磁盘使用率过高: {disk_percent:.1f}%")
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前系统指标"""
        return self.collect_metrics()
    
    def get_metrics_history(self, hours: int = 1) -> List[Dict[str, Any]]:
        """
        获取历史指标数据
        
        Args:
            hours: 获取最近几小时的数据
            
        Returns:
            历史指标数据列表
        """
        if not self.metrics_history:
            return []
        
        # 计算时间范围
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        filtered_metrics = []
        for metric in self.metrics_history:
            try:
                metric_time = datetime.fromisoformat(metric['timestamp'])
                if metric_time >= cutoff_time:
                    filtered_metrics.append(metric)
            except (KeyError, ValueError):
                continue
        
        return filtered_metrics
    
    def get_system_health(self) -> Dict[str, Any]:
        """
        获取系统健康状态
        
        Returns:
            系统健康状态信息
        """
        try:
            current_metrics = self.get_current_metrics()
            
            if not current_metrics:
                return {
                    'status': 'unknown',
                    'message': '无法获取系统指标'
                }
            
            # 评估系统健康状态
            cpu_percent = current_metrics.get('cpu', {}).get('percent', 0)
            memory_percent = current_metrics.get('memory', {}).get('percent', 0)
            disk_percent = current_metrics.get('disk', {}).get('percent', 0)
            
            issues = []
            
            if cpu_percent > 90:
                issues.append(f"CPU使用率过高: {cpu_percent:.1f}%")
            elif cpu_percent > 80:
                issues.append(f"CPU使用率较高: {cpu_percent:.1f}%")
            
            if memory_percent > 90:
                issues.append(f"内存使用率过高: {memory_percent:.1f}%")
            elif memory_percent > 80:
                issues.append(f"内存使用率较高: {memory_percent:.1f}%")
            
            if disk_percent > 90:
                issues.append(f"磁盘使用率过高: {disk_percent:.1f}%")
            elif disk_percent > 80:
                issues.append(f"磁盘使用率较高: {disk_percent:.1f}%")
            
            # 确定整体状态
            if not issues:
                status = 'healthy'
                message = '系统运行正常'
            elif any('过高' in issue for issue in issues):
                status = 'critical'
                message = '系统资源紧张'
            else:
                status = 'warning'
                message = '系统资源使用较高'
            
            return {
                'status': status,
                'message': message,
                'issues': issues,
                'metrics': current_metrics,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取系统健康状态失败: {e}")
            return {
                'status': 'error',
                'message': f'健康检查失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def is_monitoring(self) -> bool:
        """检查监控是否正在运行"""
        return self.is_running and self.monitor_thread and self.monitor_thread.is_alive()


# 全局系统监控实例
system_monitor = SystemMonitor()


def get_system_monitor() -> SystemMonitor:
    """获取系统监控实例"""
    return system_monitor


def start_system_monitoring() -> None:
    """启动系统监控"""
    system_monitor.start()


def stop_system_monitoring() -> None:
    """停止系统监控"""
    system_monitor.stop()


def get_system_health() -> Dict[str, Any]:
    """获取系统健康状态"""
    return system_monitor.get_system_health()


def get_current_metrics() -> Dict[str, Any]:
    """获取当前系统指标"""
    return system_monitor.get_current_metrics()
