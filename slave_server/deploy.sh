#!/bin/bash
# OmniLink从服务器部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 默认配置
DEPLOY_ENV="production"
DEPLOY_MODE="docker"
PROJECT_NAME="omnilink-slave-server"
BACKUP_ENABLED=true
HEALTH_CHECK_ENABLED=true
ROLLBACK_ENABLED=true

# 显示帮助信息
show_help() {
    cat << EOF
OmniLink从服务器部署脚本

用法: $0 [选项]

选项:
    -e, --env ENV           部署环境 (development|production) [默认: production]
    -m, --mode MODE         部署模式 (docker|native) [默认: docker]
    -b, --backup            启用部署前备份 [默认: 启用]
    -n, --no-backup         禁用部署前备份
    -c, --health-check      启用健康检查 [默认: 启用]
    -r, --rollback          启用回滚功能 [默认: 启用]
    -h, --help              显示此帮助信息

示例:
    $0                      # 使用默认配置部署
    $0 -e development       # 部署到开发环境
    $0 -m native            # 使用原生模式部署
    $0 -n                   # 禁用备份
EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                DEPLOY_ENV="$2"
                shift 2
                ;;
            -m|--mode)
                DEPLOY_MODE="$2"
                shift 2
                ;;
            -b|--backup)
                BACKUP_ENABLED=true
                shift
                ;;
            -n|--no-backup)
                BACKUP_ENABLED=false
                shift
                ;;
            -c|--health-check)
                HEALTH_CHECK_ENABLED=true
                shift
                ;;
            -r|--rollback)
                ROLLBACK_ENABLED=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log_info "操作系统: Linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        log_info "操作系统: macOS"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    # 检查必需的命令
    local required_commands=("git" "curl")
    
    if [[ "$DEPLOY_MODE" == "docker" ]]; then
        required_commands+=("docker" "docker-compose")
    else
        required_commands+=("python3" "pip3")
    fi
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "缺少必需的命令: $cmd"
            exit 1
        else
            log_debug "找到命令: $cmd"
        fi
    done
    
    # 检查Docker版本
    if [[ "$DEPLOY_MODE" == "docker" ]]; then
        local docker_version=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log_info "Docker版本: $docker_version"
        
        local compose_version=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        log_info "Docker Compose版本: $compose_version"
    fi
    
    log_info "系统要求检查完成"
}

# 创建备份
create_backup() {
    if [[ "$BACKUP_ENABLED" != true ]]; then
        log_info "跳过备份（已禁用）"
        return 0
    fi
    
    log_info "创建部署前备份..."
    
    local backup_dir="backups/deploy_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份配置文件
    if [[ -d "config" ]]; then
        cp -r config "$backup_dir/"
        log_debug "已备份配置文件"
    fi
    
    # 备份数据文件
    if [[ -d "data" ]]; then
        cp -r data "$backup_dir/"
        log_debug "已备份数据文件"
    fi
    
    # 备份日志文件
    if [[ -d "logs" ]]; then
        cp -r logs "$backup_dir/"
        log_debug "已备份日志文件"
    fi
    
    # 备份Docker配置
    if [[ "$DEPLOY_MODE" == "docker" ]]; then
        if [[ -f "docker-compose.yml" ]]; then
            cp docker-compose.yml "$backup_dir/"
        fi
        if [[ -f ".env" ]]; then
            cp .env "$backup_dir/"
        fi
    fi
    
    # 创建备份信息文件
    cat > "$backup_dir/backup_info.txt" << EOF
备份时间: $(date)
部署环境: $DEPLOY_ENV
部署模式: $DEPLOY_MODE
Git提交: $(git rev-parse HEAD 2>/dev/null || echo "N/A")
Git分支: $(git branch --show-current 2>/dev/null || echo "N/A")
EOF
    
    log_info "备份已创建: $backup_dir"
    echo "$backup_dir" > .last_backup
}

# 准备部署环境
prepare_environment() {
    log_info "准备部署环境..."
    
    # 创建必要的目录
    local dirs=("data" "logs" "config" "backups" "tmp")
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_debug "创建目录: $dir"
        fi
    done
    
    # 设置目录权限
    chmod 755 data logs config backups tmp
    
    # 复制配置文件模板
    if [[ ! -f "config/slave_server.ini" ]]; then
        if [[ -f "config/slave_server.ini.template" ]]; then
            cp config/slave_server.ini.template config/slave_server.ini
            log_info "已复制配置文件模板"
        else
            log_warn "配置文件模板不存在"
        fi
    fi
    
    # 复制环境变量文件
    if [[ ! -f ".env" ]]; then
        if [[ "$DEPLOY_ENV" == "development" && -f "config/docker.env.example" ]]; then
            cp config/docker.env.example .env
            log_info "已复制开发环境变量文件"
        elif [[ -f ".env.template" ]]; then
            cp .env.template .env
            log_info "已复制环境变量模板"
        fi
    fi
    
    log_info "环境准备完成"
}

# 安装VirtualHere二进制文件（支持多架构）
install_virtualhere() {
    local arch=$(uname -m)

    log_info "检测到系统架构: $arch"

    case $arch in
        armv7l|armv6l|arm*)
            log_info "ARM架构检测，优先使用本地ARM版本"
            if [[ -f "virtualhere/vhusbdarmpi3" ]]; then
                cp virtualhere/vhusbdarmpi3 vhusbd
                chmod +x vhusbd
                log_info "使用本地ARM服务器: virtualhere/vhusbdarmpi3"
            else
                log_warn "本地ARM版本不存在，下载ARM版本"
                curl -o vhusbd https://www.virtualhere.com/sites/default/files/usbserver/vhusbdarm
                chmod +x vhusbd
            fi

            # 安装ARM客户端
            if [[ -f "virtualhere/vhclientarmhf" ]]; then
                cp virtualhere/vhclientarmhf vhclient
                chmod +x vhclient
                log_info "使用本地ARM客户端: virtualhere/vhclientarmhf"
            fi
            ;;
        x86_64)
            log_info "x86_64架构，下载对应版本"
            if [[ ! -f "vhusbd" ]]; then
                curl -o vhusbd https://www.virtualhere.com/sites/default/files/usbserver/vhusbd
                chmod +x vhusbd
            fi
            ;;
        *)
            log_error "不支持的架构: $arch"
            return 1
            ;;
    esac

    log_info "VirtualHere安装完成"
    return 0
}

# Docker部署
deploy_docker() {
    log_info "开始Docker部署..."
    
    # 选择docker-compose文件
    local compose_file="docker-compose.yml"
    local compose_args=""
    
    if [[ "$DEPLOY_ENV" == "development" ]]; then
        if [[ -f "docker-compose.dev.yml" ]]; then
            compose_args="-f docker-compose.yml -f docker-compose.dev.yml"
            log_info "使用开发环境配置"
        fi
    fi
    
    # 拉取最新镜像
    log_info "拉取最新镜像..."
    docker-compose $compose_args pull
    
    # 构建镜像
    log_info "构建应用镜像..."
    docker-compose $compose_args build --no-cache
    
    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose $compose_args down
    
    # 启动服务
    log_info "启动服务..."
    docker-compose $compose_args up -d
    
    log_info "Docker部署完成"
}

# 原生部署
deploy_native() {
    log_info "开始原生部署..."
    
    # 检查Python版本
    local python_version=$(python3 --version | grep -oE '[0-9]+\.[0-9]+')
    log_info "Python版本: $python_version"
    
    # 创建虚拟环境
    if [[ ! -d "venv" ]]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    log_info "安装Python依赖..."
    pip install -r requirements.txt
    
    # 安装VirtualHere二进制文件（支持多架构）
    install_virtualhere
    
    # 停止现有服务
    if [[ -f "slave_server.pid" ]]; then
        local pid=$(cat slave_server.pid)
        if kill -0 "$pid" 2>/dev/null; then
            log_info "停止现有服务 (PID: $pid)..."
            kill "$pid"
            sleep 2
        fi
        rm -f slave_server.pid
    fi
    
    # 启动服务
    log_info "启动从服务器..."
    nohup python main.py > logs/slave_server.out 2>&1 &
    echo $! > slave_server.pid
    
    log_info "原生部署完成"
}

# 健康检查
health_check() {
    if [[ "$HEALTH_CHECK_ENABLED" != true ]]; then
        log_info "跳过健康检查（已禁用）"
        return 0
    fi
    
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    local health_url="http://localhost:8889/api/system/health"
    
    while [[ $attempt -le $max_attempts ]]; do
        log_debug "健康检查尝试 $attempt/$max_attempts"
        
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            log_info "健康检查通过"
            return 0
        fi
        
        sleep 2
        ((attempt++))
    done
    
    log_error "健康检查失败"
    return 1
}

# 部署后验证
post_deploy_verification() {
    log_info "执行部署后验证..."
    
    # 检查服务状态
    if [[ "$DEPLOY_MODE" == "docker" ]]; then
        local running_containers=$(docker-compose ps --services --filter "status=running")
        log_info "运行中的容器: $running_containers"
    else
        if [[ -f "slave_server.pid" ]]; then
            local pid=$(cat slave_server.pid)
            if kill -0 "$pid" 2>/dev/null; then
                log_info "从服务器正在运行 (PID: $pid)"
            else
                log_error "从服务器未运行"
                return 1
            fi
        fi
    fi
    
    # API测试
    log_info "测试API端点..."
    local api_tests=(
        "http://localhost:8889/api/system/health"
        "http://localhost:8889/api/system/status"
        "http://localhost:8889/api/devices"
    )
    
    for url in "${api_tests[@]}"; do
        if curl -f -s "$url" > /dev/null; then
            log_debug "API测试通过: $url"
        else
            log_warn "API测试失败: $url"
        fi
    done
    
    log_info "部署后验证完成"
}

# 回滚功能
rollback() {
    if [[ "$ROLLBACK_ENABLED" != true ]]; then
        log_error "回滚功能已禁用"
        return 1
    fi
    
    if [[ ! -f ".last_backup" ]]; then
        log_error "没有找到备份信息"
        return 1
    fi
    
    local backup_dir=$(cat .last_backup)
    if [[ ! -d "$backup_dir" ]]; then
        log_error "备份目录不存在: $backup_dir"
        return 1
    fi
    
    log_warn "开始回滚到备份: $backup_dir"
    
    # 停止服务
    if [[ "$DEPLOY_MODE" == "docker" ]]; then
        docker-compose down
    else
        if [[ -f "slave_server.pid" ]]; then
            local pid=$(cat slave_server.pid)
            if kill -0 "$pid" 2>/dev/null; then
                kill "$pid"
            fi
        fi
    fi
    
    # 恢复文件
    if [[ -d "$backup_dir/config" ]]; then
        cp -r "$backup_dir/config" ./
    fi
    
    if [[ -d "$backup_dir/data" ]]; then
        cp -r "$backup_dir/data" ./
    fi
    
    # 重新启动服务
    if [[ "$DEPLOY_MODE" == "docker" ]]; then
        docker-compose up -d
    else
        nohup python main.py > logs/slave_server.out 2>&1 &
        echo $! > slave_server.pid
    fi
    
    log_info "回滚完成"
}

# 清理函数
cleanup() {
    log_info "执行清理..."
    
    # 清理临时文件
    rm -f /tmp/omnilink_deploy_*
    
    # 清理旧的备份（保留最近10个）
    if [[ -d "backups" ]]; then
        local backup_count=$(ls -1 backups/ | wc -l)
        if [[ $backup_count -gt 10 ]]; then
            log_info "清理旧备份..."
            ls -1t backups/ | tail -n +11 | xargs -I {} rm -rf "backups/{}"
        fi
    fi
    
    log_info "清理完成"
}

# 信号处理
trap cleanup EXIT
trap 'log_error "部署被中断"; exit 1' INT TERM

# 主函数
main() {
    log_info "开始部署 OmniLink从服务器"
    log_info "部署环境: $DEPLOY_ENV"
    log_info "部署模式: $DEPLOY_MODE"
    
    # 检查系统要求
    check_requirements
    
    # 创建备份
    create_backup
    
    # 准备环境
    prepare_environment
    
    # 执行部署
    if [[ "$DEPLOY_MODE" == "docker" ]]; then
        deploy_docker
    else
        deploy_native
    fi
    
    # 健康检查
    if ! health_check; then
        log_error "健康检查失败，考虑回滚"
        if [[ "$ROLLBACK_ENABLED" == true ]]; then
            read -p "是否要回滚? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                rollback
                exit 1
            fi
        fi
        exit 1
    fi
    
    # 部署后验证
    post_deploy_verification
    
    log_info "部署成功完成!"
    log_info "服务地址: http://localhost:8889"
    log_info "健康检查: http://localhost:8889/api/system/health"
}

# 解析参数并运行
parse_args "$@"
main
