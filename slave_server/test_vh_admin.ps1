# VirtualHere管理员权限测试脚本
# 简化版本，用于快速验证VirtualHere功能

Write-Host "VirtualHere管理员权限测试" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

# 检查管理员权限
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
$isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "错误: 需要管理员权限" -ForegroundColor Red
    Write-Host "请以管理员身份运行PowerShell" -ForegroundColor Yellow
    exit 1
}

Write-Host "✓ 管理员权限验证通过" -ForegroundColor Green

# 检查VirtualHere文件
$vhPath = "virtualhere\vhusbdwin64.exe"
if (!(Test-Path $vhPath)) {
    Write-Host "错误: VirtualHere文件不存在: $vhPath" -ForegroundColor Red
    exit 1
}

Write-Host "✓ VirtualHere文件存在" -ForegroundColor Green

# 创建简单配置
$configContent = @"
ServerHubName=OmniLink-Test-Hub
ServerHubPort=7575
AutoFind=0
DeviceNicknames=1
AutoAddDevices=1
LogLevel=2
"@

Set-Content -Path "virtualhere\test.conf" -Value $configContent -Encoding UTF8
Write-Host "✓ 配置文件已创建" -ForegroundColor Green

# 启动VirtualHere
Write-Host ""
Write-Host "启动VirtualHere服务器..." -ForegroundColor Yellow

try {
    $process = Start-Process -FilePath $vhPath -ArgumentList "-b", "7575", "-c", "virtualhere\test.conf", "-r" -PassThru -NoNewWindow
    
    Start-Sleep -Seconds 3
    
    if ($process.HasExited) {
        Write-Host "VirtualHere启动失败" -ForegroundColor Red
        Write-Host "退出代码: $($process.ExitCode)" -ForegroundColor Red
    } else {
        Write-Host "✓ VirtualHere启动成功!" -ForegroundColor Green
        Write-Host "进程ID: $($process.Id)" -ForegroundColor Cyan
        Write-Host "监听端口: 7575" -ForegroundColor Cyan
        
        # 检查端口监听
        Start-Sleep -Seconds 2
        $listening = Get-NetTCPConnection -LocalPort 7575 -ErrorAction SilentlyContinue
        if ($listening) {
            Write-Host "✓ 端口7575正在监听" -ForegroundColor Green
        } else {
            Write-Host "⚠ 端口7575未监听" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "VirtualHere服务器运行中..." -ForegroundColor Green
        Write-Host "按任意键停止服务器..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
        # 停止服务器
        Write-Host "正在停止VirtualHere..." -ForegroundColor Yellow
        $process.Kill()
        $process.WaitForExit(5000)
        Write-Host "✓ VirtualHere已停止" -ForegroundColor Green
    }
} catch {
    Write-Host "启动VirtualHere时发生错误: $($_.Exception.Message)" -ForegroundColor Red
}
