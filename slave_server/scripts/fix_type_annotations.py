#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复类型注解脚本
"""

import re
from pathlib import Path

def fix_flask_routes(file_path: Path) -> Any:
    """修复Flask路由函数的类型注解"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找Flask路由函数
    route_pattern = r'@\w+_bp\.route\([^)]+\)\s*\ndef\s+(\w+)\s*\([^)]*\):'
    
    def add_return_type(match) -> Any:
        func_name = match.group(1)
        full_match = match.group(0)
        # 在函数定义后添加返回类型注解
        return full_match.replace(f'def {func_name}(', f'def {func_name}(').replace('):', ') -> Union[Response, tuple]:')
    
    # 检查是否已经有Union导入
    if 'Union[Response, tuple]' in content and 'from typing import' in content:
        if 'Union' not in content.split('from typing import')[1].split('\n')[0]:
            # 添加Union到现有的typing导入
            content = re.sub(
                r'from typing import ([^\n]+)',
                r'from typing import \1, Union',
                content
            )
    
    # 应用类型注解修复
    content = re.sub(route_pattern, add_return_type, content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info(f"已修复 {file_path.name} 的类型注解")

def main() -> None:
    """主函数"""
    project_root = Path(__file__).parent.parent
    
    # 修复设备服务的剩余函数
    device_service = project_root / 'restful/device_service.py'
    if device_service.exists():
        with open(device_service, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 手动修复剩余的函数
        functions_to_fix = [
            ('def get_device(device_uuid) -> Any:', 'def get_device(device_uuid: str) -> Union[Response, tuple]:'),
            ('def connect_device(device_uuid) -> Any:', 'def connect_device(device_uuid: str) -> Union[Response, tuple]:'),
            ('def disconnect_device(device_uuid) -> Any:', 'def disconnect_device(device_uuid: str) -> Union[Response, tuple]:'),
            ('def reset_device(device_uuid) -> Any:', 'def reset_device(device_uuid: str) -> Union[Response, tuple]:'),
            ('def rename_device(device_uuid) -> Any:', 'def rename_device(device_uuid: str) -> Union[Response, tuple]:'),
            ('def get_device_statistics() -> None:', 'def get_device_statistics() -> Union[Response, tuple]:'),
            ('def set_device_visibility(device_uuid) -> Any:', 'def set_device_visibility(device_uuid: str) -> Union[Response, tuple]:'),
            ('def set_device_enabled(device_uuid) -> Any:', 'def set_device_enabled(device_uuid: str) -> Union[Response, tuple]:')
        ]
        
        for old_def, new_def in functions_to_fix:
            content = content.replace(old_def, new_def)
        
        with open(device_service, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"已修复 {device_service.name} 的所有类型注解")

if __name__ == '__main__':
    main()
