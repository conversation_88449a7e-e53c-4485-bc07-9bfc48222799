#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动修复生产环境问题脚本
基于审查报告自动修复发现的问题
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Any

class ProductionIssueAutoFixer:
    """生产环境问题自动修复器"""
    
    def __init__(self) -> None:
        self.project_root = Path(__file__).parent.parent
        self.report_file = self.project_root / 'comprehensive_production_audit_report.json'
        self.fixes_applied = []
        
    def load_audit_report(self) -> Dict[str, Any]:
        """加载审查报告"""
        try:
            with open(self.report_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载审查报告失败: {e}")
            return {}
    
    def auto_fix_all_issues(self) -> None:
        """自动修复所有可修复的问题"""
        print("🔧 开始自动修复生产环境问题...")
        
        report = self.load_audit_report()
        if not report:
            return
        
        # 修复安全问题
        self.fix_security_issues(report.get('security_hardening', {}).get('issues', []))
        
        # 修复代码质量问题
        self.fix_code_quality_issues(report.get('code_quality', {}).get('issues', []))
        
        # 修复架构问题
        self.fix_architecture_issues(report.get('architecture_integrity', {}).get('issues', []))
        
        # 生成修复报告
        self.generate_fix_report()
    
    def fix_security_issues(self, issues: List[Dict]) -> Any:
        """修复安全问题"""
        print("🔒 修复安全问题...")
        
        for issue in issues:
            if issue['type'] == 'debug_code_found':
                self.fix_debug_code(issue)
    
    def fix_debug_code(self, issue: Dict) -> Any:
        """修复调试代码"""
        file_path = Path(issue['file'])
        
        if not file_path.exists():
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 替换print语句为logger
            if 'print(' in content:
                lines = content.split('\n')
                modified_lines = []
                
                for line in lines:
                    if 'print(' in line and 'def print' not in line and 'logger' not in line:
                        # 检查是否已经有logger导入
                        if 'from utils.logger import get_logger' not in content:
                            # 需要添加logger导入
                            if not hasattr(self, '_logger_import_added'):
                                self._add_logger_import(file_path)
                                self._logger_import_added = True
                        
                        # 替换print为logger.info
                        modified_line = re.sub(
                            r'print\s*\(([^)]+)\)',
                            r'logger.info(\1)',
                            line
                        )
                        modified_lines.append(modified_line)
                    else:
                        modified_lines.append(line)
                
                content = '\n'.join(modified_lines)
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append({
                    'type': 'debug_code_fix',
                    'file': str(file_path),
                    'description': '替换print语句为logger.info'
                })
                
                print(f"   ✅ 修复调试代码: {file_path.name}")
        
        except Exception as e:
            print(f"   ❌ 修复失败 {file_path}: {e}")
    
    def _add_logger_import(self, file_path: Path) -> Any:
        """添加logger导入"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找合适的位置插入logger导入
            lines = content.split('\n')
            insert_index = 0
            
            # 找到最后一个import语句的位置
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    insert_index = i + 1
            
            # 插入logger导入
            logger_import = 'from utils.logger import get_logger'
            if logger_import not in content:
                lines.insert(insert_index, logger_import)
                
                # 添加logger实例化
                # 查找类定义或函数定义
                for i, line in enumerate(lines[insert_index:], insert_index):
                    if line.strip().startswith('class ') or line.strip().startswith('def '):
                        # 在类或函数定义前添加logger
                        logger_instance = f"\nlogger = get_logger('{file_path.stem}')\n"
                        lines.insert(i, logger_instance)
                        break
                
                content = '\n'.join(lines)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
        
        except Exception as e:
            print(f"   ⚠️  添加logger导入失败 {file_path}: {e}")
    
    def fix_code_quality_issues(self, issues: List[Dict]) -> Any:
        """修复代码质量问题"""
        print("📝 修复代码质量问题...")
        
        for issue in issues:
            if issue['type'] == 'unsafe_file_operations':
                self.fix_unsafe_file_operations(issue)
            elif issue['type'] == 'bare_except_blocks':
                self.fix_bare_except_blocks(issue)
    
    def fix_unsafe_file_operations(self, issue: Dict) -> Any:
        """修复不安全的文件操作"""
        file_path = Path(issue['file'])
        
        if not file_path.exists():
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 查找并替换不安全的文件操作
            # 这是一个复杂的替换，需要谨慎处理
            # 简单情况：open(...) -> with open(...) as f:
            
            # 使用正则表达式查找简单的文件打开操作
            pattern = r'(\s*)(\w+)\s*=\s*open\s*\(([^)]+)\)\s*\n'
            matches = re.finditer(pattern, content)
            
            for match in reversed(list(matches)):  # 从后往前替换，避免位置偏移
                indent = match.group(1)
                var_name = match.group(2)
                args = match.group(3)
                
                # 替换为with语句
                replacement = f"{indent}with open({args}) as {var_name}:\n"
                content = content[:match.start()] + replacement + content[match.end():]
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append({
                    'type': 'file_operation_fix',
                    'file': str(file_path),
                    'description': '修复不安全的文件操作'
                })
                
                print(f"   ✅ 修复文件操作: {file_path.name}")
        
        except Exception as e:
            print(f"   ❌ 修复失败 {file_path}: {e}")
    
    def fix_bare_except_blocks(self, issue: Dict) -> Any:
        """修复裸露的except块"""
        file_path = Path(issue['file'])
        
        if not file_path.exists():
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 替换裸露的except为具体的异常类型
            content = re.sub(
                r'(\s+)except\s*:',
                r'\1except Exception as e:',
                content
            )
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append({
                    'type': 'except_block_fix',
                    'file': str(file_path),
                    'description': '修复裸露的except块'
                })
                
                print(f"   ✅ 修复except块: {file_path.name}")
        
        except Exception as e:
            print(f"   ❌ 修复失败 {file_path}: {e}")
    
    def fix_architecture_issues(self, issues: List[Dict]) -> Any:
        """修复架构问题"""
        print("🏗️  修复架构问题...")
        
        for issue in issues:
            if issue['type'] == 'inconsistent_logging':
                # 这个问题已经在安全问题修复中处理了
                pass
    
    def generate_fix_report(self) -> None:
        """生成修复报告"""
        print(f"\n📊 修复完成，共应用 {len(self.fixes_applied)} 个修复:")
        
        fix_types = {}
        for fix in self.fixes_applied:
            fix_type = fix['type']
            if fix_type not in fix_types:
                fix_types[fix_type] = 0
            fix_types[fix_type] += 1
        
        for fix_type, count in fix_types.items():
            print(f"   📝 {fix_type}: {count} 个修复")
        
        # 保存修复报告
        fix_report = {
            'timestamp': str(Path(__file__).stat().st_mtime),
            'total_fixes': len(self.fixes_applied),
            'fixes_by_type': fix_types,
            'detailed_fixes': self.fixes_applied
        }
        
        report_file = self.project_root / 'production_fixes_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(fix_report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 修复报告已保存到: {report_file}")

def main() -> None:
    """主函数"""
    fixer = ProductionIssueAutoFixer()
    fixer.auto_fix_all_issues()
    
    print("\n🎯 建议运行以下命令验证修复效果:")
    print("   python scripts/comprehensive_production_audit.py")
    
    return 0

if __name__ == '__main__':
    exit(main())
