#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于需求文档的深度功能验证脚本
对照doc/从服务器构建需求和规范.md进行全面验证
"""

import os
import ast
import sys
import json
import importlib.util
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional
from utils.logger import get_logger

logger = get_logger('deep_functional_verification')


class DeepFunctionalVerifier:
    """深度功能验证器"""
    
    def __init__(self) -> None:
        self.project_root = Path(__file__).parent.parent
        self.requirements_doc = self.project_root / "doc/从服务器构建需求和规范.md"
        self.verification_results = {
            'requirement_compliance': {'status': 'unknown', 'issues': [], 'score': 0},
            'code_syntax_verification': {'status': 'unknown', 'issues': [], 'score': 0},
            'module_integration_verification': {'status': 'unknown', 'issues': [], 'score': 0},
            'end_to_end_flow_verification': {'status': 'unknown', 'issues': [], 'score': 0},
            'runtime_environment_verification': {'status': 'unknown', 'issues': [], 'score': 0},
            'overall_assessment': {'status': 'unknown', 'critical_issues': [], 'recommendations': []}
        }
        
    def conduct_deep_verification(self) -> Dict[str, Any]:
        """执行深度功能验证"""
        logger.info("🔍 开始基于需求文档的深度功能验证...")
        logger.info("📋 参考文档: doc/从服务器构建需求和规范.md")
        
        # 1. 需求文档对照验证
        self.verify_requirement_compliance()
        
        # 2. 单文件级代码验证
        self.verify_code_syntax()
        
        # 3. 多文件协作验证
        self.verify_module_integration()
        
        # 4. 端到端功能流程验证
        self.verify_end_to_end_flows()
        
        # 5. 实际运行环境验证
        self.verify_runtime_environment()
        
        # 6. 生成综合评估
        self.generate_overall_assessment()
        
        return self.verification_results
    
    def verify_requirement_compliance(self) -> None:
        """需求文档对照验证"""
        logger.info("📋 执行需求文档对照验证...")
        
        issues = []
        score = 0
        
        # 定义需求检查项
        requirements = {
            'usb_device_management': {
                'device_discovery': ['utils/device_monitor.py', 'tasks/device_monitor.py'],
                'device_status_monitoring': ['utils/device_monitor.py', 'tasks/device_monitor.py'],
                'device_metadata_management': ['db/models.py', 'db/device_dao.py']
            },
            'virtualhere_integration': {
                'vh_server_management': ['utils/vh_server_manager.py'],
                'vh_client_management': ['utils/vh_client.py']
            },
            'master_slave_communication': {
                'slave_registration': ['utils/master_communication.py'],
                'heartbeat_mechanism': ['tasks/heartbeat.py'],
                'device_status_reporting': ['utils/master_communication.py'],
                'command_receiving': ['restful/command_service.py']  # 可能缺失
            },
            'api_interfaces': {
                'device_api': ['restful/device_service.py'],
                'system_api': ['restful/system_service.py'],
                'config_api': ['restful/config_service.py']
            }
        }
        
        total_requirements = 0
        met_requirements = 0
        
        for category, subcategories in requirements.items():
            for requirement, expected_files in subcategories.items():
                total_requirements += 1
                
                # 检查是否有对应的实现文件
                implementation_found = False
                for file_path in expected_files:
                    full_path = self.project_root / file_path
                    if full_path.exists():
                        implementation_found = True
                        break
                
                if implementation_found:
                    met_requirements += 1
                    # 进一步检查实现完整性
                    implementation_issues = self._check_requirement_implementation(
                        category, requirement, expected_files
                    )
                    issues.extend(implementation_issues)
                else:
                    issues.append({
                        'type': 'missing_implementation',
                        'severity': 'high',
                        'category': category,
                        'requirement': requirement,
                        'expected_files': expected_files,
                        'message': f"缺少{requirement}的实现"
                    })
        
        # 计算得分
        score = (met_requirements / total_requirements) * 100 if total_requirements > 0 else 0
        
        self.verification_results['requirement_compliance'] = {
            'status': 'passed' if score >= 80 else 'failed',
            'issues': issues,
            'score': score,
            'met_requirements': met_requirements,
            'total_requirements': total_requirements
        }
    
    def _check_requirement_implementation(self, category: str, requirement: str, expected_files: List[str]) -> List[Dict]:
        """检查具体需求的实现完整性"""
        issues = []
        
        for file_path in expected_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 根据需求类型检查特定的实现
                if requirement == 'device_discovery':
                    if 'discover_devices' not in content and 'get_client_state' not in content:
                        issues.append({
                            'type': 'incomplete_implementation',
                            'severity': 'medium',
                            'file': file_path,
                            'requirement': requirement,
                            'message': '缺少设备发现功能的核心方法'
                        })
                
                elif requirement == 'vh_server_management':
                    required_methods = ['start_server', 'stop_server', 'restart_server']
                    for method in required_methods:
                        if method not in content:
                            issues.append({
                                'type': 'missing_method',
                                'severity': 'medium',
                                'file': file_path,
                                'requirement': requirement,
                                'method': method,
                                'message': f'缺少VH服务器管理方法: {method}'
                            })
                
                elif requirement == 'vh_client_management':
                    if 'send_vh_command' not in content and 'send_command' not in content:
                        issues.append({
                            'type': 'incomplete_implementation',
                            'severity': 'medium',
                            'file': file_path,
                            'requirement': requirement,
                            'message': '缺少VH客户端命令发送功能'
                        })
                
                # KCPTun功能已在新架构下完全移除，无需检查
                
                elif requirement == 'slave_registration':
                    if 'register' not in content.lower():
                        issues.append({
                            'type': 'incomplete_implementation',
                            'severity': 'high',
                            'file': file_path,
                            'requirement': requirement,
                            'message': '缺少从服务器注册功能'
                        })
                
                elif requirement == 'heartbeat_mechanism':
                    if 'heartbeat' not in content.lower():
                        issues.append({
                            'type': 'incomplete_implementation',
                            'severity': 'high',
                            'file': file_path,
                            'requirement': requirement,
                            'message': '缺少心跳机制实现'
                        })
                
            except Exception as e:
                issues.append({
                    'type': 'file_analysis_error',
                    'severity': 'medium',
                    'file': file_path,
                    'requirement': requirement,
                    'message': f'文件分析失败: {e}'
                })
        
        return issues
    
    def verify_code_syntax(self) -> None:
        """单文件级代码验证"""
        logger.info("🔧 执行单文件级代码验证...")
        
        issues = []
        score = 0
        
        # 获取所有Python文件
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]
        
        total_files = len(python_files)
        valid_files = 0
        
        for file_path in python_files:
            try:
                # 语法检查
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 编译检查语法
                compile(content, str(file_path), 'exec')
                
                # AST解析检查
                tree = ast.parse(content)
                
                # 检查导入依赖
                import_issues = self._check_imports(file_path, tree)
                issues.extend(import_issues)
                
                # 检查函数完整性
                function_issues = self._check_function_completeness(file_path, tree)
                issues.extend(function_issues)
                
                if not import_issues and not function_issues:
                    valid_files += 1
                
            except SyntaxError as e:
                issues.append({
                    'type': 'syntax_error',
                    'severity': 'high',
                    'file': str(file_path.relative_to(self.project_root)),
                    'line': e.lineno,
                    'message': f'语法错误: {e.msg}'
                })
            except Exception as e:
                issues.append({
                    'type': 'file_verification_error',
                    'severity': 'medium',
                    'file': str(file_path.relative_to(self.project_root)),
                    'message': f'文件验证失败: {e}'
                })
        
        # 计算得分
        score = (valid_files / total_files) * 100 if total_files > 0 else 0
        
        self.verification_results['code_syntax_verification'] = {
            'status': 'passed' if score >= 90 else 'failed',
            'issues': issues,
            'score': score,
            'valid_files': valid_files,
            'total_files': total_files
        }
    
    def _check_imports(self, file_path: Path, tree: ast.AST) -> List[Dict]:
        """检查导入语句的正确性"""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ImportFrom):
                if node.module:
                    # 检查相对导入
                    if node.module.startswith('.'):
                        continue  # 跳过相对导入检查
                    
                    # 检查项目内模块导入
                    if node.module.split('.')[0] in ['utils', 'db', 'restful', 'tasks']:
                        module_parts = node.module.split('.')
                        module_path = self.project_root / '/'.join(module_parts)
                        
                        if not (module_path.with_suffix('.py').exists() or 
                               (module_path / '__init__.py').exists()):
                            issues.append({
                                'type': 'missing_import_module',
                                'severity': 'high',
                                'file': str(file_path.relative_to(self.project_root)),
                                'line': node.lineno,
                                'module': node.module,
                                'message': f'导入的模块不存在: {node.module}'
                            })
        
        return issues
    
    def _check_function_completeness(self, file_path: Path, tree: ast.AST) -> List[Dict]:
        """检查函数实现的完整性"""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # 检查函数体是否只有pass或docstring
                if len(node.body) == 1:
                    if isinstance(node.body[0], ast.Pass):
                        issues.append({
                            'type': 'incomplete_function',
                            'severity': 'medium',
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': node.lineno,
                            'function': node.name,
                            'message': f'函数只有pass语句，缺少实现: {node.name}'
                        })
                    elif (isinstance(node.body[0], ast.Expr) and 
                          isinstance(node.body[0].value, ast.Constant) and
                          isinstance(node.body[0].value.value, str)):
                        # 只有docstring
                        issues.append({
                            'type': 'incomplete_function',
                            'severity': 'medium',
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': node.lineno,
                            'function': node.name,
                            'message': f'函数只有文档字符串，缺少实现: {node.name}'
                        })
        
        return issues
    
    def verify_module_integration(self) -> None:
        """多文件协作验证"""
        logger.info("🔗 执行多文件协作验证...")
        
        issues = []
        score = 0
        
        # 检查关键模块间的接口调用
        integration_checks = [
            {
                'caller': 'tasks/device_monitor.py',
                'callee': 'utils/vh_client.py',
                'expected_calls': ['send_command', 'get_client_state']
            },
            {
                'caller': 'tasks/heartbeat.py',
                'callee': 'utils/master_communication.py',
                'expected_calls': ['send_heartbeat']
            },
            {
                'caller': 'restful/device_service.py',
                'callee': 'db/device_dao.py',
                'expected_calls': ['get_devices', 'update_device']
            },
            {
                'caller': 'main.py',
                'callee': 'utils/config_manager.py',
                'expected_calls': ['get_config', 'load_config']
            }
        ]
        
        total_checks = len(integration_checks)
        passed_checks = 0
        
        for check in integration_checks:
            caller_path = self.project_root / check['caller']
            callee_path = self.project_root / check['callee']
            
            if not caller_path.exists():
                issues.append({
                    'type': 'missing_caller_module',
                    'severity': 'high',
                    'caller': check['caller'],
                    'message': f'调用方模块不存在: {check["caller"]}'
                })
                continue
            
            if not callee_path.exists():
                issues.append({
                    'type': 'missing_callee_module',
                    'severity': 'high',
                    'callee': check['callee'],
                    'message': f'被调用方模块不存在: {check["callee"]}'
                })
                continue
            
            # 检查调用关系
            integration_issues = self._check_module_calls(
                caller_path, callee_path, check['expected_calls']
            )
            
            if not integration_issues:
                passed_checks += 1
            else:
                issues.extend(integration_issues)
        
        # 计算得分
        score = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
        
        self.verification_results['module_integration_verification'] = {
            'status': 'passed' if score >= 75 else 'failed',
            'issues': issues,
            'score': score,
            'passed_checks': passed_checks,
            'total_checks': total_checks
        }
    
    def _check_module_calls(self, caller_path: Path, callee_path: Path, expected_calls: List[str]) -> List[Dict]:
        """检查模块间的调用关系"""
        issues = []
        
        try:
            # 读取调用方文件
            with open(caller_path, 'r', encoding='utf-8') as f:
                caller_content = f.read()
            
            # 读取被调用方文件
            with open(callee_path, 'r', encoding='utf-8') as f:
                callee_content = f.read()
            
            # 检查被调用方是否定义了期望的函数
            for expected_call in expected_calls:
                if f'def {expected_call}' not in callee_content:
                    issues.append({
                        'type': 'missing_callee_function',
                        'severity': 'high',
                        'caller': str(caller_path.relative_to(self.project_root)),
                        'callee': str(callee_path.relative_to(self.project_root)),
                        'function': expected_call,
                        'message': f'被调用方缺少函数定义: {expected_call}'
                    })
                
                # 检查调用方是否调用了该函数
                if expected_call not in caller_content:
                    issues.append({
                        'type': 'missing_function_call',
                        'severity': 'medium',
                        'caller': str(caller_path.relative_to(self.project_root)),
                        'callee': str(callee_path.relative_to(self.project_root)),
                        'function': expected_call,
                        'message': f'调用方未调用期望的函数: {expected_call}'
                    })
        
        except Exception as e:
            issues.append({
                'type': 'module_call_check_error',
                'severity': 'medium',
                'caller': str(caller_path.relative_to(self.project_root)),
                'callee': str(callee_path.relative_to(self.project_root)),
                'message': f'模块调用检查失败: {e}'
            })
        
        return issues
    
    def verify_end_to_end_flows(self) -> None:
        """端到端功能流程验证"""
        logger.info("🔄 执行端到端功能流程验证...")
        
        issues = []
        score = 0
        
        # 定义关键流程检查
        flows = [
            {
                'name': 'server_startup_flow',
                'description': '服务器启动流程',
                'required_components': ['main.py', 'utils/config_manager.py', 'utils/vh_server_manager.py']
            },
            {
                'name': 'device_discovery_flow',
                'description': '设备发现流程',
                'required_components': ['tasks/device_monitor.py', 'utils/vh_client.py', 'db/device_dao.py']
            },
            {
                'name': 'master_communication_flow',
                'description': '主从通信流程',
                'required_components': ['utils/master_communication.py', 'tasks/heartbeat.py']
            },
            {
                'name': 'api_service_flow',
                'description': 'API服务流程',
                'required_components': ['restful/device_service.py', 'restful/system_service.py']
            }
        ]
        
        total_flows = len(flows)
        working_flows = 0
        
        for flow in flows:
            flow_issues = self._verify_flow_components(flow)
            if not flow_issues:
                working_flows += 1
            else:
                issues.extend(flow_issues)
        
        # 计算得分
        score = (working_flows / total_flows) * 100 if total_flows > 0 else 0
        
        self.verification_results['end_to_end_flow_verification'] = {
            'status': 'passed' if score >= 80 else 'failed',
            'issues': issues,
            'score': score,
            'working_flows': working_flows,
            'total_flows': total_flows
        }
    
    def _verify_flow_components(self, flow: Dict) -> List[Dict]:
        """验证流程组件的完整性"""
        issues = []
        
        for component in flow['required_components']:
            component_path = self.project_root / component
            if not component_path.exists():
                issues.append({
                    'type': 'missing_flow_component',
                    'severity': 'high',
                    'flow': flow['name'],
                    'component': component,
                    'message': f'{flow["description"]}缺少组件: {component}'
                })
        
        return issues

    def verify_runtime_environment(self) -> None:
        """实际运行环境验证"""
        logger.info("🚀 执行实际运行环境验证...")

        issues = []
        score = 0

        # 检查关键配置文件
        config_issues = self._check_configuration_files()
        issues.extend(config_issues)

        # 检查API端点定义
        api_issues = self._check_api_endpoints()
        issues.extend(api_issues)

        # 检查数据库模型
        db_issues = self._check_database_models()
        issues.extend(db_issues)

        # 检查VirtualHere集成
        vh_issues = self._check_virtualhere_integration()
        issues.extend(vh_issues)

        # 计算得分
        total_checks = 4
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        score = max(0, 100 - (high_severity_issues * 20))

        self.verification_results['runtime_environment_verification'] = {
            'status': 'passed' if score >= 70 else 'failed',
            'issues': issues,
            'score': score
        }

    def _check_configuration_files(self) -> List[Dict]:
        """检查配置文件"""
        issues = []

        # 检查主配置文件
        config_file = self.project_root / 'config/slave_server.ini'
        if not config_file.exists():
            issues.append({
                'type': 'missing_config_file',
                'severity': 'high',
                'file': 'config/slave_server.ini',
                'message': '缺少主配置文件'
            })

        # 检查配置模板
        config_template = self.project_root / 'config/slave_server.ini.template'
        if config_template.exists():
            try:
                with open(config_template, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查必需的配置节
                required_sections = ['master', 'server', 'virtualhere', 'logging']
                for section in required_sections:
                    if f'[{section}]' not in content:
                        issues.append({
                            'type': 'missing_config_section',
                            'severity': 'medium',
                            'file': 'config/slave_server.ini.template',
                            'section': section,
                            'message': f'配置模板缺少必需节: {section}'
                        })

            except Exception as e:
                issues.append({
                    'type': 'config_file_error',
                    'severity': 'medium',
                    'file': 'config/slave_server.ini.template',
                    'message': f'配置文件读取失败: {e}'
                })

        return issues

    def _check_api_endpoints(self) -> List[Dict]:
        """检查API端点定义"""
        issues = []

        # 需求文档中定义的API端点
        required_endpoints = [
            {'file': 'restful/device_service.py', 'endpoints': ['/devices', '/device']},
            {'file': 'restful/system_service.py', 'endpoints': ['/status', '/info']},
            {'file': 'restful/config_service.py', 'endpoints': ['/config']},
        ]

        for endpoint_group in required_endpoints:
            file_path = self.project_root / endpoint_group['file']
            if not file_path.exists():
                issues.append({
                    'type': 'missing_api_file',
                    'severity': 'high',
                    'file': endpoint_group['file'],
                    'message': f'API文件不存在: {endpoint_group["file"]}'
                })
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查是否定义了Blueprint
                if 'Blueprint' not in content:
                    issues.append({
                        'type': 'missing_blueprint',
                        'severity': 'high',
                        'file': endpoint_group['file'],
                        'message': 'API文件缺少Blueprint定义'
                    })

                # 检查路由定义
                for endpoint in endpoint_group['endpoints']:
                    if f"'{endpoint}'" not in content and f'"{endpoint}"' not in content:
                        issues.append({
                            'type': 'missing_endpoint',
                            'severity': 'medium',
                            'file': endpoint_group['file'],
                            'endpoint': endpoint,
                            'message': f'缺少API端点: {endpoint}'
                        })

            except Exception as e:
                issues.append({
                    'type': 'api_file_error',
                    'severity': 'medium',
                    'file': endpoint_group['file'],
                    'message': f'API文件检查失败: {e}'
                })

        return issues

    def _check_database_models(self) -> List[Dict]:
        """检查数据库模型"""
        issues = []

        # 检查模型文件
        models_file = self.project_root / 'db/models.py'
        if not models_file.exists():
            issues.append({
                'type': 'missing_models_file',
                'severity': 'high',
                'file': 'db/models.py',
                'message': '缺少数据库模型文件'
            })
            return issues

        try:
            with open(models_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 需求文档中定义的表
            required_models = ['Device', 'Config', 'CommandLog']
            for model in required_models:
                if f'class {model}' not in content:
                    issues.append({
                        'type': 'missing_model',
                        'severity': 'high',
                        'file': 'db/models.py',
                        'model': model,
                        'message': f'缺少数据库模型: {model}'
                    })

        except Exception as e:
            issues.append({
                'type': 'models_file_error',
                'severity': 'medium',
                'file': 'db/models.py',
                'message': f'模型文件检查失败: {e}'
            })

        return issues

    def _check_virtualhere_integration(self) -> List[Dict]:
        """检查VirtualHere集成"""
        issues = []

        # 检查VH管理器
        vh_manager_file = self.project_root / 'utils/vh_server_manager.py'
        if not vh_manager_file.exists():
            issues.append({
                'type': 'missing_vh_manager',
                'severity': 'high',
                'file': 'utils/vh_server_manager.py',
                'message': '缺少VirtualHere服务器管理器'
            })

        # 检查VH客户端
        vh_client_file = self.project_root / 'utils/vh_client.py'
        if not vh_client_file.exists():
            issues.append({
                'type': 'missing_vh_client',
                'severity': 'high',
                'file': 'utils/vh_client.py',
                'message': '缺少VirtualHere客户端管理器'
            })

        # KCPTun功能已在新架构下完全移除，无需检查

        return issues

    def generate_overall_assessment(self) -> None:
        """生成综合评估"""
        logger.info("📊 生成综合评估...")

        critical_issues = []
        recommendations = []

        # 收集所有高严重性问题
        for dimension, result in self.verification_results.items():
            if dimension == 'overall_assessment':
                continue

            if 'issues' in result:
                for issue in result['issues']:
                    if issue.get('severity') == 'high':
                        critical_issues.append(issue)

        # 生成建议
        if critical_issues:
            recommendations.append({
                'priority': 'high',
                'category': 'critical_fixes',
                'description': f'修复{len(critical_issues)}个高严重性问题',
                'details': [issue['message'] for issue in critical_issues[:5]]  # 只显示前5个
            })

        # 基于需求文档的特定建议
        req_result = self.verification_results.get('requirement_compliance', {})
        if req_result.get('score', 0) < 80:
            recommendations.append({
                'priority': 'high',
                'category': 'requirement_compliance',
                'description': '提高需求文档符合度',
                'details': [
                    '完善命令接收与执行机制',
                    '增强VirtualHere集成的完整性'
                ]
            })

        self.verification_results['overall_assessment']['critical_issues'] = critical_issues
        self.verification_results['overall_assessment']['recommendations'] = recommendations

    def print_verification_summary(self) -> None:
        """打印验证总结"""
        logger.info("\n" + "="*80)
        logger.info("📋 深度功能验证报告")
        logger.info("="*80)
        
        # 各维度得分
        dimensions = [
            ('需求文档对照', 'requirement_compliance'),
            ('代码语法验证', 'code_syntax_verification'),
            ('模块集成验证', 'module_integration_verification'),
            ('端到端流程验证', 'end_to_end_flow_verification'),
            ('运行环境验证', 'runtime_environment_verification')
        ]
        
        total_score = 0
        completed_verifications = 0
        
        for name, key in dimensions:
            result = self.verification_results[key]
            if result['status'] != 'unknown':
                status_icon = "✅" if result['status'] == 'passed' else "❌"
                score = result['score']
                issue_count = len(result['issues'])
                logger.info(f"   {status_icon} {name}: {score:.1f}/100 ({issue_count}个问题)")
                total_score += score
                completed_verifications += 1
        
        # 总体评估
        if completed_verifications > 0:
            overall_score = total_score / completed_verifications
            
            logger.info(f"\n🎯 总体评分: {overall_score:.1f}/100")
            
            if overall_score >= 85:
                logger.info("✅ 功能验证状态: 优秀")
                status = 'excellent'
            elif overall_score >= 75:
                logger.info("✅ 功能验证状态: 良好")
                status = 'good'
            elif overall_score >= 65:
                logger.info("⚠️  功能验证状态: 需要改进")
                status = 'needs_improvement'
            else:
                logger.info("❌ 功能验证状态: 存在严重问题")
                status = 'critical_issues'
            
            self.verification_results['overall_assessment']['status'] = status
            self.verification_results['overall_assessment']['overall_score'] = overall_score

def main() -> None:
    """主函数"""
    verifier = DeepFunctionalVerifier()
    results = verifier.conduct_deep_verification()
    verifier.print_verification_summary()
    
    # 保存验证报告
    report_file = Path(__file__).parent.parent / 'deep_functional_verification_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"\n📄 详细验证报告已保存到: {report_file}")
    
    return 0

if __name__ == '__main__':
    exit(main())
