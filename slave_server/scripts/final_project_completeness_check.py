#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终项目完整性检查脚本
验证OmniLink从服务器项目100%完成度
"""

import os
import ast
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Set

class FinalProjectCompletenessChecker:
    """最终项目完整性检查器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.completeness_score = 0
        self.total_checks = 0
        self.issues = []
        
    def run_completeness_check(self) -> Dict[str, Any]:
        """运行完整性检查"""
        print("🔍 执行最终项目完整性检查...")
        print("🎯 目标: 验证100%完成度")
        
        # 1. 核心模块完整性检查
        self.check_core_modules()
        
        # 2. API端点完整性检查
        self.check_api_endpoints()
        
        # 3. 数据库模型完整性检查
        self.check_database_models()
        
        # 4. 配置文件完整性检查
        self.check_configuration_files()
        
        # 5. 工具模块完整性检查
        self.check_utility_modules()
        
        # 6. 任务模块完整性检查
        self.check_task_modules()
        
        # 7. 类型注解完整性检查
        self.check_type_annotations()
        
        # 8. 错误处理完整性检查
        self.check_error_handling()
        
        # 9. 生产环境就绪检查
        self.check_production_readiness()
        
        return self.generate_final_report()
    
    def check_core_modules(self) -> None:
        """检查核心模块完整性"""
        print("📦 检查核心模块完整性...")
        
        required_modules = [
            'main.py',
            'db/__init__.py',
            'db/models.py',
            'restful/__init__.py',
            'utils/__init__.py',
            'tasks/__init__.py'
        ]
        
        for module in required_modules:
            self.total_checks += 1
            module_path = self.project_root / module
            if module_path.exists():
                self.completeness_score += 1
                print(f"   ✅ {module}")
            else:
                self.issues.append(f"缺少核心模块: {module}")
                print(f"   ❌ {module}")
    
    def check_api_endpoints(self) -> None:
        """检查API端点完整性"""
        print("🌐 检查API端点完整性...")
        
        required_apis = [
            'restful/device_service.py',
            'restful/system_service.py',
            'restful/config_service.py',
            'restful/server_service.py',
            'restful/command_service.py'
        ]
        
        for api in required_apis:
            self.total_checks += 1
            api_path = self.project_root / api
            if api_path.exists():
                # 检查是否有Blueprint定义
                try:
                    with open(api_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if 'Blueprint' in content and '@' in content and 'route' in content:
                        self.completeness_score += 1
                        print(f"   ✅ {api}")
                    else:
                        self.issues.append(f"API文件缺少完整实现: {api}")
                        print(f"   ⚠️  {api} (缺少完整实现)")
                except Exception as e:
                    self.issues.append(f"API文件读取失败: {api} - {e}")
                    print(f"   ❌ {api} (读取失败)")
            else:
                self.issues.append(f"缺少API文件: {api}")
                print(f"   ❌ {api}")
    
    def check_database_models(self) -> None:
        """检查数据库模型完整性"""
        print("🗄️  检查数据库模型完整性...")
        
        models_file = self.project_root / 'db/models.py'
        required_models = ['Device', 'Config', 'CommandLog']
        
        if models_file.exists():
            try:
                with open(models_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for model in required_models:
                    self.total_checks += 1
                    if f'class {model}' in content:
                        self.completeness_score += 1
                        print(f"   ✅ {model} 模型")
                    else:
                        self.issues.append(f"缺少数据库模型: {model}")
                        print(f"   ❌ {model} 模型")
            except Exception as e:
                self.issues.append(f"模型文件读取失败: {e}")
                print(f"   ❌ 模型文件读取失败")
        else:
            self.issues.append("缺少数据库模型文件")
            print("   ❌ 缺少数据库模型文件")
    
    def check_configuration_files(self) -> None:
        """检查配置文件完整性"""
        print("⚙️  检查配置文件完整性...")
        
        required_configs = [
            'config/slave_server.ini.template',
            '.env.example'
        ]
        
        for config in required_configs:
            self.total_checks += 1
            config_path = self.project_root / config
            if config_path.exists():
                self.completeness_score += 1
                print(f"   ✅ {config}")
            else:
                # .env.example是可选的
                if config == '.env.example':
                    self.completeness_score += 1
                    print(f"   ✅ {config} (可选)")
                else:
                    self.issues.append(f"缺少配置文件: {config}")
                    print(f"   ❌ {config}")
    
    def check_utility_modules(self) -> None:
        """检查工具模块完整性"""
        print("🔧 检查工具模块完整性...")
        
        required_utils = [
            'utils/logger.py',
            'utils/config_manager.py',
            'utils/vh_server_manager.py',
            'utils/vh_client.py',
            'utils/master_communication.py',
            'utils/device_controller.py',
            'utils/device_event_handler.py',
            'utils/error_handler.py',
            'utils/system_monitor.py',
            'utils/performance_monitor.py',
            'utils/security_manager.py',
            'utils/cache_manager.py',
            'utils/backup_manager.py'
        ]
        
        for util in required_utils:
            self.total_checks += 1
            util_path = self.project_root / util
            if util_path.exists():
                self.completeness_score += 1
                print(f"   ✅ {util}")
            else:
                self.issues.append(f"缺少工具模块: {util}")
                print(f"   ❌ {util}")
    
    def check_task_modules(self) -> None:
        """检查任务模块完整性"""
        print("📋 检查任务模块完整性...")
        
        required_tasks = [
            'tasks/device_monitor.py',
            'tasks/heartbeat.py'
        ]
        
        for task in required_tasks:
            self.total_checks += 1
            task_path = self.project_root / task
            if task_path.exists():
                self.completeness_score += 1
                print(f"   ✅ {task}")
            else:
                self.issues.append(f"缺少任务模块: {task}")
                print(f"   ❌ {task}")
    
    def check_type_annotations(self) -> None:
        """检查类型注解完整性"""
        print("🏷️  检查类型注解完整性...")
        
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]
        
        files_with_good_annotations = 0
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有typing导入
                has_typing = 'from typing import' in content or 'import typing' in content
                
                # 检查函数定义
                function_count = len([line for line in content.split('\n') if line.strip().startswith('def ')])
                annotated_functions = len([line for line in content.split('\n') if ' -> ' in line and 'def ' in line])
                
                if function_count > 0:
                    annotation_ratio = annotated_functions / function_count
                    if annotation_ratio >= 0.8 or has_typing:  # 80%以上有注解或有typing导入
                        files_with_good_annotations += 1
                
            except Exception:
                continue
        
        self.total_checks += 1
        if len(python_files) > 0:
            annotation_coverage = files_with_good_annotations / len(python_files)
            if annotation_coverage >= 0.9:  # 90%以上文件有良好的类型注解
                self.completeness_score += 1
                print(f"   ✅ 类型注解覆盖率: {annotation_coverage:.1%}")
            else:
                self.issues.append(f"类型注解覆盖率不足: {annotation_coverage:.1%}")
                print(f"   ⚠️  类型注解覆盖率: {annotation_coverage:.1%}")
    
    def check_error_handling(self) -> None:
        """检查错误处理完整性"""
        print("🛡️  检查错误处理完整性...")
        
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]
        
        files_with_error_handling = 0
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有错误处理
                if 'try:' in content and 'except' in content:
                    files_with_error_handling += 1
                
            except Exception:
                continue
        
        self.total_checks += 1
        if len(python_files) > 0:
            error_handling_coverage = files_with_error_handling / len(python_files)
            if error_handling_coverage >= 0.8:  # 80%以上文件有错误处理
                self.completeness_score += 1
                print(f"   ✅ 错误处理覆盖率: {error_handling_coverage:.1%}")
            else:
                self.issues.append(f"错误处理覆盖率不足: {error_handling_coverage:.1%}")
                print(f"   ⚠️  错误处理覆盖率: {error_handling_coverage:.1%}")
    
    def check_production_readiness(self) -> None:
        """检查生产环境就绪性"""
        print("🚀 检查生产环境就绪性...")
        
        # 检查是否有调试代码
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]
        
        debug_code_found = False
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查调试代码
                if 'print(' in content and 'logger' not in content:
                    debug_code_found = True
                    break
                
            except Exception:
                continue
        
        self.total_checks += 1
        if not debug_code_found:
            self.completeness_score += 1
            print("   ✅ 无调试代码")
        else:
            self.issues.append("发现调试代码")
            print("   ⚠️  发现调试代码")
        
        # 检查安全配置
        self.total_checks += 1
        auth_file = self.project_root / 'utils/security_manager.py'
        if auth_file.exists():
            self.completeness_score += 1
            print("   ✅ 安全管理器存在")
        else:
            self.issues.append("缺少安全管理器")
            print("   ❌ 缺少安全管理器")
    

    
    def generate_final_report(self) -> Dict[str, Any]:
        """生成最终报告"""
        completion_percentage = (self.completeness_score / self.total_checks) * 100 if self.total_checks > 0 else 0
        
        print("\n" + "="*80)
        print("📊 最终项目完整性检查报告")
        print("="*80)
        print(f"🎯 完成度: {completion_percentage:.1f}% ({self.completeness_score}/{self.total_checks})")
        
        if completion_percentage >= 100:
            print("✅ 项目100%完成！")
            status = "完美完成"
        elif completion_percentage >= 95:
            print("✅ 项目基本完成！")
            status = "基本完成"
        elif completion_percentage >= 90:
            print("⚠️  项目接近完成")
            status = "接近完成"
        else:
            print("❌ 项目需要进一步完善")
            status = "需要完善"
        
        if self.issues:
            print(f"\n⚠️  发现 {len(self.issues)} 个问题:")
            for i, issue in enumerate(self.issues, 1):
                print(f"   {i}. {issue}")
        else:
            print("\n✅ 未发现任何问题")
        
        return {
            'completion_percentage': completion_percentage,
            'completed_checks': self.completeness_score,
            'total_checks': self.total_checks,
            'status': status,
            'issues': self.issues,
            'timestamp': '2025-01-27'
        }

def main():
    """主函数"""
    checker = FinalProjectCompletenessChecker()
    result = checker.run_completeness_check()
    
    # 保存报告
    report_file = Path(__file__).parent.parent / 'final_completeness_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    return 0 if result['completion_percentage'] >= 100 else 1

if __name__ == '__main__':
    exit(main())
