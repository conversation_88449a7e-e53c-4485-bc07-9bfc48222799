#!/bin/bash
# OmniLink从服务器安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
INSTALL_DIR="/opt/omnilink"
SERVICE_USER="omnilink"
SERVICE_GROUP="omnilink"
PYTHON_VERSION="3.11"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统依赖..."
    
    case $OS in
        "Ubuntu"*)
            apt update
            apt install -y \
                python${PYTHON_VERSION} \
                python${PYTHON_VERSION}-venv \
                python${PYTHON_VERSION}-dev \
                python3-pip \
                git \
                curl \
                wget \
                build-essential \
                libffi-dev \
                libssl-dev \
                libudev-dev \
                libusb-1.0-0-dev \
                pkg-config \
                systemd
            ;;
        "CentOS Linux"*|"Red Hat"*)
            yum update -y
            yum install -y \
                python${PYTHON_VERSION} \
                python${PYTHON_VERSION}-devel \
                python3-pip \
                git \
                curl \
                wget \
                gcc \
                gcc-c++ \
                make \
                libffi-devel \
                openssl-devel \
                libudev-devel \
                libusb-devel \
                pkgconfig \
                systemd
            ;;
        *)
            log_error "不支持的操作系统: $OS"
            exit 1
            ;;
    esac
    
    log_info "系统依赖安装完成"
}

# 创建用户和组
create_user() {
    log_info "创建服务用户..."
    
    if ! getent group $SERVICE_GROUP > /dev/null 2>&1; then
        groupadd --system $SERVICE_GROUP
        log_info "创建组: $SERVICE_GROUP"
    fi
    
    if ! getent passwd $SERVICE_USER > /dev/null 2>&1; then
        useradd --system \
            --gid $SERVICE_GROUP \
            --home-dir $INSTALL_DIR \
            --shell /bin/false \
            --comment "OmniLink Service User" \
            $SERVICE_USER
        log_info "创建用户: $SERVICE_USER"
    fi
    
    # 添加用户到必要的组
    usermod -a -G plugdev,dialout $SERVICE_USER
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    local dirs=(
        "$INSTALL_DIR"
        "$INSTALL_DIR/data"
        "$INSTALL_DIR/logs"
        "$INSTALL_DIR/config"
        "$INSTALL_DIR/backups"
        "$INSTALL_DIR/tmp"
        "/var/log/omnilink"
        "/etc/omnilink"
    )
    
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
        chown $SERVICE_USER:$SERVICE_GROUP "$dir"
        chmod 755 "$dir"
    done
    
    log_info "目录结构创建完成"
}

# 下载和安装应用
install_application() {
    log_info "安装应用..."
    
    # 切换到安装目录
    cd $INSTALL_DIR
    
    # 如果是从本地安装
    if [[ -f "/tmp/omnilink-slave-server.tar.gz" ]]; then
        log_info "从本地包安装..."
        tar -xzf /tmp/omnilink-slave-server.tar.gz --strip-components=1
    else
        # 从Git仓库克隆
        log_info "从Git仓库克隆..."
        git clone https://github.com/your-org/omnilink-slave-server.git .
    fi
    
    # 设置权限
    chown -R $SERVICE_USER:$SERVICE_GROUP $INSTALL_DIR
    
    log_info "应用安装完成"
}

# 创建Python虚拟环境
create_virtualenv() {
    log_info "创建Python虚拟环境..."
    
    cd $INSTALL_DIR
    
    # 创建虚拟环境
    sudo -u $SERVICE_USER python${PYTHON_VERSION} -m venv venv
    
    # 激活虚拟环境并安装依赖
    sudo -u $SERVICE_USER bash -c "
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt
    "
    
    log_info "Python虚拟环境创建完成"
}

# 下载VirtualHere
download_virtualhere() {
    log_info "下载VirtualHere..."
    
    cd $INSTALL_DIR
    
    # 检测架构
    local arch=$(uname -m)
    local vh_url=""
    
    case $arch in
        x86_64)
            vh_url="https://www.virtualhere.com/sites/default/files/usbserver/vhusbd"
            ;;
        aarch64|arm64)
            vh_url="https://www.virtualhere.com/sites/default/files/usbserver/vhusbd_arm64"
            ;;
        armv7l)
            vh_url="https://www.virtualhere.com/sites/default/files/usbserver/vhusbd_arm"
            ;;
        *)
            log_error "不支持的架构: $arch"
            exit 1
            ;;
    esac
    
    # 下载VirtualHere
    sudo -u $SERVICE_USER curl -o vhusbd "$vh_url"
    sudo -u $SERVICE_USER chmod +x vhusbd
    
    log_info "VirtualHere下载完成"
}

# 配置应用
configure_application() {
    log_info "配置应用..."
    
    cd $INSTALL_DIR
    
    # 复制配置文件模板
    if [[ ! -f config/slave_server.ini ]]; then
        sudo -u $SERVICE_USER cp config/slave_server.ini.template config/slave_server.ini
    fi
    
    if [[ ! -f config/vhusbd.conf ]]; then
        sudo -u $SERVICE_USER cp config/vhusbd.conf.template config/vhusbd.conf
    fi
    
    # 创建环境变量文件
    if [[ ! -f /etc/omnilink/environment ]]; then
        cat > /etc/omnilink/environment << EOF
# OmniLink从服务器环境变量
FLASK_ENV=production
LOG_LEVEL=INFO
PYTHONPATH=$INSTALL_DIR
EOF
        chmod 644 /etc/omnilink/environment
    fi
    
    log_info "应用配置完成"
}

# 安装系统服务
install_service() {
    log_info "安装系统服务..."
    
    # 复制服务文件
    cp $INSTALL_DIR/scripts/omnilink-slave.service /etc/systemd/system/
    
    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable omnilink-slave
    
    log_info "系统服务安装完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 检查防火墙类型
    if command -v ufw > /dev/null; then
        # Ubuntu UFW
        ufw allow 8889/tcp comment "OmniLink API"
        ufw allow 7575/tcp comment "VirtualHere"
    elif command -v firewall-cmd > /dev/null; then
        # CentOS/RHEL firewalld
        firewall-cmd --permanent --add-port=8889/tcp
        firewall-cmd --permanent --add-port=7575/tcp
        firewall-cmd --reload
    else
        log_warn "未检测到防火墙，请手动配置端口 8889 和 7575"
    fi
    
    log_info "防火墙配置完成"
}

# 配置USB权限
configure_usb_permissions() {
    log_info "配置USB权限..."
    
    # 创建udev规则
    cat > /etc/udev/rules.d/99-omnilink-usb.rules << EOF
# OmniLink USB权限规则
SUBSYSTEM=="usb", GROUP="plugdev", MODE="0664"
SUBSYSTEM=="usb_device", GROUP="plugdev", MODE="0664"
EOF
    
    # 重新加载udev规则
    udevadm control --reload-rules
    udevadm trigger
    
    log_info "USB权限配置完成"
}

# 创建日志轮转配置
configure_logrotate() {
    log_info "配置日志轮转..."
    
    cat > /etc/logrotate.d/omnilink << EOF
$INSTALL_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $SERVICE_USER $SERVICE_GROUP
    postrotate
        systemctl reload omnilink-slave > /dev/null 2>&1 || true
    endscript
}

/var/log/omnilink/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $SERVICE_USER $SERVICE_GROUP
}
EOF
    
    log_info "日志轮转配置完成"
}

# 运行安装后检查
post_install_check() {
    log_info "运行安装后检查..."
    
    # 检查Python环境
    cd $INSTALL_DIR
    sudo -u $SERVICE_USER bash -c "
        source venv/bin/activate
        python -c 'import flask, peewee, requests; print(\"Python依赖检查通过\")'
    "
    
    # 检查配置文件
    sudo -u $SERVICE_USER python${PYTHON_VERSION} -c "
import configparser
c = configparser.ConfigParser()
c.read('config/slave_server.ini')
print('配置文件语法检查通过')
"
    
    # 检查VirtualHere
    if [[ -x vhusbd ]]; then
        log_info "VirtualHere二进制文件检查通过"
    else
        log_warn "VirtualHere二进制文件不可执行"
    fi
    
    log_info "安装后检查完成"
}

# 显示安装完成信息
show_completion_info() {
    log_info "安装完成!"
    echo
    echo "=== 安装信息 ==="
    echo "安装目录: $INSTALL_DIR"
    echo "服务用户: $SERVICE_USER"
    echo "配置文件: $INSTALL_DIR/config/slave_server.ini"
    echo "日志目录: $INSTALL_DIR/logs"
    echo
    echo "=== 下一步操作 ==="
    echo "1. 编辑配置文件: $INSTALL_DIR/config/slave_server.ini"
    echo "2. 启动服务: systemctl start omnilink-slave"
    echo "3. 查看状态: systemctl status omnilink-slave"
    echo "4. 查看日志: journalctl -u omnilink-slave -f"
    echo
    echo "=== 服务地址 ==="
    echo "API地址: http://localhost:8889"
    echo "健康检查: http://localhost:8889/api/system/health"
    echo "VirtualHere端口: 7575"
    echo
}

# 主函数
main() {
    log_info "开始安装 OmniLink从服务器"
    
    check_root
    detect_os
    install_system_dependencies
    create_user
    create_directories
    install_application
    create_virtualenv
    download_virtualhere
    configure_application
    install_service
    configure_firewall
    configure_usb_permissions
    configure_logrotate
    post_install_check
    show_completion_info
    
    log_info "安装完成!"
}

# 运行主函数
main "$@"
