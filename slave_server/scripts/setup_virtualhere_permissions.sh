#!/bin/bash

# ================================
# VirtualHere权限配置脚本
# 版本: 1.0
# 创建日期: 2025-07-31
# 
# 功能：
# 1. 配置USB设备访问权限
# 2. 设置udev规则
# 3. 配置用户组权限
# 4. 创建systemd服务
# ================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要以root用户运行此脚本"
        log_info "正确用法: ./setup_virtualhere_permissions.sh"
        exit 1
    fi
}

# 检测Linux发行版
detect_distro() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        DISTRO=$ID
        VERSION=$VERSION_ID
    elif [ -f /etc/redhat-release ]; then
        DISTRO="centos"
    elif [ -f /etc/debian_version ]; then
        DISTRO="debian"
    else
        DISTRO="unknown"
    fi
    
    log_info "检测到Linux发行版: $DISTRO $VERSION"
}

# 检查VirtualHere二进制文件
check_virtualhere_binary() {
    VH_BINARY_PATH="$(pwd)/virtualhere/chake/online/VirtualHere/VirtualHere/Server/4.3.3/vhusbdx86_64"
    
    if [ ! -f "$VH_BINARY_PATH" ]; then
        log_error "VirtualHere二进制文件不存在: $VH_BINARY_PATH"
        exit 1
    fi
    
    if [ ! -x "$VH_BINARY_PATH" ]; then
        log_info "设置VirtualHere二进制文件执行权限..."
        chmod +x "$VH_BINARY_PATH"
    fi
    
    log_info "VirtualHere二进制文件检查完成: $VH_BINARY_PATH"
}

# 创建VirtualHere用户组
create_virtualhere_group() {
    log_info "创建VirtualHere用户组..."
    
    if ! getent group virtualhere > /dev/null 2>&1; then
        sudo groupadd virtualhere
        log_info "已创建virtualhere用户组"
    else
        log_info "virtualhere用户组已存在"
    fi
    
    # 将当前用户添加到virtualhere组
    sudo usermod -a -G virtualhere $USER
    log_info "已将用户 $USER 添加到virtualhere组"
    
    # 将当前用户添加到其他必要的组
    sudo usermod -a -G dialout $USER
    sudo usermod -a -G plugdev $USER
    log_info "已将用户 $USER 添加到dialout和plugdev组"
}

# 配置udev规则
setup_udev_rules() {
    log_info "配置udev规则..."
    
    UDEV_RULES_FILE="/etc/udev/rules.d/99-virtualhere-usb.rules"
    
    sudo tee "$UDEV_RULES_FILE" > /dev/null << 'EOF'
# VirtualHere USB设备访问规则
# 允许virtualhere组用户访问所有USB设备

# 通用USB设备规则
SUBSYSTEM=="usb", GROUP="virtualhere", MODE="0664"
SUBSYSTEM=="usb_device", GROUP="virtualhere", MODE="0664"

# USB Hub设备
SUBSYSTEM=="usb", ATTR{bDeviceClass}=="09", GROUP="virtualhere", MODE="0664"

# 加密锁设备（常见厂商）
# SenseShield
SUBSYSTEM=="usb", ATTR{idVendor}=="096e", GROUP="virtualhere", MODE="0664"
# Rockey
SUBSYSTEM=="usb", ATTR{idVendor}=="0471", GROUP="virtualhere", MODE="0664"
# HASP/Sentinel
SUBSYSTEM=="usb", ATTR{idVendor}=="0529", GROUP="virtualhere", MODE="0664"
# CH341 (常用于加密锁)
SUBSYSTEM=="usb", ATTR{idVendor}=="1a86", GROUP="virtualhere", MODE="0664"
# CP210x (常用于加密锁)
SUBSYSTEM=="usb", ATTR{idVendor}=="10c4", GROUP="virtualhere", MODE="0664"

# 允许virtualhere组访问USB设备节点
KERNEL=="ttyUSB*", GROUP="virtualhere", MODE="0664"
KERNEL=="ttyACM*", GROUP="virtualhere", MODE="0664"
KERNEL=="hidraw*", GROUP="virtualhere", MODE="0664"

# libusb设备节点
SUBSYSTEM=="usb", ENV{DEVTYPE}=="usb_device", GROUP="virtualhere", MODE="0664"

# 为VirtualHere设置特殊权限
ACTION=="add", SUBSYSTEM=="usb", RUN+="/bin/chmod 664 /dev/bus/usb/%b/%k"
ACTION=="add", SUBSYSTEM=="usb", RUN+="/bin/chgrp virtualhere /dev/bus/usb/%b/%k"
EOF

    log_info "udev规则已创建: $UDEV_RULES_FILE"
    
    # 重新加载udev规则
    sudo udevadm control --reload-rules
    sudo udevadm trigger
    log_info "udev规则已重新加载"
}

# 配置USB设备权限
setup_usb_permissions() {
    log_info "配置USB设备权限..."
    
    # 确保/dev/bus/usb目录权限正确
    if [ -d "/dev/bus/usb" ]; then
        sudo find /dev/bus/usb -type f -exec chmod 664 {} \;
        sudo find /dev/bus/usb -type f -exec chgrp virtualhere {} \;
        log_info "已设置/dev/bus/usb目录权限"
    fi
    
    # 设置libusb权限
    if [ -f "/etc/security/limits.conf" ]; then
        if ! grep -q "virtualhere.*memlock" /etc/security/limits.conf; then
            echo "@virtualhere soft memlock unlimited" | sudo tee -a /etc/security/limits.conf
            echo "@virtualhere hard memlock unlimited" | sudo tee -a /etc/security/limits.conf
            log_info "已配置内存锁定权限"
        fi
    fi
}

# 创建systemd服务
create_systemd_service() {
    log_info "创建systemd服务..."
    
    SERVICE_FILE="/etc/systemd/system/virtualhere.service"
    VH_BINARY_PATH="$(realpath $(pwd)/virtualhere/chake/online/VirtualHere/VirtualHere/Server/4.3.3/vhusbdx86_64)"
    WORK_DIR="$(realpath $(pwd))"
    
    sudo tee "$SERVICE_FILE" > /dev/null << EOF
[Unit]
Description=VirtualHere USB Server
After=network.target
Wants=network.target

[Service]
Type=forking
User=$USER
Group=virtualhere
WorkingDirectory=$WORK_DIR
ExecStart=$VH_BINARY_PATH -b -c $WORK_DIR/virtualhere_config.ini
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=process
Restart=on-failure
RestartSec=5
TimeoutStartSec=30
TimeoutStopSec=30

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$WORK_DIR

# 允许访问USB设备
DeviceAllow=/dev/bus/usb/ rw
DeviceAllow=char-usb_device rw

[Install]
WantedBy=multi-user.target
EOF

    log_info "systemd服务已创建: $SERVICE_FILE"
    
    # 重新加载systemd
    sudo systemctl daemon-reload
    log_info "systemd配置已重新加载"
}

# 创建VirtualHere配置文件
create_virtualhere_config() {
    log_info "创建VirtualHere配置文件..."
    
    CONFIG_FILE="virtualhere_config.ini"
    
    cat > "$CONFIG_FILE" << 'EOF'
# VirtualHere服务器配置文件
# 自动生成于权限配置脚本

# 服务器名称
ServerName=OmniLink-USB-Server

# TCP端口
TCPPort=7575

# 启用Bonjour广播
UseAVAHI=1

# 压缩限制
CompressionLimit=384

# 忽略的设备（示例）
# IgnoredDevices=424/ec00

# 日志级别
LogLevel=1

# 自动附加到内核
AutoAttachToKernel=1

# 声明端口（Linux）
ClaimPorts=0
EOF

    log_info "VirtualHere配置文件已创建: $CONFIG_FILE"
}

# 测试VirtualHere启动
test_virtualhere() {
    log_info "测试VirtualHere启动..."
    
    VH_BINARY_PATH="$(pwd)/virtualhere/chake/online/VirtualHere/VirtualHere/Server/4.3.3/vhusbdx86_64"
    
    # 测试帮助命令
    if "$VH_BINARY_PATH" -h > /dev/null 2>&1; then
        log_info "VirtualHere二进制文件可正常执行"
    else
        log_error "VirtualHere二进制文件执行失败"
        return 1
    fi
    
    # 测试后台启动（短暂测试）
    log_info "测试后台启动..."
    "$VH_BINARY_PATH" -b -c virtualhere_config.ini &
    VH_PID=$!
    
    sleep 3
    
    if kill -0 $VH_PID 2>/dev/null; then
        log_info "VirtualHere后台启动成功，PID: $VH_PID"
        
        # 检查端口监听
        if netstat -tlnp 2>/dev/null | grep -q ":7575.*$VH_PID/" || ss -tlnp 2>/dev/null | grep -q ":7575.*pid=$VH_PID"; then
            log_info "VirtualHere正在监听端口7575"
        else
            log_warn "VirtualHere可能未正确监听端口7575"
        fi
        
        # 停止测试进程
        kill $VH_PID
        wait $VH_PID 2>/dev/null || true
        log_info "测试完成，已停止VirtualHere进程"
    else
        log_error "VirtualHere后台启动失败"
        return 1
    fi
}

# 显示后续步骤
show_next_steps() {
    log_info "权限配置完成！"
    echo
    echo -e "${GREEN}后续步骤：${NC}"
    echo "1. 重新登录或执行: newgrp virtualhere"
    echo "2. 启动VirtualHere服务: sudo systemctl start virtualhere"
    echo "3. 启用开机自启: sudo systemctl enable virtualhere"
    echo "4. 查看服务状态: sudo systemctl status virtualhere"
    echo "5. 查看服务日志: sudo journalctl -u virtualhere -f"
    echo
    echo -e "${YELLOW}注意事项：${NC}"
    echo "- 确保USB设备已连接"
    echo "- 防火墙需要开放7575端口"
    echo "- 某些加密锁可能需要特定驱动"
    echo
    echo -e "${BLUE}手动启动命令：${NC}"
    echo "./virtualhere/chake/online/VirtualHere/VirtualHere/Server/4.3.3/vhusbdx86_64 -b -c virtualhere_config.ini"
}

# 主函数
main() {
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}VirtualHere权限配置脚本${NC}"
    echo -e "${GREEN}================================${NC}"
    echo
    
    check_root
    detect_distro
    check_virtualhere_binary
    
    log_info "开始配置VirtualHere权限..."
    
    create_virtualhere_group
    setup_udev_rules
    setup_usb_permissions
    create_systemd_service
    create_virtualhere_config
    
    if test_virtualhere; then
        show_next_steps
    else
        log_error "VirtualHere测试失败，请检查配置"
        exit 1
    fi
}

# 执行主函数
main "$@"
