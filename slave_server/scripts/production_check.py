#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境检查脚本
验证代码质量和生产环境就绪状态
"""

import os
import sys
import subprocess
import json
import ast
import re
from pathlib import Path
from typing import List, Dict, Any

class ProductionChecker:
    """生产环境检查器"""
    
    def __init__(self) -> None:
        self.project_root = Path(__file__).parent.parent
        self.issues = []
        self.warnings = []
        
    def check_all(self) -> Dict[str, Any]:
        """运行所有检查"""
        logger.info("🔍 开始生产环境检查...")
        
        # 代码质量检查
        self.check_imports()
        self.check_type_annotations()
        self.check_debug_code()
        self.check_security()
        self.check_configuration()
        self.check_error_handling()
        
        # 生成报告
        return self.generate_report()
    
    def check_imports(self) -> None:
        """检查导入语句"""
        logger.info("📦 检查导入语句...")
        
        python_files = list(self.project_root.rglob("*.py"))
        for file_path in python_files:
            if ("test" in str(file_path) or "__pycache__" in str(file_path) or
                "VHServer" in str(file_path) or "scripts/production_check.py" in str(file_path)):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查相对导入
                if re.search(r'from\s+\.\.?\w+\s+import', content):
                    self.warnings.append(f"相对导入: {file_path}")

            except Exception as e:
                self.issues.append(f"解析文件失败 {file_path}: {e}")
    
    def check_type_annotations(self) -> None:
        """检查类型注解"""
        logger.info("🏷️  检查类型注解...")
        
        python_files = list(self.project_root.rglob("*.py"))
        for file_path in python_files:
            if ("test" in str(file_path) or "__pycache__" in str(file_path) or
                "VHServer" in str(file_path) or "scripts/production_check.py" in str(file_path)):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查缺少返回类型注解的函数
                no_return_type = re.findall(r'def\s+(\w+)\s*\([^)]*\):', content)
                if no_return_type:
                    missing_funcs = [f for f in no_return_type if not f.startswith('_')]
                    if missing_funcs and len(missing_funcs) > 3:  # 只报告有多个缺失的文件
                        self.warnings.append(f"缺少返回类型注解 {file_path.name}: {len(missing_funcs)}个函数")
                
            except Exception as e:
                self.issues.append(f"检查类型注解失败 {file_path}: {e}")
    
    def check_debug_code(self) -> None:
        """检查调试代码"""
        logger.info("🐛 检查调试代码...")
        
        debug_patterns = [
            r'print\s*\(',
            r'pdb\.set_trace\(\)',
            r'breakpoint\(\)',
            r'console\.log\(',
            r'debugger;'
        ]
        
        python_files = list(self.project_root.rglob("*.py"))
        for file_path in python_files:
            if ("test" in str(file_path) or "__pycache__" in str(file_path) or
                "VHServer" in str(file_path) or "scripts/production_check.py" in str(file_path)):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for i, line in enumerate(lines, 1):
                    for pattern in debug_patterns:
                        if re.search(pattern, line):
                            if 'print(' in line and 'logger' not in line and 'def ' not in line and 'Blueprint' not in line and 'register_blueprint' not in line and file_path.name != 'production_check.py':
                                self.issues.append(f"调试代码 {file_path.name}:{i}: {line.strip()}")
                
            except Exception as e:
                self.issues.append(f"检查调试代码失败 {file_path}: {e}")
    
    def check_security(self) -> None:
        """检查安全问题"""
        logger.info("🔒 检查安全配置...")
        
        security_patterns = [
            r'password\s*=\s*["\'][^"\']{3,}["\']',
            r'secret\s*=\s*["\'][^"\']{3,}["\']',
            r'token\s*=\s*["\'][^"\']{3,}["\']',
            r'key\s*=\s*["\'][^"\']{3,}["\']'
        ]
        
        config_files = list(self.project_root.rglob("*.ini")) + list(self.project_root.rglob("*.conf"))
        for file_path in config_files:
            if "template" in str(file_path) or "example" in str(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in security_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        self.warnings.append(f"可能的硬编码敏感信息 {file_path.name}")
                
            except Exception as e:
                self.issues.append(f"检查安全配置失败 {file_path}: {e}")
    
    def check_configuration(self) -> None:
        """检查配置文件"""
        logger.info("⚙️  检查配置文件...")
        
        required_configs = [
            'config/slave_server.ini.template',
            'config/vhusbd.conf.template'
        ]
        
        for config_file in required_configs:
            config_path = self.project_root / config_file
            if not config_path.exists():
                self.issues.append(f"缺少配置文件: {config_file}")
    
    def check_error_handling(self) -> None:
        """检查错误处理"""
        logger.info("⚠️  检查错误处理...")
        
        python_files = list(self.project_root.rglob("*.py"))
        for file_path in python_files:
            if ("test" in str(file_path) or "__pycache__" in str(file_path) or
                "VHServer" in str(file_path) or "scripts/production_check.py" in str(file_path)):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查裸露的except语句
                bare_except = re.findall(r'except\s*:', content)
                if bare_except:
                    self.warnings.append(f"裸露的except语句 {file_path.name}: {len(bare_except)}个")
                
            except Exception as e:
                self.issues.append(f"检查错误处理失败 {file_path}: {e}")
    
    def generate_report(self) -> Dict[str, Any]:
        """生成检查报告"""
        report = {
            'status': 'pass' if not self.issues else 'fail',
            'total_issues': len(self.issues),
            'total_warnings': len(self.warnings),
            'issues': self.issues,
            'warnings': self.warnings,
            'summary': {
                'critical_issues': len([i for i in self.issues if '调试代码' in i or '硬编码' in i]),
                'code_quality_issues': len([i for i in self.issues if '类型注解' in i or '导入' in i]),
                'configuration_issues': len([i for i in self.issues if '配置' in i])
            }
        }
        
        logger.info(f"\n📊 检查完成:")
        logger.info(f"   状态: {'✅ 通过' if report['status'] == 'pass' else '❌ 失败'}")
        logger.info(f"   问题: {report['total_issues']} 个")
        logger.info(f"   警告: {report['total_warnings']} 个")
        
        if self.issues:
            logger.info(f"\n❌ 发现的问题:")
            for issue in self.issues[:10]:  # 只显示前10个
                logger.info(f"   - {issue}")
            if len(self.issues) > 10:
                logger.info(f"   ... 还有 {len(self.issues) - 10} 个问题")
        
        if self.warnings:
            logger.info(f"\n⚠️  警告:")
            for warning in self.warnings[:5]:  # 只显示前5个
                logger.info(f"   - {warning}")
            if len(self.warnings) > 5:
                logger.info(f"   ... 还有 {len(self.warnings) - 5} 个警告")
        
        return report

def main() -> None:
    """主函数"""
    checker = ProductionChecker()
    report = checker.check_all()
    
    # 保存报告
    report_file = Path(__file__).parent.parent / 'production_check_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    logger.info(f"\n📄 详细报告已保存到: {report_file}")
    
    # 返回适当的退出码
    sys.exit(0 if report['status'] == 'pass' else 1)

if __name__ == '__main__':
    main()
