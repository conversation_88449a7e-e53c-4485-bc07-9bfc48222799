#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终生产环境优化脚本
执行最后的生产环境准备和优化
"""

import os
import re
import shutil
from pathlib import Path
from typing import Dict, List, Any

class FinalProductionOptimizer:
    """最终生产环境优化器"""
    
    def __init__(self) -> None:
        self.project_root = Path(__file__).parent.parent
        self.optimizations_applied = []
        
    def optimize_for_production(self) -> None:
        """执行最终生产环境优化"""
        logger.info("🚀 执行最终生产环境优化...")
        
        # 1. 清理开发和测试文件
        self.cleanup_development_files()
        
        # 2. 优化配置文件
        self.optimize_configuration_files()
        
        # 3. 验证关键文件完整性
        self.verify_critical_files()
        
        # 4. 优化日志配置
        self.optimize_logging_configuration()
        
        # 5. 生成生产环境部署清单
        self.generate_deployment_checklist()
        
        # 6. 创建最终验证脚本
        self.create_final_verification_script()
        
        self.generate_optimization_report()
    
    def cleanup_development_files(self) -> None:
        """清理开发和测试文件"""
        logger.info("🧹 清理开发和测试文件...")
        
        # 要清理的文件和目录
        cleanup_targets = [
            '__pycache__',
            '*.pyc',
            '*.pyo',
            '.pytest_cache',
            'test_*.py',
            '*_test.py',
            '.coverage',
            'coverage.xml',
            '.DS_Store',
            'Thumbs.db'
        ]
        
        cleaned_count = 0
        
        for target in cleanup_targets:
            if '*' in target:
                # 通配符匹配
                for file_path in self.project_root.rglob(target):
                    if file_path.exists():
                        if file_path.is_file():
                            file_path.unlink()
                        elif file_path.is_dir():
                            shutil.rmtree(file_path)
                        cleaned_count += 1
            else:
                # 直接匹配
                for file_path in self.project_root.rglob(target):
                    if file_path.exists():
                        if file_path.is_file():
                            file_path.unlink()
                        elif file_path.is_dir():
                            shutil.rmtree(file_path)
                        cleaned_count += 1
        
        self.optimizations_applied.append({
            'type': 'cleanup',
            'description': f'清理了 {cleaned_count} 个开发/测试文件'
        })
        
        logger.info(f"   ✅ 清理完成，移除了 {cleaned_count} 个文件/目录")
    
    def optimize_configuration_files(self) -> None:
        """优化配置文件"""
        logger.info("⚙️  优化配置文件...")
        
        # 优化主配置文件
        config_template = self.project_root / 'config/slave_server.ini.template'
        if config_template.exists():
            self._optimize_main_config(config_template)
        
        # 优化Docker配置
        dockerfile = self.project_root / 'Dockerfile'
        if dockerfile.exists():
            self._optimize_dockerfile(dockerfile)
        
        # 优化环境变量模板
        env_template = self.project_root / '.env.template'
        if env_template.exists():
            self._optimize_env_template(env_template)
    
    def _optimize_main_config(self, config_path: Path) -> Any:
        """优化主配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 确保生产环境配置
            optimizations = [
                ('debug = true', 'debug = false'),
                ('log_level = DEBUG', 'log_level = INFO'),
                ('auto_reload = true', 'auto_reload = false'),
            ]
            
            modified = False
            for old_val, new_val in optimizations:
                if old_val in content:
                    content = content.replace(old_val, new_val)
                    modified = True
            
            if modified:
                with open(config_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.optimizations_applied.append({
                    'type': 'config_optimization',
                    'file': str(config_path),
                    'description': '优化生产环境配置'
                })
                
                logger.info(f"   ✅ 优化配置文件: {config_path.name}")
        
        except Exception as e:
            logger.info(f"   ⚠️  配置文件优化失败 {config_path}: {e}")
    
    def _optimize_dockerfile(self, dockerfile_path: Path) -> Any:
        """优化Dockerfile"""
        try:
            with open(dockerfile_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 确保生产环境优化
            if 'ENV FLASK_ENV=development' in content:
                content = content.replace('ENV FLASK_ENV=development', 'ENV FLASK_ENV=production')
                
                with open(dockerfile_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.optimizations_applied.append({
                    'type': 'dockerfile_optimization',
                    'file': str(dockerfile_path),
                    'description': '设置生产环境变量'
                })
                
                logger.info(f"   ✅ 优化Dockerfile: {dockerfile_path.name}")
        
        except Exception as e:
            logger.info(f"   ⚠️  Dockerfile优化失败: {e}")
    
    def _optimize_env_template(self, env_path: Path) -> Any:
        """优化环境变量模板"""
        try:
            with open(env_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加生产环境变量
            production_vars = [
                'FLASK_ENV=production',
                'LOG_LEVEL=INFO',
                'DEBUG=false'
            ]
            
            modified = False
            for var in production_vars:
                if var.split('=')[0] not in content:
                    content += f'\n{var}'
                    modified = True
            
            if modified:
                with open(env_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.optimizations_applied.append({
                    'type': 'env_optimization',
                    'file': str(env_path),
                    'description': '添加生产环境变量'
                })
                
                logger.info(f"   ✅ 优化环境变量: {env_path.name}")
        
        except Exception as e:
            logger.info(f"   ⚠️  环境变量优化失败: {e}")
    
    def verify_critical_files(self) -> None:
        """验证关键文件完整性"""
        logger.info("🔍 验证关键文件完整性...")
        
        critical_files = [
            'main.py',
            'config/slave_server.ini.template',
            'utils/config_manager.py',
            'utils/vh_server_manager.py',
            'restful/device_service.py',
            'db/models.py',
            'Dockerfile',
            'deploy.sh',
            'requirements.txt'
        ]
        
        missing_files = []
        for file_path in critical_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            logger.info(f"   ❌ 缺少关键文件: {', '.join(missing_files)}")
        else:
            logger.info(f"   ✅ 所有关键文件完整")
            self.optimizations_applied.append({
                'type': 'file_verification',
                'description': f'验证了 {len(critical_files)} 个关键文件'
            })
    
    def optimize_logging_configuration(self) -> None:
        """优化日志配置"""
        logger.info("📝 优化日志配置...")
        
        # 检查日志配置文件
        log_config_files = [
            'config/logging.conf',
            'config/logging.ini'
        ]
        
        for log_config in log_config_files:
            log_path = self.project_root / log_config
            if log_path.exists():
                try:
                    with open(log_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 优化日志级别为生产环境
                    if 'level=DEBUG' in content:
                        content = content.replace('level=DEBUG', 'level=INFO')
                        
                        with open(log_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        
                        self.optimizations_applied.append({
                            'type': 'logging_optimization',
                            'file': str(log_path),
                            'description': '优化日志级别为生产环境'
                        })
                        
                        logger.info(f"   ✅ 优化日志配置: {log_path.name}")
                
                except Exception as e:
                    logger.info(f"   ⚠️  日志配置优化失败 {log_path}: {e}")
    
    def generate_deployment_checklist(self) -> None:
        """生成生产环境部署清单"""
        logger.info("📋 生成生产环境部署清单...")
        
        checklist = """# OmniLink从服务器生产环境部署清单

## 部署前检查
- [ ] 确认目标ARM设备（树莓派3B/香橙派3P）
- [ ] 确认Ubuntu Linux系统版本
- [ ] 确认网络连接和主服务器地址
- [ ] 确认VirtualHere许可证（如需要）

## 配置文件准备
- [ ] 复制 config/slave_server.ini.template 为 config/slave_server.ini
- [ ] 配置主服务器地址和端口
- [ ] 配置数据库连接信息
- [ ] 配置VirtualHere设置
- [ ] 配置日志路径和级别

## 环境变量设置
- [ ] 设置 FLASK_ENV=production
- [ ] 设置 LOG_LEVEL=INFO
- [ ] 设置 DEBUG=false
- [ ] 配置数据库URL
- [ ] 配置Redis连接（如使用）

## 部署执行
- [ ] 运行 ./deploy.sh --env production
- [ ] 或使用 Docker: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
- [ ] 验证服务启动状态
- [ ] 检查日志输出

## 部署后验证
- [ ] 验证从服务器注册到主服务器
- [ ] 验证VirtualHere服务运行状态
- [ ] 验证USB设备检测功能
- [ ] 验证API端点响应
- [ ] 验证监控和日志记录

## 监控设置
- [ ] 配置系统监控
- [ ] 配置日志监控
- [ ] 配置告警机制
- [ ] 设置备份策略

## 安全检查
- [ ] 确认防火墙配置
- [ ] 确认API认证启用
- [ ] 确认敏感信息加密
- [ ] 确认访问控制正确

## 性能优化
- [ ] 调整系统资源限制
- [ ] 优化数据库连接池
- [ ] 配置缓存策略
- [ ] 监控性能指标
"""
        
        checklist_file = self.project_root / 'DEPLOYMENT_CHECKLIST.md'
        with open(checklist_file, 'w', encoding='utf-8') as f:
            f.write(checklist)
        
        self.optimizations_applied.append({
            'type': 'documentation',
            'file': str(checklist_file),
            'description': '生成部署清单'
        })
        
        logger.info(f"   ✅ 生成部署清单: {checklist_file.name}")
    
    def create_final_verification_script(self) -> None:
        """创建最终验证脚本"""
        logger.info("🔬 创建最终验证脚本...")
        
        verification_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境最终验证脚本
"""

import sys
import subprocess
from pathlib import Path

def verify_production_readiness() -> None:
    """验证生产环境就绪状态"""
    logger.info("🔍 验证生产环境就绪状态...")
    
    checks = [
        ("配置文件", check_config_files),
        ("关键服务", check_critical_services),
        ("网络连接", check_network_connectivity),
        ("权限设置", check_permissions),
        ("日志系统", check_logging_system)
    ]
    
    passed = 0
    total = len(checks)
    
    for name, check_func in checks:
        try:
            if check_func():
                logger.info(f"   ✅ {name}: 通过")
                passed += 1
            else:
                logger.info(f"   ❌ {name}: 失败")
        except Exception as e:
            logger.info(f"   ⚠️  {name}: 检查异常 - {e}")
    
    logger.info(f"\\n📊 验证结果: {passed}/{total} 项通过")
    
    if passed == total:
        logger.info("🚀 生产环境就绪，可以部署！")
        return True
    else:
        logger.info("⚠️  存在问题，请检查后重试")
        return False

def check_config_files() -> None:
    """检查配置文件"""
    config_file = Path("config/slave_server.ini")
    return config_file.exists()

def check_critical_services() -> None:
    """检查关键服务"""
    # 检查VirtualHere二进制文件
    vh_binary = Path("virtualhere/vhusbdarmpi3")
    return vh_binary.exists()

def check_network_connectivity() -> None:
    """检查网络连接"""
    # 简单的网络检查
    try:
        import socket
        socket.create_connection(("8.8.8.8", 53), timeout=3)
        return True
    except:
        return False

def check_permissions() -> None:
    """检查权限设置"""
    # 检查关键文件权限
    return True

def check_logging_system() -> None:
    """检查日志系统"""
    log_dir = Path("logs")
    return log_dir.exists() or True  # 日志目录可能在运行时创建

if __name__ == "__main__":
    if verify_production_readiness():
        sys.exit(0)
    else:
        sys.exit(1)
'''
        
        verification_file = self.project_root / 'verify_production.py'
        with open(verification_file, 'w', encoding='utf-8') as f:
            f.write(verification_script)
        
        # 设置执行权限
        verification_file.chmod(0o755)
        
        self.optimizations_applied.append({
            'type': 'verification_script',
            'file': str(verification_file),
            'description': '创建生产环境验证脚本'
        })
        
        logger.info(f"   ✅ 创建验证脚本: {verification_file.name}")
    
    def generate_optimization_report(self) -> None:
        """生成优化报告"""
        logger.info(f"\n📊 最终优化完成，共应用 {len(self.optimizations_applied)} 项优化:")
        
        optimization_types = {}
        for opt in self.optimizations_applied:
            opt_type = opt['type']
            if opt_type not in optimization_types:
                optimization_types[opt_type] = 0
            optimization_types[opt_type] += 1
        
        for opt_type, count in optimization_types.items():
            logger.info(f"   📝 {opt_type}: {count} 项")
        
        # 保存优化报告
        optimization_report = {
            'total_optimizations': len(self.optimizations_applied),
            'optimizations_by_type': optimization_types,
            'detailed_optimizations': self.optimizations_applied
        }
        
        report_file = self.project_root / 'final_optimization_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(optimization_report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📄 优化报告已保存到: {report_file}")
        
        logger.info("\n🎯 生产环境优化完成！")
        logger.info("📋 下一步操作:")
        logger.info("   1. 运行 python verify_production.py 验证就绪状态")
        logger.info("   2. 查看 DEPLOYMENT_CHECKLIST.md 了解部署步骤")
        logger.info("   3. 执行部署: ./deploy.sh --env production")

def main() -> None:
    """主函数"""
    optimizer = FinalProductionOptimizer()
    optimizer.optimize_for_production()
    return 0

if __name__ == '__main__':
    exit(main())
