#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniLink从服务器全面生产环境审查脚本
基于多维度代码审查框架进行全面质量检查和生产环境优化
"""

import os
import ast
import re
import json
import importlib.util
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
import configparser

class ComprehensiveProductionAuditor:
    """全面生产环境审查器"""
    
    def __init__(self) -> None:
        self.project_root = Path(__file__).parent.parent
        self.audit_results = {
            'architecture_integrity': {'status': 'unknown', 'issues': [], 'score': 0},
            'functional_completeness': {'status': 'unknown', 'issues': [], 'score': 0},
            'logical_consistency': {'status': 'unknown', 'issues': [], 'score': 0},
            'code_quality': {'status': 'unknown', 'issues': [], 'score': 0},
            'security_hardening': {'status': 'unknown', 'issues': [], 'score': 0},
            'performance_optimization': {'status': 'unknown', 'issues': [], 'score': 0},
            'stability_assurance': {'status': 'unknown', 'issues': [], 'score': 0},
            'production_readiness': {'status': 'unknown', 'overall_score': 0, 'recommendations': []}
        }
        
    def conduct_full_audit(self) -> Dict[str, Any]:
        """执行全面生产环境审查"""
        print("🔍 开始OmniLink从服务器全面生产环境审查...")
        print("📋 审查范围: 28个核心功能模块")
        print("🎯 目标: 生产环境就绪验证")
        
        # 执行多维度审查
        self.audit_architecture_integrity()
        self.audit_functional_completeness()
        self.audit_logical_consistency()
        self.audit_code_quality()
        self.audit_security_hardening()
        self.audit_performance_optimization()
        self.audit_stability_assurance()
        
        # 生成最终评估
        self.generate_production_readiness_assessment()
        
        return self.audit_results
    
    def audit_architecture_integrity(self) -> None:
        """架构完整性审查（空间思维）"""
        logger.info("🏗️  执行架构完整性审查...")
        
        issues = []
        score = 0
        
        # 1. 验证模块间依赖关系
        dependency_issues = self._check_module_dependencies()
        issues.extend(dependency_issues)
        
        # 2. 检查导入语句准确性
        import_issues = self._check_import_accuracy()
        issues.extend(import_issues)
        
        # 3. 分析代码分层架构
        layering_issues = self._check_code_layering()
        issues.extend(layering_issues)
        
        # 4. 验证横切关注点统一性
        crosscutting_issues = self._check_crosscutting_concerns()
        issues.extend(crosscutting_issues)
        
        # 计算得分
        total_checks = 4
        passed_checks = total_checks - len([i for i in issues if i['severity'] == 'high'])
        score = (passed_checks / total_checks) * 100
        
        self.audit_results['architecture_integrity'] = {
            'status': 'passed' if score >= 80 else 'failed',
            'issues': issues,
            'score': score
        }
    
    def _check_module_dependencies(self) -> List[Dict]:
        """检查模块间依赖关系"""
        issues = []
        
        # 获取所有Python文件
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]
        
        # 检查循环依赖
        dependency_graph = {}
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 解析导入语句
                tree = ast.parse(content)
                imports = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            imports.append(node.module)
                
                module_name = str(file_path.relative_to(self.project_root)).replace('/', '.').replace('.py', '')
                dependency_graph[module_name] = imports
                
            except Exception as e:
                issues.append({
                    'type': 'dependency_analysis_error',
                    'severity': 'medium',
                    'file': str(file_path),
                    'message': f"依赖分析失败: {e}"
                })
        
        # 检测循环依赖（简化版）
        for module, deps in dependency_graph.items():
            for dep in deps:
                if dep in dependency_graph and module in dependency_graph[dep]:
                    issues.append({
                        'type': 'circular_dependency',
                        'severity': 'high',
                        'modules': [module, dep],
                        'message': f"检测到循环依赖: {module} <-> {dep}"
                    })
        
        return issues
    
    def _check_import_accuracy(self) -> List[Dict]:
        """检查导入语句准确性"""
        issues = []
        
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 解析AST
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.ImportFrom):
                        if node.module and not node.module.startswith('.'):
                            # 检查模块是否存在
                            module_parts = node.module.split('.')
                            if module_parts[0] in ['utils', 'db', 'restful', 'tasks']:
                                module_path = self.project_root / '/'.join(module_parts)
                                if not (module_path.with_suffix('.py').exists() or 
                                       (module_path / '__init__.py').exists()):
                                    issues.append({
                                        'type': 'missing_module',
                                        'severity': 'high',
                                        'file': str(file_path),
                                        'module': node.module,
                                        'message': f"导入的模块不存在: {node.module}"
                                    })
                
            except SyntaxError as e:
                issues.append({
                    'type': 'syntax_error',
                    'severity': 'high',
                    'file': str(file_path),
                    'message': f"语法错误: {e}"
                })
            except Exception as e:
                issues.append({
                    'type': 'import_check_error',
                    'severity': 'medium',
                    'file': str(file_path),
                    'message': f"导入检查失败: {e}"
                })
        
        return issues
    
    def _check_code_layering(self) -> List[Dict]:
        """检查代码分层架构"""
        issues = []
        
        # 定义层次结构
        layers = {
            'api': ['restful'],
            'business': ['utils', 'tasks'],
            'data': ['db']
        }
        
        # 检查跨层依赖
        for layer_name, layer_dirs in layers.items():
            for layer_dir in layer_dirs:
                layer_path = self.project_root / layer_dir
                if layer_path.exists():
                    python_files = list(layer_path.rglob("*.py"))
                    
                    for file_path in python_files:
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            # 检查是否有不当的跨层依赖
                            if layer_name == 'data':  # 数据层不应依赖业务层或API层
                                if 'from utils' in content or 'from restful' in content:
                                    issues.append({
                                        'type': 'layer_violation',
                                        'severity': 'medium',
                                        'file': str(file_path),
                                        'message': "数据层不应依赖业务层或API层"
                                    })
                            
                        except Exception as e:
                            continue
        
        return issues
    
    def _check_crosscutting_concerns(self) -> List[Dict]:
        """检查横切关注点统一性"""
        issues = []
        
        # 检查日志使用的一致性
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]
        
        inconsistent_logging = []
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否使用了print而不是logger
                if 'print(' in content and 'logger' not in content:
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if 'print(' in line and 'def print' not in line:
                            inconsistent_logging.append({
                                'file': str(file_path),
                                'line': i,
                                'content': line.strip()
                            })
                
            except Exception:
                continue
        
        if inconsistent_logging:
            issues.append({
                'type': 'inconsistent_logging',
                'severity': 'medium',
                'occurrences': inconsistent_logging,
                'message': f"发现{len(inconsistent_logging)}处使用print而非logger"
            })
        
        return issues
    
    def audit_functional_completeness(self) -> None:
        """功能完整性审查（立体思维）"""
        logger.info("🔧 执行功能完整性审查...")
        
        issues = []
        score = 0
        
        # 1. REST API端点完整性
        api_issues = self._check_api_completeness()
        issues.extend(api_issues)
        
        # 2. 数据库操作CRUD完整性
        crud_issues = self._check_crud_completeness()
        issues.extend(crud_issues)
        
        # 3. VirtualHere集成完整性
        vh_issues = self._check_virtualhere_integration()
        issues.extend(vh_issues)
        
        # 4. 主从通信完整性
        comm_issues = self._check_master_slave_communication()
        issues.extend(comm_issues)
        
        # 计算得分
        total_checks = 4
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        score = max(0, (total_checks - high_severity_issues) / total_checks * 100)
        
        self.audit_results['functional_completeness'] = {
            'status': 'passed' if score >= 75 else 'failed',
            'issues': issues,
            'score': score
        }
    
    def _check_api_completeness(self) -> List[Dict]:
        """检查REST API端点完整性"""
        issues = []
        
        api_files = [
            'restful/device_service.py',
            'restful/system_service.py',
            'restful/config_service.py',
            'restful/server_service.py'
        ]
        
        for api_file in api_files:
            api_path = self.project_root / api_file
            if not api_path.exists():
                issues.append({
                    'type': 'missing_api_file',
                    'severity': 'high',
                    'file': api_file,
                    'message': f"API文件不存在: {api_file}"
                })
                continue
            
            try:
                with open(api_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查基本API结构
                if 'Blueprint' not in content:
                    issues.append({
                        'type': 'missing_blueprint',
                        'severity': 'high',
                        'file': api_file,
                        'message': "缺少Blueprint定义"
                    })
                
                # 检查路由定义
                routes = re.findall(r'@\w+\.route\([^)]+\)', content)
                if len(routes) == 0:
                    issues.append({
                        'type': 'no_routes',
                        'severity': 'high',
                        'file': api_file,
                        'message': "未发现任何路由定义"
                    })
                
                # 检查错误处理
                if 'try:' not in content or 'except' not in content:
                    issues.append({
                        'type': 'missing_error_handling',
                        'severity': 'medium',
                        'file': api_file,
                        'message': "缺少错误处理机制"
                    })
                
            except Exception as e:
                issues.append({
                    'type': 'api_analysis_error',
                    'severity': 'medium',
                    'file': api_file,
                    'message': f"API分析失败: {e}"
                })
        
        return issues
    
    def _check_crud_completeness(self) -> List[Dict]:
        """检查数据库CRUD操作完整性"""
        issues = []
        
        dao_files = list(self.project_root.glob("db/*_dao.py"))
        
        for dao_file in dao_files:
            try:
                with open(dao_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查基本CRUD操作
                crud_operations = ['create', 'get', 'update', 'delete']
                missing_operations = []
                
                for operation in crud_operations:
                    if f'def {operation}_' not in content and f'def {operation}(' not in content:
                        missing_operations.append(operation)
                
                if missing_operations:
                    issues.append({
                        'type': 'incomplete_crud',
                        'severity': 'medium',
                        'file': str(dao_file),
                        'missing_operations': missing_operations,
                        'message': f"缺少CRUD操作: {', '.join(missing_operations)}"
                    })
                
            except Exception as e:
                issues.append({
                    'type': 'dao_analysis_error',
                    'severity': 'medium',
                    'file': str(dao_file),
                    'message': f"DAO分析失败: {e}"
                })
        
        return issues
    
    def _check_virtualhere_integration(self) -> List[Dict]:
        """检查VirtualHere集成完整性"""
        issues = []
        
        vh_files = [
            'utils/vh_server_manager.py',
            'utils/vh_client.py'
        ]
        
        for vh_file in vh_files:
            vh_path = self.project_root / vh_file
            if not vh_path.exists():
                issues.append({
                    'type': 'missing_vh_component',
                    'severity': 'high',
                    'file': vh_file,
                    'message': f"VirtualHere组件不存在: {vh_file}"
                })
                continue
            
            try:
                with open(vh_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键方法
                if 'vh_server_manager' in vh_file:
                    required_methods = ['start_server', 'stop_server', 'is_server_running']
                    for method in required_methods:
                        if f'def {method}' not in content:
                            issues.append({
                                'type': 'missing_vh_method',
                                'severity': 'medium',
                                'file': vh_file,
                                'method': method,
                                'message': f"缺少VH管理方法: {method}"
                            })
                
            except Exception as e:
                issues.append({
                    'type': 'vh_analysis_error',
                    'severity': 'medium',
                    'file': vh_file,
                    'message': f"VH组件分析失败: {e}"
                })
        
        return issues
    
    def _check_master_slave_communication(self) -> List[Dict]:
        """检查主从通信完整性"""
        issues = []
        
        comm_files = [
            'utils/master_communication.py',
            'tasks/heartbeat.py'
        ]
        
        for comm_file in comm_files:
            comm_path = self.project_root / comm_file
            if not comm_path.exists():
                issues.append({
                    'type': 'missing_communication_component',
                    'severity': 'high',
                    'file': comm_file,
                    'message': f"通信组件不存在: {comm_file}"
                })
        
        return issues
    
    def audit_logical_consistency(self) -> None:
        """逻辑一致性审查（逆向思维）"""
        logger.info("🧠 执行逻辑一致性审查...")
        
        issues = []
        score = 0
        
        # 1. API文档与实现一致性
        api_consistency_issues = self._check_api_documentation_consistency()
        issues.extend(api_consistency_issues)
        
        # 2. 配置项一致性
        config_consistency_issues = self._check_configuration_consistency()
        issues.extend(config_consistency_issues)
        
        # 3. 错误处理一致性
        error_handling_issues = self._check_error_handling_consistency()
        issues.extend(error_handling_issues)
        
        # 计算得分
        total_checks = 3
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        score = max(0, (total_checks - high_severity_issues) / total_checks * 100)
        
        self.audit_results['logical_consistency'] = {
            'status': 'passed' if score >= 70 else 'failed',
            'issues': issues,
            'score': score
        }
    
    def _check_api_documentation_consistency(self) -> List[Dict]:
        """检查API文档与实现一致性"""
        issues = []
        
        # 检查API文档是否存在
        api_doc_path = self.project_root / 'docs/api.md'
        if not api_doc_path.exists():
            issues.append({
                'type': 'missing_api_documentation',
                'severity': 'medium',
                'message': "缺少API文档"
            })
        
        return issues
    
    def _check_configuration_consistency(self) -> List[Dict]:
        """检查配置项一致性"""
        issues = []
        
        # 检查配置模板与代码的一致性
        config_template = self.project_root / 'config/slave_server.ini.template'
        if config_template.exists():
            try:
                config = configparser.ConfigParser()
                config.read(config_template, encoding='utf-8')
                
                # 检查配置管理器是否处理了所有配置项
                config_manager_path = self.project_root / 'utils/config_manager.py'
                if config_manager_path.exists():
                    with open(config_manager_path, 'r', encoding='utf-8') as f:
                        config_manager_content = f.read()
                    
                    for section in config.sections():
                        if f'get_{section}_config' not in config_manager_content:
                            issues.append({
                                'type': 'unhandled_config_section',
                                'severity': 'medium',
                                'section': section,
                                'message': f"配置节未在配置管理器中处理: {section}"
                            })
                
            except Exception as e:
                issues.append({
                    'type': 'config_analysis_error',
                    'severity': 'medium',
                    'message': f"配置分析失败: {e}"
                })
        
        return issues
    
    def _check_error_handling_consistency(self) -> List[Dict]:
        """检查错误处理一致性"""
        issues = []
        
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]
        
        bare_except_count = 0
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查裸露的except语句
                bare_except_matches = re.findall(r'except\s*:', content)
                bare_except_count += len(bare_except_matches)
                
            except Exception:
                continue
        
        if bare_except_count > 0:
            issues.append({
                'type': 'bare_except_statements',
                'severity': 'medium',
                'count': bare_except_count,
                'message': f"发现{bare_except_count}个裸露的except语句"
            })
        
        return issues

    def audit_code_quality(self) -> None:
        """代码质量深度检查"""
        logger.info("📝 执行代码质量深度检查...")

        issues = []
        score = 0

        # 1. 函数参数类型注解检查
        type_annotation_issues = self._check_type_annotations()
        issues.extend(type_annotation_issues)

        # 2. 异常处理完整性检查
        exception_handling_issues = self._check_exception_handling()
        issues.extend(exception_handling_issues)

        # 3. 资源管理检查
        resource_management_issues = self._check_resource_management()
        issues.extend(resource_management_issues)

        # 计算得分
        total_checks = 3
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        medium_severity_issues = len([i for i in issues if i['severity'] == 'medium'])
        score = max(0, 100 - (high_severity_issues * 15) - (medium_severity_issues * 5))

        self.audit_results['code_quality'] = {
            'status': 'passed' if score >= 70 else 'failed',
            'issues': issues,
            'score': score
        }

    def audit_security_hardening(self) -> None:
        """安全加固审查"""
        logger.info("🔒 执行安全加固审查...")

        issues = []
        score = 0

        # 1. 调试代码检查
        debug_issues = self._check_debug_code()
        issues.extend(debug_issues)

        # 2. 敏感信息检查
        sensitive_info_issues = self._check_sensitive_information()
        issues.extend(sensitive_info_issues)

        # 3. API认证检查
        auth_issues = self._check_api_authentication()
        issues.extend(auth_issues)

        # 计算得分
        total_checks = 3
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        score = max(0, 100 - (high_severity_issues * 20))

        self.audit_results['security_hardening'] = {
            'status': 'passed' if score >= 80 else 'failed',
            'issues': issues,
            'score': score
        }

    def audit_performance_optimization(self) -> None:
        """性能优化审查"""
        logger.info("⚡ 执行性能优化审查...")

        issues = []
        score = 0

        # 1. 数据库查询优化检查
        db_issues = self._check_database_optimization()
        issues.extend(db_issues)

        # 2. 缓存机制检查
        cache_issues = self._check_caching_mechanisms()
        issues.extend(cache_issues)

        # 计算得分
        total_checks = 2
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        score = max(0, 100 - (high_severity_issues * 25))

        self.audit_results['performance_optimization'] = {
            'status': 'passed' if score >= 75 else 'failed',
            'issues': issues,
            'score': score
        }

    def audit_stability_assurance(self) -> None:
        """稳定性保障审查"""
        logger.info("🛡️  执行稳定性保障审查...")

        issues = []
        score = 0

        # 1. 错误恢复机制检查
        recovery_issues = self._check_error_recovery()
        issues.extend(recovery_issues)

        # 2. 监控告警检查
        monitoring_issues = self._check_monitoring_alerting()
        issues.extend(monitoring_issues)

        # 计算得分
        total_checks = 2
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        score = max(0, 100 - (high_severity_issues * 25))

        self.audit_results['stability_assurance'] = {
            'status': 'passed' if score >= 75 else 'failed',
            'issues': issues,
            'score': score
        }

    def _check_type_annotations(self) -> List[Dict]:
        """检查函数参数类型注解"""
        issues = []

        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]

        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查缺少返回类型注解的函数
                no_return_type = re.findall(r'def\s+(\w+)\s*\([^)]*\):', content)
                missing_return_types = [f for f in no_return_type if not f.startswith('_')]

                if missing_return_types and len(missing_return_types) > 3:
                    issues.append({
                        'type': 'missing_return_type_annotations',
                        'severity': 'low',
                        'file': str(file_path),
                        'count': len(missing_return_types),
                        'message': f"缺少返回类型注解的函数: {len(missing_return_types)}个"
                    })

            except Exception as e:
                continue

        return issues

    def _check_exception_handling(self) -> List[Dict]:
        """检查异常处理完整性"""
        issues = []

        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]

        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查裸露的except语句
                bare_except_blocks = len(re.findall(r'\bexcept\s*:', content))

                if bare_except_blocks > 0:
                    issues.append({
                        'type': 'bare_except_blocks',
                        'severity': 'medium',
                        'file': str(file_path),
                        'count': bare_except_blocks,
                        'message': f"存在{bare_except_blocks}个裸露的except块"
                    })

            except Exception as e:
                continue

        return issues

    def _check_resource_management(self) -> List[Dict]:
        """检查资源管理"""
        issues = []

        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]

        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查文件操作是否使用with语句
                file_opens = re.findall(r'open\s*\([^)]+\)', content)
                with_statements = re.findall(r'with\s+open\s*\([^)]+\)', content)

                if len(file_opens) > len(with_statements):
                    issues.append({
                        'type': 'unsafe_file_operations',
                        'severity': 'medium',
                        'file': str(file_path),
                        'message': "存在未使用with语句的文件操作"
                    })

            except Exception as e:
                continue

        return issues

    def _check_debug_code(self) -> List[Dict]:
        """检查调试代码"""
        issues = []

        debug_patterns = [
            r'print\s*\(',
            r'pdb\.set_trace\(\)',
            r'breakpoint\(\)',
            r'console\.log\(',
            r'debugger;'
        ]

        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]

        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                for pattern in debug_patterns:
                    if re.search(pattern, content):
                        if 'print(' in pattern and ('logger' in content or 'def print' in content):
                            continue
                        issues.append({
                            'type': 'debug_code_found',
                            'severity': 'high',
                            'file': str(file_path),
                            'pattern': pattern,
                            'message': f"发现调试代码: {pattern}"
                        })

            except Exception as e:
                continue

        return issues

    def _check_sensitive_information(self) -> List[Dict]:
        """检查敏感信息"""
        issues = []

        sensitive_patterns = [
            r'password\s*=\s*["\'][^"\']{3,}["\']',
            r'secret\s*=\s*["\'][^"\']{3,}["\']',
            r'token\s*=\s*["\'][^"\']{3,}["\']',
            r'key\s*=\s*["\'][^"\']{3,}["\']'
        ]

        config_files = list(self.project_root.rglob("*.ini")) + list(self.project_root.rglob("*.conf"))

        for file_path in config_files:
            if "template" in str(file_path) or "example" in str(file_path):
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                for pattern in sensitive_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        issues.append({
                            'type': 'hardcoded_sensitive_info',
                            'severity': 'high',
                            'file': str(file_path),
                            'message': "发现硬编码的敏感信息"
                        })

            except Exception as e:
                continue

        return issues

    def _check_api_authentication(self) -> List[Dict]:
        """检查API认证"""
        issues = []

        api_files = list(self.project_root.glob("restful/*.py"))

        for api_file in api_files:
            try:
                with open(api_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查是否有认证装饰器或中间件
                if '@' in content and 'route' in content:
                    if 'auth' not in content.lower() and 'token' not in content.lower():
                        issues.append({
                            'type': 'missing_authentication',
                            'severity': 'medium',
                            'file': str(api_file),
                            'message': "API端点可能缺少认证机制"
                        })

            except Exception as e:
                continue

        return issues

    def _check_database_optimization(self) -> List[Dict]:
        """检查数据库优化"""
        issues = []

        dao_files = list(self.project_root.glob("db/*_dao.py"))

        for dao_file in dao_files:
            try:
                with open(dao_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查是否使用了事务
                if 'database.atomic' not in content and 'transaction' not in content:
                    issues.append({
                        'type': 'missing_transaction_management',
                        'severity': 'medium',
                        'file': str(dao_file),
                        'message': "可能缺少事务管理"
                    })

            except Exception as e:
                continue

        return issues

    def _check_caching_mechanisms(self) -> List[Dict]:
        """检查缓存机制"""
        issues = []

        # 检查是否实现了缓存
        cache_files = list(self.project_root.glob("utils/*cache*.py"))
        if not cache_files:
            issues.append({
                'type': 'missing_caching',
                'severity': 'low',
                'message': "未发现缓存机制实现"
            })

        return issues

    def _check_error_recovery(self) -> List[Dict]:
        """检查错误恢复机制"""
        issues = []

        # 检查是否有错误处理器
        error_handler_path = self.project_root / 'utils/error_handler.py'
        if not error_handler_path.exists():
            issues.append({
                'type': 'missing_error_handler',
                'severity': 'high',
                'message': "缺少错误处理器"
            })

        return issues

    def _check_monitoring_alerting(self) -> List[Dict]:
        """检查监控告警"""
        issues = []

        # 检查是否有监控系统
        monitoring_files = list(self.project_root.glob("utils/*monitor*.py"))
        if not monitoring_files:
            issues.append({
                'type': 'missing_monitoring',
                'severity': 'medium',
                'message': "未发现监控系统实现"
            })

        return issues

    def print_audit_summary(self) -> None:
        """打印审查总结"""
        logger.info("\n" + "="*80)
        logger.info("📋 OmniLink从服务器生产环境审查报告")
        logger.info("="*80)
        
        # 各维度得分
        dimensions = [
            ('架构完整性', 'architecture_integrity'),
            ('功能完整性', 'functional_completeness'),
            ('逻辑一致性', 'logical_consistency'),
            ('代码质量', 'code_quality'),
            ('安全加固', 'security_hardening'),
            ('性能优化', 'performance_optimization'),
            ('稳定性保障', 'stability_assurance')
        ]
        
        total_score = 0
        completed_audits = 0
        
        for name, key in dimensions:
            result = self.audit_results[key]
            if result['status'] != 'unknown':
                status_icon = "✅" if result['status'] == 'passed' else "❌"
                score = result['score']
                issue_count = len(result['issues'])
                logger.info(f"   {status_icon} {name}: {score:.1f}/100 ({issue_count}个问题)")
                total_score += score
                completed_audits += 1
        
        # 总体评估
        if completed_audits > 0:
            overall_score = total_score / completed_audits
            self.audit_results['production_readiness']['overall_score'] = overall_score
            
            logger.info(f"\n🎯 总体评分: {overall_score:.1f}/100")
            
            if overall_score >= 85:
                logger.info("✅ 生产环境就绪状态: 优秀")
                self.audit_results['production_readiness']['status'] = 'excellent'
            elif overall_score >= 75:
                logger.info("✅ 生产环境就绪状态: 良好")
                self.audit_results['production_readiness']['status'] = 'good'
            elif overall_score >= 65:
                logger.info("⚠️  生产环境就绪状态: 需要改进")
                self.audit_results['production_readiness']['status'] = 'needs_improvement'
            else:
                logger.info("❌ 生产环境就绪状态: 不建议部署")
                self.audit_results['production_readiness']['status'] = 'not_ready'

    def generate_production_readiness_assessment(self) -> None:
        """生成生产就绪评估"""
        logger.info("📊 生成生产就绪评估...")

        recommendations = []

        # 分析各维度问题并生成建议
        for dimension, result in self.audit_results.items():
            if dimension == 'production_readiness':
                continue

            if result['status'] == 'failed':
                high_issues = [i for i in result['issues'] if i['severity'] == 'high']
                if high_issues:
                    recommendations.append({
                        'dimension': dimension,
                        'priority': 'high',
                        'issue_count': len(high_issues),
                        'recommendation': f"修复{dimension}中的{len(high_issues)}个高严重性问题"
                    })

        self.audit_results['production_readiness']['recommendations'] = recommendations

def main() -> None:
    """主函数"""
    auditor = ComprehensiveProductionAuditor()
    results = auditor.conduct_full_audit()
    auditor.print_audit_summary()
    
    # 保存审查报告
    report_file = Path(__file__).parent.parent / 'comprehensive_production_audit_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"\n📄 详细审查报告已保存到: {report_file}")
    
    return 0

if __name__ == '__main__':
    exit(main())
