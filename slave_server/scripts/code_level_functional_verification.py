#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码级功能校验核查脚本
对OmniLink从服务器项目进行全面的代码质量审查和生产环境优化
"""

import os
import ast
import re
import json
import importlib.util
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional, Set

class CodeLevelFunctionalVerifier:
    """代码级功能校验器"""
    
    def __init__(self) -> None:
        self.project_root = Path(__file__).parent.parent
        self.verification_results = {
            'architecture_integrity': {'status': 'unknown', 'issues': [], 'score': 0},
            'functional_completeness': {'status': 'unknown', 'issues': [], 'score': 0},
            'logical_consistency': {'status': 'unknown', 'issues': [], 'score': 0},
            'code_quality': {'status': 'unknown', 'issues': [], 'score': 0},
            'production_optimization': {'status': 'unknown', 'issues': [], 'score': 0},
            'integration_points': {'status': 'unknown', 'issues': [], 'score': 0},
            'overall_assessment': {'status': 'unknown', 'score': 0, 'recommendations': []}
        }
        
    def conduct_code_level_verification(self) -> Dict[str, Any]:
        """执行代码级功能校验"""
        print("🔍 开始代码级功能校验核查...")
        print("📋 审查范围: 28个核心功能模块")
        print("🎯 目标: 生产环境代码质量标准")
        
        # 1. 架构完整性审查（空间思维）
        self.verify_architecture_integrity()
        
        # 2. 功能完整性审查（立体思维）
        self.verify_functional_completeness()
        
        # 3. 逻辑一致性审查（逆向思维）
        self.verify_logical_consistency()
        
        # 4. 代码质量深度检查
        self.verify_code_quality()
        
        # 5. 生产环境优化检查
        self.verify_production_optimization()
        
        # 6. 集成点检查
        self.verify_integration_points()
        
        # 7. 生成总体评估
        self.generate_overall_assessment()
        
        return self.verification_results
    
    def verify_architecture_integrity(self) -> None:
        """架构完整性审查（空间思维）"""
        print("🏗️  执行架构完整性审查...")
        
        issues = []
        score = 0
        
        # 1. 验证模块间依赖关系
        dependency_issues = self._check_module_dependencies()
        issues.extend(dependency_issues)
        
        # 2. 检查导入语句准确性
        import_issues = self._check_import_accuracy()
        issues.extend(import_issues)
        
        # 3. 分析代码分层架构
        layering_issues = self._check_code_layering()
        issues.extend(layering_issues)
        
        # 4. 验证横切关注点统一性
        crosscutting_issues = self._check_crosscutting_concerns()
        issues.extend(crosscutting_issues)
        
        # 计算得分
        total_checks = 4
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        score = max(0, 100 - (high_severity_issues * 15))
        
        self.verification_results['architecture_integrity'] = {
            'status': 'passed' if score >= 85 else 'failed',
            'issues': issues,
            'score': score
        }
    
    def _check_module_dependencies(self) -> List[Dict]:
        """检查模块间依赖关系"""
        issues = []
        
        # 获取所有Python文件
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]
        
        # 构建依赖图
        dependency_graph = {}
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                imports = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            imports.append(node.module)
                
                module_name = str(file_path.relative_to(self.project_root)).replace('/', '.').replace('.py', '')
                dependency_graph[module_name] = imports
                
            except Exception as e:
                issues.append({
                    'type': 'dependency_analysis_error',
                    'severity': 'medium',
                    'file': str(file_path.relative_to(self.project_root)),
                    'message': f"依赖分析失败: {e}"
                })
        
        # 检测循环依赖
        for module, deps in dependency_graph.items():
            for dep in deps:
                if dep in dependency_graph and module in dependency_graph[dep]:
                    issues.append({
                        'type': 'circular_dependency',
                        'severity': 'high',
                        'modules': [module, dep],
                        'message': f"检测到循环依赖: {module} <-> {dep}"
                    })
        
        return issues
    
    def _check_import_accuracy(self) -> List[Dict]:
        """检查导入语句准确性"""
        issues = []
        
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.ImportFrom):
                        if node.module and not node.module.startswith('.'):
                            # 检查项目内模块导入
                            module_parts = node.module.split('.')
                            if module_parts[0] in ['utils', 'db', 'restful', 'tasks']:
                                module_path = self.project_root / '/'.join(module_parts)
                                if not (module_path.with_suffix('.py').exists() or 
                                       (module_path / '__init__.py').exists()):
                                    issues.append({
                                        'type': 'missing_import_module',
                                        'severity': 'high',
                                        'file': str(file_path.relative_to(self.project_root)),
                                        'line': node.lineno,
                                        'module': node.module,
                                        'message': f'导入的模块不存在: {node.module}'
                                    })
                
            except SyntaxError as e:
                issues.append({
                    'type': 'syntax_error',
                    'severity': 'high',
                    'file': str(file_path.relative_to(self.project_root)),
                    'line': e.lineno,
                    'message': f'语法错误: {e.msg}'
                })
            except Exception as e:
                issues.append({
                    'type': 'import_check_error',
                    'severity': 'medium',
                    'file': str(file_path.relative_to(self.project_root)),
                    'message': f'导入检查失败: {e}'
                })
        
        return issues
    
    def _check_code_layering(self) -> List[Dict]:
        """检查代码分层架构"""
        issues = []
        
        # 定义层次结构
        layers = {
            'api': ['restful'],
            'business': ['utils', 'tasks'],
            'data': ['db']
        }
        
        # 检查跨层依赖
        for layer_name, layer_dirs in layers.items():
            for layer_dir in layer_dirs:
                layer_path = self.project_root / layer_dir
                if layer_path.exists():
                    python_files = list(layer_path.rglob("*.py"))
                    
                    for file_path in python_files:
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            # 检查不当的跨层依赖
                            if layer_name == 'data':  # 数据层不应依赖业务层或API层
                                if 'from utils' in content or 'from restful' in content:
                                    issues.append({
                                        'type': 'layer_violation',
                                        'severity': 'medium',
                                        'file': str(file_path.relative_to(self.project_root)),
                                        'message': "数据层不应依赖业务层或API层"
                                    })
                            
                        except Exception:
                            continue
        
        return issues
    
    def _check_crosscutting_concerns(self) -> List[Dict]:
        """检查横切关注点统一性"""
        issues = []
        
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]
        
        # 检查日志使用的一致性
        inconsistent_logging = []
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否使用了print而不是logger
                if 'print(' in content and 'logger' not in content:
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if 'print(' in line and 'def print' not in line:
                            inconsistent_logging.append({
                                'file': str(file_path.relative_to(self.project_root)),
                                'line': i,
                                'content': line.strip()
                            })
                
            except Exception:
                continue
        
        if inconsistent_logging:
            issues.append({
                'type': 'inconsistent_logging',
                'severity': 'medium',
                'occurrences': inconsistent_logging,
                'message': f"发现{len(inconsistent_logging)}处使用print而非logger"
            })
        
        return issues
    
    def verify_functional_completeness(self) -> None:
        """功能完整性审查（立体思维）"""
        print("🔧 执行功能完整性审查...")
        
        issues = []
        score = 0
        
        # 1. REST API端点完整性
        api_issues = self._check_api_completeness()
        issues.extend(api_issues)
        
        # 2. 数据库操作CRUD完整性
        crud_issues = self._check_crud_completeness()
        issues.extend(crud_issues)
        
        # 3. VirtualHere集成完整性
        vh_issues = self._check_virtualhere_integration()
        issues.extend(vh_issues)
        
        # 4. 主从通信完整性
        comm_issues = self._check_master_slave_communication()
        issues.extend(comm_issues)
        
        # 5. 支撑系统完整性
        support_issues = self._check_support_systems()
        issues.extend(support_issues)
        
        # 计算得分
        total_checks = 5
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        score = max(0, 100 - (high_severity_issues * 12))
        
        self.verification_results['functional_completeness'] = {
            'status': 'passed' if score >= 80 else 'failed',
            'issues': issues,
            'score': score
        }
    
    def _check_api_completeness(self) -> List[Dict]:
        """检查REST API端点完整性"""
        issues = []
        
        api_files = [
            'restful/device_service.py',
            'restful/system_service.py',
            'restful/config_service.py',
            'restful/server_service.py',
            'restful/command_service.py'
        ]
        
        for api_file in api_files:
            api_path = self.project_root / api_file
            if not api_path.exists():
                issues.append({
                    'type': 'missing_api_file',
                    'severity': 'high',
                    'file': api_file,
                    'message': f"API文件不存在: {api_file}"
                })
                continue
            
            try:
                with open(api_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查基本API结构
                if 'Blueprint' not in content:
                    issues.append({
                        'type': 'missing_blueprint',
                        'severity': 'high',
                        'file': api_file,
                        'message': "缺少Blueprint定义"
                    })
                
                # 检查路由定义
                routes = re.findall(r'@\w+\.route\([^)]+\)', content)
                if len(routes) == 0:
                    issues.append({
                        'type': 'no_routes',
                        'severity': 'high',
                        'file': api_file,
                        'message': "未发现任何路由定义"
                    })
                
                # 检查错误处理
                if 'try:' not in content or 'except' not in content:
                    issues.append({
                        'type': 'missing_error_handling',
                        'severity': 'medium',
                        'file': api_file,
                        'message': "缺少错误处理机制"
                    })
                
                # 检查响应格式
                if 'jsonify' not in content:
                    issues.append({
                        'type': 'missing_json_response',
                        'severity': 'medium',
                        'file': api_file,
                        'message': "缺少JSON响应格式"
                    })
                
            except Exception as e:
                issues.append({
                    'type': 'api_analysis_error',
                    'severity': 'medium',
                    'file': api_file,
                    'message': f"API分析失败: {e}"
                })
        
        return issues
    
    def _check_crud_completeness(self) -> List[Dict]:
        """检查数据库CRUD操作完整性"""
        issues = []
        
        dao_files = list(self.project_root.glob("db/*_dao.py"))
        
        for dao_file in dao_files:
            try:
                with open(dao_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查基本CRUD操作
                crud_operations = ['create', 'get', 'update', 'delete']
                missing_operations = []
                
                for operation in crud_operations:
                    if f'def {operation}_' not in content and f'def {operation}(' not in content:
                        missing_operations.append(operation)
                
                if missing_operations:
                    issues.append({
                        'type': 'incomplete_crud',
                        'severity': 'medium',
                        'file': str(dao_file.relative_to(self.project_root)),
                        'missing_operations': missing_operations,
                        'message': f"缺少CRUD操作: {', '.join(missing_operations)}"
                    })
                
                # 检查事务处理
                if 'database.atomic' not in content and 'transaction' not in content:
                    issues.append({
                        'type': 'missing_transaction',
                        'severity': 'low',
                        'file': str(dao_file.relative_to(self.project_root)),
                        'message': "建议添加事务处理"
                    })
                
            except Exception as e:
                issues.append({
                    'type': 'dao_analysis_error',
                    'severity': 'medium',
                    'file': str(dao_file.relative_to(self.project_root)),
                    'message': f"DAO分析失败: {e}"
                })
        
        return issues
    
    def _check_virtualhere_integration(self) -> List[Dict]:
        """检查VirtualHere集成完整性"""
        issues = []
        
        vh_files = [
            'utils/vh_server_manager.py',
            'utils/vh_client.py'
        ]
        
        for vh_file in vh_files:
            vh_path = self.project_root / vh_file
            if not vh_path.exists():
                issues.append({
                    'type': 'missing_vh_component',
                    'severity': 'high',
                    'file': vh_file,
                    'message': f"VirtualHere组件不存在: {vh_file}"
                })
                continue
            
            try:
                with open(vh_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键方法
                if 'vh_server_manager' in vh_file:
                    required_methods = ['start_server', 'stop_server', 'is_server_running']
                    for method in required_methods:
                        if f'def {method}' not in content:
                            issues.append({
                                'type': 'missing_vh_method',
                                'severity': 'medium',
                                'file': vh_file,
                                'method': method,
                                'message': f"缺少VH管理方法: {method}"
                            })
                
                elif 'vh_client' in vh_file:
                    required_methods = ['send_command', 'get_client_state']
                    for method in required_methods:
                        if f'def {method}' not in content:
                            issues.append({
                                'type': 'missing_vh_method',
                                'severity': 'medium',
                                'file': vh_file,
                                'method': method,
                                'message': f"缺少VH客户端方法: {method}"
                            })
                
            except Exception as e:
                issues.append({
                    'type': 'vh_analysis_error',
                    'severity': 'medium',
                    'file': vh_file,
                    'message': f"VH组件分析失败: {e}"
                })
        
        return issues
    
    def _check_master_slave_communication(self) -> List[Dict]:
        """检查主从通信完整性"""
        issues = []
        
        comm_files = [
            'utils/master_communication.py',
            'tasks/heartbeat.py'
        ]
        
        for comm_file in comm_files:
            comm_path = self.project_root / comm_file
            if not comm_path.exists():
                issues.append({
                    'type': 'missing_communication_component',
                    'severity': 'high',
                    'file': comm_file,
                    'message': f"通信组件不存在: {comm_file}"
                })
                continue
            
            try:
                with open(comm_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键方法
                if 'master_communication' in comm_file:
                    required_methods = ['register_slave', 'send_heartbeat', 'send_device_status']
                    for method in required_methods:
                        if f'def {method}' not in content:
                            issues.append({
                                'type': 'missing_comm_method',
                                'severity': 'medium',
                                'file': comm_file,
                                'method': method,
                                'message': f"缺少通信方法: {method}"
                            })
                
            except Exception as e:
                issues.append({
                    'type': 'comm_analysis_error',
                    'severity': 'medium',
                    'file': comm_file,
                    'message': f"通信组件分析失败: {e}"
                })
        
        return issues
    
    def _check_support_systems(self) -> List[Dict]:
        """检查支撑系统完整性"""
        issues = []
        
        # 检查监控系统
        monitor_files = list(self.project_root.glob("utils/*monitor*.py"))
        if not monitor_files:
            issues.append({
                'type': 'missing_monitoring',
                'severity': 'medium',
                'message': "缺少监控系统实现"
            })
        
        # 检查日志系统
        logger_file = self.project_root / 'utils/logger.py'
        if not logger_file.exists():
            issues.append({
                'type': 'missing_logger',
                'severity': 'high',
                'file': 'utils/logger.py',
                'message': "缺少日志系统实现"
            })
        
        # 检查配置管理
        config_manager_file = self.project_root / 'utils/config_manager.py'
        if not config_manager_file.exists():
            issues.append({
                'type': 'missing_config_manager',
                'severity': 'high',
                'file': 'utils/config_manager.py',
                'message': "缺少配置管理器"
            })
        
        return issues

    def verify_logical_consistency(self) -> None:
        """逻辑一致性审查（逆向思维）"""
        print("🧠 执行逻辑一致性审查...")

        issues = []
        score = 0

        # 1. API文档与实现一致性
        api_consistency_issues = self._check_api_documentation_consistency()
        issues.extend(api_consistency_issues)

        # 2. 用户场景与功能实现一致性
        scenario_issues = self._check_user_scenario_consistency()
        issues.extend(scenario_issues)

        # 3. 错误处理覆盖度
        error_coverage_issues = self._check_error_handling_coverage()
        issues.extend(error_coverage_issues)

        # 4. 安全需求与认证机制一致性
        security_issues = self._check_security_consistency()
        issues.extend(security_issues)

        # 计算得分
        total_checks = 4
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        score = max(0, 100 - (high_severity_issues * 15))

        self.verification_results['logical_consistency'] = {
            'status': 'passed' if score >= 75 else 'failed',
            'issues': issues,
            'score': score
        }

    def verify_code_quality(self) -> None:
        """代码质量深度检查"""
        print("📝 执行代码质量深度检查...")

        issues = []
        score = 0

        # 1. 代码实现检查
        implementation_issues = self._check_code_implementation()
        issues.extend(implementation_issues)

        # 2. 数据处理检查
        data_processing_issues = self._check_data_processing()
        issues.extend(data_processing_issues)

        # 3. 配置和环境检查
        config_issues = self._check_configuration_environment()
        issues.extend(config_issues)

        # 计算得分
        total_checks = 3
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        medium_severity_issues = len([i for i in issues if i['severity'] == 'medium'])
        score = max(0, 100 - (high_severity_issues * 20) - (medium_severity_issues * 5))

        self.verification_results['code_quality'] = {
            'status': 'passed' if score >= 80 else 'failed',
            'issues': issues,
            'score': score
        }

    def verify_production_optimization(self) -> None:
        """生产环境优化检查"""
        print("🚀 执行生产环境优化检查...")

        issues = []
        score = 0

        # 1. 安全加固
        security_issues = self._check_security_hardening()
        issues.extend(security_issues)

        # 2. 性能优化
        performance_issues = self._check_performance_optimization()
        issues.extend(performance_issues)

        # 3. 稳定性保障
        stability_issues = self._check_stability_assurance()
        issues.extend(stability_issues)

        # 计算得分
        total_checks = 3
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        score = max(0, 100 - (high_severity_issues * 25))

        self.verification_results['production_optimization'] = {
            'status': 'passed' if score >= 85 else 'failed',
            'issues': issues,
            'score': score
        }

    def verify_integration_points(self) -> None:
        """集成点检查"""
        print("🔗 执行集成点检查...")

        issues = []
        score = 0

        # 1. VirtualHere服务集成
        vh_integration_issues = self._check_vh_service_integration()
        issues.extend(vh_integration_issues)

        # 2. 主服务器通信集成
        master_comm_issues = self._check_master_communication_integration()
        issues.extend(master_comm_issues)

        # 3. 数据库连接池
        db_pool_issues = self._check_database_pool()
        issues.extend(db_pool_issues)

        # 4. 监控系统集成
        monitoring_issues = self._check_monitoring_integration()
        issues.extend(monitoring_issues)

        # 计算得分
        total_checks = 4
        high_severity_issues = len([i for i in issues if i['severity'] == 'high'])
        score = max(0, 100 - (high_severity_issues * 20))

        self.verification_results['integration_points'] = {
            'status': 'passed' if score >= 80 else 'failed',
            'issues': issues,
            'score': score
        }

    def generate_overall_assessment(self) -> None:
        """生成总体评估"""
        print("📊 生成总体评估...")

        recommendations = []

        # 分析各维度问题并生成建议
        for dimension, result in self.verification_results.items():
            if dimension == 'overall_assessment':
                continue

            if result['status'] == 'failed':
                high_issues = [i for i in result['issues'] if i['severity'] == 'high']
                if high_issues:
                    recommendations.append({
                        'dimension': dimension,
                        'priority': 'high',
                        'issue_count': len(high_issues),
                        'recommendation': f"修复{dimension}中的{len(high_issues)}个高严重性问题"
                    })

        self.verification_results['overall_assessment']['recommendations'] = recommendations

    def _check_api_documentation_consistency(self) -> List[Dict]:
        """检查API文档与实现一致性"""
        issues = []

        # 检查API文档是否存在
        api_doc_path = self.project_root / 'docs/api.md'
        if not api_doc_path.exists():
            issues.append({
                'type': 'missing_api_documentation',
                'severity': 'medium',
                'message': "缺少API文档"
            })

        return issues

    def _check_user_scenario_consistency(self) -> List[Dict]:
        """检查用户场景与功能实现一致性"""
        issues = []

        # 检查关键用户场景的实现
        scenarios = [
            {'name': 'device_discovery', 'files': ['tasks/device_monitor.py']},
            {'name': 'device_sharing', 'files': ['utils/vh_server_manager.py']},
            {'name': 'master_registration', 'files': ['utils/master_communication.py']}
        ]

        for scenario in scenarios:
            missing_files = []
            for file_path in scenario['files']:
                if not (self.project_root / file_path).exists():
                    missing_files.append(file_path)

            if missing_files:
                issues.append({
                    'type': 'missing_scenario_implementation',
                    'severity': 'high',
                    'scenario': scenario['name'],
                    'missing_files': missing_files,
                    'message': f"用户场景{scenario['name']}缺少实现文件"
                })

        return issues

    def _check_error_handling_coverage(self) -> List[Dict]:
        """检查错误处理覆盖度"""
        issues = []

        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]

        files_without_error_handling = []

        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查是否有错误处理
                if 'try:' not in content and 'except' not in content:
                    files_without_error_handling.append(str(file_path.relative_to(self.project_root)))

            except Exception:
                continue

        if files_without_error_handling:
            issues.append({
                'type': 'insufficient_error_handling',
                'severity': 'medium',
                'files': files_without_error_handling,
                'message': f"{len(files_without_error_handling)}个文件缺少错误处理"
            })

        return issues

    def _check_security_consistency(self) -> List[Dict]:
        """检查安全需求与认证机制一致性"""
        issues = []

        # 检查API认证
        api_files = list(self.project_root.glob("restful/*.py"))
        unprotected_apis = []

        for api_file in api_files:
            try:
                with open(api_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查是否有认证装饰器或中间件
                if '@' in content and 'route' in content:
                    if 'auth' not in content.lower() and 'token' not in content.lower():
                        unprotected_apis.append(str(api_file.relative_to(self.project_root)))

            except Exception:
                continue

        if unprotected_apis:
            issues.append({
                'type': 'unprotected_api_endpoints',
                'severity': 'high',
                'files': unprotected_apis,
                'message': f"{len(unprotected_apis)}个API文件可能缺少认证机制"
            })

        return issues

    def _check_code_implementation(self) -> List[Dict]:
        """检查代码实现质量"""
        issues = []

        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]

        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查类型注解
                function_count = len(re.findall(r'def\s+\w+\s*\(', content))
                annotated_functions = len(re.findall(r'def\s+\w+\s*\([^)]*\)\s*->', content))

                if function_count > 0 and annotated_functions / function_count < 0.8:
                    issues.append({
                        'type': 'insufficient_type_annotations',
                        'severity': 'low',
                        'file': str(file_path.relative_to(self.project_root)),
                        'message': f"类型注解覆盖率低: {annotated_functions}/{function_count}"
                    })

                # 检查资源管理
                file_opens = len(re.findall(r'open\s*\(', content))
                with_statements = len(re.findall(r'with\s+open\s*\(', content))

                if file_opens > with_statements:
                    issues.append({
                        'type': 'unsafe_resource_management',
                        'severity': 'medium',
                        'file': str(file_path.relative_to(self.project_root)),
                        'message': "存在未使用with语句的文件操作"
                    })

            except Exception:
                continue

        return issues

    def _check_data_processing(self) -> List[Dict]:
        """检查数据处理安全性"""
        issues = []

        # 检查SQL注入防护
        dao_files = list(self.project_root.glob("db/*_dao.py"))

        for dao_file in dao_files:
            try:
                with open(dao_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查是否使用字符串拼接构建SQL
                if '.format(' in content or '% ' in content:
                    issues.append({
                        'type': 'potential_sql_injection',
                        'severity': 'high',
                        'file': str(dao_file.relative_to(self.project_root)),
                        'message': "可能存在SQL注入风险"
                    })

            except Exception:
                continue

        return issues

    def _check_configuration_environment(self) -> List[Dict]:
        """检查配置和环境"""
        issues = []

        # 检查配置文件一致性
        config_template = self.project_root / 'config/slave_server.ini.template'
        config_manager = self.project_root / 'utils/config_manager.py'

        if config_template.exists() and config_manager.exists():
            try:
                with open(config_template, 'r', encoding='utf-8') as f:
                    template_content = f.read()

                with open(config_manager, 'r', encoding='utf-8') as f:
                    manager_content = f.read()

                # 检查配置节是否在管理器中处理
                import re
                sections = re.findall(r'\[(\w+)\]', template_content)

                for section in sections:
                    if f'get_{section}_config' not in manager_content:
                        issues.append({
                            'type': 'unhandled_config_section',
                            'severity': 'medium',
                            'section': section,
                            'message': f"配置节未在管理器中处理: {section}"
                        })

            except Exception:
                pass

        return issues

    def _check_security_hardening(self) -> List[Dict]:
        """检查安全加固"""
        issues = []

        # 检查调试代码
        python_files = list(self.project_root.rglob("*.py"))
        python_files = [f for f in python_files if 'VHServer' not in str(f) and '__pycache__' not in str(f)]

        debug_code_found = []

        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查调试代码
                debug_patterns = [r'print\s*\(', r'pdb\.set_trace', r'breakpoint\s*\(']
                for pattern in debug_patterns:
                    if re.search(pattern, content):
                        debug_code_found.append(str(file_path.relative_to(self.project_root)))
                        break

            except Exception:
                continue

        if debug_code_found:
            issues.append({
                'type': 'debug_code_present',
                'severity': 'medium',
                'files': debug_code_found,
                'message': f"{len(debug_code_found)}个文件包含调试代码"
            })

        return issues

    def _check_performance_optimization(self) -> List[Dict]:
        """检查性能优化"""
        issues = []

        # 检查数据库查询优化
        dao_files = list(self.project_root.glob("db/*_dao.py"))

        for dao_file in dao_files:
            try:
                with open(dao_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查是否使用了事务
                if 'database.atomic' not in content and 'transaction' not in content:
                    issues.append({
                        'type': 'missing_transaction_optimization',
                        'severity': 'low',
                        'file': str(dao_file.relative_to(self.project_root)),
                        'message': "建议使用事务优化数据库操作"
                    })

            except Exception:
                continue

        return issues

    def _check_stability_assurance(self) -> List[Dict]:
        """检查稳定性保障"""
        issues = []

        # 检查错误恢复机制
        error_handler_file = self.project_root / 'utils/error_handler.py'
        if not error_handler_file.exists():
            issues.append({
                'type': 'missing_error_recovery',
                'severity': 'medium',
                'message': "缺少错误恢复机制"
            })

        return issues

    def _check_vh_service_integration(self) -> List[Dict]:
        """检查VirtualHere服务集成"""
        issues = []

        vh_manager_file = self.project_root / 'utils/vh_server_manager.py'
        if vh_manager_file.exists():
            try:
                with open(vh_manager_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查关键集成点
                if 'start_server' not in content:
                    issues.append({
                        'type': 'missing_vh_integration',
                        'severity': 'high',
                        'message': "VH服务器管理器缺少启动方法"
                    })

            except Exception:
                pass

        return issues

    def _check_master_communication_integration(self) -> List[Dict]:
        """检查主服务器通信集成"""
        issues = []

        master_comm_file = self.project_root / 'utils/master_communication.py'
        if master_comm_file.exists():
            try:
                with open(master_comm_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查关键通信方法
                required_methods = ['register_slave', 'send_heartbeat']
                for method in required_methods:
                    if method not in content:
                        issues.append({
                            'type': 'missing_communication_method',
                            'severity': 'high',
                            'method': method,
                            'message': f"缺少通信方法: {method}"
                        })

            except Exception:
                pass

        return issues

    def _check_database_pool(self) -> List[Dict]:
        """检查数据库连接池"""
        issues = []

        # 检查数据库配置
        db_init_file = self.project_root / 'db/__init__.py'
        if db_init_file.exists():
            try:
                with open(db_init_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查连接池配置
                if 'pool' not in content.lower():
                    issues.append({
                        'type': 'missing_connection_pool',
                        'severity': 'medium',
                        'message': "建议配置数据库连接池"
                    })

            except Exception:
                pass

        return issues

    def _check_monitoring_integration(self) -> List[Dict]:
        """检查监控系统集成"""
        issues = []

        # 检查监控文件
        monitor_files = list(self.project_root.glob("utils/*monitor*.py"))
        if not monitor_files:
            issues.append({
                'type': 'missing_monitoring_system',
                'severity': 'medium',
                'message': "缺少监控系统集成"
            })

        return issues

    def print_verification_summary(self) -> None:
        """打印验证总结"""
        print("\n" + "="*80)
        print("📋 代码级功能校验核查报告")
        print("="*80)
        
        # 各维度得分
        dimensions = [
            ('架构完整性', 'architecture_integrity'),
            ('功能完整性', 'functional_completeness'),
            ('逻辑一致性', 'logical_consistency'),
            ('代码质量', 'code_quality'),
            ('生产环境优化', 'production_optimization'),
            ('集成点检查', 'integration_points')
        ]
        
        total_score = 0
        completed_verifications = 0
        
        for name, key in dimensions:
            result = self.verification_results[key]
            if result['status'] != 'unknown':
                status_icon = "✅" if result['status'] == 'passed' else "❌"
                score = result['score']
                issue_count = len(result['issues'])
                print(f"   {status_icon} {name}: {score:.1f}/100 ({issue_count}个问题)")
                total_score += score
                completed_verifications += 1
        
        # 总体评估
        if completed_verifications > 0:
            overall_score = total_score / completed_verifications
            
            print(f"\n🎯 总体评分: {overall_score:.1f}/100")
            
            if overall_score >= 90:
                print("✅ 代码质量状态: 优秀")
                status = 'excellent'
            elif overall_score >= 80:
                print("✅ 代码质量状态: 良好")
                status = 'good'
            elif overall_score >= 70:
                print("⚠️  代码质量状态: 需要改进")
                status = 'needs_improvement'
            else:
                print("❌ 代码质量状态: 存在严重问题")
                status = 'critical_issues'
            
            self.verification_results['overall_assessment']['status'] = status
            self.verification_results['overall_assessment']['score'] = overall_score

def main() -> None:
    """主函数"""
    verifier = CodeLevelFunctionalVerifier()
    results = verifier.conduct_code_level_verification()
    verifier.print_verification_summary()
    
    # 保存验证报告
    report_file = Path(__file__).parent.parent / 'code_level_verification_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细验证报告已保存到: {report_file}")
    
    return 0

if __name__ == '__main__':
    exit(main())
