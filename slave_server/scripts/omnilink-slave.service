[Unit]
Description=OmniLink从服务器
Documentation=https://github.com/your-org/omnilink-slave-server
After=network.target network-online.target
Wants=network-online.target
Requires=network.target

[Service]
Type=simple
User=omnilink
Group=omnilink
WorkingDirectory=/opt/omnilink
Environment=PATH=/opt/omnilink/venv/bin
Environment=PYTHONPATH=/opt/omnilink
Environment=FLASK_ENV=production
Environment=LOG_LEVEL=INFO

# 主进程
ExecStart=/opt/omnilink/venv/bin/python main.py
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID

# 重启策略
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/omnilink/data /opt/omnilink/logs /opt/omnilink/tmp

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryLimit=2G
CPUQuota=400%

# 进程管理
KillMode=mixed
KillSignal=SIGTERM
TimeoutStartSec=60
TimeoutStopSec=30

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=omnilink-slave

[Install]
WantedBy=multi-user.target
