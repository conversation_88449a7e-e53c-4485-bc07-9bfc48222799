#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VHServer目录依赖分析脚本
分析删除VHServer目录对当前OmniLink从服务器项目的影响
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Any

class VHServerDependencyAnalyzer:
    """VHServer依赖分析器"""
    
    def __init__(self) -> None:
        self.project_root = Path(__file__).parent.parent
        self.vhserver_path = self.project_root / "VHServer"
        self.analysis_results = {
            'import_dependencies': [],
            'config_references': [],
            'script_dependencies': [],
            'file_references': [],
            'functional_dependencies': [],
            'safe_to_delete': True,
            'risks': [],
            'recommendations': []
        }
    
    def analyze_all(self) -> Dict[str, Any]:
        """运行完整的依赖分析"""
        logger.info("🔍 开始VHServer依赖分析...")
        
        # 检查VHServer目录是否存在
        if not self.vhserver_path.exists():
            logger.info("❌ VHServer目录不存在")
            return self.analysis_results
        
        logger.info(f"📁 VHServer目录存在: {self.vhserver_path}")
        
        # 执行各项分析
        self.analyze_import_dependencies()
        self.analyze_config_references()
        self.analyze_script_dependencies()
        self.analyze_file_references()
        self.analyze_functional_dependencies()
        
        # 生成最终结论
        self.generate_conclusion()
        
        return self.analysis_results
    
    def analyze_import_dependencies(self) -> None:
        """分析导入依赖"""
        logger.info("📦 分析导入依赖...")
        
        # 获取当前项目的所有Python文件（排除VHServer）
        python_files = []
        for pattern in ['*.py', '*/*.py', '*/*/*.py']:
            python_files.extend(self.project_root.glob(pattern))
        
        # 过滤掉VHServer目录下的文件
        python_files = [f for f in python_files if 'VHServer' not in str(f)]
        
        import_patterns = [
            r'from\s+VHServer\.',
            r'import\s+VHServer\.',
            r'from\s+\.VHServer',
            r'import\s+\.VHServer'
        ]
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in import_patterns:
                    matches = re.findall(pattern, content, re.MULTILINE)
                    if matches:
                        self.analysis_results['import_dependencies'].append({
                            'file': str(file_path.relative_to(self.project_root)),
                            'pattern': pattern,
                            'matches': matches
                        })
            except Exception as e:
                logger.info(f"⚠️  读取文件失败 {file_path}: {e}")
    
    def analyze_config_references(self) -> None:
        """分析配置文件引用"""
        logger.info("⚙️  分析配置文件引用...")
        
        config_files = [
            'config/slave_server.ini.template',
            'config/development.ini.example',
            'config/production.ini.example',
            'docker-compose.yml',
            'docker-compose.dev.yml',
            'docker-compose.prod.yml',
            '.env.template',
            'config/docker.env.example'
        ]
        
        vhserver_patterns = [
            r'VHServer',
            r'/VHServer',
            r'./VHServer',
            r'vhserver',
            r'vh_server'
        ]
        
        for config_file in config_files:
            config_path = self.project_root / config_file
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    for pattern in vhserver_patterns:
                        if re.search(pattern, content, re.IGNORECASE):
                            self.analysis_results['config_references'].append({
                                'file': config_file,
                                'pattern': pattern,
                                'context': 'Found reference in config'
                            })
                except Exception as e:
                    logger.info(f"⚠️  读取配置文件失败 {config_path}: {e}")
    
    def analyze_script_dependencies(self) -> None:
        """分析脚本依赖"""
        logger.info("📜 分析脚本依赖...")
        
        script_files = [
            'deploy.sh',
            'Dockerfile',
            'docker-entrypoint.sh',
            'Makefile',
            'scripts/install.sh'
        ]
        
        for script_file in script_files:
            script_path = self.project_root / script_file
            if script_path.exists():
                try:
                    with open(script_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if 'VHServer' in content:
                        # 查找具体的引用行
                        lines = content.split('\n')
                        for i, line in enumerate(lines, 1):
                            if 'VHServer' in line:
                                self.analysis_results['script_dependencies'].append({
                                    'file': script_file,
                                    'line': i,
                                    'content': line.strip(),
                                    'context': 'VHServer reference found'
                                })
                except Exception as e:
                    logger.info(f"⚠️  读取脚本文件失败 {script_path}: {e}")
    
    def analyze_file_references(self) -> None:
        """分析文件引用"""
        logger.info("📄 分析文件引用...")
        
        # 检查是否有符号链接或硬链接指向VHServer
        try:
            for item in self.project_root.rglob('*'):
                if item.is_symlink():
                    target = item.resolve()
                    if 'VHServer' in str(target):
                        self.analysis_results['file_references'].append({
                            'type': 'symlink',
                            'source': str(item.relative_to(self.project_root)),
                            'target': str(target),
                            'risk': 'high'
                        })
        except Exception as e:
            logger.info(f"⚠️  检查符号链接失败: {e}")
    
    def analyze_functional_dependencies(self) -> None:
        """分析功能依赖"""
        logger.info("🔧 分析功能依赖...")
        
        # 检查当前项目的核心功能是否完整
        core_modules = [
            'main.py',
            'utils/config_manager.py',
            'utils/vh_server_manager.py',
            'restful/device_service.py',
            'restful/system_service.py',
            'db/models.py',
            'tasks/device_monitor.py'
        ]
        
        missing_modules = []
        for module in core_modules:
            module_path = self.project_root / module
            if not module_path.exists():
                missing_modules.append(module)
        
        if missing_modules:
            self.analysis_results['functional_dependencies'].append({
                'type': 'missing_core_modules',
                'modules': missing_modules,
                'risk': 'high'
            })
        
        # 检查VirtualHere集成是否独立
        vh_manager_path = self.project_root / 'utils/vh_server_manager.py'
        if vh_manager_path.exists():
            try:
                with open(vh_manager_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'VHServer' in content:
                    self.analysis_results['functional_dependencies'].append({
                        'type': 'vh_manager_dependency',
                        'file': 'utils/vh_server_manager.py',
                        'risk': 'medium',
                        'description': 'VirtualHere管理器可能依赖VHServer'
                    })
                else:
                    self.analysis_results['functional_dependencies'].append({
                        'type': 'vh_manager_independent',
                        'file': 'utils/vh_server_manager.py',
                        'risk': 'none',
                        'description': 'VirtualHere管理器独立实现'
                    })
            except Exception as e:
                logger.info(f"⚠️  检查VH管理器失败: {e}")
    
    def generate_conclusion(self) -> None:
        """生成分析结论"""
        logger.info("📊 生成分析结论...")
        
        # 评估风险
        high_risk_count = 0
        medium_risk_count = 0
        
        # 检查导入依赖
        if self.analysis_results['import_dependencies']:
            high_risk_count += len(self.analysis_results['import_dependencies'])
            self.analysis_results['risks'].append({
                'level': 'high',
                'type': 'import_dependency',
                'description': f"发现 {len(self.analysis_results['import_dependencies'])} 个导入依赖",
                'impact': '删除VHServer将导致导入错误'
            })
        
        # 检查配置引用
        if self.analysis_results['config_references']:
            medium_risk_count += len(self.analysis_results['config_references'])
            self.analysis_results['risks'].append({
                'level': 'medium',
                'type': 'config_reference',
                'description': f"发现 {len(self.analysis_results['config_references'])} 个配置引用",
                'impact': '可能需要更新配置文件'
            })
        
        # 检查脚本依赖
        if self.analysis_results['script_dependencies']:
            medium_risk_count += len(self.analysis_results['script_dependencies'])
            self.analysis_results['risks'].append({
                'level': 'medium',
                'type': 'script_dependency',
                'description': f"发现 {len(self.analysis_results['script_dependencies'])} 个脚本依赖",
                'impact': '可能需要更新部署脚本'
            })
        
        # 检查文件引用
        if self.analysis_results['file_references']:
            high_risk_count += len(self.analysis_results['file_references'])
            self.analysis_results['risks'].append({
                'level': 'high',
                'type': 'file_reference',
                'description': f"发现 {len(self.analysis_results['file_references'])} 个文件引用",
                'impact': '删除VHServer将破坏文件链接'
            })
        
        # 生成建议
        if high_risk_count == 0 and medium_risk_count == 0:
            self.analysis_results['safe_to_delete'] = True
            self.analysis_results['recommendations'].append({
                'action': 'safe_delete',
                'description': '可以安全删除VHServer目录',
                'reason': '未发现任何依赖关系'
            })
        elif high_risk_count == 0:
            self.analysis_results['safe_to_delete'] = True
            self.analysis_results['recommendations'].append({
                'action': 'conditional_delete',
                'description': '可以删除VHServer目录，但需要先处理配置引用',
                'reason': f'发现 {medium_risk_count} 个中等风险项'
            })
        else:
            self.analysis_results['safe_to_delete'] = False
            self.analysis_results['recommendations'].append({
                'action': 'do_not_delete',
                'description': '不建议删除VHServer目录',
                'reason': f'发现 {high_risk_count} 个高风险依赖'
            })
    
    def print_report(self) -> None:
        """打印分析报告"""
        logger.info("\n" + "="*60)
        logger.info("📋 VHServer依赖分析报告")
        logger.info("="*60)
        
        # 导入依赖
        if self.analysis_results['import_dependencies']:
            logger.info(f"\n❌ 导入依赖 ({len(self.analysis_results['import_dependencies'])} 个):")
            for dep in self.analysis_results['import_dependencies']:
                logger.info(f"   📄 {dep['file']}: {dep['pattern']}")
        else:
            logger.info(f"\n✅ 导入依赖: 无")
        
        # 配置引用
        if self.analysis_results['config_references']:
            logger.info(f"\n⚠️  配置引用 ({len(self.analysis_results['config_references'])} 个):")
            for ref in self.analysis_results['config_references']:
                logger.info(f"   📄 {ref['file']}: {ref['pattern']}")
        else:
            logger.info(f"\n✅ 配置引用: 无")
        
        # 脚本依赖
        if self.analysis_results['script_dependencies']:
            logger.info(f"\n⚠️  脚本依赖 ({len(self.analysis_results['script_dependencies'])} 个):")
            for dep in self.analysis_results['script_dependencies']:
                logger.info(f"   📄 {dep['file']}:{dep['line']}: {dep['content']}")
        else:
            logger.info(f"\n✅ 脚本依赖: 无")
        
        # 风险评估
        logger.info(f"\n🎯 风险评估:")
        if self.analysis_results['risks']:
            for risk in self.analysis_results['risks']:
                level_icon = "🔴" if risk['level'] == 'high' else "🟡"
                logger.info(f"   {level_icon} {risk['type']}: {risk['description']}")
        else:
            logger.info(f"   ✅ 无风险")
        
        # 建议
        logger.info(f"\n💡 建议:")
        for rec in self.analysis_results['recommendations']:
            action_icon = "✅" if rec['action'] == 'safe_delete' else "⚠️" if rec['action'] == 'conditional_delete' else "❌"
            logger.info(f"   {action_icon} {rec['description']}")
            logger.info(f"      理由: {rec['reason']}")
        
        # 最终结论
        logger.info(f"\n🎯 最终结论:")
        if self.analysis_results['safe_to_delete']:
            logger.info(f"   ✅ VHServer目录可以安全删除")
        else:
            logger.info(f"   ❌ VHServer目录不建议删除")

def main() -> None:
    """主函数"""
    analyzer = VHServerDependencyAnalyzer()
    results = analyzer.analyze_all()
    analyzer.print_report()
    
    # 保存详细报告
    report_file = Path(__file__).parent.parent / 'vhserver_dependency_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"\n📄 详细报告已保存到: {report_file}")
    
    return 0 if results['safe_to_delete'] else 1

if __name__ == '__main__':
    exit(main())
