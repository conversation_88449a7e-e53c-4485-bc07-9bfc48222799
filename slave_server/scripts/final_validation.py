#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证脚本
验证所有代码质量优化是否正确应用
"""

import os
import sys
import importlib.util
import ast
from pathlib import Path
from typing import Dict, List, Any

class FinalValidator:
    """最终验证器"""
    
    def __init__(self) -> None:
        self.project_root = Path(__file__).parent.parent
        self.results = {
            'import_validation': {'status': 'unknown', 'details': []},
            'type_annotation_validation': {'status': 'unknown', 'details': []},
            'configuration_validation': {'status': 'unknown', 'details': []},
            'api_validation': {'status': 'unknown', 'details': []},
            'security_validation': {'status': 'unknown', 'details': []}
        }
    
    def validate_all(self) -> Dict[str, Any]:
        """运行所有验证"""
        logger.info("🔍 开始最终验证...")
        
        self.validate_imports()
        self.validate_type_annotations()
        self.validate_configuration()
        self.validate_api_completeness()
        self.validate_security()
        
        return self.generate_final_report()
    
    def validate_imports(self) -> None:
        """验证导入语句"""
        logger.info("📦 验证导入语句...")
        
        try:
            # 验证主要模块可以正常导入
            main_modules = [
                'utils.config_manager',
                'utils.logger',
                'utils.error_handler',
                'db.models',
                'db.device_dao',
                'restful.device_service',
                'restful.system_service'
            ]
            
            failed_imports = []
            for module_name in main_modules:
                try:
                    module_path = self.project_root / module_name.replace('.', '/')
                    if module_path.with_suffix('.py').exists():
                        spec = importlib.util.spec_from_file_location(
                            module_name, 
                            module_path.with_suffix('.py')
                        )
                        if spec and spec.loader:
                            module = importlib.util.module_from_spec(spec)
                            # 不实际执行，只检查语法
                            with open(module_path.with_suffix('.py'), 'r', encoding='utf-8') as f:
                                ast.parse(f.read())
                except Exception as e:
                    failed_imports.append(f"{module_name}: {e}")
            
            if failed_imports:
                self.results['import_validation']['status'] = 'failed'
                self.results['import_validation']['details'] = failed_imports
            else:
                self.results['import_validation']['status'] = 'passed'
                self.results['import_validation']['details'] = ['所有主要模块导入验证通过']
                
        except Exception as e:
            self.results['import_validation']['status'] = 'error'
            self.results['import_validation']['details'] = [f"验证过程出错: {e}"]
    
    def validate_type_annotations(self) -> None:
        """验证类型注解"""
        logger.info("🏷️  验证类型注解...")
        
        try:
            key_files = [
                'utils/config_manager.py',
                'main.py',
                'restful/device_service.py',
                'db/device_dao.py'
            ]
            
            annotation_issues = []
            for file_path in key_files:
                full_path = self.project_root / file_path
                if full_path.exists():
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否有类型导入
                    if 'from typing import' not in content and 'typing.' not in content:
                        # 检查是否有函数定义
                        if 'def ' in content:
                            annotation_issues.append(f"{file_path}: 缺少typing导入")
                    
                    # 检查主要函数是否有返回类型注解
                    import re
                    func_without_return_type = re.findall(r'def\s+(\w+)\s*\([^)]*\):', content)
                    if func_without_return_type:
                        public_funcs = [f for f in func_without_return_type if not f.startswith('_')]
                        if len(public_funcs) > 2:  # 允许少量函数没有注解
                            annotation_issues.append(f"{file_path}: {len(public_funcs)}个公共函数缺少返回类型注解")
            
            if annotation_issues:
                self.results['type_annotation_validation']['status'] = 'warning'
                self.results['type_annotation_validation']['details'] = annotation_issues
            else:
                self.results['type_annotation_validation']['status'] = 'passed'
                self.results['type_annotation_validation']['details'] = ['类型注解验证通过']
                
        except Exception as e:
            self.results['type_annotation_validation']['status'] = 'error'
            self.results['type_annotation_validation']['details'] = [f"验证过程出错: {e}"]
    
    def validate_configuration(self) -> None:
        """验证配置文件"""
        logger.info("⚙️  验证配置文件...")
        
        try:
            required_configs = [
                'config/slave_server.ini.template',
                'config/vhusbd.conf.template',
                'config/production.ini.example',
                'config/development.ini.example'
            ]
            
            missing_configs = []
            for config_file in required_configs:
                config_path = self.project_root / config_file
                if not config_path.exists():
                    missing_configs.append(config_file)
            
            # 检查配置文件内容
            config_issues = []
            main_config = self.project_root / 'config/slave_server.ini.template'
            if main_config.exists():
                with open(main_config, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                required_sections = ['server', 'database', 'virtualhere', 'logging', 'master']
                for section in required_sections:
                    if f'[{section}]' not in content:
                        config_issues.append(f"主配置文件缺少[{section}]节")
            
            all_issues = missing_configs + config_issues
            if all_issues:
                self.results['configuration_validation']['status'] = 'failed'
                self.results['configuration_validation']['details'] = all_issues
            else:
                self.results['configuration_validation']['status'] = 'passed'
                self.results['configuration_validation']['details'] = ['配置文件验证通过']
                
        except Exception as e:
            self.results['configuration_validation']['status'] = 'error'
            self.results['configuration_validation']['details'] = [f"验证过程出错: {e}"]
    
    def validate_api_completeness(self) -> None:
        """验证API完整性"""
        logger.info("🌐 验证API完整性...")
        
        try:
            api_files = [
                'restful/device_service.py',
                'restful/system_service.py',
                'restful/config_service.py',
                'restful/server_service.py'
            ]
            
            api_issues = []
            for api_file in api_files:
                api_path = self.project_root / api_file
                if not api_path.exists():
                    api_issues.append(f"缺少API文件: {api_file}")
                else:
                    with open(api_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否有Blueprint定义
                    if 'Blueprint' not in content:
                        api_issues.append(f"{api_file}: 缺少Blueprint定义")
                    
                    # 检查是否有路由定义
                    if '@' not in content or 'route' not in content:
                        api_issues.append(f"{api_file}: 缺少路由定义")
            
            if api_issues:
                self.results['api_validation']['status'] = 'failed'
                self.results['api_validation']['details'] = api_issues
            else:
                self.results['api_validation']['status'] = 'passed'
                self.results['api_validation']['details'] = ['API完整性验证通过']
                
        except Exception as e:
            self.results['api_validation']['status'] = 'error'
            self.results['api_validation']['details'] = [f"验证过程出错: {e}"]
    
    def validate_security(self) -> None:
        """验证安全配置"""
        logger.info("🔒 验证安全配置...")
        
        try:
            security_issues = []
            
            # 检查开发配置文件是否清理了敏感信息
            dev_config = self.project_root / 'config/development.ini.example'
            if dev_config.exists():
                with open(dev_config, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否还有测试令牌
                if 'dev-auth-token' in content or 'dev-api-key' in content:
                    security_issues.append("开发配置文件仍包含测试令牌")
            
            # 检查主程序是否有调试代码
            main_file = self.project_root / 'main.py'
            if main_file.exists():
                with open(main_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'print(' in content and 'logger' not in content:
                    security_issues.append("主程序仍包含print语句")
            
            if security_issues:
                self.results['security_validation']['status'] = 'failed'
                self.results['security_validation']['details'] = security_issues
            else:
                self.results['security_validation']['status'] = 'passed'
                self.results['security_validation']['details'] = ['安全配置验证通过']
                
        except Exception as e:
            self.results['security_validation']['status'] = 'error'
            self.results['security_validation']['details'] = [f"验证过程出错: {e}"]
    
    def generate_final_report(self) -> Dict[str, Any]:
        """生成最终报告"""
        passed_count = sum(1 for result in self.results.values() if result['status'] == 'passed')
        total_count = len(self.results)
        
        overall_status = 'passed' if passed_count == total_count else 'failed'
        
        logger.info(f"\n📊 最终验证完成:")
        logger.info(f"   总体状态: {'✅ 通过' if overall_status == 'passed' else '❌ 失败'}")
        logger.info(f"   通过项目: {passed_count}/{total_count}")
        
        for category, result in self.results.items():
            status_icon = {
                'passed': '✅',
                'failed': '❌',
                'warning': '⚠️',
                'error': '🔥'
            }.get(result['status'], '❓')
            
            logger.info(f"   {status_icon} {category}: {result['status']}")
            if result['status'] != 'passed':
                for detail in result['details'][:3]:  # 只显示前3个
                    logger.info(f"      - {detail}")
        
        final_report = {
            'overall_status': overall_status,
            'passed_count': passed_count,
            'total_count': total_count,
            'details': self.results,
            'summary': {
                'production_ready': overall_status == 'passed',
                'critical_issues': sum(1 for r in self.results.values() if r['status'] == 'failed'),
                'warnings': sum(1 for r in self.results.values() if r['status'] == 'warning')
            }
        }
        
        return final_report

def main() -> None:
    """主函数"""
    validator = FinalValidator()
    report = validator.validate_all()
    
    # 保存报告
    report_file = Path(__file__).parent.parent / 'final_validation_report.json'
    import json
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    logger.info(f"\n📄 详细报告已保存到: {report_file}")
    
    if report['summary']['production_ready']:
        logger.info("\n🚀 项目已准备好投入生产环境！")
    else:
        logger.info(f"\n⚠️  发现 {report['summary']['critical_issues']} 个关键问题需要解决")
    
    # 返回适当的退出码
    sys.exit(0 if report['overall_status'] == 'passed' else 1)

if __name__ == '__main__':
    main()
