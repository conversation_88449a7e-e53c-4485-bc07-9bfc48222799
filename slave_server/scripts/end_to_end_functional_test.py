#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端功能测试脚本
验证OmniLink从服务器的完整功能流程
"""

import os
import sys
import time
import json
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional

class EndToEndFunctionalTester:
    """端到端功能测试器"""
    
    def __init__(self) -> None:
        self.project_root = Path(__file__).parent.parent
        self.test_results = {
            'server_startup': {'status': 'unknown', 'details': {}},
            'api_endpoints': {'status': 'unknown', 'details': {}},
            'virtualhere_integration': {'status': 'unknown', 'details': {}},
            'device_management': {'status': 'unknown', 'details': {}},
            'master_communication': {'status': 'unknown', 'details': {}},
            'overall_assessment': {'status': 'unknown', 'score': 0}
        }
        self.server_process = None
        self.base_url = "http://localhost:8889"
        
    def run_full_test_suite(self) -> Dict[str, Any]:
        """运行完整的测试套件"""
        logger.info("🧪 开始端到端功能测试...")
        logger.info("🎯 测试目标: 验证完整功能流程")
        
        try:
            # 1. 服务器启动测试
            self.test_server_startup()
            
            # 等待服务器完全启动
            time.sleep(3)
            
            # 2. API端点测试
            self.test_api_endpoints()
            
            # 3. VirtualHere集成测试
            self.test_virtualhere_integration()
            
            # 4. 设备管理测试
            self.test_device_management()
            
            # 5. 主从通信测试
            self.test_master_communication()
            
            # 6. 生成总体评估
            self.generate_overall_assessment()
            
        finally:
            # 清理：停止服务器
            self.cleanup_server()
        
        return self.test_results
    
    def test_server_startup(self) -> None:
        """测试服务器启动"""
        logger.info("🚀 测试服务器启动...")
        
        try:
            # 检查配置文件
            config_file = self.project_root / 'config/slave_server.ini'
            if not config_file.exists():
                # 从模板创建配置文件
                template_file = self.project_root / 'config/slave_server.ini.template'
                if template_file.exists():
                    import shutil
                    shutil.copy(template_file, config_file)
            
            # 启动服务器
            main_file = self.project_root / 'main.py'
            if main_file.exists():
                # 使用subprocess启动服务器
                self.server_process = subprocess.Popen(
                    [sys.executable, str(main_file)],
                    cwd=str(self.project_root),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                
                # 等待服务器启动
                time.sleep(5)
                
                # 检查进程是否还在运行
                if self.server_process.poll() is None:
                    self.test_results['server_startup'] = {
                        'status': 'passed',
                        'details': {
                            'process_id': self.server_process.pid,
                            'startup_time': 5,
                            'config_file_exists': config_file.exists()
                        }
                    }
                    logger.info("   ✅ 服务器启动成功")
                else:
                    # 获取错误信息
                    stdout, stderr = self.server_process.communicate()
                    self.test_results['server_startup'] = {
                        'status': 'failed',
                        'details': {
                            'error': 'Server process terminated',
                            'stdout': stdout.decode('utf-8', errors='ignore')[:500],
                            'stderr': stderr.decode('utf-8', errors='ignore')[:500]
                        }
                    }
                    logger.info("   ❌ 服务器启动失败")
            else:
                self.test_results['server_startup'] = {
                    'status': 'failed',
                    'details': {'error': 'main.py not found'}
                }
                logger.info("   ❌ 主程序文件不存在")
        
        except Exception as e:
            self.test_results['server_startup'] = {
                'status': 'failed',
                'details': {'error': str(e)}
            }
            logger.info(f"   ❌ 服务器启动测试失败: {e}")
    
    def test_api_endpoints(self) -> None:
        """测试API端点"""
        logger.info("🌐 测试API端点...")
        
        # 定义要测试的端点
        endpoints = [
            {'path': '/', 'method': 'GET', 'name': '根路径'},
            {'path': '/health', 'method': 'GET', 'name': '健康检查'},
            {'path': '/api/system/status', 'method': 'GET', 'name': '系统状态'},
            {'path': '/api/system/info', 'method': 'GET', 'name': '系统信息'},
            {'path': '/api/devices', 'method': 'GET', 'name': '设备列表'},
            {'path': '/api/config', 'method': 'GET', 'name': '配置信息'},
        ]
        
        test_results = {}
        passed_count = 0
        
        for endpoint in endpoints:
            try:
                url = f"{self.base_url}{endpoint['path']}"
                
                if endpoint['method'] == 'GET':
                    response = requests.get(url, timeout=10)
                else:
                    response = requests.post(url, timeout=10)
                
                if response.status_code in [200, 201]:
                    test_results[endpoint['name']] = {
                        'status': 'passed',
                        'status_code': response.status_code,
                        'response_size': len(response.content)
                    }
                    passed_count += 1
                    logger.info(f"   ✅ {endpoint['name']}: {response.status_code}")
                else:
                    test_results[endpoint['name']] = {
                        'status': 'failed',
                        'status_code': response.status_code,
                        'error': f"HTTP {response.status_code}"
                    }
                    logger.info(f"   ❌ {endpoint['name']}: {response.status_code}")
            
            except requests.exceptions.ConnectionError:
                test_results[endpoint['name']] = {
                    'status': 'failed',
                    'error': 'Connection refused'
                }
                logger.info(f"   ❌ {endpoint['name']}: 连接被拒绝")
            
            except Exception as e:
                test_results[endpoint['name']] = {
                    'status': 'failed',
                    'error': str(e)
                }
                logger.info(f"   ❌ {endpoint['name']}: {e}")
        
        self.test_results['api_endpoints'] = {
            'status': 'passed' if passed_count >= len(endpoints) * 0.8 else 'failed',
            'details': {
                'total_endpoints': len(endpoints),
                'passed_endpoints': passed_count,
                'test_results': test_results
            }
        }
    
    def test_virtualhere_integration(self) -> None:
        """测试VirtualHere集成"""
        logger.info("🔌 测试VirtualHere集成...")
        
        try:
            # 检查VirtualHere管理器
            vh_manager_file = self.project_root / 'utils/vh_server_manager.py'
            vh_client_file = self.project_root / 'utils/vh_client.py'
            
            integration_status = {
                'vh_manager_exists': vh_manager_file.exists(),
                'vh_client_exists': vh_client_file.exists(),
                'arm_binary_exists': (self.project_root / 'virtualhere/vhusbdarmpi3').exists(),
                'config_section_exists': False
            }
            
            # 检查配置文件中的VirtualHere配置
            config_file = self.project_root / 'config/slave_server.ini'
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_content = f.read()
                    integration_status['config_section_exists'] = '[virtualhere]' in config_content
            
            # 计算集成得分
            score = sum(integration_status.values()) / len(integration_status)
            
            self.test_results['virtualhere_integration'] = {
                'status': 'passed' if score >= 0.75 else 'failed',
                'details': {
                    'integration_score': score,
                    'components': integration_status
                }
            }
            
            if score >= 0.75:
                logger.info(f"   ✅ VirtualHere集成: {score:.1%}")
            else:
                logger.info(f"   ❌ VirtualHere集成: {score:.1%}")
        
        except Exception as e:
            self.test_results['virtualhere_integration'] = {
                'status': 'failed',
                'details': {'error': str(e)}
            }
            logger.info(f"   ❌ VirtualHere集成测试失败: {e}")
    
    def test_device_management(self) -> None:
        """测试设备管理"""
        logger.info("📱 测试设备管理...")
        
        try:
            # 检查设备管理组件
            device_monitor_file = self.project_root / 'tasks/device_monitor.py'
            device_dao_file = self.project_root / 'db/device_dao.py'
            device_service_file = self.project_root / 'restful/device_service.py'
            
            components = {
                'device_monitor': device_monitor_file.exists(),
                'device_dao': device_dao_file.exists(),
                'device_service': device_service_file.exists(),
                'database_models': (self.project_root / 'db/models.py').exists()
            }
            
            # 尝试调用设备API
            api_test = False
            try:
                response = requests.get(f"{self.base_url}/api/devices", timeout=5)
                api_test = response.status_code in [200, 404]  # 404也算正常，表示API存在但无设备
            except:
                pass
            
            components['api_functional'] = api_test
            
            score = sum(components.values()) / len(components)
            
            self.test_results['device_management'] = {
                'status': 'passed' if score >= 0.8 else 'failed',
                'details': {
                    'management_score': score,
                    'components': components
                }
            }
            
            if score >= 0.8:
                logger.info(f"   ✅ 设备管理: {score:.1%}")
            else:
                logger.info(f"   ❌ 设备管理: {score:.1%}")
        
        except Exception as e:
            self.test_results['device_management'] = {
                'status': 'failed',
                'details': {'error': str(e)}
            }
            logger.info(f"   ❌ 设备管理测试失败: {e}")
    
    def test_master_communication(self) -> None:
        """测试主从通信"""
        logger.info("📡 测试主从通信...")
        
        try:
            # 检查通信组件
            master_comm_file = self.project_root / 'utils/master_communication.py'
            heartbeat_file = self.project_root / 'tasks/heartbeat.py'
            command_service_file = self.project_root / 'restful/command_service.py'
            
            components = {
                'master_communication': master_comm_file.exists(),
                'heartbeat_task': heartbeat_file.exists(),
                'command_service': command_service_file.exists()
            }
            
            # 检查配置中的主服务器设置
            config_file = self.project_root / 'config/slave_server.ini'
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_content = f.read()
                    components['master_config'] = '[master]' in config_content
            else:
                components['master_config'] = False
            
            score = sum(components.values()) / len(components)
            
            self.test_results['master_communication'] = {
                'status': 'passed' if score >= 0.75 else 'failed',
                'details': {
                    'communication_score': score,
                    'components': components
                }
            }
            
            if score >= 0.75:
                logger.info(f"   ✅ 主从通信: {score:.1%}")
            else:
                logger.info(f"   ❌ 主从通信: {score:.1%}")
        
        except Exception as e:
            self.test_results['master_communication'] = {
                'status': 'failed',
                'details': {'error': str(e)}
            }
            logger.info(f"   ❌ 主从通信测试失败: {e}")
    
    def generate_overall_assessment(self) -> None:
        """生成总体评估"""
        logger.info("📊 生成总体评估...")
        
        # 计算总体得分
        test_categories = [
            'server_startup',
            'api_endpoints', 
            'virtualhere_integration',
            'device_management',
            'master_communication'
        ]
        
        passed_tests = 0
        total_tests = len(test_categories)
        
        for category in test_categories:
            if self.test_results[category]['status'] == 'passed':
                passed_tests += 1
        
        overall_score = (passed_tests / total_tests) * 100
        
        if overall_score >= 90:
            status = 'excellent'
        elif overall_score >= 80:
            status = 'good'
        elif overall_score >= 70:
            status = 'acceptable'
        else:
            status = 'needs_improvement'
        
        self.test_results['overall_assessment'] = {
            'status': status,
            'score': overall_score,
            'passed_tests': passed_tests,
            'total_tests': total_tests
        }
    
    def cleanup_server(self) -> None:
        """清理服务器进程"""
        if self.server_process and self.server_process.poll() is None:
            logger.info("🧹 清理服务器进程...")
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
    
    def print_test_summary(self) -> None:
        """打印测试总结"""
        logger.info("\n" + "="*80)
        logger.info("📋 端到端功能测试报告")
        logger.info("="*80)
        
        # 各测试项结果
        test_items = [
            ('服务器启动', 'server_startup'),
            ('API端点', 'api_endpoints'),
            ('VirtualHere集成', 'virtualhere_integration'),
            ('设备管理', 'device_management'),
            ('主从通信', 'master_communication')
        ]
        
        for name, key in test_items:
            result = self.test_results[key]
            status_icon = "✅" if result['status'] == 'passed' else "❌"
            logger.info(f"   {status_icon} {name}: {result['status']}")
        
        # 总体评估
        overall = self.test_results['overall_assessment']
        logger.info(f"\n🎯 总体评分: {overall['score']:.1f}/100")
        logger.info(f"📊 通过测试: {overall['passed_tests']}/{overall['total_tests']}")
        
        status_messages = {
            'excellent': '✅ 功能状态: 优秀',
            'good': '✅ 功能状态: 良好',
            'acceptable': '⚠️  功能状态: 可接受',
            'needs_improvement': '❌ 功能状态: 需要改进'
        }
        
        logger.info(status_messages.get(overall['status'], '❓ 功能状态: 未知'))

def main() -> None:
    """主函数"""
    tester = EndToEndFunctionalTester()
    results = tester.run_full_test_suite()
    tester.print_test_summary()
    
    # 保存测试报告
    report_file = Path(__file__).parent.parent / 'end_to_end_test_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"\n📄 详细测试报告已保存到: {report_file}")
    
    return 0

if __name__ == '__main__':
    exit(main())
