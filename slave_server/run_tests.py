#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从服务器测试运行器
执行完整的功能测试和集成测试
"""

import sys
from typing import Dict, List, Any, Optional
import time
import requests
import json
from utils.logger import setup_logger

logger = setup_logger('test_runner')


class SlaveServerTester:
    """从服务器测试器"""
    
    def __init__(self, base_url="http://localhost:8889") -> Any:
        """
        初始化测试器
        
        Args:
            base_url: 服务器基础URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def run_test(self, test_name, test_func) -> Any:
        """
        运行单个测试
        
        Args:
            test_name: 测试名称
            test_func: 测试函数
        """
        try:
            logger.info(f"运行测试: {test_name}")
            start_time = time.time()
            
            result = test_func()
            
            duration = time.time() - start_time
            
            if result:
                logger.info(f"✅ {test_name} - 通过 ({duration:.2f}s)")
                self.test_results.append({
                    'name': test_name,
                    'status': 'PASS',
                    'duration': duration,
                    'message': 'Test passed'
                })
            else:
                logger.error(f"❌ {test_name} - 失败 ({duration:.2f}s)")
                self.test_results.append({
                    'name': test_name,
                    'status': 'FAIL',
                    'duration': duration,
                    'message': 'Test failed'
                })
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            self.test_results.append({
                'name': test_name,
                'status': 'ERROR',
                'duration': 0,
                'message': str(e)
            })
    
    def test_health_endpoint(self) -> None:
        """测试健康检查端点"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('status') == 'healthy'
            
            return False
            
        except Exception as e:
            logger.error(f"健康检查测试失败: {e}")
            return False
    
    def test_server_status_endpoint(self) -> None:
        """测试服务器状态端点"""
        try:
            response = self.session.get(f"{self.base_url}/api/server/status", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return (data.get('status') is True and 
                       'data' in data and 
                       'health' in data['data'])
            
            return False
            
        except Exception as e:
            logger.error(f"服务器状态测试失败: {e}")
            return False
    
    def test_server_health_endpoint(self) -> None:
        """测试服务器健康检查端点"""
        try:
            response = self.session.get(f"{self.base_url}/api/server/health", timeout=10)
            
            if response.status_code in [200, 503]:  # 200正常，503不健康但响应正常
                data = response.json()
                return 'data' in data and 'overall_status' in data['data']
            
            return False
            
        except Exception as e:
            logger.error(f"服务器健康检查测试失败: {e}")
            return False
    
    def test_devices_endpoint(self) -> None:
        """测试设备管理端点"""
        try:
            response = self.session.get(f"{self.base_url}/api/devices/", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                return (data.get('status') is True and 
                       'data' in data and 
                       'devices' in data['data'])
            
            return False
            
        except Exception as e:
            logger.error(f"设备端点测试失败: {e}")
            return False
    
    def test_server_info_endpoint(self) -> None:
        """测试服务器信息端点"""
        try:
            response = self.session.get(f"{self.base_url}/api/server/info", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                return (data.get('status') is True and 
                       'data' in data and 
                       'hostname' in data['data'])
            
            return False
            
        except Exception as e:
            logger.error(f"服务器信息测试失败: {e}")
            return False
    
    def test_error_stats_endpoint(self) -> None:
        """测试错误统计端点"""
        try:
            response = self.session.get(f"{self.base_url}/api/server/errors", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                return (data.get('status') is True and 
                       'data' in data and 
                       'error_count' in data['data'])
            
            return False
            
        except Exception as e:
            logger.error(f"错误统计测试失败: {e}")
            return False
    
    def test_cors_headers(self) -> None:
        """测试CORS头部"""
        try:
            response = self.session.options(f"{self.base_url}/api/devices/", timeout=5)
            
            # 检查CORS头部
            cors_headers = response.headers.get('Access-Control-Allow-Origin')
            return cors_headers == '*'
            
        except Exception as e:
            logger.error(f"CORS测试失败: {e}")
            return False
    
    def test_invalid_endpoint(self) -> None:
        """测试无效端点"""
        try:
            response = self.session.get(f"{self.base_url}/api/invalid", timeout=5)
            
            # 应该返回404
            return response.status_code == 404
            
        except Exception as e:
            logger.error(f"无效端点测试失败: {e}")
            return False
    
    def run_all_tests(self) -> None:
        """运行所有测试"""
        logger.info("开始运行从服务器测试套件")
        logger.info("=" * 50)
        
        # 基础功能测试
        self.run_test("健康检查端点", self.test_health_endpoint)
        self.run_test("服务器状态端点", self.test_server_status_endpoint)
        self.run_test("服务器健康检查端点", self.test_server_health_endpoint)
        self.run_test("设备管理端点", self.test_devices_endpoint)
        self.run_test("服务器信息端点", self.test_server_info_endpoint)
        self.run_test("错误统计端点", self.test_error_stats_endpoint)
        
        # 网络功能测试
        self.run_test("CORS头部测试", self.test_cors_headers)
        self.run_test("无效端点测试", self.test_invalid_endpoint)
        
        # 输出测试结果
        self.print_test_summary()
    
    def print_test_summary(self) -> None:
        """打印测试摘要"""
        logger.info("=" * 50)
        logger.info("测试结果摘要")
        logger.info("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        error_tests = len([r for r in self.test_results if r['status'] == 'ERROR'])
        
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过: {passed_tests}")
        logger.info(f"失败: {failed_tests}")
        logger.info(f"错误: {error_tests}")
        logger.info(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0 or error_tests > 0:
            logger.info("\n失败的测试:")
            for result in self.test_results:
                if result['status'] in ['FAIL', 'ERROR']:
                    logger.info(f"  - {result['name']}: {result['message']}")
        
        logger.info("=" * 50)
        
        return passed_tests == total_tests


def main() -> None:
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8889"
    
    logger.info(f"测试目标服务器: {base_url}")
    
    # 等待服务器启动
    logger.info("等待服务器启动...")
    time.sleep(3)
    
    # 运行测试
    tester = SlaveServerTester(base_url)
    success = tester.run_all_tests()
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
