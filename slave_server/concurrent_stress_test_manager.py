#!/usr/bin/env python3
"""
OmniLink并发压力测试管理器
同时启动多个从服务器实例进行并发压力测试
"""

import sys
import time
import threading
import signal
from production_stress_test_client import ProductionStressTestClient

class ConcurrentStressTestManager:
    def __init__(self, num_instances=15, base_port=8891, base_vh_port=7576):
        self.num_instances = num_instances
        self.base_port = base_port
        self.base_vh_port = base_vh_port
        self.clients = []
        self.running = False
        
    def create_clients(self):
        """创建所有测试客户端实例"""
        print(f"创建 {self.num_instances} 个并发压力测试实例...")
        
        for i in range(self.num_instances):
            instance_id = i + 1
            server_port = self.base_port + i
            vh_port = self.base_vh_port + i
            
            client = ProductionStressTestClient(
                instance_id=instance_id,
                server_port=server_port,
                vh_port=vh_port
            )
            self.clients.append(client)
            
        print(f"已创建 {len(self.clients)} 个测试实例")
    
    def start_concurrent_test(self):
        """启动并发压力测试"""
        print("=" * 80)
        print("OmniLink生产环境预部署验证：并发压力测试开始")
        print("=" * 80)
        
        self.running = True
        successful_starts = 0
        failed_starts = 0
        
        # 分批启动，模拟真实环境的渐进式上线
        batch_size = 5
        for batch_start in range(0, len(self.clients), batch_size):
            batch_end = min(batch_start + batch_size, len(self.clients))
            batch_clients = self.clients[batch_start:batch_end]
            
            print(f"\n启动第 {batch_start//batch_size + 1} 批次 ({len(batch_clients)} 个实例)...")
            
            # 并发启动当前批次
            start_threads = []
            for client in batch_clients:
                thread = threading.Thread(target=self._start_client, args=(client,))
                start_threads.append(thread)
                thread.start()
            
            # 等待当前批次启动完成
            for thread in start_threads:
                thread.join()
            
            # 统计启动结果
            for client in batch_clients:
                if client.registration_success:
                    successful_starts += 1
                else:
                    failed_starts += 1
            
            print(f"第 {batch_start//batch_size + 1} 批次启动完成 - 成功: {successful_starts}, 失败: {failed_starts}")
            
            # 批次间间隔，模拟真实部署场景
            if batch_end < len(self.clients):
                time.sleep(3)
        
        print(f"\n并发启动完成 - 总成功: {successful_starts}, 总失败: {failed_starts}")
        print(f"成功率: {successful_starts/len(self.clients)*100:.1f}%")
        
        return successful_starts, failed_starts
    
    def _start_client(self, client):
        """启动单个客户端"""
        try:
            client.start()
        except Exception as e:
            print(f"[实例{client.instance_id}] 启动异常: {e}")
    
    def monitor_test(self, duration_minutes=10):
        """监控测试运行状态"""
        print(f"\n开始监控测试运行状态 ({duration_minutes} 分钟)...")
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        while time.time() < end_time and self.running:
            time.sleep(30)  # 每30秒检查一次
            
            # 统计当前状态
            active_clients = sum(1 for client in self.clients if client.running)
            total_heartbeats = sum(client.heartbeat_count for client in self.clients)
            total_errors = sum(client.error_count for client in self.clients)
            
            elapsed = time.time() - start_time
            remaining = end_time - time.time()
            
            print(f"[监控] 运行时间: {elapsed/60:.1f}分钟, 剩余: {remaining/60:.1f}分钟")
            print(f"[监控] 活跃实例: {active_clients}/{len(self.clients)}, 总心跳: {total_heartbeats}, 总错误: {total_errors}")
            
            if total_heartbeats > 0:
                error_rate = total_errors / total_heartbeats * 100
                print(f"[监控] 错误率: {error_rate:.2f}%")
    
    def stop_all_clients(self):
        """停止所有客户端"""
        print("\n正在停止所有测试实例...")
        self.running = False
        
        # 并发停止所有客户端
        stop_threads = []
        for client in self.clients:
            if client.running:
                thread = threading.Thread(target=client.stop)
                stop_threads.append(thread)
                thread.start()
        
        # 等待所有客户端停止
        for thread in stop_threads:
            thread.join(timeout=10)
        
        print("所有测试实例已停止")
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("OmniLink并发压力测试报告")
        print("=" * 80)
        
        successful_registrations = sum(1 for client in self.clients if client.registration_success)
        total_heartbeats = sum(client.heartbeat_count for client in self.clients)
        total_errors = sum(client.error_count for client in self.clients)
        
        print(f"测试实例总数: {len(self.clients)}")
        print(f"成功注册数量: {successful_registrations}")
        print(f"注册成功率: {successful_registrations/len(self.clients)*100:.1f}%")
        print(f"总心跳次数: {total_heartbeats}")
        print(f"总错误次数: {total_errors}")
        
        if total_heartbeats > 0:
            error_rate = total_errors / total_heartbeats * 100
            print(f"整体错误率: {error_rate:.2f}%")
        
        print("\n详细统计:")
        print("-" * 80)
        print(f"{'实例ID':<8} {'服务器名称':<35} {'IP地址':<15} {'端口':<6} {'心跳':<6} {'错误':<6} {'错误率':<8}")
        print("-" * 80)
        
        for client in self.clients:
            stats = client.get_stats()
            error_rate = stats['error_rate'] * 100
            print(f"{stats['instance_id']:<8} {stats['server_name'][:34]:<35} {stats['simulated_ip']:<15} {stats['server_port']:<6} {stats['heartbeat_count']:<6} {stats['error_count']:<6} {error_rate:<7.1f}%")

def main():
    num_instances = 15
    if len(sys.argv) > 1:
        num_instances = int(sys.argv[1])
    
    manager = ConcurrentStressTestManager(num_instances=num_instances)
    
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在停止测试...")
        manager.stop_all_clients()
        manager.generate_test_report()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 创建测试实例
        manager.create_clients()
        
        # 启动并发测试
        successful, failed = manager.start_concurrent_test()
        
        if successful > 0:
            # 监控测试运行
            manager.monitor_test(duration_minutes=5)  # 运行5分钟
        
        # 停止测试
        manager.stop_all_clients()
        
        # 生成报告
        manager.generate_test_report()
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        manager.stop_all_clients()
        manager.generate_test_report()

if __name__ == "__main__":
    main()
