#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniLink轻量化从服务器主程序
专注于USB设备代理和与主服务器通信
基于Flask框架构建，符合轻量化规范要求
"""

import os
import sys
import logging
import threading
import time
import configparser
import socket
import shutil
from datetime import datetime
from typing import Optional, Dict, Any

from flask import Flask, jsonify, request
from flask_cors import CORS
import requests
import psutil
import subprocess

# 导入本地数据库
from database.local_db import get_local_db

# 全局配置
config = {}
vh_process = None
device_list = []
last_device_count = 0
last_device_hash = ""
last_heartbeat = None

def load_config(config_filename='slave_server.ini'):
    """加载配置文件"""
    global config
    config_file = os.path.join(os.path.dirname(__file__), 'config', config_filename)

    if os.path.exists(config_file):
        parser = configparser.ConfigParser()
        parser.read(config_file, encoding='utf-8')

        config = {
            'server_host': parser.get('server', 'host', fallback='0.0.0.0'),
            'server_port': parser.getint('server', 'port', fallback=8889),
            'vh_port': parser.getint('virtualhere', 'server_port', fallback=7575),
            'vh_binary': parser.get('virtualhere', 'binary_path', fallback='/app/vhusbd'),
            'log_level': parser.get('logging', 'log_level', fallback='INFO'),
            'log_file': parser.get('logging', 'log_file', fallback='logs/slave_server.log'),
            'master_url': os.environ.get('MASTER_SERVER_URL', 'http://localhost:8000')
        }
    else:
        # 默认配置
        config = {
            'server_host': '0.0.0.0',
            'server_port': 8889,
            'vh_port': 7575,
            'vh_binary': '/app/vhusbd',
            'log_level': 'INFO',
            'log_file': 'logs/slave_server.log',
            'master_url': os.environ.get('MASTER_SERVER_URL', 'http://*************:8000')
        }

def setup_logging():
    """设置日志"""
    log_dir = os.path.dirname(config['log_file'])
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=getattr(logging, config['log_level']),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config['log_file']),
            logging.StreamHandler()
        ]
    )

def get_usb_topology():
    """获取USB硬件拓扑信息"""
    topology = {
        'hub_count': 0,
        'total_ports': 0,
        'occupied_ports': 0,
        'free_ports': 0,
        'hub_details': []
    }

    # 检测是否在WSL环境中
    is_wsl = False
    try:
        with open('/proc/version', 'r') as f:
            if 'microsoft' in f.read().lower():
                is_wsl = True
    except:
        pass

    if is_wsl:
        try:
            import subprocess
            import re

            # 获取USB Hub信息
            hub_cmd = [
                'powershell.exe', '-Command',
                "Get-PnpDevice -Class USB | Where-Object { $_.FriendlyName -like '*Hub*' -or $_.FriendlyName -like '*hub*' } | Select-Object FriendlyName, InstanceId, Status"
            ]

            hub_result = subprocess.run(hub_cmd, capture_output=True, text=True, timeout=15, encoding='utf-8', errors='ignore')

            if hub_result.returncode == 0 and hub_result.stdout.strip():
                lines = hub_result.stdout.strip().split('\n')
                data_lines = [line for line in lines if line.strip() and not line.startswith('FriendlyName') and not line.startswith('------------')]

                for line in data_lines:
                    parts = line.strip().split()
                    if len(parts) >= 3:
                        status = parts[-1]
                        instance_id = parts[-2]
                        friendly_name = ' '.join(parts[:-2])

                        if status == 'OK':
                            topology['hub_count'] += 1
                            # 估算Hub端口数（通常为4-8个端口）
                            estimated_ports = 4  # 默认4个端口
                            if 'USB 3' in friendly_name or '3.0' in friendly_name:
                                estimated_ports = 4
                            elif 'USB 2' in friendly_name or '2.0' in friendly_name:
                                estimated_ports = 4

                            topology['hub_details'].append({
                                'name': friendly_name,
                                'instance_id': instance_id,
                                'estimated_ports': estimated_ports,
                                'status': status
                            })
                            topology['total_ports'] += estimated_ports

            # 添加主板USB端口（估算）
            topology['total_ports'] += 8  # 假设主板有8个USB端口

        except Exception as e:
            logging.error(f"获取USB拓扑信息失败: {e}")

    return topology

def extract_port_location(instance_id):
    """从InstanceId中提取端口位置信息"""
    import re

    # 尝试从InstanceId中提取总线和端口信息
    # 格式通常为: USB\VID_xxxx&PID_xxxx\xxxxxxx 或包含端口信息
    bus = 1  # 默认总线
    port = 1  # 默认端口

    try:
        # 查找数字模式来推断端口位置
        numbers = re.findall(r'\d+', instance_id)
        if len(numbers) >= 2:
            bus = int(numbers[0]) if int(numbers[0]) > 0 else 1
            port = int(numbers[1]) if int(numbers[1]) > 0 else 1
        elif len(numbers) == 1:
            port = int(numbers[0]) if int(numbers[0]) > 0 else 1
    except:
        pass

    return {'bus': bus, 'port': port}

def detect_device_type(friendly_name, vendor_id, product_id):
    """检测设备类型（支持加密锁设备的智能识别）"""
    name_lower = (friendly_name or "").lower()

    logging.info(f"检测设备类型: VID=0x{vendor_id:04X}, PID=0x{product_id:04X}, 名称='{friendly_name}'")

    # 基于VID/PID识别常见加密锁厂商
    encryption_lock_vendors = {
        0x096E: 'senseshield',  # SenseShield
        0x1BC0: 'senseshield',  # SenseShield深思数盾 (正确的VID)
        0x0557: 'aten',         # ATEN (也用于加密锁)
        0x0471: 'rockey',       # Rockey
        0x0529: 'hasp',         # HASP/Sentinel
        0x1A86: 'ch341',        # CH341 (常用于加密锁)
        0x10C4: 'cp210x',       # CP210x (常用于加密锁)
    }

    # 基于名称识别加密锁
    encryption_keywords = ['senseshield', 'elite5', 'rockey', 'hasp', 'sentinel', 'dongle', 'key', 'lock']

    # 检查是否为加密锁设备
    if (vendor_id in encryption_lock_vendors or
        any(keyword in name_lower for keyword in encryption_keywords)):
        vendor_name = encryption_lock_vendors.get(vendor_id, 'unknown')
        logging.info(f"检测到加密锁设备: VID=0x{vendor_id:04X}, 厂商={vendor_name}, 名称='{friendly_name}'")
        return 'encryption_lock'

    # USB Hub
    if 'hub' in name_lower:
        return 'usb_hub'

    # 安全设备
    if any(keyword in name_lower for keyword in ['security', 'token', 'smart card']):
        return 'security_device'

    # 复合设备
    if 'composite' in name_lower:
        return 'composite_device'

    # 存储设备
    if any(keyword in name_lower for keyword in ['storage', 'disk', 'drive', 'flash', 'mass storage']):
        return 'storage_device'

    # 输入设备
    if any(keyword in name_lower for keyword in ['mouse', 'keyboard', 'hid']):
        return 'input_device'

    # 网络设备
    if any(keyword in name_lower for keyword in ['ethernet', 'wifi', 'network']):
        return 'network_device'

    # 如果名称为空或Unknown，但VID在已知范围内，可能是加密锁
    if (not name_lower or 'unknown' in name_lower) and vendor_id in encryption_lock_vendors:
        return 'encryption_lock'

    return 'unknown'

def generate_device_description(friendly_name, vendor_id, product_id, device_type):
    """生成设备描述（针对加密锁设备优化）"""
    name = (friendly_name or "").strip()

    if name and name != "Unknown Device" and "unknown" not in name.lower():
        return name

    # 为加密锁设备生成友好的描述
    if device_type == 'encryption_lock':
        vendor_names = {
            0x096E: 'SenseShield',
            0x0471: 'Rockey',
            0x0529: 'HASP/Sentinel',
            0x1A86: 'CH341',
            0x10C4: 'CP210x'
        }
        vendor_name = vendor_names.get(vendor_id, f"厂商{vendor_id:04X}")
        return f"{vendor_name}加密锁 ({vendor_id:04X}:{product_id:04X})"

    elif device_type == 'security_device':
        return f"安全设备 ({vendor_id:04X}:{product_id:04X})"

    elif device_type == 'usb_hub':
        return f"USB Hub ({vendor_id:04X}:{product_id:04X})"

    else:
        return f"USB设备 ({vendor_id:04X}:{product_id:04X})"

def generate_device_name(friendly_name, vendor_id, product_id, device_type):
    """生成设备名称（用于显示）"""
    description = generate_device_description(friendly_name, vendor_id, product_id, device_type)

    # 如果是加密锁，添加特殊标识
    if device_type == 'encryption_lock':
        return f"🔐 {description}"
    elif device_type == 'security_device':
        return f"🛡️ {description}"
    elif device_type == 'usb_hub':
        return f"🔌 {description}"
    else:
        return description

def get_usb_devices():
    """获取USB设备列表（支持WSL环境）"""
    global device_list, last_device_count
    devices = []

    # 检测是否在WSL环境中
    is_wsl = False
    try:
        with open('/proc/version', 'r') as f:
            if 'microsoft' in f.read().lower():
                is_wsl = True
    except:
        pass

    if is_wsl:
        # WSL环境：通过powershell.exe获取Windows USB设备
        try:
            import subprocess
            import re
            import json

            # 使用PowerShell获取USB设备信息
            cmd = [
                'powershell.exe', '-Command',
                "Get-PnpDevice -Class USB | Where-Object { $_.InstanceId -like '*VID_*' -and $_.InstanceId -like '*PID_*' } | Select-Object FriendlyName, InstanceId, Status"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15, encoding='utf-8', errors='ignore')

            if result.returncode == 0 and result.stdout.strip():
                # 解析PowerShell输出
                lines = result.stdout.strip().split('\n')
                # 跳过标题行
                data_lines = [line for line in lines if line.strip() and not line.startswith('FriendlyName') and not line.startswith('------------')]

                for i, line in enumerate(data_lines):
                    # 解析每行数据
                    parts = line.strip().split()
                    if len(parts) >= 3:
                        # 提取设备名称（可能包含空格）
                        status = parts[-1]
                        instance_id = parts[-2]
                        friendly_name = ' '.join(parts[:-2])

                        # 从InstanceId中提取VID和PID
                        vid_match = re.search(r'VID_([0-9A-F]{4})', instance_id, re.IGNORECASE)
                        pid_match = re.search(r'PID_([0-9A-F]{4})', instance_id, re.IGNORECASE)

                        # 提取端口位置信息
                        port_location = extract_port_location(instance_id)

                        if vid_match and pid_match:
                            vendor_id = int(vid_match.group(1), 16)
                            product_id = int(pid_match.group(1), 16)

                            # 检测设备类型
                            device_type = detect_device_type(friendly_name, vendor_id, product_id)

                            # 生成唯一设备ID（包含索引以避免重复）
                            unique_device_id = f"USB_{vendor_id:04X}_{product_id:04X}_{port_location['bus']}_{port_location['port']}_{i:03d}"

                            # 生成硬件签名（用于识别相同硬件）
                            hardware_signature = f"USB_{vendor_id:04X}_{product_id:04X}_{instance_id[-8:]}"

                            device_info = {
                                'device_id': unique_device_id,  # 使用唯一ID避免重复
                                'vendor_id': vendor_id,
                                'product_id': product_id,
                                'bus': port_location['bus'],
                                'address': port_location['port'],
                                'port_location': f"Bus{port_location['bus']}-Port{port_location['port']}",
                                'description': generate_device_description(friendly_name, vendor_id, product_id, device_type),
                                'device_name': generate_device_name(friendly_name, vendor_id, product_id, device_type),
                                'device_type': device_type,
                                'status': status,
                                'device_path': instance_id,
                                'hardware_signature': hardware_signature,
                                'auto_generated_name': f"{friendly_name} ({vendor_id:04X}:{product_id:04X})",
                                'custom_name': '',
                                'is_real_hardware': True,
                                'auto_bind_eligible': device_type in ['encryption_lock', 'security_device'],
                                'physical_port': f"Bus{port_location['bus']}Port{port_location['port']}",
                                'port_location_code': instance_id,
                                'last_seen': datetime.now().isoformat(),
                                'connected_at': datetime.now().isoformat()
                            }

                            # 检查设备状态（加密锁设备名称为空是正常的）
                            if status != 'OK':
                                device_info['status'] = 'error'
                                device_info['description'] = f"设备异常-需要核实是否损坏 ({vendor_id:04X}:{product_id:04X})"
                                # 如果是已知的加密锁VID，保持正确的设备类型
                                if vendor_id not in [0x1BC0, 0x0557, 0x096E, 0x0471, 0x0529]:
                                    device_info['device_type'] = 'unknown_error'
                                device_info['auto_bind_eligible'] = device_type in ['encryption_lock', 'security_device']
                            else:
                                # 加密锁设备即使名称为空也可能是正常的
                                device_info['auto_bind_eligible'] = device_type in ['encryption_lock', 'security_device']

                            devices.append(device_info)

                logging.info(f"WSL环境：通过PowerShell检测到 {len(devices)} 个USB设备")
        except Exception as e:
            logging.error(f"WSL环境获取USB设备失败: {e}")
    else:
        # 非WSL环境：使用pyusb
        try:
            import usb.core
            for device in usb.core.find(find_all=True):
                devices.append({
                    'vendor_id': device.idVendor,
                    'product_id': device.idProduct,
                    'bus': device.bus,
                    'address': device.address,
                    'description': f"USB Device {device.idVendor:04x}:{device.idProduct:04x}"
                })
            logging.info(f"非WSL环境：通过pyusb检测到 {len(devices)} 个USB设备")
        except Exception as e:
            logging.error(f"pyusb获取USB设备失败: {e}")

    # 获取USB拓扑信息
    topology = get_usb_topology()

    # 计算端口占用情况
    topology['occupied_ports'] = len(devices)
    topology['free_ports'] = max(0, topology['total_ports'] - topology['occupied_ports'])

    # 检测设备变化
    global last_device_count, last_device_hash
    current_count = len(devices)

    # 计算设备列表的哈希值
    import hashlib
    device_signatures = sorted([f"{d['vendor_id']:04x}:{d['product_id']:04x}:{d['port_location']}" for d in devices])
    current_hash = hashlib.md5('|'.join(device_signatures).encode()).hexdigest()

    # 检测是否有变化
    device_changed = (current_count != last_device_count) or (current_hash != last_device_hash)

    if device_changed:
        logging.info(f"检测到USB设备变化: 数量 {last_device_count} -> {current_count}")
        logging.info(f"USB拓扑: Hub数量={topology['hub_count']}, 总端口={topology['total_ports']}, 占用={topology['occupied_ports']}, 空闲={topology['free_ports']}")

        # 检测具体变化
        if current_count > last_device_count:
            logging.info("检测到设备接入事件")
        elif current_count < last_device_count:
            logging.info("检测到设备移除事件")

        last_device_count = current_count
        last_device_hash = current_hash

        # 使用线程池管理器处理设备变化事件
        from core.thread_pool_manager import get_thread_manager, ThreadPoolType

        thread_manager = get_thread_manager()
        if not thread_manager.create_pool(ThreadPoolType.DEVICE_MONITOR):
            logger.error("创建设备监控线程池失败")
        else:
            future = thread_manager.submit_task(
                ThreadPoolType.DEVICE_MONITOR,
                send_device_change_event,
                current_count > last_device_count
            )
            if not future:
                logger.error("提交设备变化事件任务失败")

    device_list = devices

    # 保存到本地数据库
    try:
        local_db = get_local_db()
        local_db.update_usb_devices(devices)
        local_db.save_topology_snapshot(topology)
        local_db.cleanup_old_snapshots(keep_count=5)  # 只保留最新5个快照
        logging.debug("USB设备状态已保存到本地数据库")
    except Exception as e:
        logging.error(f"保存USB设备状态到本地数据库失败: {e}")

    # 返回设备列表和拓扑信息
    return {
        'devices': devices,
        'topology': topology,
        'device_count': current_count
    }

def is_production_environment():
    """检查是否在生产环境中"""
    try:
        # 检查是否在WSL中
        with open('/proc/version', 'r') as f:
            version_info = f.read().lower()
            if 'microsoft' in version_info or 'wsl' in version_info:
                return False

        # 检查是否有真实的USB控制器
        usb_controllers = subprocess.run(['lsusb'], capture_output=True, text=True)
        if usb_controllers.returncode == 0 and usb_controllers.stdout.strip():
            return True

        return False
    except Exception:
        return False

def check_virtualhere_permissions():
    """检查VirtualHere权限配置"""
    try:
        import grp
        import pwd

        # 检查virtualhere用户组是否存在
        try:
            grp.getgrnam('virtualhere')
        except KeyError:
            logging.error("virtualhere用户组不存在")
            return False

        # 检查当前用户是否在virtualhere组中
        user_groups = [g.gr_name for g in grp.getgrall() if pwd.getpwuid(os.getuid()).pw_name in g.gr_mem]
        if 'virtualhere' not in user_groups:
            logging.error("当前用户不在virtualhere组中")
            return False

        # 检查udev规则是否存在
        udev_rules_file = "/etc/udev/rules.d/99-virtualhere-usb.rules"
        if not os.path.exists(udev_rules_file):
            logging.error("udev规则文件不存在")
            return False

        return True
    except Exception as e:
        logging.error(f"权限检查失败: {e}")
        return False

def create_virtualhere_config(config):
    """创建VirtualHere配置文件"""
    config_file = "virtualhere_config.ini"

    config_content = f"""# VirtualHere服务器配置文件
# 自动生成

# 服务器名称
ServerName={config.get('server_name', 'OmniLink-USB-Server')}

# TCP端口
TCPPort={config.get('vh_port', 7575)}

# 启用Bonjour广播
UseAVAHI=1

# 压缩限制
CompressionLimit=384

# 自动附加到内核
AutoAttachToKernel=1

# 声明端口（Linux）
ClaimPorts=0

# 日志级别
LogLevel=2
"""

    try:
        with open(config_file, 'w') as f:
            f.write(config_content)
        logging.info(f"VirtualHere配置文件已创建: {config_file}")
        return config_file
    except Exception as e:
        logging.error(f"创建配置文件失败: {e}")
        return None

def verify_virtualhere_port(port):
    """验证VirtualHere端口监听"""
    try:
        import socket

        # 检查端口是否被监听
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', port))
        sock.close()

        return result == 0
    except Exception:
        return False

def start_virtualhere():
    """启动VirtualHere服务"""
    global vh_process
    try:
        if vh_process and vh_process.poll() is None:
            logging.info("VirtualHere已在运行")
            return True

        # 检查是否在生产环境中
        if not is_production_environment():
            logging.warning("检测到非生产环境（可能是WSL），VirtualHere可能无法正常运行")
            logging.info("建议在真实Linux环境中部署以获得完整功能")
            logging.info("运行权限配置脚本: ./scripts/setup_virtualhere_permissions.sh")
            return False

        # 创建VirtualHere配置文件
        config_file = create_virtualhere_config(config)
        if not config_file:
            return False

        # VirtualHere运行在用户空间，使用-b参数作为后台守护进程运行
        cmd = [config['vh_binary'], '-b', '-c', config_file]

        logging.info(f"启动VirtualHere命令: {' '.join(cmd)}")

        try:
            # 检查权限配置
            if not check_virtualhere_permissions():
                logging.error("VirtualHere权限配置不正确，请运行权限配置脚本")
                logging.error("运行: ./scripts/setup_virtualhere_permissions.sh")
                return False

            # 启动VirtualHere作为后台守护进程
            vh_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid if hasattr(os, 'setsid') else None  # 创建新的进程组
            )

            # 等待短暂时间检查是否启动成功
            time.sleep(3)

            if vh_process.poll() is None:
                logging.info(f"VirtualHere已启动，PID: {vh_process.pid}, 端口: {config.get('vh_port', 7575)}")

                # 验证端口监听
                if verify_virtualhere_port(config.get('vh_port', 7575)):
                    logging.info("VirtualHere端口监听验证成功")
                    return True
                else:
                    logging.warning("VirtualHere端口监听验证失败")
                    return False
            else:
                stdout, stderr = vh_process.communicate()
                error_msg = stderr.decode() if stderr else stdout.decode()
                logging.error(f"VirtualHere启动失败: {error_msg}")

                # 提供具体的解决建议
                if "Permission denied" in error_msg:
                    logging.error("权限不足，请运行权限配置脚本:")
                    logging.error("./scripts/setup_virtualhere_permissions.sh")
                elif "Address already in use" in error_msg:
                    logging.error("端口已被占用，请检查是否有其他VirtualHere实例运行")

                return False

        except Exception as e:
            logging.error(f"启动VirtualHere异常: {e}")
            return False
    except Exception as e:
        logging.error(f"启动VirtualHere失败: {e}")
        return False

def stop_virtualhere():
    """停止VirtualHere服务"""
    global vh_process
    try:
        if vh_process and vh_process.poll() is None:
            vh_process.terminate()
            vh_process.wait(timeout=5)
            logging.info("VirtualHere已停止")
        return True
    except Exception as e:
        logging.error(f"停止VirtualHere失败: {e}")
        return False

def generate_unique_server_name():
    """
    生成唯一的服务器名称，避免多实例部署冲突
    统一命名规范：基于硬件UUID + MAC地址 + 时间戳的三重保障机制
    """
    import uuid
    import time
    import hashlib
    import socket

    try:
        # 方案1：尝试获取硬件UUID（最稳定的标识）
        hardware_uuid = None
        try:
            from core.hardware_fingerprint import HardwareFingerprint
            fingerprint = HardwareFingerprint()
            hardware_uuid = fingerprint.generate_uuid()[:8]  # 取前8位作为标识
        except Exception:
            pass

        # 方案2：基于MAC地址生成唯一标识
        mac = uuid.getnode()
        mac_suffix = f"{mac:012x}"[-6:]  # 取MAC地址后6位

        # 方案3：基于主机名增加可识别性
        hostname = socket.gethostname()
        hostname_hash = hashlib.md5(hostname.encode()).hexdigest()[:4]

        # 方案4：基于端口号增加可识别性
        port_suffix = config.get('server_port', 8890)

        # 方案5：基于时间戳确保唯一性
        timestamp = int(time.time() * 1000) % 100000  # 取时间戳后5位

        # 组合生成唯一名称（优先使用硬件UUID）
        if hardware_uuid:
            unique_name = f"OmniLink-Slave-{hardware_uuid}-{hostname_hash}-{port_suffix}"
        else:
            unique_name = f"OmniLink-Slave-{mac_suffix}-{hostname_hash}-{port_suffix}-{timestamp}"

        logging.info(f"生成唯一服务器名称: {unique_name}")
        return unique_name

    except Exception as e:
        # 降级方案：使用时间戳和随机数
        import random
        fallback_id = f"{int(time.time() % 10000)}-{random.randint(1000, 9999)}"
        fallback_name = f"OmniLink-Slave-{fallback_id}"
        logging.warning(f"名称生成失败，使用降级方案: {fallback_name}, 错误: {e}")
        return fallback_name

def register_to_master():
    """向主服务器注册"""
    try:
        # 先发送一个测试请求到主服务器，让主服务器识别我们的真实IP
        test_response = requests.get(f"{config['master_url']}/health", timeout=5)

        # 生成唯一的服务器名称，避免多实例部署冲突
        unique_server_name = generate_unique_server_name()

        # 然后通过主服务器的API获取我们的真实客户端IP
        # 这样可以确保注册时使用的IP与心跳时主服务器看到的IP一致
        data = {
            'server_name': unique_server_name,
            'server_ip': 'auto-detect',  # 让主服务器自动检测IP
            'server_port': config['server_port'],
            'vh_port': config['vh_port'],
            'hardware_uuid': None,  # 添加可选字段，避免处理时出错
            'hardware_info': None,  # 添加可选字段，避免处理时出错
            'description': f'OmniLink Lightweight Slave Server - {unique_server_name}',
            'version': '2.0'
        }

        response = requests.post(
            f"{config['master_url']}/api/v1/slave/register",
            json=data,
            timeout=10,
            headers={'Content-Type': 'application/json; charset=utf-8'}
        )

        if response.status_code == 200:
            logging.info("从服务器注册成功")
            return True
        else:
            logging.warning(f"从服务器注册失败: {response.status_code}")
            return False

    except Exception as e:
        logging.error(f"从服务器注册失败: {e}")
        return False

def send_immediate_data_sync():
    """立即发送数据同步（用于设备变化时）"""
    try:
        logging.info("设备变化触发立即数据同步")
        send_full_data_sync()
    except Exception as e:
        logging.error(f"立即数据同步失败: {e}")

# 数据传输状态管理
data_sync_in_progress = False
data_sync_lock = threading.Lock()

def send_lightweight_heartbeat():
    """发送轻量级心跳到主服务器（包含完整服务器信息）"""
    global last_heartbeat, data_sync_in_progress

    # 如果正在进行数据同步，暂停心跳避免冲突
    if data_sync_in_progress:
        logging.debug("数据同步进行中，跳过心跳发送")
        return True

    try:
        # 获取服务器配置信息
        server_name = config.get('server_name', f"slave-server-{config.get('server_port', 8890)}")
        server_port = config.get('server_port', 8890)
        vh_port = config.get('virtualhere', {}).get('server_port', 7575)
        server_version = "2.0"  # 从服务器版本号

        # 获取IP地址
        try:
            import socket
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip_address = s.getsockname()[0]
            s.close()
        except:
            ip_address = "127.0.0.1"

        # 获取VirtualHere状态
        vh_status = 'stopped'
        if vh_process and vh_process.poll() is None:
            vh_status = 'running'
        elif vh_process and vh_process.poll() is not None:
            vh_status = 'error'

        # 获取USB拓扑信息用于一致性检查
        usb_info = get_usb_devices()
        topology = usb_info.get('topology', {})

        # 轻量级心跳数据（包含完整服务器信息和拓扑数据）
        data = {
            'timestamp': datetime.now().isoformat(),
            'status': 'online',
            'device_count_summary': len(device_list),  # 仅设备数量，不包含详细信息
            'hub_count': topology.get('hub_count', 0),  # Hub数量用于一致性检查
            'total_ports': topology.get('total_ports', 0),  # 总端口数用于一致性检查
            'heartbeat_type': 'lightweight',
            'server_name': server_name,
            'ip_address': ip_address,
            'web_port': server_port,
            'virtualhere_status': vh_status,
            'virtualhere_port': vh_port,
            'server_version': server_version,
            'heartbeat_timestamp': datetime.now().isoformat(),
            'data_sync_status': 'idle' if not data_sync_in_progress else 'syncing'
        }

        response = requests.post(
            f"{config['master_url']}/api/v1/slave/heartbeat",
            json=data,
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            last_heartbeat = datetime.now()

            # 检查是否需要配置验证
            if result.get('require_config_verification', False):
                logging.info("主服务器请求配置验证")
                threading.Thread(target=handle_config_verification, args=(result,), daemon=True).start()

            return True
        else:
            logging.warning(f"轻量级心跳发送失败: {response.status_code}")
            return False

    except Exception as e:
        logging.error(f"轻量级心跳发送异常: {e}")
        return False

def send_full_data_sync():
    """发送完整数据同步到主服务器"""
    global data_sync_in_progress

    # 使用锁确保数据同步的原子性
    with data_sync_lock:
        if data_sync_in_progress:
            logging.debug("数据同步已在进行中，跳过重复同步")
            return True

        data_sync_in_progress = True
        logging.info("开始数据同步，暂停心跳发送")

    try:
        # 获取最新的设备和拓扑信息
        usb_info = get_usb_devices()
        devices = usb_info.get('devices', [])
        topology = usb_info.get('topology', {})

        # 获取设备详细信息
        device_details = []
        for device in devices:
            # 设备数据已经在get_usb_devices中完整处理
            device_details.append(device)

        # 完整数据同步
        data = {
            'timestamp': datetime.now().isoformat(),
            'sync_type': 'full_data',
            'device_count': len(devices),
            'usb_topology': topology,
            'device_details': device_details,
            'device_summary': {
                'total_devices': len(devices),
                'real_hardware_count': sum(1 for d in device_details if d.get('is_real_hardware', True)),
                'auto_bind_eligible_count': sum(1 for d in device_details if d.get('auto_bind_eligible', False)),
                'device_types': {},
                'port_statistics': {
                    'hub_count': topology.get('hub_count', 0),
                    'total_ports': topology.get('total_ports', 0),
                    'occupied_ports': topology.get('occupied_ports', 0),
                    'free_ports': topology.get('free_ports', 0)
                }
            }
        }

        # 统计设备类型
        for device_info in device_details:
            device_type = device_info['device_type']
            data['device_summary']['device_types'][device_type] = data['device_summary']['device_types'].get(device_type, 0) + 1

        # 发送到数据同步API端点
        response = requests.post(
            f"{config['master_url']}/api/v1/slave/data-sync",
            json=data,
            timeout=60,  # 数据同步允许更长的超时时间
            headers={'Content-Type': 'application/json; charset=utf-8'}
        )

        if response.status_code == 200:
            result = response.json()
            logging.info(f"数据同步成功: {result.get('message', '成功')}")
            return True
        elif response.status_code == 404:
            # 从服务器未注册，尝试注册
            logging.info("从服务器未注册，尝试重新注册")
            if register_to_master():
                # 注册成功后不再重新发送心跳，避免无限循环
                last_heartbeat = datetime.now()
                logging.debug("注册成功，心跳将在下次定时发送")
                return True
            return False
        else:
            logging.warning(f"数据同步失败: {response.status_code}")
            return False

    except Exception as e:
        logging.error(f"数据同步异常: {e}")
        return False
    finally:
        # 确保数据同步状态被重置
        with data_sync_lock:
            data_sync_in_progress = False
            logging.info("数据同步完成，恢复心跳发送")

def handle_config_verification(verification_request):
    """处理配置验证请求"""
    try:
        # 获取主服务器记录的配置
        server_config = verification_request.get('server_config', {})

        # 获取当前实际配置
        current_usb_info = get_usb_devices()
        current_devices = current_usb_info.get('devices', [])
        current_topology = current_usb_info.get('topology', {})

        # 比较配置
        config_changed = False

        # 比较设备数量
        if len(current_devices) != server_config.get('device_count', 0):
            config_changed = True
            logging.info(f"设备数量变化: {server_config.get('device_count', 0)} -> {len(current_devices)}")

        # 比较拓扑信息
        server_topology = server_config.get('topology', {})
        if (current_topology.get('hub_count', 0) != server_topology.get('hub_count', 0) or
            current_topology.get('total_ports', 0) != server_topology.get('total_ports', 0)):
            config_changed = True
            logging.info("USB拓扑变化检测到")

        if config_changed:
            logging.info("配置不一致，触发完整数据同步")
            send_full_data_sync()
        else:
            logging.info("配置一致，无需同步")

    except Exception as e:
        logging.error(f"配置验证处理失败: {e}")

def send_device_change_event(is_device_added=True):
    """发送设备变化事件到主服务器"""
    try:
        event_data = {
            'timestamp': datetime.now().isoformat(),
            'event_type': 'device_added' if is_device_added else 'device_removed',
            'server_info': {
                'ip_address': '*********',  # 使用固定IP，避免函数依赖
                'server_name': 'slave-server-local'
            },
            'device_summary': {
                'total_count': len(device_list),
                'change_type': 'addition' if is_device_added else 'removal'
            }
        }

        response = requests.post(
            f"{config['master_url']}/api/v1/slave/device-event",
            json=event_data,
            timeout=5  # 事件同步使用较短超时
        )

        if response.status_code == 200:
            logging.info(f"设备变化事件发送成功: {event_data['event_type']}")
            return True
        else:
            logging.warning(f"设备变化事件发送失败: {response.status_code}")
            # 事件发送失败时，触发完整数据同步作为备用
            threading.Thread(target=send_immediate_data_sync, daemon=True).start()
            return False

    except Exception as e:
        logging.error(f"设备变化事件发送异常: {e}")
        # 异常时触发完整数据同步作为备用
        threading.Thread(target=send_immediate_data_sync, daemon=True).start()
        return False

def send_heartbeat():
    """兼容性函数，调用轻量级心跳"""
    return send_lightweight_heartbeat()

def process_config_update(config_update):
    """处理主服务器下发的配置更新"""
    try:
        config_version = config_update.get('config_version')
        device_configs = config_update.get('device_configs', [])
        server_config = config_update.get('server_config', {})

        logging.info(f"收到配置更新 - 版本: {config_version}, 设备数: {len(device_configs)}")

        # 保存设备配置到本地缓存
        config_cache = {
            'config_version': config_version,
            'device_configs': device_configs,
            'server_config': server_config,
            'last_updated': datetime.now().isoformat()
        }

        # 保存到本地文件
        config_file = os.path.join(os.path.dirname(__file__), 'config', 'device_config_cache.json')
        os.makedirs(os.path.dirname(config_file), exist_ok=True)

        with open(config_file, 'w', encoding='utf-8') as f:
            import json
            json.dump(config_cache, f, ensure_ascii=False, indent=2)

        # 应用服务器配置
        if server_config:
            apply_server_config(server_config)

        # 应用设备配置
        if device_configs:
            apply_device_configs(device_configs)

        logging.info(f"配置更新应用成功 - 版本: {config_version}")

    except Exception as e:
        logging.error(f"处理配置更新失败: {e}")

def apply_server_config(server_config):
    """应用服务器配置"""
    try:
        # 更新全局配置
        global config

        if 'auto_bind_enabled' in server_config:
            config['auto_bind_enabled'] = server_config['auto_bind_enabled']

        if 'scan_interval' in server_config:
            config['scan_interval'] = server_config['scan_interval']

        if 'heartbeat_interval' in server_config:
            config['heartbeat_interval'] = server_config['heartbeat_interval']

        logging.debug(f"服务器配置已更新: {server_config}")

    except Exception as e:
        logging.error(f"应用服务器配置失败: {e}")

def apply_device_configs(device_configs):
    """应用设备配置"""
    try:
        # 更新设备配置映射
        device_config_map = {}
        for device_config in device_configs:
            hardware_signature = device_config.get('hardware_signature')
            if hardware_signature:
                device_config_map[hardware_signature] = device_config

        # 更新全局设备列表中的配置信息
        global device_list
        for device in device_list:
            hardware_signature = getattr(device, 'hardware_signature', '')
            if hardware_signature in device_config_map:
                device_config = device_config_map[hardware_signature]

                # 更新设备名称
                if device_config.get('custom_name'):
                    device.custom_name = device_config['custom_name']
                elif device_config.get('device_name'):
                    device.auto_generated_name = device_config['device_name']

                # 更新设备类型
                if device_config.get('device_type'):
                    device.device_type = device_config['device_type']

                # 更新端口位置码
                if device_config.get('port_location_code'):
                    device.port_location_code = device_config['port_location_code']

        logging.debug(f"设备配置已更新: {len(device_configs)} 个设备")

    except Exception as e:
        logging.error(f"应用设备配置失败: {e}")

def load_cached_config():
    """加载本地缓存的配置"""
    try:
        config_file = os.path.join(os.path.dirname(__file__), 'config', 'device_config_cache.json')
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                import json
                cached_config = json.load(f)

                # 应用缓存的配置
                if 'server_config' in cached_config:
                    apply_server_config(cached_config['server_config'])

                if 'device_configs' in cached_config:
                    apply_device_configs(cached_config['device_configs'])

                logging.info(f"加载缓存配置成功 - 版本: {cached_config.get('config_version')}")
                return cached_config

    except Exception as e:
        logging.error(f"加载缓存配置失败: {e}")

    return None

def heartbeat_worker():
    """心跳工作线程"""
    while True:
        send_heartbeat()
        time.sleep(30)  # 每30秒发送一次心跳

def device_monitor_worker():
    """设备监控工作线程"""
    while True:
        get_usb_devices()
        time.sleep(10)  # 每10秒扫描一次设备

def trigger_immediate_device_sync():
    """立即触发设备扫描和数据同步"""
    try:
        # 1. 立即扫描设备
        scan_devices()

        # 2. 立即发送数据同步到主服务器
        from utils.master_communication import MasterCommunication
        master_comm = MasterCommunication()

        # 准备设备详细信息
        device_details = []
        for device in device_list:
            device_details.append({
                'hardware_signature': device.get('hardware_signature', ''),
                'vendor_id': device.get('vendor_id', 0),
                'product_id': device.get('product_id', 0),
                'bus': device.get('bus', 0),
                'address': device.get('address', 0),
                'description': device.get('description', ''),
                'device_type': device.get('device_type', 'unknown'),
                'is_real_hardware': device.get('is_real_hardware', True),
                'auto_bind_eligible': device.get('auto_bind_eligible', False),
                'auto_generated_name': device.get('auto_generated_name', ''),
                'custom_name': device.get('custom_name', ''),
                'status': device.get('status', 'online'),
                'port_location_code': device.get('port_location_code', ''),
                'physical_port': device.get('physical_port', ''),
                'last_seen': datetime.now().isoformat(),
                # USB.IDS增强信息（如果有）
                'usb_ids_vendor_name': device.get('usb_ids_vendor_name'),
                'usb_ids_device_name': device.get('usb_ids_device_name'),
                'usb_ids_full_name': device.get('usb_ids_full_name'),
                'identification_source': device.get('identification_source', 'local_rules')
            })

        # 发送完整数据同步
        sync_result = master_comm.send_full_data_sync(device_details)

        if sync_result:
            logging.info(f"强制设备同步成功，已上报 {len(device_details)} 个设备")
            return True
        else:
            logging.error("强制设备同步失败")
            return False

    except Exception as e:
        logging.error(f"触发立即设备同步失败: {e}")
        return False

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    CORS(app)

    @app.route('/api/system/health', methods=['GET'])
    def health_check():
        """健康检查"""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'vh_status': 'running' if vh_process and vh_process.poll() is None else 'stopped',
            'device_count': len(device_list)
        })

    @app.route('/api/devices', methods=['GET'])
    def get_devices():
        """获取设备列表"""
        return jsonify({
            'devices': device_list,
            'count': len(device_list),
            'timestamp': datetime.now().isoformat()
        })

    @app.route('/api/local-db/stats', methods=['GET'])
    def get_local_db_stats():
        """获取本地数据库统计信息"""
        try:
            local_db = get_local_db()
            stats = local_db.get_database_stats()

            # 添加当前设备信息
            current_devices = local_db.get_current_devices()
            latest_topology = local_db.get_latest_topology()

            return jsonify({
                'status': 'success',
                'database_stats': stats,
                'current_devices_count': len(current_devices),
                'latest_topology': latest_topology,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            logging.error(f"获取本地数据库统计失败: {e}")
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500

    @app.route('/api/local-db/devices', methods=['GET'])
    def get_local_devices():
        """从本地数据库获取设备列表"""
        try:
            local_db = get_local_db()
            devices = local_db.get_current_devices()

            return jsonify({
                'status': 'success',
                'devices': devices,
                'count': len(devices),
                'source': 'local_database',
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            logging.error(f"从本地数据库获取设备列表失败: {e}")
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500

    @app.route('/api/system/status', methods=['GET'])
    def system_status():
        """系统状态"""
        # 检查VirtualHere状态
        vh_status = 'stopped'
        vh_message = 'VirtualHere未启动'

        if vh_process and vh_process.poll() is None:
            vh_status = 'running'
            vh_message = 'VirtualHere正常运行'
        else:
            # 检查是否是权限问题
            try:
                test_result = subprocess.run(
                    [config['vh_binary'], '-h'],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if test_result.returncode == 0:
                    vh_status = 'permission_denied'
                    vh_message = 'VirtualHere需要USB设备访问权限（WSL环境限制）'
                else:
                    vh_status = 'error'
                    vh_message = 'VirtualHere二进制文件异常'
            except Exception:
                vh_status = 'unavailable'
                vh_message = 'VirtualHere不可用'

        return jsonify({
            'status': 'online',
            'vh_status': vh_status,
            'vh_message': vh_message,
            'device_count': len(device_list),
            'last_heartbeat': last_heartbeat.isoformat() if last_heartbeat else None,
            'system_info': {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent
            },
            'timestamp': datetime.now().isoformat()
        })

    @app.route('/api/system/detail', methods=['GET'])
    def system_detail():
        """获取系统详细信息"""
        try:
            # 基础系统信息
            cpu_info = {
                'percent': psutil.cpu_percent(interval=1),
                'count': psutil.cpu_count(),
                'freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
            }

            memory = psutil.virtual_memory()
            memory_info = {
                'total': memory.total,
                'available': memory.available,
                'percent': memory.percent,
                'used': memory.used,
                'free': memory.free
            }

            disk = psutil.disk_usage('/')
            disk_info = {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': disk.percent
            }

            # 网络信息
            network_info = {}
            try:
                net_io = psutil.net_io_counters()
                network_info = {
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv,
                    'packets_sent': net_io.packets_sent,
                    'packets_recv': net_io.packets_recv
                }
            except:
                pass

            # 系统运行时间
            boot_time = psutil.boot_time()
            uptime = datetime.now().timestamp() - boot_time

            # VirtualHere信息
            vh_info = {
                'status': 'running' if vh_process and vh_process.poll() is None else 'stopped',
                'port': config.get('vh_port', 7575),
                'process_id': vh_process.pid if vh_process and vh_process.poll() is None else None,
                'version': 'VirtualHere USB Server 4.3.3'  # 可以从实际进程获取
            }

            detail_data = {
                'server_info': {
                    'name': generate_unique_server_name(),
                    'ip_address': '127.0.0.1',  # 可以改为动态获取
                    'port': config.get('server_port', 8890),
                    'vh_port': config.get('vh_port', 7575),
                    'uptime': uptime,
                    'boot_time': datetime.fromtimestamp(boot_time).isoformat()
                },
                'system_info': {
                    'cpu': cpu_info,
                    'memory': memory_info,
                    'disk': disk_info,
                    'network': network_info
                },
                'virtualhere_info': vh_info,
                'usb_info': {
                    'devices': device_list,
                    'count': len(device_list),
                    'hub_count': len([d for d in device_list if 'hub' in d.get('description', '').lower()]),
                    'device_count': len([d for d in device_list if 'hub' not in d.get('description', '').lower()])
                },
                'timestamp': datetime.now().isoformat()
            }

            return jsonify(detail_data)
        except Exception as e:
            logging.error(f"获取系统详细信息失败: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/virtualhere/start', methods=['POST'])
    def start_vh():
        """启动VirtualHere"""
        if start_virtualhere():
            return jsonify({'status': 'success', 'message': 'VirtualHere已启动'})
        else:
            return jsonify({'status': 'error', 'message': 'VirtualHere启动失败'}), 500

    @app.route('/api/virtualhere/stop', methods=['POST'])
    def stop_vh():
        """停止VirtualHere"""
        if stop_virtualhere():
            return jsonify({'status': 'success', 'message': 'VirtualHere已停止'})
        else:
            return jsonify({'status': 'error', 'message': 'VirtualHere停止失败'}), 500

    @app.route('/api/virtualhere/restart', methods=['POST'])
    def restart_vh():
        """重启VirtualHere"""
        try:
            # 先停止
            stop_virtualhere()
            time.sleep(2)  # 等待2秒
            # 再启动
            if start_virtualhere():
                return jsonify({'status': 'success', 'message': 'VirtualHere已重启'})
            else:
                return jsonify({'status': 'error', 'message': 'VirtualHere重启失败'}), 500
        except Exception as e:
            logging.error(f"重启VirtualHere失败: {e}")
            return jsonify({'status': 'error', 'message': f'VirtualHere重启失败: {str(e)}'}), 500

    @app.route('/api/system/restart', methods=['POST'])
    def restart_system():
        """重启系统（仅重启从服务器进程）"""
        try:
            # 这里可以实现重启逻辑
            # 注意：在实际环境中，这可能需要特殊的权限和处理
            return jsonify({'status': 'success', 'message': '系统重启命令已发送'})
        except Exception as e:
            logging.error(f"重启系统失败: {e}")
            return jsonify({'status': 'error', 'message': f'系统重启失败: {str(e)}'}), 500

    @app.route('/api/force-sync', methods=['POST'])
    def force_sync_devices():
        """接收主服务器的强制同步请求"""
        try:
            data = request.get_json()
            action = data.get('action')

            if action != 'force_device_sync':
                return jsonify({
                    'status': 'error',
                    'message': '无效的同步动作'
                }), 400

            logging.info("收到主服务器强制同步请求，开始上报设备数据")

            # 立即触发设备扫描和数据同步
            success = trigger_immediate_device_sync()

            if success:
                return jsonify({
                    'status': 'success',
                    'message': '强制同步已触发，设备数据正在上报',
                    'timestamp': datetime.now().isoformat(),
                    'device_count': len(device_list)
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': '强制同步触发失败'
                }), 500

        except Exception as e:
            logging.error(f"处理强制同步请求失败: {e}")
            return jsonify({
                'status': 'error',
                'message': f'强制同步处理失败: {str(e)}'
            }), 500

    @app.route('/api/files/virtualhere', methods=['GET'])
    def get_virtualhere_files():
        """获取VirtualHere相关文件列表"""
        try:
            vh_dir = os.path.join(os.path.dirname(__file__), 'virtualhere')
            files = []

            if os.path.exists(vh_dir):
                for root, dirs, filenames in os.walk(vh_dir):
                    for filename in filenames:
                        if filename.startswith('vhusbd') or filename.startswith('vhclient'):
                            filepath = os.path.join(root, filename)
                            stat = os.stat(filepath)
                            files.append({
                                'name': filename,
                                'path': os.path.relpath(filepath, vh_dir),
                                'size': stat.st_size,
                                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                            })

            return jsonify({
                'status': 'success',
                'files': files,
                'count': len(files),
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            logging.error(f"获取VirtualHere文件列表失败: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    @app.route('/api/files/upload', methods=['POST'])
    def upload_file():
        """上传文件"""
        try:
            if 'file' not in request.files:
                return jsonify({'status': 'error', 'message': '没有文件'}), 400

            file = request.files['file']
            target_name = request.form.get('target_name', file.filename)

            if file.filename == '':
                return jsonify({'status': 'error', 'message': '文件名为空'}), 400

            # 保存到临时目录
            vh_dir = os.path.join(os.path.dirname(__file__), 'virtualhere')
            temp_dir = os.path.join(vh_dir, 'temp')
            os.makedirs(temp_dir, exist_ok=True)

            temp_path = os.path.join(temp_dir, target_name)
            file.save(temp_path)

            return jsonify({
                'status': 'success',
                'message': '文件上传成功',
                'filename': target_name,
                'size': os.path.getsize(temp_path)
            })
        except Exception as e:
            logging.error(f"文件上传失败: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    @app.route('/api/files/replace', methods=['POST'])
    def replace_file():
        """替换文件"""
        try:
            data = request.get_json()
            temp_filename = data.get('temp_filename')
            target_filename = data.get('target_filename')

            if not temp_filename or not target_filename:
                return jsonify({'status': 'error', 'message': '参数不完整'}), 400

            vh_dir = os.path.join(os.path.dirname(__file__), 'virtualhere')
            temp_path = os.path.join(vh_dir, 'temp', temp_filename)
            target_path = os.path.join(vh_dir, target_filename)

            # 备份原文件
            if os.path.exists(target_path):
                backup_path = target_path + '.bak'
                shutil.copy2(target_path, backup_path)
                logging.info(f"备份文件: {target_path} -> {backup_path}")

            # 替换文件
            shutil.move(temp_path, target_path)

            # 设置执行权限（Linux环境）
            if os.name != 'nt':  # 非Windows系统
                os.chmod(target_path, 0o755)

            return jsonify({
                'status': 'success',
                'message': '文件替换成功',
                'target_file': target_filename
            })
        except Exception as e:
            logging.error(f"文件替换失败: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    @app.route('/api/files/backup', methods=['POST'])
    def backup_file():
        """备份文件"""
        try:
            data = request.get_json()
            filename = data.get('filename')

            if not filename:
                return jsonify({'status': 'error', 'message': '文件名不能为空'}), 400

            vh_dir = os.path.join(os.path.dirname(__file__), 'virtualhere')
            source_path = os.path.join(vh_dir, filename)
            backup_path = source_path + '.bak'

            if not os.path.exists(source_path):
                return jsonify({'status': 'error', 'message': '源文件不存在'}), 404

            shutil.copy2(source_path, backup_path)

            return jsonify({
                'status': 'success',
                'message': '文件备份成功',
                'backup_file': filename + '.bak'
            })
        except Exception as e:
            logging.error(f"文件备份失败: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    @app.route('/api/files/adapt-code', methods=['POST'])
    def adapt_code():
        """代码适配：更新配置文件中的VirtualHere程序路径"""
        try:
            data = request.get_json()
            old_filename = data.get('old_filename')
            new_filename = data.get('new_filename')

            if not old_filename or not new_filename:
                return jsonify({'status': 'error', 'message': '参数不完整'}), 400

            # 更新配置文件中的路径引用
            config_file = os.path.join(os.path.dirname(__file__), 'config', 'slave_server.ini')

            if os.path.exists(config_file):
                # 读取配置文件
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 替换路径引用
                old_path = f'virtualhere/{old_filename}'
                new_path = f'virtualhere/{new_filename}'
                updated_content = content.replace(old_path, new_path)

                # 如果有变化，写回文件
                if updated_content != content:
                    # 备份原配置文件
                    backup_config = config_file + '.bak'
                    shutil.copy2(config_file, backup_config)

                    # 写入新配置
                    with open(config_file, 'w', encoding='utf-8') as f:
                        f.write(updated_content)

                    logging.info(f"配置文件已更新: {old_filename} -> {new_filename}")

                    return jsonify({
                        'status': 'success',
                        'message': '代码适配完成',
                        'updated_paths': [old_path + ' -> ' + new_path],
                        'config_backup': backup_config
                    })
                else:
                    return jsonify({
                        'status': 'success',
                        'message': '无需更新配置文件',
                        'updated_paths': []
                    })
            else:
                return jsonify({
                    'status': 'warning',
                    'message': '配置文件不存在，跳过代码适配',
                    'updated_paths': []
                })

        except Exception as e:
            logging.error(f"代码适配失败: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    @app.route('/api/files/rollback', methods=['POST'])
    def rollback_file():
        """回滚文件到备份版本"""
        try:
            data = request.get_json()
            filename = data.get('filename')

            if not filename:
                return jsonify({'status': 'error', 'message': '文件名不能为空'}), 400

            vh_dir = os.path.join(os.path.dirname(__file__), 'virtualhere')
            target_path = os.path.join(vh_dir, filename)
            backup_path = target_path + '.bak'

            if not os.path.exists(backup_path):
                return jsonify({'status': 'error', 'message': '备份文件不存在'}), 404

            # 回滚：备份文件 -> 目标文件
            shutil.copy2(backup_path, target_path)

            # 设置执行权限（Linux环境）
            if os.name != 'nt':  # 非Windows系统
                os.chmod(target_path, 0o755)

            logging.info(f"文件已回滚: {filename}")

            return jsonify({
                'status': 'success',
                'message': '文件回滚成功',
                'restored_file': filename
            })
        except Exception as e:
            logging.error(f"文件回滚失败: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    @app.route('/', methods=['GET'])
    def web_interface():
        """简易Web界面"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>OmniLink从服务器</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .status {{ padding: 10px; margin: 10px 0; border-radius: 5px; }}
                .online {{ background-color: #d4edda; color: #155724; }}
                .offline {{ background-color: #f8d7da; color: #721c24; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <h1>OmniLink从服务器状态</h1>
            <div class="status online">
                <h3>服务状态: 在线</h3>
                <p>VirtualHere: {'运行中' if vh_process and vh_process.poll() is None else '已停止'}</p>
                <p>设备数量: {len(device_list)}</p>
                <p>最后心跳: {last_heartbeat.strftime('%Y-%m-%d %H:%M:%S') if last_heartbeat else '未发送'}</p>
            </div>

            <h3>USB设备列表</h3>
            <table>
                <tr><th>厂商ID</th><th>产品ID</th><th>总线</th><th>地址</th><th>描述</th></tr>
                {''.join([f"<tr><td>{d['vendor_id']:04x}</td><td>{d['product_id']:04x}</td><td>{d['bus']}</td><td>{d['address']}</td><td>{d['description']}</td></tr>" for d in device_list])}
            </table>

            <h3>系统信息</h3>
            <p>CPU使用率: {psutil.cpu_percent():.1f}%</p>
            <p>内存使用率: {psutil.virtual_memory().percent:.1f}%</p>
            <p>磁盘使用率: {psutil.disk_usage('/').percent:.1f}%</p>

            <script>
                setTimeout(function(){{ location.reload(); }}, 30000);
            </script>
        </body>
        </html>
        """
        return html

    return app

def main():
    """主函数"""
    # 设置日志
    setup_logging()

    logging.info("=" * 50)
    logging.info("OmniLink轻量化从服务器启动")
    logging.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info("=" * 50)

    # 创建Flask应用
    app = create_app()

    # 加载缓存配置
    load_cached_config()

    # 启动VirtualHere
    start_virtualhere()

    # 向主服务器注册
    register_to_master()

    # 启动后台线程
    heartbeat_thread = threading.Thread(target=heartbeat_worker, daemon=True)
    heartbeat_thread.start()

    device_thread = threading.Thread(target=device_monitor_worker, daemon=True)
    device_thread.start()

    # 启动Web服务器
    try:
        logging.info(f"启动Web服务器: http://{config['server_host']}:{config['server_port']}")
        app.run(
            host=config['server_host'],
            port=config['server_port'],
            debug=False,
            threaded=True
        )
    except KeyboardInterrupt:
        logging.info("收到中断信号，正在关闭服务器...")
    finally:
        stop_virtualhere()
        logging.info("服务器已关闭")

if __name__ == '__main__':
    import sys

    # 支持命令行参数指定配置文件
    config_file = 'slave_server.ini'
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
        logging.info(f"使用指定配置文件: {config_file}")

    # 加载配置
    load_config(config_file)

    main()
