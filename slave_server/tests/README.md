# OmniLink从服务器测试文档

## 测试概述

本项目包含完整的测试套件，包括单元测试、集成测试和API测试，确保代码质量和功能正确性。

## 测试结构

```
tests/
├── __init__.py                 # 测试包初始化
├── conftest.py                 # pytest配置和fixtures
├── README.md                   # 测试文档
├── unit/                       # 单元测试
│   ├── test_config_manager.py  # 配置管理器测试
│   ├── test_device_dao.py      # 设备DAO测试
│   ├── test_error_handler.py   # 错误处理器测试
│   ├── test_logger.py          # 日志系统测试
│   └── test_performance_monitor.py # 性能监控测试
├── integration/                # 集成测试
│   ├── test_device_api.py      # 设备API测试
│   ├── test_system_api.py      # 系统API测试
│   ├── test_config_api.py      # 配置API测试
│   └── test_server_api.py      # 服务器API测试
└── fixtures/                   # 测试数据
    ├── sample_devices.json     # 示例设备数据
    └── test_configs.ini        # 测试配置文件
```

## 测试类型

### 1. 单元测试 (Unit Tests)

测试单个模块或函数的功能，使用mock对象隔离依赖。

**标记**: `@pytest.mark.unit`

**特点**:
- 快速执行
- 隔离测试
- 高覆盖率
- 无外部依赖

**示例**:
```python
@pytest.mark.unit
def test_config_manager_get_value(config_manager):
    """测试配置管理器获取值"""
    value = config_manager.get_config_value('server', 'host')
    assert value == '127.0.0.1'
```

### 2. 集成测试 (Integration Tests)

测试多个模块之间的交互和集成。

**标记**: `@pytest.mark.integration`

**特点**:
- 测试模块间交互
- 使用真实或模拟的外部服务
- 验证端到端功能

**示例**:
```python
@pytest.mark.integration
def test_device_api_workflow(client):
    """测试设备API工作流"""
    # 获取设备列表
    response = client.get('/api/devices')
    assert response.status_code == 200
    
    # 连接设备
    response = client.post('/api/devices/test-device/connect', json=data)
    assert response.status_code == 200
```

### 3. API测试 (API Tests)

测试REST API端点的功能和响应。

**标记**: `@pytest.mark.api`

**特点**:
- 测试HTTP接口
- 验证请求/响应格式
- 测试错误处理
- 验证状态码

## 运行测试

### 使用pytest直接运行

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit -m unit

# 运行集成测试
pytest tests/integration -m integration

# 运行API测试
pytest tests/integration -m api

# 运行特定测试文件
pytest tests/unit/test_config_manager.py

# 运行特定测试函数
pytest tests/unit/test_config_manager.py::TestConfigManager::test_get_server_config

# 详细输出
pytest -v

# 生成覆盖率报告
pytest --cov=. --cov-report=html

# 并行运行
pytest -n auto
```

### 使用测试运行器

```bash
# 运行所有测试
python test_runner.py

# 运行单元测试
python test_runner.py --type unit

# 运行集成测试
python test_runner.py --type integration

# 运行API测试
python test_runner.py --type api

# 详细输出
python test_runner.py --verbose

# 生成报告
python test_runner.py --report

# 安装测试依赖
python test_runner.py --install-deps

# 清理测试产物
python test_runner.py --clean
```

## 测试配置

### pytest.ini

项目根目录的`pytest.ini`文件包含pytest配置：

```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts = --strict-markers --verbose --cov=. --cov-report=html
markers =
    unit: 单元测试
    integration: 集成测试
    api: API测试
    slow: 慢速测试
```

### conftest.py

包含pytest fixtures和配置：

- `temp_dir`: 临时目录
- `test_config`: 测试配置文件
- `config_manager`: 配置管理器实例
- `test_db`: 测试数据库
- `app`: Flask应用实例
- `client`: Flask测试客户端
- `mock_*`: 各种mock对象

## 编写测试

### 测试命名规范

- 测试文件: `test_*.py`
- 测试类: `Test*`
- 测试函数: `test_*`

### 测试结构

```python
@pytest.mark.unit
class TestClassName:
    """测试类文档字符串"""
    
    def test_function_name(self, fixture_name):
        """测试函数文档字符串"""
        # Arrange - 准备测试数据
        expected_value = 'expected'
        
        # Act - 执行被测试的功能
        result = function_under_test(input_data)
        
        # Assert - 验证结果
        assert result == expected_value
```

### 使用Mock

```python
from unittest.mock import patch, Mock

def test_with_mock():
    """使用mock的测试示例"""
    with patch('module.external_service') as mock_service:
        mock_service.return_value = 'mocked_result'
        
        result = function_that_uses_external_service()
        
        assert result == 'mocked_result'
        mock_service.assert_called_once()
```

### 参数化测试

```python
@pytest.mark.parametrize("input_value,expected", [
    ('input1', 'output1'),
    ('input2', 'output2'),
    ('input3', 'output3'),
])
def test_parametrized(input_value, expected):
    """参数化测试示例"""
    result = function_under_test(input_value)
    assert result == expected
```

## 测试数据

### Fixtures

测试数据通过fixtures提供：

```python
@pytest.fixture
def sample_device():
    """示例设备数据"""
    return {
        'device_uuid': 'test-device-001',
        'vendor': 'Test Vendor',
        'product': 'Test Product',
        'status': 'available'
    }
```

### 测试数据文件

在`tests/fixtures/`目录下存放测试数据文件：

- `sample_devices.json`: 示例设备数据
- `test_configs.ini`: 测试配置文件

## 覆盖率要求

- 最低覆盖率: 80%
- 目标覆盖率: 90%+

### 查看覆盖率报告

```bash
# 生成HTML覆盖率报告
pytest --cov=. --cov-report=html

# 查看报告
open htmlcov/index.html
```

### 覆盖率排除

在`pytest.ini`中配置排除项：

```ini
[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
```

## 持续集成

### GitHub Actions

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.11
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        python test_runner.py --install-deps
    - name: Run tests
      run: python test_runner.py --report
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

## 测试最佳实践

### 1. 测试隔离

- 每个测试应该独立运行
- 使用fixtures提供测试数据
- 清理测试后的状态

### 2. 测试命名

- 使用描述性的测试名称
- 说明测试的目的和预期结果
- 遵循命名约定

### 3. 断言

- 使用明确的断言
- 提供有意义的错误消息
- 一个测试一个断言（推荐）

### 4. Mock使用

- 只mock外部依赖
- 验证mock的调用
- 使用合适的mock类型

### 5. 测试数据

- 使用最小化的测试数据
- 避免硬编码值
- 使用工厂模式生成测试数据

## 故障排除

### 常见问题

1. **导入错误**
   ```bash
   # 确保项目根目录在Python路径中
   export PYTHONPATH=$PYTHONPATH:$(pwd)
   ```

2. **数据库错误**
   ```bash
   # 清理测试数据库
   rm -f test-data/*.db
   ```

3. **权限错误**
   ```bash
   # 确保测试目录权限
   chmod -R 755 tests/
   ```

4. **依赖冲突**
   ```bash
   # 重新安装测试依赖
   python test_runner.py --install-deps
   ```

### 调试测试

```bash
# 运行单个测试并显示输出
pytest tests/unit/test_config_manager.py::test_function -s

# 在失败时进入调试器
pytest --pdb

# 显示本地变量
pytest --tb=long
```

## 更多资源

- [pytest文档](https://docs.pytest.org/)
- [unittest.mock文档](https://docs.python.org/3/library/unittest.mock.html)
- [coverage.py文档](https://coverage.readthedocs.io/)
- [项目API文档](../doc/API_Documentation.md)
