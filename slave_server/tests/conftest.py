# -*- coding: utf-8 -*-
"""
pytest配置文件
"""

import pytest
from typing import Dict, List, Any, Optional
import tempfile
import shutil
import os
from unittest.mock import Mock, patch

# 导入应用模块
from utils.config_manager import ConfigManager
from utils.logger import get_logger
from db import init_database
from main import create_app


@pytest.fixture(scope="session")
def temp_dir() -> None:
    """创建临时目录"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture(scope="session")
def test_config(temp_dir) -> Any:
    """测试配置"""
    config_file = os.path.join(temp_dir, "test_config.ini")
    
    # 创建测试配置文件
    config_content = """
[server]
host = 127.0.0.1
port = 8890
debug = true
workers = 1

[database]
db_file = {}/test.db
backup_enabled = false

[virtualhere]
server_port = 7576
binary_path = /tmp/vhusbd_test
auto_start = false

[logging]
log_level = DEBUG
log_file = {}/test.log
console_output = false

[master]
server_url = http://localhost:8888
auth_token = test-token
heartbeat_interval = 60

[monitoring]
enabled = false
performance_enabled = false
""".format(temp_dir, temp_dir)
    
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    return config_file


@pytest.fixture
def config_manager(test_config) -> Any:
    """配置管理器实例"""
    return ConfigManager(test_config)


@pytest.fixture
def test_db(temp_dir) -> Any:
    """测试数据库"""
    db_file = os.path.join(temp_dir, "test.db")
    
    # 初始化测试数据库
    with patch('utils.config_manager.ConfigManager') as mock_config:
        mock_config.return_value.get_database_config.return_value = {
            'db_file': db_file
        }
        init_database()
    
    yield db_file
    
    # 清理
    if os.path.exists(db_file):
        os.remove(db_file)


@pytest.fixture
def app(test_config, test_db) -> Any:
    """Flask应用实例"""
    with patch('main.config_manager') as mock_config:
        mock_config.config_file = test_config
        mock_config.get_server_config.return_value = {
            'host': '127.0.0.1',
            'port': 8890,
            'debug': True
        }
        mock_config.get_database_config.return_value = {
            'db_file': test_db
        }
        
        app = create_app()
        app.config['TESTING'] = True
        
        yield app


@pytest.fixture
def client(app) -> Any:
    """Flask测试客户端"""
    return app.test_client()


@pytest.fixture
def mock_vh_server() -> None:
    """模拟VirtualHere服务器"""
    with patch('utils.vh_server_manager.VirtualHereServerManager') as mock:
        mock_instance = Mock()
        mock_instance.get_server_status.return_value = {
            'running': True,
            'pid': 1234,
            'port': 7575,
            'port_status': 'listening',
            'uptime': 3600,
            'version': '4.5.4'
        }
        mock_instance.start_server.return_value = True
        mock_instance.stop_server.return_value = True
        mock_instance.restart_server.return_value = True
        mock.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_device_dao() -> None:
    """模拟设备DAO"""
    with patch('db.device_dao.DeviceDAO') as mock:
        mock_instance = Mock()
        mock_instance.get_all_devices.return_value = []
        mock_instance.get_device_count.return_value = 0
        mock_instance.get_device_statistics.return_value = {
            'total_devices': 0,
            'available_devices': 0,
            'connected_devices': 0
        }
        mock.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def sample_device() -> None:
    """示例设备数据"""
    return {
        'device_uuid': 'test-device-uuid',
        'device_address': '1-1.2',
        'vendor': 'Test Vendor',
        'product': 'Test Product',
        'vendor_id': '1234',
        'product_id': '5678',
        'status': 'available',
        'shown': True,
        'disable': False,
        'nick_name': 'Test Device',
        'last_seen': '2025-01-27T10:30:00'
    }


@pytest.fixture
def mock_master_comm() -> None:
    """模拟主从通信"""
    with patch('utils.master_communication.MasterCommunication') as mock:
        mock_instance = Mock()
        mock_instance.is_connected.return_value = True
        mock_instance.get_connection_status.return_value = {
            'connected': True,
            'consecutive_failures': 0,
            'circuit_breaker_open': False,
            'pending_reports': 0
        }
        mock_instance.report_device_states_incremental.return_value = True
        mock.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_performance_monitor() -> None:
    """模拟性能监控器"""
    with patch('utils.performance_monitor.global_performance_monitor') as mock:
        mock.get_current_performance.return_value = {
            'cpu': {'cpu_percent': 15.2},
            'memory': {'virtual': {'percent': 45.8}},
            'disk': {'usage': {'percent': 32.1}},
            'timestamp': 1706342400.123
        }
        mock.get_statistics.return_value = {
            'monitor_status': {'running': True},
            'data_counts': {'cpu_samples': 100}
        }
        yield mock


@pytest.fixture
def mock_error_handler() -> None:
    """模拟错误处理器"""
    with patch('utils.error_handler.global_error_handler') as mock:
        mock.get_error_statistics.return_value = {
            'total_errors': 5,
            'recent_errors_1h': 1,
            'category_distribution': {'network': 2, 'system': 3}
        }
        mock.get_error_history.return_value = []
        yield mock


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch) -> Any:
    """设置测试环境"""
    # 设置测试环境变量
    monkeypatch.setenv('FLASK_ENV', 'testing')
    monkeypatch.setenv('LOG_LEVEL', 'DEBUG')
    monkeypatch.setenv('ENABLE_MONITORING', 'false')
    monkeypatch.setenv('ENABLE_PERFORMANCE_MONITOR', 'false')


# 测试标记
def pytest_configure(config) -> Any:
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "api: API测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )


# 测试数据清理
@pytest.fixture(autouse=True)
def cleanup_after_test() -> None:
    """测试后清理"""
    yield
    # 这里可以添加测试后的清理逻辑
