#!/usr/bin/env python3
"""
OmniLink极简从服务器 v2.0
专注USB设备代理，极致性能优化
基于aiohttp + 事件驱动架构
"""

import asyncio
import signal
import logging
import os
import sys
import configparser
from pathlib import Path
from datetime import datetime

# 添加core模块到路径
sys.path.insert(0, str(Path(__file__).parent / 'core'))

from core.server import LightweightServer
from core.usb_manager import EventDrivenUSBManager
from core.vh_manager import VirtualHereManager
from core.communication import MasterCommunicator
from core.management import ManagementModule

class OmniLinkSlaveServer:
    """OmniLink极简从服务器"""
    
    def __init__(self):
        self.config = {}
        self.running = False
        
        # 核心组件
        self.usb_manager = None
        self.vh_manager = None
        self.communicator = None
        self.management = None
        self.server = None
        
        # 加载配置
        self._load_config()
        self._setup_logging()
    
    def _detect_master_server_url(self):
        """智能检测主服务器URL"""
        # 优先级：环境变量 > 配置文件 > 自动检测

        # 1. 检查环境变量
        env_url = os.environ.get('MASTER_SERVER_URL')
        if env_url:
            return env_url

        # 2. 检查配置文件
        config_file = Path(__file__).parent / 'config' / 'slave_server.ini'
        if config_file.exists():
            parser = configparser.ConfigParser()
            parser.read(config_file, encoding='utf-8')
            config_url = parser.get('master', 'url', fallback=None)
            if config_url:
                return config_url

        # 3. 自动检测网络环境
        import socket
        import subprocess

        # 检测是否在容器环境中
        in_container = os.path.exists('/.dockerenv') or os.environ.get('DOCKER_CONTAINER') == 'true'

        if in_container:
            # 容器环境：尝试不同的容器网络地址
            possible_urls = [
                'http://main_server:8000',  # Docker Compose服务名
                'http://omnilink-main:8000',  # 可能的服务名
                'http://**********:8000',   # Docker默认网关
                'http://host.docker.internal:8000',  # Docker Desktop
                'http://localhost:8000'      # 本地回环
            ]
        else:
            # 非容器环境：尝试本地网络地址
            try:
                # 获取本机IP
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                s.close()

                possible_urls = [
                    'http://localhost:8000',
                    'http://127.0.0.1:8000',
                    f'http://{local_ip}:8000',
                    'http://*************:8000',  # 原始配置作为备选
                ]
            except:
                possible_urls = [
                    'http://localhost:8000',
                    'http://127.0.0.1:8000',
                    'http://*************:8000'
                ]

        # 测试连接并返回第一个可用的URL
        for url in possible_urls:
            try:
                import urllib.request
                urllib.request.urlopen(f"{url}/api/v1/slave/stats", timeout=2)
                print(f"✅ 检测到主服务器: {url}")
                return url
            except:
                continue

        # 如果都不可用，返回默认值并警告
        default_url = 'http://localhost:8000'
        print(f"⚠️ 无法自动检测主服务器，使用默认地址: {default_url}")
        return default_url

    def _generate_unique_server_name(self):
        """
        生成唯一的服务器名称，避免多实例部署冲突
        统一命名规范：基于硬件UUID + MAC地址 + 时间戳的三重保障机制
        """
        import uuid
        import time
        import hashlib
        import socket

        try:
            # 方案1：尝试获取硬件UUID（最稳定的标识）
            hardware_uuid = None
            try:
                from core.hardware_fingerprint import HardwareFingerprint
                fingerprint = HardwareFingerprint()
                hardware_uuid = fingerprint.generate_uuid()[:8]  # 取前8位作为标识
            except Exception:
                pass

            # 方案2：基于MAC地址生成唯一标识
            mac = uuid.getnode()
            mac_suffix = f"{mac:012x}"[-6:]  # 取MAC地址后6位

            # 方案3：基于主机名增加可识别性
            hostname = socket.gethostname()
            hostname_hash = hashlib.md5(hostname.encode()).hexdigest()[:4]

            # 方案4：基于端口号增加可识别性
            port_suffix = getattr(self, 'config', {}).get('server_port', 8889)

            # 方案5：基于时间戳确保唯一性
            timestamp = int(time.time() * 1000) % 100000  # 取时间戳后5位

            # 组合生成唯一名称（优先使用硬件UUID）
            if hardware_uuid:
                unique_name = f"OmniLink-Slave-{hardware_uuid}-{hostname_hash}-{port_suffix}"
            else:
                unique_name = f"OmniLink-Slave-{mac_suffix}-{hostname_hash}-{port_suffix}-{timestamp}"

            print(f"生成唯一服务器名称: {unique_name}")
            return unique_name

        except Exception as e:
            # 降级方案：使用时间戳和随机数
            import random
            fallback_id = f"{int(time.time() % 10000)}-{random.randint(1000, 9999)}"
            fallback_name = f"OmniLink-Slave-{fallback_id}"
            print(f"名称生成失败，使用降级方案: {fallback_name}, 错误: {e}")
            return fallback_name

    def _load_config(self):
        """加载配置文件"""
        config_file = Path(__file__).parent / 'config' / 'slave_server.ini'
        
        if config_file.exists():
            parser = configparser.ConfigParser()
            parser.read(config_file, encoding='utf-8')
            
            self.config = {
                # 服务器配置
                'server_host': parser.get('server', 'host', fallback='0.0.0.0'),
                'server_port': parser.getint('server', 'port', fallback=8889),
                'server_name': self._generate_unique_server_name(),

                # VirtualHere配置
                'vh_port': parser.getint('virtualhere', 'server_port', fallback=7575),
                'vh_binary': parser.get('virtualhere', 'binary_path', fallback='/app/vhusbd'),

                # 日志配置
                'log_level': parser.get('logging', 'log_level', fallback='INFO'),
                'log_file': parser.get('logging', 'log_file', fallback='logs/slave_server.log'),

                # 主服务器配置 - 智能检测
                'master_url': self._detect_master_server_url()
            }
        else:
            # 默认配置
            self.config = {
                'server_host': '0.0.0.0',
                'server_port': 8889,
                'server_name': self._generate_unique_server_name(),
                'vh_port': 7575,
                'vh_binary': '/app/vhusbd',
                'log_level': 'INFO',
                'log_file': 'logs/slave_server.log',
                'master_url': self._detect_master_server_url()
            }

    def _load_config_from_file(self, config_file_path):
        """从指定文件加载配置"""
        config_file = Path(config_file_path)

        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file_path}")

        parser = configparser.ConfigParser()
        parser.read(config_file, encoding='utf-8')

        self.config = {
            # 服务器配置
            'server_host': parser.get('server', 'host', fallback='0.0.0.0'),
            'server_port': parser.getint('server', 'port', fallback=8889),
            'server_name': self._generate_unique_server_name(),

            # VirtualHere配置
            'vh_port': parser.getint('virtualhere', 'server_port', fallback=7575),
            'vh_binary': parser.get('virtualhere', 'binary_path', fallback='/app/vhusbd'),

            # 日志配置
            'log_level': parser.get('logging', 'log_level', fallback='INFO'),
            'log_file': parser.get('logging', 'log_file', fallback='logs/slave_server.log'),

            # 主服务器配置
            'master_url': parser.get('master', 'url', fallback=self._detect_master_server_url())
        }

        print(f"从文件加载配置完成: {config_file_path}")
        print(f"配置内容: {self.config}")

    def _setup_logging(self):
        """设置日志"""
        log_dir = Path(self.config['log_file']).parent
        log_dir.mkdir(exist_ok=True)
        
        # 配置日志格式
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # 配置日志处理器
        handlers = [
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(self.config['log_file'], encoding='utf-8')
        ]
        
        # 设置日志级别
        log_level = getattr(logging, self.config['log_level'].upper(), logging.INFO)
        
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=handlers
        )
        
        # 设置第三方库日志级别
        logging.getLogger('aiohttp').setLevel(logging.WARNING)
        logging.getLogger('asyncio').setLevel(logging.WARNING)
    
    async def start(self):
        """启动服务器"""
        try:
            logger = logging.getLogger(__name__)
            
            logger.info("=" * 60)
            logger.info("🚀 OmniLink极简从服务器 v2.0 启动中...")
            logger.info(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"🌐 服务地址: http://{self.config['server_host']}:{self.config['server_port']}")
            logger.info(f"🔌 VirtualHere端口: {self.config['vh_port']}")
            logger.info(f"📡 主服务器: {self.config['master_url']}")
            logger.info("=" * 60)
            
            # 初始化组件
            await self._initialize_components()
            
            # 设置组件依赖关系
            self._setup_dependencies()
            
            # 启动各个组件
            await self._start_components()
            
            self.running = True
            logger.info("✅ 服务器启动完成，所有组件运行正常")
            
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"❌ 服务器启动失败: {e}")
            raise
    
    async def _initialize_components(self):
        """初始化组件"""
        logger = logging.getLogger(__name__)
        
        # 初始化USB管理器
        logger.info("🔌 初始化USB设备管理器...")
        self.usb_manager = EventDrivenUSBManager()
        
        # 初始化VirtualHere管理器
        logger.info("🖥️ 初始化VirtualHere管理器...")
        self.vh_manager = VirtualHereManager(self.config)
        
        # 初始化主服务器通信器
        logger.info("📡 初始化主服务器通信器...")
        self.communicator = MasterCommunicator(self.config)
        
        # 初始化管理模块
        logger.info("⚙️ 初始化管理模块...")
        self.management = ManagementModule(self.config)
        
        # 初始化HTTP服务器
        logger.info("🌐 初始化HTTP服务器...")
        self.server = LightweightServer(
            host=self.config['server_host'],
            port=self.config['server_port']
        )
    
    def _setup_dependencies(self):
        """设置组件依赖关系"""
        logger = logging.getLogger(__name__)
        logger.info("🔗 设置组件依赖关系...")
        
        # 设置HTTP服务器依赖
        self.server.set_dependencies(
            self.usb_manager,
            self.vh_manager,
            self.communicator,
            self.management
        )
        
        # 设置通信器依赖
        self.communicator.set_dependencies(
            self.usb_manager,
            self.vh_manager
        )
        
        # 设置管理模块依赖
        self.management.set_dependencies(
            self.vh_manager,
            self.server
        )
    
    async def _start_components(self):
        """启动各个组件"""
        logger = logging.getLogger(__name__)
        
        # 启动USB管理器
        logger.info("🔌 启动USB设备监控...")
        await self.usb_manager.start()
        
        # 启动VirtualHere管理器
        logger.info("🖥️ 启动VirtualHere服务...")
        await self.vh_manager.start()
        
        # 启动主服务器通信
        logger.info("📡 启动主服务器通信...")
        await self.communicator.start()

        # 初始化端口位置码系统
        if self.communicator.hardware_uuid:
            logger.info("📍 初始化端口位置码系统...")
            success = self.usb_manager.initialize_port_location_system(self.communicator.hardware_uuid)
            if success:
                logger.info("✅ 端口位置码系统初始化成功")
            else:
                logger.warning("⚠️  端口位置码系统初始化失败")

        # 启动HTTP服务器
        logger.info("🌐 启动HTTP服务器...")
        await self.server.start()
    
    async def stop(self):
        """停止服务器"""
        logger = logging.getLogger(__name__)
        logger.info("🛑 正在关闭服务器...")
        
        self.running = False
        
        try:
            # 按相反顺序停止组件
            if self.server:
                logger.info("🌐 停止HTTP服务器...")
                await self.server.stop()
            
            if self.communicator:
                logger.info("📡 停止主服务器通信...")
                await self.communicator.stop()
            
            if self.vh_manager:
                logger.info("🖥️ 停止VirtualHere服务...")
                await self.vh_manager.stop()
            
            if self.usb_manager:
                logger.info("🔌 停止USB设备监控...")
                await self.usb_manager.stop()
            
            logger.info("✅ 服务器已安全关闭")
            
        except Exception as e:
            logger.error(f"❌ 关闭服务器时出错: {e}")
    
    def get_status(self):
        """获取服务器状态"""
        return {
            'running': self.running,
            'config': self.config,
            'components': {
                'usb_manager': self.usb_manager.get_statistics() if self.usb_manager else None,
                'vh_manager': asyncio.create_task(self.vh_manager.get_status()) if self.vh_manager else None,
                'communicator': self.communicator.get_status() if self.communicator else None,
                'management': self.management.get_status() if self.management else None
            }
        }

async def main():
    """主函数"""
    server = OmniLinkSlaveServer()
    
    # 信号处理
    def signal_handler(signum, frame):
        logger = logging.getLogger(__name__)
        logger.info(f"收到信号 {signum}，准备关闭服务器...")
        asyncio.create_task(server.stop())
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动服务器
        await server.start()
        
        # 保持运行
        while server.running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger = logging.getLogger(__name__)
        logger.info("收到键盘中断，正在关闭服务器...")
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"服务器运行时出错: {e}")
    finally:
        await server.stop()

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)
