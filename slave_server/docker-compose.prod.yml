version: '3.8'

# 生产环境配置
# 使用方法: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

services:
  # 从服务器生产配置
  slave-server:
    # 生产环境变量
    environment:
      - FLASK_ENV=production
      - LOG_LEVEL=WARNING
      - ENABLE_PERFORMANCE_MONITOR=true
      - PYTHONUNBUFFERED=1
      - ENABLE_PROFILING=false
      - DEBUG_MODE=false
    
    # 生产端口映射（移除调试端口）
    ports:
      - "8889:8889"
      - "7575:7575"
    
    # 生产数据卷（使用命名卷）
    volumes:
      - slave_prod_data:/app/data
      - slave_prod_logs:/app/logs
      - slave_prod_config:/app/config
      - /dev/bus/usb:/dev/bus/usb:rw
    
    # 生产启动命令
    command: >
      sh -c "
        echo 'Starting in production mode...' &&
        python main.py
      "
    
    # 生产健康检查（更严格）
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8889/api/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # 生产资源限制
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    
    # 生产重启策略
    restart: unless-stopped
    
    # 生产日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Redis生产配置
  redis:
    # 生产配置（更严格的内存限制）
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru --save 900 1 --save 300 10 --save 60 10000
    
    # 生产资源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.2'
          memory: 128M
    
    # 生产重启策略
    restart: unless-stopped
    
    # 生产日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # 监控服务生产配置
  monitoring:
    # 生产配置文件
    volumes:
      - ./monitoring/prometheus.prod.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_prod_data:/prometheus
    
    # 生产启动参数（更长的保留时间）
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--log.level=warn'
    
    # 生产资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 256M
    
    # 生产重启策略
    restart: unless-stopped

  # Grafana生产配置
  grafana:
    # 生产环境变量
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      - GF_LOG_LEVEL=warn
      - GF_FEATURE_TOGGLES_ENABLE=ngalert
      - GF_SECURITY_DISABLE_GRAVATAR=true
      - GF_ANALYTICS_REPORTING_ENABLED=false
    
    # 生产数据卷
    volumes:
      - grafana_prod_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    
    # 生产资源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.2'
          memory: 128M
    
    # 生产重启策略
    restart: unless-stopped

  # 日志收集服务生产配置
  fluentd:
    # 生产配置文件
    volumes:
      - ./monitoring/fluentd/fluent.prod.conf:/fluentd/etc/fluent.conf:ro
      - fluentd_prod_data:/fluentd/log
    
    # 生产资源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M
    
    # 生产重启策略
    restart: unless-stopped

  # Nginx反向代理（生产环境）
  nginx:
    image: nginx:alpine
    container_name: omnilink-nginx
    hostname: nginx
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "80:80"
      - "443:443"
    
    # 配置文件
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    
    # 依赖服务
    depends_on:
      - slave-server
      - grafana
    
    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M
    
    # 标签
    labels:
      - "com.omnilink.service=nginx"
      - "com.omnilink.description=Nginx反向代理"

  # 备份服务
  backup:
    image: alpine:latest
    container_name: omnilink-backup
    hostname: backup
    restart: "no"
    
    # 数据卷
    volumes:
      - slave_prod_data:/backup/data:ro
      - slave_prod_config:/backup/config:ro
      - backup_storage:/backup/output
      - ./scripts/backup.sh:/backup/backup.sh:ro
    
    # 备份命令
    command: >
      sh -c "
        echo 'Setting up backup cron job...' &&
        echo '0 2 * * * /backup/backup.sh' | crontab - &&
        crond -f
      "
    
    # 环境变量
    environment:
      - BACKUP_RETENTION_DAYS=30
      - BACKUP_COMPRESS=true
    
    # 标签
    labels:
      - "com.omnilink.service=backup"
      - "com.omnilink.description=数据备份服务"

# 生产数据卷
volumes:
  slave_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/omnilink/data
  
  slave_prod_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/omnilink/logs
  
  slave_prod_config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/omnilink/config
  
  prometheus_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/omnilink/monitoring/prometheus-data
  
  grafana_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/omnilink/monitoring/grafana-data
  
  fluentd_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/omnilink/monitoring/fluentd-data
  
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/omnilink/nginx/logs
  
  backup_storage:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/omnilink/backups

# 生产网络配置
networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: omnilink-prod
