version: '3.8'

services:
  # OmniLink从服务器 - 轻量化版本
  slave-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: omnilink-slave-server
    hostname: slave-server
    restart: unless-stopped

    # 端口映射
    ports:
      - "8889:8889"    # Web API端口
      - "7575:7575"    # VirtualHere服务器端口

    # 环境变量
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8889
      - VH_SERVER_PORT=7575
      - LOG_LEVEL=INFO
      - FLASK_ENV=production
      - DEBUG=false
      - MASTER_SERVER_URL=${MASTER_SERVER_URL:-http://*************:8000}
      - TZ=Asia/Shanghai

    # 数据卷
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
      - /dev/bus/usb:/dev/bus/usb:rw  # USB设备访问

    # 设备访问
    devices:
      - /dev/bus/usb:/dev/bus/usb

    # 特权模式（USB设备访问需要）
    privileged: true

    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8889/api/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

    # 资源限制 - 符合轻量化要求
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M

    # 标签
    labels:
      - "com.omnilink.service=slave-server"
      - "com.omnilink.version=2.0.0"
      - "com.omnilink.description=OmniLink轻量化从服务器"


