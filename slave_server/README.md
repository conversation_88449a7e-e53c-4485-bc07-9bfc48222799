# OmniLink从服务器

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.11+-green.svg)](https://python.org)
[![Docker](https://img.shields.io/badge/docker-supported-blue.svg)](https://docker.com)
[![Tests](https://img.shields.io/badge/tests-passing-green.svg)](tests/)

OmniLink从服务器是一个基于Python和Flask的USB设备远程共享服务，通过VirtualHere技术实现USB设备的网络共享和管理。

## 🚀 特性

### 核心功能
- **USB设备管理**: 自动发现、连接、断开USB设备
- **远程共享**: 通过VirtualHere实现USB设备网络共享
- **设备监控**: 实时监控设备状态和连接情况
- **主从通信**: 与主服务器进行心跳和状态同步

### 系统管理
- **性能监控**: CPU、内存、磁盘、网络实时监控
- **健康检查**: 全面的系统健康状态检查
- **自诊断**: 智能故障诊断和问题分析
- **日志管理**: 结构化日志和自动轮转

### 配置管理
- **多环境支持**: 开发、测试、生产环境配置
- **环境变量**: 支持环境变量覆盖配置
- **热重载**: 配置文件动态重载
- **备份恢复**: 配置备份和恢复功能

### 监控告警
- **Prometheus集成**: 指标收集和监控
- **Grafana仪表板**: 可视化监控面板
- **错误分析**: 智能错误分类和处理
- **告警通知**: 邮件、Webhook告警支持

## 📋 系统要求

### 最低要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+) 或 macOS 10.15+
- **CPU**: 2核心
- **内存**: 2GB RAM
- **存储**: 10GB 可用空间
- **网络**: 千兆以太网连接

### 推荐配置
- **操作系统**: Ubuntu 22.04 LTS
- **CPU**: 4核心
- **内存**: 4GB RAM
- **存储**: 50GB SSD
- **网络**: 千兆以太网连接

### 软件依赖
- Python 3.11+
- Docker 20.10+ (可选)
- VirtualHere USB服务器

## 🛠️ 快速开始

### 使用Docker部署（推荐）

```bash
# 1. 克隆仓库
git clone <repository-url> omnilink-slave-server
cd omnilink-slave-server

# 2. 配置环境变量
cp config/docker.env.example .env
vim .env  # 编辑配置

# 3. 启动服务
docker-compose up -d

# 4. 验证部署
curl http://localhost:8889/api/system/health
```

### 使用部署脚本

```bash
# 赋予执行权限
chmod +x deploy.sh

# 部署到生产环境
./deploy.sh

# 部署到开发环境
./deploy.sh --env development

# 使用原生模式部署
./deploy.sh --mode native
```

### 手动安装

```bash
# 1. 安装系统依赖
sudo apt update
sudo apt install python3.11 python3.11-venv git curl

# 2. 创建虚拟环境
python3.11 -m venv venv
source venv/bin/activate

# 3. 安装Python依赖
pip install -r requirements.txt

# 4. 下载VirtualHere
curl -o vhusbd https://www.virtualhere.com/sites/default/files/usbserver/vhusbd
chmod +x vhusbd

# 5. 配置服务
cp config/slave_server.ini.template config/slave_server.ini
vim config/slave_server.ini

# 6. 启动服务
python main.py
```

## 📖 文档

### 用户文档
- [部署指南](doc/Deployment_Guide.md) - 详细的部署说明
- [API文档](doc/API_Documentation.md) - 完整的API参考
- [API使用指南](doc/API_Usage_Guide.md) - API使用示例
- [配置说明](config/README.md) - 配置文件详解

### 开发文档
- [架构文档](doc/Architecture.md) - 系统架构说明
- [开发指南](doc/Development_Guide.md) - 开发环境搭建
- [测试文档](tests/README.md) - 测试框架说明
- [故障排除](doc/Troubleshooting.md) - 常见问题解决

## 🏗️ 项目结构

```
omnilink-slave-server/
├── main.py                    # 主程序入口
├── requirements.txt           # Python依赖
├── docker-compose.yml         # Docker编排文件
├── deploy.sh                  # 部署脚本
├── Makefile                   # 构建脚本
├── pytest.ini                # 测试配置
├── config/                    # 配置文件
│   ├── slave_server.ini.template
│   ├── vhusbd.conf.template
│   └── README.md
├── db/                        # 数据库模块
│   ├── models.py              # 数据模型
│   ├── device_dao.py          # 设备数据访问
│   └── __init__.py
├── restful/                   # REST API模块
│   ├── device_service.py      # 设备服务API
│   ├── system_service.py      # 系统服务API
│   ├── config_service.py      # 配置服务API
│   └── __init__.py
├── utils/                     # 工具模块
│   ├── config_manager.py      # 配置管理
│   ├── logger.py              # 日志系统
│   ├── error_handler.py       # 错误处理
│   ├── performance_monitor.py # 性能监控
│   └── vh_server_manager.py   # VirtualHere管理
├── tasks/                     # 后台任务
│   ├── device_monitor.py      # 设备监控
│   ├── heartbeat.py           # 心跳管理
│   └── command_processor.py   # 命令处理
├── tests/                     # 测试代码
│   ├── unit/                  # 单元测试
│   ├── integration/           # 集成测试
│   └── conftest.py            # 测试配置
├── doc/                       # 文档
├── scripts/                   # 脚本文件
└── monitoring/                # 监控配置
```

## 🔧 配置

### 主配置文件

编辑 `config/slave_server.ini`:

```ini
[server]
host = 0.0.0.0
port = 8889
debug = false

[master]
server_url = http://your-master-server:8888
auth_token = your-auth-token
heartbeat_interval = 30

[virtualhere]
server_port = 7575
binary_path = ./vhusbd
auto_start = true

[logging]
log_level = INFO
log_file = logs/slave_server.log
```

### 环境变量

支持通过环境变量覆盖配置：

```bash
export SLAVE_SERVER_PORT=8889
export MASTER_SERVER_URL=http://master:8888
export LOG_LEVEL=DEBUG
```

## 📊 监控

### 内置监控

- **健康检查**: `GET /api/system/health`
- **系统状态**: `GET /api/system/status`
- **性能数据**: `GET /api/system/performance`
- **错误统计**: `GET /api/system/errors`

### 外部监控

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin123)

### 关键指标

- CPU使用率
- 内存使用率
- 磁盘使用率
- 网络流量
- USB设备数量
- API响应时间

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
python test_runner.py

# 运行单元测试
python test_runner.py --type unit

# 运行集成测试
python test_runner.py --type integration

# 生成覆盖率报告
python test_runner.py --report
```

### 测试覆盖率

- 目标覆盖率: 90%+
- 当前覆盖率: 85%+

## 🚀 部署

### Docker部署

```bash
# 生产环境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 开发环境
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

### 原生部署

```bash
# 使用安装脚本
sudo ./scripts/install.sh

# 启动服务
sudo systemctl start omnilink-slave
sudo systemctl enable omnilink-slave
```

## 🔒 安全

### API安全

- API密钥认证
- JWT令牌支持
- CORS配置
- 请求频率限制

### 系统安全

- 用户权限隔离
- 文件系统保护
- 网络访问控制
- 日志审计

## 🤝 贡献

我们欢迎社区贡献！请阅读 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

### 开发流程

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 代码规范

- 遵循 PEP 8 Python代码规范
- 使用 Black 进行代码格式化
- 编写单元测试和集成测试
- 更新相关文档

## 📝 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解版本更新历史。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

### 获取帮助

- 📖 [文档](doc/)
- 🐛 [问题反馈](https://github.com/your-org/omnilink-slave-server/issues)
- 💬 [讨论区](https://github.com/your-org/omnilink-slave-server/discussions)

### 联系方式

- 邮箱: <EMAIL>
- 官网: https://omnilink.example.com

## 🙏 致谢

感谢以下开源项目：

- [Flask](https://flask.palletsprojects.com/) - Web框架
- [Peewee](http://docs.peewee-orm.com/) - ORM框架
- [VirtualHere](https://www.virtualhere.com/) - USB网络共享
- [Prometheus](https://prometheus.io/) - 监控系统
- [Grafana](https://grafana.com/) - 可视化平台

---

<p align="center">
  Made with ❤️ by OmniLink Team
</p>
