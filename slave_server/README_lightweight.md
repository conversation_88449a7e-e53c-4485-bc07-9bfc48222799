# OmniLink轻量化从服务器

OmniLink轻量化从服务器是一个专注于USB设备代理的轻量级服务，基于VirtualHere技术实现USB设备的网络化访问。

## 核心特性

- **USB设备代理**: 专注于USB设备网络共享功能
- **VirtualHere集成**: 内置VirtualHere服务器端
- **主从通信**: 与主服务器保持心跳连接，实时上报设备状态
- **简易Web界面**: 提供基础的设备状态查看界面
- **轻量化设计**: 最小化资源占用，内存<256MB，CPU<0.5核心

## 系统要求

- Python 3.9+
- Linux系统（推荐Ubuntu 20.04+）
- USB设备访问权限
- 网络连接

## 快速部署

### 使用Docker部署（推荐）

1. 构建镜像：
```bash
docker build -t omnilink-slave-lightweight:2.0 .
```

2. 运行容器：
```bash
docker-compose up -d
```

### 直接运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 启动服务：
```bash
python main.py
```

## 配置说明

配置文件：`config/slave_server.ini`

```ini
[server]
host = 0.0.0.0
port = 8889

[virtualhere]
server_port = 7575
binary_path = /app/vhusbd

[logging]
log_level = INFO
log_file = logs/slave_server.log
```

## API接口

- `GET /api/system/health` - 健康检查
- `GET /api/devices` - 获取设备列表  
- `GET /api/system/status` - 系统状态
- `POST /api/virtualhere/start` - 启动VirtualHere
- `POST /api/virtualhere/stop` - 停止VirtualHere
- `GET /` - 简易Web界面

## 轻量化特点

### 资源占用
- **内存使用**: <256MB
- **CPU使用**: <0.5核心
- **镜像大小**: <200MB
- **依赖包数**: 仅6个核心依赖

### 功能专注
- 移除了复杂的监控系统（Prometheus、Grafana）
- 移除了缓存服务（Redis）
- 移除了日志收集服务（Fluentd）
- 专注于USB设备代理核心功能

### 架构简化
```
主服务器 <--HTTP--> 轻量化从服务器 <--USB--> USB设备
    |                    |
    |                    +-- VirtualHere服务器
    |                    +-- 设备监控线程
    |                    +-- 心跳上报线程
    |
    +-- 用户认证鉴权
    +-- 设备权限管理
    +-- 设备分组管理
```

## 项目结构

```
slave_server/
├── main.py              # 主程序（单文件架构）
├── config/              # 配置文件
│   └── slave_server.ini # 简化配置
├── virtualhere/         # VirtualHere二进制文件
├── Dockerfile           # 轻量化Docker镜像
├── docker-compose.yml   # 简化容器编排
└── requirements.txt     # 最小化依赖（6个包）
```

## 故障排除

### 常见问题

1. **VirtualHere启动失败**
   - 检查二进制文件路径：`/app/vhusbd`
   - 确认端口7575未被占用

2. **设备检测失败**
   - 安装pyusb依赖：`pip install pyusb`
   - 检查USB设备权限

3. **主服务器连接失败**
   - 检查环境变量：`MASTER_SERVER_URL`
   - 默认地址：`http://*************:8000`

### 日志查看

```bash
# 查看应用日志
tail -f logs/slave_server.log

# 查看容器日志
docker logs omnilink-slave-server
```

## 与原版本对比

| 特性 | 原版本 | 轻量化版本 |
|------|--------|------------|
| 容器数量 | 5个 | 1个 |
| 内存占用 | 2.8GB | <256MB |
| CPU占用 | 5.0核心 | <0.5核心 |
| 依赖包数 | 50+ | 6个 |
| 代码文件 | 60+ | 1个主文件 |
| 功能复杂度 | 企业级 | 专注核心 |

## 许可证

本项目采用MIT许可证。
