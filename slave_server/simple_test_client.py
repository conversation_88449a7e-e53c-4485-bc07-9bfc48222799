#!/usr/bin/env python3
"""
简化的从服务器测试客户端
用于测试主从服务器动态管理功能
"""

import sys
import time
import json
import uuid
import socket
import hashlib
import threading
from datetime import datetime
from urllib.request import urlopen, Request
from urllib.parse import urlencode
from urllib.error import URLError, HTTPError

class SimpleSlaveTestClient:
    def __init__(self, server_port, vh_port, master_url="http://172.17.176.1:8000"):
        self.server_port = server_port
        self.vh_port = vh_port
        self.master_url = master_url
        self.server_name = self._generate_unique_server_name()
        self.server_uuid = self._generate_server_uuid()
        self.running = False
        self.heartbeat_thread = None
        
    def _generate_unique_server_name(self):
        """生成唯一的服务器名称"""
        try:
            # 基于MAC地址生成唯一标识
            mac = uuid.getnode()
            mac_suffix = f"{mac:012x}"[-6:]

            # 基于主机名增加可识别性
            hostname = socket.gethostname()
            hostname_hash = hashlib.md5(hostname.encode()).hexdigest()[:4]

            # 基于端口号增加可识别性（关键差异点）
            port_suffix = self.server_port

            # 基于时间戳和随机数确保唯一性
            import random
            timestamp = int(time.time() * 1000) % 100000
            random_suffix = random.randint(10000, 99999)

            unique_name = f"OmniLink-Slave-{mac_suffix}-{hostname_hash}-{port_suffix}-{timestamp}-{random_suffix}"
            print(f"生成唯一服务器名称: {unique_name}")
            return unique_name

        except Exception as e:
            # 降级方案
            import random
            fallback_id = f"{self.server_port}-{int(time.time() % 10000)}-{random.randint(1000, 9999)}"
            fallback_name = f"OmniLink-Slave-{fallback_id}"
            print(f"名称生成失败，使用降级方案: {fallback_name}, 错误: {e}")
            return fallback_name
    
    def _generate_server_uuid(self):
        """生成服务器UUID"""
        import random
        hostname = socket.gethostname()
        mac = uuid.getnode()
        # 加入端口号和随机数确保每个实例有唯一UUID
        random_suffix = random.randint(10000, 99999)
        uuid_source = f"{hostname}-{mac}-{self.server_port}-{int(time.time())}-{random_suffix}"
        return hashlib.md5(uuid_source.encode()).hexdigest()
    
    def _get_local_ip(self):
        """获取本地IP地址"""
        try:
            # 连接到一个远程地址来获取本地IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "127.0.0.1"
    
    def register_to_master(self):
        """向主服务器注册"""
        try:
            ip_address = self._get_local_ip()
            
            data = {
                'server_name': self.server_name,
                'server_ip': ip_address,
                'server_port': self.server_port,
                'vh_port': self.vh_port,
                'hardware_uuid': self.server_uuid,
                'version': '2.0-test',
                'description': f'测试从服务器实例 - 端口{self.server_port}'
            }
            
            json_data = json.dumps(data).encode('utf-8')
            
            req = Request(
                f"{self.master_url}/api/v1/slave/register",
                data=json_data,
                headers={'Content-Type': 'application/json'}
            )
            
            with urlopen(req, timeout=10) as response:
                result = json.loads(response.read().decode('utf-8'))
                print(f"注册成功: {result}")
                return True
                
        except Exception as e:
            print(f"注册失败: {e}")
            return False
    
    def send_heartbeat(self):
        """发送心跳"""
        try:
            ip_address = self._get_local_ip()
            
            data = {
                'timestamp': datetime.now().isoformat(),
                'status': 'online',
                'device_count_summary': 5,
                'hub_count': 1,
                'total_ports': 8,
                'heartbeat_type': 'lightweight',
                'server_name': self.server_name,
                'ip_address': ip_address,
                'web_port': self.server_port,
                'virtualhere_status': 'running',
                'virtualhere_port': self.vh_port,
                'server_version': '2.0-test',
                'heartbeat_timestamp': datetime.now().isoformat(),
                'data_sync_status': 'idle'
            }
            
            json_data = json.dumps(data).encode('utf-8')
            
            req = Request(
                f"{self.master_url}/api/v1/slave/heartbeat",
                data=json_data,
                headers={'Content-Type': 'application/json'}
            )
            
            with urlopen(req, timeout=5) as response:
                result = json.loads(response.read().decode('utf-8'))
                print(f"心跳发送成功: {datetime.now().strftime('%H:%M:%S')}")
                return True
                
        except Exception as e:
            print(f"心跳发送失败: {e}")
            return False
    
    def heartbeat_loop(self):
        """心跳循环"""
        while self.running:
            self.send_heartbeat()
            time.sleep(30)  # 每30秒发送一次心跳
    
    def start(self):
        """启动测试客户端"""
        print(f"启动从服务器测试客户端: {self.server_name}")
        print(f"端口: {self.server_port}, VH端口: {self.vh_port}")
        print(f"主服务器: {self.master_url}")
        
        # 注册到主服务器
        if not self.register_to_master():
            print("注册失败，退出")
            return
        
        # 启动心跳线程
        self.running = True
        self.heartbeat_thread = threading.Thread(target=self.heartbeat_loop)
        self.heartbeat_thread.daemon = True
        self.heartbeat_thread.start()
        
        print("从服务器测试客户端已启动，按Ctrl+C停止")
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在停止...")
            self.stop()
    
    def stop(self):
        """停止测试客户端"""
        self.running = False
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=5)
        print("从服务器测试客户端已停止")

def main():
    if len(sys.argv) != 3:
        print("用法: python3 simple_test_client.py <server_port> <vh_port>")
        print("示例: python3 simple_test_client.py 8891 7576")
        sys.exit(1)
    
    server_port = int(sys.argv[1])
    vh_port = int(sys.argv[2])
    
    client = SimpleSlaveTestClient(server_port, vh_port)
    client.start()

if __name__ == "__main__":
    main()
