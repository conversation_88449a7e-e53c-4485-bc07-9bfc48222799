# OmniLink从服务器开发指南

## 概述

本指南为OmniLink从服务器项目的开发者提供详细的开发环境搭建、代码规范、开发流程等信息。

## 开发环境搭建

### 1. 系统要求

- **操作系统**: Linux (Ubuntu 20.04+) 或 macOS 10.15+
- **Python**: 3.11+
- **Git**: 2.0+
- **Docker**: 20.10+ (可选)
- **IDE**: VS Code, PyCharm 或其他Python IDE

### 2. 环境准备

#### 安装Python 3.11

```bash
# Ubuntu
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-dev

# macOS (使用Homebrew)
brew install python@3.11

# 验证安装
python3.11 --version
```

#### 安装开发工具

```bash
# 安装Git
sudo apt install git  # Ubuntu
brew install git      # macOS

# 安装Docker (可选)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装其他工具
sudo apt install curl wget build-essential
```

### 3. 项目设置

#### 克隆项目

```bash
git clone <repository-url> omnilink-slave-server
cd omnilink-slave-server
```

#### 创建虚拟环境

```bash
# 创建虚拟环境
python3.11 -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 升级pip
pip install --upgrade pip
```

#### 安装依赖

```bash
# 安装生产依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements-dev.txt

# 或者一次性安装所有依赖
pip install -r requirements.txt -r requirements-dev.txt
```

#### 配置开发环境

```bash
# 复制配置文件
cp config/slave_server.ini.template config/slave_server.ini
cp config/vhusbd.conf.template config/vhusbd.conf
cp .env.template .env

# 编辑配置文件
vim config/slave_server.ini
vim .env
```

### 4. IDE配置

#### VS Code配置

创建 `.vscode/settings.json`:

```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length=88"],
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"],
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".pytest_cache": true,
        "htmlcov": true
    }
}
```

创建 `.vscode/launch.json`:

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Flask",
            "type": "python",
            "request": "launch",
            "program": "main.py",
            "env": {
                "FLASK_ENV": "development",
                "FLASK_DEBUG": "1"
            },
            "console": "integratedTerminal",
            "justMyCode": true
        }
    ]
}
```

#### PyCharm配置

1. 打开项目目录
2. 配置Python解释器: `File > Settings > Project > Python Interpreter`
3. 选择虚拟环境中的Python: `./venv/bin/python`
4. 配置代码格式化: `File > Settings > Tools > External Tools`
5. 添加Black格式化工具

## 代码规范

### 1. Python代码规范

#### PEP 8规范

- 使用4个空格缩进
- 行长度限制为88字符
- 函数和类之间空两行
- 导入语句按标准库、第三方库、本地库分组

#### 命名规范

```python
# 变量和函数: snake_case
user_name = "admin"
def get_device_list():
    pass

# 类名: PascalCase
class DeviceManager:
    pass

# 常量: UPPER_SNAKE_CASE
MAX_RETRY_COUNT = 3
DEFAULT_TIMEOUT = 30

# 私有成员: 前缀下划线
class MyClass:
    def __init__(self):
        self._private_var = None
        self.__very_private = None
```

#### 类型注解

```python
from typing import List, Dict, Optional, Union

def process_devices(devices: List[Dict[str, str]]) -> Optional[bool]:
    """处理设备列表"""
    if not devices:
        return None
    
    for device in devices:
        device_id: str = device.get('id', '')
        if device_id:
            # 处理设备
            pass
    
    return True
```

#### 文档字符串

```python
def connect_device(device_uuid: str, user_info: Dict[str, str]) -> bool:
    """
    连接指定的USB设备
    
    Args:
        device_uuid: 设备UUID
        user_info: 用户信息字典，包含user_id、user_name等
    
    Returns:
        bool: 连接成功返回True，失败返回False
    
    Raises:
        DeviceNotFoundError: 设备不存在
        DeviceConnectError: 设备连接失败
    
    Example:
        >>> user_info = {'user_id': '123', 'user_name': 'admin'}
        >>> connect_device('device-001', user_info)
        True
    """
    pass
```

### 2. 代码质量工具

#### Black代码格式化

```bash
# 格式化单个文件
black main.py

# 格式化整个项目
black .

# 检查格式但不修改
black --check .
```

#### Flake8代码检查

```bash
# 检查代码质量
flake8 .

# 检查特定文件
flake8 main.py

# 生成报告
flake8 --format=html --htmldir=flake8-report .
```

#### isort导入排序

```bash
# 排序导入语句
isort .

# 检查导入顺序
isort --check-only .
```

### 3. Git提交规范

#### 提交消息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 提交类型

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例

```
feat(device): 添加设备批量操作功能

- 添加批量连接设备接口
- 添加批量断开设备接口
- 更新设备状态管理逻辑

Closes #123
```

## 开发流程

### 1. 功能开发流程

#### 创建功能分支

```bash
# 从main分支创建功能分支
git checkout main
git pull origin main
git checkout -b feature/device-batch-operations

# 或者从develop分支创建
git checkout develop
git pull origin develop
git checkout -b feature/device-batch-operations
```

#### 开发和测试

```bash
# 开发代码
# ... 编写代码 ...

# 运行测试
python test_runner.py --type unit
python test_runner.py --type integration

# 代码格式化
black .
isort .

# 代码检查
flake8 .

# 提交代码
git add .
git commit -m "feat(device): 添加设备批量操作功能"
```

#### 创建Pull Request

```bash
# 推送分支
git push origin feature/device-batch-operations

# 在GitHub/GitLab上创建Pull Request
# 填写PR描述，关联相关Issue
```

### 2. 测试驱动开发

#### 编写测试

```python
# tests/unit/test_device_batch.py
import pytest
from unittest.mock import Mock, patch
from services.device_service import DeviceService

class TestDeviceBatch:
    def test_batch_connect_devices(self):
        """测试批量连接设备"""
        device_service = DeviceService()
        device_uuids = ['device-1', 'device-2', 'device-3']
        user_info = {'user_id': '123', 'user_name': 'admin'}
        
        with patch.object(device_service, 'connect_device') as mock_connect:
            mock_connect.return_value = True
            
            result = device_service.batch_connect_devices(device_uuids, user_info)
            
            assert result['success_count'] == 3
            assert result['failed_count'] == 0
            assert mock_connect.call_count == 3
```

#### 运行测试

```bash
# 运行特定测试
pytest tests/unit/test_device_batch.py::TestDeviceBatch::test_batch_connect_devices

# 运行所有测试
pytest

# 生成覆盖率报告
pytest --cov=. --cov-report=html
```

### 3. 调试技巧

#### 使用pdb调试

```python
import pdb

def problematic_function():
    data = get_some_data()
    pdb.set_trace()  # 设置断点
    processed_data = process_data(data)
    return processed_data
```

#### 使用日志调试

```python
import logging

logger = logging.getLogger(__name__)

def debug_function():
    logger.debug("开始处理数据")
    data = get_data()
    logger.debug(f"获取到数据: {data}")
    
    result = process_data(data)
    logger.debug(f"处理结果: {result}")
    
    return result
```

#### 使用IDE调试器

在VS Code或PyCharm中设置断点，使用调试模式运行程序。

## 项目结构

### 1. 目录组织

```
omnilink-slave-server/
├── main.py                    # 应用入口
├── requirements.txt           # 生产依赖
├── requirements-dev.txt       # 开发依赖
├── setup.py                   # 包安装配置
├── Makefile                   # 构建脚本
├── .env.template              # 环境变量模板
├── .gitignore                 # Git忽略文件
├── .flake8                    # Flake8配置
├── pyproject.toml             # 项目配置
├── config/                    # 配置文件
├── db/                        # 数据库模块
├── restful/                   # REST API模块
├── utils/                     # 工具模块
├── tasks/                     # 后台任务
├── tests/                     # 测试代码
├── doc/                       # 文档
├── scripts/                   # 脚本文件
└── monitoring/                # 监控配置
```

### 2. 模块设计原则

#### 单一职责原则

每个模块只负责一个功能领域：

```python
# 好的设计
class DeviceManager:
    """专门负责设备管理"""
    def connect_device(self): pass
    def disconnect_device(self): pass
    def get_device_status(self): pass

class ConfigManager:
    """专门负责配置管理"""
    def load_config(self): pass
    def save_config(self): pass
    def validate_config(self): pass
```

#### 依赖注入

使用依赖注入减少耦合：

```python
class DeviceService:
    def __init__(self, device_dao, vh_manager, logger):
        self.device_dao = device_dao
        self.vh_manager = vh_manager
        self.logger = logger
    
    def connect_device(self, device_uuid):
        device = self.device_dao.get_device(device_uuid)
        result = self.vh_manager.connect(device)
        self.logger.info(f"设备连接结果: {result}")
        return result
```

### 3. 错误处理

#### 自定义异常

```python
# utils/exceptions.py
class OmniLinkError(Exception):
    """基础异常类"""
    pass

class DeviceNotFoundError(OmniLinkError):
    """设备不存在异常"""
    pass

class DeviceConnectError(OmniLinkError):
    """设备连接异常"""
    pass

class ConfigurationError(OmniLinkError):
    """配置错误异常"""
    pass
```

#### 异常处理模式

```python
from utils.exceptions import DeviceNotFoundError, DeviceConnectError

def connect_device(device_uuid):
    try:
        device = device_dao.get_device(device_uuid)
        if not device:
            raise DeviceNotFoundError(f"设备不存在: {device_uuid}")
        
        result = vh_manager.connect(device)
        if not result:
            raise DeviceConnectError(f"设备连接失败: {device_uuid}")
        
        return True
        
    except DeviceNotFoundError:
        logger.error(f"设备不存在: {device_uuid}")
        raise
    except DeviceConnectError:
        logger.error(f"设备连接失败: {device_uuid}")
        raise
    except Exception as e:
        logger.error(f"未知错误: {e}")
        raise OmniLinkError(f"连接设备时发生未知错误: {e}")
```

## 性能优化

### 1. 数据库优化

#### 使用连接池

```python
from peewee import SqliteDatabase
from playhouse.pool import PooledSqliteDatabase

# 使用连接池
database = PooledSqliteDatabase(
    'data/slave_server.db',
    max_connections=20,
    stale_timeout=300
)
```

#### 查询优化

```python
# 避免N+1查询
devices = Device.select().prefetch(DeviceConnection)

# 使用索引
class Device(Model):
    device_uuid = CharField(unique=True, index=True)
    vendor_id = CharField(index=True)
    product_id = CharField(index=True)
```

### 2. 缓存策略

#### Redis缓存

```python
import redis
import json
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expire_time=300):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            redis_client.setex(cache_key, expire_time, json.dumps(result))
            
            return result
        return wrapper
    return decorator

@cache_result(expire_time=600)
def get_device_statistics():
    # 耗时的统计查询
    return device_dao.get_statistics()
```

### 3. 异步处理

#### 后台任务

```python
import threading
from queue import Queue

class TaskQueue:
    def __init__(self):
        self.queue = Queue()
        self.worker_thread = threading.Thread(target=self._worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
    
    def add_task(self, func, *args, **kwargs):
        self.queue.put((func, args, kwargs))
    
    def _worker(self):
        while True:
            func, args, kwargs = self.queue.get()
            try:
                func(*args, **kwargs)
            except Exception as e:
                logger.error(f"后台任务执行失败: {e}")
            finally:
                self.queue.task_done()

# 使用示例
task_queue = TaskQueue()
task_queue.add_task(send_notification, "设备连接成功", user_id="123")
```

## 部署和发布

### 1. 本地开发服务器

```bash
# 开发模式启动
export FLASK_ENV=development
export FLASK_DEBUG=1
python main.py

# 或使用Flask命令
flask run --host=0.0.0.0 --port=8889 --debug
```

### 2. Docker开发环境

```bash
# 构建开发镜像
docker build -t omnilink-slave:dev .

# 运行开发容器
docker run -it --rm \
    -p 8889:8889 \
    -v $(pwd):/app \
    -e FLASK_ENV=development \
    omnilink-slave:dev
```

### 3. 生产部署

```bash
# 使用部署脚本
./deploy.sh --env production

# 或使用Docker Compose
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 故障排除

### 1. 常见问题

#### 依赖安装失败

```bash
# 清理pip缓存
pip cache purge

# 升级pip和setuptools
pip install --upgrade pip setuptools

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 数据库连接问题

```bash
# 检查数据库文件权限
ls -la data/slave_server.db

# 重新初始化数据库
rm data/slave_server.db
python -c "from db import init_database; init_database()"
```

#### VirtualHere启动失败

```bash
# 检查VirtualHere二进制文件
ls -la vhusbd
file vhusbd

# 检查USB权限
groups $USER | grep plugdev

# 添加用户到plugdev组
sudo usermod -a -G plugdev $USER
```

### 2. 调试技巧

#### 启用详细日志

```bash
export LOG_LEVEL=DEBUG
python main.py
```

#### 使用调试工具

```bash
# 使用pdb调试
python -m pdb main.py

# 使用ipdb (更好的调试器)
pip install ipdb
python -c "import ipdb; ipdb.set_trace(); import main"
```

## 贡献指南

### 1. 提交代码

1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 确保测试通过
5. 提交Pull Request

### 2. 代码审查

- 代码符合规范
- 测试覆盖率足够
- 文档更新完整
- 性能影响评估

### 3. 发布流程

1. 更新版本号
2. 更新CHANGELOG
3. 创建Release Tag
4. 构建和发布

## 更多资源

- [API文档](./API_Documentation.md)
- [架构文档](./Architecture.md)
- [部署指南](./Deployment_Guide.md)
- [测试文档](../tests/README.md)
