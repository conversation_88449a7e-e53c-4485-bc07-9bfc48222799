# OmniLink从服务器 API 文档

## 概述

OmniLink从服务器提供完整的RESTful API，用于设备管理、系统监控、配置管理等功能。所有API都返回JSON格式的响应。

## 基础信息

- **基础URL**: `http://localhost:8889/api`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 通用响应格式

所有API响应都遵循以下格式：

```json
{
  "status": true,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

### 错误响应格式

```json
{
  "status": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "details": {
    // 错误详细信息
  }
}
```

## API 端点分类

### 1. 设备管理 API (`/api/devices`)

#### 1.1 获取设备列表

**GET** `/api/devices`

获取所有USB设备列表。

**查询参数:**
- `status` (可选): 设备状态过滤 (`connected`, `disconnected`, `available`)
- `include_hidden` (可选): 是否包含隐藏设备 (默认: `false`)

**响应示例:**
```json
{
  "status": true,
  "message": "获取设备列表成功",
  "data": {
    "devices": [
      {
        "device_uuid": "usb-1234-5678",
        "device_address": "1-1.2",
        "vendor": "Example Corp",
        "product": "USB Device",
        "vendor_id": "1234",
        "product_id": "5678",
        "status": "available",
        "shown": true,
        "disable": false,
        "nick_name": "我的设备",
        "last_seen": "2025-01-27T10:30:00",
        "connected_user": null
      }
    ],
    "total_count": 1
  }
}
```

#### 1.2 获取设备详情

**GET** `/api/devices/{device_uuid}`

获取指定设备的详细信息。

**路径参数:**
- `device_uuid`: 设备UUID

**响应示例:**
```json
{
  "status": true,
  "message": "获取设备详情成功",
  "data": {
    "device_uuid": "usb-1234-5678",
    "device_address": "1-1.2",
    "vendor": "Example Corp",
    "product": "USB Device",
    "vendor_id": "1234",
    "product_id": "5678",
    "status": "available",
    "shown": true,
    "disable": false,
    "nick_name": "我的设备",
    "last_seen": "2025-01-27T10:30:00",
    "connected_user": null,
    "device_class": "03",
    "device_subclass": "01",
    "device_protocol": "02",
    "max_power": "100mA",
    "speed": "Full Speed",
    "configuration_count": 1,
    "interface_count": 2
  }
}
```

#### 1.3 连接设备

**POST** `/api/devices/{device_uuid}/connect`

连接指定的USB设备。

**请求体:**
```json
{
  "user_info": {
    "user_id": "user123",
    "user_name": "张三",
    "client_ip": "*************"
  },
  "force": false
}
```

**响应示例:**
```json
{
  "status": true,
  "message": "设备连接成功",
  "data": {
    "device_uuid": "usb-1234-5678",
    "connection_id": "conn-789",
    "connected_at": "2025-01-27T10:35:00",
    "user_info": {
      "user_id": "user123",
      "user_name": "张三",
      "client_ip": "*************"
    }
  }
}
```

#### 1.4 断开设备

**POST** `/api/devices/{device_uuid}/disconnect`

断开指定的USB设备连接。

**请求体:**
```json
{
  "user_info": {
    "user_id": "user123",
    "user_name": "张三"
  },
  "force": false
}
```

#### 1.5 重置设备

**POST** `/api/devices/{device_uuid}/reset`

重置指定的USB设备。

#### 1.6 设置设备昵称

**PUT** `/api/devices/{device_uuid}/nickname`

设置设备的显示昵称。

**请求体:**
```json
{
  "nick_name": "我的新设备名称"
}
```

#### 1.7 设置设备可见性

**PUT** `/api/devices/{device_uuid}/visibility`

设置设备的可见性状态。

**请求体:**
```json
{
  "shown": true
}
```

#### 1.8 设置设备启用状态

**PUT** `/api/devices/{device_uuid}/enabled`

设置设备的启用状态。

**请求体:**
```json
{
  "enabled": true
}
```

#### 1.9 获取设备统计

**GET** `/api/devices/statistics`

获取设备统计信息。

**响应示例:**
```json
{
  "status": true,
  "message": "获取设备统计成功",
  "data": {
    "total_devices": 10,
    "available_devices": 8,
    "connected_devices": 2,
    "hidden_devices": 1,
    "disabled_devices": 0,
    "device_types": {
      "HID": 5,
      "Storage": 3,
      "Audio": 1,
      "Other": 1
    }
  }
}
```

### 2. 服务器管理 API (`/api/server`)

#### 2.1 获取服务器状态

**GET** `/api/server/status`

获取VirtualHere服务器运行状态。

**响应示例:**
```json
{
  "status": true,
  "message": "获取服务器状态成功",
  "data": {
    "running": true,
    "pid": 1234,
    "port": 7575,
    "port_status": "listening",
    "uptime": 3600,
    "version": "4.5.4",
    "client_count": 2,
    "device_count": 10,
    "process_info": {
      "cpu_percent": 2.5,
      "memory_mb": 45.2,
      "threads": 8
    }
  }
}
```

#### 2.2 启动服务器

**POST** `/api/server/start`

启动VirtualHere服务器。

#### 2.3 停止服务器

**POST** `/api/server/stop`

停止VirtualHere服务器。

#### 2.4 重启服务器

**POST** `/api/server/restart`

重启VirtualHere服务器。

#### 2.5 获取服务器配置

**GET** `/api/server/config`

获取VirtualHere服务器配置。

#### 2.6 更新服务器配置

**PUT** `/api/server/config`

更新VirtualHere服务器配置。

**请求体:**
```json
{
  "ServerName": "OmniLink从服务器",
  "Port": 7575,
  "MaxClients": 10,
  "LogLevel": 2,
  "AutoShare": true
}
```

### 3. 系统管理 API (`/api/system`)

#### 3.1 获取系统状态

**GET** `/api/system/status`

获取系统整体状态信息。

**响应示例:**
```json
{
  "status": true,
  "message": "获取系统状态成功",
  "data": {
    "system_info": {
      "hostname": "slave-server-01",
      "platform": "Linux",
      "architecture": "x86_64",
      "python_version": "3.11.0",
      "uptime": 86400
    },
    "resource_usage": {
      "cpu_percent": 15.2,
      "memory_percent": 45.8,
      "disk_percent": 32.1,
      "network_io": {
        "bytes_sent": 1048576,
        "bytes_recv": 2097152
      }
    },
    "service_status": {
      "virtualhere_server": "running",
      "device_monitor": "running",
      "heartbeat_manager": "running",
      "command_processor": "running"
    }
  }
}
```

#### 3.2 健康检查

**GET** `/api/system/health`

系统健康检查端点。

**响应示例:**
```json
{
  "status": true,
  "message": "系统健康",
  "data": {
    "overall_status": "healthy",
    "checks": {
      "virtualhere_server": "healthy",
      "database": "healthy",
      "master_communication": "healthy",
      "disk_space": "healthy"
    },
    "timestamp": "2025-01-27T10:40:00"
  }
}
```

#### 3.3 重启系统

**POST** `/api/system/restart`

重启整个系统服务。

### 4. 配置管理 API (`/api/config`)

#### 4.1 获取所有配置

**GET** `/api/config`

获取所有配置信息。

#### 4.2 获取系统配置

**GET** `/api/config/system`

获取系统配置。

#### 4.3 获取配置节

**GET** `/api/config/system/{section}`

获取指定配置节的内容。

**路径参数:**
- `section`: 配置节名称 (`server`, `database`, `virtualhere`, `logging`, `master`)

#### 4.4 获取配置值

**GET** `/api/config/system/{section}/{key}`

获取指定配置项的值。

#### 4.5 设置配置值

**PUT** `/api/config/system/{section}/{key}`

设置指定配置项的值。

**请求体:**
```json
{
  "value": "新的配置值"
}
```

#### 4.6 获取环境变量配置

**GET** `/api/config/env`

获取环境变量配置信息。

#### 4.7 验证环境变量配置

**GET** `/api/config/env/validate`

验证环境变量配置的有效性。

#### 4.8 导出环境变量模板

**GET** `/api/config/env/template`

导出环境变量配置模板。

**查询参数:**
- `format`: 返回格式 (`json`, `text`)

#### 4.9 获取有效配置

**GET** `/api/config/effective`

获取有效配置（文件配置 + 环境变量覆盖）。

#### 4.10 重新加载配置

**POST** `/api/config/reload`

重新加载配置文件。

#### 4.11 备份配置

**POST** `/api/config/backup`

备份当前配置。

### 5. 性能监控 API (`/api/system/performance`)

#### 5.1 获取当前性能数据

**GET** `/api/system/performance?type=current`

获取当前系统性能数据。

**响应示例:**
```json
{
  "status": true,
  "message": "获取current性能数据成功",
  "data": {
    "cpu": {
      "cpu_percent": 15.2,
      "cpu_count": 8,
      "cpu_freq_current": 2400.0,
      "cpu_per_core": [12.5, 18.3, 14.1, 16.8, 13.2, 19.5, 11.7, 15.9]
    },
    "memory": {
      "virtual": {
        "total": 8589934592,
        "used": 3932160000,
        "percent": 45.8,
        "available": 4657774592
      },
      "swap": {
        "total": 2147483648,
        "used": 0,
        "percent": 0.0
      }
    },
    "disk": {
      "usage": {
        "total": 107374182400,
        "used": 34495119360,
        "percent": 32.1
      },
      "io": {
        "read_bytes": 1048576000,
        "write_bytes": 524288000
      }
    },
    "network": {
      "rates": {
        "bytes_sent_per_sec": 1024.5,
        "bytes_recv_per_sec": 2048.3
      }
    },
    "timestamp": 1706342400.123
  }
}
```

#### 5.2 获取性能历史数据

**GET** `/api/system/performance?type=history&duration=60`

获取指定时长的性能历史数据。

**查询参数:**
- `duration`: 历史数据时长（分钟，默认60）

#### 5.3 获取性能摘要

**GET** `/api/system/performance?type=summary&duration=60`

获取性能统计摘要。

#### 5.4 获取性能监控统计

**GET** `/api/system/performance/statistics`

获取性能监控统计信息。

#### 5.5 控制性能监控器

**POST** `/api/system/performance/control`

控制性能监控器的启动、停止等操作。

**请求体:**
```json
{
  "action": "start",
  "sample_interval": 5
}
```

**可用操作:**
- `start`: 启动性能监控
- `stop`: 停止性能监控
- `clear`: 清除历史数据
- `config`: 更新配置

### 6. 自诊断 API (`/api/system/diagnostic`)

#### 6.1 运行完整诊断

**GET** `/api/system/diagnostic?type=full`

运行完整的系统诊断。

**查询参数:**
- `format`: 返回格式 (`json`, `text`)

**响应示例:**
```json
{
  "status": true,
  "message": "full诊断完成",
  "data": {
    "timestamp": "2025-01-27T10:45:00",
    "diagnostic_version": "1.0.0",
    "system_info": {
      "hostname": "slave-server-01",
      "platform": "Linux",
      "architecture": "x86_64"
    },
    "results": {
      "system_info": {
        "status": "pass",
        "message": "系统信息正常"
      },
      "virtualhere_server": {
        "status": "pass",
        "message": "VirtualHere服务器运行正常"
      },
      "database": {
        "status": "pass",
        "message": "数据库连接正常，设备数量: 10"
      }
    },
    "summary": {
      "total_checks": 10,
      "passed_checks": 9,
      "failed_checks": 0,
      "warning_checks": 1,
      "overall_status": "warning"
    },
    "recommendations": [
      "网络错误频率较高，建议检查网络连接稳定性"
    ]
  }
}
```

#### 6.2 运行特定诊断

**GET** `/api/system/diagnostic?type=specific&item=virtualhere_server`

运行特定的诊断项目。

**查询参数:**
- `item`: 诊断项目名称

#### 6.3 获取诊断项目列表

**GET** `/api/system/diagnostic/items`

获取可用的诊断项目列表。

### 7. 错误管理 API (`/api/system/errors`)

#### 7.1 获取错误统计

**GET** `/api/system/errors?type=statistics`

获取错误统计信息。

**响应示例:**
```json
{
  "status": true,
  "message": "获取错误statistics成功",
  "data": {
    "total_errors": 25,
    "recent_errors_1h": 3,
    "recent_errors_24h": 15,
    "category_distribution": {
      "network": 8,
      "database": 2,
      "virtualhere": 1,
      "system": 5,
      "unknown": 9
    },
    "severity_distribution": {
      "low": 10,
      "medium": 12,
      "high": 3,
      "critical": 0
    },
    "top_error_types": [
      ["ConnectionError", 8],
      ["TimeoutError", 5],
      ["OSError", 3]
    ],
    "error_rate_1h": 0.05,
    "error_rate_24h": 0.01
  }
}
```

#### 7.2 获取错误历史

**GET** `/api/system/errors?type=history&limit=100`

获取错误历史记录。

**查询参数:**
- `limit`: 返回数量限制（默认100）
- `category`: 错误类别过滤
- `severity`: 严重程度过滤

#### 7.3 获取错误模式分析

**GET** `/api/system/errors?type=patterns`

获取错误模式分析结果。

#### 7.4 清理错误历史

**POST** `/api/system/errors/clear`

清理错误历史记录。

**请求体:**
```json
{
  "older_than_hours": 24
}
```

#### 7.5 获取错误分类信息

**GET** `/api/system/errors/categories`

获取错误类别和严重程度列表。

### 8. 日志管理 API (`/api/system/logs`)

#### 8.1 获取日志信息

**GET** `/api/system/logs?type=info`

获取日志记录器信息。

#### 8.2 获取日志内容

**GET** `/api/system/logs?type=content&logger_name=slave_server&lines=100`

获取指定日志文件的内容。

**查询参数:**
- `logger_name`: 日志记录器名称
- `lines`: 日志行数（默认100）
- `level`: 日志级别过滤

#### 8.3 更新日志配置

**POST** `/api/system/logs/config`

更新日志配置。

**请求体:**
```json
{
  "logger_name": "slave_server",
  "level": "DEBUG",
  "format": "structured",
  "max_bytes": 20971520,
  "backup_count": 10
}
```

#### 8.4 手动轮转日志

**POST** `/api/system/logs/rotate`

手动轮转日志文件。

**请求体:**
```json
{
  "logger_name": "slave_server"
}
```

## 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|----------|------------|------|
| DEVICE_NOT_FOUND | 404 | 设备不存在 |
| DEVICE_BUSY | 409 | 设备正在使用中 |
| DEVICE_DISABLED | 403 | 设备已被禁用 |
| SERVER_NOT_RUNNING | 503 | VirtualHere服务器未运行 |
| CONFIG_INVALID | 400 | 配置参数无效 |
| PERMISSION_DENIED | 403 | 权限不足 |
| INTERNAL_ERROR | 500 | 内部服务器错误 |

## 认证

目前API支持以下认证方式：

### API密钥认证

在请求头中添加API密钥：

```http
X-API-Key: your-api-key-here
```

### JWT认证

在请求头中添加JWT令牌：

```http
Authorization: Bearer your-jwt-token-here
```

## 限流

API请求受到频率限制，默认限制为每小时100次请求。超出限制时会返回429状态码。

## 示例代码

### Python示例

```python
import requests

# 基础配置
base_url = "http://localhost:8889/api"
headers = {
    "Content-Type": "application/json",
    "X-API-Key": "your-api-key"
}

# 获取设备列表
response = requests.get(f"{base_url}/devices", headers=headers)
devices = response.json()

# 连接设备
device_uuid = "usb-1234-5678"
connect_data = {
    "user_info": {
        "user_id": "user123",
        "user_name": "张三",
        "client_ip": "*************"
    }
}
response = requests.post(
    f"{base_url}/devices/{device_uuid}/connect",
    json=connect_data,
    headers=headers
)
```

### JavaScript示例

```javascript
const baseUrl = 'http://localhost:8889/api';
const headers = {
    'Content-Type': 'application/json',
    'X-API-Key': 'your-api-key'
};

// 获取系统状态
async function getSystemStatus() {
    const response = await fetch(`${baseUrl}/system/status`, {
        headers: headers
    });
    const data = await response.json();
    return data;
}

// 连接设备
async function connectDevice(deviceUuid, userInfo) {
    const response = await fetch(`${baseUrl}/devices/${deviceUuid}/connect`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({ user_info: userInfo })
    });
    const data = await response.json();
    return data;
}
```

### curl示例

```bash
# 获取设备列表
curl -X GET "http://localhost:8889/api/devices" \
  -H "X-API-Key: your-api-key"

# 连接设备
curl -X POST "http://localhost:8889/api/devices/usb-1234-5678/connect" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "user_info": {
      "user_id": "user123",
      "user_name": "张三",
      "client_ip": "*************"
    }
  }'

# 获取系统健康状态
curl -X GET "http://localhost:8889/api/system/health" \
  -H "X-API-Key: your-api-key"
```

## 更新日志

### v1.0.0 (2025-01-27)
- 初始API版本发布
- 支持设备管理、系统监控、配置管理等核心功能
- 完整的错误处理和日志管理
- 性能监控和自诊断功能
