# OmniLink从服务器部署指南

## 概述

本指南详细说明了如何在不同环境中部署OmniLink从服务器，包括Docker容器化部署和原生部署两种方式。

## 系统要求

### 最低要求

- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+) 或 macOS 10.15+
- **CPU**: 2核心
- **内存**: 2GB RAM
- **存储**: 10GB 可用空间
- **网络**: 千兆以太网连接

### 推荐配置

- **操作系统**: Ubuntu 22.04 LTS
- **CPU**: 4核心
- **内存**: 4GB RAM
- **存储**: 50GB SSD
- **网络**: 千兆以太网连接

### 软件依赖

#### Docker部署
- Docker 20.10+
- Docker Compose 2.0+
- Git 2.0+
- curl

#### 原生部署
- Python 3.11+
- pip 21.0+
- Git 2.0+
- curl

## 快速开始

### 1. 获取代码

```bash
# 克隆仓库
git clone <repository-url> omnilink-slave-server
cd omnilink-slave-server

# 切换到稳定版本（可选）
git checkout v1.0.0
```

### 2. 使用部署脚本

```bash
# 赋予执行权限
chmod +x deploy.sh

# 使用默认配置部署（Docker + 生产环境）
./deploy.sh

# 部署到开发环境
./deploy.sh --env development

# 使用原生模式部署
./deploy.sh --mode native
```

### 3. 验证部署

```bash
# 检查服务状态
curl http://localhost:8889/api/system/health

# 查看服务信息
curl http://localhost:8889/api/system/status
```

## Docker部署

### 1. 环境准备

```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp config/docker.env.example .env

# 编辑环境变量
vim .env
```

**重要配置项**:
```bash
# 服务器配置
SLAVE_SERVER_HOST=0.0.0.0
SLAVE_SERVER_PORT=8889

# 主服务器配置
MASTER_SERVER_URL=http://your-master-server:8888
MASTER_AUTH_TOKEN=your-auth-token

# VirtualHere配置
VH_SERVER_PORT=7575

# 安全配置
API_KEY=your-secure-api-key
```

### 3. 启动服务

```bash
# 生产环境
docker-compose up -d

# 开发环境
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# 查看日志
docker-compose logs -f slave-server
```

### 4. 服务管理

```bash
# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 更新服务
docker-compose pull
docker-compose up -d

# 查看状态
docker-compose ps
```

## 原生部署

### 1. 环境准备

```bash
# 安装Python 3.11
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-dev

# 创建项目目录
sudo mkdir -p /opt/omnilink
sudo chown $USER:$USER /opt/omnilink
cd /opt/omnilink

# 克隆代码
git clone <repository-url> .
```

### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python3.11 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置服务

```bash
# 复制配置文件
cp config/slave_server.ini.template config/slave_server.ini

# 编辑配置
vim config/slave_server.ini
```

### 4. 下载VirtualHere

```bash
# 下载VirtualHere服务器
curl -o vhusbd https://www.virtualhere.com/sites/default/files/usbserver/vhusbd
chmod +x vhusbd

# 复制VirtualHere配置
cp config/vhusbd.conf.template config/vhusbd.conf
```

### 5. 创建系统服务

```bash
# 创建systemd服务文件
sudo tee /etc/systemd/system/omnilink-slave.service > /dev/null << EOF
[Unit]
Description=OmniLink Slave Server
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/opt/omnilink
Environment=PATH=/opt/omnilink/venv/bin
ExecStart=/opt/omnilink/venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable omnilink-slave
sudo systemctl start omnilink-slave

# 查看状态
sudo systemctl status omnilink-slave
```

## 配置管理

### 1. 主配置文件

编辑 `config/slave_server.ini`:

```ini
[server]
host = 0.0.0.0
port = 8889
debug = false

[master]
server_url = http://your-master-server:8888
auth_token = your-auth-token
heartbeat_interval = 30

[virtualhere]
server_port = 7575
binary_path = ./vhusbd
auto_start = true

[logging]
log_level = INFO
log_file = logs/slave_server.log
```

### 2. VirtualHere配置

编辑 `config/vhusbd.conf`:

```
ServerName=OmniLink从服务器
Port=7575
MaxClients=10
LogLevel=2
AutoShare=true
```

### 3. 环境变量配置

对于Docker部署，编辑 `.env` 文件：

```bash
# 覆盖配置文件中的设置
SLAVE_SERVER_PORT=8889
MASTER_SERVER_URL=http://master:8888
LOG_LEVEL=INFO
```

## 网络配置

### 1. 端口配置

确保以下端口可访问：

- **8889**: Web API端口
- **7575**: VirtualHere服务器端口
- **9100**: 监控指标端口（可选）

### 2. 防火墙配置

```bash
# Ubuntu/Debian
sudo ufw allow 8889
sudo ufw allow 7575

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8889/tcp
sudo firewall-cmd --permanent --add-port=7575/tcp
sudo firewall-cmd --reload
```

### 3. 反向代理配置

#### Nginx配置示例

```nginx
server {
    listen 80;
    server_name slave-server.example.com;
    
    location / {
        proxy_pass http://localhost:8889;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 监控和日志

### 1. 日志管理

```bash
# 查看应用日志
tail -f logs/slave_server.log

# 查看VirtualHere日志
tail -f logs/vhusbd.log

# Docker环境查看日志
docker-compose logs -f slave-server
```

### 2. 监控配置

访问监控面板：

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin123)

### 3. 健康检查

```bash
# 系统健康检查
curl http://localhost:8889/api/system/health

# 详细状态信息
curl http://localhost:8889/api/system/status

# 性能监控
curl http://localhost:8889/api/system/performance?type=current
```

## 备份和恢复

### 1. 自动备份

部署脚本会自动创建备份：

```bash
# 查看备份
ls -la backups/

# 手动创建备份
./deploy.sh --backup
```

### 2. 数据备份

```bash
# 备份重要数据
tar -czf backup_$(date +%Y%m%d).tar.gz \
    config/ data/ logs/

# 恢复数据
tar -xzf backup_20250127.tar.gz
```

### 3. 数据库备份

```bash
# 备份SQLite数据库
cp data/slave_server.db data/slave_server.db.backup

# 定期备份脚本
echo "0 2 * * * cp /opt/omnilink/data/slave_server.db /opt/omnilink/data/slave_server.db.$(date +\%Y\%m\%d)" | crontab -
```

## 故障排除

### 1. 常见问题

#### 服务无法启动

```bash
# 检查端口占用
netstat -tlnp | grep 8889

# 检查配置文件
python -c "import configparser; c=configparser.ConfigParser(); c.read('config/slave_server.ini'); print('配置文件正确')"

# 检查权限
ls -la config/ data/ logs/
```

#### VirtualHere连接失败

```bash
# 检查VirtualHere进程
ps aux | grep vhusbd

# 检查USB设备
lsusb

# 检查权限
groups $USER | grep -q plugdev || echo "用户需要加入plugdev组"
```

#### 网络连接问题

```bash
# 测试主服务器连接
curl -v http://master-server:8888/api/health

# 检查DNS解析
nslookup master-server

# 检查防火墙
sudo iptables -L
```

### 2. 日志分析

```bash
# 查看错误日志
grep -i error logs/slave_server.log

# 查看连接日志
grep -i connect logs/slave_server.log

# 实时监控日志
tail -f logs/slave_server.log | grep -i error
```

### 3. 性能问题

```bash
# 检查系统资源
top
htop
iotop

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

## 安全配置

### 1. API安全

```bash
# 生成安全的API密钥
openssl rand -hex 32

# 配置API认证
echo "API_KEY=your-secure-key" >> .env
```

### 2. 网络安全

```bash
# 限制访问IP
iptables -A INPUT -p tcp --dport 8889 -s ***********/24 -j ACCEPT
iptables -A INPUT -p tcp --dport 8889 -j DROP
```

### 3. SSL/TLS配置

```bash
# 生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# 配置HTTPS
echo "SSL_ENABLED=true" >> .env
echo "SSL_CERT_FILE=cert.pem" >> .env
echo "SSL_KEY_FILE=key.pem" >> .env
```

## 升级和维护

### 1. 版本升级

```bash
# 备份当前版本
./deploy.sh --backup

# 拉取最新代码
git pull origin main

# 重新部署
./deploy.sh
```

### 2. 依赖更新

```bash
# Docker环境
docker-compose pull
docker-compose up -d

# 原生环境
source venv/bin/activate
pip install --upgrade -r requirements.txt
```

### 3. 定期维护

```bash
# 清理旧日志
find logs/ -name "*.log.*" -mtime +30 -delete

# 清理旧备份
find backups/ -name "*.tar.gz" -mtime +7 -delete

# 数据库优化
sqlite3 data/slave_server.db "VACUUM;"
```

## 生产环境最佳实践

### 1. 高可用配置

- 使用负载均衡器
- 配置多个从服务器实例
- 实施健康检查和自动故障转移

### 2. 监控告警

- 配置Prometheus告警规则
- 设置邮件/短信通知
- 监控关键指标

### 3. 安全加固

- 定期更新系统和依赖
- 使用防火墙限制访问
- 启用日志审计

### 4. 性能优化

- 调整工作进程数量
- 优化数据库连接池
- 配置缓存策略

## 更多资源

- [API文档](./API_Documentation.md)
- [配置文件说明](../config/README.md)
- [故障排除指南](./Troubleshooting.md)
- [架构文档](./Architecture.md)
