# OmniLink从服务器 API 使用指南

## 快速开始

本指南将帮助您快速上手OmniLink从服务器的API使用。

### 前置条件

1. OmniLink从服务器已正常运行
2. 已获取API访问权限（API密钥或JWT令牌）
3. 网络连接正常

### 基础配置

```python
import requests
import json

# API基础配置
API_BASE_URL = "http://localhost:8889/api"
API_KEY = "your-api-key-here"

# 通用请求头
HEADERS = {
    "Content-Type": "application/json",
    "X-API-Key": API_KEY
}

def make_request(method, endpoint, data=None):
    """通用API请求函数"""
    url = f"{API_BASE_URL}{endpoint}"
    
    if method.upper() == "GET":
        response = requests.get(url, headers=HEADERS, params=data)
    elif method.upper() == "POST":
        response = requests.post(url, headers=HEADERS, json=data)
    elif method.upper() == "PUT":
        response = requests.put(url, headers=HEADERS, json=data)
    elif method.upper() == "DELETE":
        response = requests.delete(url, headers=HEADERS)
    
    return response.json()
```

## 常用场景示例

### 1. 设备管理场景

#### 场景1：获取并显示所有可用设备

```python
def list_available_devices():
    """获取所有可用设备"""
    response = make_request("GET", "/devices", {"status": "available"})
    
    if response["status"]:
        devices = response["data"]["devices"]
        print(f"找到 {len(devices)} 个可用设备:")
        
        for device in devices:
            print(f"- {device['vendor']} {device['product']}")
            print(f"  UUID: {device['device_uuid']}")
            print(f"  地址: {device['device_address']}")
            print(f"  状态: {device['status']}")
            print()
    else:
        print(f"获取设备列表失败: {response['message']}")

# 使用示例
list_available_devices()
```

#### 场景2：连接特定设备

```python
def connect_device(device_uuid, user_info):
    """连接指定设备"""
    data = {
        "user_info": user_info,
        "force": False
    }
    
    response = make_request("POST", f"/devices/{device_uuid}/connect", data)
    
    if response["status"]:
        print(f"设备连接成功!")
        print(f"连接ID: {response['data']['connection_id']}")
        print(f"连接时间: {response['data']['connected_at']}")
    else:
        print(f"设备连接失败: {response['message']}")
    
    return response["status"]

# 使用示例
user_info = {
    "user_id": "user123",
    "user_name": "张三",
    "client_ip": "*************"
}

device_uuid = "usb-1234-5678"
connect_device(device_uuid, user_info)
```

#### 场景3：批量管理设备

```python
def batch_manage_devices():
    """批量管理设备示例"""
    # 1. 获取所有设备
    response = make_request("GET", "/devices")
    if not response["status"]:
        print("获取设备列表失败")
        return
    
    devices = response["data"]["devices"]
    
    # 2. 批量设置设备昵称
    for i, device in enumerate(devices):
        new_name = f"设备-{i+1:02d}"
        make_request("PUT", f"/devices/{device['device_uuid']}/nickname", 
                    {"nick_name": new_name})
        print(f"设备 {device['device_uuid']} 昵称已更新为: {new_name}")
    
    # 3. 隐藏特定类型的设备
    for device in devices:
        if device.get("device_class") == "09":  # USB Hub
            make_request("PUT", f"/devices/{device['device_uuid']}/visibility", 
                        {"shown": False})
            print(f"已隐藏USB Hub设备: {device['device_uuid']}")

# 使用示例
batch_manage_devices()
```

### 2. 系统监控场景

#### 场景1：实时监控系统状态

```python
import time

def monitor_system_status(duration=300, interval=30):
    """实时监控系统状态"""
    print(f"开始监控系统状态，持续 {duration} 秒，间隔 {interval} 秒")
    
    start_time = time.time()
    
    while time.time() - start_time < duration:
        # 获取系统状态
        response = make_request("GET", "/system/status")
        
        if response["status"]:
            data = response["data"]
            resource = data["resource_usage"]
            
            print(f"\n[{time.strftime('%H:%M:%S')}] 系统状态:")
            print(f"  CPU使用率: {resource['cpu_percent']:.1f}%")
            print(f"  内存使用率: {resource['memory_percent']:.1f}%")
            print(f"  磁盘使用率: {resource['disk_percent']:.1f}%")
            
            # 检查告警阈值
            if resource['cpu_percent'] > 80:
                print("  ⚠️  CPU使用率过高!")
            if resource['memory_percent'] > 80:
                print("  ⚠️  内存使用率过高!")
            if resource['disk_percent'] > 85:
                print("  ⚠️  磁盘使用率过高!")
        
        time.sleep(interval)

# 使用示例
monitor_system_status(duration=300, interval=30)
```

#### 场景2：性能数据分析

```python
def analyze_performance():
    """分析系统性能数据"""
    # 获取性能摘要
    response = make_request("GET", "/system/performance", 
                           {"type": "summary", "duration": 60})
    
    if response["status"]:
        data = response["data"]
        
        print("=== 性能分析报告 ===")
        print(f"采样时长: {data['duration_minutes']} 分钟")
        print(f"采样数量: {data['sample_count']} 个")
        
        # CPU分析
        cpu = data["cpu"]
        print(f"\nCPU性能:")
        print(f"  平均使用率: {cpu['avg']:.1f}%")
        print(f"  最高使用率: {cpu['max']:.1f}%")
        print(f"  最低使用率: {cpu['min']:.1f}%")
        
        # 内存分析
        memory = data["memory"]
        print(f"\n内存性能:")
        print(f"  平均使用率: {memory['avg']:.1f}%")
        print(f"  最高使用率: {memory['max']:.1f}%")
        
        # 网络分析
        if "network" in data:
            network = data["network"]
            print(f"\n网络性能:")
            print(f"  平均发送速度: {network['sent']['avg_mbps']:.2f} MB/s")
            print(f"  平均接收速度: {network['recv']['avg_mbps']:.2f} MB/s")
            print(f"  总发送数据: {network['sent']['total_mb']:.2f} MB")
            print(f"  总接收数据: {network['recv']['total_mb']:.2f} MB")

# 使用示例
analyze_performance()
```

### 3. 配置管理场景

#### 场景1：动态配置更新

```python
def update_configuration():
    """动态更新配置示例"""
    
    # 1. 获取当前配置
    response = make_request("GET", "/config/system/logging")
    if response["status"]:
        current_config = response["data"]
        print("当前日志配置:", current_config)
    
    # 2. 更新日志级别
    new_level = "DEBUG"
    response = make_request("PUT", "/config/system/logging/log_level", 
                           {"value": new_level})
    
    if response["status"]:
        print(f"日志级别已更新为: {new_level}")
    
    # 3. 更新VirtualHere配置
    vh_config = {
        "MaxClients": 20,
        "LogLevel": 3,
        "AutoShare": True
    }
    
    response = make_request("PUT", "/server/config", vh_config)
    if response["status"]:
        print("VirtualHere配置已更新")
    
    # 4. 重新加载配置
    response = make_request("POST", "/config/reload")
    if response["status"]:
        print("配置已重新加载")

# 使用示例
update_configuration()
```

#### 场景2：配置备份和恢复

```python
def backup_and_restore_config():
    """配置备份和恢复示例"""
    
    # 1. 备份当前配置
    response = make_request("POST", "/config/backup")
    if response["status"]:
        backup_file = response["data"]["backup_file"]
        print(f"配置已备份到: {backup_file}")
    
    # 2. 获取有效配置（用于验证）
    response = make_request("GET", "/config/effective")
    if response["status"]:
        effective_config = response["data"]
        print("有效配置获取成功")
        
        # 保存配置到本地文件
        with open("config_backup.json", "w", encoding="utf-8") as f:
            json.dump(effective_config, f, indent=2, ensure_ascii=False)
        print("配置已保存到本地文件: config_backup.json")

# 使用示例
backup_and_restore_config()
```

### 4. 错误处理和诊断场景

#### 场景1：系统健康检查

```python
def health_check():
    """系统健康检查"""
    
    # 1. 基础健康检查
    response = make_request("GET", "/system/health")
    
    if response["status"]:
        health_data = response["data"]
        overall_status = health_data["overall_status"]
        
        print(f"系统整体状态: {overall_status}")
        
        if overall_status != "healthy":
            print("\n检查详情:")
            for check_name, status in health_data["checks"].items():
                if status != "healthy":
                    print(f"  ❌ {check_name}: {status}")
                else:
                    print(f"  ✅ {check_name}: {status}")
    
    # 2. 运行完整诊断
    response = make_request("GET", "/system/diagnostic", {"type": "full"})
    
    if response["status"]:
        diagnostic_data = response["data"]
        summary = diagnostic_data["summary"]
        
        print(f"\n诊断结果:")
        print(f"  总检查项: {summary['total_checks']}")
        print(f"  通过: {summary['passed_checks']}")
        print(f"  警告: {summary['warning_checks']}")
        print(f"  失败: {summary['failed_checks']}")
        
        if diagnostic_data.get("recommendations"):
            print("\n建议:")
            for rec in diagnostic_data["recommendations"]:
                print(f"  • {rec}")

# 使用示例
health_check()
```

#### 场景2：错误分析和处理

```python
def analyze_errors():
    """错误分析和处理"""
    
    # 1. 获取错误统计
    response = make_request("GET", "/system/errors", {"type": "statistics"})
    
    if response["status"]:
        stats = response["data"]
        
        print("=== 错误统计分析 ===")
        print(f"总错误数: {stats['total_errors']}")
        print(f"最近1小时错误: {stats['recent_errors_1h']}")
        print(f"最近24小时错误: {stats['recent_errors_24h']}")
        
        print("\n错误类别分布:")
        for category, count in stats["category_distribution"].items():
            if count > 0:
                print(f"  {category}: {count}")
        
        print("\n最常见错误类型:")
        for error_type, count in stats["top_error_types"]:
            print(f"  {error_type}: {count}")
    
    # 2. 获取最近的错误历史
    response = make_request("GET", "/system/errors", 
                           {"type": "history", "limit": 10})
    
    if response["status"]:
        errors = response["data"]
        
        print(f"\n最近 {len(errors)} 个错误:")
        for error in errors:
            print(f"  [{error['timestamp']}] {error['error_type']}: {error['error_message']}")
            if error['context']:
                print(f"    上下文: {error['context']}")
    
    # 3. 清理旧错误记录
    response = make_request("POST", "/system/errors/clear", 
                           {"older_than_hours": 72})
    
    if response["status"]:
        print("\n已清理72小时前的错误记录")

# 使用示例
analyze_errors()
```

## 最佳实践

### 1. 错误处理

```python
def robust_api_call(method, endpoint, data=None, max_retries=3):
    """带重试机制的API调用"""
    
    for attempt in range(max_retries):
        try:
            response = make_request(method, endpoint, data)
            
            if response.get("status"):
                return response
            else:
                print(f"API调用失败 (尝试 {attempt + 1}/{max_retries}): {response.get('message')}")
                
        except requests.exceptions.RequestException as e:
            print(f"网络错误 (尝试 {attempt + 1}/{max_retries}): {e}")
            
        if attempt < max_retries - 1:
            time.sleep(2 ** attempt)  # 指数退避
    
    return {"status": False, "message": "API调用失败，已达到最大重试次数"}
```

### 2. 批量操作

```python
def batch_operation(operations, batch_size=10):
    """批量执行操作"""
    
    results = []
    
    for i in range(0, len(operations), batch_size):
        batch = operations[i:i + batch_size]
        
        print(f"执行批次 {i//batch_size + 1}, 操作数: {len(batch)}")
        
        for operation in batch:
            result = robust_api_call(**operation)
            results.append(result)
            
            # 避免过于频繁的请求
            time.sleep(0.1)
    
    return results
```

### 3. 配置管理

```python
class ConfigManager:
    """配置管理器"""
    
    def __init__(self, api_base_url, api_key):
        self.api_base_url = api_base_url
        self.api_key = api_key
        self.headers = {
            "Content-Type": "application/json",
            "X-API-Key": api_key
        }
    
    def get_config(self, section=None, key=None):
        """获取配置"""
        if section and key:
            endpoint = f"/config/system/{section}/{key}"
        elif section:
            endpoint = f"/config/system/{section}"
        else:
            endpoint = "/config/system"
        
        return make_request("GET", endpoint)
    
    def set_config(self, section, key, value):
        """设置配置"""
        endpoint = f"/config/system/{section}/{key}"
        return make_request("PUT", endpoint, {"value": value})
    
    def backup_config(self):
        """备份配置"""
        return make_request("POST", "/config/backup")
    
    def reload_config(self):
        """重新加载配置"""
        return make_request("POST", "/config/reload")
```

## 故障排除

### 常见问题

1. **API密钥无效**
   ```python
   # 检查API密钥是否正确
   response = make_request("GET", "/system/health")
   if response.get("error_code") == "PERMISSION_DENIED":
       print("API密钥无效，请检查配置")
   ```

2. **设备连接失败**
   ```python
   # 检查设备状态
   device_response = make_request("GET", f"/devices/{device_uuid}")
   if device_response["status"]:
       device = device_response["data"]
       if device["status"] != "available":
           print(f"设备状态不可用: {device['status']}")
       if device["disable"]:
           print("设备已被禁用")
   ```

3. **服务器连接问题**
   ```python
   # 检查服务器状态
   try:
       response = requests.get(f"{API_BASE_URL}/system/health", 
                              headers=HEADERS, timeout=5)
       if response.status_code != 200:
           print(f"服务器响应异常: {response.status_code}")
   except requests.exceptions.ConnectionError:
       print("无法连接到服务器，请检查网络和服务器状态")
   ```

## 更多资源

- [完整API文档](./API_Documentation.md)
- [配置文件说明](../config/README.md)
- [项目架构文档](./Architecture.md)
- [故障排除指南](./Troubleshooting.md)
