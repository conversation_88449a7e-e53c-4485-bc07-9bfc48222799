{"info": {"name": "OmniLink从服务器 API", "description": "OmniLink从服务器完整API集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8889/api", "type": "string"}, {"key": "api_key", "value": "your-api-key-here", "type": "string"}, {"key": "device_uuid", "value": "usb-1234-5678", "type": "string"}], "auth": {"type": "apikey", "apikey": [{"key": "key", "value": "X-API-Key", "type": "string"}, {"key": "value", "value": "{{api_key}}", "type": "string"}]}, "item": [{"name": "设备管理", "item": [{"name": "获取设备列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/devices", "host": ["{{base_url}}"], "path": ["devices"]}}}, {"name": "获取可用设备", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/devices?status=available", "host": ["{{base_url}}"], "path": ["devices"], "query": [{"key": "status", "value": "available"}]}}}, {"name": "获取设备详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/devices/{{device_uuid}}", "host": ["{{base_url}}"], "path": ["devices", "{{device_uuid}}"]}}}, {"name": "连接设备", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_info\": {\n    \"user_id\": \"user123\",\n    \"user_name\": \"张三\",\n    \"client_ip\": \"*************\"\n  },\n  \"force\": false\n}"}, "url": {"raw": "{{base_url}}/devices/{{device_uuid}}/connect", "host": ["{{base_url}}"], "path": ["devices", "{{device_uuid}}", "connect"]}}}, {"name": "断开设备", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_info\": {\n    \"user_id\": \"user123\",\n    \"user_name\": \"张三\"\n  },\n  \"force\": false\n}"}, "url": {"raw": "{{base_url}}/devices/{{device_uuid}}/disconnect", "host": ["{{base_url}}"], "path": ["devices", "{{device_uuid}}", "disconnect"]}}}, {"name": "重置设备", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/devices/{{device_uuid}}/reset", "host": ["{{base_url}}"], "path": ["devices", "{{device_uuid}}", "reset"]}}}, {"name": "设置设备昵称", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nick_name\": \"我的设备\"\n}"}, "url": {"raw": "{{base_url}}/devices/{{device_uuid}}/nickname", "host": ["{{base_url}}"], "path": ["devices", "{{device_uuid}}", "nickname"]}}}, {"name": "设置设备可见性", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"shown\": true\n}"}, "url": {"raw": "{{base_url}}/devices/{{device_uuid}}/visibility", "host": ["{{base_url}}"], "path": ["devices", "{{device_uuid}}", "visibility"]}}}, {"name": "获取设备统计", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/devices/statistics", "host": ["{{base_url}}"], "path": ["devices", "statistics"]}}}]}, {"name": "服务器管理", "item": [{"name": "获取服务器状态", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/server/status", "host": ["{{base_url}}"], "path": ["server", "status"]}}}, {"name": "启动服务器", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/server/start", "host": ["{{base_url}}"], "path": ["server", "start"]}}}, {"name": "停止服务器", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/server/stop", "host": ["{{base_url}}"], "path": ["server", "stop"]}}}, {"name": "重启服务器", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/server/restart", "host": ["{{base_url}}"], "path": ["server", "restart"]}}}]}, {"name": "系统管理", "item": [{"name": "获取系统状态", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/system/status", "host": ["{{base_url}}"], "path": ["system", "status"]}}}, {"name": "健康检查", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/system/health", "host": ["{{base_url}}"], "path": ["system", "health"]}}}, {"name": "重启系统", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/system/restart", "host": ["{{base_url}}"], "path": ["system", "restart"]}}}]}, {"name": "性能监控", "item": [{"name": "获取当前性能数据", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/system/performance?type=current", "host": ["{{base_url}}"], "path": ["system", "performance"], "query": [{"key": "type", "value": "current"}]}}}, {"name": "获取性能历史数据", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/system/performance?type=history&duration=60", "host": ["{{base_url}}"], "path": ["system", "performance"], "query": [{"key": "type", "value": "history"}, {"key": "duration", "value": "60"}]}}}, {"name": "获取性能摘要", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/system/performance?type=summary&duration=60", "host": ["{{base_url}}"], "path": ["system", "performance"], "query": [{"key": "type", "value": "summary"}, {"key": "duration", "value": "60"}]}}}, {"name": "启动性能监控", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"start\",\n  \"sample_interval\": 5\n}"}, "url": {"raw": "{{base_url}}/system/performance/control", "host": ["{{base_url}}"], "path": ["system", "performance", "control"]}}}]}, {"name": "配置管理", "item": [{"name": "获取所有配置", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/config", "host": ["{{base_url}}"], "path": ["config"]}}}, {"name": "获取系统配置", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/config/system", "host": ["{{base_url}}"], "path": ["config", "system"]}}}, {"name": "获取环境变量配置", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/config/env", "host": ["{{base_url}}"], "path": ["config", "env"]}}}, {"name": "验证环境变量配置", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/config/env/validate", "host": ["{{base_url}}"], "path": ["config", "env", "validate"]}}}, {"name": "获取有效配置", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/config/effective", "host": ["{{base_url}}"], "path": ["config", "effective"]}}}, {"name": "重新加载配置", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/config/reload", "host": ["{{base_url}}"], "path": ["config", "reload"]}}}]}]}