# OmniLink从服务器故障排除指南

## 概述

本指南提供OmniLink从服务器常见问题的诊断和解决方案，帮助用户快速定位和解决问题。

## 快速诊断

### 1. 系统健康检查

```bash
# 检查服务状态
curl http://localhost:8889/api/system/health

# 检查系统状态
curl http://localhost:8889/api/system/status

# 运行完整诊断
curl http://localhost:8889/api/system/diagnostic?type=full
```

### 2. 日志检查

```bash
# 查看应用日志
tail -f logs/slave_server.log

# 查看系统日志
journalctl -u omnilink-slave -f

# 查看Docker日志
docker-compose logs -f slave-server
```

### 3. 进程检查

```bash
# 检查Python进程
ps aux | grep python

# 检查VirtualHere进程
ps aux | grep vhusbd

# 检查端口占用
netstat -tlnp | grep -E '8889|7575'
```

## 常见问题分类

### 1. 服务启动问题

#### 问题：服务无法启动

**症状**:
- 服务启动失败
- 端口绑定错误
- 配置文件错误

**诊断步骤**:

```bash
# 1. 检查端口占用
sudo netstat -tlnp | grep 8889
sudo lsof -i :8889

# 2. 检查配置文件
python -c "import configparser; c=configparser.ConfigParser(); c.read('config/slave_server.ini'); print('配置文件正确')"

# 3. 检查Python环境
python --version
pip list | grep -E 'flask|peewee'

# 4. 检查权限
ls -la config/ data/ logs/
```

**解决方案**:

```bash
# 杀死占用端口的进程
sudo kill -9 $(lsof -t -i:8889)

# 修复配置文件语法错误
cp config/slave_server.ini.template config/slave_server.ini

# 重新安装依赖
pip install -r requirements.txt

# 修复权限问题
sudo chown -R $USER:$USER config/ data/ logs/
chmod 755 config/ data/ logs/
```

#### 问题：Python依赖错误

**症状**:
- ImportError: No module named 'xxx'
- 版本兼容性问题

**解决方案**:

```bash
# 重新创建虚拟环境
rm -rf venv
python3.11 -m venv venv
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 重新安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import flask, peewee, requests; print('依赖安装正确')"
```

### 2. VirtualHere相关问题

#### 问题：VirtualHere服务器无法启动

**症状**:
- VirtualHere进程不存在
- 端口7575无法访问
- USB设备无法共享

**诊断步骤**:

```bash
# 1. 检查VirtualHere二进制文件
ls -la vhusbd
file vhusbd

# 2. 检查执行权限
chmod +x vhusbd

# 3. 手动启动测试
./vhusbd -b

# 4. 检查配置文件
cat config/vhusbd.conf

# 5. 检查USB权限
groups $USER | grep plugdev
ls -la /dev/bus/usb/
```

**解决方案**:

```bash
# 下载正确的VirtualHere版本
# x86_64
curl -o vhusbd https://www.virtualhere.com/sites/default/files/usbserver/vhusbd

# ARM64
curl -o vhusbd https://www.virtualhere.com/sites/default/files/usbserver/vhusbd_arm64

# 设置权限
chmod +x vhusbd

# 添加用户到plugdev组
sudo usermod -a -G plugdev $USER

# 配置udev规则
sudo tee /etc/udev/rules.d/99-virtualhere.rules > /dev/null << EOF
SUBSYSTEM=="usb", GROUP="plugdev", MODE="0664"
SUBSYSTEM=="usb_device", GROUP="plugdev", MODE="0664"
EOF

# 重新加载udev规则
sudo udevadm control --reload-rules
sudo udevadm trigger

# 重新登录以应用组权限
# 或者使用 newgrp plugdev
```

#### 问题：USB设备无法检测

**症状**:
- lsusb显示设备但VirtualHere检测不到
- 设备权限问题

**解决方案**:

```bash
# 检查USB设备
lsusb -v

# 检查设备权限
ls -la /dev/bus/usb/*/*

# 修复权限
sudo chmod 666 /dev/bus/usb/*/*

# 重启udev服务
sudo systemctl restart udev

# 重新插拔USB设备
```

### 3. 网络连接问题

#### 问题：无法连接主服务器

**症状**:
- 心跳失败
- 主从通信中断
- 连接超时

**诊断步骤**:

```bash
# 1. 检查网络连通性
ping master-server-ip

# 2. 检查端口连通性
telnet master-server-ip 8888
nc -zv master-server-ip 8888

# 3. 检查DNS解析
nslookup master-server-hostname
dig master-server-hostname

# 4. 检查防火墙
sudo iptables -L
sudo ufw status

# 5. 检查路由
traceroute master-server-ip
```

**解决方案**:

```bash
# 修复DNS问题
echo "nameserver *******" | sudo tee -a /etc/resolv.conf

# 配置hosts文件
echo "************* master-server" | sudo tee -a /etc/hosts

# 开放防火墙端口
sudo ufw allow out 8888
sudo ufw allow in 8889

# 配置代理（如果需要）
export http_proxy=http://proxy-server:port
export https_proxy=http://proxy-server:port
```

#### 问题：API请求失败

**症状**:
- HTTP 500错误
- 连接被拒绝
- 超时错误

**解决方案**:

```bash
# 检查服务状态
systemctl status omnilink-slave

# 重启服务
sudo systemctl restart omnilink-slave

# 检查日志
journalctl -u omnilink-slave --since "1 hour ago"

# 测试API
curl -v http://localhost:8889/api/system/health
```

### 4. 数据库问题

#### 问题：数据库连接失败

**症状**:
- SQLite database is locked
- 数据库文件不存在
- 权限错误

**诊断步骤**:

```bash
# 1. 检查数据库文件
ls -la data/slave_server.db

# 2. 检查数据库锁
lsof data/slave_server.db

# 3. 检查数据库完整性
sqlite3 data/slave_server.db "PRAGMA integrity_check;"

# 4. 检查权限
stat data/slave_server.db
```

**解决方案**:

```bash
# 杀死锁定数据库的进程
sudo kill -9 $(lsof -t data/slave_server.db)

# 修复权限
sudo chown $USER:$USER data/slave_server.db
chmod 644 data/slave_server.db

# 重新初始化数据库
mv data/slave_server.db data/slave_server.db.backup
python -c "from db import init_database; init_database()"

# 恢复数据（如果需要）
sqlite3 data/slave_server.db < backup.sql
```

#### 问题：数据库损坏

**解决方案**:

```bash
# 备份当前数据库
cp data/slave_server.db data/slave_server.db.corrupt

# 尝试修复
sqlite3 data/slave_server.db ".recover" | sqlite3 data/slave_server_recovered.db

# 如果修复失败，从备份恢复
cp backups/latest/slave_server.db data/slave_server.db

# 重新初始化（最后手段）
rm data/slave_server.db
python -c "from db import init_database; init_database()"
```

### 5. 性能问题

#### 问题：系统响应慢

**症状**:
- API响应时间长
- CPU使用率高
- 内存不足

**诊断步骤**:

```bash
# 1. 检查系统资源
top
htop
free -h
df -h

# 2. 检查进程状态
ps aux --sort=-%cpu | head -10
ps aux --sort=-%mem | head -10

# 3. 检查网络状态
iotop
nethogs

# 4. 检查应用性能
curl -w "@curl-format.txt" http://localhost:8889/api/system/status
```

**解决方案**:

```bash
# 重启服务释放内存
sudo systemctl restart omnilink-slave

# 清理日志文件
find logs/ -name "*.log.*" -mtime +7 -delete

# 清理临时文件
rm -rf tmp/*
rm -rf __pycache__/

# 优化数据库
sqlite3 data/slave_server.db "VACUUM;"

# 调整系统参数
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 6. Docker相关问题

#### 问题：Docker容器无法启动

**症状**:
- 容器启动失败
- 镜像构建错误
- 卷挂载问题

**诊断步骤**:

```bash
# 1. 检查Docker状态
docker version
docker info

# 2. 检查容器状态
docker ps -a
docker logs omnilink-slave-server

# 3. 检查镜像
docker images
docker inspect omnilink-slave:latest

# 4. 检查网络
docker network ls
docker network inspect omnilink_default
```

**解决方案**:

```bash
# 重新构建镜像
docker-compose build --no-cache

# 清理Docker资源
docker system prune -f
docker volume prune -f

# 重新启动服务
docker-compose down
docker-compose up -d

# 检查卷权限
docker exec -it omnilink-slave-server ls -la /app/data
```

## 日志分析

### 1. 日志级别

- **DEBUG**: 详细调试信息
- **INFO**: 一般信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

### 2. 常见错误模式

#### 连接错误

```
ERROR - Connection refused to master server
ERROR - Timeout connecting to master server
ERROR - SSL certificate verification failed
```

**解决方案**: 检查网络连接、防火墙、SSL证书

#### 设备错误

```
ERROR - Device not found: device-uuid
ERROR - Device connection failed: permission denied
ERROR - VirtualHere server not responding
```

**解决方案**: 检查设备状态、权限、VirtualHere服务

#### 配置错误

```
ERROR - Configuration file not found
ERROR - Invalid configuration value
ERROR - Environment variable validation failed
```

**解决方案**: 检查配置文件、环境变量

### 3. 日志分析工具

```bash
# 统计错误数量
grep -c "ERROR" logs/slave_server.log

# 查找特定错误
grep -n "Connection refused" logs/slave_server.log

# 分析错误趋势
awk '/ERROR/ {print $1, $2}' logs/slave_server.log | sort | uniq -c

# 实时监控错误
tail -f logs/slave_server.log | grep --color=always "ERROR\|WARNING"
```

## 监控和告警

### 1. 关键指标监控

```bash
# CPU使用率
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

# 内存使用率
free | grep Mem | awk '{printf("%.2f%%\n", $3/$2 * 100.0)}'

# 磁盘使用率
df -h | grep -E '/$|/data' | awk '{print $5}'

# 网络连接数
netstat -an | grep :8889 | wc -l
```

### 2. 自动化监控脚本

```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

# 检查服务状态
if ! systemctl is-active --quiet omnilink-slave; then
    echo "ALERT: OmniLink服务未运行"
    systemctl restart omnilink-slave
fi

# 检查API健康状态
if ! curl -f -s http://localhost:8889/api/system/health > /dev/null; then
    echo "ALERT: API健康检查失败"
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "ALERT: 磁盘使用率过高: ${DISK_USAGE}%"
fi

# 检查内存使用
MEM_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
if [ $MEM_USAGE -gt 90 ]; then
    echo "ALERT: 内存使用率过高: ${MEM_USAGE}%"
fi
```

## 恢复程序

### 1. 服务恢复

```bash
#!/bin/bash
# recovery.sh - 服务恢复脚本

echo "开始服务恢复程序..."

# 1. 停止服务
sudo systemctl stop omnilink-slave

# 2. 备份当前状态
mkdir -p recovery/$(date +%Y%m%d_%H%M%S)
cp -r data/ logs/ config/ recovery/$(date +%Y%m%d_%H%M%S)/

# 3. 清理临时文件
rm -rf tmp/* __pycache__/

# 4. 重置数据库连接
pkill -f "python.*main.py"

# 5. 重启服务
sudo systemctl start omnilink-slave

# 6. 验证恢复
sleep 10
if curl -f -s http://localhost:8889/api/system/health > /dev/null; then
    echo "服务恢复成功"
else
    echo "服务恢复失败，请检查日志"
fi
```

### 2. 数据恢复

```bash
#!/bin/bash
# data_recovery.sh - 数据恢复脚本

BACKUP_DIR="backups/$(ls -t backups/ | head -1)"

if [ -d "$BACKUP_DIR" ]; then
    echo "从备份恢复数据: $BACKUP_DIR"
    
    # 停止服务
    sudo systemctl stop omnilink-slave
    
    # 备份当前数据
    mv data/ data.corrupted.$(date +%Y%m%d_%H%M%S)
    
    # 恢复数据
    cp -r "$BACKUP_DIR/data" ./
    
    # 重启服务
    sudo systemctl start omnilink-slave
    
    echo "数据恢复完成"
else
    echo "未找到备份文件"
fi
```

## 预防措施

### 1. 定期维护

```bash
# 创建维护脚本
cat > maintenance.sh << 'EOF'
#!/bin/bash
# 定期维护脚本

# 清理日志
find logs/ -name "*.log.*" -mtime +30 -delete

# 清理备份
find backups/ -name "*.tar.gz" -mtime +7 -delete

# 数据库优化
sqlite3 data/slave_server.db "VACUUM;"

# 更新系统
sudo apt update && sudo apt upgrade -y

# 重启服务
sudo systemctl restart omnilink-slave

echo "维护完成: $(date)"
EOF

chmod +x maintenance.sh

# 添加到crontab
echo "0 2 * * 0 /path/to/maintenance.sh" | crontab -
```

### 2. 监控告警

```bash
# 配置邮件告警
sudo apt install mailutils

# 创建告警脚本
cat > alert.sh << 'EOF'
#!/bin/bash
ALERT_EMAIL="<EMAIL>"

if ! curl -f -s http://localhost:8889/api/system/health > /dev/null; then
    echo "OmniLink从服务器健康检查失败 - $(date)" | mail -s "OmniLink告警" $ALERT_EMAIL
fi
EOF

# 添加到crontab
echo "*/5 * * * * /path/to/alert.sh" | crontab -
```

## 获取支持

### 1. 收集诊断信息

```bash
#!/bin/bash
# collect_info.sh - 收集诊断信息

REPORT_DIR="diagnostic_$(date +%Y%m%d_%H%M%S)"
mkdir -p $REPORT_DIR

# 系统信息
uname -a > $REPORT_DIR/system_info.txt
cat /etc/os-release >> $REPORT_DIR/system_info.txt

# 服务状态
systemctl status omnilink-slave > $REPORT_DIR/service_status.txt

# 配置文件
cp config/slave_server.ini $REPORT_DIR/
cp .env $REPORT_DIR/ 2>/dev/null || true

# 日志文件
tail -1000 logs/slave_server.log > $REPORT_DIR/recent_logs.txt

# 系统资源
top -bn1 > $REPORT_DIR/system_resources.txt
free -h >> $REPORT_DIR/system_resources.txt
df -h >> $REPORT_DIR/system_resources.txt

# 网络状态
netstat -tlnp > $REPORT_DIR/network_status.txt

# 打包
tar -czf ${REPORT_DIR}.tar.gz $REPORT_DIR/
echo "诊断信息已收集到: ${REPORT_DIR}.tar.gz"
```

### 2. 联系支持

- **GitHub Issues**: https://github.com/your-org/omnilink-slave-server/issues
- **邮箱支持**: <EMAIL>
- **文档**: https://docs.omnilink.example.com

提交问题时请包含：
- 系统信息
- 错误日志
- 复现步骤
- 诊断信息包
