#!/usr/bin/env python3
"""
从服务器本地SQLite数据库管理
仅存储本机相关的配置信息和当前状态
"""

import sqlite3
import logging
import os
import threading
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class SlaveLocalDatabase:
    """从服务器本地数据库管理器"""
    
    def __init__(self, db_path: str = "data/slave_local.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 初始化数据库
        self._initialize_database()
    
    def _initialize_database(self):
        """初始化数据库表结构"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                # 服务器配置表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS server_config (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        updated_at TEXT NOT NULL
                    )
                ''')
                
                # USB端口配置表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS usb_port_config (
                        port_id TEXT PRIMARY KEY,
                        bus_number INTEGER NOT NULL,
                        port_number INTEGER NOT NULL,
                        hub_path TEXT DEFAULT '',
                        port_type TEXT DEFAULT 'standard',
                        is_enabled BOOLEAN DEFAULT 1,
                        max_power INTEGER DEFAULT 500,
                        notes TEXT DEFAULT '',
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL
                    )
                ''')
                
                # 当前USB设备状态表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS current_usb_devices (
                        device_id TEXT PRIMARY KEY,
                        hardware_signature TEXT NOT NULL,
                        vendor_id TEXT NOT NULL,
                        product_id TEXT NOT NULL,
                        device_name TEXT NOT NULL,
                        device_type TEXT NOT NULL,
                        port_location TEXT NOT NULL,
                        status TEXT DEFAULT 'online',
                        is_real_hardware BOOLEAN DEFAULT 1,
                        auto_bind_eligible BOOLEAN DEFAULT 0,
                        connected_at TEXT NOT NULL,
                        last_seen TEXT NOT NULL,
                        device_data TEXT DEFAULT '{}'
                    )
                ''')
                
                # USB拓扑快照表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS usb_topology_snapshot (
                        snapshot_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        hub_count INTEGER DEFAULT 0,
                        total_ports INTEGER DEFAULT 0,
                        occupied_ports INTEGER DEFAULT 0,
                        free_ports INTEGER DEFAULT 0,
                        hub_details TEXT DEFAULT '[]',
                        created_at TEXT NOT NULL
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_devices_hardware_signature ON current_usb_devices(hardware_signature)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_devices_port_location ON current_usb_devices(port_location)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_devices_status ON current_usb_devices(status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_topology_created_at ON usb_topology_snapshot(created_at)')
                
                conn.commit()
                logger.info("本地数据库初始化完成")
                
            except Exception as e:
                conn.rollback()
                logger.error(f"数据库初始化失败: {e}")
                raise
            finally:
                conn.close()
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接上下文管理器"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            try:
                yield conn
            finally:
                conn.close()
    
    def set_config(self, key: str, value: Any):
        """设置配置项"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO server_config (key, value, updated_at)
                VALUES (?, ?, ?)
            ''', (key, json.dumps(value), datetime.now().isoformat()))
            conn.commit()
    
    def get_config(self, key: str, default=None):
        """获取配置项"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT value FROM server_config WHERE key = ?', (key,))
            result = cursor.fetchone()
            if result:
                return json.loads(result[0])
            return default
    
    def update_usb_devices(self, devices: List[Dict[str, Any]]):
        """更新当前USB设备状态"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 清空现有设备
                cursor.execute('DELETE FROM current_usb_devices')

                # 插入新设备
                for device in devices:
                    try:
                        # 确保device_id存在，如果没有则使用hardware_signature
                        device_id = device.get('device_id') or device.get('hardware_signature', '')
                        if not device_id:
                            # 如果还是没有，生成一个基于vendor_id和product_id的ID
                            vendor_id = device.get('vendor_id', 0)
                            product_id = device.get('product_id', 0)
                            port_location = device.get('port_location', 'unknown')
                            device_id = f"USB_{vendor_id:04X}_{product_id:04X}_{port_location}"

                        # 数据类型和空值处理
                        vendor_id_str = str(device.get('vendor_id', '0000')).strip()
                        if not vendor_id_str or vendor_id_str == '':
                            vendor_id_str = '0000'

                        product_id_str = str(device.get('product_id', '0000')).strip()
                        if not product_id_str or product_id_str == '':
                            product_id_str = '0000'

                        device_name = device.get('device_name') or device.get('description') or 'Unknown Device'
                        if not device_name or device_name.strip() == '':
                            device_name = 'Unknown Device'

                        device_type = device.get('device_type', 'unknown').strip()
                        if not device_type:
                            device_type = 'unknown'

                        port_location = device.get('port_location', 'unknown').strip()
                        if not port_location:
                            port_location = 'unknown'

                        hardware_signature = device.get('hardware_signature', '').strip()

                        status = device.get('status', 'online').strip()
                        if not status:
                            status = 'online'

                        cursor.execute('''
                            INSERT OR REPLACE INTO current_usb_devices (
                                device_id, hardware_signature, vendor_id, product_id,
                                device_name, device_type, port_location, status,
                                is_real_hardware, auto_bind_eligible, connected_at,
                                last_seen, device_data
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            device_id,
                            hardware_signature,
                            vendor_id_str,
                            product_id_str,
                            device_name,
                            device_type,
                            port_location,
                            status,
                            bool(device.get('is_real_hardware', True)),
                            bool(device.get('auto_bind_eligible', False)),
                            device.get('connected_at', datetime.now().isoformat()),
                            datetime.now().isoformat(),
                            json.dumps(device, ensure_ascii=False)
                        ))

                    except Exception as device_error:
                        logger.error(f"插入设备数据失败 {device.get('device_id', 'unknown')}: {device_error}")
                        continue

                conn.commit()
                logger.debug(f"更新了 {len(devices)} 个USB设备状态")

        except Exception as e:
            logger.error(f"更新USB设备状态失败: {e}")
            raise
    
    def get_current_devices(self) -> List[Dict[str, Any]]:
        """获取当前USB设备列表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM current_usb_devices ORDER BY connected_at')
            rows = cursor.fetchall()
            
            devices = []
            for row in rows:
                device = dict(row)
                device['device_data'] = json.loads(device['device_data'])
                devices.append(device)
            
            return devices
    
    def save_topology_snapshot(self, topology: Dict[str, Any]):
        """保存USB拓扑快照"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO usb_topology_snapshot (
                    hub_count, total_ports, occupied_ports, free_ports,
                    hub_details, created_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                topology.get('hub_count', 0),
                topology.get('total_ports', 0),
                topology.get('occupied_ports', 0),
                topology.get('free_ports', 0),
                json.dumps(topology.get('hub_details', [])),
                datetime.now().isoformat()
            ))
            conn.commit()
    
    def get_latest_topology(self) -> Optional[Dict[str, Any]]:
        """获取最新的USB拓扑快照"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM usb_topology_snapshot 
                ORDER BY created_at DESC LIMIT 1
            ''')
            row = cursor.fetchone()
            
            if row:
                topology = dict(row)
                topology['hub_details'] = json.loads(topology['hub_details'])
                return topology
            return None
    
    def cleanup_old_snapshots(self, keep_count: int = 10):
        """清理旧的拓扑快照，只保留最新的几个"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                DELETE FROM usb_topology_snapshot 
                WHERE snapshot_id NOT IN (
                    SELECT snapshot_id FROM usb_topology_snapshot 
                    ORDER BY created_at DESC LIMIT ?
                )
            ''', (keep_count,))
            conn.commit()
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            stats = {}
            
            # 配置项数量
            cursor.execute('SELECT COUNT(*) FROM server_config')
            stats['config_count'] = cursor.fetchone()[0]
            
            # 当前设备数量
            cursor.execute('SELECT COUNT(*) FROM current_usb_devices')
            stats['device_count'] = cursor.fetchone()[0]
            
            # 拓扑快照数量
            cursor.execute('SELECT COUNT(*) FROM usb_topology_snapshot')
            stats['snapshot_count'] = cursor.fetchone()[0]
            
            # 数据库文件大小
            if os.path.exists(self.db_path):
                stats['db_size_bytes'] = os.path.getsize(self.db_path)
            else:
                stats['db_size_bytes'] = 0
            
            return stats

# 全局数据库实例
local_db = None

def get_local_db() -> SlaveLocalDatabase:
    """获取本地数据库实例"""
    global local_db
    if local_db is None:
        local_db = SlaveLocalDatabase()
    return local_db
