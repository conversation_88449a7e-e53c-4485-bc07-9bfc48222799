# OmniLink从服务器 - 轻量化Docker镜像
# 支持ARM Linux架构，专注USB设备代理功能
FROM python:3.11-slim

# 设置维护者信息
LABEL maintainer="OmniLink Team"
LABEL description="OmniLink Lightweight Slave Server for USB device proxy"
LABEL version="2.0.0"

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app \
    FLASK_ENV=production

# 安装系统依赖 - 最小化安装
RUN apt-get update && apt-get install -y --no-install-recommends \
    # 基础工具
    curl \
    wget \
    # USB设备支持
    libusb-1.0-0 \
    libusb-1.0-0-dev \
    # 编译工具（临时）
    gcc \
    libc6-dev \
    linux-libc-dev \
    # 清理缓存在同一层
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户（非root用户）
RUN groupadd -g 1000 omnilink && \
    useradd -r -s /bin/bash -u 1000 -g omnilink omnilink

# 复制requirements文件并安装Python依赖
COPY requirements.txt /app/
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    # 清理编译工具
    apt-get remove -y gcc libc6-dev linux-libc-dev && \
    apt-get autoremove -y && \
    rm -rf /var/lib/apt/lists/*

# 复制应用代码
COPY . .

# 复制本地VirtualHere文件
COPY virtualhere/ /app/virtualhere/

# 根据架构选择合适的二进制文件
RUN if [ "$(uname -m)" = "armv7l" ] || [ "$(uname -m)" = "aarch64" ]; then \
        echo "检测到ARM架构，使用本地ARM版本"; \
        cp /app/virtualhere/vhusbdarmpi3 /app/vhusbd; \
    else \
        echo "检测到x86架构，下载对应版本"; \
        wget -O /app/vhusbd https://www.virtualhere.com/sites/default/files/usbserver/vhusbdx86_64; \
    fi && \
    chmod +x /app/vhusbd

# 创建必要的目录并设置权限
RUN mkdir -p /app/logs /app/data /app/config && \
    chown -R omnilink:omnilink /app && \
    chmod -R 755 /app

# 创建配置文件模板
RUN echo "[server]" > /app/config/slave_server.ini && \
    echo "host = 0.0.0.0" >> /app/config/slave_server.ini && \
    echo "port = 8889" >> /app/config/slave_server.ini && \
    echo "" >> /app/config/slave_server.ini && \
    echo "[virtualhere]" >> /app/config/slave_server.ini && \
    echo "server_port = 7575" >> /app/config/slave_server.ini && \
    echo "binary_path = /app/vhusbd" >> /app/config/slave_server.ini && \
    echo "" >> /app/config/slave_server.ini && \
    echo "[logging]" >> /app/config/slave_server.ini && \
    echo "log_level = INFO" >> /app/config/slave_server.ini && \
    echo "log_file = logs/slave_server.log" >> /app/config/slave_server.ini && \
    chown omnilink:omnilink /app/config/slave_server.ini

# 暴露端口
EXPOSE 8889 7575

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8889/api/system/health || exit 1

# 切换到非root用户
USER omnilink

# 启动命令
CMD ["python", "main.py"]
