#!/bin/bash
# Docker容器启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量..."
    
    # 设置默认值
    export SLAVE_SERVER_HOST=${SLAVE_SERVER_HOST:-"0.0.0.0"}
    export SLAVE_SERVER_PORT=${SLAVE_SERVER_PORT:-"8889"}
    export VH_SERVER_PORT=${VH_SERVER_PORT:-"7575"}
    export LOG_LEVEL=${LOG_LEVEL:-"INFO"}
    export MASTER_SERVER_URL=${MASTER_SERVER_URL:-""}
    
    log_info "服务器地址: ${SLAVE_SERVER_HOST}:${SLAVE_SERVER_PORT}"
    log_info "VirtualHere端口: ${VH_SERVER_PORT}"
    log_info "日志级别: ${LOG_LEVEL}"
    
    if [ -n "$MASTER_SERVER_URL" ]; then
        log_info "主服务器地址: ${MASTER_SERVER_URL}"
    else
        log_warn "未设置主服务器地址，将在运行时配置"
    fi
}

# 初始化目录
init_directories() {
    log_info "初始化目录结构..."
    
    # 确保目录存在
    mkdir -p /app/logs /app/data /app/config /app/tmp
    
    # 检查权限
    if [ ! -w /app/logs ]; then
        log_error "日志目录不可写: /app/logs"
        exit 1
    fi
    
    if [ ! -w /app/data ]; then
        log_error "数据目录不可写: /app/data"
        exit 1
    fi
    
    log_info "目录初始化完成"
}

# 更新配置文件
update_config() {
    log_info "更新配置文件..."
    
    CONFIG_FILE="/app/config/slave_server.ini"
    
    # 备份原配置文件
    if [ -f "$CONFIG_FILE" ]; then
        cp "$CONFIG_FILE" "${CONFIG_FILE}.backup"
    fi
    
    # 生成新配置文件
    cat > "$CONFIG_FILE" << EOF
[server]
host = ${SLAVE_SERVER_HOST}
port = ${SLAVE_SERVER_PORT}

[database]
db_file = data/slave_server.db

[virtualhere]
server_port = ${VH_SERVER_PORT}
binary_path = /app/vhusbd

[logging]
log_level = ${LOG_LEVEL}
log_file = logs/slave_server.log

[master]
server_url = ${MASTER_SERVER_URL}
EOF
    
    log_info "配置文件已更新: $CONFIG_FILE"
}

# 检查VirtualHere二进制文件
check_virtualhere() {
    log_info "检查VirtualHere服务器..."
    
    VH_BINARY="/app/vhusbd"
    
    if [ ! -f "$VH_BINARY" ]; then
        log_error "VirtualHere二进制文件不存在: $VH_BINARY"
        exit 1
    fi
    
    if [ ! -x "$VH_BINARY" ]; then
        log_error "VirtualHere二进制文件不可执行: $VH_BINARY"
        exit 1
    fi
    
    # 测试VirtualHere版本
    VH_VERSION=$("$VH_BINARY" -v 2>/dev/null || echo "unknown")
    log_info "VirtualHere版本: $VH_VERSION"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    DB_FILE="/app/data/slave_server.db"
    
    if [ ! -f "$DB_FILE" ]; then
        log_info "数据库文件不存在，将在应用启动时自动创建"
    else
        log_info "数据库文件已存在: $DB_FILE"
        # 检查数据库文件大小
        DB_SIZE=$(stat -c%s "$DB_FILE" 2>/dev/null || echo "0")
        log_info "数据库文件大小: ${DB_SIZE} bytes"
    fi
}

# 启动性能监控
start_performance_monitor() {
    log_info "启动性能监控..."
    
    # 性能监控将在Python应用中启动
    # 这里只是记录启动意图
    export ENABLE_PERFORMANCE_MONITOR=${ENABLE_PERFORMANCE_MONITOR:-"true"}
    
    if [ "$ENABLE_PERFORMANCE_MONITOR" = "true" ]; then
        log_info "性能监控已启用"
    else
        log_info "性能监控已禁用"
    fi
}

# 健康检查
health_check() {
    log_info "执行启动前健康检查..."
    
    # 检查端口是否被占用
    if netstat -tuln 2>/dev/null | grep -q ":${SLAVE_SERVER_PORT} "; then
        log_warn "端口 ${SLAVE_SERVER_PORT} 已被占用"
    fi
    
    if netstat -tuln 2>/dev/null | grep -q ":${VH_SERVER_PORT} "; then
        log_warn "端口 ${VH_SERVER_PORT} 已被占用"
    fi
    
    # 检查磁盘空间
    DISK_USAGE=$(df /app | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -gt 90 ]; then
        log_warn "磁盘使用率过高: ${DISK_USAGE}%"
    fi
    
    log_info "健康检查完成"
}

# 信号处理
cleanup() {
    log_info "接收到停止信号，正在清理..."
    
    # 停止Python应用
    if [ -n "$PYTHON_PID" ]; then
        log_info "停止Python应用 (PID: $PYTHON_PID)"
        kill -TERM "$PYTHON_PID" 2>/dev/null || true
        wait "$PYTHON_PID" 2>/dev/null || true
    fi
    
    log_info "清理完成"
    exit 0
}

# 注册信号处理
trap cleanup SIGTERM SIGINT

# 主函数
main() {
    log_info "OmniLink从服务器容器启动中..."
    log_info "容器版本: $(cat /app/VERSION 2>/dev/null || echo 'unknown')"
    
    # 执行初始化步骤
    check_environment
    init_directories
    update_config
    check_virtualhere
    init_database
    start_performance_monitor
    health_check
    
    log_info "初始化完成，启动应用..."
    
    # 启动Python应用
    cd /app
    python main.py &
    PYTHON_PID=$!
    
    log_info "应用已启动 (PID: $PYTHON_PID)"
    log_info "容器启动完成"
    
    # 等待应用进程
    wait "$PYTHON_PID"
    
    # 如果应用意外退出
    EXIT_CODE=$?
    log_error "应用进程意外退出 (退出码: $EXIT_CODE)"
    exit $EXIT_CODE
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
