# VirtualHere管理员权限启动脚本
# 用于解决VirtualHere需要管理员权限访问USB设备的问题

param(
    [string]$VHPath = "virtualhere\vhusbdwin64.exe",
    [int]$Port = 7575,
    [string]$ConfigFile = "virtualhere\vhusbd.conf"
)

# 检查是否以管理员权限运行
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 创建VirtualHere配置文件
function Create-VHConfig {
    param([string]$ConfigPath)
    
    $configContent = @"
# OmniLink VirtualHere配置文件
# 自动生成的配置，支持USB设备共享

# 服务器基本设置
ServerHubName=OmniLink-USB-Hub
ServerHubPort=$Port

# 网络设置
AutoFind=0
ReverseClients=1
ClientTimeout=60
MaxClients=10

# 设备设置
DeviceNicknames=1
AutoAddDevices=1
ExcludeHubs=1
MaxDevices=32

# 安全设置
onClientConnect=`$SCRIPT`$
echo "Client connected: `$CLIENT_IP`$ at `$(date)"
`$ENDSCRIPT`$

onClientDisconnect=`$SCRIPT`$
echo "Client disconnected: `$CLIENT_IP`$ at `$(date)"
`$ENDSCRIPT`$

# 日志设置
LogLevel=2

# 性能优化设置
UseCompression=1
CompressionLevel=6
BufferSize=65536
"@

    $configDir = Split-Path $ConfigPath -Parent
    if (!(Test-Path $configDir)) {
        New-Item -ItemType Directory -Path $configDir -Force | Out-Null
    }
    
    Set-Content -Path $ConfigPath -Value $configContent -Encoding UTF8
    Write-Host "VirtualHere配置文件已创建: $ConfigPath" -ForegroundColor Green
}

# 主执行逻辑
Write-Host "OmniLink VirtualHere管理员启动脚本" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# 检查管理员权限
if (-not (Test-Administrator)) {
    Write-Host "错误: 需要管理员权限才能启动VirtualHere" -ForegroundColor Red
    Write-Host "请以管理员身份运行PowerShell，然后重新执行此脚本" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "或者使用以下命令自动提升权限:" -ForegroundColor Yellow
    Write-Host "Start-Process PowerShell -Verb RunAs -ArgumentList '-File $($MyInvocation.MyCommand.Path)'" -ForegroundColor Cyan
    exit 1
}

Write-Host "✓ 管理员权限验证通过" -ForegroundColor Green

# 检查VirtualHere二进制文件
if (!(Test-Path $VHPath)) {
    Write-Host "错误: VirtualHere二进制文件不存在: $VHPath" -ForegroundColor Red
    exit 1
}

Write-Host "✓ VirtualHere二进制文件存在: $VHPath" -ForegroundColor Green

# 创建配置文件
Create-VHConfig -ConfigPath $ConfigFile

# 启动VirtualHere服务器
Write-Host ""
Write-Host "启动VirtualHere服务器..." -ForegroundColor Yellow
Write-Host "端口: $Port" -ForegroundColor Cyan
Write-Host "配置文件: $ConfigFile" -ForegroundColor Cyan
Write-Host ""

try {
    $process = Start-Process -FilePath $VHPath -ArgumentList "-b", $Port, "-c", $ConfigFile, "-r" -PassThru -NoNewWindow
    
    # 等待短暂时间检查启动状态
    Start-Sleep -Seconds 2
    
    if ($process.HasExited) {
        Write-Host "VirtualHere启动失败，进程已退出" -ForegroundColor Red
        Write-Host "退出代码: $($process.ExitCode)" -ForegroundColor Red
    } else {
        Write-Host "✓ VirtualHere服务器启动成功!" -ForegroundColor Green
        Write-Host "进程ID: $($process.Id)" -ForegroundColor Cyan
        Write-Host "监听端口: $Port" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "按任意键停止服务器..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
        # 停止服务器
        Write-Host "正在停止VirtualHere服务器..." -ForegroundColor Yellow
        $process.Kill()
        $process.WaitForExit(5000)
        Write-Host "✓ VirtualHere服务器已停止" -ForegroundColor Green
    }
} catch {
    Write-Host "启动VirtualHere时发生错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
