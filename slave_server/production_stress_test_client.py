#!/usr/bin/env python3
"""
OmniLink生产环境预部署验证：并发压力测试客户端
模拟真实生产环境下的多从服务器并发场景
"""

import sys
import time
import json
import uuid
import socket
import hashlib
import threading
import random
import asyncio
from datetime import datetime
from urllib.request import urlopen, Request
from urllib.parse import urlencode
from urllib.error import URLError, HTTPError

class ProductionStressTestClient:
    def __init__(self, instance_id, server_port, vh_port, simulated_ip=None, master_url="http://************:8000"):
        self.instance_id = instance_id
        self.server_port = server_port
        self.vh_port = vh_port
        self.master_url = master_url
        self.simulated_ip = simulated_ip or self._generate_simulated_ip()
        self.server_name = self._generate_unique_server_name()
        self.server_uuid = self._generate_server_uuid()
        self.running = False
        self.heartbeat_thread = None
        self.registration_success = False
        self.heartbeat_count = 0
        self.error_count = 0
        self.start_time = time.time()
        
    def _generate_simulated_ip(self):
        """生成模拟的不同IP地址段"""
        # 模拟不同的内网IP段
        ip_segments = [
            "192.168.1",
            "192.168.2", 
            "192.168.10",
            "10.0.1",
            "10.0.2",
            "172.16.1",
            "172.16.2",
            "172.17.1"
        ]
        segment = random.choice(ip_segments)
        host = random.randint(10, 250)
        return f"{segment}.{host}"
        
    def _generate_unique_server_name(self):
        """生成生产级唯一服务器名称"""
        # 模拟真实的服务器命名规范
        location_codes = ["BJ", "SH", "GZ", "SZ", "CD", "WH", "NJ", "HZ"]
        dept_codes = ["DEV", "TEST", "PROD", "UAT", "STG"]
        
        location = random.choice(location_codes)
        dept = random.choice(dept_codes)
        
        # 基于实例ID和时间戳确保唯一性
        timestamp = int(time.time() * 1000) % 100000
        random_suffix = random.randint(1000, 9999)
        
        unique_name = f"OmniLink-{location}-{dept}-{self.instance_id:03d}-{self.server_port}-{timestamp}-{random_suffix}"
        print(f"[实例{self.instance_id}] 生成服务器名称: {unique_name}")
        return unique_name
        
    def _generate_server_uuid(self):
        """生成唯一的服务器UUID"""
        # 基于实例ID、端口、IP和时间戳生成唯一UUID
        uuid_source = f"{self.instance_id}-{self.simulated_ip}-{self.server_port}-{int(time.time())}-{random.randint(10000, 99999)}"
        return hashlib.md5(uuid_source.encode()).hexdigest()
    
    def register_to_master(self):
        """向主服务器注册"""
        try:
            # 模拟真实的从服务器注册数据
            device_count = random.randint(3, 12)  # 模拟不同数量的USB设备
            hub_count = random.randint(1, 3)      # 模拟不同数量的Hub
            total_ports = hub_count * 8           # 每个Hub 8个端口
            
            data = {
                'server_name': self.server_name,
                'server_ip': self.simulated_ip,
                'server_port': self.server_port,
                'vh_port': self.vh_port,
                'hardware_uuid': self.server_uuid,
                'version': '2.0-production-test',
                'description': f'生产环境压力测试实例 #{self.instance_id} - IP:{self.simulated_ip}:{self.server_port}'
            }
            
            json_data = json.dumps(data).encode('utf-8')
            
            req = Request(
                f"{self.master_url}/api/v1/slave/register",
                data=json_data,
                headers={'Content-Type': 'application/json'}
            )
            
            start_time = time.time()
            with urlopen(req, timeout=15) as response:
                response_time = time.time() - start_time
                result = json.loads(response.read().decode('utf-8'))
                print(f"[实例{self.instance_id}] 注册成功 - 响应时间: {response_time:.3f}s - 结果: {result}")
                self.registration_success = True
                return True
                
        except Exception as e:
            self.error_count += 1
            print(f"[实例{self.instance_id}] 注册失败: {e}")
            return False
    
    def send_heartbeat(self):
        """发送心跳"""
        try:
            # 模拟真实的心跳数据，包含系统监控信息
            device_count = random.randint(3, 12)
            hub_count = random.randint(1, 3)
            total_ports = hub_count * 8
            occupied_ports = random.randint(device_count, total_ports)
            
            data = {
                'timestamp': datetime.now().isoformat(),
                'status': 'online',
                'device_count_summary': device_count,
                'hub_count': hub_count,
                'total_ports': total_ports,
                'occupied_ports': occupied_ports,
                'free_ports': total_ports - occupied_ports,
                'heartbeat_type': 'lightweight',
                'server_name': self.server_name,
                'ip_address': self.simulated_ip,
                'web_port': self.server_port,
                'virtualhere_status': 'running',
                'virtualhere_port': self.vh_port,
                'server_version': '2.0-production-test',
                'heartbeat_timestamp': datetime.now().isoformat(),
                'data_sync_status': 'active',
                'system_load': round(random.uniform(0.1, 2.0), 2),
                'memory_usage': round(random.uniform(20.0, 80.0), 1),
                'disk_usage': round(random.uniform(10.0, 90.0), 1),
                'uptime_seconds': int(time.time() - self.start_time)
            }
            
            json_data = json.dumps(data).encode('utf-8')
            
            req = Request(
                f"{self.master_url}/api/v1/slave/heartbeat",
                data=json_data,
                headers={'Content-Type': 'application/json'}
            )
            
            start_time = time.time()
            with urlopen(req, timeout=10) as response:
                response_time = time.time() - start_time
                result = json.loads(response.read().decode('utf-8'))
                self.heartbeat_count += 1
                if self.heartbeat_count % 5 == 0:  # 每5次心跳打印一次状态
                    print(f"[实例{self.instance_id}] 心跳#{self.heartbeat_count} - 响应时间: {response_time:.3f}s - {datetime.now().strftime('%H:%M:%S')}")
                return True
                
        except Exception as e:
            self.error_count += 1
            if self.error_count % 3 == 0:  # 每3次错误打印一次
                print(f"[实例{self.instance_id}] 心跳发送失败#{self.error_count}: {e}")
            return False
    
    def heartbeat_loop(self):
        """心跳循环"""
        while self.running:
            self.send_heartbeat()
            # 模拟真实环境的心跳间隔变化
            interval = random.uniform(25, 35)  # 25-35秒的随机间隔
            time.sleep(interval)
    
    def start(self):
        """启动压力测试客户端"""
        print(f"[实例{self.instance_id}] 启动生产环境压力测试客户端")
        print(f"[实例{self.instance_id}] 服务器: {self.server_name}")
        print(f"[实例{self.instance_id}] 模拟IP: {self.simulated_ip}:{self.server_port}")
        print(f"[实例{self.instance_id}] VH端口: {self.vh_port}")
        
        # 注册到主服务器
        if not self.register_to_master():
            print(f"[实例{self.instance_id}] 注册失败，退出")
            return False
        
        # 启动心跳线程
        self.running = True
        self.heartbeat_thread = threading.Thread(target=self.heartbeat_loop)
        self.heartbeat_thread.daemon = True
        self.heartbeat_thread.start()
        
        return True
    
    def stop(self):
        """停止压力测试客户端"""
        self.running = False
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=5)
        
        runtime = time.time() - self.start_time
        print(f"[实例{self.instance_id}] 停止 - 运行时间: {runtime:.1f}s, 心跳次数: {self.heartbeat_count}, 错误次数: {self.error_count}")
    
    def get_stats(self):
        """获取统计信息"""
        runtime = time.time() - self.start_time
        return {
            'instance_id': self.instance_id,
            'server_name': self.server_name,
            'simulated_ip': self.simulated_ip,
            'server_port': self.server_port,
            'registration_success': self.registration_success,
            'heartbeat_count': self.heartbeat_count,
            'error_count': self.error_count,
            'runtime': runtime,
            'error_rate': self.error_count / max(self.heartbeat_count, 1)
        }

def main():
    if len(sys.argv) != 4:
        print("用法: python3 production_stress_test_client.py <instance_id> <server_port> <vh_port>")
        print("示例: python3 production_stress_test_client.py 1 8891 7576")
        sys.exit(1)
    
    instance_id = int(sys.argv[1])
    server_port = int(sys.argv[2])
    vh_port = int(sys.argv[3])
    
    client = ProductionStressTestClient(instance_id, server_port, vh_port)
    
    try:
        if client.start():
            print(f"[实例{instance_id}] 压力测试客户端已启动，按Ctrl+C停止")
            while client.running:
                time.sleep(1)
        else:
            print(f"[实例{instance_id}] 启动失败")
    except KeyboardInterrupt:
        print(f"\n[实例{instance_id}] 正在停止...")
        client.stop()

if __name__ == "__main__":
    main()
