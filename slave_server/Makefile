# OmniLink从服务器 Makefile

# 变量定义
PROJECT_NAME = omnilink-slave-server
DOCKER_COMPOSE = docker-compose
DOCKER_COMPOSE_DEV = docker-compose -f docker-compose.yml -f docker-compose.dev.yml
IMAGE_NAME = omnilink/slave-server
VERSION = $(shell cat VERSION)

# 默认目标
.DEFAULT_GOAL := help

# 帮助信息
.PHONY: help
help: ## 显示帮助信息
	@echo "OmniLink从服务器 - 可用命令:"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""

# 构建相关
.PHONY: build
build: ## 构建Docker镜像
	@echo "构建Docker镜像..."
	$(DOCKER_COMPOSE) build --no-cache

.PHONY: build-dev
build-dev: ## 构建开发环境Docker镜像
	@echo "构建开发环境Docker镜像..."
	$(DOCKER_COMPOSE_DEV) build --no-cache

.PHONY: pull
pull: ## 拉取最新的基础镜像
	@echo "拉取最新的基础镜像..."
	docker pull python:3.11-slim
	docker pull redis:7-alpine
	docker pull prom/prometheus:latest
	docker pull grafana/grafana:latest

# 运行相关
.PHONY: up
up: ## 启动生产环境服务
	@echo "启动生产环境服务..."
	$(DOCKER_COMPOSE) up -d

.PHONY: up-dev
up-dev: ## 启动开发环境服务
	@echo "启动开发环境服务..."
	$(DOCKER_COMPOSE_DEV) up -d

.PHONY: down
down: ## 停止所有服务
	@echo "停止所有服务..."
	$(DOCKER_COMPOSE) down

.PHONY: down-dev
down-dev: ## 停止开发环境服务
	@echo "停止开发环境服务..."
	$(DOCKER_COMPOSE_DEV) down

.PHONY: restart
restart: down up ## 重启生产环境服务

.PHONY: restart-dev
restart-dev: down-dev up-dev ## 重启开发环境服务

# 日志相关
.PHONY: logs
logs: ## 查看所有服务日志
	$(DOCKER_COMPOSE) logs -f

.PHONY: logs-slave
logs-slave: ## 查看从服务器日志
	$(DOCKER_COMPOSE) logs -f slave-server

.PHONY: logs-redis
logs-redis: ## 查看Redis日志
	$(DOCKER_COMPOSE) logs -f redis

.PHONY: logs-monitoring
logs-monitoring: ## 查看监控服务日志
	$(DOCKER_COMPOSE) logs -f monitoring grafana

# 状态检查
.PHONY: status
status: ## 查看服务状态
	@echo "服务状态:"
	$(DOCKER_COMPOSE) ps

.PHONY: health
health: ## 检查服务健康状态
	@echo "检查服务健康状态..."
	@docker exec omnilink-slave-server curl -f http://localhost:8889/api/system/health || echo "从服务器健康检查失败"
	@docker exec omnilink-redis redis-cli ping || echo "Redis健康检查失败"

# 开发相关
.PHONY: shell
shell: ## 进入从服务器容器Shell
	docker exec -it omnilink-slave-server /bin/bash

.PHONY: shell-dev
shell-dev: ## 进入开发工具容器Shell
	$(DOCKER_COMPOSE_DEV) exec dev-tools bash

.PHONY: test
test: ## 运行测试
	$(DOCKER_COMPOSE_DEV) exec dev-tools python -m pytest tests/ -v

.PHONY: lint
lint: ## 代码检查
	$(DOCKER_COMPOSE_DEV) exec dev-tools black --check .
	$(DOCKER_COMPOSE_DEV) exec dev-tools flake8 .

.PHONY: format
format: ## 代码格式化
	$(DOCKER_COMPOSE_DEV) exec dev-tools black .

# 数据管理
.PHONY: backup
backup: ## 备份数据
	@echo "备份数据..."
	@mkdir -p backups
	@docker exec omnilink-slave-server tar -czf /tmp/backup.tar.gz -C /app data logs config
	@docker cp omnilink-slave-server:/tmp/backup.tar.gz backups/backup-$(shell date +%Y%m%d-%H%M%S).tar.gz
	@echo "备份完成: backups/backup-$(shell date +%Y%m%d-%H%M%S).tar.gz"

.PHONY: restore
restore: ## 恢复数据 (使用: make restore BACKUP=backup-file.tar.gz)
	@if [ -z "$(BACKUP)" ]; then echo "请指定备份文件: make restore BACKUP=backup-file.tar.gz"; exit 1; fi
	@echo "恢复数据: $(BACKUP)"
	@docker cp backups/$(BACKUP) omnilink-slave-server:/tmp/restore.tar.gz
	@docker exec omnilink-slave-server tar -xzf /tmp/restore.tar.gz -C /app
	@echo "数据恢复完成"

# 清理相关
.PHONY: clean
clean: ## 清理未使用的Docker资源
	@echo "清理未使用的Docker资源..."
	docker system prune -f
	docker volume prune -f

.PHONY: clean-all
clean-all: down ## 清理所有Docker资源（包括数据卷）
	@echo "清理所有Docker资源..."
	$(DOCKER_COMPOSE) down -v --rmi all
	docker system prune -af

# 初始化相关
.PHONY: init
init: ## 初始化项目环境
	@echo "初始化项目环境..."
	@mkdir -p data logs config dev-data dev-logs monitoring/grafana/dashboards monitoring/grafana/datasources
	@chmod 755 docker-entrypoint.sh
	@echo "环境初始化完成"

.PHONY: init-dev
init-dev: init ## 初始化开发环境
	@echo "初始化开发环境..."
	@echo "开发环境初始化完成"

# 监控相关
.PHONY: monitoring
monitoring: ## 打开监控面板
	@echo "监控服务地址:"
	@echo "  Prometheus: http://localhost:9090"
	@echo "  Grafana:    http://localhost:3000 (admin/admin123)"
	@echo "  Adminer:    http://localhost:8080 (开发环境)"

# 版本相关
.PHONY: version
version: ## 显示版本信息
	@echo "项目版本: $(VERSION)"
	@echo "Docker镜像: $(IMAGE_NAME):$(VERSION)"

.PHONY: tag
tag: ## 标记Docker镜像版本
	docker tag $(IMAGE_NAME):latest $(IMAGE_NAME):$(VERSION)

# 部署相关
.PHONY: deploy
deploy: build tag ## 构建并标记用于部署的镜像
	@echo "镜像已准备用于部署: $(IMAGE_NAME):$(VERSION)"

# 快速启动
.PHONY: quick-start
quick-start: init build up ## 快速启动（初始化+构建+运行）
	@echo "快速启动完成!"
	@echo "服务地址:"
	@echo "  从服务器API: http://localhost:8889"
	@echo "  VirtualHere:  localhost:7575"
	@make monitoring
