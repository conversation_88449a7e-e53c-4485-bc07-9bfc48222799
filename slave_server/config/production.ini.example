# OmniLink从服务器生产环境配置示例
# 此配置针对生产环境进行了优化

[server]
host = 0.0.0.0
port = 8889
debug = false
workers = 8
timeout = 60

[database]
db_file = /data/slave_server.db
backup_enabled = true
backup_interval = 1800
pool_size = 20
query_timeout = 30

[virtualhere]
server_port = 7575
binary_path = /usr/local/bin/vhusbd
auto_start = true
max_devices = 100
scan_interval = 5
config_file = /etc/vhusbd.conf
enable_device_filter = true

[master]
server_url = https://master.omnilink.local:8888
auth_token = prod-auth-token-change-me
heartbeat_interval = 15
retry_count = 5
timeout = 30
ssl_verify = true
batch_size = 100
batch_timeout = 60

[logging]
log_level = WARNING
log_file = /var/log/omnilink/slave_server.log
max_size = 52428800
backup_count = 10
log_format = structured
console_output = false
file_output = true

[monitoring]
enabled = true
performance_enabled = true
health_check_interval = 30
metrics_port = 9100
sample_interval = 10
history_size = 2880
cpu_threshold = 70.0
memory_threshold = 75.0
disk_threshold = 80.0

[security]
api_key = prod-api-key-change-me
cors_enabled = false
cors_origins = https://admin.omnilink.local
rate_limit = 1000/hour
api_auth_enabled = true
jwt_secret = prod-jwt-secret-change-me
session_timeout = 7200

[cache]
redis_url = redis://redis-cluster:6379/0
enabled = true
ttl = 600
key_prefix = omnilink:prod:slave:
max_connections = 50

[performance]
sample_interval = 10
history_size = 2880
cpu_warning = 70.0
cpu_critical = 90.0
memory_warning = 75.0
memory_critical = 90.0
disk_warning = 80.0
disk_critical = 90.0

[development]
debug_mode = false
hot_reload = false
profiling = false
verbose_errors = false
test_mode = false

[alerts]
enabled = true
smtp_server = smtp.company.com
smtp_port = 587
smtp_username = <EMAIL>
smtp_password = smtp-password-change-me
from_email = <EMAIL>
to_emails = <EMAIL>,<EMAIL>
webhook_url = https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
alert_interval = 600

[backup]
enabled = true
interval = 21600
retention_days = 30
backup_dir = /backup/omnilink
compress = true
filename_format = slave_backup_%Y%m%d_%H%M%S.tar.gz
