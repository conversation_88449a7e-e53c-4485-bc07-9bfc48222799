# OmniLink从服务器配置文件模板
# 复制此文件为 slave_server.ini 并修改相应的值

[server]
# 服务器监听地址
host = 0.0.0.0

# 服务器监听端口
port = 8889

# 调试模式（生产环境请设置为false）
debug = false

# 工作进程数量
workers = 4

# 请求超时时间（秒）
timeout = 30

[database]
# 数据库文件路径
db_file = data/slave_server.db

# 是否启用数据库备份
backup_enabled = true

# 数据库备份间隔（秒）
backup_interval = 3600

# 数据库连接池大小
pool_size = 10

# 数据库查询超时（秒）
query_timeout = 30

[virtualhere]
# VirtualHere服务器端口
server_port = 7575

# VirtualHere二进制文件路径 (auto=自动检测架构)
binary_path = auto

# 是否自动启动VirtualHere服务器
auto_start = true

# 最大设备数量
max_devices = 50

# 设备扫描间隔（秒）
scan_interval = 10

# VirtualHere配置文件路径
config_file = config/vhusbd.conf

# 是否启用设备过滤
enable_device_filter = false

# 设备过滤规则（逗号分隔的设备ID）
device_filter =

# 是否优先使用本地二进制文件
prefer_local_binary = true

# 系统架构 (auto=自动检测)
architecture = auto

[master]
# 主服务器URL
server_url = 

# 认证令牌
auth_token = 

# 心跳间隔（秒）
heartbeat_interval = 30

# 重试次数
retry_count = 3

# 连接超时（秒）
timeout = 10

# 是否启用SSL验证
ssl_verify = true

# 批量上报大小
batch_size = 50

# 批量上报超时（秒）
batch_timeout = 30

[logging]
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
log_level = INFO

# 日志文件路径
log_file = logs/slave_server.log

# 日志文件最大大小（字节）
max_size = 10485760

# 日志备份文件数量
backup_count = 5

# 日志格式 (standard, structured, detailed)
log_format = standard

# 是否输出到控制台
console_output = true

# 是否输出到文件
file_output = true

[monitoring]
# 是否启用监控
enabled = true

# 是否启用性能监控
performance_enabled = true

# 健康检查间隔（秒）
health_check_interval = 60

# 指标端口
metrics_port = 9100

# 性能数据采样间隔（秒）
sample_interval = 5

# 性能数据历史大小
history_size = 720

# CPU使用率阈值（百分比）
cpu_threshold = 80.0

# 内存使用率阈值（百分比）
memory_threshold = 80.0

# 磁盘使用率阈值（百分比）
disk_threshold = 85.0

[security]
# API密钥
api_key = 

# 是否启用CORS
cors_enabled = true

# CORS允许的源
cors_origins = *

# 请求频率限制
rate_limit = 100/hour

# 是否启用API认证
api_auth_enabled = false

# JWT密钥
jwt_secret = 

# 会话超时（秒）
session_timeout = 3600

[cache]
# Redis连接URL
redis_url = redis://redis:6379/0

# 是否启用缓存
enabled = true

# 缓存TTL（秒）
ttl = 300

# 缓存键前缀
key_prefix = omnilink:slave:

# 最大连接数
max_connections = 10

[performance]
# 性能监控采样间隔（秒）
sample_interval = 5

# 历史数据保留大小
history_size = 720

# CPU警告阈值（百分比）
cpu_warning = 80.0

# CPU严重阈值（百分比）
cpu_critical = 95.0

# 内存警告阈值（百分比）
memory_warning = 80.0

# 内存严重阈值（百分比）
memory_critical = 95.0

# 磁盘警告阈值（百分比）
disk_warning = 85.0

# 磁盘严重阈值（百分比）
disk_critical = 95.0

[development]
# 开发模式
debug_mode = false

# 热重载
hot_reload = false

# 性能分析
profiling = false

# 详细错误信息
verbose_errors = false

# 测试模式
test_mode = false

[alerts]
# 是否启用告警
enabled = false

# 邮件服务器
smtp_server = 

# 邮件端口
smtp_port = 587

# 邮件用户名
smtp_username = 

# 邮件密码
smtp_password = 

# 发件人邮箱
from_email = 

# 收件人邮箱（逗号分隔）
to_emails = 

# Webhook URL
webhook_url = 

# 告警间隔（秒）
alert_interval = 300

[backup]
# 是否启用自动备份
enabled = true

# 备份间隔（秒）
interval = 86400

# 备份保留天数
retention_days = 7

# 备份目录
backup_dir = backups

# 是否压缩备份
compress = true

# 备份文件名格式
filename_format = backup_%Y%m%d_%H%M%S.tar.gz
