# Docker环境变量配置示例
# 复制此文件为 .env 并修改相应的值

# ===========================================
# 基础服务配置
# ===========================================

# 从服务器配置
SLAVE_SERVER_HOST=0.0.0.0
SLAVE_SERVER_PORT=8889
FLASK_ENV=production
FLASK_DEBUG=False

# VirtualHere配置
VH_SERVER_PORT=7575
VH_BINARY_PATH=/app/vhusbd
VH_AUTO_START=True
VH_MAX_DEVICES=50

# 主服务器配置
MASTER_SERVER_URL=http://master-server:8888
MASTER_AUTH_TOKEN=your-auth-token-here
MASTER_HEARTBEAT_INTERVAL=30
MASTER_TIMEOUT=10

# ===========================================
# 数据库配置
# ===========================================

DB_FILE=data/slave_server.db
DB_BACKUP_ENABLED=True
DB_BACKUP_INTERVAL=3600

# ===========================================
# 日志配置
# ===========================================

LOG_LEVEL=INFO
LOG_FILE=logs/slave_server.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# ===========================================
# 监控配置
# ===========================================

ENABLE_MONITORING=True
ENABLE_PERFORMANCE_MONITOR=True
HEALTH_CHECK_INTERVAL=60
METRICS_PORT=9100

# 性能监控阈值
PERF_CPU_THRESHOLD=80.0
PERF_MEMORY_THRESHOLD=80.0
PERF_SAMPLE_INTERVAL=5
PERF_HISTORY_SIZE=720

# ===========================================
# 安全配置
# ===========================================

API_KEY=your-api-key-here
CORS_ENABLED=True
CORS_ORIGINS=*
RATE_LIMIT=100/hour

# ===========================================
# 缓存配置
# ===========================================

REDIS_URL=redis://redis:6379/0
CACHE_ENABLED=True
CACHE_TTL=300

# ===========================================
# 开发配置
# ===========================================

DEBUG_MODE=False
HOT_RELOAD=False
ENABLE_PROFILING=False

# ===========================================
# Grafana配置
# ===========================================

GRAFANA_PASSWORD=admin123

# ===========================================
# 时区配置
# ===========================================

TZ=Asia/Shanghai

# ===========================================
# 网络配置
# ===========================================

# Docker网络子网
DOCKER_SUBNET=172.20.0.0/16

# ===========================================
# 存储配置
# ===========================================

# 数据目录
DATA_DIR=./data
LOGS_DIR=./logs
CONFIG_DIR=./config
BACKUP_DIR=./backups

# ===========================================
# 资源限制
# ===========================================

# 从服务器资源限制
SLAVE_SERVER_CPU_LIMIT=2.0
SLAVE_SERVER_MEMORY_LIMIT=1G
SLAVE_SERVER_CPU_RESERVATION=0.5
SLAVE_SERVER_MEMORY_RESERVATION=256M

# Redis资源限制
REDIS_CPU_LIMIT=0.5
REDIS_MEMORY_LIMIT=512M
REDIS_CPU_RESERVATION=0.1
REDIS_MEMORY_RESERVATION=64M

# 监控服务资源限制
MONITORING_CPU_LIMIT=1.0
MONITORING_MEMORY_LIMIT=512M
MONITORING_CPU_RESERVATION=0.2
MONITORING_MEMORY_RESERVATION=128M

# ===========================================
# 健康检查配置
# ===========================================

HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_START_PERIOD=30s

# ===========================================
# 备份配置
# ===========================================

BACKUP_ENABLED=True
BACKUP_INTERVAL=86400
BACKUP_RETENTION_DAYS=7
BACKUP_COMPRESS=True

# ===========================================
# 告警配置
# ===========================================

ALERTS_ENABLED=False
SMTP_SERVER=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-smtp-password
FROM_EMAIL=<EMAIL>
TO_EMAILS=<EMAIL>,<EMAIL>
WEBHOOK_URL=https://hooks.slack.com/your-webhook-url
ALERT_INTERVAL=300

# ===========================================
# SSL/TLS配置
# ===========================================

SSL_ENABLED=False
SSL_CERT_FILE=
SSL_KEY_FILE=
SSL_VERIFY=True

# ===========================================
# 扩展配置
# ===========================================

# 自定义插件目录
PLUGINS_DIR=./plugins

# 自定义脚本目录
SCRIPTS_DIR=./scripts

# 临时文件目录
TEMP_DIR=./tmp

# ===========================================
# 调试配置
# ===========================================

# 是否启用详细错误信息
VERBOSE_ERRORS=False

# 是否启用SQL调试
SQL_DEBUG=False

# 是否启用网络调试
NETWORK_DEBUG=False

# 是否启用设备调试
DEVICE_DEBUG=False
