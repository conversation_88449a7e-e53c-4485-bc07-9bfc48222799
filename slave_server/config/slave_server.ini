# OmniLink轻量化从服务器配置文件

[server]
host = 0.0.0.0
port = 8890

[virtualhere]
server_port = 7575
binary_path = /mnt/e/key/sever/slave_server/virtualhere/chake/online/VirtualHere/VirtualHere/Server/4.3.3/vhusbdx86_64

[logging]
log_level = INFO
log_file = logs/slave_server.log

[master]
# 主服务器配置
# 支持多种环境的URL配置
# 开发环境: http://localhost:8000
# 容器环境: http://main_server:8000
# 生产环境: http://*************:8000
# WSL测试环境配置
url = http://localhost:8000

[network]
# 网络配置
# 心跳间隔（秒）
heartbeat_interval = 30
# 连接超时（秒）
connection_timeout = 10
# 重试次数
retry_count = 3
