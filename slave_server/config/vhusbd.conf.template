# VirtualHere USB服务器配置文件模板
# 复制此文件为 vhusbd.conf 并根据需要修改

# 服务器名称
ServerName=OmniLink从服务器

# 监听端口
Port=7575

# 最大客户端连接数
MaxClients=10

# 日志级别 (0=无, 1=错误, 2=警告, 3=信息, 4=调试)
LogLevel=2

# 日志文件路径
LogFile=logs/vhusbd.log

# 是否启用SSL
UseSSL=false

# SSL证书文件路径
SSLCertFile=

# SSL私钥文件路径
SSLKeyFile=

# 是否要求客户端认证
RequireAuth=false

# 用户名密码文件路径
AuthFile=

# 设备过滤规则
# 格式: DeviceNicknames=设备ID,昵称;设备ID,昵称
DeviceNicknames=

# 隐藏的设备（不显示给客户端）
# 格式: HiddenDevices=设备ID,设备ID
HiddenDevices=

# 禁用的设备（不允许连接）
# 格式: DisabledDevices=设备ID,设备ID
DisabledDevices=

# 自动共享所有USB设备
AutoShare=true

# 共享特定设备类型
# 格式: ShareClass=类别代码,类别代码
# 常见类别: 03=HID设备, 08=存储设备, 09=集线器, 0A=数据接口
ShareClass=

# 不共享的设备类型
# 格式: DontShareClass=类别代码,类别代码
DontShareClass=09

# 共享特定厂商的设备
# 格式: ShareVendor=厂商ID,厂商ID
ShareVendor=

# 不共享特定厂商的设备
# 格式: DontShareVendor=厂商ID,厂商ID
DontShareVendor=

# 共享特定产品
# 格式: ShareProduct=厂商ID.产品ID,厂商ID.产品ID
ShareProduct=

# 不共享特定产品
# 格式: DontShareProduct=厂商ID.产品ID,厂商ID.产品ID
DontShareProduct=

# 设备连接回调脚本
OnClientConnect=

# 设备断开回调脚本
OnClientDisconnect=

# 设备使用回调脚本
OnDeviceUse=

# 设备释放回调脚本
OnDeviceUnuse=

# 网络接口绑定
# 留空表示绑定所有接口
BindToInterface=

# 客户端超时时间（秒）
ClientTimeout=300

# 设备枚举间隔（毫秒）
EnumerationInterval=1000

# 是否启用即插即用
EnablePnP=true

# 是否启用设备热插拔
EnableHotplug=true

# 缓冲区大小（字节）
BufferSize=65536

# 传输超时时间（毫秒）
TransferTimeout=5000

# 重试次数
RetryCount=3

# 是否启用压缩
EnableCompression=false

# 压缩级别（1-9）
CompressionLevel=6

# 是否启用加密
EnableEncryption=false

# 加密算法
EncryptionAlgorithm=AES256

# 是否启用统计信息
EnableStats=true

# 统计信息更新间隔（秒）
StatsInterval=60

# 最大传输速度（KB/s，0表示无限制）
MaxTransferRate=0

# 设备描述符缓存大小
DescriptorCacheSize=1024

# 是否启用设备重置
EnableDeviceReset=true

# 设备重置超时时间（秒）
DeviceResetTimeout=10

# 是否启用设备电源管理
EnablePowerManagement=false

# 电源管理超时时间（秒）
PowerManagementTimeout=300

# 是否启用设备监控
EnableDeviceMonitoring=true

# 设备监控间隔（秒）
DeviceMonitoringInterval=30

# 错误重试间隔（毫秒）
ErrorRetryInterval=1000

# 最大错误重试次数
MaxErrorRetries=5

# 是否启用调试模式
DebugMode=false

# 调试输出文件
DebugFile=logs/vhusbd_debug.log

# 是否启用性能监控
EnablePerformanceMonitoring=false

# 性能监控间隔（秒）
PerformanceMonitoringInterval=60

# 性能日志文件
PerformanceLogFile=logs/vhusbd_performance.log
