# OmniLink从服务器开发环境配置示例
# 此配置针对开发环境进行了优化

[server]
host = 0.0.0.0
port = 8889
debug = true
workers = 2
timeout = 30

[database]
db_file = dev-data/slave_server.db
backup_enabled = false
backup_interval = 3600
pool_size = 5
query_timeout = 10

[virtualhere]
server_port = 7575
binary_path = ./vhusbd
auto_start = true
max_devices = 20
scan_interval = 15
config_file = config/vhusbd.conf
enable_device_filter = false

[master]
server_url = http://localhost:8888
auth_token =
heartbeat_interval = 60
retry_count = 2
timeout = 10
ssl_verify = false
batch_size = 10
batch_timeout = 30

[logging]
log_level = DEBUG
log_file = dev-logs/slave_server.log
max_size = 5242880
backup_count = 3
log_format = detailed
console_output = true
file_output = true

[monitoring]
enabled = true
performance_enabled = true
health_check_interval = 120
metrics_port = 9100
sample_interval = 5
history_size = 720
cpu_threshold = 90.0
memory_threshold = 90.0
disk_threshold = 95.0

[security]
api_key =
cors_enabled = true
cors_origins = *
rate_limit = 10000/hour
api_auth_enabled = false
jwt_secret =
session_timeout = 3600

[cache]
redis_url = redis://localhost:6379/1
enabled = true
ttl = 60
key_prefix = omnilink:dev:slave:
max_connections = 5

[performance]
sample_interval = 5
history_size = 720
cpu_warning = 90.0
cpu_critical = 98.0
memory_warning = 90.0
memory_critical = 98.0
disk_warning = 95.0
disk_critical = 99.0

[development]
debug_mode = true
hot_reload = true
profiling = true
verbose_errors = true
test_mode = false

[alerts]
enabled = false
smtp_server = 
smtp_port = 587
smtp_username = 
smtp_password = 
from_email = 
to_emails = 
webhook_url = 
alert_interval = 60

[backup]
enabled = false
interval = 86400
retention_days = 3
backup_dir = dev-backups
compress = false
filename_format = dev_backup_%Y%m%d_%H%M%S.tar.gz
