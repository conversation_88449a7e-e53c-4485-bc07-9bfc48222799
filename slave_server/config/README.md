# OmniLink从服务器配置文件说明

## 配置文件概述

本目录包含OmniLink从服务器的各种配置文件模板和示例，用于不同的部署环境和使用场景。

## 配置文件列表

### 主配置文件

- **`slave_server.ini.template`** - 主配置文件模板，包含所有可配置选项
- **`production.ini.example`** - 生产环境配置示例
- **`development.ini.example`** - 开发环境配置示例

### VirtualHere配置

- **`vhusbd.conf.template`** - VirtualHere服务器配置模板

### 环境变量配置

- **`docker.env.example`** - Docker环境变量配置示例
- **`.env.template`** - 环境变量模板（项目根目录）

## 配置文件使用方法

### 1. 基本配置

```bash
# 复制主配置文件模板
cp config/slave_server.ini.template config/slave_server.ini

# 复制VirtualHere配置模板
cp config/vhusbd.conf.template config/vhusbd.conf

# 根据需要修改配置文件
vim config/slave_server.ini
vim config/vhusbd.conf
```

### 2. 环境特定配置

#### 开发环境
```bash
cp config/development.ini.example config/slave_server.ini
```

#### 生产环境
```bash
cp config/production.ini.example config/slave_server.ini
```

### 3. Docker部署

```bash
# 复制Docker环境变量配置
cp config/docker.env.example .env

# 修改环境变量
vim .env

# 使用docker-compose启动
docker-compose up -d
```

## 配置节说明

### [server] - 服务器配置

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| host | 监听地址 | 0.0.0.0 | 0.0.0.0 |
| port | 监听端口 | 8889 | 8889 |
| debug | 调试模式 | false | true |
| workers | 工作进程数 | 4 | 8 |
| timeout | 请求超时 | 30 | 60 |

### [database] - 数据库配置

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| db_file | 数据库文件路径 | data/slave_server.db | /data/slave_server.db |
| backup_enabled | 启用备份 | true | true |
| backup_interval | 备份间隔(秒) | 3600 | 1800 |
| pool_size | 连接池大小 | 10 | 20 |

### [virtualhere] - VirtualHere配置

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| server_port | VH服务端口 | 7575 | 7575 |
| binary_path | VH二进制路径 | /app/vhusbd | /usr/local/bin/vhusbd |
| auto_start | 自动启动 | true | true |
| max_devices | 最大设备数 | 50 | 100 |

### [master] - 主服务器配置

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| server_url | 主服务器URL | - | https://master.example.com:8888 |
| auth_token | 认证令牌 | - | your-auth-token |
| heartbeat_interval | 心跳间隔(秒) | 30 | 15 |
| retry_count | 重试次数 | 3 | 5 |

### [logging] - 日志配置

| 参数 | 说明 | 默认值 | 可选值 |
|------|------|--------|--------|
| log_level | 日志级别 | INFO | DEBUG, INFO, WARNING, ERROR, CRITICAL |
| log_format | 日志格式 | standard | standard, structured, detailed |
| max_size | 最大文件大小 | 10485760 | 字节数 |
| backup_count | 备份文件数 | 5 | 数字 |

### [monitoring] - 监控配置

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| enabled | 启用监控 | true | true |
| performance_enabled | 启用性能监控 | true | true |
| health_check_interval | 健康检查间隔(秒) | 60 | 30 |
| cpu_threshold | CPU阈值(%) | 80.0 | 70.0 |

### [security] - 安全配置

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| api_key | API密钥 | - | your-api-key |
| cors_enabled | 启用CORS | true | false |
| cors_origins | CORS源 | * | https://admin.example.com |
| rate_limit | 频率限制 | 100/hour | 1000/hour |

## 环境变量优先级

配置的优先级顺序（从高到低）：

1. **环境变量** - 运行时环境变量
2. **配置文件** - slave_server.ini文件
3. **默认值** - 代码中的默认值

## 配置验证

系统启动时会自动验证配置的有效性：

```bash
# 验证配置文件
python -c "from utils.config_manager import ConfigManager; cm = ConfigManager(); print(cm.validate_env_config())"
```

## 常见配置场景

### 1. 高性能生产环境

```ini
[server]
workers = 16
timeout = 60

[database]
pool_size = 50

[monitoring]
sample_interval = 5
cpu_threshold = 60.0
memory_threshold = 70.0

[logging]
log_level = WARNING
log_format = structured
```

### 2. 开发调试环境

```ini
[server]
debug = true
workers = 1

[logging]
log_level = DEBUG
log_format = detailed
console_output = true

[development]
debug_mode = true
verbose_errors = true
profiling = true
```

### 3. 安全生产环境

```ini
[security]
api_auth_enabled = true
cors_enabled = false
rate_limit = 500/hour

[master]
ssl_verify = true

[alerts]
enabled = true
```

## 故障排除

### 配置文件不存在
```bash
# 检查配置文件是否存在
ls -la config/slave_server.ini

# 如果不存在，复制模板
cp config/slave_server.ini.template config/slave_server.ini
```

### 权限问题
```bash
# 检查配置文件权限
ls -la config/

# 修复权限
chmod 644 config/*.ini
chmod 644 config/*.conf
```

### 配置验证失败
```bash
# 检查配置语法
python -c "import configparser; c = configparser.ConfigParser(); c.read('config/slave_server.ini'); print('配置文件语法正确')"
```

## 配置备份

建议定期备份配置文件：

```bash
# 创建配置备份
tar -czf config_backup_$(date +%Y%m%d).tar.gz config/

# 恢复配置
tar -xzf config_backup_20250127.tar.gz
```

## 更多信息

- 查看环境变量配置：`GET /api/config/env`
- 查看有效配置：`GET /api/config/effective`
- 在线配置管理：访问Web管理界面的配置页面
