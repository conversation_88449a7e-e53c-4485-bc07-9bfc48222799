# -*- coding: utf-8 -*-
"""
命令日志数据访问对象
提供命令日志的CRUD操作
"""

import json
from datetime import datetime
from typing import List, Dict, Optional
from .models import CommandLog, database
from utils.logger import get_logger

logger = get_logger('command_dao')


class CommandDAO:
    """命令日志数据访问对象"""
    
    @staticmethod
    def create_command_log(command_id: str, command_type: str, command_params: Dict = None) -> Optional[CommandLog]:
        """
        创建命令日志
        
        Args:
            command_id: 命令ID
            command_type: 命令类型
            command_params: 命令参数
        
        Returns:
            CommandLog: 创建的命令日志对象，失败返回None
        """
        try:
            with database.atomic():
                params_json = json.dumps(command_params) if command_params else None
                
                command_log = CommandLog.create(
                    command_id=command_id,
                    command_type=command_type,
                    command_params=params_json,
                    status='pending'
                )
                
                logger.info(f"命令日志创建成功: {command_id}")
                return command_log
                
        except Exception as e:
            logger.error(f"创建命令日志失败: {e}")
            return None
    
    @staticmethod
    def get_command_log(command_id: str) -> Optional[CommandLog]:
        """
        获取命令日志
        
        Args:
            command_id: 命令ID
        
        Returns:
            CommandLog: 命令日志对象，不存在返回None
        """
        try:
            return CommandLog.get(CommandLog.command_id == command_id)
        except CommandLog.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"获取命令日志失败: {e}")
            return None
    
    @staticmethod
    def update_command_status(command_id: str, status: str, result: str = None, error_message: str = None) -> bool:
        """
        更新命令状态
        
        Args:
            command_id: 命令ID
            status: 新状态
            result: 执行结果
            error_message: 错误信息
        
        Returns:
            bool: 更新是否成功
        """
        try:
            with database.atomic():
                update_data = {
                    'status': status,
                    'complete_time': datetime.now()
                }
                
                if result is not None:
                    update_data['result'] = result
                
                if error_message is not None:
                    update_data['error_message'] = error_message
                
                query = CommandLog.update(**update_data).where(CommandLog.command_id == command_id)
                rows_updated = query.execute()
                
                if rows_updated > 0:
                    logger.info(f"命令状态更新成功: {command_id} -> {status}")
                    return True
                else:
                    logger.warning(f"命令不存在: {command_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"更新命令状态失败: {e}")
            return False
    
    @staticmethod
    def mark_command_success(command_id: str, result: str) -> bool:
        """
        标记命令执行成功
        
        Args:
            command_id: 命令ID
            result: 执行结果
        
        Returns:
            bool: 更新是否成功
        """
        return CommandDAO.update_command_status(command_id, 'success', result)
    
    @staticmethod
    def mark_command_failed(command_id: str, error_message: str) -> bool:
        """
        标记命令执行失败
        
        Args:
            command_id: 命令ID
            error_message: 错误信息
        
        Returns:
            bool: 更新是否成功
        """
        return CommandDAO.update_command_status(command_id, 'failed', error_message=error_message)
    
    @staticmethod
    def get_pending_commands() -> List[CommandLog]:
        """
        获取待执行的命令
        
        Returns:
            List[CommandLog]: 待执行命令列表
        """
        try:
            return list(CommandLog.select().where(CommandLog.status == 'pending').order_by(CommandLog.create_time))
        except Exception as e:
            logger.error(f"获取待执行命令失败: {e}")
            return []
    
    @staticmethod
    def get_commands_by_type(command_type: str, limit: int = 100) -> List[CommandLog]:
        """
        根据类型获取命令
        
        Args:
            command_type: 命令类型
            limit: 限制数量
        
        Returns:
            List[CommandLog]: 命令列表
        """
        try:
            return list(CommandLog.select()
                       .where(CommandLog.command_type == command_type)
                       .order_by(CommandLog.create_time.desc())
                       .limit(limit))
        except Exception as e:
            logger.error(f"获取命令列表失败: {e}")
            return []
    
    @staticmethod
    def get_recent_commands(limit: int = 50) -> List[CommandLog]:
        """
        获取最近的命令
        
        Args:
            limit: 限制数量
        
        Returns:
            List[CommandLog]: 命令列表
        """
        try:
            return list(CommandLog.select()
                       .order_by(CommandLog.create_time.desc())
                       .limit(limit))
        except Exception as e:
            logger.error(f"获取最近命令失败: {e}")
            return []
    
    @staticmethod
    def get_command_statistics() -> Dict:
        """
        获取命令统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            total = CommandLog.select().count()
            pending = CommandLog.select().where(CommandLog.status == 'pending').count()
            success = CommandLog.select().where(CommandLog.status == 'success').count()
            failed = CommandLog.select().where(CommandLog.status == 'failed').count()
            
            return {
                'total': total,
                'pending': pending,
                'success': success,
                'failed': failed,
                'success_rate': (success / total * 100) if total > 0 else 0
            }
        except Exception as e:
            logger.error(f"获取命令统计失败: {e}")
            return {
                'total': 0,
                'pending': 0,
                'success': 0,
                'failed': 0,
                'success_rate': 0
            }
    
    @staticmethod
    def cleanup_old_commands(days: int = 30) -> int:
        """
        清理旧命令日志
        
        Args:
            days: 保留天数
        
        Returns:
            int: 清理的记录数
        """
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with database.atomic():
                query = CommandLog.delete().where(CommandLog.create_time < cutoff_date)
                deleted_count = query.execute()
                
                logger.info(f"清理旧命令日志完成，删除 {deleted_count} 条记录")
                return deleted_count
                
        except Exception as e:
            logger.error(f"清理旧命令日志失败: {e}")
            return 0
    
    @staticmethod
    def parse_command_params(command_log: CommandLog) -> Dict:
        """
        解析命令参数
        
        Args:
            command_log: 命令日志对象
        
        Returns:
            Dict: 解析后的参数字典
        """
        try:
            if command_log.command_params:
                return json.loads(command_log.command_params)
            return {}
        except Exception as e:
            logger.error(f"解析命令参数失败: {e}")
            return {}
