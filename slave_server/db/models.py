# -*- coding: utf-8 -*-
"""
数据库模型定义
使用Peewee ORM定义数据表结构
"""

import os
from typing import Dict, List, Any, Optional, Union, ClassVar
from datetime import datetime
from peewee import *
from utils.logger import get_logger

logger = get_logger('models')

# 数据库文件路径
db_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
os.makedirs(db_dir, exist_ok=True)
db_path = os.path.join(db_dir, 'slave_server.db')

# 创建数据库连接
database = SqliteDatabase(db_path)


class BaseModel(Model):
    """基础模型类"""
    
    class Meta:
        database = database


class Device(BaseModel):
    """设备模型"""
    
    id = AutoField(primary_key=True)
    device_uuid = CharField(unique=True, max_length=64, help_text="设备唯一标识")
    device_address = CharField(max_length=32, help_text="设备地址")
    device_serial = CharField(max_length=128, null=True, help_text="设备序列号")
    connection_uuid = CharField(max_length=64, null=True, help_text="连接UUID")
    vendor = CharField(max_length=128, null=True, help_text="供应商名称")
    vendor_id = CharField(max_length=16, null=True, help_text="供应商ID")
    product = CharField(max_length=128, null=True, help_text="产品名称")
    product_id = CharField(max_length=16, null=True, help_text="产品ID")
    nick_name = CharField(max_length=128, null=True, help_text="设备昵称")
    description = TextField(null=True, help_text="设备描述")
    status = CharField(max_length=32, default='available', help_text="设备状态")
    shown = BooleanField(default=True, help_text="是否显示")
    disable = BooleanField(default=False, help_text="是否禁用")
    last_seen = DateTimeField(default=datetime.now, help_text="最后检测时间")
    create_time = DateTimeField(default=datetime.now, help_text="创建时间")
    
    class Meta:
        table_name = 'devices'
        indexes = (
            (('device_uuid',), True),  # 唯一索引
            (('device_address',), False),
            (('status',), False),
        )
    
    def to_dict(self) -> None:
        """转换为字典"""
        return {
            'id': self.id,
            'device_uuid': self.device_uuid,
            'device_address': self.device_address,
            'device_serial': self.device_serial,
            'connection_uuid': self.connection_uuid,
            'vendor': self.vendor,
            'vendor_id': self.vendor_id,
            'product': self.product,
            'product_id': self.product_id,
            'nick_name': self.nick_name,
            'description': self.description,
            'status': self.status,
            'shown': self.shown,
            'disable': self.disable,
            'last_seen': self.last_seen.isoformat() if self.last_seen else None,
            'create_time': self.create_time.isoformat() if self.create_time else None
        }


class Config(BaseModel):
    """配置模型"""
    
    id = AutoField(primary_key=True)
    code = CharField(unique=True, max_length=64, help_text="配置键")
    type = CharField(max_length=32, help_text="配置类型")
    value = TextField(null=True, help_text="配置值")
    description = CharField(max_length=256, null=True, help_text="配置描述")
    weight = IntegerField(default=0, help_text="权重")
    pid = IntegerField(default=0, help_text="父配置ID")
    create_time = DateTimeField(default=datetime.now, help_text="创建时间")
    
    class Meta:
        table_name = 'configs'
        indexes = (
            (('code',), True),  # 唯一索引
            (('type',), False),
            (('pid',), False),
        )
    
    def to_dict(self) -> None:
        """转换为字典"""
        return {
            'id': self.id,
            'code': self.code,
            'type': self.type,
            'value': self.value,
            'description': self.description,
            'weight': self.weight,
            'pid': self.pid,
            'create_time': self.create_time.isoformat() if self.create_time else None
        }


class CommandLog(BaseModel):
    """命令日志模型"""
    
    id = AutoField(primary_key=True)
    command_id = CharField(unique=True, max_length=64, help_text="命令ID")
    command_type = CharField(max_length=64, help_text="命令类型")
    command_params = TextField(null=True, help_text="命令参数(JSON)")
    status = CharField(max_length=32, default='pending', help_text="执行状态")
    result = TextField(null=True, help_text="执行结果")
    error_message = TextField(null=True, help_text="错误信息")
    create_time = DateTimeField(default=datetime.now, help_text="创建时间")
    complete_time = DateTimeField(null=True, help_text="完成时间")
    
    class Meta:
        table_name = 'command_logs'
        indexes = (
            (('command_id',), True),  # 唯一索引
            (('command_type',), False),
            (('status',), False),
            (('create_time',), False),
        )
    
    def to_dict(self) -> None:
        """转换为字典"""
        return {
            'id': self.id,
            'command_id': self.command_id,
            'command_type': self.command_type,
            'command_params': self.command_params,
            'status': self.status,
            'result': self.result,
            'error_message': self.error_message,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'complete_time': self.complete_time.isoformat() if self.complete_time else None
        }


# 所有模型类列表
MODELS = [Device, Config, CommandLog]


def create_tables() -> None:
    """创建数据表"""
    try:
        database.connect()
        database.create_tables(MODELS, safe=True)
        logger.info("数据表创建完成")
    except Exception as e:
        logger.error(f"创建数据表失败: {e}")
        raise
    finally:
        if not database.is_closed():
            database.close()


def drop_tables() -> None:
    """删除数据表"""
    try:
        database.connect()
        database.drop_tables(MODELS, safe=True)
        logger.info("数据表删除完成")
    except Exception as e:
        logger.error(f"删除数据表失败: {e}")
        raise
    finally:
        if not database.is_closed():
            database.close()


def init_database() -> None:
    """初始化数据库"""
    try:
        create_tables()
        _insert_default_configs()
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


def _insert_default_configs() -> None:
    """插入默认配置数据"""
    try:
        database.connect()

        # 检查是否已有配置数据
        if Config.select().count() > 0:
            return

        # 插入默认配置
        default_configs = [
            {'code': 'master_server_url', 'type': 'string', 'value': 'http://localhost:8888', 'description': '主服务器URL'},
            {'code': 'server_uuid', 'type': 'string', 'value': '', 'description': '从服务器UUID'},
            {'code': 'auth_token', 'type': 'string', 'value': '', 'description': '认证令牌'},
            {'code': 'device_scan_interval', 'type': 'integer', 'value': '3', 'description': '设备扫描间隔(秒)'},
            {'code': 'heartbeat_interval', 'type': 'integer', 'value': '30', 'description': '心跳间隔(秒)'},
            {'code': 'command_poll_interval', 'type': 'integer', 'value': '5', 'description': '命令轮询间隔(秒)'},
            {'code': 'vh_server_port', 'type': 'integer', 'value': '7575', 'description': 'VirtualHere服务器端口'},
        ]

        for config_data in default_configs:
            Config.create(**config_data)

        logger.info("默认配置数据插入完成")

    except Exception as e:
        logger.error(f"插入默认配置失败: {e}")
    finally:
        if not database.is_closed():
            database.close()
