# -*- coding: utf-8 -*-
"""
设备数据访问对象
提供设备信息的CRUD操作
"""

from datetime import datetime
from typing import List, Dict, Optional, Any, Union, Tuple
from .models import Device, database
from utils.logger import get_logger

logger = get_logger('device_dao')


class DeviceDAO:
    """设备数据访问对象"""
    
    @staticmethod
    def create_device(device_data: Dict[str, Any]) -> Optional[Device]:
        """
        创建设备记录
        
        Args:
            device_data: 设备数据字典
        
        Returns:
            Device: 创建的设备对象，失败返回None
        """
        try:
            with database.atomic():
                device = Device.create(**device_data)
                logger.info(f"设备创建成功: {device.device_uuid}")
                return device
        except Exception as e:
            logger.error(f"创建设备失败: {e}")
            return None
    
    @staticmethod
    def get_device_by_uuid(device_uuid: str) -> Optional[Device]:
        """
        根据UUID获取设备
        
        Args:
            device_uuid: 设备UUID
        
        Returns:
            Device: 设备对象，不存在返回None
        """
        try:
            return Device.get(Device.device_uuid == device_uuid)
        except Device.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"获取设备失败: {e}")
            return None
    
    @staticmethod
    def get_device_by_address(device_address: str) -> Optional[Device]:
        """
        根据地址获取设备
        
        Args:
            device_address: 设备地址
        
        Returns:
            Device: 设备对象，不存在返回None
        """
        try:
            return Device.get(Device.device_address == device_address)
        except Device.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"获取设备失败: {e}")
            return None
    
    @staticmethod
    def get_all_devices() -> List[Device]:
        """
        获取所有设备
        
        Returns:
            List[Device]: 设备列表
        """
        try:
            return list(Device.select())
        except Exception as e:
            logger.error(f"获取设备列表失败: {e}")
            return []
    
    @staticmethod
    def get_available_devices() -> List[Device]:
        """
        获取可用设备
        
        Returns:
            List[Device]: 可用设备列表
        """
        try:
            return list(Device.select().where(
                (Device.status == 'available') & 
                (Device.shown == True) & 
                (Device.disable == False)
            ))
        except Exception as e:
            logger.error(f"获取可用设备失败: {e}")
            return []
    
    @staticmethod
    def update_device(device_uuid: str, update_data: Dict) -> bool:
        """
        更新设备信息
        
        Args:
            device_uuid: 设备UUID
            update_data: 更新数据字典
        
        Returns:
            bool: 更新是否成功
        """
        try:
            with database.atomic():
                # 添加更新时间
                update_data['last_seen'] = datetime.now()
                
                query = Device.update(**update_data).where(Device.device_uuid == device_uuid)
                rows_updated = query.execute()
                
                if rows_updated > 0:
                    logger.info(f"设备更新成功: {device_uuid}")
                    return True
                else:
                    logger.warning(f"设备不存在: {device_uuid}")
                    return False
                    
        except Exception as e:
            logger.error(f"更新设备失败: {e}")
            return False
    
    @staticmethod
    def update_device_status(device_uuid: str, status: str) -> bool:
        """
        更新设备状态
        
        Args:
            device_uuid: 设备UUID
            status: 新状态
        
        Returns:
            bool: 更新是否成功
        """
        return DeviceDAO.update_device(device_uuid, {'status': status})
    
    @staticmethod
    def delete_device(device_uuid: str) -> bool:
        """
        删除设备
        
        Args:
            device_uuid: 设备UUID
        
        Returns:
            bool: 删除是否成功
        """
        try:
            with database.atomic():
                query = Device.delete().where(Device.device_uuid == device_uuid)
                rows_deleted = query.execute()
                
                if rows_deleted > 0:
                    logger.info(f"设备删除成功: {device_uuid}")
                    return True
                else:
                    logger.warning(f"设备不存在: {device_uuid}")
                    return False
                    
        except Exception as e:
            logger.error(f"删除设备失败: {e}")
            return False
    
    @staticmethod
    def batch_update_devices(devices_data: List[Dict]) -> int:
        """
        批量更新设备
        
        Args:
            devices_data: 设备数据列表
        
        Returns:
            int: 成功更新的设备数量
        """
        success_count = 0
        
        try:
            with database.atomic():
                for device_data in devices_data:
                    device_uuid = device_data.get('device_uuid')
                    if not device_uuid:
                        continue
                    
                    # 检查设备是否存在
                    existing_device = DeviceDAO.get_device_by_uuid(device_uuid)
                    
                    if existing_device:
                        # 更新现有设备
                        if DeviceDAO.update_device(device_uuid, device_data):
                            success_count += 1
                    else:
                        # 创建新设备
                        if DeviceDAO.create_device(device_data):
                            success_count += 1
                            
        except Exception as e:
            logger.error(f"批量更新设备失败: {e}")
        
        logger.info(f"批量更新完成，成功处理 {success_count} 个设备")
        return success_count
    
    @staticmethod
    def get_device_count() -> int:
        """
        获取设备总数
        
        Returns:
            int: 设备总数
        """
        try:
            return Device.select().count()
        except Exception as e:
            logger.error(f"获取设备数量失败: {e}")
            return 0
    
    @staticmethod
    def search_devices(keyword: str) -> List[Device]:
        """
        搜索设备
        
        Args:
            keyword: 搜索关键词
        
        Returns:
            List[Device]: 匹配的设备列表
        """
        try:
            return list(Device.select().where(
                (Device.vendor.contains(keyword)) |
                (Device.product.contains(keyword)) |
                (Device.nick_name.contains(keyword)) |
                (Device.device_serial.contains(keyword))
            ))
        except Exception as e:
            logger.error(f"搜索设备失败: {e}")
            return []

    @staticmethod
    def add_device(device_data: Dict) -> bool:
        """
        添加设备（create_device的别名，用于兼容性）

        Args:
            device_data: 设备数据字典

        Returns:
            bool: 添加是否成功
        """
        device = DeviceDAO.create_device(device_data)
        return device is not None

    @staticmethod
    def get_devices_by_status(status: str) -> List[Device]:
        """
        根据状态获取设备

        Args:
            status: 设备状态

        Returns:
            List[Device]: 指定状态的设备列表
        """
        try:
            return list(Device.select().where(Device.status == status))
        except Exception as e:
            logger.error(f"根据状态获取设备失败: {e}")
            return []

    @staticmethod
    def get_devices_by_server(server_name: str) -> List[Device]:
        """
        根据服务器名称获取设备

        Args:
            server_name: 服务器名称

        Returns:
            List[Device]: 指定服务器的设备列表
        """
        try:
            return list(Device.select().where(Device.server_name == server_name))
        except Exception as e:
            logger.error(f"根据服务器获取设备失败: {e}")
            return []

    @staticmethod
    def get_devices_by_vendor(vendor_id: str) -> List[Device]:
        """
        根据厂商ID获取设备

        Args:
            vendor_id: 厂商ID

        Returns:
            List[Device]: 指定厂商的设备列表
        """
        try:
            return list(Device.select().where(Device.vendor_id == vendor_id))
        except Exception as e:
            logger.error(f"根据厂商获取设备失败: {e}")
            return []

    @staticmethod
    def update_device_last_seen(device_uuid: str) -> bool:
        """
        更新设备最后见到时间

        Args:
            device_uuid: 设备UUID

        Returns:
            bool: 更新是否成功
        """
        return DeviceDAO.update_device(device_uuid, {'last_seen': datetime.now()})

    @staticmethod
    def set_device_visibility(device_uuid: str, shown: bool) -> bool:
        """
        设置设备可见性

        Args:
            device_uuid: 设备UUID
            shown: 是否显示

        Returns:
            bool: 设置是否成功
        """
        return DeviceDAO.update_device(device_uuid, {'shown': shown})

    @staticmethod
    def set_device_enabled(device_uuid: str, enabled: bool) -> bool:
        """
        设置设备启用状态

        Args:
            device_uuid: 设备UUID
            enabled: 是否启用

        Returns:
            bool: 设置是否成功
        """
        return DeviceDAO.update_device(device_uuid, {'disable': not enabled})

    @staticmethod
    def get_device_statistics() -> Dict:
        """
        获取设备统计信息

        Returns:
            Dict: 统计信息
        """
        try:
            total_devices = Device.select().count()
            available_devices = Device.select().where(Device.status == 'available').count()
            in_use_devices = Device.select().where(Device.status == 'in_use').count()
            disabled_devices = Device.select().where(Device.disable == True).count()
            hidden_devices = Device.select().where(Device.shown == False).count()

            return {
                'total_devices': total_devices,
                'available_devices': available_devices,
                'in_use_devices': in_use_devices,
                'disabled_devices': disabled_devices,
                'hidden_devices': hidden_devices,
                'enabled_devices': total_devices - disabled_devices,
                'visible_devices': total_devices - hidden_devices
            }
        except Exception as e:
            logger.error(f"获取设备统计信息失败: {e}")
            return {}

    @staticmethod
    def cleanup_offline_devices(offline_threshold_minutes: int = 30) -> int:
        """
        清理离线设备

        Args:
            offline_threshold_minutes: 离线阈值（分钟）

        Returns:
            int: 清理的设备数量
        """
        try:
            from datetime import timedelta

            threshold_time = datetime.now() - timedelta(minutes=offline_threshold_minutes)

            with database.atomic():
                query = Device.delete().where(Device.last_seen < threshold_time)
                deleted_count = query.execute()

                logger.info(f"清理离线设备完成，删除 {deleted_count} 个设备")
                return deleted_count

        except Exception as e:
            logger.error(f"清理离线设备失败: {e}")
            return 0

    @staticmethod
    def bulk_delete_devices(device_uuids: List[str]) -> int:
        """
        批量删除设备

        Args:
            device_uuids: 设备UUID列表

        Returns:
            int: 删除的设备数量
        """
        try:
            with database.atomic():
                query = Device.delete().where(Device.device_uuid.in_(device_uuids))
                deleted_count = query.execute()

                logger.info(f"批量删除设备完成，删除 {deleted_count} 个设备")
                return deleted_count

        except Exception as e:
            logger.error(f"批量删除设备失败: {e}")
            return 0

    @staticmethod
    def get_devices_paginated(page: int = 1, page_size: int = 20) -> Dict:
        """
        分页获取设备

        Args:
            page: 页码（从1开始）
            page_size: 每页大小

        Returns:
            Dict: 分页结果
        """
        try:
            offset = (page - 1) * page_size

            total_count = Device.select().count()
            devices = list(Device.select().offset(offset).limit(page_size))

            total_pages = (total_count + page_size - 1) // page_size

            return {
                'devices': devices,
                'total_count': total_count,
                'page': page,
                'page_size': page_size,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        except Exception as e:
            logger.error(f"分页获取设备失败: {e}")
            return {
                'devices': [],
                'total_count': 0,
                'page': page,
                'page_size': page_size,
                'total_pages': 0,
                'has_next': False,
                'has_prev': False
            }
