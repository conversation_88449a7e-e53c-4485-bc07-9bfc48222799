# -*- coding: utf-8 -*-
"""
配置数据访问对象
提供配置信息的CRUD操作
"""

from typing import List, Dict, Optional, Any
from .models import Config, database
from utils.logger import get_logger

logger = get_logger('config_dao')


class ConfigDAO:
    """配置数据访问对象"""
    
    @staticmethod
    def get_config_value(code: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            code: 配置键
            default: 默认值
        
        Returns:
            配置值
        """
        try:
            config = Config.get(Config.code == code)
            value = config.value
            
            # 根据类型转换值
            if config.type == 'integer':
                return int(value) if value else default
            elif config.type == 'boolean':
                return value.lower() == 'true' if value else default
            elif config.type == 'float':
                return float(value) if value else default
            else:
                return value if value else default
                
        except Config.DoesNotExist:
            logger.warning(f"配置不存在: {code}")
            return default
        except Exception as e:
            logger.error(f"获取配置失败: {e}")
            return default
    
    @staticmethod
    def set_config_value(code: str, value: Any, config_type: str = 'string', description: str = '') -> bool:
        """
        设置配置值
        
        Args:
            code: 配置键
            value: 配置值
            config_type: 配置类型
            description: 配置描述
        
        Returns:
            bool: 设置是否成功
        """
        try:
            with database.atomic():
                # 转换值为字符串
                str_value = str(value)
                
                # 检查配置是否存在
                try:
                    config = Config.get(Config.code == code)
                    # 更新现有配置
                    config.value = str_value
                    config.type = config_type
                    if description:
                        config.description = description
                    config.save()
                    logger.info(f"配置更新成功: {code} = {str_value}")
                    
                except Config.DoesNotExist:
                    # 创建新配置
                    Config.create(
                        code=code,
                        value=str_value,
                        type=config_type,
                        description=description
                    )
                    logger.info(f"配置创建成功: {code} = {str_value}")
                
                return True
                
        except Exception as e:
            logger.error(f"设置配置失败: {e}")
            return False
    
    @staticmethod
    def get_all_configs() -> List[Dict]:
        """
        获取所有配置
        
        Returns:
            List[Dict]: 配置列表
        """
        try:
            configs = Config.select()
            return [config.to_dict() for config in configs]
        except Exception as e:
            logger.error(f"获取配置列表失败: {e}")
            return []
    
    @staticmethod
    def get_configs_by_type(config_type: str) -> List[Dict]:
        """
        根据类型获取配置
        
        Args:
            config_type: 配置类型
        
        Returns:
            List[Dict]: 配置列表
        """
        try:
            configs = Config.select().where(Config.type == config_type)
            return [config.to_dict() for config in configs]
        except Exception as e:
            logger.error(f"获取配置列表失败: {e}")
            return []
    
    @staticmethod
    def delete_config(code: str) -> bool:
        """
        删除配置
        
        Args:
            code: 配置键
        
        Returns:
            bool: 删除是否成功
        """
        try:
            with database.atomic():
                query = Config.delete().where(Config.code == code)
                rows_deleted = query.execute()
                
                if rows_deleted > 0:
                    logger.info(f"配置删除成功: {code}")
                    return True
                else:
                    logger.warning(f"配置不存在: {code}")
                    return False
                    
        except Exception as e:
            logger.error(f"删除配置失败: {e}")
            return False
    
    @staticmethod
    def batch_set_configs(configs_data: Dict[str, Any]) -> int:
        """
        批量设置配置
        
        Args:
            configs_data: 配置数据字典
        
        Returns:
            int: 成功设置的配置数量
        """
        success_count = 0
        
        try:
            with database.atomic():
                for code, value in configs_data.items():
                    if ConfigDAO.set_config_value(code, value):
                        success_count += 1
                        
        except Exception as e:
            logger.error(f"批量设置配置失败: {e}")
        
        logger.info(f"批量设置完成，成功处理 {success_count} 个配置")
        return success_count
    
    # 便捷方法：获取常用配置
    @staticmethod
    def get_master_server_url() -> str:
        """获取主服务器URL"""
        return ConfigDAO.get_config_value('master_server_url', 'http://localhost:8888')
    
    @staticmethod
    def get_server_uuid() -> str:
        """获取服务器UUID"""
        return ConfigDAO.get_config_value('server_uuid', '')
    
    @staticmethod
    def get_auth_token() -> str:
        """获取认证令牌"""
        return ConfigDAO.get_config_value('auth_token', '')
    
    @staticmethod
    def set_auth_token(token: str) -> bool:
        """设置认证令牌"""
        return ConfigDAO.set_config_value('auth_token', token, 'string', '认证令牌')
    
    @staticmethod
    def get_device_scan_interval() -> int:
        """获取设备扫描间隔"""
        return ConfigDAO.get_config_value('device_scan_interval', 3)
    
    @staticmethod
    def get_heartbeat_interval() -> int:
        """获取心跳间隔"""
        return ConfigDAO.get_config_value('heartbeat_interval', 30)
    
    @staticmethod
    def get_command_poll_interval() -> int:
        """获取命令轮询间隔"""
        return ConfigDAO.get_config_value('command_poll_interval', 5)
    
    @staticmethod
    def get_vh_server_port() -> int:
        """获取VirtualHere服务器端口"""
        return ConfigDAO.get_config_value('vh_server_port', 7575)
