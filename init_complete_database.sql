-- ===========================================
-- OmniLink 完整数据库初始化脚本
-- ===========================================

-- 设置数据库编码和时区
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- 开始事务
BEGIN;

-- ===========================================
-- 1. 基础角色数据
-- ===========================================
INSERT INTO roles (name, description, permissions) VALUES 
('超级管理员', '系统超级管理员，拥有所有权限', '["*"]'),
('管理员', '组织管理员，拥有组织内管理权限', '["read", "write", "manage_users", "manage_devices"]'),
('普通用户', '普通用户，拥有基本操作权限', '["read", "write"]'),
('新用户', '新注册用户，权限受限', '["read"]')
ON CONFLICT (name) DO NOTHING;

-- ===========================================
-- 2. 基础组织数据（集团总部）
-- ===========================================
INSERT INTO organizations (name, parent_id, level, path, description) VALUES 
('集团总部', NULL, 0, '/集团总部', '集团总部，最高级别组织')
ON CONFLICT (name) DO NOTHING;

-- ===========================================
-- 3. 基础管理员用户
-- ===========================================
-- 密码都是对应的123，例如admin123, manager123等
INSERT INTO users (username, email, hashed_password, full_name, phone, is_active, organization_id) VALUES 
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L3jzjvG4e', '系统管理员', '18800000001', true, 1),
('manager', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L3jzjvG4e', '总部管理员', '18800000002', true, 1)
ON CONFLICT (username) DO NOTHING;

-- ===========================================
-- 4. 基础用户角色关联
-- ===========================================
INSERT INTO user_role_association (user_id, role_id, assigned_by) VALUES 
(1, 1, 1), -- admin -> 超级管理员
(2, 2, 1)  -- manager -> 管理员
ON CONFLICT (user_id, role_id) DO NOTHING;

COMMIT;
