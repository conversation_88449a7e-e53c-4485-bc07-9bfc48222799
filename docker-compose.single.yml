# ================================
# OmniLink 生产环境 Docker Compose
# 版本: 3.0 - 单容器集成PostgreSQL+Redis+Python
# 创建日期: 2025-01-10
# 优化: 使用轻量级gcc替代方案，总镜像大小约400MB
# 特性: 生产级安全配置，多阶段构建优化
# ================================

services:
  # 生产环境单容器集成服务 (PostgreSQL + Redis + FastAPI)
  omnilink-prod:
    build:
      context: ./main_server/backend
      dockerfile: Dockerfile
    container_name: omnilink-prod-1
    ports:
      - "8000:8000"  # 仅对外暴露FastAPI服务端口
      # PostgreSQL和Redis仅内部通信，不对外暴露端口
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:bro2fhz12@localhost:5432/omnilink_main
      - REDIS_URL=redis://:bro2fhz12@localhost:6379
      - JWT_SECRET_KEY=omnilink-jwt-secret-key-2024
      - JWT_ALGORITHM=HS256
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
      - DEBUG=False
      - LOG_LEVEL=INFO
      - ENVIRONMENT=production
      - WORKERS=2
    volumes:
      # 持久化数据存储
      - postgres_data:/var/lib/postgresql/data
      - redis_data:/var/lib/redis
      - app_logs:/app/logs
      - supervisor_logs:/var/log/supervisor
    restart: unless-stopped
    networks:
      - omnilink-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s
    # 内存限制（单容器集成，适配1GB总内存）
    mem_limit: 768m
    memswap_limit: 768m
    # CPU限制
    cpus: '1.0'

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  supervisor_logs:
    driver: local

networks:
  omnilink-network:
    driver: bridge

# ================================
# 使用说明
# ================================
# 1. 构建并启动: docker-compose -f docker-compose.single.yml up --build -d
# 2. 查看日志: docker-compose -f docker-compose.single.yml logs -f
# 3. 停止服务: docker-compose -f docker-compose.single.yml down
# 4. 完全清理: docker-compose -f docker-compose.single.yml down -v
# 
# 服务访问:
# - Web界面: http://localhost:8000 (对外访问)
# - PostgreSQL: 仅容器内部通信 (localhost:5432, 用户: postgres, 密码: bro2fhz12)
# - Redis: 仅容器内部通信 (localhost:6379, 密码: bro2fhz12)
#
# 安全特性:
# - PostgreSQL和Redis仅监听localhost，不对外暴露端口
# - 防止数据库直接被外部攻击
# - 仅Web服务对外提供访问
#
# 镜像大小优化:
# - 原gcc:latest: 2.04GB
# - 优化后总镜像: ~400MB (节省约85%)
# ================================
