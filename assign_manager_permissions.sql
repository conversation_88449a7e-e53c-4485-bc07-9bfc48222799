-- 为管理员角色分配权限
-- 管理员角色ID: 2

-- 组织管理权限
INSERT INTO role_permissions (role_id, permission_id, granted_at) VALUES
(2, 6, NOW()),  -- org.manage
(2, 7, NOW()),  -- org.view
(2, 8, NOW()),  -- org.create
(2, 9, NOW()),  -- org.edit
(2, 10, NOW()); -- org.delete

-- 用户管理权限
INSERT INTO role_permissions (role_id, permission_id, granted_at) VALUES
(2, 11, NOW()), -- user.manage
(2, 12, NOW()), -- user.view
(2, 13, NOW()), -- user.create
(2, 14, NOW()), -- user.edit
(2, 15, NOW()), -- user.delete
(2, 16, NOW()), -- user.role.assign
(2, 17, NOW()); -- user.password.reset

-- 设备管理权限
INSERT INTO role_permissions (role_id, permission_id, granted_at) VALUES
(2, 18, NOW()), -- device.manage
(2, 19, NOW()), -- device.view
(2, 20, NOW()), -- device.use
(2, 21, NOW()); -- device.assign

-- 从服务器管理权限
INSERT INTO role_permissions (role_id, permission_id, granted_at) VALUES
(2, 22, NOW()); -- server.manage

-- 申请处理权限
INSERT INTO role_permissions (role_id, permission_id, granted_at) VALUES
(2, 23, NOW()), -- application.submit
(2, 24, NOW()), -- application.process
(2, 25, NOW()); -- application.view
