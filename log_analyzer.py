#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniLink日志分析和去重工具
用于分析主服务器和压力测试日志，识别问题并生成解决方案
"""

import re
import json
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Set
import argparse

class LogAnalyzer:
    def __init__(self):
        self.main_server_logs = []
        self.stress_test_logs = []
        self.deduplicated_logs = []
        self.problems = defaultdict(list)
        
        # 时间标签正则表达式
        self.time_patterns = {
            'main_server': re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})'),
            'stress_test': re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})'),
            'http_log': re.compile(r'INFO:\s+(\d+\.\d+\.\d+\.\d+:\d+)')
        }
        
        # 问题识别模式
        self.problem_patterns = {
            'thread_error': re.compile(r'cannot join current thread'),
            'http_timeout': re.compile(r'HTTP请求异常: timed out'),
            'topology_mismatch': re.compile(r'拓扑信息不一致'),
            'heartbeat_failure': re.compile(r'心跳.*失败|heartbeat.*fail', re.IGNORECASE),
            'connection_error': re.compile(r'连接.*错误|connection.*error', re.IGNORECASE),
            'memory_issue': re.compile(r'内存|memory', re.IGNORECASE),
            'cpu_issue': re.compile(r'CPU|cpu'),
            'database_error': re.compile(r'数据库.*错误|database.*error', re.IGNORECASE)
        }

    def parse_log_line(self, line: str, source: str) -> Dict:
        """解析日志行，提取时间戳和内容"""
        line = line.strip()
        if not line:
            return None
            
        # 提取时间戳
        timestamp = None
        content = line
        
        if source == 'main_server':
            # 尝试匹配应用日志时间戳
            match = self.time_patterns['main_server'].search(line)
            if match:
                timestamp = match.group(1)
                content = line[match.end():].strip(' -')
            else:
                # HTTP日志没有时间戳，使用IP作为标识
                http_match = self.time_patterns['http_log'].search(line)
                if http_match:
                    timestamp = f"HTTP_{http_match.group(1)}"
        else:
            # 压力测试日志
            match = self.time_patterns['stress_test'].search(line)
            if match:
                timestamp = match.group(1)
                content = line[match.end():].strip(' -')
        
        return {
            'timestamp': timestamp,
            'content': content,
            'source': source,
            'original': line
        }

    def load_logs(self, main_server_file: str, stress_test_file: str):
        """加载两个日志文件"""
        print("正在加载主服务器日志...")
        try:
            with open(main_server_file, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    parsed = self.parse_log_line(line, 'main_server')
                    if parsed:
                        parsed['line_num'] = line_num
                        self.main_server_logs.append(parsed)
        except Exception as e:
            print(f"加载主服务器日志失败: {e}")
            
        print("正在加载压力测试日志...")
        try:
            with open(stress_test_file, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    parsed = self.parse_log_line(line, 'stress_test')
                    if parsed:
                        parsed['line_num'] = line_num
                        self.stress_test_logs.append(parsed)
        except Exception as e:
            print(f"加载压力测试日志失败: {e}")

    def deduplicate_logs(self):
        """去重日志内容"""
        print("正在进行日志去重...")
        
        seen_contents = set()
        content_counts = Counter()
        
        all_logs = self.main_server_logs + self.stress_test_logs
        
        for log in all_logs:
            content = log['content']
            
            # 标准化内容（移除时间戳、IP地址等变化部分）
            normalized_content = self.normalize_content(content)
            content_counts[normalized_content] += 1
            
            if normalized_content not in seen_contents:
                seen_contents.add(normalized_content)
                log['normalized_content'] = normalized_content
                log['count'] = 1
                self.deduplicated_logs.append(log)
            else:
                # 找到对应的去重日志并增加计数
                for dedup_log in self.deduplicated_logs:
                    if dedup_log.get('normalized_content') == normalized_content:
                        dedup_log['count'] += 1
                        break
        
        print(f"去重完成：原始日志 {len(all_logs)} 条，去重后 {len(self.deduplicated_logs)} 条")

    def normalize_content(self, content: str) -> str:
        """标准化日志内容，移除变化的部分"""
        # 移除IP地址
        content = re.sub(r'\d+\.\d+\.\d+\.\d+:\d+', 'IP:PORT', content)
        # 移除时间戳参数
        content = re.sub(r'_t=\d+', '_t=TIMESTAMP', content)
        # 移除具体的ID和数字
        content = re.sub(r'OmniLink-FORMAL-\d+-\d+', 'OmniLink-FORMAL-ID', content)
        content = re.sub(r'ID=\d+', 'ID=NUM', content)
        # 移除具体的时间值
        content = re.sub(r'\d+\.\d+s', 'TIME_VALUE', content)
        content = re.sub(r'\d+%', 'PERCENT', content)
        
        return content.strip()

    def analyze_problems(self):
        """分析问题模式"""
        print("正在分析问题模式...")
        
        for log in self.deduplicated_logs:
            content = log['content']
            
            for problem_type, pattern in self.problem_patterns.items():
                if pattern.search(content):
                    self.problems[problem_type].append({
                        'log': log,
                        'severity': self.get_severity(problem_type, log['count'])
                    })

    def get_severity(self, problem_type: str, count: int) -> str:
        """根据问题类型和出现次数确定严重程度"""
        if problem_type in ['thread_error', 'http_timeout'] and count > 100:
            return 'CRITICAL'
        elif problem_type in ['topology_mismatch', 'heartbeat_failure'] and count > 50:
            return 'HIGH'
        elif count > 10:
            return 'MEDIUM'
        else:
            return 'LOW'

    def generate_report(self) -> str:
        """生成问题分析报告"""
        report = []
        report.append("# OmniLink系统问题分析报告")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 统计信息
        report.append("## 日志统计")
        report.append(f"- 主服务器日志条数: {len(self.main_server_logs)}")
        report.append(f"- 压力测试日志条数: {len(self.stress_test_logs)}")
        report.append(f"- 去重后日志条数: {len(self.deduplicated_logs)}")
        report.append("")
        
        # 问题分析
        report.append("## 问题分析")
        
        if not self.problems:
            report.append("未发现明显问题模式。")
        else:
            for problem_type, issues in self.problems.items():
                if not issues:
                    continue
                    
                report.append(f"### {self.get_problem_title(problem_type)}")
                
                # 按严重程度排序
                issues.sort(key=lambda x: {'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1}[x['severity']], reverse=True)
                
                total_count = sum(issue['log']['count'] for issue in issues)
                report.append(f"**总出现次数**: {total_count}")
                report.append(f"**不同模式数**: {len(issues)}")
                report.append("")
                
                # 显示前5个最严重的问题
                for i, issue in enumerate(issues[:5]):
                    log = issue['log']
                    report.append(f"**{i+1}. [{issue['severity']}] 出现{log['count']}次**")
                    report.append(f"```")
                    report.append(f"{log['content'][:200]}...")
                    report.append(f"```")
                    report.append("")
        
        return "\n".join(report)

    def get_problem_title(self, problem_type: str) -> str:
        """获取问题类型的中文标题"""
        titles = {
            'thread_error': '线程管理异常',
            'http_timeout': 'HTTP请求超时',
            'topology_mismatch': '拓扑信息不一致',
            'heartbeat_failure': '心跳失败',
            'connection_error': '连接错误',
            'memory_issue': '内存问题',
            'cpu_issue': 'CPU问题',
            'database_error': '数据库错误'
        }
        return titles.get(problem_type, problem_type)

def main():
    parser = argparse.ArgumentParser(description='OmniLink日志分析工具')
    parser.add_argument('--main-server', default='test.md', help='主服务器日志文件')
    parser.add_argument('--stress-test', default='test/stress_test/正式压力测试_20250802_024335.log', help='压力测试日志文件')
    parser.add_argument('--output', default='problem_analysis_report.md', help='输出报告文件')
    
    args = parser.parse_args()
    
    analyzer = LogAnalyzer()
    analyzer.load_logs(args.main_server, args.stress_test)
    analyzer.deduplicate_logs()
    analyzer.analyze_problems()
    
    report = analyzer.generate_report()
    
    # 保存报告
    with open(args.output, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"分析完成，报告已保存到: {args.output}")
    print("\n" + "="*50)
    print(report)

if __name__ == "__main__":
    main()
