#!/bin/bash
# ================================
# OmniLink 单容器集成构建脚本
# 版本: 1.0
# 创建日期: 2025-01-10
# ================================

set -e

echo "================================"
echo "OmniLink 单容器集成构建脚本"
echo "================================"

# 检查Docker是否运行
if ! docker version >/dev/null 2>&1; then
    echo "[错误] Docker未运行或未安装，请先启动Docker"
    exit 1
fi

echo "[信息] Docker检查通过"

# 停止并清理现有容器
echo "[信息] 停止现有容器..."
docker-compose -f docker-compose.single.yml down -v || true

# 清理旧镜像（保留基础镜像）
echo "[信息] 清理旧的应用镜像..."
docker images --filter "reference=sever*" --format "{{.Repository}}:{{.Tag}}" | \
    grep -v "python:3.11-slim" | \
    xargs -r docker rmi 2>/dev/null || true

# 拉取必要的基础镜像
echo "[信息] 拉取基础镜像..."
docker pull python:3.11-slim

# 构建前端（如果存在）
if [ -d "main_server/frontend" ]; then
    echo "[信息] 构建前端..."
    if [ -f "build-frontend.sh" ]; then
        ./build-frontend.sh
    else
        echo "[警告] 未找到前端构建脚本"
    fi
fi

# 构建单容器集成镜像
echo "[信息] 构建单容器集成镜像..."
docker-compose -f docker-compose.single.yml build --no-cache

# 启动服务
echo "[信息] 启动集成服务..."
docker-compose -f docker-compose.single.yml up -d

# 等待服务启动
echo "[信息] 等待服务启动..."
sleep 30

# 检查服务状态
echo "[信息] 检查服务状态..."
docker-compose -f docker-compose.single.yml ps

# 显示镜像大小
echo "[信息] 镜像大小信息:"
docker images --filter "reference=sever*" --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}"

echo "================================"
echo "构建完成！"
echo "================================"
echo "Web界面: http://localhost:8000 (对外访问)"
echo "PostgreSQL: 仅容器内部通信 (安全模式)"
echo "Redis: 仅容器内部通信 (安全模式)"
echo "================================"
echo "安全特性: 数据库服务不对外暴露端口"
echo "================================"
echo "查看日志: docker-compose -f docker-compose.single.yml logs -f"
echo "停止服务: docker-compose -f docker-compose.single.yml down"
echo "================================"
