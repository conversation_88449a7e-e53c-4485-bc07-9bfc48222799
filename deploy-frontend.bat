@echo off
echo ================================
echo OmniLink 前端构建部署脚本
echo ================================

echo [1/4] 切换到前端目录...
cd main_server\frontend

echo [2/4] 构建前端项目...
call npm run build
if %errorlevel% neq 0 (
    echo 构建失败！请检查错误信息。
    pause
    exit /b 1
)

echo [3/4] 清理旧的静态文件...
Remove-Item -Recurse -Force ..\backend\static\* 2>nul

echo [4/4] 部署新的构建文件...
Copy-Item -Recurse dist\* ..\backend\static\

echo ================================
echo ✅ 前端部署完成！
echo 现在可以刷新浏览器查看更新
echo ================================
pause
