#!/usr/bin/env python3
"""
前端安全测试脚本
版本: 1.0
创建日期: 2025-07-31

功能：
1. 测试前端安全防护机制
2. 验证Token安全性
3. 检查API安全性
4. 评估整体安全性
"""

import requests
import json
import time
import subprocess
import sys
from pathlib import Path

class FrontendSecurityTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, passed, details=""):
        """记录测试结果"""
        result = {
            "test": test_name,
            "passed": passed,
            "details": details,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
    
    def test_security_headers(self):
        """测试安全头"""
        try:
            response = self.session.get(f"{self.base_url}/")
            headers = response.headers
            
            # 检查关键安全头
            security_headers = {
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "X-XSS-Protection": "1; mode=block",
                "Strict-Transport-Security": None,  # 可选
                "Content-Security-Policy": None,    # 可选
                "Referrer-Policy": None             # 可选
            }
            
            missing_headers = []
            for header, expected_value in security_headers.items():
                if header not in headers:
                    missing_headers.append(header)
                elif expected_value and headers[header] != expected_value:
                    missing_headers.append(f"{header} (incorrect value)")
            
            if missing_headers:
                self.log_test(
                    "Security Headers",
                    False,
                    f"Missing or incorrect headers: {', '.join(missing_headers)}"
                )
            else:
                self.log_test("Security Headers", True, "All security headers present")
                
        except Exception as e:
            self.log_test("Security Headers", False, f"Error: {e}")
    
    def test_api_authentication(self):
        """测试API认证"""
        try:
            # 测试未认证访问
            response = self.session.get(f"{self.base_url}/api/v1/users/profile")
            
            if response.status_code == 401:
                self.log_test("API Authentication", True, "Unauthorized access properly blocked")
            else:
                self.log_test(
                    "API Authentication",
                    False,
                    f"Expected 401, got {response.status_code}"
                )
                
        except Exception as e:
            self.log_test("API Authentication", False, f"Error: {e}")
    
    def test_csrf_protection(self):
        """测试CSRF保护"""
        try:
            # 尝试不带CSRF token的POST请求
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/login",
                json={"username": "test", "password": "test"}
            )
            
            # 检查是否有CSRF保护
            if "csrf" in response.text.lower() or response.status_code == 403:
                self.log_test("CSRF Protection", True, "CSRF protection active")
            else:
                self.log_test("CSRF Protection", False, "No CSRF protection detected")
                
        except Exception as e:
            self.log_test("CSRF Protection", False, f"Error: {e}")
    
    def test_sql_injection(self):
        """测试SQL注入防护"""
        try:
            # 尝试SQL注入攻击
            payloads = [
                "' OR '1'='1",
                "'; DROP TABLE users; --",
                "' UNION SELECT * FROM users --"
            ]
            
            for payload in payloads:
                response = self.session.post(
                    f"{self.base_url}/api/v1/auth/login",
                    json={"username": payload, "password": "test"}
                )
                
                # 检查是否返回了敏感信息
                if response.status_code == 200 and "token" in response.text:
                    self.log_test(
                        "SQL Injection Protection",
                        False,
                        f"Possible SQL injection with payload: {payload}"
                    )
                    return
            
            self.log_test("SQL Injection Protection", True, "No SQL injection vulnerabilities found")
            
        except Exception as e:
            self.log_test("SQL Injection Protection", False, f"Error: {e}")
    
    def test_xss_protection(self):
        """测试XSS防护"""
        try:
            # 尝试XSS攻击
            xss_payloads = [
                "<script>alert('xss')</script>",
                "javascript:alert('xss')",
                "<img src=x onerror=alert('xss')>"
            ]
            
            for payload in xss_payloads:
                response = self.session.post(
                    f"{self.base_url}/api/v1/auth/login",
                    json={"username": payload, "password": "test"}
                )
                
                # 检查响应中是否包含未转义的脚本
                if "<script>" in response.text or "javascript:" in response.text:
                    self.log_test(
                        "XSS Protection",
                        False,
                        f"Possible XSS vulnerability with payload: {payload}"
                    )
                    return
            
            self.log_test("XSS Protection", True, "No XSS vulnerabilities found")
            
        except Exception as e:
            self.log_test("XSS Protection", False, f"Error: {e}")
    
    def test_rate_limiting(self):
        """测试速率限制"""
        try:
            # 快速发送多个请求
            start_time = time.time()
            responses = []
            
            for i in range(20):
                response = self.session.post(
                    f"{self.base_url}/api/v1/auth/login",
                    json={"username": "test", "password": "test"}
                )
                responses.append(response.status_code)
            
            # 检查是否有速率限制
            rate_limited = any(status == 429 for status in responses)
            
            if rate_limited:
                self.log_test("Rate Limiting", True, "Rate limiting is active")
            else:
                self.log_test("Rate Limiting", False, "No rate limiting detected")
                
        except Exception as e:
            self.log_test("Rate Limiting", False, f"Error: {e}")
    
    def test_directory_traversal(self):
        """测试目录遍历攻击防护"""
        try:
            # 尝试目录遍历攻击
            traversal_payloads = [
                "../../../etc/passwd",
                "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
                "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
            ]
            
            for payload in traversal_payloads:
                response = self.session.get(f"{self.base_url}/static/{payload}")
                
                # 检查是否返回了系统文件内容
                if response.status_code == 200 and ("root:" in response.text or "localhost" in response.text):
                    self.log_test(
                        "Directory Traversal Protection",
                        False,
                        f"Possible directory traversal with payload: {payload}"
                    )
                    return
            
            self.log_test("Directory Traversal Protection", True, "No directory traversal vulnerabilities found")
            
        except Exception as e:
            self.log_test("Directory Traversal Protection", False, f"Error: {e}")
    
    def test_information_disclosure(self):
        """测试信息泄露"""
        try:
            # 检查错误页面是否泄露敏感信息
            response = self.session.get(f"{self.base_url}/nonexistent-page")
            
            # 检查是否泄露了技术栈信息
            sensitive_info = [
                "FastAPI",
                "SQLAlchemy", 
                "Python",
                "traceback",
                "stack trace"
            ]
            
            leaked_info = []
            for info in sensitive_info:
                if info.lower() in response.text.lower():
                    leaked_info.append(info)
            
            if leaked_info:
                self.log_test(
                    "Information Disclosure",
                    False,
                    f"Leaked information: {', '.join(leaked_info)}"
                )
            else:
                self.log_test("Information Disclosure", True, "No sensitive information leaked")
                
        except Exception as e:
            self.log_test("Information Disclosure", False, f"Error: {e}")
    
    def test_frontend_compilation(self):
        """测试前端编译安全性"""
        try:
            # 检查是否启用了动态编译
            response = self.session.get(f"{self.base_url}/api/v1/frontend/security/config")
            
            if response.status_code == 401:
                self.log_test("Frontend Compilation Security", True, "Compilation API properly protected")
            elif response.status_code == 200:
                data = response.json()
                if data.get("success") and "security_config" in data.get("data", {}):
                    self.log_test("Frontend Compilation Security", True, "Dynamic compilation configured")
                else:
                    self.log_test("Frontend Compilation Security", False, "Compilation API accessible but misconfigured")
            else:
                self.log_test("Frontend Compilation Security", False, f"Unexpected response: {response.status_code}")
                
        except Exception as e:
            self.log_test("Frontend Compilation Security", False, f"Error: {e}")
    
    def test_static_file_security(self):
        """测试静态文件安全性"""
        try:
            # 检查是否可以访问源码文件
            sensitive_files = [
                "package.json",
                "vite.config.js",
                "src/main.js",
                ".env",
                "node_modules"
            ]
            
            accessible_files = []
            for file in sensitive_files:
                response = self.session.get(f"{self.base_url}/static/{file}")
                if response.status_code == 200:
                    accessible_files.append(file)
            
            if accessible_files:
                self.log_test(
                    "Static File Security",
                    False,
                    f"Accessible sensitive files: {', '.join(accessible_files)}"
                )
            else:
                self.log_test("Static File Security", True, "Sensitive files properly protected")
                
        except Exception as e:
            self.log_test("Static File Security", False, f"Error: {e}")
    
    def run_all_tests(self):
        """运行所有安全测试"""
        print("🔒 Starting Frontend Security Tests...")
        print("=" * 50)
        
        # 运行所有测试
        self.test_security_headers()
        self.test_api_authentication()
        self.test_csrf_protection()
        self.test_sql_injection()
        self.test_xss_protection()
        self.test_rate_limiting()
        self.test_directory_traversal()
        self.test_information_disclosure()
        self.test_frontend_compilation()
        self.test_static_file_security()
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 50)
        print("🔒 Security Test Report")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["passed"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["passed"]:
                    print(f"  - {result['test']}: {result['details']}")
        
        # 保存详细报告
        report_file = "security_test_report.json"
        with open(report_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        # 安全评级
        if passed_tests == total_tests:
            print("\n🟢 Security Level: EXCELLENT")
        elif passed_tests >= total_tests * 0.8:
            print("\n🟡 Security Level: GOOD")
        elif passed_tests >= total_tests * 0.6:
            print("\n🟠 Security Level: MODERATE")
        else:
            print("\n🔴 Security Level: POOR")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8000"
    
    print(f"Testing frontend security for: {base_url}")
    
    tester = FrontendSecurityTester(base_url)
    tester.run_all_tests()

if __name__ == "__main__":
    main()
