#!/bin/bash

# FRP客户端停止脚本

PID_FILE="/var/run/frpc.pid"

if [ -f "$PID_FILE" ]; then
    PID=$(cat $PID_FILE)
    if ps -p $PID > /dev/null 2>&1; then
        echo "停止FRP客户端 (PID: $PID)..."
        kill $PID
        
        # 等待进程结束
        for i in {1..10}; do
            if ! ps -p $PID > /dev/null 2>&1; then
                echo "FRP客户端已停止"
                rm -f $PID_FILE
                exit 0
            fi
            sleep 1
        done
        
        # 强制杀死进程
        echo "强制停止FRP客户端..."
        kill -9 $PID
        rm -f $PID_FILE
        echo "FRP客户端已强制停止"
    else
        echo "FRP客户端未运行"
        rm -f $PID_FILE
    fi
else
    echo "PID文件不存在，FRP客户端可能未运行"
fi
