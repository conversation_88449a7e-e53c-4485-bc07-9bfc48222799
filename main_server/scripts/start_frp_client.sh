#!/bin/bash

# FRP客户端启动脚本
# 用于连接到 skfirefly.cn:7000

# 设置变量
FRP_DIR="/opt/frp"
CONFIG_FILE="/opt/frp/frpc.ini"
LOG_FILE="/var/log/frp/frpc.log"
PID_FILE="/var/run/frpc.pid"

# 创建日志目录
mkdir -p /var/log/frp

# 检查FRP客户端是否已安装
if [ ! -f "$FRP_DIR/frpc" ]; then
    echo "FRP客户端未找到，开始下载安装..."
    
    # 创建FRP目录
    mkdir -p $FRP_DIR
    cd $FRP_DIR
    
    # 下载FRP客户端
    wget https://github.com/fatedier/frp/releases/download/v0.52.3/frp_0.52.3_linux_amd64.tar.gz
    tar -xzf frp_0.52.3_linux_amd64.tar.gz
    mv frp_0.52.3_linux_amd64/* .
    rm -rf frp_0.52.3_linux_amd64*
    
    # 设置执行权限
    chmod +x frpc
    
    echo "FRP客户端安装完成"
fi

# 复制配置文件
cp /app/configs/frp_client.ini $CONFIG_FILE

# 检查是否已经运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat $PID_FILE)
    if ps -p $PID > /dev/null 2>&1; then
        echo "FRP客户端已经在运行 (PID: $PID)"
        exit 1
    else
        rm -f $PID_FILE
    fi
fi

# 启动FRP客户端
echo "启动FRP客户端..."
nohup $FRP_DIR/frpc -c $CONFIG_FILE > $LOG_FILE 2>&1 &
echo $! > $PID_FILE

echo "FRP客户端已启动"
echo "配置文件: $CONFIG_FILE"
echo "日志文件: $LOG_FILE"
echo "PID文件: $PID_FILE"

# 等待几秒钟检查启动状态
sleep 3
if ps -p $(cat $PID_FILE) > /dev/null 2>&1; then
    echo "FRP客户端启动成功"
    tail -n 10 $LOG_FILE
else
    echo "FRP客户端启动失败，请检查日志："
    cat $LOG_FILE
    exit 1
fi
