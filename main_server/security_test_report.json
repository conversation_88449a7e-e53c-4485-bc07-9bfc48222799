[{"test": "Security Headers", "passed": false, "details": "Missing or incorrect headers: X-Content-Type-Options, X-Frame-Options, X-XSS-Protection, Strict-Transport-Security, Content-Security-Policy, Referrer-Policy", "timestamp": 1753955840.2818677}, {"test": "API Authentication", "passed": false, "details": "Expected 401, got 403", "timestamp": 1753955840.2870238}, {"test": "CSRF Protection", "passed": false, "details": "No CSRF protection detected", "timestamp": 1753955840.3122134}, {"test": "SQL Injection Protection", "passed": true, "details": "No SQL injection vulnerabilities found", "timestamp": 1753955840.3272822}, {"test": "XSS Protection", "passed": true, "details": "No XSS vulnerabilities found", "timestamp": 1753955840.3409433}, {"test": "Rate Limiting", "passed": true, "details": "Rate limiting is active", "timestamp": 1753955840.4475129}, {"test": "Directory Traversal Protection", "passed": true, "details": "No directory traversal vulnerabilities found", "timestamp": 1753955840.4810681}, {"test": "Information Disclosure", "passed": true, "details": "No sensitive information leaked", "timestamp": 1753955840.492866}, {"test": "Frontend Compilation Security", "passed": false, "details": "Unexpected response: 403", "timestamp": 1753955840.497225}, {"test": "Static File Security", "passed": true, "details": "Sensitive files properly protected", "timestamp": 1753955840.5455508}]