#!/usr/bin/env python3
"""
设备分类测试脚本
"""

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from database import get_db
from models import Device
from sqlalchemy import select
from core.device_type_classifier import classify_device_type

async def test_device_classification():
    async for db in get_db():
        try:
            # 获取所有设备
            result = await db.execute(select(Device))
            devices = result.scalars().all()
            
            print(f'找到 {len(devices)} 个设备')
            print('\n设备详细信息和分类测试:')
            
            for device in devices:
                print(f'\n设备ID: {device.device_id}')
                print(f'VID: {device.vendor_id}')
                print(f'PID: {device.product_id}')
                print(f'描述: {device.description}')
                print(f'厂商名称: {device.usb_ids_vendor_name}')
                print(f'设备名称: {device.usb_ids_device_name}')
                print(f'当前类型: {device.final_device_type}')
                
                # 测试分类器
                classification = classify_device_type(
                    vendor_id=device.vendor_id,
                    product_id=device.product_id,
                    description=device.description,
                    vendor_name=device.usb_ids_vendor_name,
                    device_name=device.usb_ids_device_name
                )
                
                print(f'分类结果: {classification["device_type"]} (置信度: {classification["confidence"]:.2f})')
                print(f'类型描述: {classification["type_description"]}')
                print('-' * 50)
                
        except Exception as e:
            print(f'测试失败: {e}')
            import traceback
            traceback.print_exc()
        finally:
            await db.close()
        break

if __name__ == "__main__":
    asyncio.run(test_device_classification())
