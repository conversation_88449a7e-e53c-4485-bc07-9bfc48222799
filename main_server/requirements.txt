# OmniLink 主服务器依赖
# 版本: 4.0
# 更新日期: 2024-12-19

# ===========================================
# 核心框架
# ===========================================
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# ===========================================
# 数据库相关
# ===========================================
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.13.1
redis==5.0.1

# ===========================================
# 认证与安全
# ===========================================
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# ===========================================
# HTTP 客户端
# ===========================================
httpx==0.25.2
aiohttp==3.9.1

# ===========================================
# WebSocket 支持
# ===========================================
websockets==12.0

# ===========================================
# 工具库
# ===========================================
python-dotenv==1.0.0
loguru==0.7.2
click==8.1.7
rich==13.7.0

# ===========================================
# 数据验证与序列化
# ===========================================
email-validator==2.1.0
phonenumbers==8.13.26

# ===========================================
# 开发工具 (可选)
# ===========================================
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# ===========================================
# 系统监控（轻量级）
# ===========================================
psutil==5.9.6

# ===========================================
# CORS 支持
# ===========================================
fastapi-cors==0.0.6
