"""
OmniLink申请处理智能管理员查找服务
版本: 1.0
创建日期: 2025-01-12
描述: 实现智能管理员查找机制，支持申请分发和处理权限控制
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from models import User, Organization, ApplicationRequest
from dual_permission_system import DualPermissionChecker
import logging

logger = logging.getLogger(__name__)

class ApplicationManagerFinder:
    """申请处理智能管理员查找器"""
    
    @staticmethod
    async def find_available_managers(
        db: AsyncSession, 
        user: User, 
        target_permission_level: Optional[int] = None
    ) -> List[User]:
        """
        为用户查找可用的管理员
        
        Args:
            db: 数据库会话
            user: 申请用户
            target_permission_level: 目标权限级别（可选）
        
        Returns:
            可用管理员列表
        """
        try:
            # 1. 确定目标权限级别
            if target_permission_level is None:
                # 默认查找比当前用户权限级别更高的管理员
                target_permission_level = max(0, user.permission_level - 1)
            
            # 2. 从当前组织开始向上查找
            current_org_id = user.organization_id
            managers = []
            
            while current_org_id and not managers:
                # 查找当前组织层级的管理员
                org_managers = await ApplicationManagerFinder._find_managers_in_organization(
                    db, current_org_id, target_permission_level
                )
                
                if org_managers:
                    managers.extend(org_managers)
                    break
                
                # 向上查找父组织
                parent_org = await ApplicationManagerFinder._get_parent_organization(
                    db, current_org_id
                )
                current_org_id = parent_org.id if parent_org else None
            
            # 3. 如果仍未找到，查找全局管理员
            if not managers:
                global_managers = await ApplicationManagerFinder._find_global_managers(
                    db, target_permission_level
                )
                managers.extend(global_managers)
            
            logger.info(f"为用户 {user.username} 找到 {len(managers)} 个可用管理员")
            return managers
            
        except Exception as e:
            logger.error(f"查找管理员失败: {e}")
            return []
    
    @staticmethod
    async def _find_managers_in_organization(
        db: AsyncSession, 
        org_id: int, 
        target_permission_level: int
    ) -> List[User]:
        """在指定组织中查找管理员"""
        try:
            query = select(User).where(
                and_(
                    User.organization_id == org_id,
                    User.permission_level <= target_permission_level,
                    User.permission_level < 3,  # 排除普通用户和新用户
                    User.is_active == True
                )
            ).order_by(User.permission_level)
            
            result = await db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"在组织 {org_id} 中查找管理员失败: {e}")
            return []
    
    @staticmethod
    async def _get_parent_organization(
        db: AsyncSession, 
        org_id: int
    ) -> Optional[Organization]:
        """获取父组织"""
        try:
            query = select(Organization).where(Organization.id == org_id)
            result = await db.execute(query)
            org = result.scalar_one_or_none()
            
            if org and org.parent_id:
                parent_query = select(Organization).where(Organization.id == org.parent_id)
                parent_result = await db.execute(parent_query)
                return parent_result.scalar_one_or_none()
            
            return None
            
        except Exception as e:
            logger.error(f"获取组织 {org_id} 的父组织失败: {e}")
            return None
    
    @staticmethod
    async def _find_global_managers(
        db: AsyncSession, 
        target_permission_level: int
    ) -> List[User]:
        """查找全局管理员"""
        try:
            query = select(User).where(
                and_(
                    User.permission_level <= target_permission_level,
                    User.permission_level <= 1,  # 只查找超级管理员和全域管理员
                    User.is_active == True
                )
            ).order_by(User.permission_level)
            
            result = await db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"查找全局管理员失败: {e}")
            return []
    
    @staticmethod
    async def can_process_application(
        db: AsyncSession, 
        user: User, 
        application: ApplicationRequest
    ) -> bool:
        """
        检查用户是否可以处理指定申请
        
        Args:
            db: 数据库会话
            user: 处理用户
            application: 申请对象
        
        Returns:
            是否可以处理
        """
        try:
            # 1. 检查基本权限级别
            if user.permission_level >= 3:  # 普通用户和新用户不能处理申请
                return False
            
            # 2. 全域管理员可以处理所有申请
            if await DualPermissionChecker.is_special_user(user):
                return True
            
            # 3. 检查是否为指定处理人
            if application.target_processor_id == user.id:
                return True
            
            # 4. 检查组织权限范围
            if application.applicant_organization_id:
                # 检查用户是否有权限管理申请人所在组织
                user_manageable_orgs = await ApplicationManagerFinder._get_user_manageable_organizations(
                    db, user
                )
                if application.applicant_organization_id in user_manageable_orgs:
                    return True
            
            # 5. 检查是否为转交给该用户的申请
            if application.forwarded_to_id == user.id:
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查申请处理权限失败: {e}")
            return False
    
    @staticmethod
    async def _get_user_manageable_organizations(
        db: AsyncSession, 
        user: User
    ) -> List[int]:
        """获取用户可管理的组织ID列表"""
        try:
            # 全域管理员可以管理所有组织
            if await DualPermissionChecker.is_special_user(user):
                query = select(Organization.id)
                result = await db.execute(query)
                return [org_id for org_id, in result.all()]
            
            # 其他管理员只能管理其组织及下级组织
            manageable_orgs = [user.organization_id] if user.organization_id else []
            
            # 递归查找下级组织
            await ApplicationManagerFinder._add_child_organizations(
                db, user.organization_id, manageable_orgs
            )
            
            return manageable_orgs
            
        except Exception as e:
            logger.error(f"获取用户可管理组织失败: {e}")
            return []
    
    @staticmethod
    async def _add_child_organizations(
        db: AsyncSession, 
        parent_org_id: int, 
        org_list: List[int]
    ):
        """递归添加子组织"""
        try:
            if not parent_org_id:
                return
            
            query = select(Organization.id).where(Organization.parent_id == parent_org_id)
            result = await db.execute(query)
            child_orgs = [org_id for org_id, in result.all()]
            
            for child_org_id in child_orgs:
                if child_org_id not in org_list:
                    org_list.append(child_org_id)
                    await ApplicationManagerFinder._add_child_organizations(
                        db, child_org_id, org_list
                    )
                    
        except Exception as e:
            logger.error(f"添加子组织失败: {e}")

class ApplicationPermissionFilter:
    """申请权限过滤器"""
    
    @staticmethod
    async def filter_applications_for_user(
        db: AsyncSession, 
        user: User, 
        base_query
    ):
        """
        根据用户权限过滤申请查询
        
        Args:
            db: 数据库会话
            user: 当前用户
            base_query: 基础查询
        
        Returns:
            过滤后的查询
        """
        try:
            # 全域管理员可以查看所有申请
            if await DualPermissionChecker.is_special_user(user):
                return base_query
            
            # 构建权限过滤条件
            conditions = []
            
            # 1. 用户提交的申请
            conditions.append(ApplicationRequest.applicant_id == user.id)
            
            # 2. 指定给用户处理的申请
            conditions.append(ApplicationRequest.target_processor_id == user.id)
            
            # 3. 用户已处理的申请
            conditions.append(ApplicationRequest.processor_id == user.id)
            
            # 4. 转交给用户的申请
            conditions.append(ApplicationRequest.forwarded_to_id == user.id)
            
            # 5. 如果是管理员，可以查看其管理范围内的申请
            if user.permission_level <= 2:  # 管理员及以上
                manageable_orgs = await ApplicationManagerFinder._get_user_manageable_organizations(
                    db, user
                )
                if manageable_orgs:
                    conditions.append(
                        ApplicationRequest.applicant_organization_id.in_(manageable_orgs)
                    )
            
            # 应用过滤条件
            return base_query.where(or_(*conditions))
            
        except Exception as e:
            logger.error(f"过滤申请查询失败: {e}")
            return base_query
