#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的设备同步测试
直接为已注册的50台服务器添加USB设备
"""

import asyncio
import aiohttp
import logging
import random
from datetime import datetime
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleDeviceSyncTester:
    """简化的设备同步测试器"""
    
    def __init__(self, master_url: str = "http://localhost:8000"):
        self.master_url = master_url
        self.session = None
    
    async def start(self):
        """启动测试"""
        timeout = aiohttp.ClientTimeout(total=30.0)
        self.session = aiohttp.ClientSession(timeout=timeout)
        
        logger.info("🚀 开始简化设备同步测试")
        
        # 获取已注册的服务器列表
        servers = await self._get_registered_servers()
        
        if not servers:
            logger.error("❌ 没有找到已注册的服务器")
            return
        
        logger.info(f"找到 {len(servers)} 台已注册的服务器")
        
        # 为前5台服务器添加设备
        success_count = 0
        for i, server in enumerate(servers[:5]):
            server_name = server.get('name', '')
            hardware_uuid = server.get('hardware_uuid', '')
            
            if not hardware_uuid:
                logger.warning(f"服务器 {server_name} 缺少hardware_uuid，跳过")
                continue
            
            logger.info(f"为服务器 {server_name} 添加USB设备...")
            
            success = await self._sync_devices_for_server(hardware_uuid, server_name)
            if success:
                success_count += 1
                logger.info(f"✅ {server_name} 设备同步成功")
            else:
                logger.error(f"❌ {server_name} 设备同步失败")
        
        logger.info(f"设备同步完成: {success_count}/{min(5, len(servers))} 台服务器成功")
        
        # 验证结果
        await self._verify_device_count()
    
    async def stop(self):
        """停止测试"""
        if self.session:
            await self.session.close()
    
    async def _get_registered_servers(self) -> List[Dict[str, Any]]:
        """获取已注册的服务器列表"""
        try:
            async with self.session.get(f"{self.master_url}/api/v1/slave/list") as response:
                if response.status == 200:
                    result = await response.json()
                    # 修正数据解析：API返回格式是 {"success":true,"data":[...]}
                    return result.get('data', [])
                else:
                    logger.error(f"获取服务器列表失败: HTTP {response.status}")
                    return []
        except Exception as e:
            logger.error(f"获取服务器列表异常: {e}")
            return []
    
    async def _sync_devices_for_server(self, hardware_uuid: str, server_name: str) -> bool:
        """为指定服务器同步设备"""
        # 生成测试设备
        devices = self._generate_test_devices()
        
        # 构建设备同步请求
        sync_request = {
            'hardware_uuid': hardware_uuid,
            'devices': devices,
            'sync_timestamp': datetime.now().isoformat()
        }
        
        try:
            sync_url = f"{self.master_url}/api/v1/slave/{hardware_uuid}/sync-devices"
            
            async with self.session.post(
                sync_url,
                json=sync_request,
                headers={'Content-Type': 'application/json; charset=utf-8'}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    sync_results = result.get('sync_results', {})
                    created = sync_results.get('created', 0)
                    updated = sync_results.get('updated', 0)
                    logger.info(f"设备同步结果: 创建 {created}, 更新 {updated}")
                    return True
                else:
                    response_text = await response.text()
                    logger.error(f"设备同步失败: HTTP {response.status}")
                    logger.error(f"错误详情: {response_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"设备同步异常: {e}")
            return False
    
    def _generate_test_devices(self) -> List[Dict[str, Any]]:
        """生成测试设备"""
        devices = []
        device_types = [
            {"vendor_id": "096e", "product_id": "031b", "name": "广联达加密锁"},
            {"vendor_id": "0471", "product_id": "0888", "name": "新点加密锁"},
            {"vendor_id": "1a86", "product_id": "7523", "name": "博威加密锁"},
        ]
        
        # 随机生成3-5个设备
        device_count = random.randint(3, 5)
        
        # 为每台服务器生成唯一的设备序列号前缀，避免跨服务器冲突
        import time
        server_prefix = int(time.time() * 1000) % 100000  # 使用时间戳生成唯一前缀

        for port in range(1, device_count + 1):
            device_info = random.choice(device_types)
            # 生成全局唯一的序列号：服务器前缀 + 端口号 + 随机数
            serial_number = f"S{server_prefix:05d}P{port:02d}R{random.randint(1000, 9999)}"

            # 生成唯一的hardware_signature，确保每个设备都不同
            # 格式：vendor_id:product_id:serial_number
            hardware_signature = f"{device_info['vendor_id']}:{device_info['product_id']}:{serial_number}"

            # 注意：device_id由主服务器自动生成，格式为：
            # f"{vendor_id}:{product_id}:{hardware_signature[:8]}"
            # 我们不需要在这里生成device_id，主服务器会自动处理

            device = {
                "hardware_signature": hardware_signature,
                "physical_port": f"1-{port}",
                "vendor_id": device_info["vendor_id"],
                "product_id": device_info["product_id"],
                "serial_number": serial_number,
                "description": device_info["name"],
                "device_path": f"/dev/bus/usb/001/{port:03d}",
                "custom_name": None,
                "notes": f"压力测试设备 - {device_info['name']}",
                "config_data": {
                    "device_type": "encryption_dongle",
                    "auto_bind_eligible": True,
                    "is_real_hardware": True
                }
            }
            devices.append(device)
        
        return devices
    
    async def _verify_device_count(self):
        """验证设备数量"""
        try:
            # 检查从服务器统计
            async with self.session.get(f"{self.master_url}/api/v1/slave/servers/stats") as response:
                if response.status == 200:
                    result = await response.json()
                    server_count = result.get('total_servers', 0)
                    device_count = result.get('total_devices', 0)
                    
                    logger.info("=" * 80)
                    logger.info("📊 设备同步验证结果")
                    logger.info("=" * 80)
                    logger.info(f"✅ 从服务器总数: {server_count}")
                    logger.info(f"✅ USB设备总数: {device_count}")
                    logger.info(f"✅ 平均每服务器设备数: {device_count/max(server_count, 1):.1f}")
                    
                    if device_count > 0:
                        logger.info("🎉 USB设备自动注册功能验证成功！")
                    else:
                        logger.warning("⚠️ 设备数量仍为0，可能需要进一步检查")
                    
                    logger.info("=" * 80)
                else:
                    logger.error(f"获取统计信息失败: HTTP {response.status}")
        except Exception as e:
            logger.error(f"验证设备数量异常: {e}")

async def main():
    """主函数"""
    tester = SimpleDeviceSyncTester()
    
    try:
        await tester.start()
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await tester.stop()

if __name__ == "__main__":
    asyncio.run(main())
