#!/usr/bin/env python3
"""
VirtualHere专用压缩中间件
版本: 1.0
创建日期: 2025-01-15
描述: 针对VirtualHere数据流和Web访问数据进行高效压缩处理，支持500+并发用户，3Mbps带宽优化
"""

import asyncio
import time
import logging
import threading
from typing import Dict, Any, Optional, Callable, Tuple, Union, List
from enum import Enum
import struct
import hashlib
from collections import deque
import weakref

# 导入压缩库
try:
    import snappy
    SNAPPY_AVAILABLE = True
except ImportError:
    SNAPPY_AVAILABLE = False

try:
    import lz4.frame
    LZ4_AVAILABLE = True
except ImportError:
    LZ4_AVAILABLE = False

try:
    import zstandard as zstd
    ZSTD_AVAILABLE = True
except ImportError:
    ZSTD_AVAILABLE = False

logger = logging.getLogger(__name__)

class DataStreamType(str, Enum):
    """数据流类型"""
    WEB_HTTP = "web_http"           # Web页面HTTP数据
    WEB_HTTPS = "web_https"         # Web页面HTTPS数据
    VIRTUALHERE_USB = "vh_usb"      # VirtualHere USB数据流
    VIRTUALHERE_CONTROL = "vh_ctrl" # VirtualHere 控制数据流

class CompressionStrategy(str, Enum):
    """压缩策略"""
    ULTRA_FAST = "ultra_fast"       # 超快速：优先速度，适合实时USB数据
    BALANCED = "balanced"           # 平衡：速度与压缩率平衡，适合Web数据
    HIGH_COMPRESSION = "high_comp"  # 高压缩：优先压缩率，适合大文件传输

class VirtualHereCompressionMiddleware:
    """VirtualHere专用压缩中间件"""
    
    def __init__(self, max_concurrent_users: int = 500, bandwidth_limit_mbps: float = 3.0):
        """
        初始化VirtualHere压缩中间件
        
        Args:
            max_concurrent_users: 最大并发用户数
            bandwidth_limit_mbps: 带宽限制(Mbps)
        """
        self.max_concurrent_users = max_concurrent_users
        self.bandwidth_limit_mbps = bandwidth_limit_mbps
        self.bandwidth_limit_bps = bandwidth_limit_mbps * 1024 * 1024  # 转换为bps
        
        # 连接管理
        self.active_connections: Dict[str, Dict[str, Any]] = {}
        self.connection_stats: Dict[str, Dict[str, Any]] = {}
        self.global_stats = {
            'total_compressed_bytes': 0,
            'total_original_bytes': 0,
            'total_connections': 0,
            'active_connections': 0,
            'bandwidth_usage_bps': 0,
            'start_time': time.time()
        }
        
        # 压缩器缓存（每个连接一个压缩器实例）
        self.compressor_cache: Dict[str, Dict[str, Any]] = {}
        
        # 带宽控制
        self.bandwidth_tokens = deque(maxlen=1000)  # 令牌桶
        self.last_token_refill = time.time()
        
        # 初始化压缩器
        self._init_compressors()
        
        # 启动后台任务
        self._start_background_tasks()
    
    def _init_compressors(self):
        """初始化压缩器"""
        self.compressors = {}
        
        # 超快速策略 - 使用LZ4
        if LZ4_AVAILABLE:
            self.compressors[CompressionStrategy.ULTRA_FAST] = {
                'compress': lambda data: lz4.frame.compress(data, compression_level=0),
                'decompress': lz4.frame.decompress,
                'name': 'LZ4_ULTRA_FAST'
            }
        elif SNAPPY_AVAILABLE:
            self.compressors[CompressionStrategy.ULTRA_FAST] = {
                'compress': snappy.compress,
                'decompress': snappy.decompress,
                'name': 'SNAPPY_ULTRA_FAST'
            }
        
        # 平衡策略 - 使用Snappy
        if SNAPPY_AVAILABLE:
            self.compressors[CompressionStrategy.BALANCED] = {
                'compress': snappy.compress,
                'decompress': snappy.decompress,
                'name': 'SNAPPY_BALANCED'
            }
        elif LZ4_AVAILABLE:
            self.compressors[CompressionStrategy.BALANCED] = {
                'compress': lambda data: lz4.frame.compress(data, compression_level=3),
                'decompress': lz4.frame.decompress,
                'name': 'LZ4_BALANCED'
            }
        
        # 高压缩策略 - 使用Zstandard
        if ZSTD_AVAILABLE:
            zstd_compressor = zstd.ZstdCompressor(level=3)
            zstd_decompressor = zstd.ZstdDecompressor()
            self.compressors[CompressionStrategy.HIGH_COMPRESSION] = {
                'compress': zstd_compressor.compress,
                'decompress': zstd_decompressor.decompress,
                'name': 'ZSTD_HIGH_COMPRESSION'
            }
        elif SNAPPY_AVAILABLE:
            self.compressors[CompressionStrategy.HIGH_COMPRESSION] = {
                'compress': snappy.compress,
                'decompress': snappy.decompress,
                'name': 'SNAPPY_HIGH_COMPRESSION'
            }
        
        logger.info(f"压缩器初始化完成，可用策略: {list(self.compressors.keys())}")
    
    def _get_compression_strategy(self, stream_type: DataStreamType) -> CompressionStrategy:
        """根据数据流类型选择压缩策略"""
        if stream_type == DataStreamType.VIRTUALHERE_USB:
            return CompressionStrategy.ULTRA_FAST  # USB数据需要极低延迟
        elif stream_type == DataStreamType.VIRTUALHERE_CONTROL:
            return CompressionStrategy.BALANCED    # 控制数据平衡处理
        elif stream_type in [DataStreamType.WEB_HTTP, DataStreamType.WEB_HTTPS]:
            return CompressionStrategy.HIGH_COMPRESSION  # Web数据可以牺牲一些速度换取压缩率
        else:
            return CompressionStrategy.BALANCED
    
    def _check_bandwidth_limit(self, data_size: int) -> bool:
        """检查带宽限制"""
        current_time = time.time()
        
        # 令牌桶算法
        time_passed = current_time - self.last_token_refill
        tokens_to_add = int(time_passed * self.bandwidth_limit_bps)
        
        if tokens_to_add > 0:
            self.bandwidth_tokens.extend([1] * min(tokens_to_add, 1000))
            self.last_token_refill = current_time
        
        # 检查是否有足够的令牌
        if len(self.bandwidth_tokens) >= data_size:
            # 消耗令牌
            for _ in range(min(data_size, len(self.bandwidth_tokens))):
                if self.bandwidth_tokens:
                    self.bandwidth_tokens.popleft()
            return True
        
        return False
    
    async def create_connection(self, connection_id: str, stream_type: DataStreamType, 
                              user_info: Dict[str, Any] = None) -> bool:
        """
        创建新连接
        
        Args:
            connection_id: 连接ID
            stream_type: 数据流类型
            user_info: 用户信息
            
        Returns:
            是否创建成功
        """
        if len(self.active_connections) >= self.max_concurrent_users:
            logger.warning(f"达到最大并发用户数限制: {self.max_concurrent_users}")
            return False
        
        strategy = self._get_compression_strategy(stream_type)
        
        self.active_connections[connection_id] = {
            'stream_type': stream_type,
            'strategy': strategy,
            'user_info': user_info or {},
            'created_at': time.time(),
            'last_activity': time.time(),
            'bytes_compressed': 0,
            'bytes_original': 0,
            'compression_count': 0,
            'decompression_count': 0,
            'errors': 0
        }
        
        self.connection_stats[connection_id] = {
            'compression_times': deque(maxlen=100),
            'decompression_times': deque(maxlen=100),
            'compression_ratios': deque(maxlen=100)
        }
        
        # 为连接创建专用压缩器实例
        if strategy in self.compressors:
            self.compressor_cache[connection_id] = self.compressors[strategy].copy()
        
        self.global_stats['total_connections'] += 1
        self.global_stats['active_connections'] += 1
        
        logger.info(f"连接创建成功: {connection_id}, 类型: {stream_type}, 策略: {strategy}")
        return True
    
    async def compress_data(self, connection_id: str, data: bytes) -> Optional[bytes]:
        """
        压缩数据
        
        Args:
            connection_id: 连接ID
            data: 原始数据
            
        Returns:
            压缩后的数据，失败返回None
        """
        if connection_id not in self.active_connections:
            logger.error(f"连接不存在: {connection_id}")
            return None
        
        if not data:
            return data
        
        # 检查带宽限制
        if not self._check_bandwidth_limit(len(data)):
            logger.warning(f"带宽限制，延迟处理: {connection_id}")
            await asyncio.sleep(0.001)  # 短暂延迟
        
        connection = self.active_connections[connection_id]
        compressor = self.compressor_cache.get(connection_id)
        
        if not compressor:
            logger.error(f"压缩器不存在: {connection_id}")
            return None
        
        try:
            start_time = time.perf_counter()
            compressed_data = compressor['compress'](data)
            compress_time = time.perf_counter() - start_time
            
            # 更新统计信息 - 使用统一的统计工具
            from compression_stats_utils import update_global_compression_stats, get_connection_stats

            # 更新全局统计
            update_global_compression_stats(
                self.global_stats, len(data), len(compressed_data), compress_time
            )

            # 更新连接统计
            conn_stats = get_connection_stats(self.global_stats, connection_id)
            conn_stats.update_compression(len(data), len(compressed_data), compress_time)
            
            return compressed_data
            
        except Exception as e:
            logger.error(f"压缩失败 {connection_id}: {e}")
            # 使用统一的错误记录
            conn_stats = get_connection_stats(self.global_stats, connection_id)
            conn_stats.record_error()
            return data  # 失败时返回原始数据
    
    async def decompress_data(self, connection_id: str, compressed_data: bytes) -> Optional[bytes]:
        """
        解压数据
        
        Args:
            connection_id: 连接ID
            compressed_data: 压缩数据
            
        Returns:
            解压后的数据，失败返回None
        """
        if connection_id not in self.active_connections:
            logger.error(f"连接不存在: {connection_id}")
            return None
        
        if not compressed_data:
            return compressed_data
        
        connection = self.active_connections[connection_id]
        compressor = self.compressor_cache.get(connection_id)
        
        if not compressor:
            logger.error(f"解压器不存在: {connection_id}")
            return None
        
        try:
            start_time = time.perf_counter()
            decompressed_data = compressor['decompress'](compressed_data)
            decompress_time = time.perf_counter() - start_time
            
            # 更新统计信息
            connection['decompression_count'] += 1
            connection['last_activity'] = time.time()
            
            # 记录性能数据
            stats = self.connection_stats[connection_id]
            stats['decompression_times'].append(decompress_time)
            
            return decompressed_data
            
        except Exception as e:
            logger.error(f"解压失败 {connection_id}: {e}")
            connection['errors'] += 1
            return compressed_data  # 失败时返回原始数据
    
    async def close_connection(self, connection_id: str):
        """关闭连接"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
            self.global_stats['active_connections'] -= 1
        
        if connection_id in self.connection_stats:
            del self.connection_stats[connection_id]
        
        if connection_id in self.compressor_cache:
            del self.compressor_cache[connection_id]
        
        logger.info(f"连接已关闭: {connection_id}")
    
    def get_connection_stats(self, connection_id: str) -> Optional[Dict[str, Any]]:
        """获取连接统计信息"""
        if connection_id not in self.active_connections:
            return None
        
        connection = self.active_connections[connection_id]
        stats = self.connection_stats[connection_id]
        
        # 计算平均值
        avg_compress_time = sum(stats['compression_times']) / len(stats['compression_times']) if stats['compression_times'] else 0
        avg_decompress_time = sum(stats['decompression_times']) / len(stats['decompression_times']) if stats['decompression_times'] else 0
        avg_compression_ratio = sum(stats['compression_ratios']) / len(stats['compression_ratios']) if stats['compression_ratios'] else 1.0
        
        return {
            'connection_id': connection_id,
            'stream_type': connection['stream_type'],
            'strategy': connection['strategy'],
            'uptime_seconds': time.time() - connection['created_at'],
            'bytes_original': connection['bytes_original'],
            'bytes_compressed': connection['bytes_compressed'],
            'compression_ratio': avg_compression_ratio,
            'compression_rate': (1 - avg_compression_ratio) * 100,
            'compression_count': connection['compression_count'],
            'decompression_count': connection['decompression_count'],
            'avg_compress_time_ms': avg_compress_time * 1000,
            'avg_decompress_time_ms': avg_decompress_time * 1000,
            'errors': connection['errors'],
            'last_activity': connection['last_activity']
        }
    
    def get_global_stats(self) -> Dict[str, Any]:
        """获取全局统计信息"""
        uptime = time.time() - self.global_stats['start_time']
        
        if self.global_stats['total_original_bytes'] > 0:
            overall_compression_ratio = self.global_stats['total_compressed_bytes'] / self.global_stats['total_original_bytes']
            overall_compression_rate = (1 - overall_compression_ratio) * 100
        else:
            overall_compression_ratio = 1.0
            overall_compression_rate = 0.0
        
        # 计算当前带宽使用率
        current_bandwidth_usage = len(self.bandwidth_tokens) / self.bandwidth_limit_bps * 100
        
        return {
            'uptime_seconds': uptime,
            'max_concurrent_users': self.max_concurrent_users,
            'bandwidth_limit_mbps': self.bandwidth_limit_mbps,
            'active_connections': self.global_stats['active_connections'],
            'total_connections': self.global_stats['total_connections'],
            'total_original_bytes': self.global_stats['total_original_bytes'],
            'total_compressed_bytes': self.global_stats['total_compressed_bytes'],
            'overall_compression_ratio': overall_compression_ratio,
            'overall_compression_rate': overall_compression_rate,
            'bandwidth_usage_percent': current_bandwidth_usage,
            'available_strategies': list(self.compressors.keys())
        }
    
    def _start_background_tasks(self):
        """启动后台任务"""
        def cleanup_inactive_connections():
            """清理非活跃连接"""
            while True:
                try:
                    current_time = time.time()
                    inactive_connections = []
                    
                    for conn_id, conn_info in self.active_connections.items():
                        # 12小时无活动则清理连接
                        if current_time - conn_info['last_activity'] > 12 * 3600:
                            inactive_connections.append(conn_id)
                    
                    for conn_id in inactive_connections:
                        asyncio.create_task(self.close_connection(conn_id))
                        logger.info(f"清理非活跃连接: {conn_id}")
                    
                    time.sleep(300)  # 每5分钟检查一次
                except Exception as e:
                    logger.error(f"清理任务异常: {e}")
                    time.sleep(60)
        
        # 启动清理线程
        cleanup_thread = threading.Thread(target=cleanup_inactive_connections, daemon=True)
        cleanup_thread.start()

# 创建全局实例
virtualhere_middleware = VirtualHereCompressionMiddleware(
    max_concurrent_users=500,
    bandwidth_limit_mbps=3.0
)
