#!/usr/bin/env python3
"""
网络穿透工具压缩适配器
版本: 1.0
创建日期: 2025-01-15
描述: 为FRP、Linker等网络穿透工具提供统一的数据压缩接口
"""

import asyncio
import logging
import time
import os
import json
import socket
import threading
from typing import Dict, Any, Optional, Callable, Tuple, Union, List
from enum import Enum
import struct
import hashlib
import queue
import weakref
import random

from virtualhere_compression_middleware import VirtualHereCompressionMiddleware, DataStreamType

logger = logging.getLogger(__name__)

class TunnelType(str, Enum):
    """穿透工具类型"""
    FRP = "frp"
    LINKER = "linker"

class ProtocolType(str, Enum):
    """协议类型"""
    TCP = "tcp"
    UDP = "udp"
    HTTP = "http"
    HTTPS = "https"
    SSH = "ssh"
    RDP = "rdp"
    VNC = "vnc"
    CUSTOM = "custom"

class TunnelCompressionAdapter:
    """网络穿透工具压缩适配器"""
    
    def __init__(self, tunnel_type: TunnelType, protocol: ProtocolType, 
                 max_concurrent_users: int = 500, bandwidth_limit_mbps: float = 3.0):
        """
        初始化压缩适配器
        
        Args:
            tunnel_type: 穿透工具类型
            protocol: 协议类型
            max_concurrent_users: 最大并发用户数
            bandwidth_limit_mbps: 带宽限制(Mbps)
        """
        self.tunnel_type = tunnel_type
        self.protocol = protocol
        
        # 创建压缩中间件
        self.middleware = VirtualHereCompressionMiddleware(
            max_concurrent_users=max_concurrent_users,
            bandwidth_limit_mbps=bandwidth_limit_mbps
        )
        
        # 连接映射
        self.connections: Dict[str, Dict[str, Any]] = {}
        
        # 统计信息
        self.stats = {
            'start_time': time.time(),
            'total_connections': 0,
            'active_connections': 0,
            'total_bytes_in': 0,
            'total_bytes_out': 0,
            'total_bytes_compressed': 0,
            'total_bytes_decompressed': 0,
            'compression_errors': 0,
            'decompression_errors': 0
        }
        
        # 初始化协议处理器
        self._init_protocol_handlers()
        
        logger.info(f"初始化 {tunnel_type} 压缩适配器，协议: {protocol}")
    
    def _init_protocol_handlers(self):
        """初始化协议处理器"""
        # 协议到数据流类型的映射
        self.protocol_to_stream_type = {
            ProtocolType.HTTP: DataStreamType.WEB_HTTP,
            ProtocolType.HTTPS: DataStreamType.WEB_HTTPS,
            ProtocolType.TCP: DataStreamType.VIRTUALHERE_USB,
            ProtocolType.UDP: DataStreamType.VIRTUALHERE_USB,
            ProtocolType.SSH: DataStreamType.VIRTUALHERE_CONTROL,
            ProtocolType.RDP: DataStreamType.VIRTUALHERE_USB,
            ProtocolType.VNC: DataStreamType.VIRTUALHERE_USB,
            ProtocolType.CUSTOM: DataStreamType.VIRTUALHERE_USB
        }
        
        # 协议处理器
        self.protocol_handlers = {
            ProtocolType.HTTP: self._handle_http_data,
            ProtocolType.HTTPS: self._handle_https_data,
            ProtocolType.TCP: self._handle_tcp_data,
            ProtocolType.UDP: self._handle_udp_data,
            ProtocolType.SSH: self._handle_ssh_data,
            ProtocolType.RDP: self._handle_rdp_data,
            ProtocolType.VNC: self._handle_vnc_data,
            ProtocolType.CUSTOM: self._handle_custom_data
        }
    
    async def create_connection(self, connection_id: str, client_info: Dict[str, Any] = None) -> bool:
        """
        创建新连接
        
        Args:
            connection_id: 连接ID
            client_info: 客户端信息
            
        Returns:
            是否创建成功
        """
        if connection_id in self.connections:
            logger.warning(f"连接已存在: {connection_id}")
            return False
        
        # 获取数据流类型
        stream_type = self.protocol_to_stream_type.get(self.protocol, DataStreamType.VIRTUALHERE_USB)
        
        # 创建中间件连接
        success = await self.middleware.create_connection(
            connection_id, 
            stream_type,
            client_info
        )
        
        if not success:
            logger.error(f"创建中间件连接失败: {connection_id}")
            return False
        
        # 记录连接信息
        self.connections[connection_id] = {
            'created_at': time.time(),
            'last_activity': time.time(),
            'client_info': client_info or {},
            'bytes_in': 0,
            'bytes_out': 0,
            'bytes_compressed': 0,
            'bytes_decompressed': 0,
            'compression_errors': 0,
            'decompression_errors': 0
        }
        
        # 更新统计信息
        self.stats['total_connections'] += 1
        self.stats['active_connections'] += 1
        
        logger.info(f"创建连接: {connection_id}, 协议: {self.protocol}")
        return True
    
    async def close_connection(self, connection_id: str):
        """关闭连接"""
        if connection_id not in self.connections:
            logger.warning(f"连接不存在: {connection_id}")
            return
        
        # 关闭中间件连接
        await self.middleware.close_connection(connection_id)
        
        # 移除连接记录
        del self.connections[connection_id]
        
        # 更新统计信息
        self.stats['active_connections'] -= 1
        
        logger.info(f"关闭连接: {connection_id}")
    
    async def compress_data(self, connection_id: str, data: bytes) -> Optional[bytes]:
        """
        压缩数据
        
        Args:
            connection_id: 连接ID
            data: 原始数据
            
        Returns:
            压缩后的数据，失败返回None
        """
        if not data:
            return data
        
        if connection_id not in self.connections:
            logger.error(f"连接不存在: {connection_id}")
            return None
        
        conn_info = self.connections[connection_id]
        conn_info['last_activity'] = time.time()
        conn_info['bytes_in'] += len(data)
        self.stats['total_bytes_in'] += len(data)
        
        # 使用协议处理器预处理数据
        handler = self.protocol_handlers.get(self.protocol)
        if handler:
            processed_data = await handler(data, connection_id, 'compress')
        else:
            processed_data = data
        
        # 压缩数据
        try:
            compressed = await self.middleware.compress_data(connection_id, processed_data)
            
            if compressed:
                conn_info['bytes_compressed'] += len(compressed)
                self.stats['total_bytes_compressed'] += len(compressed)
                return compressed
            else:
                conn_info['compression_errors'] += 1
                self.stats['compression_errors'] += 1
                return processed_data  # 失败时返回原始数据
                
        except Exception as e:
            logger.error(f"压缩数据失败 {connection_id}: {e}")
            conn_info['compression_errors'] += 1
            self.stats['compression_errors'] += 1
            return processed_data  # 失败时返回原始数据
    
    async def decompress_data(self, connection_id: str, data: bytes) -> Optional[bytes]:
        """
        解压数据
        
        Args:
            connection_id: 连接ID
            data: 压缩数据
            
        Returns:
            解压后的数据，失败返回None
        """
        if not data:
            return data
        
        if connection_id not in self.connections:
            logger.error(f"连接不存在: {connection_id}")
            return None
        
        conn_info = self.connections[connection_id]
        conn_info['last_activity'] = time.time()
        
        # 解压数据
        try:
            decompressed = await self.middleware.decompress_data(connection_id, data)
            
            if decompressed:
                conn_info['bytes_decompressed'] += len(decompressed)
                self.stats['total_bytes_decompressed'] += len(decompressed)
                
                # 使用协议处理器后处理数据
                handler = self.protocol_handlers.get(self.protocol)
                if handler:
                    processed_data = await handler(decompressed, connection_id, 'decompress')
                else:
                    processed_data = decompressed
                
                conn_info['bytes_out'] += len(processed_data)
                self.stats['total_bytes_out'] += len(processed_data)
                
                return processed_data
            else:
                conn_info['decompression_errors'] += 1
                self.stats['decompression_errors'] += 1
                return data  # 失败时返回原始数据
                
        except Exception as e:
            logger.error(f"解压数据失败 {connection_id}: {e}")
            conn_info['decompression_errors'] += 1
            self.stats['decompression_errors'] += 1
            return data  # 失败时返回原始数据
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        uptime = time.time() - self.stats['start_time']
        
        # 计算压缩率
        if self.stats['total_bytes_in'] > 0:
            compression_ratio = self.stats['total_bytes_compressed'] / self.stats['total_bytes_in']
            compression_rate = (1 - compression_ratio) * 100
        else:
            compression_ratio = 1.0
            compression_rate = 0.0
        
        # 计算带宽使用
        bytes_per_second = (self.stats['total_bytes_in'] + self.stats['total_bytes_out']) / uptime if uptime > 0 else 0
        bandwidth_mbps = bytes_per_second * 8 / 1024 / 1024
        
        return {
            'tunnel_type': self.tunnel_type,
            'protocol': self.protocol,
            'uptime_seconds': uptime,
            'total_connections': self.stats['total_connections'],
            'active_connections': self.stats['active_connections'],
            'total_bytes_in': self.stats['total_bytes_in'],
            'total_bytes_out': self.stats['total_bytes_out'],
            'total_bytes_compressed': self.stats['total_bytes_compressed'],
            'total_bytes_decompressed': self.stats['total_bytes_decompressed'],
            'compression_ratio': compression_ratio,
            'compression_rate': compression_rate,
            'bandwidth_usage_mbps': bandwidth_mbps,
            'compression_errors': self.stats['compression_errors'],
            'decompression_errors': self.stats['decompression_errors'],
            'middleware_stats': self.middleware.get_global_stats()
        }
    
    # 协议处理器
    async def _handle_http_data(self, data: bytes, connection_id: str, direction: str) -> bytes:
        """处理HTTP数据"""
        # 简单实现，实际应用中可能需要更复杂的HTTP解析
        return data
    
    async def _handle_https_data(self, data: bytes, connection_id: str, direction: str) -> bytes:
        """处理HTTPS数据"""
        # HTTPS数据已加密，直接传递
        return data
    
    async def _handle_tcp_data(self, data: bytes, connection_id: str, direction: str) -> bytes:
        """处理TCP数据"""
        return data
    
    async def _handle_udp_data(self, data: bytes, connection_id: str, direction: str) -> bytes:
        """处理UDP数据"""
        return data
    
    async def _handle_ssh_data(self, data: bytes, connection_id: str, direction: str) -> bytes:
        """处理SSH数据"""
        return data
    
    async def _handle_rdp_data(self, data: bytes, connection_id: str, direction: str) -> bytes:
        """处理RDP数据"""
        return data
    
    async def _handle_vnc_data(self, data: bytes, connection_id: str, direction: str) -> bytes:
        """处理VNC数据"""
        return data
    
    async def _handle_custom_data(self, data: bytes, connection_id: str, direction: str) -> bytes:
        """处理自定义协议数据"""
        return data

# 创建全局适配器实例
tunnel_adapters: Dict[str, TunnelCompressionAdapter] = {}
