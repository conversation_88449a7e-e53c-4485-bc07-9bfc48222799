#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniLink企业级全链路压力测试系统 v3.0
基于已修复的架构，完整模拟从服务器的本地数据库同步逻辑

功能覆盖：
1. 从服务器注册（支持最大10,000台）
2. 本地SQLite数据库模拟
3. USB设备自动上报和同步
4. 智能心跳机制（轻量级/完整同步）
5. 主从数据库同步验证
6. 完整工作流验证

技术规范：
- IP地址分配：127.0.x.x网段，支持10,000台服务器
- 设备数据：真实PID/VID，SHA256哈希签名
- device_id格式：vendor_id:product_id:bus:address
- 本地数据库：每台服务器独立SQLite文件
"""

import asyncio
import aiohttp
import sqlite3
import os
import time
import random
import logging
import json
import argparse
import hashlib
import tempfile
import shutil
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import uuid
import threading
from concurrent.futures import ThreadPoolExecutor
import signal
import sys
import psutil
import platform
from dataclasses import dataclass, asdict
from enum import Enum

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'enterprise_stress_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DeviceChangeType(Enum):
    """设备变动类型"""
    ADDED = "added"
    REMOVED = "removed"
    MODIFIED = "modified"
    NO_CHANGE = "no_change"

@dataclass
class USBDeviceInfo:
    """USB设备信息"""
    vendor_id: str
    product_id: str
    serial_number: str
    description: str
    manufacturer: str
    bus: int
    address: int
    device_path: str
    hardware_signature: str
    physical_port: str
    device_id: str
    
    def to_sync_format(self) -> Dict[str, Any]:
        """转换为设备同步API格式"""
        return {
            "hardware_signature": self.hardware_signature,
            "physical_port": self.physical_port,
            "vendor_id": self.vendor_id,
            "product_id": self.product_id,
            "serial_number": self.serial_number,
            "description": self.description,
            "device_path": self.device_path,
            "custom_name": None,
            "notes": f"企业级压力测试设备 - {self.description}",
            "config_data": {
                "device_type": "encryption_dongle" if "加密锁" in self.description or "U盾" in self.description else "other",
                "auto_bind_eligible": True,
                "is_real_hardware": True,
                "manufacturer": self.manufacturer
            }
        }

class USBDeviceDatabase:
    """真实USB设备数据库"""
    
    DEVICE_CATALOG = [
        {"vendor_id": "096e", "product_id": "031b", "name": "广联达加密锁", "manufacturer": "广联达"},
        {"vendor_id": "0471", "product_id": "0888", "name": "新点加密锁", "manufacturer": "新点"},
        {"vendor_id": "1a86", "product_id": "7523", "name": "博威加密锁", "manufacturer": "博威"},
        {"vendor_id": "0483", "product_id": "5740", "name": "CA证书锁", "manufacturer": "CA厂商"},
        {"vendor_id": "096e", "product_id": "0006", "name": "银行U盾", "manufacturer": "银行"},
        {"vendor_id": "1234", "product_id": "5678", "name": "通用加密锁", "manufacturer": "通用"},
        {"vendor_id": "2468", "product_id": "1357", "name": "财务加密锁", "manufacturer": "财务"},
        {"vendor_id": "1111", "product_id": "2222", "name": "税务加密锁", "manufacturer": "税务"},
        {"vendor_id": "0781", "product_id": "5567", "name": "SanDisk USB", "manufacturer": "SanDisk"},
        {"vendor_id": "0930", "product_id": "6545", "name": "Toshiba USB", "manufacturer": "Toshiba"},
        {"vendor_id": "046d", "product_id": "c52b", "name": "Logitech Mouse", "manufacturer": "Logitech"},
        {"vendor_id": "413c", "product_id": "2113", "name": "Dell Keyboard", "manufacturer": "Dell"},
    ]
    
    @classmethod
    def generate_device(cls, server_id: int, port: int, timestamp: int) -> USBDeviceInfo:
        """生成USB设备信息"""
        device_template = random.choice(cls.DEVICE_CATALOG)
        
        # 生成全局唯一的序列号
        serial_number = f"ENT{server_id:05d}P{port:02d}T{timestamp}{random.randint(1000, 9999)}"
        
        # 生成hardware_signature（模拟真实从服务器的逻辑）
        signature_components = [
            f"VID:{device_template['vendor_id']}",
            f"PID:{device_template['product_id']}",
            f"SN:{serial_number}",
            f"DESC:{device_template['name']}",
            f"MFG:{device_template['manufacturer']}"
        ]
        signature_string = "|".join(signature_components)
        hardware_signature = hashlib.sha256(signature_string.encode('utf-8')).hexdigest()[:16]
        
        # 生成physical_port（模拟真实格式）
        physical_port = f"BUS:1|ADDR:{port}"
        
        # 生成device_id（使用修复后的格式）
        device_id = f"{device_template['vendor_id']}:{device_template['product_id']}:1:{port}"
        
        return USBDeviceInfo(
            vendor_id=device_template["vendor_id"],
            product_id=device_template["product_id"],
            serial_number=serial_number,
            description=device_template["name"],
            manufacturer=device_template["manufacturer"],
            bus=1,
            address=port,
            device_path=f"/dev/bus/usb/001/{port:03d}",
            hardware_signature=hardware_signature,
            physical_port=physical_port,
            device_id=device_id
        )

class LocalSlaveDatabase:
    """从服务器本地数据库模拟"""
    
    def __init__(self, server_id: int, db_dir: str):
        self.server_id = server_id
        self.db_path = os.path.join(db_dir, f"slave_{server_id:05d}_port_locations.db")
        self._lock = threading.RLock()
        self._initialize_database()
        
    def _initialize_database(self):
        """初始化本地数据库表结构"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 创建端口位置表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS port_locations (
                        location_code TEXT PRIMARY KEY,
                        server_uuid TEXT NOT NULL,
                        controller_id TEXT NOT NULL,
                        hub_id TEXT NOT NULL,
                        port_number INTEGER NOT NULL,
                        hub_path TEXT DEFAULT '',
                        created_at TEXT NOT NULL,
                        last_seen TEXT NOT NULL,
                        is_active BOOLEAN DEFAULT 1
                    )
                ''')
                
                # 创建设备绑定表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS device_bindings (
                        binding_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        location_code TEXT NOT NULL,
                        device_signature TEXT NOT NULL,
                        custom_name TEXT DEFAULT '',
                        notes TEXT DEFAULT '',
                        config_data TEXT DEFAULT '{}',
                        binding_time TEXT NOT NULL,
                        last_verified TEXT NOT NULL,
                        is_bound BOOLEAN DEFAULT 1,
                        binding_confidence REAL DEFAULT 1.0,
                        FOREIGN KEY (location_code) REFERENCES port_locations (location_code),
                        UNIQUE(location_code, device_signature)
                    )
                ''')
                
                # 创建设备历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS device_history (
                        history_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        location_code TEXT NOT NULL,
                        device_signature TEXT NOT NULL,
                        event_type TEXT NOT NULL,
                        event_time TEXT NOT NULL,
                        event_data TEXT DEFAULT '{}',
                        FOREIGN KEY (location_code) REFERENCES port_locations (location_code)
                    )
                ''')
                
                # 创建同步状态表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sync_status (
                        sync_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sync_type TEXT NOT NULL,
                        sync_time TEXT NOT NULL,
                        sync_result TEXT NOT NULL,
                        device_count INTEGER DEFAULT 0,
                        sync_data TEXT DEFAULT '{}'
                    )
                ''')
                
                conn.commit()
                conn.close()
                
        except Exception as e:
            logger.error(f"初始化本地数据库失败 (服务器{self.server_id}): {e}")
    
    def save_devices(self, devices: List[USBDeviceInfo]) -> bool:
        """保存设备信息到本地数据库"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                current_time = datetime.now().isoformat()
                
                for device in devices:
                    # 保存端口位置
                    location_code = f"HUB1-PORT{device.address}"
                    cursor.execute('''
                        INSERT OR REPLACE INTO port_locations 
                        (location_code, server_uuid, controller_id, hub_id, port_number, 
                         hub_path, created_at, last_seen, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (location_code, f"server_{self.server_id}", "USB_CTRL_1", "HUB_1", 
                          device.address, device.physical_port, current_time, current_time, True))
                    
                    # 保存设备绑定
                    cursor.execute('''
                        INSERT OR REPLACE INTO device_bindings 
                        (location_code, device_signature, custom_name, notes, config_data,
                         binding_time, last_verified, is_bound, binding_confidence)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (location_code, device.hardware_signature, "", device.description,
                          json.dumps({"device_id": device.device_id, "vendor_id": device.vendor_id}),
                          current_time, current_time, True, 1.0))
                
                conn.commit()
                conn.close()
                return True
                
        except Exception as e:
            logger.error(f"保存设备到本地数据库失败 (服务器{self.server_id}): {e}")
            return False
    
    def get_device_count(self) -> int:
        """获取本地数据库中的设备数量"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM device_bindings WHERE is_bound = 1")
                count = cursor.fetchone()[0]
                conn.close()
                return count
        except Exception as e:
            logger.error(f"获取设备数量失败 (服务器{self.server_id}): {e}")
            return 0
    
    def record_sync(self, sync_type: str, sync_result: str, device_count: int, sync_data: Dict[str, Any]) -> bool:
        """记录同步状态"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO sync_status (sync_type, sync_time, sync_result, device_count, sync_data)
                    VALUES (?, ?, ?, ?, ?)
                ''', (sync_type, datetime.now().isoformat(), sync_result, device_count, json.dumps(sync_data)))
                conn.commit()
                conn.close()
                return True
        except Exception as e:
            logger.error(f"记录同步状态失败 (服务器{self.server_id}): {e}")
            return False

class EnterpriseSlaveSimulator:
    """企业级从服务器模拟器（完整本地数据库支持）"""

    def __init__(self, server_id: int, master_url: str, db_dir: str, behavior_mode: str = "stable"):
        self.server_id = server_id
        self.master_url = master_url
        self.behavior_mode = behavior_mode

        # IP地址计算（支持10,000台服务器）
        self.server_ip = self._calculate_ip_address(server_id)
        self.server_port = 8889 + server_id
        self.vh_port = 7575 + server_id

        # 服务器标识
        self.server_name = f"OmniLink-ENT-{server_id:05d}"
        self.hardware_uuid = str(uuid.uuid4())

        # 本地数据库
        self.local_db = LocalSlaveDatabase(server_id, db_dir)

        # 设备管理
        self.devices: List[USBDeviceInfo] = []
        self.device_change_type = DeviceChangeType.NO_CHANGE
        self.last_device_sync_time: Optional[datetime] = None

        # 网络会话
        self.session: Optional[aiohttp.ClientSession] = None

        # 状态管理
        self.is_running = False
        self.registered = False
        self.last_heartbeat_time: Optional[datetime] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.device_change_task: Optional[asyncio.Task] = None

        # 统计信息
        self.stats = {
            'register_attempts': 0,
            'register_success': 0,
            'heartbeat_sent': 0,
            'heartbeat_success': 0,
            'heartbeat_failed': 0,
            'device_sync_attempts': 0,
            'device_sync_success': 0,
            'device_changes': 0,
            'timeout_count': 0,
            'total_response_time': 0.0,
            'max_response_time': 0.0,
            'avg_response_time': 0.0
        }

        # 行为控制
        self.restart_cycle_count = 0
        self.device_change_probability = 0.1  # 10%概率发生设备变动

    def _calculate_ip_address(self, server_id: int) -> str:
        """计算IP地址（支持10,000台服务器）"""
        # 计算公式：127.0.((server_id-1)//254+1).((server_id-1)%254+2)
        third_octet = ((server_id - 1) // 254) + 1
        fourth_octet = ((server_id - 1) % 254) + 2
        return f"127.0.{third_octet}.{fourth_octet}"

    async def start(self) -> bool:
        """启动从服务器模拟器"""
        try:
            # 创建HTTP会话
            timeout = aiohttp.ClientTimeout(total=30.0)
            self.session = aiohttp.ClientSession(timeout=timeout)

            # 生成初始设备
            await self._generate_initial_devices()

            # 注册到主服务器
            if await self._register():
                self.registered = True
                self.is_running = True

                # 启动心跳任务
                self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())

                # 启动设备变动模拟任务
                if self.behavior_mode in ["dynamic_devices", "restart"]:
                    self.device_change_task = asyncio.create_task(self._device_change_loop())

                logger.info(f"✅ {self.server_name} 启动成功 (IP: {self.server_ip})")
                return True
            else:
                logger.error(f"❌ {self.server_name} 注册失败")
                return False

        except Exception as e:
            logger.error(f"❌ {self.server_name} 启动异常: {e}")
            return False

    async def stop(self):
        """停止从服务器模拟器"""
        self.is_running = False

        # 停止任务
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        if self.device_change_task:
            self.device_change_task.cancel()

        # 关闭会话
        if self.session:
            await self.session.close()

        logger.info(f"🔄 {self.server_name} 已停止")

    async def _generate_initial_devices(self):
        """生成初始USB设备"""
        device_count = random.randint(3, 8)  # 每台服务器3-8个设备
        timestamp = int(time.time())

        self.devices = []
        for port in range(1, device_count + 1):
            device = USBDeviceDatabase.generate_device(self.server_id, port, timestamp)
            self.devices.append(device)

        # 保存到本地数据库
        if self.local_db.save_devices(self.devices):
            logger.debug(f"📱 {self.server_name} 生成了 {len(self.devices)} 个初始设备")
        else:
            logger.warning(f"⚠️ {self.server_name} 本地数据库保存失败")

    async def _register(self) -> bool:
        """注册到主服务器"""
        self.stats['register_attempts'] += 1

        register_data = {
            "server_name": self.server_name,
            "server_ip": self.server_ip,
            "server_port": self.server_port,
            "vh_port": self.vh_port,
            "hardware_uuid": self.hardware_uuid,
            "hardware_info": {
                "os": "Ubuntu 20.04 LTS",
                "arch": "x86_64",
                "python_version": "3.11.0",
                "total_memory": "8GB",
                "cpu_cores": 4,
                "hostname": f"ent-slave-{self.server_id:05d}",
                "kernel": "5.4.0-generic"
            },
            "description": f"企业级压力测试从服务器 #{self.server_id}",
            "version": "3.0"
        }

        try:
            start_time = time.time()
            async with self.session.post(
                f"{self.master_url}/api/v1/slave/register",
                json=register_data,
                headers={'Content-Type': 'application/json; charset=utf-8'}
            ) as response:
                response_time = time.time() - start_time
                self._update_response_stats(response_time)

                if response.status == 200:
                    self.stats['register_success'] += 1
                    result = await response.json()
                    action = result.get('action', 'unknown')

                    # 重要：更新hardware_uuid为服务器返回的实际UUID
                    if 'hardware_uuid' in result:
                        self.hardware_uuid = result['hardware_uuid']
                        logger.debug(f"✅ {self.server_name} 注册成功，使用UUID: {self.hardware_uuid}")

                    logger.debug(f"✅ {self.server_name} 注册成功 ({response_time:.3f}s) - {action}")

                    # 注册成功后立即同步设备
                    await self._sync_devices_to_master()
                    return True
                else:
                    response_text = await response.text()
                    logger.error(f"❌ {self.server_name} 注册失败: HTTP {response.status}")
                    logger.error(f"错误详情: {response_text}")
                    return False

        except asyncio.TimeoutError:
            self.stats['timeout_count'] += 1
            logger.error(f"⏰ {self.server_name} 注册超时")
            return False
        except Exception as e:
            logger.error(f"❌ {self.server_name} 注册异常: {e}")
            return False

    async def _sync_devices_to_master(self) -> bool:
        """向主服务器同步设备清单"""
        self.stats['device_sync_attempts'] += 1

        # 构建设备同步请求
        sync_request = {
            'hardware_uuid': self.hardware_uuid,
            'devices': [device.to_sync_format() for device in self.devices],
            'sync_timestamp': datetime.now().isoformat()
        }

        try:
            start_time = time.time()
            sync_url = f"{self.master_url}/api/v1/slave/{self.hardware_uuid}/sync-devices"

            async with self.session.post(sync_url, json=sync_request) as response:
                response_time = time.time() - start_time
                self._update_response_stats(response_time)

                if response.status == 200:
                    self.stats['device_sync_success'] += 1
                    result = await response.json()
                    sync_results = result.get('sync_results', {})

                    created = sync_results.get('created', 0)
                    updated = sync_results.get('updated', 0)

                    # 记录到本地数据库
                    self.local_db.record_sync(
                        "device_sync", "success", len(self.devices),
                        {"created": created, "updated": updated, "response_time": response_time}
                    )

                    self.last_device_sync_time = datetime.now()
                    logger.debug(f"📱 {self.server_name} 设备同步成功: 创建{created}, 更新{updated}")
                    return True
                else:
                    response_text = await response.text()
                    logger.error(f"❌ {self.server_name} 设备同步失败: HTTP {response.status}")
                    logger.error(f"错误详情: {response_text}")

                    # 记录失败到本地数据库
                    self.local_db.record_sync(
                        "device_sync", f"failed_http_{response.status}", len(self.devices),
                        {"error": response_text, "response_time": response_time}
                    )
                    return False

        except asyncio.TimeoutError:
            self.stats['timeout_count'] += 1
            logger.error(f"⏰ {self.server_name} 设备同步超时")
            self.local_db.record_sync("device_sync", "timeout", len(self.devices), {})
            return False
        except Exception as e:
            logger.error(f"❌ {self.server_name} 设备同步异常: {e}")
            self.local_db.record_sync("device_sync", f"exception_{type(e).__name__}", len(self.devices), {"error": str(e)})
            return False

    async def _heartbeat_loop(self):
        """心跳循环"""
        while self.is_running:
            try:
                # 检测设备变动
                has_device_changes = self._detect_device_changes()

                if has_device_changes:
                    # 有设备变动：发送完整同步
                    await self._send_full_heartbeat_with_sync()
                else:
                    # 无设备变动：发送轻量级心跳
                    await self._send_lightweight_heartbeat()

                # 根据行为模式调整心跳间隔
                if self.behavior_mode == "stable":
                    interval = 30 + random.uniform(-2, 2)  # 30±2秒
                elif self.behavior_mode == "dynamic_devices":
                    interval = 20 + random.uniform(-3, 3)  # 20±3秒
                elif self.behavior_mode == "restart":
                    interval = 15 + random.uniform(-2, 2)  # 15±2秒
                else:
                    interval = 30

                await asyncio.sleep(interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ {self.server_name} 心跳循环异常: {e}")
                await asyncio.sleep(5)

    def _detect_device_changes(self) -> bool:
        """检测设备变动"""
        if self.behavior_mode == "stable":
            return False
        elif self.behavior_mode == "dynamic_devices":
            # 10%概率发生设备变动
            return random.random() < self.device_change_probability
        elif self.behavior_mode == "restart":
            # 重启模式：周期性设备变动
            self.restart_cycle_count += 1
            return self.restart_cycle_count % 10 == 0  # 每10次心跳重启一次
        else:
            return False

    async def _send_lightweight_heartbeat(self):
        """发送轻量级心跳（无设备变动）"""
        self.stats['heartbeat_sent'] += 1

        heartbeat_data = {
            "server_name": self.server_name,  # 添加服务器名称用于查找
            "hardware_uuid": self.hardware_uuid,
            "timestamp": datetime.now().isoformat(),
            "status": "online",
            "device_count": len(self.devices),
            "device_count_summary": len(self.devices),  # 修复：添加主服务器期望的字段
            "hub_count": 1,  # 模拟1个USB Hub
            "total_ports": 8,  # 模拟8个端口
            "occupied_ports": len(self.devices),
            "free_ports": max(0, 8 - len(self.devices)),
            "has_changes": False,
            "heartbeat_type": "lightweight",
            "vh_status": "running",
            "system_info": {
                "cpu_usage": random.uniform(5, 15),
                "memory_usage": random.uniform(10, 30),
                "disk_usage": random.uniform(20, 50)
            }
        }

        try:
            start_time = time.time()
            async with self.session.post(
                f"{self.master_url}/api/v1/slave/heartbeat",
                json=heartbeat_data
            ) as response:
                response_time = time.time() - start_time
                self._update_response_stats(response_time)

                if response.status == 200:
                    self.stats['heartbeat_success'] += 1
                    self.last_heartbeat_time = datetime.now()
                    logger.debug(f"💓 {self.server_name} 轻量级心跳成功 ({response_time:.3f}s)")
                else:
                    self.stats['heartbeat_failed'] += 1
                    logger.warning(f"⚠️ {self.server_name} 轻量级心跳失败: HTTP {response.status}")

        except asyncio.TimeoutError:
            self.stats['timeout_count'] += 1
            self.stats['heartbeat_failed'] += 1
            logger.warning(f"⏰ {self.server_name} 轻量级心跳超时")
        except Exception as e:
            self.stats['heartbeat_failed'] += 1
            logger.error(f"❌ {self.server_name} 轻量级心跳异常: {e}")

    async def _send_full_heartbeat_with_sync(self):
        """发送完整心跳（包含设备同步）"""
        self.stats['heartbeat_sent'] += 1

        # 先模拟设备变动
        await self._simulate_device_changes()

        # 发送心跳（注意：主服务器心跳API只接受lightweight类型）
        heartbeat_data = {
            "server_name": self.server_name,  # 添加服务器名称用于查找
            "hardware_uuid": self.hardware_uuid,
            "timestamp": datetime.now().isoformat(),
            "status": "online",
            "device_count": len(self.devices),
            "device_count_summary": len(self.devices),  # 修复：添加主服务器期望的字段
            "hub_count": 1,  # 模拟1个USB Hub
            "total_ports": 8,  # 模拟8个端口
            "occupied_ports": len(self.devices),
            "free_ports": max(0, 8 - len(self.devices)),
            "has_changes": True,
            "heartbeat_type": "lightweight",  # 修复：使用lightweight而不是full_sync
            "change_type": self.device_change_type.value,
            "vh_status": "running",
            "system_info": {
                "cpu_usage": random.uniform(5, 15),
                "memory_usage": random.uniform(10, 30),
                "disk_usage": random.uniform(20, 50)
            }
        }

        try:
            start_time = time.time()
            async with self.session.post(
                f"{self.master_url}/api/v1/slave/heartbeat",
                json=heartbeat_data
            ) as response:
                response_time = time.time() - start_time
                self._update_response_stats(response_time)

                if response.status == 200:
                    self.stats['heartbeat_success'] += 1
                    self.last_heartbeat_time = datetime.now()

                    # 心跳成功后，同步设备
                    await self._sync_devices_to_master()

                    logger.debug(f"💓 {self.server_name} 完整心跳+同步成功 ({response_time:.3f}s)")
                else:
                    self.stats['heartbeat_failed'] += 1
                    logger.warning(f"⚠️ {self.server_name} 完整心跳失败: HTTP {response.status}")

        except asyncio.TimeoutError:
            self.stats['timeout_count'] += 1
            self.stats['heartbeat_failed'] += 1
            logger.warning(f"⏰ {self.server_name} 完整心跳超时")
        except Exception as e:
            self.stats['heartbeat_failed'] += 1
            logger.error(f"❌ {self.server_name} 完整心跳异常: {e}")

    async def _simulate_device_changes(self):
        """模拟设备变动"""
        change_type = random.choice([DeviceChangeType.ADDED, DeviceChangeType.REMOVED, DeviceChangeType.MODIFIED])

        if change_type == DeviceChangeType.ADDED and len(self.devices) < 10:
            # 添加新设备
            new_port = max([d.address for d in self.devices]) + 1 if self.devices else 1
            new_device = USBDeviceDatabase.generate_device(self.server_id, new_port, int(time.time()))
            self.devices.append(new_device)
            self.device_change_type = DeviceChangeType.ADDED
            self.stats['device_changes'] += 1
            logger.debug(f"🔌 {self.server_name} 添加设备: {new_device.description}")

        elif change_type == DeviceChangeType.REMOVED and len(self.devices) > 1:
            # 移除设备
            removed_device = self.devices.pop(random.randint(0, len(self.devices) - 1))
            self.device_change_type = DeviceChangeType.REMOVED
            self.stats['device_changes'] += 1
            logger.debug(f"🔌 {self.server_name} 移除设备: {removed_device.description}")

        elif change_type == DeviceChangeType.MODIFIED and self.devices:
            # 修改设备（重新生成序列号）
            device_index = random.randint(0, len(self.devices) - 1)
            old_device = self.devices[device_index]
            new_device = USBDeviceDatabase.generate_device(self.server_id, old_device.address, int(time.time()))
            self.devices[device_index] = new_device
            self.device_change_type = DeviceChangeType.MODIFIED
            self.stats['device_changes'] += 1
            logger.debug(f"🔌 {self.server_name} 修改设备: {new_device.description}")

        # 更新本地数据库
        self.local_db.save_devices(self.devices)

    async def _device_change_loop(self):
        """设备变动循环（仅用于动态行为模式）"""
        while self.is_running:
            try:
                if self.behavior_mode == "dynamic_devices":
                    # 随机间隔的设备变动
                    await asyncio.sleep(random.uniform(60, 180))  # 1-3分钟
                    if random.random() < 0.3:  # 30%概率
                        await self._simulate_device_changes()

                elif self.behavior_mode == "restart":
                    # 模拟重启：清空所有设备，然后重新生成
                    await asyncio.sleep(random.uniform(300, 600))  # 5-10分钟
                    logger.info(f"🔄 {self.server_name} 模拟重启")
                    await self._generate_initial_devices()
                    self.device_change_type = DeviceChangeType.ADDED
                    self.stats['device_changes'] += 1

                else:
                    await asyncio.sleep(60)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ {self.server_name} 设备变动循环异常: {e}")
                await asyncio.sleep(30)

    def _update_response_stats(self, response_time: float):
        """更新响应时间统计"""
        self.stats['total_response_time'] += response_time
        if response_time > self.stats['max_response_time']:
            self.stats['max_response_time'] = response_time

        # 计算平均响应时间
        total_requests = (self.stats['register_attempts'] +
                         self.stats['heartbeat_sent'] +
                         self.stats['device_sync_attempts'])
        if total_requests > 0:
            self.stats['avg_response_time'] = self.stats['total_response_time'] / total_requests

    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        return {
            'server_id': self.server_id,
            'server_name': self.server_name,
            'server_ip': self.server_ip,
            'hardware_uuid': self.hardware_uuid,
            'is_running': self.is_running,
            'registered': self.registered,
            'device_count': len(self.devices),
            'local_db_device_count': self.local_db.get_device_count(),
            'behavior_mode': self.behavior_mode,
            'last_heartbeat': self.last_heartbeat_time.isoformat() if self.last_heartbeat_time else None,
            'last_device_sync': self.last_device_sync_time.isoformat() if self.last_device_sync_time else None,
            'stats': self.stats.copy()
        }

class HardwareMonitor:
    """硬件资源监控器"""

    def __init__(self, cpu_warning_threshold: float = 90.0, cpu_stop_threshold: float = 100.0):
        self.cpu_warning_threshold = cpu_warning_threshold
        self.cpu_stop_threshold = cpu_stop_threshold
        self.cpu_warning_count = 0
        self.cpu_stop_count = 0
        self.monitoring = False

    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统资源统计"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()

            # 内存使用情况
            memory = psutil.virtual_memory()

            # 磁盘使用情况
            disk = psutil.disk_usage('/')

            # 网络统计
            network = psutil.net_io_counters()

            return {
                'cpu_percent': cpu_percent,
                'cpu_count': cpu_count,
                'memory_total': memory.total,
                'memory_used': memory.used,
                'memory_percent': memory.percent,
                'memory_available': memory.available,
                'disk_total': disk.total,
                'disk_used': disk.used,
                'disk_percent': (disk.used / disk.total) * 100,
                'network_bytes_sent': network.bytes_sent,
                'network_bytes_recv': network.bytes_recv,
                'network_packets_sent': network.packets_sent,
                'network_packets_recv': network.packets_recv,
                'platform': platform.platform(),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"获取系统统计失败: {e}")
            return {}

    def check_resource_limits(self, stats: Dict[str, Any]) -> Dict[str, Any]:
        """检查资源限制"""
        alerts = {
            'cpu_warning': False,
            'cpu_critical': False,
            'should_stop': False,
            'warnings': [],
            'critical_alerts': []
        }

        cpu_percent = stats.get('cpu_percent', 0)

        # CPU警告检查
        if cpu_percent >= self.cpu_warning_threshold:
            self.cpu_warning_count += 1
            alerts['cpu_warning'] = True
            alerts['warnings'].append(f"CPU使用率高: {cpu_percent:.1f}% (警告阈值: {self.cpu_warning_threshold}%)")

            # 连续3分钟高CPU使用率
            if self.cpu_warning_count >= 3:
                alerts['warnings'].append(f"CPU使用率持续高负载 {self.cpu_warning_count} 分钟")
        else:
            self.cpu_warning_count = 0

        # CPU停止检查
        if cpu_percent >= self.cpu_stop_threshold:
            self.cpu_stop_count += 1
            alerts['cpu_critical'] = True
            alerts['critical_alerts'].append(f"CPU使用率达到停止阈值: {cpu_percent:.1f}%")

            # 连续5分钟100%CPU使用率
            if self.cpu_stop_count >= 5:
                alerts['should_stop'] = True
                alerts['critical_alerts'].append("CPU使用率持续100%超过5分钟，建议停止测试")
        else:
            self.cpu_stop_count = 0

        return alerts

class IncrementalStressTestController:
    """递增式压力测试控制器"""

    def __init__(self, master_url: str, db_dir: Optional[str] = None):
        self.master_url = master_url
        self.db_dir = db_dir or tempfile.mkdtemp(prefix="omnilink_incremental_test_")
        self.hardware_monitor = HardwareMonitor()

        # 测试阶段配置 - 快速启动到400台服务器
        self.test_phases = [
            {"name": "快速启动阶段（400台基线）", "target_servers": 400, "increment": 400, "wait_cycles": 1, "rapid_deployment": True},
            {"name": "第一扩展阶段（中等规模）", "target_servers": 800, "increment": 400, "wait_cycles": 1},
            {"name": "第二扩展阶段（大规模）", "target_servers": 1500, "increment": 700, "wait_cycles": 1},
            {"name": "第三扩展阶段（超大规模）", "target_servers": 3000, "increment": 1500, "wait_cycles": 1},
            {"name": "第四扩展阶段（极限测试）", "target_servers": 10000, "increment": "dynamic", "wait_cycles": 1}
        ]

        # 异常状态配置
        self.anomaly_config = {
            "long_term_online_ratio": 0.01,  # 1%长期在线
            "device_change_ratio": 0.001,    # 0.1%设备变化
            "restart_cycle_ratio": 0.001,    # 0.1%重启循环
            "ratio_decay": 0.003             # 每2000台降低0.3%
        }

        # 运行状态
        self.simulators: List[EnterpriseSlaveSimulator] = []
        self.is_running = False
        self.current_phase = 0
        self.total_servers_created = 0
        self.test_start_time: Optional[datetime] = None
        self.phase_reports: List[Dict[str, Any]] = []

        # 确保数据库目录存在
        os.makedirs(self.db_dir, exist_ok=True)
        logger.info(f"递增式测试数据库目录: {self.db_dir}")

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，正在安全停止递增式测试...")
        asyncio.create_task(self.emergency_stop())

    async def run_incremental_stress_test(self) -> Dict[str, Any]:
        """运行递增式压力测试"""
        logger.info("=" * 120)
        logger.info("🚀 启动OmniLink递增式压力测试系统")
        logger.info("🎯 目标：验证从服务器自动注册流程的极限承载能力")
        logger.info("=" * 120)

        self.test_start_time = datetime.now()
        self.is_running = True

        try:
            # 执行各个测试阶段
            for phase_index, phase_config in enumerate(self.test_phases):
                self.current_phase = phase_index

                logger.info(f"")
                logger.info("=" * 100)
                logger.info(f"📊 {phase_config['name']}")
                logger.info(f"🎯 目标服务器数: {phase_config['target_servers']}")
                logger.info(f"📈 增量: {phase_config['increment']}")
                logger.info("=" * 100)

                # 执行阶段测试
                phase_result = await self._execute_phase(phase_config)
                self.phase_reports.append(phase_result)

                # 检查是否需要停止
                if not phase_result.get('success', False):
                    logger.warning(f"⚠️ {phase_config['name']} 未完全成功，停止后续测试")
                    break

                # 硬件资源检查
                hardware_stats = self.hardware_monitor.get_system_stats()
                resource_alerts = self.hardware_monitor.check_resource_limits(hardware_stats)

                if resource_alerts.get('should_stop', False):
                    logger.critical("🚨 硬件资源达到停止阈值，安全停止测试")
                    break

                # 阶段间休息
                if phase_index < len(self.test_phases) - 1:
                    logger.info(f"⏸️ 阶段间休息30秒...")
                    await asyncio.sleep(30)

            # 生成最终报告
            final_report = self._generate_incremental_report()

            logger.info("=" * 120)
            logger.info("🎉 递增式压力测试完成")
            logger.info("=" * 120)

            return final_report

        except Exception as e:
            logger.error(f"❌ 递增式压力测试异常: {e}")
            raise
        finally:
            await self.emergency_stop()

    async def _execute_phase(self, phase_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个测试阶段"""
        phase_start_time = datetime.now()
        target_servers = phase_config['target_servers']
        increment = phase_config['increment']
        wait_cycles = phase_config['wait_cycles']

        # 计算需要创建的新服务器数量
        current_count = len(self.simulators)

        if isinstance(increment, str) and increment == "dynamic":
            # 第五阶段：动态增量
            servers_to_create = await self._calculate_dynamic_increment(current_count, target_servers)
        else:
            # 固定增量
            servers_to_create = min(increment, target_servers - current_count)

        if servers_to_create <= 0:
            logger.info(f"✅ 当前已有 {current_count} 台服务器，无需创建新服务器")
            return {"success": True, "servers_created": 0, "phase_duration": 0}

        logger.info(f"🔧 准备创建 {servers_to_create} 台新服务器...")

        try:
            # 创建新的模拟器
            new_simulators = await self._create_phase_simulators(servers_to_create)

            # 启动新模拟器
            success_count = await self._start_phase_simulators(new_simulators)

            # 等待心跳周期稳定
            await self._wait_for_stability(wait_cycles)

            # 生成阶段报告
            phase_duration = (datetime.now() - phase_start_time).total_seconds()
            phase_report = self._generate_phase_report(phase_config, success_count, servers_to_create, phase_duration)

            return phase_report

        except Exception as e:
            logger.error(f"❌ 阶段执行失败: {e}")
            return {"success": False, "error": str(e), "servers_created": 0}

    async def _calculate_dynamic_increment(self, current_count: int, target_servers: int) -> int:
        """计算动态增量（第五阶段）"""
        remaining = target_servers - current_count

        if remaining <= 0:
            return 0

        # 动态增量：100-300台，随机选择
        base_increment = random.randint(100, 300)

        # 根据当前规模调整
        if current_count >= 5000:
            # 大规模时减少增量
            base_increment = random.randint(50, 150)
        elif current_count >= 2000:
            # 中等规模时适中增量
            base_increment = random.randint(80, 200)

        return min(base_increment, remaining)

    async def _create_phase_simulators(self, count: int) -> List[EnterpriseSlaveSimulator]:
        """创建阶段模拟器"""
        new_simulators = []

        for i in range(count):
            server_id = self.total_servers_created + i + 1

            # 根据异常状态配置选择行为模式
            behavior_mode = self._select_behavior_mode(server_id)

            simulator = EnterpriseSlaveSimulator(
                server_id=server_id,
                master_url=self.master_url,
                db_dir=self.db_dir,
                behavior_mode=behavior_mode
            )

            new_simulators.append(simulator)
            self.simulators.append(simulator)

        self.total_servers_created += count
        logger.info(f"✅ 已创建 {count} 个新模拟器，总计 {len(self.simulators)} 个")

        return new_simulators

    def _select_behavior_mode(self, server_id: int) -> str:
        """根据异常状态配置选择行为模式"""
        # 计算当前异常比例（每2000台降低0.3%）
        scale_factor = max(0, 1 - (self.total_servers_created // 2000) * self.anomaly_config['ratio_decay'])

        long_term_ratio = self.anomaly_config['long_term_online_ratio'] * scale_factor
        device_change_ratio = self.anomaly_config['device_change_ratio'] * scale_factor
        restart_ratio = self.anomaly_config['restart_cycle_ratio'] * scale_factor

        rand = random.random()

        if rand < restart_ratio:
            return "restart"
        elif rand < restart_ratio + device_change_ratio:
            return "dynamic_devices"
        elif rand < restart_ratio + device_change_ratio + long_term_ratio:
            return "stable_long_term"
        else:
            return "stable"

    async def _start_phase_simulators(self, simulators: List[EnterpriseSlaveSimulator]) -> int:
        """启动阶段模拟器"""
        logger.info(f"🚀 启动 {len(simulators)} 台新服务器...")

        # 检查是否为快速部署阶段
        is_rapid_deployment = (self.current_phase == 0 and
                              len(simulators) >= 300 and
                              hasattr(self, 'test_phases') and
                              self.test_phases[0].get('rapid_deployment', False))

        if is_rapid_deployment:
            return await self._rapid_deployment_startup(simulators)
        else:
            return await self._standard_deployment_startup(simulators)

    async def _rapid_deployment_startup(self, simulators: List[EnterpriseSlaveSimulator]) -> int:
        """快速部署启动策略 - 30秒内部署400台服务器"""
        total_servers = len(simulators)
        logger.info(f"⚡ 快速部署模式: 30秒内启动 {total_servers} 台服务器")

        # 快速部署配置
        batch_size = random.randint(20, 25)  # 每批20-25台
        inter_batch_delay = random.uniform(1.5, 2.0)  # 批次间间隔1.5-2秒
        total_batches = (total_servers + batch_size - 1) // batch_size

        logger.info(f"📋 部署策略: {total_batches} 批次, 每批 ~{batch_size} 台, 间隔 ~{inter_batch_delay:.1f}s")

        start_time = time.time()
        success_count = 0

        for batch_index in range(total_batches):
            batch_start_idx = batch_index * batch_size
            batch_end_idx = min(batch_start_idx + batch_size, total_servers)
            batch = simulators[batch_start_idx:batch_end_idx]

            batch_start_time = time.time()

            logger.info(f"⚡ 批次 {batch_index + 1}/{total_batches}: 启动第 {batch_start_idx + 1}-{batch_end_idx} 台服务器...")

            # 并发启动当前批次
            tasks = [simulator.start() for simulator in batch]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计成功数量
            batch_success = sum(1 for result in results if result is True)
            success_count += batch_success

            batch_duration = time.time() - batch_start_time
            elapsed_time = time.time() - start_time

            logger.info(f"✅ 批次 {batch_index + 1}: {batch_success}/{len(batch)} 成功 "
                       f"(耗时: {batch_duration:.1f}s, 总耗时: {elapsed_time:.1f}s)")

            # 批次间间隔（最后一批不需要等待）
            if batch_index < total_batches - 1:
                # 动态调整间隔以确保在30秒内完成
                remaining_batches = total_batches - batch_index - 1
                remaining_time = 30 - elapsed_time

                if remaining_time > 0 and remaining_batches > 0:
                    adjusted_delay = min(inter_batch_delay, remaining_time / remaining_batches * 0.8)
                    await asyncio.sleep(max(0.5, adjusted_delay))  # 最小0.5秒间隔

        total_duration = time.time() - start_time
        logger.info(f"⚡ 快速部署完成: {success_count}/{total_servers} 台服务器成功启动 "
                   f"(总耗时: {total_duration:.1f}s)")

        if total_duration <= 30:
            logger.info(f"🎯 快速部署目标达成: 在 {total_duration:.1f} 秒内完成部署")
        else:
            logger.warning(f"⚠️ 快速部署超时: 耗时 {total_duration:.1f} 秒 (目标: 30秒)")

        return success_count

    async def _standard_deployment_startup(self, simulators: List[EnterpriseSlaveSimulator]) -> int:
        """标准部署启动策略"""
        # 分批启动，避免同时发起过多连接
        batch_size = 20
        success_count = 0

        for i in range(0, len(simulators), batch_size):
            batch = simulators[i:i + batch_size]
            batch_start = i + 1
            batch_end = min(i + batch_size, len(simulators))

            logger.info(f"🔄 启动第 {batch_start}-{batch_end} 台服务器...")

            # 并发启动当前批次
            tasks = [simulator.start() for simulator in batch]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计成功数量
            batch_success = sum(1 for result in results if result is True)
            success_count += batch_success

            logger.info(f"✅ 第 {batch_start}-{batch_end} 台: {batch_success}/{len(batch)} 成功")

            # 批次间间隔（第五阶段使用随机间隔）
            if i + batch_size < len(simulators):
                if self.current_phase >= 3:  # 后期阶段
                    interval = random.uniform(1, 5)
                else:
                    interval = 2
                await asyncio.sleep(interval)

        logger.info(f"🎉 标准部署完成: {success_count}/{len(simulators)} 台服务器成功启动")
        return success_count

    async def _wait_for_stability(self, wait_cycles: int):
        """等待心跳周期稳定"""
        # 检查是否为快速部署阶段
        is_rapid_deployment = (self.current_phase == 0 and
                              hasattr(self, 'test_phases') and
                              self.test_phases[0].get('rapid_deployment', False))

        if is_rapid_deployment:
            # 快速部署阶段：考虑主服务器15秒心跳间隔，等待2个周期确保同步
            cycle_duration = 15  # 主服务器心跳间隔15秒
            actual_wait_cycles = 2  # 等待2个心跳周期
            logger.info(f"⚡ 快速部署稳定性检查: 等待 {actual_wait_cycles} 个心跳周期（每周期 {cycle_duration} 秒）")
        else:
            # 标准等待：使用从服务器心跳周期
            cycle_duration = 30  # 从服务器心跳周期约30秒
            actual_wait_cycles = wait_cycles
            logger.info(f"⏱️ 标准稳定性等待: {actual_wait_cycles} 个心跳周期（每周期 {cycle_duration} 秒）")

        total_wait_time = actual_wait_cycles * cycle_duration
        logger.info(f"📊 预计等待时间: {total_wait_time} 秒")

        for cycle in range(actual_wait_cycles):
            cycle_start_time = time.time()
            logger.info(f"💓 心跳周期 {cycle + 1}/{actual_wait_cycles}")

            # 监控期间检查硬件资源
            for second in range(cycle_duration):
                if second % 5 == 0:  # 每5秒检查一次（快速部署时更频繁）
                    hardware_stats = self.hardware_monitor.get_system_stats()
                    resource_alerts = self.hardware_monitor.check_resource_limits(hardware_stats)

                    if resource_alerts.get('warnings'):
                        for warning in resource_alerts['warnings']:
                            logger.warning(f"⚠️ {warning}")

                    if resource_alerts.get('should_stop', False):
                        logger.critical("🚨 硬件资源达到停止阈值")
                        raise Exception("硬件资源限制")

                    # 快速部署阶段显示更详细的进度
                    if is_rapid_deployment and second % 10 == 0:
                        elapsed = time.time() - cycle_start_time
                        logger.info(f"   ├─ 周期 {cycle + 1} 进度: {elapsed:.1f}/{cycle_duration}s "
                                   f"(CPU: {hardware_stats.get('cpu_percent', 0):.1f}%)")

                await asyncio.sleep(1)

            cycle_duration_actual = time.time() - cycle_start_time
            logger.info(f"   └─ 周期 {cycle + 1} 完成 (实际耗时: {cycle_duration_actual:.1f}s)")

        logger.info("✅ 稳定性等待完成")

    def _generate_phase_report(self, phase_config: Dict[str, Any], success_count: int,
                              servers_created: int, phase_duration: float) -> Dict[str, Any]:
        """生成阶段报告"""
        # 聚合当前所有模拟器的统计
        total_register_attempts = sum(s.stats['register_attempts'] for s in self.simulators)
        total_register_success = sum(s.stats['register_success'] for s in self.simulators)
        total_heartbeats = sum(s.stats['heartbeat_sent'] for s in self.simulators)
        total_heartbeat_success = sum(s.stats['heartbeat_success'] for s in self.simulators)
        total_device_sync_attempts = sum(s.stats['device_sync_attempts'] for s in self.simulators)
        total_device_sync_success = sum(s.stats['device_sync_success'] for s in self.simulators)
        total_devices = sum(len(s.devices) for s in self.simulators)
        total_timeouts = sum(s.stats['timeout_count'] for s in self.simulators)

        # 计算成功率
        register_success_rate = (total_register_success / max(total_register_attempts, 1)) * 100
        heartbeat_success_rate = (total_heartbeat_success / max(total_heartbeats, 1)) * 100
        device_sync_success_rate = (total_device_sync_success / max(total_device_sync_attempts, 1)) * 100

        # 响应时间统计
        avg_response_times = [s.stats['avg_response_time'] for s in self.simulators if s.stats['avg_response_time'] > 0]
        overall_avg_response = sum(avg_response_times) / max(len(avg_response_times), 1)

        # 硬件资源统计
        hardware_stats = self.hardware_monitor.get_system_stats()

        # 检查是否为快速部署阶段
        is_rapid_deployment = phase_config.get('rapid_deployment', False)

        # 快速部署阶段的成功标准更宽松
        if is_rapid_deployment:
            success_criteria = (success_count >= servers_created * 0.95 and  # 95%部署成功率
                              register_success_rate >= 90)  # 90%注册成功率
        else:
            success_criteria = (success_count == servers_created and
                              register_success_rate >= 95)

        phase_report = {
            "phase_name": phase_config['name'],
            "phase_duration": phase_duration,
            "servers_created": servers_created,
            "servers_success": success_count,
            "total_servers": len(self.simulators),
            "total_devices": total_devices,
            "is_rapid_deployment": is_rapid_deployment,
            "deployment_rate": success_count / max(phase_duration, 1),  # 每秒部署率
            "performance_metrics": {
                "register_success_rate": register_success_rate,
                "heartbeat_success_rate": heartbeat_success_rate,
                "device_sync_success_rate": device_sync_success_rate,
                "avg_response_time": overall_avg_response,
                "timeout_count": total_timeouts
            },
            "hardware_stats": hardware_stats,
            "success": success_criteria,
            "timestamp": datetime.now().isoformat()
        }

        # 输出阶段报告
        logger.info("=" * 100)
        logger.info(f"📋 {phase_config['name']} 完成报告")
        logger.info("=" * 100)
        logger.info(f"⏱️ 阶段耗时: {phase_duration:.1f} 秒")

        # 快速部署阶段显示特殊信息
        if is_rapid_deployment:
            deployment_rate = phase_report['deployment_rate']
            logger.info(f"⚡ 快速部署: {success_count}/{servers_created} 台服务器")
            logger.info(f"🚀 部署速率: {deployment_rate:.1f} 台/秒")
            if phase_duration <= 30:
                logger.info(f"🎯 快速部署目标: ✅ 达成 ({phase_duration:.1f}s ≤ 30s)")
            else:
                logger.info(f"🎯 快速部署目标: ⚠️ 超时 ({phase_duration:.1f}s > 30s)")
        else:
            logger.info(f"🖥️ 服务器创建: {success_count}/{servers_created} 成功")

        logger.info(f"📊 总计服务器: {len(self.simulators)} 台")
        logger.info(f"🔌 总计设备: {total_devices} 个")
        logger.info(f"📈 注册成功率: {register_success_rate:.1f}%")
        logger.info(f"💓 心跳成功率: {heartbeat_success_rate:.1f}%")
        logger.info(f"🔄 设备同步成功率: {device_sync_success_rate:.1f}%")
        logger.info(f"⚡ 平均响应时间: {overall_avg_response:.3f}s")
        logger.info(f"🖥️ CPU使用率: {hardware_stats.get('cpu_percent', 0):.1f}%")
        logger.info(f"💾 内存使用率: {hardware_stats.get('memory_percent', 0):.1f}%")

        success_status = "✅ 成功" if phase_report['success'] else "⚠️ 部分成功"
        logger.info(f"🏆 阶段结果: {success_status}")
        logger.info("=" * 100)

        return phase_report

    def _generate_incremental_report(self) -> Dict[str, Any]:
        """生成递增式测试最终报告"""
        test_duration = (datetime.now() - self.test_start_time).total_seconds() if self.test_start_time else 0

        # 聚合所有统计
        total_servers = len(self.simulators)
        running_servers = sum(1 for s in self.simulators if s.is_running)
        registered_servers = sum(1 for s in self.simulators if s.registered)
        total_devices = sum(len(s.devices) for s in self.simulators)

        # 性能统计
        total_register_attempts = sum(s.stats['register_attempts'] for s in self.simulators)
        total_register_success = sum(s.stats['register_success'] for s in self.simulators)
        total_heartbeats = sum(s.stats['heartbeat_sent'] for s in self.simulators)
        total_heartbeat_success = sum(s.stats['heartbeat_success'] for s in self.simulators)
        total_device_sync_attempts = sum(s.stats['device_sync_attempts'] for s in self.simulators)
        total_device_sync_success = sum(s.stats['device_sync_success'] for s in self.simulators)
        total_timeouts = sum(s.stats['timeout_count'] for s in self.simulators)

        # 计算成功率
        register_success_rate = (total_register_success / max(total_register_attempts, 1)) * 100
        heartbeat_success_rate = (total_heartbeat_success / max(total_heartbeats, 1)) * 100
        device_sync_success_rate = (total_device_sync_success / max(total_device_sync_attempts, 1)) * 100

        # 响应时间统计
        avg_response_times = [s.stats['avg_response_time'] for s in self.simulators if s.stats['avg_response_time'] > 0]
        overall_avg_response = sum(avg_response_times) / max(len(avg_response_times), 1)
        max_response_time = max([s.stats['max_response_time'] for s in self.simulators], default=0)

        # 最终硬件统计
        final_hardware_stats = self.hardware_monitor.get_system_stats()

        # 验证标准检查
        verification_results = {
            "register_success_rate_target": register_success_rate >= 99.0,
            "device_sync_success_rate_target": device_sync_success_rate >= 99.0,
            "heartbeat_success_rate_target": heartbeat_success_rate >= 95.0,
            "avg_response_time_target": overall_avg_response <= 0.5,
            "hardware_stability": final_hardware_stats.get('cpu_percent', 0) < 95.0
        }

        verification_passed = all(verification_results.values())

        final_report = {
            "test_type": "incremental_stress_test",
            "test_summary": {
                "test_start_time": self.test_start_time.isoformat() if self.test_start_time else None,
                "test_duration_seconds": test_duration,
                "phases_completed": len(self.phase_reports),
                "total_servers_created": self.total_servers_created,
                "total_servers": total_servers,
                "running_servers": running_servers,
                "registered_servers": registered_servers,
                "total_devices": total_devices,
                "database_directory": self.db_dir
            },
            "performance_metrics": {
                "register_attempts": total_register_attempts,
                "register_success": total_register_success,
                "register_success_rate": register_success_rate,
                "heartbeat_sent": total_heartbeats,
                "heartbeat_success": total_heartbeat_success,
                "heartbeat_success_rate": heartbeat_success_rate,
                "device_sync_attempts": total_device_sync_attempts,
                "device_sync_success": total_device_sync_success,
                "device_sync_success_rate": device_sync_success_rate,
                "timeout_count": total_timeouts,
                "avg_response_time": overall_avg_response,
                "max_response_time": max_response_time
            },
            "hardware_performance": final_hardware_stats,
            "phase_reports": self.phase_reports,
            "verification_results": verification_results,
            "verification_passed": verification_passed,
            "max_concurrent_servers": total_servers,
            "system_limits_reached": final_hardware_stats.get('cpu_percent', 0) >= 90.0
        }

        return final_report

    async def emergency_stop(self):
        """紧急停止所有模拟器"""
        if not self.is_running:
            return

        self.is_running = False
        logger.info(f"🛑 紧急停止 {len(self.simulators)} 个模拟器...")

        # 并发停止所有模拟器
        if self.simulators:
            tasks = [simulator.stop() for simulator in self.simulators]
            await asyncio.gather(*tasks, return_exceptions=True)

        logger.info("✅ 所有模拟器已安全停止")

    def cleanup_database_files(self):
        """清理数据库文件"""
        try:
            if os.path.exists(self.db_dir):
                shutil.rmtree(self.db_dir)
                logger.info(f"🗑️ 已清理测试数据库目录: {self.db_dir}")
        except Exception as e:
            logger.warning(f"⚠️ 清理数据库目录失败: {e}")

class EnterpriseStressTestController:
    """企业级压力测试控制器"""

    def __init__(self, master_url: str, db_dir: Optional[str] = None):
        self.master_url = master_url
        self.db_dir = db_dir or tempfile.mkdtemp(prefix="omnilink_enterprise_test_")
        self.simulators: List[EnterpriseSlaveSimulator] = []
        self.is_running = False
        self.test_start_time: Optional[datetime] = None

        # 确保数据库目录存在
        os.makedirs(self.db_dir, exist_ok=True)
        logger.info(f"企业级测试数据库目录: {self.db_dir}")

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，正在优雅关闭...")
        asyncio.create_task(self.stop_all())

    async def run_enterprise_test(self, test_config: Dict[str, Any]) -> Dict[str, Any]:
        """运行企业级压力测试"""
        test_name = test_config.get('name', '企业级压力测试')
        server_count = test_config.get('server_count', 50)
        duration = test_config.get('duration', 300)
        behavior_distribution = test_config.get('behavior_distribution', {"stable": 1.0})

        logger.info("=" * 100)
        logger.info(f"🚀 启动{test_name}")
        logger.info(f"📊 目标规模: {server_count} 台从服务器")
        logger.info(f"⏱️  测试时长: {duration} 秒")
        logger.info(f"🎭 行为分布: {behavior_distribution}")
        logger.info("=" * 100)

        self.test_start_time = datetime.now()
        self.is_running = True

        try:
            # 第一阶段：创建和启动模拟器
            await self._create_simulators(server_count, behavior_distribution)
            await self._start_simulators()

            # 第二阶段：运行测试并监控
            await self._run_test_monitoring(duration)

            # 第三阶段：生成测试报告
            report = self._generate_enterprise_report()

            logger.info("=" * 100)
            logger.info("🎉 企业级压力测试完成")
            logger.info("=" * 100)

            return report

        except Exception as e:
            logger.error(f"❌ 企业级压力测试异常: {e}")
            raise
        finally:
            await self.stop_all()

    async def _create_simulators(self, server_count: int, behavior_distribution: Dict[str, float]):
        """创建模拟器"""
        logger.info(f"🔧 创建 {server_count} 个从服务器模拟器...")

        # 计算每种行为模式的数量
        behavior_counts = {}
        total_assigned = 0

        for behavior, ratio in behavior_distribution.items():
            count = int(server_count * ratio)
            behavior_counts[behavior] = count
            total_assigned += count

        # 处理舍入误差
        if total_assigned < server_count:
            # 将剩余的分配给第一个行为模式
            first_behavior = list(behavior_counts.keys())[0]
            behavior_counts[first_behavior] += (server_count - total_assigned)

        logger.info(f"📋 行为模式分配: {behavior_counts}")

        # 创建模拟器
        server_id = 1
        for behavior, count in behavior_counts.items():
            for _ in range(count):
                simulator = EnterpriseSlaveSimulator(
                    server_id=server_id,
                    master_url=self.master_url,
                    db_dir=self.db_dir,
                    behavior_mode=behavior
                )
                self.simulators.append(simulator)
                server_id += 1

        logger.info(f"✅ 已创建 {len(self.simulators)} 个模拟器")

    async def _start_simulators(self):
        """启动所有模拟器"""
        logger.info(f"🚀 启动 {len(self.simulators)} 个从服务器模拟器...")

        # 分批启动，避免同时发起过多连接
        batch_size = 20
        success_count = 0

        for i in range(0, len(self.simulators), batch_size):
            batch = self.simulators[i:i + batch_size]
            batch_start = i + 1
            batch_end = min(i + batch_size, len(self.simulators))

            logger.info(f"🔄 启动第 {batch_start}-{batch_end} 台服务器...")

            # 并发启动当前批次
            tasks = [simulator.start() for simulator in batch]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计成功数量
            batch_success = sum(1 for result in results if result is True)
            success_count += batch_success

            logger.info(f"✅ 第 {batch_start}-{batch_end} 台: {batch_success}/{len(batch)} 成功")

            # 批次间间隔
            if i + batch_size < len(self.simulators):
                await asyncio.sleep(2)

        logger.info(f"🎉 启动完成: {success_count}/{len(self.simulators)} 台服务器成功启动")

        if success_count == 0:
            raise Exception("没有任何从服务器成功启动")

    async def _run_test_monitoring(self, duration: int):
        """运行测试监控"""
        logger.info(f"📊 开始 {duration} 秒的测试监控...")

        start_time = time.time()
        report_interval = min(30, duration // 10)  # 报告间隔
        next_report_time = start_time + report_interval

        while self.is_running and (time.time() - start_time) < duration:
            current_time = time.time()

            # 定期报告状态
            if current_time >= next_report_time:
                await self._report_enterprise_status()
                next_report_time = current_time + report_interval

            await asyncio.sleep(5)

        logger.info("⏱️ 测试监控完成")

    async def _report_enterprise_status(self):
        """报告企业级状态"""
        if not self.simulators:
            return

        # 基础统计
        total_servers = len(self.simulators)
        running_servers = sum(1 for s in self.simulators if s.is_running)
        registered_servers = sum(1 for s in self.simulators if s.registered)

        # 按行为模式分组统计
        behavior_stats = {}
        for simulator in self.simulators:
            mode = simulator.behavior_mode
            if mode not in behavior_stats:
                behavior_stats[mode] = {"count": 0, "running": 0, "registered": 0, "devices": 0}
            behavior_stats[mode]["count"] += 1
            if simulator.is_running:
                behavior_stats[mode]["running"] += 1
            if simulator.registered:
                behavior_stats[mode]["registered"] += 1
            behavior_stats[mode]["devices"] += len(simulator.devices)

        # 设备统计
        total_devices = sum(len(s.devices) for s in self.simulators)
        total_local_db_devices = sum(s.local_db.get_device_count() for s in self.simulators)

        # 聚合性能统计
        total_register_attempts = sum(s.stats['register_attempts'] for s in self.simulators)
        total_register_success = sum(s.stats['register_success'] for s in self.simulators)
        total_heartbeats = sum(s.stats['heartbeat_sent'] for s in self.simulators)
        total_heartbeat_success = sum(s.stats['heartbeat_success'] for s in self.simulators)
        total_device_sync_attempts = sum(s.stats['device_sync_attempts'] for s in self.simulators)
        total_device_sync_success = sum(s.stats['device_sync_success'] for s in self.simulators)
        total_device_changes = sum(s.stats['device_changes'] for s in self.simulators)
        total_timeouts = sum(s.stats['timeout_count'] for s in self.simulators)

        # 计算成功率
        register_success_rate = (total_register_success / max(total_register_attempts, 1)) * 100
        heartbeat_success_rate = (total_heartbeat_success / max(total_heartbeats, 1)) * 100
        device_sync_success_rate = (total_device_sync_success / max(total_device_sync_attempts, 1)) * 100
        timeout_rate = (total_timeouts / max(total_heartbeats + total_device_sync_attempts, 1)) * 100

        # 响应时间统计
        avg_response_times = [s.stats['avg_response_time'] for s in self.simulators if s.stats['avg_response_time'] > 0]
        overall_avg_response = sum(avg_response_times) / max(len(avg_response_times), 1)
        max_response_time = max([s.stats['max_response_time'] for s in self.simulators], default=0)

        # 详细状态报告
        logger.info("=" * 120)
        logger.info("📊 OmniLink企业级全链路压力测试实时状态报告")
        logger.info("=" * 120)
        logger.info(f"🖥️  从服务器状态:")
        logger.info(f"   ├─ 总数量: {total_servers}")
        logger.info(f"   ├─ 运行中: {running_servers}")
        logger.info(f"   ├─ 已注册: {registered_servers}")
        logger.info(f"   └─ 离线数: {total_servers - running_servers}")

        logger.info(f"")
        logger.info(f"🎭 行为模式分布:")
        for mode, stats in behavior_stats.items():
            logger.info(f"   ├─ {mode}: {stats['registered']}/{stats['count']} 台已注册, {stats['devices']} 个设备")

        logger.info(f"")
        logger.info(f"🔌 设备管理状态:")
        logger.info(f"   ├─ 内存中设备总数: {total_devices}")
        logger.info(f"   ├─ 本地数据库设备数: {total_local_db_devices}")
        logger.info(f"   ├─ 设备动态变化次数: {total_device_changes}")
        logger.info(f"   └─ 平均每服务器设备数: {total_devices/max(total_servers, 1):.1f}")

        logger.info(f"")
        logger.info(f"📝 注册性能:")
        logger.info(f"   ├─ 注册尝试: {total_register_attempts}")
        logger.info(f"   ├─ 注册成功: {total_register_success}")
        logger.info(f"   └─ 注册成功率: {register_success_rate:.1f}%")

        logger.info(f"")
        logger.info(f"💓 心跳性能:")
        logger.info(f"   ├─ 心跳发送: {total_heartbeats}")
        logger.info(f"   ├─ 心跳成功: {total_heartbeat_success}")
        logger.info(f"   └─ 心跳成功率: {heartbeat_success_rate:.1f}%")

        logger.info(f"")
        logger.info(f"🔄 设备同步性能:")
        logger.info(f"   ├─ 同步尝试: {total_device_sync_attempts}")
        logger.info(f"   ├─ 同步成功: {total_device_sync_success}")
        logger.info(f"   └─ 同步成功率: {device_sync_success_rate:.1f}%")

        logger.info(f"")
        logger.info(f"⚡ 响应时间统计:")
        logger.info(f"   ├─ 平均响应时间: {overall_avg_response:.3f}s")
        logger.info(f"   ├─ 最大响应时间: {max_response_time:.3f}s")
        logger.info(f"   ├─ 超时次数: {total_timeouts}")
        logger.info(f"   └─ 超时率: {timeout_rate:.1f}%")
        logger.info("=" * 120)

    def _generate_enterprise_report(self) -> Dict[str, Any]:
        """生成企业级测试报告"""
        if not self.simulators:
            return {"error": "没有模拟器数据"}

        test_duration = (datetime.now() - self.test_start_time).total_seconds() if self.test_start_time else 0

        # 基础统计
        total_servers = len(self.simulators)
        running_servers = sum(1 for s in self.simulators if s.is_running)
        registered_servers = sum(1 for s in self.simulators if s.registered)
        total_devices = sum(len(s.devices) for s in self.simulators)
        total_local_db_devices = sum(s.local_db.get_device_count() for s in self.simulators)

        # 聚合性能统计
        total_register_attempts = sum(s.stats['register_attempts'] for s in self.simulators)
        total_register_success = sum(s.stats['register_success'] for s in self.simulators)
        total_heartbeats = sum(s.stats['heartbeat_sent'] for s in self.simulators)
        total_heartbeat_success = sum(s.stats['heartbeat_success'] for s in self.simulators)
        total_device_sync_attempts = sum(s.stats['device_sync_attempts'] for s in self.simulators)
        total_device_sync_success = sum(s.stats['device_sync_success'] for s in self.simulators)
        total_device_changes = sum(s.stats['device_changes'] for s in self.simulators)
        total_timeouts = sum(s.stats['timeout_count'] for s in self.simulators)

        # 计算成功率
        register_success_rate = (total_register_success / max(total_register_attempts, 1)) * 100
        heartbeat_success_rate = (total_heartbeat_success / max(total_heartbeats, 1)) * 100
        device_sync_success_rate = (total_device_sync_success / max(total_device_sync_attempts, 1)) * 100
        timeout_rate = (total_timeouts / max(total_heartbeats + total_device_sync_attempts, 1)) * 100

        # 响应时间统计
        avg_response_times = [s.stats['avg_response_time'] for s in self.simulators if s.stats['avg_response_time'] > 0]
        overall_avg_response = sum(avg_response_times) / max(len(avg_response_times), 1)
        max_response_time = max([s.stats['max_response_time'] for s in self.simulators], default=0)

        # 按行为模式分组统计
        behavior_stats = {}
        for simulator in self.simulators:
            mode = simulator.behavior_mode
            if mode not in behavior_stats:
                behavior_stats[mode] = {
                    "count": 0, "running": 0, "registered": 0, "devices": 0,
                    "register_success_rate": 0, "heartbeat_success_rate": 0,
                    "device_sync_success_rate": 0, "avg_response_time": 0
                }
            behavior_stats[mode]["count"] += 1
            if simulator.is_running:
                behavior_stats[mode]["running"] += 1
            if simulator.registered:
                behavior_stats[mode]["registered"] += 1
            behavior_stats[mode]["devices"] += len(simulator.devices)

        # 计算每种行为模式的性能指标
        for mode in behavior_stats:
            mode_simulators = [s for s in self.simulators if s.behavior_mode == mode]
            if mode_simulators:
                mode_register_attempts = sum(s.stats['register_attempts'] for s in mode_simulators)
                mode_register_success = sum(s.stats['register_success'] for s in mode_simulators)
                mode_heartbeats = sum(s.stats['heartbeat_sent'] for s in mode_simulators)
                mode_heartbeat_success = sum(s.stats['heartbeat_success'] for s in mode_simulators)
                mode_device_sync_attempts = sum(s.stats['device_sync_attempts'] for s in mode_simulators)
                mode_device_sync_success = sum(s.stats['device_sync_success'] for s in mode_simulators)
                mode_avg_times = [s.stats['avg_response_time'] for s in mode_simulators if s.stats['avg_response_time'] > 0]

                behavior_stats[mode]["register_success_rate"] = (mode_register_success / max(mode_register_attempts, 1)) * 100
                behavior_stats[mode]["heartbeat_success_rate"] = (mode_heartbeat_success / max(mode_heartbeats, 1)) * 100
                behavior_stats[mode]["device_sync_success_rate"] = (mode_device_sync_success / max(mode_device_sync_attempts, 1)) * 100
                behavior_stats[mode]["avg_response_time"] = sum(mode_avg_times) / max(len(mode_avg_times), 1)

        # 验证标准检查
        verification_results = {
            "register_success_rate_target": register_success_rate >= 99.0,
            "device_sync_success_rate_target": device_sync_success_rate >= 99.0,
            "heartbeat_success_rate_target": heartbeat_success_rate >= 95.0,
            "avg_response_time_target": overall_avg_response <= 0.5,
            "data_consistency_check": total_devices == total_local_db_devices
        }

        verification_passed = all(verification_results.values())

        report = {
            "test_summary": {
                "test_start_time": self.test_start_time.isoformat() if self.test_start_time else None,
                "test_duration_seconds": test_duration,
                "total_servers": total_servers,
                "running_servers": running_servers,
                "registered_servers": registered_servers,
                "total_devices": total_devices,
                "total_local_db_devices": total_local_db_devices,
                "database_directory": self.db_dir
            },
            "performance_metrics": {
                "register_attempts": total_register_attempts,
                "register_success": total_register_success,
                "register_success_rate": register_success_rate,
                "heartbeat_sent": total_heartbeats,
                "heartbeat_success": total_heartbeat_success,
                "heartbeat_success_rate": heartbeat_success_rate,
                "device_sync_attempts": total_device_sync_attempts,
                "device_sync_success": total_device_sync_success,
                "device_sync_success_rate": device_sync_success_rate,
                "device_changes": total_device_changes,
                "timeout_count": total_timeouts,
                "timeout_rate": timeout_rate,
                "avg_response_time": overall_avg_response,
                "max_response_time": max_response_time
            },
            "behavior_mode_analysis": behavior_stats,
            "verification_results": verification_results,
            "verification_passed": verification_passed,
            "detailed_server_status": [s.get_status_summary() for s in self.simulators[:10]]  # 前10台的详细状态
        }

        return report

    async def stop_all(self):
        """停止所有模拟器"""
        if not self.is_running:
            return

        self.is_running = False
        logger.info(f"🛑 正在停止 {len(self.simulators)} 个从服务器模拟器...")

        # 并发停止所有模拟器
        if self.simulators:
            tasks = [simulator.stop() for simulator in self.simulators]
            await asyncio.gather(*tasks, return_exceptions=True)

        logger.info("✅ 所有模拟器已停止")

    def cleanup_database_files(self):
        """清理数据库文件"""
        try:
            if os.path.exists(self.db_dir):
                shutil.rmtree(self.db_dir)
                logger.info(f"🗑️ 已清理测试数据库目录: {self.db_dir}")
        except Exception as e:
            logger.warning(f"⚠️ 清理数据库目录失败: {e}")

# 预定义测试配置
ENTERPRISE_TEST_CONFIGS = {
    "phase1_basic": {
        "name": "第一阶段：基础功能验证",
        "server_count": 50,
        "duration": 300,  # 5分钟
        "behavior_distribution": {"stable": 1.0}
    },
    "phase2_medium": {
        "name": "第二阶段：中等规模测试",
        "server_count": 150,
        "duration": 600,  # 10分钟
        "behavior_distribution": {"stable": 0.7, "dynamic_devices": 0.3}
    },
    "phase3_large": {
        "name": "第三阶段：大规模压力测试",
        "server_count": 350,
        "duration": 900,  # 15分钟
        "behavior_distribution": {"stable": 0.5, "dynamic_devices": 0.3, "restart": 0.2}
    },
    "phase4_enterprise": {
        "name": "第四阶段：企业级极限测试",
        "server_count": 1000,
        "duration": 1800,  # 30分钟
        "behavior_distribution": {"stable": 0.4, "dynamic_devices": 0.4, "restart": 0.2}
    },
    "phase5_ultimate": {
        "name": "第五阶段：终极规模测试",
        "server_count": 10000,
        "duration": 3600,  # 60分钟
        "behavior_distribution": {"stable": 0.6, "dynamic_devices": 0.3, "restart": 0.1}
    },
    "incremental_test": {
        "name": "递增式压力测试",
        "description": "验证从服务器自动注册流程的极限承载能力",
        "max_servers": 10000,
        "hardware_monitoring": True
    }
}

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OmniLink企业级全链路压力测试系统 v3.0")
    parser.add_argument("--master-url", default="http://localhost:8000", help="主服务器URL")
    parser.add_argument("--phase", choices=list(ENTERPRISE_TEST_CONFIGS.keys()),
                       default="phase1_basic", help="测试阶段")
    parser.add_argument("--incremental", action="store_true", help="运行递增式压力测试")
    parser.add_argument("--custom-servers", type=int, help="自定义服务器数量")
    parser.add_argument("--custom-duration", type=int, help="自定义测试时长（秒）")
    parser.add_argument("--db-dir", help="数据库目录路径")
    parser.add_argument("--cleanup", action="store_true", help="测试完成后清理数据库文件")
    parser.add_argument("--report-file", help="测试报告输出文件")
    parser.add_argument("--monitor-interval", type=int, default=10, help="硬件监控间隔（秒）")

    args = parser.parse_args()

    try:
        if args.incremental:
            # 运行递增式压力测试
            logger.info("🚀 启动递增式压力测试模式")

            controller = IncrementalStressTestController(
                master_url=args.master_url,
                db_dir=args.db_dir
            )

            # 运行递增式测试
            report = await controller.run_incremental_stress_test()

            # 输出递增式测试报告
            logger.info("=" * 120)
            logger.info("📋 OmniLink递增式压力测试最终报告")
            logger.info("=" * 120)

            summary = report["test_summary"]
            metrics = report["performance_metrics"]
            hardware = report["hardware_performance"]
            verification = report["verification_results"]

            logger.info(f"🎯 测试类型: 递增式压力测试")
            logger.info(f"⏱️ 测试时长: {summary['test_duration_seconds']:.1f} 秒")
            logger.info(f"📊 完成阶段: {summary['phases_completed']} 个")
            logger.info(f"🖥️ 最大并发服务器: {summary['total_servers']} 台")
            logger.info(f"🔌 USB设备总数: {summary['total_devices']} 个")
            logger.info(f"")
            logger.info(f"📈 性能指标:")
            logger.info(f"   ├─ 注册成功率: {metrics['register_success_rate']:.1f}% (目标: ≥99%)")
            logger.info(f"   ├─ 设备同步成功率: {metrics['device_sync_success_rate']:.1f}% (目标: ≥99%)")
            logger.info(f"   ├─ 心跳成功率: {metrics['heartbeat_success_rate']:.1f}% (目标: ≥95%)")
            logger.info(f"   ├─ 平均响应时间: {metrics['avg_response_time']:.3f}s (目标: ≤0.5s)")
            logger.info(f"   └─ 超时次数: {metrics['timeout_count']}")
            logger.info(f"")
            logger.info(f"🖥️ 硬件性能:")
            logger.info(f"   ├─ 最终CPU使用率: {hardware.get('cpu_percent', 0):.1f}%")
            logger.info(f"   ├─ 最终内存使用率: {hardware.get('memory_percent', 0):.1f}%")
            logger.info(f"   ├─ CPU核心数: {hardware.get('cpu_count', 0)}")
            logger.info(f"   └─ 系统平台: {hardware.get('platform', 'Unknown')}")
            logger.info(f"")
            logger.info(f"✅ 验证结果:")
            for check, passed in verification.items():
                status = "✅ 通过" if passed else "❌ 失败"
                logger.info(f"   ├─ {check}: {status}")

            overall_result = "🎉 测试通过" if report["verification_passed"] else "⚠️ 测试未完全通过"
            limits_reached = "🚨 是" if report["system_limits_reached"] else "✅ 否"
            logger.info(f"")
            logger.info(f"🏆 总体结果: {overall_result}")
            logger.info(f"⚡ 系统极限: {limits_reached}")
            logger.info("=" * 120)

        else:
            # 运行标准企业级测试
            logger.info("🚀 启动标准企业级测试模式")

            # 获取测试配置
            test_config = ENTERPRISE_TEST_CONFIGS[args.phase].copy()

            # 应用自定义参数
            if args.custom_servers:
                test_config["server_count"] = args.custom_servers
            if args.custom_duration:
                test_config["duration"] = args.custom_duration

            # 创建测试控制器
            controller = EnterpriseStressTestController(
                master_url=args.master_url,
                db_dir=args.db_dir
            )

            # 运行测试
            report = await controller.run_enterprise_test(test_config)

            # 输出标准测试报告
            logger.info("=" * 120)
            logger.info("📋 OmniLink企业级全链路压力测试最终报告")
            logger.info("=" * 120)

            summary = report["test_summary"]
            metrics = report["performance_metrics"]
            verification = report["verification_results"]

            logger.info(f"🎯 测试目标: {test_config['name']}")
            logger.info(f"⏱️ 测试时长: {summary['test_duration_seconds']:.1f} 秒")
            logger.info(f"🖥️ 从服务器: {summary['registered_servers']}/{summary['total_servers']} 台成功注册")
            logger.info(f"🔌 USB设备: {summary['total_devices']} 个（本地数据库: {summary['total_local_db_devices']} 个）")
            logger.info(f"")
            logger.info(f"📊 性能指标:")
            logger.info(f"   ├─ 注册成功率: {metrics['register_success_rate']:.1f}% (目标: ≥99%)")
            logger.info(f"   ├─ 设备同步成功率: {metrics['device_sync_success_rate']:.1f}% (目标: ≥99%)")
            logger.info(f"   ├─ 心跳成功率: {metrics['heartbeat_success_rate']:.1f}% (目标: ≥95%)")
            logger.info(f"   ├─ 平均响应时间: {metrics['avg_response_time']:.3f}s (目标: ≤0.5s)")
            logger.info(f"   └─ 超时率: {metrics['timeout_rate']:.1f}%")
            logger.info(f"")
            logger.info(f"✅ 验证结果:")
            for check, passed in verification.items():
                status = "✅ 通过" if passed else "❌ 失败"
                logger.info(f"   ├─ {check}: {status}")

            overall_result = "🎉 测试通过" if report["verification_passed"] else "⚠️ 测试未完全通过"
            logger.info(f"")
            logger.info(f"🏆 总体结果: {overall_result}")
            logger.info("=" * 120)

        # 保存测试报告
        if args.report_file:
            with open(args.report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"📄 测试报告已保存: {args.report_file}")

        return 0 if report["verification_passed"] else 1

    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断测试")
        return 130
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        # 清理资源
        if args.incremental:
            await controller.emergency_stop()
        else:
            await controller.stop_all()

        if args.cleanup:
            controller.cleanup_database_files()

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
