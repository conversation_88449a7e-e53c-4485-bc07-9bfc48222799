"""
OmniLink 二次权限定义服务
版本: 2.0
创建日期: 2025-01-11
描述: 实现独立的权限分配流程，遵循层级管理原则，包含完整审计日志记录
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, update
from fastapi import HTTPException, status

from models import User, Role
from strict_permissions import StrictPermissionChecker, set_session_variables

logger = logging.getLogger(__name__)

class RoleAssignmentService:
    """角色分配服务"""
    
    @staticmethod
    async def assign_role_with_validation(
        db: AsyncSession,
        operator: User,
        target_user_id: int,
        new_role_name: str,
        reason: str = "",
        request_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分配角色并进行完整验证
        
        Args:
            db: 数据库会话
            operator: 操作者用户对象
            target_user_id: 目标用户ID
            new_role_name: 新角色名称
            reason: 变更原因
            request_info: 请求信息（IP、User-Agent等）
            
        Returns:
            分配结果字典
        """
        result = {
            "success": False,
            "old_role": None,
            "new_role": None,
            "validation_errors": [],
            "warnings": [],
            "audit_id": None
        }
        
        try:
            # 设置数据库会话变量
            await set_session_variables(db, operator)
            
            # 1. 获取目标用户
            target_user_result = await db.execute(
                select(User).where(User.id == target_user_id)
            )
            target_user = target_user_result.scalar_one_or_none()
            
            if not target_user:
                result["validation_errors"].append("目标用户不存在")
                return result
            
            # 2. 检查是否为自己分配角色（禁止）
            if operator.id == target_user.id:
                result["validation_errors"].append("不能为自己分配角色")
                return result
            
            # 3. 验证新角色存在性
            new_role_result = await db.execute(
                select(Role).where(Role.name == new_role_name)
            )
            new_role = new_role_result.scalar_one_or_none()
            
            if not new_role:
                result["validation_errors"].append(f"角色 '{new_role_name}' 不存在")
                return result
            
            # 4. 检查操作者权限
            if not await StrictPermissionChecker.can_assign_role(db, operator, new_role_name):
                result["validation_errors"].append(f"您没有分配 '{new_role_name}' 角色的权限")
                return result
            
            # 5. 检查是否可以管理目标用户
            if not await StrictPermissionChecker.can_manage_user(db, operator, target_user):
                result["validation_errors"].append("您没有管理该用户的权限")
                return result
            
            # 6. 检查角色变更的合理性
            old_role_name = target_user.role_name
            
            # 获取角色等级信息
            operator_level = await StrictPermissionChecker.get_user_permission_level(db, operator)
            old_role_level = await StrictPermissionChecker.get_user_permission_level(db, target_user)
            new_role_level = new_role.hierarchy_level
            
            # 验证层级管理原则
            validation_errors = await RoleAssignmentService._validate_role_assignment_hierarchy(
                operator_level, old_role_level, new_role_level, operator.role_name
            )
            
            if validation_errors:
                result["validation_errors"].extend(validation_errors)
                return result
            
            # 7. 执行角色变更
            old_role_name = target_user.role_name
            target_user.role_name = new_role_name
            target_user.updated_at = datetime.utcnow()
            
            # 8. 记录审计日志
            audit_data = {
                "operator_info": {
                    "id": operator.id,
                    "username": operator.username,
                    "role": operator.role_name,
                    "permission_level": operator_level
                },
                "target_user_info": {
                    "id": target_user.id,
                    "username": target_user.username,
                    "organization_id": target_user.organization_id
                },
                "role_change": {
                    "old_role": old_role_name,
                    "new_role": new_role_name,
                    "old_level": old_role_level,
                    "new_level": new_role_level
                },
                "request_info": request_info or {},
                "reason": reason
            }
            
            # 9. 提交事务
            await db.commit()
            
            result.update({
                "success": True,
                "old_role": old_role_name,
                "new_role": new_role_name,
                "message": f"用户 '{target_user.username}' 的角色已从 '{old_role_name}' 变更为 '{new_role_name}'"
            })
            
            logger.info(f"角色分配成功: {target_user.username} {old_role_name} -> {new_role_name} (操作者: {operator.username})")
            
        except Exception as e:
            await db.rollback()
            logger.error(f"角色分配失败: {e}")
            result["validation_errors"].append(f"分配过程中发生错误: {str(e)}")
        
        return result
    
    @staticmethod
    async def _validate_role_assignment_hierarchy(
        operator_level: int,
        old_role_level: int,
        new_role_level: int,
        operator_role: str
    ) -> List[str]:
        """验证角色分配的层级关系"""
        errors = []
        
        try:
            # 全域管理员可以分配任何角色
            if operator_level == 1:  # 全域管理员
                return errors
            
            # 超级管理员可以将新用户提升为管理员或普通用户
            elif operator_level == 2:  # 超级管理员
                if new_role_level < 3:  # 不能分配全域管理员或超级管理员
                    errors.append("超级管理员不能分配全域管理员或超级管理员角色")
                elif new_role_level > 4:  # 不能分配新用户角色（应该是升级）
                    errors.append("不应该将用户降级为新用户角色")
            
            # 管理员只能将新用户提升为普通用户
            elif operator_level == 3:  # 管理员
                if new_role_level < 4:  # 不能分配管理员及以上角色
                    errors.append("管理员只能分配普通用户角色")
                elif new_role_level > 4:  # 不能分配新用户角色
                    errors.append("不应该将用户降级为新用户角色")
            
            # 普通用户和新用户不能分配角色
            else:
                errors.append("您没有分配角色的权限")
            
            # 检查是否为合理的升级路径
            if new_role_level >= old_role_level and old_role_level != 5:  # 除了新用户外，不应该降级或平级
                errors.append("角色变更应该是升级，不应该降级或保持不变")
            
        except Exception as e:
            logger.error(f"验证角色分配层级关系失败: {e}")
            errors.append("权限验证过程中发生错误")
        
        return errors
    
    @staticmethod
    async def get_assignable_roles(
        db: AsyncSession,
        operator: User
    ) -> List[Dict[str, Any]]:
        """获取操作者可分配的角色列表"""
        try:
            operator_level = await StrictPermissionChecker.get_user_permission_level(db, operator)
            
            # 获取操作者角色信息
            operator_role_result = await db.execute(
                select(Role).where(Role.name == operator.role_name)
            )
            operator_role = operator_role_result.scalar_one_or_none()
            
            if not operator_role or not operator_role.can_assign_roles:
                return []
            
            # 获取可分配的角色
            assignable_roles_result = await db.execute(
                select(Role).where(
                    Role.hierarchy_level <= operator_role.max_manageable_level,
                    Role.hierarchy_level > operator_level  # 不能分配等于或高于自己的角色
                ).order_by(Role.hierarchy_level)
            )
            
            roles = []
            for role in assignable_roles_result.scalars():
                roles.append({
                    "id": role.id,
                    "name": role.name,
                    "description": role.description,
                    "hierarchy_level": role.hierarchy_level,
                    "permissions": role.permissions
                })
            
            return roles
            
        except Exception as e:
            logger.error(f"获取可分配角色失败: {e}")
            return []
    
    @staticmethod
    async def get_role_assignment_audit(
        db: AsyncSession,
        target_user_id: Optional[int] = None,
        operator_id: Optional[int] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """获取角色分配审计记录"""
        try:
            query = """
                SELECT 
                    pca.*,
                    u1.username as target_username,
                    u2.username as operator_username
                FROM permission_change_audit pca
                LEFT JOIN users u1 ON pca.target_user_id = u1.id
                LEFT JOIN users u2 ON pca.operator_user_id = u2.id
                WHERE pca.operation_type = 'ROLE_ASSIGN'
            """
            params = {}
            
            if target_user_id:
                query += " AND pca.target_user_id = :target_user_id"
                params["target_user_id"] = target_user_id
            
            if operator_id:
                query += " AND pca.operator_user_id = :operator_id"
                params["operator_id"] = operator_id
            
            query += " ORDER BY pca.created_at DESC LIMIT :limit"
            params["limit"] = limit
            
            result = await db.execute(text(query), params)
            return [dict(row) for row in result.fetchall()]
            
        except Exception as e:
            logger.error(f"获取角色分配审计记录失败: {e}")
            return []
    
    @staticmethod
    async def batch_assign_roles(
        db: AsyncSession,
        operator: User,
        assignments: List[Dict[str, Any]],
        request_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """批量分配角色"""
        result = {
            "success": False,
            "successful_assignments": [],
            "failed_assignments": [],
            "total_count": len(assignments),
            "success_count": 0,
            "error_count": 0
        }
        
        try:
            for assignment in assignments:
                user_id = assignment.get("user_id")
                role_name = assignment.get("role_name")
                reason = assignment.get("reason", "批量分配")
                
                if not user_id or not role_name:
                    result["failed_assignments"].append({
                        "user_id": user_id,
                        "role_name": role_name,
                        "error": "缺少必要参数"
                    })
                    continue
                
                # 执行单个角色分配
                assignment_result = await RoleAssignmentService.assign_role_with_validation(
                    db, operator, user_id, role_name, reason, request_info
                )
                
                if assignment_result["success"]:
                    result["successful_assignments"].append({
                        "user_id": user_id,
                        "old_role": assignment_result["old_role"],
                        "new_role": assignment_result["new_role"]
                    })
                    result["success_count"] += 1
                else:
                    result["failed_assignments"].append({
                        "user_id": user_id,
                        "role_name": role_name,
                        "errors": assignment_result["validation_errors"]
                    })
                    result["error_count"] += 1
            
            result["success"] = result["success_count"] > 0
            
        except Exception as e:
            logger.error(f"批量角色分配失败: {e}")
            result["failed_assignments"].append({
                "error": f"批量处理过程中发生错误: {str(e)}"
            })
        
        return result
