#!/usr/bin/env python3
"""
设备分组管理API路由
提供设备分组CRUD操作和虚拟设备管理功能
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, update, delete, func
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import logging

from database import get_db
from models import User, Device, DeviceGroup, DeviceGroupMember, UserDeviceGroupPermission, SlaveServer, Organization
from auth_utils import get_current_user
from dual_permission_system import DualPermissionChecker

router = APIRouter(prefix="/api/v1/device-groups", tags=["设备分组管理"])
logger = logging.getLogger(__name__)

# ==================== 数据模型 ====================

class DeviceGroupCreateRequest(BaseModel):
    """设备分组创建请求"""
    name: str = Field(..., description="分组名称")
    group_type: str = Field(..., description="分组类型: server, mixed, single, nested")
    description: Optional[str] = Field(None, description="分组描述")
    parent_group_id: Optional[int] = Field(None, description="父分组ID")
    auto_virtual: bool = Field(True, description="是否自动添加虚拟设备")

class DeviceGroupUpdateRequest(BaseModel):
    """设备分组更新请求"""
    name: Optional[str] = Field(None, description="分组名称")
    description: Optional[str] = Field(None, description="分组描述")

class DeviceGroupAddDevicesRequest(BaseModel):
    """添加设备到分组请求"""
    device_ids: List[int] = Field(..., description="设备ID列表")

class DeviceGroupResponse(BaseModel):
    """设备分组响应"""
    id: int
    name: str
    group_type: str
    description: Optional[str]
    parent_group_id: Optional[int]
    nesting_level: int
    device_count: int
    child_group_count: int
    has_virtual_devices: bool
    created_by: int
    organization_id: int
    created_at: datetime
    updated_at: datetime
    can_manage: Optional[bool] = Field(False, description="是否可以管理此分组")
    is_readonly: Optional[bool] = Field(False, description="是否为只读分组")
    can_view_devices: Optional[bool] = Field(False, description="是否可以查看设备详情")

class DeviceGroupDetailResponse(BaseModel):
    """设备分组详情响应"""
    id: int
    name: str
    group_type: str
    description: Optional[str]
    device_count: int
    has_virtual_devices: bool
    created_by: int
    organization_id: int
    created_at: datetime
    updated_at: datetime
    devices: List[Dict[str, Any]]

class DeviceGroupPermissionRequest(BaseModel):
    """设备分组权限请求"""
    user_ids: List[int] = Field(..., description="用户ID列表")
    permission_type: str = Field(..., description="权限类型: view, use, manage")
    expires_at: Optional[datetime] = Field(None, description="过期时间")

class DeviceGroupPermissionResponse(BaseModel):
    """设备分组权限响应"""
    id: int
    user_id: int
    username: str
    user_role: str
    organization_name: str
    permission_type: str
    granted_by: int
    granted_by_name: str
    granted_at: datetime
    expires_at: Optional[datetime]
    is_active: bool

# ==================== 设备分组CRUD ====================

@router.get("/", response_model=List[DeviceGroupResponse])
async def get_device_groups(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取设备分组列表 - 基于组织层级权限过滤"""
    try:
        # 查询所有活跃的设备分组
        stmt = select(DeviceGroup).where(DeviceGroup.is_active == True).order_by(DeviceGroup.created_at.desc())
        result = await db.execute(stmt)
        all_groups = result.scalars().all()

        # 基于权限过滤分组
        groups = []
        for group in all_groups:
            # 检查用户是否有权限查看此分组
            can_view = await DualPermissionChecker.can_view_device_group(db, current_user, group)
            if can_view:
                groups.append(group)

        response_data = []
        for group in groups:
            # 统计设备数量
            device_count_stmt = select(func.count(DeviceGroupMember.id)).where(
                and_(
                    DeviceGroupMember.group_id == group.id,
                    DeviceGroupMember.is_active == True
                )
            )
            device_count_result = await db.execute(device_count_stmt)
            device_count = device_count_result.scalar() or 0

            # 检查是否有虚拟设备
            virtual_device_stmt = select(func.count(Device.id)).join(
                DeviceGroupMember, Device.id == DeviceGroupMember.device_id
            ).where(
                and_(
                    DeviceGroupMember.group_id == group.id,
                    DeviceGroupMember.is_active == True,
                    Device.is_virtual == True
                )
            )
            virtual_result = await db.execute(virtual_device_stmt)
            has_virtual = (virtual_result.scalar() or 0) > 0

            # 统计子分组数量
            child_count_stmt = select(func.count(DeviceGroup.id)).where(
                and_(
                    DeviceGroup.parent_group_id == group.id,
                    DeviceGroup.is_active == True
                )
            )
            child_count_result = await db.execute(child_count_stmt)
            child_group_count = child_count_result.scalar() or 0

            # 检查用户是否可以管理此分组
            can_manage = await DualPermissionChecker.can_manage_device_group(db, current_user, group)

            # 检查用户是否可以查看设备详情
            can_view_devices = await DualPermissionChecker.can_view_device_details(db, current_user, group)

            response_data.append(DeviceGroupResponse(
                id=group.id,
                name=group.name,
                group_type=group.group_type,
                description=group.description,
                parent_group_id=group.parent_group_id,
                nesting_level=group.nesting_level,
                device_count=device_count,
                child_group_count=child_group_count,
                has_virtual_devices=has_virtual,
                created_by=group.created_by,
                organization_id=group.organization_id,
                created_at=group.created_at,
                updated_at=group.updated_at,
                can_manage=can_manage,
                is_readonly=not can_manage and group.created_by != current_user.id,
                can_view_devices=can_view_devices
            ))

        return response_data

    except Exception as e:
        logger.error(f"获取设备分组列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取设备分组列表失败: {str(e)}"
        )

@router.get("/tree", response_model=List[Dict[str, Any]])
async def get_device_groups_tree(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取设备分组树形结构"""
    try:
        # 查询所有顶级分组（没有父分组的分组）
        top_level_stmt = select(DeviceGroup).where(
            and_(
                DeviceGroup.parent_group_id.is_(None),
                DeviceGroup.is_active == True
            )
        ).order_by(DeviceGroup.created_at.desc())

        top_level_result = await db.execute(top_level_stmt)
        top_level_groups = top_level_result.scalars().all()

        async def build_group_tree(group):
            """递归构建分组树"""
            # 检查用户是否有权限查看此分组
            can_view = await DualPermissionChecker.can_view_device_group(db, current_user, group)
            if not can_view:
                return None

            # 统计设备数量
            device_count_stmt = select(func.count(DeviceGroupMember.device_id)).where(
                and_(
                    DeviceGroupMember.group_id == group.id,
                    DeviceGroupMember.is_active == True
                )
            )
            device_count_result = await db.execute(device_count_stmt)
            device_count = device_count_result.scalar() or 0

            # 检查权限
            can_manage = await DualPermissionChecker.can_manage_device_group(db, current_user, group)
            can_view_devices = await DualPermissionChecker.can_view_device_details(db, current_user, group)

            # 查询子分组
            children_stmt = select(DeviceGroup).where(
                and_(
                    DeviceGroup.parent_group_id == group.id,
                    DeviceGroup.is_active == True
                )
            ).order_by(DeviceGroup.created_at.desc())

            children_result = await db.execute(children_stmt)
            children = children_result.scalars().all()

            # 递归构建子分组
            child_nodes = []
            for child in children:
                child_node = await build_group_tree(child)
                if child_node:
                    child_nodes.append(child_node)

            return {
                "id": group.id,
                "name": group.name,
                "group_type": group.group_type,
                "description": group.description,
                "nesting_level": group.nesting_level,
                "device_count": device_count,
                "child_count": len(child_nodes),
                "can_manage": can_manage,
                "can_view_devices": can_view_devices,
                "is_readonly": not can_manage and group.created_by != current_user.id,
                "children": child_nodes
            }

        # 构建树形结构
        tree_data = []
        for group in top_level_groups:
            tree_node = await build_group_tree(group)
            if tree_node:
                tree_data.append(tree_node)

        logger.info(f"获取设备分组树形结构成功: {len(tree_data)} 个顶级分组")
        return tree_data

    except Exception as e:
        logger.error(f"获取设备分组树形结构失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取设备分组树形结构失败"
        )

@router.post("/", response_model=Dict[str, Any])
async def create_device_group(
    request: DeviceGroupCreateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建设备分组 - 支持嵌套分组和权限控制"""
    try:
        # 检查用户是否有创建嵌套分组的权限
        can_create = await DualPermissionChecker.can_create_nested_group(
            db, current_user, request.parent_group_id
        )
        if not can_create:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足或嵌套深度超过限制（最多4层）"
            )

        # 检查分组名称是否已存在
        existing_stmt = select(DeviceGroup).where(
            and_(
                DeviceGroup.name == request.name,
                DeviceGroup.is_active == True
            )
        )
        existing_result = await db.execute(existing_stmt)
        if existing_result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Group name already exists"
            )

        # 计算嵌套层级
        nesting_level = 0
        if request.parent_group_id:
            nesting_level = await DualPermissionChecker.check_nesting_depth(
                db, request.parent_group_id
            )

        # 创建设备分组
        new_group = DeviceGroup(
            name=request.name,
            group_type=request.group_type,
            description=request.description,
            parent_group_id=request.parent_group_id,
            nesting_level=nesting_level,
            created_by=current_user.id,
            organization_id=current_user.organization_id,
            is_active=True
        )

        db.add(new_group)
        await db.commit()
        await db.refresh(new_group)

        # 实时广播分组创建事件
        from core.websocket_manager import websocket_manager
        await websocket_manager.trigger_event("device_group_created", {
            "group_id": new_group.id,
            "group_name": new_group.name,
            "group_type": new_group.group_type,
            "created_by": default_user.username,
            "timestamp": datetime.now().isoformat()
        })

        # 如果启用自动虚拟设备，创建虚拟设备占位
        if request.auto_virtual:
            # 获取默认从服务器
            slave_stmt = select(SlaveServer).limit(1)
            slave_result = await db.execute(slave_stmt)
            default_slave = slave_result.scalar_one_or_none()

            if default_slave:
                virtual_device = Device(
                    device_id=f"virtual_{new_group.id}_{datetime.now().timestamp()}",
                    device_name=f"Virtual Device Placeholder - {new_group.name}",
                    device_type="virtual",
                    vendor_id="0000",
                    product_id="0000",
                    status="virtual",
                    is_virtual=True,
                    slave_server_id=default_slave.id
                )

                db.add(virtual_device)
                await db.commit()
                await db.refresh(virtual_device)

                # 将虚拟设备添加到分组
                group_member = DeviceGroupMember(
                    group_id=new_group.id,
                    device_id=virtual_device.id,
                    added_by=default_user.id
                )

                db.add(group_member)
                await db.commit()

        logger.info(f"Device group created successfully: {request.name}")
        return {
            "status": "success",
            "message": "Device group created successfully",
            "group_id": new_group.id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create device group: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create device group: {str(e)}"
        )

@router.post("/batch", response_model=Dict[str, Any])
async def create_device_groups_batch(
    request: List[DeviceGroupCreateRequest],
    db: AsyncSession = Depends(get_db)
):
    """批量创建设备分组 - 动态化实现"""
    try:
        # 获取第一个可用用户作为创建者
        user_stmt = select(User).limit(1)
        user_result = await db.execute(user_stmt)
        default_user = user_result.scalar_one_or_none()

        if not default_user:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="No available users in system"
            )

        created_groups = []

        for group_request in request:
            # 检查分组名称是否已存在
            existing_stmt = select(DeviceGroup).where(
                and_(
                    DeviceGroup.name == group_request.name,
                    DeviceGroup.is_active == True
                )
            )
            existing_result = await db.execute(existing_stmt)
            if existing_result.scalar_one_or_none():
                continue  # 跳过已存在的分组

            # 创建设备分组
            new_group = DeviceGroup(
                name=group_request.name,
                group_type=group_request.group_type,
                description=group_request.description,
                created_by=default_user.id,
                organization_id=default_user.organization_id,
                is_active=True
            )

            db.add(new_group)
            created_groups.append(new_group)

        await db.commit()

        # 刷新所有创建的分组
        for group in created_groups:
            await db.refresh(group)

        # 实时广播批量创建事件
        from core.websocket_manager import websocket_manager
        await websocket_manager.trigger_event("device_groups_batch_created", {
            "groups": [{"id": g.id, "name": g.name, "type": g.group_type} for g in created_groups],
            "created_by": default_user.username,
            "timestamp": datetime.now().isoformat()
        })

        return {
            "success": True,
            "message": f"Successfully created {len(created_groups)} device groups",
            "created_groups": [{"id": g.id, "name": g.name} for g in created_groups]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量创建设备分组失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create device groups: {str(e)}"
        )

@router.get("/{group_id}", response_model=DeviceGroupDetailResponse)
async def get_device_group_detail(
    group_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取设备分组详情"""
    try:
        # 查询设备分组
        stmt = select(DeviceGroup).where(DeviceGroup.id == group_id)
        result = await db.execute(stmt)
        group = result.scalar_one_or_none()
        
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备分组不存在"
            )
        
        # 查询分组中的设备
        devices_stmt = select(Device).join(
            DeviceGroupMember, Device.id == DeviceGroupMember.device_id
        ).where(DeviceGroupMember.group_id == group_id)
        devices_result = await db.execute(devices_stmt)
        devices = devices_result.scalars().all()
        
        device_list = []
        has_virtual = False
        for device in devices:
            if device.is_virtual:
                has_virtual = True
            device_list.append({
                "id": device.id,
                "device_id": device.device_id,
                "device_name": device.device_name,
                "device_type": device.device_type,
                "vendor_id": device.vendor_id,
                "product_id": device.product_id,
                "serial_number": device.serial_number,
                "status": device.status,
                "is_virtual": device.is_virtual,
                "slave_server_id": device.slave_server_id
            })
        
        return DeviceGroupDetailResponse(
            id=group.id,
            name=group.name,
            group_type=group.group_type,
            description=group.description,
            device_count=len(device_list),
            has_virtual_devices=has_virtual,
            created_by=group.created_by,
            organization_id=group.organization_id,
            created_at=group.created_at,
            updated_at=group.updated_at,
            devices=device_list
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备分组详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取设备分组详情失败: {str(e)}"
        )

@router.put("/{group_id}", response_model=Dict[str, Any])
async def update_device_group(
    group_id: int,
    request: DeviceGroupUpdateRequest,
    db: AsyncSession = Depends(get_db)
):
    """更新设备分组"""
    try:
        # 查询设备分组
        stmt = select(DeviceGroup).where(DeviceGroup.id == group_id)
        result = await db.execute(stmt)
        group = result.scalar_one_or_none()
        
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备分组不存在"
            )
        
        # 更新分组信息
        if request.name is not None:
            group.name = request.name
        if request.description is not None:
            group.description = request.description
        
        group.updated_at = datetime.now()
        
        await db.commit()
        
        logger.info(f"设备分组更新成功: {group_id}")
        return {
            "status": "success",
            "message": "设备分组更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新设备分组失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新设备分组失败: {str(e)}"
        )

@router.delete("/{group_id}", response_model=Dict[str, Any])
async def delete_device_group(
    group_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除设备分组 - 基于权限控制"""
    try:
        # 查询设备分组
        stmt = select(DeviceGroup).where(DeviceGroup.id == group_id)
        result = await db.execute(stmt)
        group = result.scalar_one_or_none()

        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备分组不存在"
            )

        # 检查用户是否有权限删除此分组
        can_manage = await DualPermissionChecker.can_manage_device_group(db, current_user, group)
        if not can_manage:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，无法删除此设备分组"
            )
        
        # 删除分组设备关联
        delete_devices_stmt = delete(DeviceGroupMember).where(
            DeviceGroupMember.group_id == group_id
        )
        await db.execute(delete_devices_stmt)
        
        # 删除用户权限
        delete_permissions_stmt = delete(UserDeviceGroupPermission).where(
            UserDeviceGroupPermission.device_group_id == group_id
        )
        await db.execute(delete_permissions_stmt)
        
        # 删除虚拟设备
        virtual_devices_stmt = select(Device.id).join(
            DeviceGroupMember, Device.id == DeviceGroupMember.device_id
        ).where(
            and_(
                DeviceGroupMember.group_id == group_id,
                Device.is_virtual == True
            )
        )
        virtual_result = await db.execute(virtual_devices_stmt)
        virtual_device_ids = virtual_result.scalars().all()
        
        if virtual_device_ids:
            delete_virtual_stmt = delete(Device).where(
                Device.id.in_(virtual_device_ids)
            )
            await db.execute(delete_virtual_stmt)
        
        # 删除设备分组
        delete_group_stmt = delete(DeviceGroup).where(DeviceGroup.id == group_id)
        await db.execute(delete_group_stmt)
        
        await db.commit()
        
        logger.info(f"设备分组删除成功: {group_id}")
        return {
            "status": "success",
            "message": "设备分组删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除设备分组失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除设备分组失败: {str(e)}"
        )

# ==================== 设备分组成员管理 ====================

@router.post("/{group_id}/devices", response_model=Dict[str, Any])
async def add_devices_to_group(
    group_id: int,
    request: DeviceGroupAddDevicesRequest,
    db: AsyncSession = Depends(get_db)
):
    """添加设备到分组"""
    try:
        # 检查分组是否存在
        stmt = select(DeviceGroup).where(DeviceGroup.id == group_id)
        result = await db.execute(stmt)
        group = result.scalar_one_or_none()

        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备分组不存在"
            )

        # 检查设备是否存在
        device_stmt = select(Device).where(Device.id.in_(request.device_ids))
        device_result = await db.execute(device_stmt)
        devices = device_result.scalars().all()

        if len(devices) != len(request.device_ids):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="部分设备不存在"
            )

        # 添加设备到分组（避免重复）
        added_count = 0
        for device_id in request.device_ids:
            # 检查是否已存在
            existing_stmt = select(DeviceGroupMember).where(
                and_(
                    DeviceGroupMember.group_id == group_id,
                    DeviceGroupMember.device_id == device_id
                )
            )
            existing_result = await db.execute(existing_stmt)
            if not existing_result.scalar_one_or_none():
                # 获取默认用户作为添加者
                user_stmt = select(User).limit(1)
                user_result = await db.execute(user_stmt)
                default_user = user_result.scalar_one_or_none()

                # 检查设备状态，支持离线设备分配
                device_stmt = select(Device).where(Device.id == device_id)
                device_result = await db.execute(device_stmt)
                device = device_result.scalar_one_or_none()

                if not device:
                    continue

                # 设备状态无关分配：离线设备也可以被分配
                # 无论设备状态如何，都允许分配到分组

                # 添加新成员
                member = DeviceGroupMember(
                    group_id=group_id,
                    device_id=device_id,
                    added_at=datetime.now(),
                    added_by=default_user.id if default_user else 1,
                    is_active=True
                )
                db.add(member)
                added_count += 1

        await db.commit()

        # 实时广播设备分配事件
        from core.websocket_manager import websocket_manager
        await websocket_manager.trigger_event("devices_assigned_to_group", {
            "group_id": group_id,
            "added_count": added_count,
            "timestamp": datetime.now().isoformat()
        })

        logger.info(f"成功添加 {added_count} 个设备到分组 {group_id}")
        return {
            "status": "success",
            "message": f"成功添加 {added_count} 个设备到分组",
            "added_count": added_count
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"添加设备到分组失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加设备到分组失败: {str(e)}"
        )

@router.get("/{group_id}/devices", response_model=Dict[str, Any])
async def get_group_devices(
    group_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取分组中的设备列表"""
    try:
        # 检查分组是否存在
        stmt = select(DeviceGroup).where(DeviceGroup.id == group_id)
        result = await db.execute(stmt)
        group = result.scalar_one_or_none()

        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备分组不存在"
            )

        # 查询分组中的设备
        device_stmt = select(Device, DeviceGroupMember).join(
            DeviceGroupMember, Device.id == DeviceGroupMember.device_id
        ).where(DeviceGroupMember.group_id == group_id)

        device_result = await db.execute(device_stmt)
        device_members = device_result.all()

        devices = []
        for device, member in device_members:
            devices.append({
                "id": device.id,
                "device_name": device.device_name,
                "device_type": device.device_type,
                "vendor_id": device.vendor_id,
                "product_id": device.product_id,
                "status": device.status,
                "slave_server_id": device.slave_server_id,
                "added_at": member.added_at.isoformat() if member.added_at else None
            })

        return {
            "success": True,
            "data": devices,
            "message": f"成功获取分组中的 {len(devices)} 个设备"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分组设备失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分组设备失败: {str(e)}"
        )

# ==================== 设备分组权限管理 ====================

@router.get("/{group_id}/permissions", response_model=List[DeviceGroupPermissionResponse])
async def get_group_permissions(
    group_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取设备分组权限列表"""
    try:
        # 检查分组是否存在
        stmt = select(DeviceGroup).where(DeviceGroup.id == group_id)
        result = await db.execute(stmt)
        group = result.scalar_one_or_none()

        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备分组不存在"
            )

        # 查询分组权限
        from sqlalchemy.orm import aliased
        granter_user = aliased(User)

        permission_stmt = select(
            UserDeviceGroupPermission,
            User.username,
            User.role_name,
            Organization.name.label("organization_name"),
            granter_user.full_name.label("granted_by_name")
        ).join(
            User, UserDeviceGroupPermission.user_id == User.id
        ).join(
            Organization, User.organization_id == Organization.id, isouter=True
        ).join(
            granter_user, UserDeviceGroupPermission.granted_by == granter_user.id, isouter=True
        ).where(
            and_(
                UserDeviceGroupPermission.device_group_id == group_id,
                UserDeviceGroupPermission.is_active == True
            )
        )

        permission_result = await db.execute(permission_stmt)
        permissions = permission_result.all()

        permission_list = []
        for perm, username, role_name, org_name, granter_name in permissions:
            permission_list.append(DeviceGroupPermissionResponse(
                id=perm.id,
                user_id=perm.user_id,
                username=username,
                user_role=role_name or "普通用户",
                organization_name=org_name or "未知组织",
                permission_type=perm.permission_type,
                granted_by=perm.granted_by,
                granted_by_name=granter_name or "系统",
                granted_at=perm.granted_at,
                expires_at=perm.expires_at,
                is_active=perm.is_active
            ))

        return permission_list

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分组权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分组权限失败: {str(e)}"
        )

@router.post("/{group_id}/permissions", response_model=Dict[str, Any])
async def grant_group_permissions(
    group_id: int,
    request: DeviceGroupPermissionRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """分配设备分组权限"""
    try:
        # 检查分组是否存在
        stmt = select(DeviceGroup).where(DeviceGroup.id == group_id)
        result = await db.execute(stmt)
        group = result.scalar_one_or_none()

        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备分组不存在"
            )

        # 验证权限类型
        valid_permissions = ["view", "use", "manage"]
        if request.permission_type not in valid_permissions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的权限类型，支持的类型: {', '.join(valid_permissions)}"
            )

        # 检查用户是否存在
        user_stmt = select(User).where(User.id.in_(request.user_ids))
        user_result = await db.execute(user_stmt)
        users = user_result.scalars().all()

        if len(users) != len(request.user_ids):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="部分用户不存在"
            )

        granted_count = 0
        for user in users:
            # 检查是否已有相同权限
            existing_stmt = select(UserDeviceGroupPermission).where(
                and_(
                    UserDeviceGroupPermission.user_id == user.id,
                    UserDeviceGroupPermission.device_group_id == group_id,
                    UserDeviceGroupPermission.permission_type == request.permission_type,
                    UserDeviceGroupPermission.is_active == True
                )
            )
            existing_result = await db.execute(existing_stmt)
            existing_permission = existing_result.scalar_one_or_none()

            if existing_permission:
                # 更新过期时间
                if request.expires_at:
                    existing_permission.expires_at = request.expires_at
                    granted_count += 1
            else:
                # 创建新权限
                new_permission = UserDeviceGroupPermission(
                    user_id=user.id,
                    device_group_id=group_id,
                    permission_type=request.permission_type,
                    granted_by=current_user.id,
                    granted_at=datetime.now(),
                    expires_at=request.expires_at,
                    is_active=True
                )
                db.add(new_permission)
                granted_count += 1

        await db.commit()

        logger.info(f"设备分组权限分配成功: 分组{group_id}, 用户{len(request.user_ids)}个, 权限{request.permission_type}")
        return {
            "status": "success",
            "message": f"成功分配权限给 {granted_count} 个用户",
            "granted_count": granted_count
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分配分组权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分配分组权限失败: {str(e)}"
        )

@router.delete("/{group_id}/permissions/{permission_id}", response_model=Dict[str, Any])
async def revoke_group_permission(
    group_id: int,
    permission_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """撤销设备分组权限"""
    try:
        # 查询权限记录
        stmt = select(UserDeviceGroupPermission).where(
            and_(
                UserDeviceGroupPermission.id == permission_id,
                UserDeviceGroupPermission.device_group_id == group_id,
                UserDeviceGroupPermission.is_active == True
            )
        )
        result = await db.execute(stmt)
        permission = result.scalar_one_or_none()

        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="权限记录不存在"
            )

        # 撤销权限（软删除）
        permission.is_active = False

        await db.commit()

        logger.info(f"设备分组权限撤销成功: 权限ID{permission_id}")
        return {
            "status": "success",
            "message": "权限撤销成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"撤销分组权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"撤销分组权限失败: {str(e)}"
        )

@router.get("/{group_id}/available-users", response_model=List[Dict[str, Any]])
async def get_available_users_for_group(
    group_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取可分配权限的用户列表"""
    try:
        # 检查分组是否存在
        stmt = select(DeviceGroup).where(DeviceGroup.id == group_id)
        result = await db.execute(stmt)
        group = result.scalar_one_or_none()

        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备分组不存在"
            )

        # 查询所有活跃用户
        user_stmt = select(User, Organization.name.label("organization_name")).join(
            Organization, User.organization_id == Organization.id, isouter=True
        ).where(
            and_(
                User.is_active == True,
                User.id != current_user.id  # 排除当前用户
            )
        ).order_by(User.username)

        user_result = await db.execute(user_stmt)
        users = user_result.all()

        user_list = []
        for user, org_name in users:
            user_list.append({
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "role_name": user.role_name or "普通用户",
                "organization_name": org_name or "未知组织",
                "is_active": user.is_active
            })

        return user_list

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取可分配用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取可分配用户失败: {str(e)}"
        )

@router.post("/batch-assign", response_model=Dict[str, Any])
async def batch_assign_devices_to_groups(
    request: Dict[str, Any],
    db: AsyncSession = Depends(get_db)
):
    """批量设备分配 - 动态化实现"""
    try:
        # 获取默认用户
        user_stmt = select(User).limit(1)
        user_result = await db.execute(user_stmt)
        default_user = user_result.scalar_one_or_none()

        if not default_user:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="No available users in system"
            )

        assignments = request.get("assignments", [])  # [{"group_id": 1, "device_ids": [1,2,3]}, ...]
        total_assigned = 0
        assignment_results = []

        for assignment in assignments:
            group_id = assignment.get("group_id")
            device_ids = assignment.get("device_ids", [])

            # 验证分组存在
            group_stmt = select(DeviceGroup).where(DeviceGroup.id == group_id)
            group_result = await db.execute(group_stmt)
            group = group_result.scalar_one_or_none()

            if not group:
                continue

            added_devices = []

            for device_id in device_ids:
                # 检查设备是否已在分组中
                existing_stmt = select(DeviceGroupMember).where(
                    and_(
                        DeviceGroupMember.group_id == group_id,
                        DeviceGroupMember.device_id == device_id,
                        DeviceGroupMember.is_active == True
                    )
                )
                existing_result = await db.execute(existing_stmt)
                if existing_result.scalar_one_or_none():
                    continue

                # 获取设备信息
                device_stmt = select(Device).where(Device.id == device_id)
                device_result = await db.execute(device_stmt)
                device = device_result.scalar_one_or_none()

                if not device:
                    continue

                # 创建分组成员关系（支持离线设备）
                member = DeviceGroupMember(
                    group_id=group_id,
                    device_id=device_id,
                    added_at=datetime.now(),
                    added_by=default_user.id,
                    is_active=True
                )

                db.add(member)
                added_devices.append({
                    "device_id": device_id,
                    "device_name": device.device_name,
                    "status": device.status
                })

            assignment_results.append({
                "group_id": group_id,
                "group_name": group.name,
                "added_devices": added_devices
            })
            total_assigned += len(added_devices)

        await db.commit()

        # 实时广播批量分配事件
        from core.websocket_manager import device_event_manager
        await device_event_manager.add_event({
            "type": "devices_batch_assigned",
            "assignments": assignment_results,
            "total_assigned": total_assigned,
            "timestamp": datetime.now().isoformat()
        })

        return {
            "success": True,
            "message": f"Successfully assigned {total_assigned} devices to groups",
            "assignments": assignment_results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量分配设备失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to batch assign devices: {str(e)}"
        )

@router.post("/reorganize", response_model=Dict[str, Any])
async def reorganize_devices_to_new_group(
    request: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """设备重新分组 - 管理员将继承设备重新组合创建新分组"""
    try:
        # 检查用户权限等级
        user_permission_level = await DualPermissionChecker.get_user_permission_level(db, current_user)
        if user_permission_level > 2:  # 只有管理员及以上可以重新分组
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只有管理员及以上可以进行设备重新分组"
            )

        # 解析请求参数
        new_group_name = request.get("group_name")
        new_group_description = request.get("group_description", "")
        device_ids = request.get("device_ids", [])
        source_group_id = request.get("source_group_id")  # 继承来源分组
        parent_group_id = request.get("parent_group_id")  # 父分组（可选）

        if not new_group_name or not device_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="分组名称和设备列表不能为空"
            )

        # 验证来源分组权限
        if source_group_id:
            source_group_stmt = select(DeviceGroup).where(DeviceGroup.id == source_group_id)
            source_group_result = await db.execute(source_group_stmt)
            source_group = source_group_result.scalar_one_or_none()

            if not source_group:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="来源分组不存在"
                )

            # 检查管理员是否有权限查看来源分组的设备
            can_view_devices = await DualPermissionChecker.can_view_device_details(db, current_user, source_group)
            if not can_view_devices:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权限访问来源分组的设备"
                )

        # 验证设备是否存在且在来源分组中
        for device_id in device_ids:
            device_stmt = select(Device).where(Device.id == device_id)
            device_result = await db.execute(device_stmt)
            device = device_result.scalar_one_or_none()

            if not device:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"设备 {device_id} 不存在"
                )

            # 如果指定了来源分组，验证设备是否在该分组中
            if source_group_id:
                member_stmt = select(DeviceGroupMember).where(
                    and_(
                        DeviceGroupMember.group_id == source_group_id,
                        DeviceGroupMember.device_id == device_id,
                        DeviceGroupMember.is_active == True
                    )
                )
                member_result = await db.execute(member_stmt)
                if not member_result.scalar_one_or_none():
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"设备 {device_id} 不在指定的来源分组中"
                    )

        # 检查嵌套深度限制
        nesting_level = 0
        if parent_group_id:
            nesting_level = await DualPermissionChecker.check_nesting_depth(db, parent_group_id)
            if nesting_level >= 3:  # 最多4层（0-3）
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="嵌套深度超过限制（最多4层）"
                )

        # 创建新的设备分组
        new_group = DeviceGroup(
            name=new_group_name,
            group_type="reorganized",  # 标识为重新分组
            description=new_group_description,
            parent_group_id=parent_group_id,
            inherited_from=source_group_id,  # 记录继承来源
            nesting_level=nesting_level,
            created_by=current_user.id,
            organization_id=current_user.organization_id,
            is_active=True
        )

        db.add(new_group)
        await db.flush()  # 获取新分组ID

        # 将设备添加到新分组
        added_devices = []
        for device_id in device_ids:
            # 检查设备是否已在新分组中
            existing_stmt = select(DeviceGroupMember).where(
                and_(
                    DeviceGroupMember.group_id == new_group.id,
                    DeviceGroupMember.device_id == device_id,
                    DeviceGroupMember.is_active == True
                )
            )
            existing_result = await db.execute(existing_stmt)
            if existing_result.scalar_one_or_none():
                continue

            # 添加设备到新分组
            member = DeviceGroupMember(
                group_id=new_group.id,
                device_id=device_id,
                added_at=datetime.now(),
                added_by=current_user.id,
                is_active=True
            )

            db.add(member)
            added_devices.append(device_id)

        await db.commit()

        # 实时广播重新分组事件
        from core.websocket_manager import websocket_manager
        await websocket_manager.trigger_event("devices_reorganized", {
            "new_group_id": new_group.id,
            "new_group_name": new_group.name,
            "source_group_id": source_group_id,
            "device_count": len(added_devices),
            "created_by": current_user.username,
            "timestamp": datetime.now().isoformat()
        })

        logger.info(f"设备重新分组成功: 新分组 {new_group.name}, {len(added_devices)} 个设备")
        return {
            "success": True,
            "message": f"成功创建新分组并重新分配 {len(added_devices)} 个设备",
            "new_group_id": new_group.id,
            "new_group_name": new_group.name,
            "device_count": len(added_devices)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设备重新分组失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"设备重新分组失败: {str(e)}"
        )
