#!/usr/bin/env python3
"""
用户管理路由 V2 - 性能优化版
实现基于角色的用户数据访问控制和firefly账户隐藏
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
import logging
from enum import Enum

from database import get_db
from models import User, Organization, Role
from auth_utils import get_current_user
from pydantic import BaseModel, Field
from permissions import (
    Permission<PERSON>he<PERSON>, DataFilter, require_permission,
    require_super_admin_or_above, PermissionLevel
)

router = APIRouter(prefix="/api/v2/users", tags=["用户管理V2"])
logger = logging.getLogger(__name__)

# 响应模型定义
class UserResponse(BaseModel):
    id: int
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    organization_id: Optional[int] = None
    organization_name: Optional[str] = None
    role_name: Optional[str] = None
    is_active: bool = True
    is_superuser: bool = False
    is_hardcoded_admin: bool = False
    last_login_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class OrganizationResponse(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    parent_id: Optional[int] = None
    level: int = 0
    is_active: bool = True
    created_at: datetime
    updated_at: datetime
    children: List['OrganizationResponse'] = []
    users: List[UserResponse] = []
    user_count: int = 0

    class Config:
        from_attributes = True

# 更新前向引用
OrganizationResponse.model_rebuild()

@router.get("/test")
async def test_endpoint():
    """测试端点，验证路由是否正常工作"""
    logger.info("🔍 测试端点被调用")
    return {"status": "ok", "message": "users_v2 路由正常工作"}

async def has_organization_access(db: AsyncSession, current_user: User, org_id: int) -> bool:
    """检查用户是否有权限访问指定组织"""
    try:
        # 管理员可以访问自己组织及其子组织
        if current_user.organization_id == org_id:
            return True
        
        # 全域管理员和超级管理员可以访问所有组织
        if PermissionChecker.is_global_admin(current_user) or PermissionChecker.is_super_admin(current_user):
            return True
        
        return False
    except Exception as e:
        logger.error(f"检查组织访问权限失败: {e}")
        return False

async def get_child_organizations(db: AsyncSession, parent_org_id: int) -> List[int]:
    """获取指定组织的所有子组织ID"""
    try:
        result = await db.execute(
            select(Organization.id).where(Organization.parent_id == parent_org_id)
        )
        child_ids = [row[0] for row in result.fetchall()]
        
        # 递归获取子组织的子组织
        all_child_ids = child_ids.copy()
        for child_id in child_ids:
            grandchild_ids = await get_child_organizations(db, child_id)
            all_child_ids.extend(grandchild_ids)
        
        return all_child_ids
    except Exception as e:
        logger.error(f"获取子组织失败: {e}")
        return []

class FilterMode(str, Enum):
    """筛选模式枚举"""
    ALL_WITH_HIERARCHY_WITH_ROLE = "all_with_hierarchy_with_role"
    ALL_WITH_HIERARCHY_NO_ROLE = "all_with_hierarchy_no_role"
    ALL_NO_HIERARCHY_WITH_ROLE = "all_no_hierarchy_with_role"
    ALL_NO_HIERARCHY_NO_ROLE = "all_no_hierarchy_no_role"

class UserResponse(BaseModel):
    """用户响应模型"""
    id: int
    username: str
    email: str
    full_name: str
    phone: Optional[str] = None
    organization_id: Optional[int] = None
    organization_name: Optional[str] = None
    role_name: Optional[str] = None
    is_active: bool
    is_superuser: Optional[bool] = False
    is_hardcoded_admin: Optional[bool] = False
    last_login_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    can_edit: Optional[bool] = False
    can_delete: Optional[bool] = False
    is_hidden: Optional[bool] = False

    class Config:
        from_attributes = True

class UserListResponse(BaseModel):
    """用户列表响应模型"""
    users: List[UserResponse]
    total: int
    page: int
    page_size: int
    filtered_by_permission: bool = False
    filter_mode: Optional[str] = None
    available_filter_modes: List[str] = []
    current_user_permissions: dict = {}

class UserUpdateRequest(BaseModel):
    """用户更新请求模型"""
    full_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = None
    role_name: Optional[str] = None
    organization_id: Optional[int] = None

def get_available_filter_modes(current_user: User) -> List[str]:
    """根据用户角色获取可用的筛选模式"""
    if PermissionChecker.is_global_admin(current_user):
        return [mode.value for mode in FilterMode]
    elif PermissionChecker.is_super_admin(current_user):
        return [mode.value for mode in FilterMode]
    elif PermissionChecker.is_admin(current_user):
        return [mode.value for mode in FilterMode]
    elif PermissionChecker.is_regular_user(current_user):
        return [mode.value for mode in FilterMode]
    else:
        return [FilterMode.ALL_NO_HIERARCHY_NO_ROLE.value]

@router.get("/list", response_model=UserListResponse)
async def get_users_list(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    organization_id: Optional[int] = Query(None, description="组织ID过滤"),
    include_children: Optional[bool] = Query(False, description="是否包含子组织用户"),
    role_filter: Optional[str] = Query(None, description="角色过滤"),
    filter_mode: Optional[FilterMode] = Query(FilterMode.ALL_WITH_HIERARCHY_WITH_ROLE, description="筛选模式"),
    scope: Optional[str] = Query(None, description="显示范围: all, current_only, current_and_sub"),
    org_id: Optional[int] = Query(None, description="智能联动组织ID"),
    org_filter: Optional[str] = Query(None, description="组织筛选: by_level, none"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户列表 - 支持智能联动和多维度筛选"""
    return await _get_users_impl(request, page, page_size, search, organization_id, include_children,
                                role_filter, filter_mode, scope, org_id, org_filter, current_user, db)

@router.get("/", response_model=UserListResponse)
async def get_users(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    organization_id: Optional[int] = Query(None, description="组织ID过滤"),
    include_children: Optional[bool] = Query(False, description="是否包含子组织用户"),
    role_filter: Optional[str] = Query(None, description="角色过滤"),
    filter_mode: Optional[FilterMode] = Query(FilterMode.ALL_WITH_HIERARCHY_WITH_ROLE, description="筛选模式"),
    scope: Optional[str] = Query(None, description="显示范围: all, current_only, current_and_sub"),
    org_id: Optional[int] = Query(None, description="智能联动组织ID"),
    org_filter: Optional[str] = Query(None, description="组织筛选: by_level, none"),
    # 🔧 修复：处理前端嵌套参数
    params_page: Optional[int] = Query(None, alias="params[page]", description="嵌套参数-页码"),
    params_size: Optional[int] = Query(None, alias="params[size]", description="嵌套参数-每页数量"),
    params_page_size: Optional[int] = Query(None, alias="params[page_size]", description="嵌套参数-页面大小"),
    params_role_filter: Optional[str] = Query(None, alias="params[role_filter]", description="嵌套参数-角色过滤"),
    params_org_filter: Optional[str] = Query(None, alias="params[org_filter]", description="嵌套参数-组织过滤"),
    params_filter_mode: Optional[str] = Query(None, alias="params[filter_mode]", description="嵌套参数-过滤模式"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户列表 - 基于权限过滤，支持嵌套参数"""
    # 🔧 修复：处理嵌套参数优先级
    final_page = params_page or page
    final_page_size = params_page_size or params_size or page_size
    final_role_filter = params_role_filter or role_filter
    final_org_filter = params_org_filter or org_filter

    # 🔧 修复：正确处理filter_mode类型转换
    if params_filter_mode:
        try:
            final_filter_mode = FilterMode(params_filter_mode)
        except (ValueError, TypeError):
            final_filter_mode = FilterMode.ALL_WITH_HIERARCHY_WITH_ROLE
    else:
        final_filter_mode = filter_mode or FilterMode.ALL_WITH_HIERARCHY_WITH_ROLE

    logger.info(f"🔍 用户API调试 - 参数: page={final_page}, page_size={final_page_size}, role_filter={final_role_filter}, filter_mode={final_filter_mode}")

    return await _get_users_impl(request, final_page, final_page_size, search, organization_id, include_children,
                                final_role_filter, final_filter_mode, scope, org_id, final_org_filter, current_user, db)

async def _get_users_impl(
    request: Request,
    page: int,
    page_size: int,
    search: Optional[str],
    organization_id: Optional[int],
    include_children: Optional[bool],
    role_filter: Optional[str],
    filter_mode: Optional[FilterMode],
    scope: Optional[str],
    org_id: Optional[int],
    org_filter: Optional[str],
    current_user: User,
    db: AsyncSession
):
    """获取用户列表的共享实现 - 性能优化版"""
    try:
        logger.info(f"🔍 用户API实现调试 - 用户: {current_user.username}, 参数: page={page}, page_size={page_size}")

        # 使用性能优化的查询函数
        from database_query_utils import get_users_with_pagination_optimized
        from middleware.api_response_middleware import paginated_response

        # 🔧 修复：确定状态过滤，添加安全检查
        status_filter = None
        try:
            if filter_mode and "ACTIVE" in str(filter_mode):
                status_filter = "active"
        except Exception as e:
            logger.warning(f"处理filter_mode时出错: {e}, 使用默认值")
            status_filter = None

        # 🔧 修复：使用优化的分页查询，添加错误处理
        try:
            result = await get_users_with_pagination_optimized(
                db=db,
                page=page,
                page_size=page_size,
                search=search,
                organization_id=organization_id or org_id,
                role_filter=role_filter,
                status_filter=status_filter
            )
        except Exception as query_error:
            logger.error(f"数据库查询失败: {query_error}")
            # 返回空结果而不是抛出异常
            result = {
                "users": [],
                "total": 0,
                "page": page,
                "page_size": page_size,
                "total_pages": 0
            }

        logger.info(f"🔍 查询结果调试 - 用户数量: {len(result.get('users', []))}")

        # 应用权限过滤到结果 - 性能优化版本
        filtered_users = result["users"]

        # 根据用户角色过滤结果
        if current_user.username == "firefly" or current_user.role_name == "全域管理员":
            pass  # 不过滤
        elif current_user.role_name == "超级管理员":
            filtered_users = [user for user in filtered_users if user.get("username") != "firefly"]
        elif current_user.role_name == "管理员":
            if current_user.organization_id:
                filtered_users = [user for user in filtered_users if user.get("organization_id") == current_user.organization_id]
            else:
                filtered_users = [user for user in filtered_users if user.get("id") == current_user.id]
        elif current_user.role_name == "普通用户":
            filtered_users = [user for user in filtered_users if user.get("organization_id") == current_user.organization_id]
        else:
            filtered_users = [user for user in filtered_users if user.get("id") == current_user.id]

        # 更新结果
        result["users"] = filtered_users
        result["total"] = len(filtered_users)
        result["total_pages"] = (len(filtered_users) + page_size - 1) // page_size

        logger.info(f"🔍 过滤后结果调试 - 用户数量: {len(filtered_users)}")

        # 🔧 修复：处理NULL datetime字段，避免Pydantic验证错误
        from datetime import datetime
        default_time = datetime.now()

        processed_users = []
        for user in result["users"]:
            # 确保datetime字段不为None
            user_data = user.copy()
            if user_data.get("created_at") is None:
                user_data["created_at"] = default_time
            if user_data.get("updated_at") is None:
                user_data["updated_at"] = default_time
            if user_data.get("last_login_at") is None:
                user_data["last_login_at"] = None  # 这个字段允许为None

            processed_users.append(UserResponse(**user_data))

        # 🔧 修复：直接返回UserListResponse对象，避免API中间件包装冲突
        return UserListResponse(
            users=processed_users,
            total=result["total"],
            page=result["page"],
            page_size=result["page_size"],
            filtered_by_permission=True,
            filter_mode=str(filter_mode) if filter_mode else None,
            available_filter_modes=[mode.value for mode in FilterMode],
            current_user_permissions={
                "can_manage_users": True,
                "can_view_all_users": current_user.username == "firefly" or current_user.role_name == "全域管理员"
            }
        )

    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")

        # 🔧 修复：返回更详细的错误信息用于调试
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户列表失败: {str(e)}"
        )


@router.get("/organization-tree", response_model=List[dict])
async def get_organization_tree(
    include_users: bool = Query(True, description="是否包含用户"),
    exclude_empty: bool = Query(True, description="是否排除空组织"),
    exclude_new_users: bool = Query(True, description="是否排除新用户"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取组织架构树（V2版本，兼容前端调用）"""
    logger.info(f"🔍 组织架构API调用开始 - 用户: {current_user.username}, 参数: include_users={include_users}, exclude_empty={exclude_empty}, exclude_new_users={exclude_new_users}")
    try:
        # 直接实现组织架构树逻辑，避免导入问题

        # 获取所有组织
        org_result = await db.execute(
            select(Organization).order_by(Organization.level, Organization.name)
        )
        organizations = org_result.scalars().all()

        # 获取所有用户
        user_result = await db.execute(
            select(User).order_by(User.full_name)
        )
        users = user_result.scalars().all()

        # 构建组织树形结构
        org_dict = {}
        org_responses = []

        for org in organizations:
            # 获取该组织的用户
            org_users = [user for user in users if user.organization_id == org.id]

            org_response = OrganizationResponse(
                id=org.id,
                name=org.name,
                description=org.description,
                parent_id=org.parent_id,
                level=org.level,
                is_active=org.is_active if org.is_active is not None else True,
                created_at=org.created_at or datetime.utcnow(),
                updated_at=org.updated_at or datetime.utcnow(),
                children=[],
                users=[
                    UserResponse(
                        id=user.id,
                        username=user.username,
                        email=user.email,
                        full_name=user.full_name,
                        phone=user.phone,
                        organization_id=user.organization_id,
                        organization_name=org.name,
                        role_name=user.role_name,
                        is_active=user.is_active,
                        is_superuser=user.is_superuser if user.is_superuser is not None else False,
                        is_hardcoded_admin=user.is_hardcoded_admin if user.is_hardcoded_admin is not None else False,
                        last_login_at=user.last_login_at,
                        created_at=user.created_at or datetime.utcnow(),
                        updated_at=user.updated_at or datetime.utcnow()
                    ) for user in org_users
                ],
                user_count=len(org_users)
            )

            org_dict[org.id] = org_response

        # 构建根组织列表
        for org in organizations:
            if org.parent_id is None or org.parent_id not in org_dict:
                org_responses.append(org_dict[org.id])

        # 构建父子关系
        for org in organizations:
            if org.parent_id and org.parent_id in org_dict:
                org_dict[org.parent_id].children.append(org_dict[org.id])

        org_tree = org_responses

        # 如果需要排除新用户，过滤掉permission_level=4的用户
        if exclude_new_users:
            def filter_new_users(org_list):
                filtered_orgs = []
                for org in org_list:
                    # 过滤用户列表 - 基于数据库用户数据而不是响应模型
                    if hasattr(org, 'users') and org.users:
                        filtered_users = []
                        for user_response in org.users:
                            try:
                                # 从数据库用户列表中找到对应的用户
                                db_user = next((u for u in users if u.id == user_response.id), None)
                                if db_user:
                                    # 安全地获取permission_level，如果不存在则默认为4（新用户）
                                    perm_level = getattr(db_user, 'permission_level', 4)
                                    if perm_level is None:
                                        perm_level = 4
                                    if perm_level < 4:
                                        filtered_users.append(user_response)
                                else:
                                    # 如果找不到对应的数据库用户，默认包含
                                    filtered_users.append(user_response)
                            except Exception:
                                # 如果获取permission_level失败，默认包含该用户
                                filtered_users.append(user_response)
                        org.users = filtered_users

                    # 递归处理子组织
                    if hasattr(org, 'children') and org.children:
                        org.children = filter_new_users(org.children)

                    # 如果排除空组织且该组织没有用户和子组织，则跳过
                    if exclude_empty:
                        has_users = hasattr(org, 'users') and org.users
                        has_children = hasattr(org, 'children') and org.children
                        if not has_users and not has_children:
                            continue

                    filtered_orgs.append(org)
                return filtered_orgs

            org_tree = filter_new_users(org_tree)

        logger.info(f"V2组织树API调用成功，用户: {current_user.username}")
        return org_tree

    except Exception as e:
        logger.error(f"获取组织树失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取组织树失败: {str(e)}"
        )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取单个用户信息 - 性能优化版"""
    try:
        # 权限检查
        if not await PermissionChecker.check_user_management_permission(db, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="您没有用户管理权限"
            )

        # 使用优化查询获取用户信息
        query = select(
            User.id,
            User.username,
            User.email,
            User.full_name,
            User.phone,
            User.role_name,
            User.is_active,
            User.organization_id,
            Organization.name.label('organization_name'),
            User.created_at,
            User.updated_at
        ).join(Organization, User.organization_id == Organization.id).where(User.id == user_id)
        
        result = await db.execute(query)
        user_data = result.first()
        
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return dict(user_data._mapping)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    update_data: UserUpdateRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新用户信息 - 性能优化版"""
    try:
        # 权限检查
        if not await PermissionChecker.check_user_management_permission(db, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="您没有用户管理权限"
            )

        # 获取用户
        user_result = await db.execute(select(User).where(User.id == user_id))
        user = user_result.scalar_one_or_none()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 更新用户信息
        for field, value in update_data.dict(exclude_unset=True).items():
            setattr(user, field, value)

        await db.commit()
        await db.refresh(user)

        return user
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户信息失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户信息失败"
        )


@router.delete("/{user_id}")
async def delete_user(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除用户 - 性能优化版"""
    try:
        # 权限检查
        if not await PermissionChecker.check_user_management_permission(db, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="您没有用户管理权限"
            )

        # 获取用户
        user_result = await db.execute(select(User).where(User.id == user_id))
        user = user_result.scalar_one_or_none()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 删除用户
        await db.delete(user)
        await db.commit()

        return {"message": f"用户 {user.username} 已成功删除"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除用户失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )
