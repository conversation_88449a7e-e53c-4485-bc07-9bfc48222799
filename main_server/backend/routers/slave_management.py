#!/usr/bin/env python3
"""
从服务器管理API路由
提供从服务器注册、心跳、设备管理等功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request, Body
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, update, delete, func, text
from typing import List
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import logging
import aiohttp
import asyncio
import json

from database import get_db
from models import User, SlaveServer, Device
from auth_utils import get_current_user
from permissions import require_global_admin, require_super_admin_or_above
from core.usb_ids_manager import get_usb_ids_manager

router = APIRouter(prefix="/api/v1/slave", tags=["从服务器管理"])
logger = logging.getLogger(__name__)

# ==================== 辅助函数 ====================

async def get_next_available_id(db: AsyncSession) -> int:
    """智能获取下一个可用的ID，自动检测和重用间隙（并发安全）"""
    try:
        # 使用数据库行级锁确保并发安全
        # 锁定slave_servers表以防止并发ID分配冲突
        await db.execute(text("LOCK TABLE slave_servers IN EXCLUSIVE MODE"))

        # 查询所有已使用的ID，按升序排列
        stmt = select(SlaveServer.id).order_by(SlaveServer.id)
        result = await db.execute(stmt)
        used_ids = [row[0] for row in result.fetchall()]

        # 如果没有任何记录，从1开始
        if not used_ids:
            logger.info("没有现有服务器，分配ID: 1")
            return 1

        # 查找第一个缺失的ID（间隙检测）
        for i, used_id in enumerate(used_ids, start=1):
            if i != used_id:
                logger.info(f"检测到ID间隙，重用ID: {i}")
                return i

        # 如果没有间隙，返回下一个连续ID
        next_id = len(used_ids) + 1
        logger.info(f"没有ID间隙，分配新ID: {next_id}")
        return next_id

    except Exception as e:
        logger.error(f"获取下一个可用ID失败: {e}")
        # 如果出错，使用传统的最大ID+1方式作为备用
        try:
            stmt = select(func.max(SlaveServer.id))
            result = await db.execute(stmt)
            max_id = result.scalar()
            fallback_id = (max_id or 0) + 1
            logger.warning(f"使用备用ID分配方式: {fallback_id}")
            return fallback_id
        except Exception as fallback_error:
            logger.error(f"备用ID分配也失败: {fallback_error}")
            return 1

async def reset_slave_server_sequence(db: AsyncSession) -> bool:
    """重置从服务器ID序列，用于清理测试数据后"""
    try:
        # 检查是否还有从服务器记录
        stmt = select(func.count(SlaveServer.id))
        result = await db.execute(stmt)
        server_count = result.scalar()

        if server_count == 0:
            # 如果没有记录，重置序列到1
            await db.execute(text("ALTER SEQUENCE slave_servers_id_seq RESTART WITH 1"))
            await db.commit()
            logger.info("已重置slave_servers序列到1")
            return True
        else:
            # 如果还有记录，重置序列到最大ID+1
            stmt = select(func.max(SlaveServer.id))
            result = await db.execute(stmt)
            max_id = result.scalar()
            next_id = (max_id or 0) + 1

            await db.execute(text(f"ALTER SEQUENCE slave_servers_id_seq RESTART WITH {next_id}"))
            await db.commit()
            logger.info(f"已重置slave_servers序列到{next_id}")
            return True

    except Exception as e:
        logger.error(f"重置序列失败: {e}")
        return False

# ==================== 数据模型 ====================

class SlaveServerRegisterRequest(BaseModel):
    """从服务器注册请求"""
    server_name: str = Field(..., description="服务器名称")
    server_ip: str = Field(..., description="服务器IP地址")
    server_port: int = Field(default=8889, description="服务器端口")
    vh_port: int = Field(default=7575, description="VirtualHere端口")
    hardware_uuid: Optional[str] = Field(None, description="硬件UUID")
    hardware_info: Optional[Dict[str, Any]] = Field(None, description="硬件信息摘要")
    description: Optional[str] = Field(None, description="服务器描述")
    version: Optional[str] = Field("2.0", description="从服务器版本")
    # location: Optional[str] = Field(None, description="服务器位置")  # 暂时注释，数据库模型中暂无此字段

class DeviceDetailInfo(BaseModel):
    """设备详细信息"""
    hardware_signature: str = Field(..., description="硬件签名")
    vendor_id: int = Field(..., description="厂商ID")
    product_id: int = Field(..., description="产品ID")
    bus: int = Field(..., description="总线号")
    address: int = Field(..., description="地址")
    description: str = Field(..., description="设备描述")
    device_type: str = Field(..., description="设备类型")
    is_real_hardware: bool = Field(..., description="是否为真实硬件")
    auto_bind_eligible: bool = Field(..., description="是否可自动绑定")
    auto_generated_name: str = Field("", description="自动生成名称")
    custom_name: str = Field("", description="自定义名称")
    status: str = Field("online", description="设备状态")
    port_location_code: str = Field("", description="端口位置码")
    physical_port: str = Field("", description="物理端口")
    last_seen: str = Field(..., description="最后发现时间")

class DeviceSummaryInfo(BaseModel):
    """设备摘要信息"""
    total_devices: int = Field(..., description="设备总数")
    real_hardware_count: int = Field(..., description="真实硬件设备数")
    auto_bind_eligible_count: int = Field(..., description="可自动绑定设备数")
    device_types: Dict[str, int] = Field(..., description="设备类型统计")

class SlaveServerHeartbeatRequest(BaseModel):
    """从服务器心跳请求"""
    timestamp: str = Field(..., description="时间戳")
    status: str = Field(..., description="服务器状态")
    device_count: int = Field(..., description="设备数量")
    vh_status: str = Field(..., description="VirtualHere状态")
    system_info: Dict[str, Any] = Field(..., description="系统信息")
    server_name: Optional[str] = Field(None, description="服务器名称（用于自适应更新）")
    server_port: Optional[int] = Field(None, description="服务器端口（用于自适应更新）")
    vh_port: Optional[int] = Field(None, description="VirtualHere端口（用于自适应更新）")
    server_ip: Optional[str] = Field(None, description="服务器IP（用于验证）")
    device_details: Optional[List[DeviceDetailInfo]] = Field(None, description="设备详细信息")
    device_summary: Optional[DeviceSummaryInfo] = Field(None, description="设备摘要信息")

class SlaveServerControlRequest(BaseModel):
    """从服务器控制请求"""
    action: str = Field(..., description="控制动作")
    parameters: Optional[Dict[str, Any]] = Field(None, description="控制参数")

class SlaveServerResponse(BaseModel):
    """从服务器响应"""
    id: int
    server_id: str
    name: Optional[str]
    ip_address: Optional[str]
    port: Optional[int]
    vh_port: Optional[int]
    status: str
    last_seen: Optional[str]  # 改为字符串格式
    description: Optional[str]
    location: Optional[str]
    created_at: Optional[str]  # 改为字符串格式
    device_count: Optional[int] = 0
    hardware_uuid: Optional[str] = None
    config_version: Optional[int] = None
    hardware_info: Optional[Dict[str, Any]] = None

class DeviceResponse(BaseModel):
    """设备响应"""
    id: int
    device_name: str
    device_type: str
    vendor_id: str
    product_id: str
    serial_number: Optional[str]
    status: str
    slave_server_id: int
    created_at: datetime

# ==================== 从服务器注册 ====================

@router.post("/register", response_model=Dict[str, Any])
async def register_slave_server(
    request: SlaveServerRegisterRequest,
    client_request: Request,
    db: AsyncSession = Depends(get_db)
):
    """从服务器注册接口"""
    try:
        # 获取客户端真实IP地址（与心跳检查保持一致）
        client_ip = client_request.client.host

        # 如果请求中的IP是auto-detect，则使用客户端IP
        actual_ip = client_ip if request.server_ip == "auto-detect" else request.server_ip

        logger.info(f"从服务器注册请求 - 客户端IP: {client_ip}, 请求IP: {request.server_ip}, 实际使用IP: {actual_ip}")

        # 智能查找现有的从服务器（基于IP+端口组合，确保唯一性）
        existing_server = None

        # 1. 优先基于IP地址+端口组合查找（最准确的唯一标识）
        stmt = select(SlaveServer).where(
            SlaveServer.ip_address == actual_ip,
            SlaveServer.port == request.server_port
        )
        result = await db.execute(stmt)
        existing_server = result.scalar_one_or_none()

        if existing_server:
            logger.info(f"基于IP+端口找到现有从服务器: {existing_server.name} (IP: {actual_ip}:{request.server_port})")

        # 2. 如果IP+端口查找失败，基于hardware_uuid查找（兼容性）
        if not existing_server and request.hardware_uuid:
            stmt = select(SlaveServer).where(SlaveServer.hardware_uuid == request.hardware_uuid)
            result = await db.execute(stmt)
            potential_servers = result.scalars().all()

            # 如果找到多个相同UUID的服务器，选择端口匹配的
            for server in potential_servers:
                if server.port == request.server_port:
                    existing_server = server
                    logger.info(f"基于UUID+端口找到现有从服务器: {existing_server.name} (UUID: {request.hardware_uuid}, 端口: {request.server_port})")
                    break

        # 3. 检查服务器名称冲突（仅在创建新服务器时）
        name_conflict_server = None
        if not existing_server:
            stmt = select(SlaveServer).where(SlaveServer.name == request.server_name)
            result = await db.execute(stmt)
            name_conflict_server = result.scalar_one_or_none()

            if name_conflict_server:
                logger.warning(f"检测到服务器名称冲突: {request.server_name} 已被服务器 {name_conflict_server.id} 使用")
                # 生成建议的替代名称
                import time
                timestamp = int(time.time() % 10000)
                suggested_name = f"{request.server_name}-{timestamp}"
                logger.info(f"建议使用替代名称: {suggested_name}")

        if existing_server:
            # 更新现有从服务器信息（基于UUID或IP匹配）
            existing_server.name = request.server_name
            existing_server.ip_address = actual_ip  # 使用实际检测到的IP
            existing_server.port = request.server_port
            existing_server.description = request.description or existing_server.description
            existing_server.version = request.version or existing_server.version
            existing_server.status = "online"
            existing_server.last_seen = datetime.now()
            existing_server.updated_at = datetime.now()

            # 更新或设置hardware_uuid
            if request.hardware_uuid and not existing_server.hardware_uuid:
                existing_server.hardware_uuid = request.hardware_uuid
                logger.info(f"为现有从服务器设置UUID: {request.hardware_uuid}")

            # 更新硬件信息
            if request.hardware_info:
                existing_server.hardware_info = request.hardware_info

            # 只在有实际配置变更时才递增版本
            # 这里简化处理：只在首次注册或硬件信息变更时递增版本
            if not existing_server.hardware_uuid or (request.hardware_info and existing_server.hardware_info != request.hardware_info):
                existing_server.config_version = (existing_server.config_version or 0) + 1
                logger.info(f"从服务器配置有变更，版本递增至: {existing_server.config_version}")
            else:
                logger.debug(f"从服务器配置无变更，版本保持: {existing_server.config_version}")

            await db.commit()
            await db.refresh(existing_server)

            logger.info(f"从服务器已更新: {request.server_name} ({request.server_ip})")
            return {
                "status": "success",
                "message": "从服务器信息已更新",
                "server_id": existing_server.id,
                "hardware_uuid": existing_server.hardware_uuid,
                "config_version": existing_server.config_version,
                "action": "updated"
            }

        # 如果没有找到现有服务器，创建新的从服务器
        # 生成有意义的默认名称
        ip_suffix = actual_ip.split('.')[-1]  # 获取IP最后一段
        default_name = f"OmniLink-Slave-{ip_suffix}-{request.server_port}"

        # 安全处理server_name，防止None值导致的AttributeError，并处理名称冲突
        try:
            if request.server_name and isinstance(request.server_name, str) and request.server_name.strip():
                proposed_name = request.server_name.strip()

                # 如果检测到名称冲突，自动生成新名称
                if name_conflict_server:
                    import time
                    timestamp = int(time.time() % 10000)
                    server_name = f"{proposed_name}-{timestamp}"
                    logger.info(f"名称冲突自动解决: {proposed_name} -> {server_name}")
                else:
                    server_name = proposed_name
            else:
                server_name = default_name
                logger.info(f"使用默认服务器名称: {default_name} (原名称: {request.server_name})")
        except Exception as e:
            logger.warning(f"处理server_name时出错: {e}, 使用默认名称: {default_name}")
            server_name = default_name

        # 智能分配ID：自动检测和重用间隙
        next_id = await get_next_available_id(db)

        # 创建新的从服务器（使用智能ID分配）
        new_server = SlaveServer(
            id=next_id,  # 显式设置智能分配的ID
            server_id=f"slave-{actual_ip.replace('.', '-')}-{request.server_port}",
            hardware_uuid=request.hardware_uuid,  # 设置硬件UUID
            name=server_name,
            ip_address=actual_ip,  # 使用实际检测到的IP
            port=request.server_port,
            vh_port=request.vh_port,  # 保存VirtualHere端口
            description=request.description or f"OmniLink从服务器 - {actual_ip}:{request.server_port}",
            version=request.version or "2.0",
            hardware_info=request.hardware_info,  # 保存硬件信息
            config_version=1,  # 初始配置版本
            status="online",
            last_seen=datetime.now(),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        db.add(new_server)
        await db.commit()
        await db.refresh(new_server)

        logger.info(f"新从服务器已注册: {server_name} ({actual_ip})")
        return {
            "status": "success",
            "message": "从服务器注册成功",
            "server_id": new_server.id,
            "hardware_uuid": new_server.hardware_uuid,
            "config_version": new_server.config_version,
            "action": "created"
        }

        db.add(new_server)
        await db.commit()
        await db.refresh(new_server)

        logger.info(f"新从服务器已注册: {server_name} ({actual_ip})")
        return {
            "status": "success",
            "message": "从服务器注册成功",
            "server_id": new_server.id,
            "hardware_uuid": new_server.hardware_uuid,
            "config_version": new_server.config_version,
            "action": "created"
        }
            
    except Exception as e:
        import traceback
        logger.error(f"从服务器注册失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        logger.error(f"请求数据: server_name={getattr(request, 'server_name', 'N/A')}, server_ip={getattr(request, 'server_ip', 'N/A')}")
        logger.error(f"客户端IP: {client_ip}, 实际IP: {actual_ip if 'actual_ip' in locals() else 'N/A'}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"从服务器注册失败: {str(e)}"
        )

# ==================== 从服务器心跳 ====================

@router.post("/heartbeat", response_model=Dict[str, Any])
async def receive_lightweight_heartbeat(
    request: Dict[str, Any],
    client_request: Request,
    db: AsyncSession = Depends(get_db)
):
    """接收从服务器轻量级心跳"""
    try:
        # 获取客户端IP
        client_ip = client_request.client.host

        # 验证心跳类型
        if request.get('heartbeat_type') != 'lightweight':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的心跳类型"
            )

        # 智能查找从服务器（多种匹配方式）
        slave_server = None

        # 1. 优先基于服务器名称查找（最准确）
        if 'server_name' in request:
            stmt = select(SlaveServer).where(SlaveServer.name == request['server_name'])
            result = await db.execute(stmt)
            slave_server = result.scalar_one_or_none()

        # 2. 如果名称查找失败，基于客户端IP查找
        if not slave_server:
            stmt = select(SlaveServer).where(SlaveServer.ip_address == client_ip)
            result = await db.execute(stmt)
            slave_server = result.scalar_one_or_none()

        # 3. 如果客户端IP查找失败，尝试基于IP地址前缀查找（处理Docker网络IP变化）
        if not slave_server:
            stmt = select(SlaveServer).where(SlaveServer.ip_address.like(f"{client_ip.split('.')[0]}.%"))
            result = await db.execute(stmt)
            servers = result.scalars().all()
            if len(servers) == 1:
                slave_server = servers[0]
                # 更新IP地址
                slave_server.ip_address = client_ip
                logger.info(f"从服务器IP地址已更新: {slave_server.name} -> {client_ip}")

        if not slave_server:
            logger.warning(f"心跳请求的从服务器未找到 - 客户端IP: {client_ip}, 服务器名称: {request.get('server_name', 'N/A')}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器未注册"
            )

        # 更新心跳时间和状态
        current_time = datetime.now()
        old_status = slave_server.status
        slave_server.last_heartbeat = current_time
        slave_server.status = request.get('status', 'online')
        slave_server.updated_at = current_time

        # 更新扩展的心跳信息
        if 'server_name' in request:
            slave_server.name = request['server_name']
        if 'web_port' in request:
            slave_server.port = request['web_port']
        if 'virtualhere_port' in request:
            # 如果数据库模型有vh_port字段，则更新
            if hasattr(slave_server, 'vh_port'):
                slave_server.vh_port = request['virtualhere_port']
        if 'server_version' in request:
            # 如果数据库模型有version字段，则更新
            if hasattr(slave_server, 'version'):
                slave_server.version = request['server_version']

        # 更新VirtualHere状态（可以存储在描述或其他字段中）
        if 'virtualhere_status' in request:
            vh_status = request['virtualhere_status']
            # 更新描述字段以包含VH状态信息
            if slave_server.description:
                # 移除旧的VH状态信息
                desc_parts = [part for part in slave_server.description.split(' | ') if not part.startswith('VH:')]
                desc_parts.append(f"VH:{vh_status}")
                slave_server.description = ' | '.join(desc_parts)
            else:
                slave_server.description = f"VH:{vh_status}"

        await db.commit()

        # 检查是否需要配置验证
        require_verification = False
        server_config = {}

        # 数据一致性验证机制
        current_device_count = request.get('device_count_summary', 0)
        current_hub_count = request.get('hub_count', 0)
        current_total_ports = request.get('total_ports', 0)

        # 检查设备数量一致性
        if slave_server.device_count != current_device_count:
            require_verification = True
            logger.warning(f"设备数量不一致 - 主服务器: {slave_server.device_count}, 从服务器: {current_device_count}")

        # 使用拓扑同步管理器检查一致性
        from core.topology_sync_manager import get_topology_sync_manager

        topology_manager = get_topology_sync_manager()
        topology_validation = await topology_manager.validate_topology_consistency(
            db, slave_server.id, {
                'hub_count': current_hub_count,
                'total_ports': current_total_ports,
                'device_count': current_device_count,
                'occupied_ports': request.get('occupied_ports', 0),
                'free_ports': request.get('free_ports', 0),
                'topology_hash': request.get('topology_hash')
            }
        )

        if topology_validation["status"] in ["inconsistent", "force_sync"]:
            require_verification = True
            logger.warning(f"拓扑信息验证: {topology_validation['message']}")
            if topology_validation.get("inconsistencies"):
                logger.warning(f"具体不一致项: {topology_validation['inconsistencies']}")
        elif topology_validation["status"] == "error":
            logger.error(f"拓扑验证失败: {topology_validation['message']}")

        if require_verification:
            # 更新配置核对时间
            slave_server.last_config_check = current_time
            server_config = {
                'device_count': slave_server.device_count,
                'topology': {
                    'hub_count': getattr(slave_server, 'hub_count', 0),
                    'total_ports': getattr(slave_server, 'total_ports', 0)
                },
                'sync_required': True,
                'sync_reason': 'data_inconsistency'
            }

        # 如果状态发生变化，通过WebSocket推送实时更新
        if old_status != slave_server.status:
            try:
                from core.websocket_manager import get_connection_manager
                connection_manager = await get_connection_manager()
                await connection_manager.broadcast_to_subscribers('slave_status', {
                    'type': 'slave_status_change',
                    'server_id': slave_server.id,
                    'server_name': slave_server.name,
                    'old_status': old_status,
                    'new_status': slave_server.status,
                    'timestamp': current_time.isoformat()
                })
                logger.info(f"从服务器状态变化推送: {slave_server.name} {old_status} -> {slave_server.status}")
            except Exception as e:
                logger.warning(f"WebSocket状态推送失败: {e}")

        response_data = {
            'status': 'success',
            'timestamp': datetime.now().isoformat(),
            'require_config_verification': require_verification
        }

        if require_verification:
            response_data['server_config'] = server_config

        logger.info(f"轻量级心跳接收成功 - 服务器: {slave_server.name}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"轻量级心跳处理失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"心跳处理失败: {str(e)}"
        )


@router.post("/device-event", response_model=Dict[str, Any])
async def receive_device_event(
    request: Dict[str, Any],
    client_request: Request,
    db: AsyncSession = Depends(get_db)
):
    """接收从服务器设备变化事件"""
    try:
        # 获取客户端IP
        client_ip = client_request.client.host

        # 查找从服务器
        stmt = select(SlaveServer).where(SlaveServer.ip_address == client_ip)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()

        if not slave_server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器未注册"
            )

        # 处理设备事件
        event_type = request.get('event_type')
        device_summary = request.get('device_summary', {})

        logger.info(f"收到设备事件: {event_type} from {slave_server.name}")

        # 更新从服务器的设备数量（如果提供）
        if 'total_count' in device_summary:
            old_count = slave_server.device_count or 0
            slave_server.device_count = device_summary['total_count']
            slave_server.updated_at = datetime.now()
            await db.commit()

            # 触发WebSocket推送到前端
            from core.websocket_manager import websocket_manager
            await websocket_manager.emit_event({
                "event_type": "device_count_change",
                "server_id": slave_server.id,
                "server_name": slave_server.name,
                "old_count": old_count,
                "new_count": device_summary['total_count'],
                "change_type": device_summary.get('change_type', 'unknown'),
                "timestamp": request.get('timestamp')
            })

        return {
            "success": True,
            "message": "设备事件处理成功",
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设备事件处理失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"设备事件处理失败: {str(e)}"
        )


@router.post("/data-sync", response_model=Dict[str, Any])
async def receive_data_sync(
    request: Dict[str, Any],
    client_request: Request,
    db: AsyncSession = Depends(get_db)
):
    """接收从服务器数据同步"""
    try:
        # 获取客户端IP
        client_ip = client_request.client.host
        
        # 验证同步类型
        if request.get('sync_type') != 'full_data':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的同步类型"
            )

        # 查找从服务器
        stmt = select(SlaveServer).where(SlaveServer.ip_address == client_ip)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()

        if not slave_server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器未注册"
            )

        logger.info(f"数据同步请求 - 服务器: {slave_server.name}, IP: {client_ip}")

        # 更新从服务器状态
        current_time = datetime.now()
        slave_server.last_heartbeat = current_time
        slave_server.last_config_check = current_time  # 数据同步时也更新配置核对时间
        slave_server.updated_at = current_time

        # 处理完整数据同步
        device_sync_result = None
        device_details = request.get('device_details', [])
        usb_topology = request.get('usb_topology', {})

        if device_details or usb_topology:
            try:
                device_count = len(device_details)
                logger.info(f"收到完整数据同步，设备数量: {device_count}")

                # 统计设备类型
                device_types = {}
                auto_bind_count = 0

                for device in device_details:
                    device_type = device.get('device_type', 'unknown')
                    device_types[device_type] = device_types.get(device_type, 0) + 1

                    if device.get('auto_bind_eligible', False):
                        auto_bind_count += 1

                # 更新从服务器的设备数量和拓扑信息
                slave_server.device_count = device_count

                # 更新USB拓扑信息（需要先检查字段是否存在）
                if usb_topology:
                    if hasattr(slave_server, 'hub_count'):
                        slave_server.hub_count = usb_topology.get('hub_count', 0)
                    if hasattr(slave_server, 'total_ports'):
                        slave_server.total_ports = usb_topology.get('total_ports', 0)
                    if hasattr(slave_server, 'occupied_ports'):
                        slave_server.occupied_ports = usb_topology.get('occupied_ports', 0)
                    if hasattr(slave_server, 'free_ports'):
                        slave_server.free_ports = usb_topology.get('free_ports', 0)

                slave_server.updated_at = datetime.now()

                logger.info(f"USB拓扑更新: Hub={usb_topology.get('hub_count', 0)}, 总端口={usb_topology.get('total_ports', 0)}, 占用={usb_topology.get('occupied_ports', 0)}, 空闲={usb_topology.get('free_ports', 0)}")
                logger.info(f"设备类型统计: {device_types}")
                logger.info(f"可自动绑定设备数量: {auto_bind_count}")

                # 自动注册设备到Device表
                created_devices, updated_devices, skipped_devices = await auto_register_devices(
                    db, slave_server, device_details
                )

                device_sync_result = {
                    'processed': device_count,
                    'created': created_devices,
                    'updated': updated_devices,
                    'skipped': skipped_devices,
                    'errors': 0,
                    'device_types': device_types,
                    'auto_bind_eligible_count': auto_bind_count,
                    'topology_updated': bool(usb_topology)
                }

                await db.commit()
                logger.info(f"设备同步成功，设备数量: {device_count}")
            except Exception as e:
                await db.rollback()
                logger.error(f"设备同步失败: {e}")
                device_sync_result = {
                    'processed': 0,
                    'created': 0,
                    'updated': 0,
                    'skipped': 0,
                    'errors': 1,
                    'error_message': str(e)
                }

        logger.info(f"数据同步完成: {slave_server.name} ({client_ip})")

        response_data = {
            "status": "success",
            "message": "数据同步成功",
            "server_time": datetime.now().isoformat(),
            "sync_result": device_sync_result or {
                'processed': 0,
                'created': 0,
                'updated': 0,
                'skipped': 0,
                'errors': 0
            }
        }

        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"数据同步处理失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"数据同步处理失败: {str(e)}"
        )

# ==================== 设备同步功能 ====================

async def sync_slave_devices(db: AsyncSession, slave_server: SlaveServer, device_details: List[Dict]) -> Dict[str, Any]:
    """同步从服务器设备信息（增强版本，集成USB.IDS识别）"""
    try:
        device_count = len(device_details)
        logger.info(f"收到从服务器 {slave_server.name} 的设备信息，共 {device_count} 个设备")

        # 获取USB.IDS管理器
        usb_ids_manager = await get_usb_ids_manager()

        # 增强设备信息并统计
        enhanced_devices = []
        device_types = {}
        auto_bind_count = 0

        for device in device_details:
            # 使用USB.IDS增强设备信息
            enhanced_device = await enhance_device_with_usb_ids(device, usb_ids_manager)
            enhanced_devices.append(enhanced_device)

            device_type = enhanced_device.get('device_type', 'unknown')
            device_types[device_type] = device_types.get(device_type, 0) + 1

            if enhanced_device.get('auto_bind_eligible', False):
                auto_bind_count += 1

        logger.info(f"设备类型统计: {device_types}")
        logger.info(f"可自动绑定设备数量: {auto_bind_count}")

        # 调用设备自动注册，存储增强后的设备信息
        created_count, updated_count, skipped_count = await auto_register_devices(db, slave_server, enhanced_devices)

        return {
            'processed': device_count,
            'created': created_count,
            'updated': updated_count,
            'skipped': skipped_count,
            'errors': 0,
            'device_types': device_types,
            'auto_bind_eligible_count': auto_bind_count
        }

    except Exception as e:
        logger.error(f"设备同步过程失败: {e}")
        return {
            'processed': 0,
            'created': 0,
            'updated': 0,
            'skipped': 0,
            'errors': 1,
            'error_message': str(e)
        }

async def enhance_device_with_usb_ids(device: Dict[str, Any], usb_ids_manager) -> Dict[str, Any]:
    """使用USB.IDS数据库增强设备信息"""
    try:
        enhanced_device = device.copy()

        vendor_id = device.get('vendor_id')
        product_id = device.get('product_id')

        if vendor_id is not None and product_id is not None:
            # 确保VID和PID是整数
            if isinstance(vendor_id, str):
                vendor_id = int(vendor_id.replace('0x', ''), 16) if vendor_id.startswith('0x') else int(vendor_id, 16)
            if isinstance(product_id, str):
                product_id = int(product_id.replace('0x', ''), 16) if product_id.startswith('0x') else int(product_id, 16)

            # 查询USB.IDS信息
            device_info = await usb_ids_manager.get_device_info(vendor_id, product_id)

            # 增强设备信息
            enhanced_device.update({
                'usb_ids_vendor_name': device_info.get('vendor_name'),
                'usb_ids_device_name': device_info.get('device_name'),
                'usb_ids_full_name': device_info.get('full_name'),
                'identification_source': 'usb_ids' if device_info.get('vendor_name') else 'local_rules'
            })

            # 如果原始设备类型是unknown，尝试使用USB.IDS信息改进识别
            if device.get('device_type') == 'unknown' and device_info.get('vendor_name'):
                # 基于厂商名称进行简单的设备类型推断
                vendor_name_lower = device_info.get('vendor_name', '').lower()
                if any(keyword in vendor_name_lower for keyword in ['senseshield', 'rockey', 'hasp', 'sentinel']):
                    enhanced_device['device_type'] = 'encryption_lock'
                    enhanced_device['identification_source'] = 'usb_ids_enhanced'
                    logger.info(f"USB.IDS增强识别: VID=0x{vendor_id:04X} 识别为加密锁 ({device_info.get('vendor_name')})")

        return enhanced_device

    except Exception as e:
        logger.warning(f"USB.IDS设备信息增强失败: {e}")
        return device

async def check_and_generate_config_update(db: AsyncSession, slave_server: SlaveServer) -> Optional[Dict[str, Any]]:
    """检查并生成配置更新"""
    try:
        # 获取从服务器的设备配置
        stmt = select(Device).where(Device.slave_server_id == slave_server.id)
        result = await db.execute(stmt)
        devices = result.scalars().all()

        # 生成设备配置表
        device_configs = []
        for device in devices:
            device_config = {
                'hardware_signature': device.hardware_signature,
                'device_id': device.device_id,
                'device_name': device.device_name,
                'device_type': device.device_type,
                'custom_name': device.custom_name or '',
                'status': device.status,
                'port_location_code': device.port_location_code,
                'physical_port': device.physical_port,
                'auto_bind': True,  # 默认启用自动绑定
                'updated_at': device.updated_at.isoformat() if device.updated_at else None
            }
            device_configs.append(device_config)

        # 配置版本管理：只有在配置真正发生变化时才递增
        current_config_version = slave_server.config_version or 1

        # 生成当前配置的哈希值来检测变化
        import hashlib
        config_hash = hashlib.md5(
            json.dumps(device_configs, sort_keys=True).encode()
        ).hexdigest()

        # 检查配置是否发生变化
        if slave_server.config_hash != config_hash:
            # 配置发生变化，递增版本号
            config_version = current_config_version + 1
            slave_server.config_hash = config_hash
            logger.info(f"从服务器 {slave_server.name} 配置发生变化，版本更新: {current_config_version} -> {config_version}")
        else:
            # 配置无变化，保持当前版本
            config_version = current_config_version

        config_update = {
            'config_version': config_version,
            'device_configs': device_configs,
            'server_config': {
                'server_name': slave_server.name,
                'auto_bind_enabled': True,
                'scan_interval': 30,
                'heartbeat_interval': 30
            },
            'update_type': 'incremental',
            'timestamp': datetime.now().isoformat()
        }

        logger.info(f"生成配置更新 - 服务器: {slave_server.name}, 版本: {config_version}, 设备数: {len(device_configs)}")
        return config_update

    except Exception as e:
        logger.error(f"生成配置更新失败: {e}")
        return None

# ==================== 从服务器列表 ====================

@router.get("/list", response_model=Dict[str, Any])
async def get_slave_servers(
    db: AsyncSession = Depends(get_db)
):
    """获取从服务器列表"""
    try:
        from datetime import datetime, timedelta

        # 查询所有从服务器
        stmt = select(SlaveServer).order_by(SlaveServer.created_at.desc())
        result = await db.execute(stmt)
        slave_servers = result.scalars().all()

        # 心跳超时阈值（15秒）- 生产环境标准
        heartbeat_timeout = timedelta(seconds=15)
        current_time = datetime.now()

        # 为每个从服务器查询设备数量并计算实时状态
        response_list = []
        for server in slave_servers:
            # 基于心跳时间计算实时在线状态
            real_time_status = "offline"  # 默认离线
            if server.last_heartbeat:
                time_since_heartbeat = current_time - server.last_heartbeat
                if time_since_heartbeat <= heartbeat_timeout:
                    real_time_status = "online"
                else:
                    real_time_status = "offline"

            # 查询该从服务器的设备数量
            device_stmt = select(Device).where(Device.slave_server_id == server.id)
            device_result = await db.execute(device_stmt)
            devices = device_result.scalars().all()

            # 如果从服务器离线，所有设备也应显示为离线
            device_count = len(devices)
            if real_time_status == "offline":
                # 更新数据库中的从服务器状态
                server.status = "offline"
                await db.commit()

            response_list.append(SlaveServerResponse(
                id=server.id,
                server_id=server.server_id,
                name=server.name,
                ip_address=server.ip_address,
                port=server.port,
                vh_port=server.vh_port,  # 使用数据库中的VH端口
                status=real_time_status,  # 使用实时计算的状态
                last_seen=server.last_heartbeat.strftime("%Y/%m/%d %H:%M:%S") if server.last_heartbeat else None,  # 使用心跳时间作为最后见到时间
                description=server.description,
                location="未设置位置",  # 暂时硬编码，后续可添加到模型中
                created_at=server.created_at.isoformat() if server.created_at else None,
                device_count=device_count,
                hardware_uuid=server.hardware_uuid,
                config_version=server.config_version,
                hardware_info=server.hardware_info
            ))

        return {
            "success": True,
            "data": response_list,
            "message": f"成功获取 {len(response_list)} 个从服务器"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取从服务器列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取从服务器列表失败: {str(e)}"
        )

# ==================== 从服务器详情 ====================
# 注意：详情API已在后面定义，此处删除重复定义

# ==================== 从服务器名称更新 ====================

@router.put("/{server_id}/name", response_model=Dict[str, Any])
async def update_slave_server_name(
    server_id: int,
    request: Dict[str, str],
    db: AsyncSession = Depends(get_db)
):
    """更新从服务器名称"""
    try:
        new_name = request.get("name", "").strip()
        if not new_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="服务器名称不能为空"
            )

        # 查询从服务器
        stmt = select(SlaveServer).where(SlaveServer.id == server_id)
        result = await db.execute(stmt)
        server = result.scalar_one_or_none()

        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器不存在"
            )

        # 检查名称是否重复
        name_check_stmt = select(SlaveServer).where(
            SlaveServer.name == new_name,
            SlaveServer.id != server_id
        )
        name_check_result = await db.execute(name_check_stmt)
        existing_server = name_check_result.scalar_one_or_none()

        if existing_server:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"服务器名称 '{new_name}' 已被使用"
            )

        # 更新名称
        old_name = server.name
        server.name = new_name
        server.updated_at = datetime.now()

        await db.commit()
        await db.refresh(server)

        logger.info(f"从服务器名称已更新: {old_name} -> {new_name}")

        return {
            "success": True,
            "message": f"服务器名称已更新为: {new_name}",
            "data": {
                "id": server.id,
                "old_name": old_name,
                "new_name": new_name
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新从服务器名称失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新从服务器名称失败: {str(e)}"
        )

# ==================== 从服务器删除 ====================

async def deep_delete_slave_server(db: AsyncSession, server_id: int) -> Dict[str, Any]:
    """深度删除从服务器及其所有关联数据"""
    deletion_stats = {
        "server_id": server_id,
        "server_name": "",
        "devices_deleted": 0,
        "device_group_assignments_removed": 0,
        "empty_groups_cleaned": 0,
        "occupation_records_updated": 0,
        "affected_groups": [],
        "audit_logs_preserved": True
    }

    try:
        # 1. 查找从服务器
        stmt = select(SlaveServer).where(SlaveServer.id == server_id)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()

        if not slave_server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器不存在"
            )

        deletion_stats["server_name"] = slave_server.name
        logger.info(f"开始深度删除从服务器: {slave_server.name} (ID: {server_id})")

        # 2. 查找所有关联的设备
        devices_stmt = select(Device).where(Device.slave_server_id == server_id)
        devices_result = await db.execute(devices_stmt)
        devices = devices_result.scalars().all()
        device_ids = [device.id for device in devices]

        if device_ids:
            logger.info(f"发现 {len(device_ids)} 个关联设备，开始深度清理...")

            # 3. 查找受影响的设备分组
            from models import DeviceGroupMember, DeviceGroup
            affected_groups_stmt = select(DeviceGroup.id, DeviceGroup.name).join(
                DeviceGroupMember, DeviceGroup.id == DeviceGroupMember.group_id
            ).where(DeviceGroupMember.device_id.in_(device_ids)).distinct()

            affected_groups_result = await db.execute(affected_groups_stmt)
            affected_groups = affected_groups_result.fetchall()
            deletion_stats["affected_groups"] = [{"id": g.id, "name": g.name} for g in affected_groups]

            logger.info(f"受影响的设备分组: {len(affected_groups)} 个")

            # 4. 查找直接使用这些设备的用户
            direct_users_stmt = select(User.id, User.username, Device.device_name).join(
                Device, or_(
                    User.id == Device.current_user_id,
                    User.id == Device.last_user_id
                )
            ).where(Device.id.in_(device_ids)).distinct()

            direct_users_result = await db.execute(direct_users_stmt)
            direct_users = direct_users_result.fetchall()
            deletion_stats["affected_users"] = [{"id": u.id, "username": u.username, "device": u.device_name} for u in direct_users]

            logger.info(f"直接使用这些设备的用户: {len(direct_users)} 个")

            # 5. 清除设备的直接用户关联
            device_user_clear_stmt = update(Device).where(
                Device.id.in_(device_ids)
            ).values(
                current_user_id=None,
                current_user_contact=None,
                last_user_contact=None,
                connected_at=None
            )
            await db.execute(device_user_clear_stmt)
            logger.info(f"清除了 {len(device_ids)} 个设备的直接用户关联")

            # 6. 删除设备分组关联 (device_group_members)
            group_members_stmt = delete(DeviceGroupMember).where(
                DeviceGroupMember.device_id.in_(device_ids)
            )
            group_result = await db.execute(group_members_stmt)
            deletion_stats["device_group_assignments_removed"] = group_result.rowcount
            logger.info(f"从设备分组中移除了 {deletion_stats['device_group_assignments_removed']} 个设备关联")

            # 7. 检查并清理空的设备分组
            for group in affected_groups:
                # 检查分组是否还有其他设备
                remaining_devices_stmt = select(func.count(DeviceGroupMember.id)).where(
                    DeviceGroupMember.group_id == group.id
                )
                remaining_count_result = await db.execute(remaining_devices_stmt)
                remaining_count = remaining_count_result.scalar()

                if remaining_count == 0:
                    # 分组已空，删除相关的用户权限
                    from models import UserDeviceGroupPermission
                    permissions_stmt = delete(UserDeviceGroupPermission).where(
                        UserDeviceGroupPermission.device_group_id == group.id
                    )
                    await db.execute(permissions_stmt)

                    # 标记分组为非活跃状态（保留记录但不可用）
                    group_update_stmt = update(DeviceGroup).where(
                        DeviceGroup.id == group.id
                    ).values(is_active=False)
                    await db.execute(group_update_stmt)

                    deletion_stats["empty_groups_cleaned"] += 1
                    logger.info(f"设备分组 '{group.name}' 已变空，已清理相关权限并标记为非活跃")

            # 8. 更新设备占用记录状态（保留历史记录，但标记为已删除）
            try:
                from models import DeviceOccupationRecord
                occupation_stmt = update(DeviceOccupationRecord).where(
                    and_(
                        DeviceOccupationRecord.device_id.in_(device_ids),
                        DeviceOccupationRecord.status.in_(["active", "occupied", "pending"])
                    )
                ).values(
                    status="device_deleted",
                    end_time=func.now(),
                    release_reason="server_deleted"
                )
                occupation_result = await db.execute(occupation_stmt)
                deletion_stats["occupation_records_updated"] = occupation_result.rowcount
                logger.info(f"更新了 {deletion_stats['occupation_records_updated']} 个设备占用记录")
            except Exception as e:
                logger.warning(f"更新设备占用记录时出错: {e}")

            # 9. 删除设备记录
            for device in devices:
                await db.delete(device)
            deletion_stats["devices_deleted"] = len(devices)
            logger.info(f"删除了 {len(devices)} 个设备记录")

        # 10. 删除从服务器记录
        await db.delete(slave_server)

        # 11. 创建审计日志（保留删除记录）
        try:
            from models import AuditLog
            audit_log = AuditLog(
                user_id=None,  # 系统操作
                action="slave_server_deleted",
                target_type="slave_server",
                target_id=server_id,
                details={
                    "server_name": slave_server.name,
                    "deletion_stats": deletion_stats,
                    "deletion_time": datetime.now().isoformat()
                },
                ip_address="system",
                user_agent="deep_deletion_system"
            )
            db.add(audit_log)
            logger.info("已创建删除审计日志")
        except Exception as e:
            logger.warning(f"创建审计日志失败: {e}")
            deletion_stats["audit_logs_preserved"] = False

        return deletion_stats

    except Exception as e:
        logger.error(f"深度删除从服务器失败: {e}")
        raise

async def require_super_admin(current_user: User = Depends(get_current_user)) -> User:
    """要求超级管理员权限"""
    if not (current_user.is_superuser or
            current_user.role_name in ['全域管理员', '超级管理员']):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有全域管理员和超级管理员可以删除从服务器"
        )
    return current_user

@router.delete("/{server_id}", response_model=Dict[str, Any])
async def delete_slave_server(
    server_id: int,
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """深度删除从服务器及其所有关联数据"""
    try:
        # 执行深度删除
        deletion_stats = await deep_delete_slave_server(db, server_id)

        # 提交事务
        await db.commit()

        logger.info(f"从服务器深度删除成功: {deletion_stats['server_name']} (ID: {server_id})")
        return {
            "status": "success",
            "message": "从服务器及所有关联数据删除成功",
            "deletion_stats": deletion_stats
        }

    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除从服务器失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除从服务器失败: {str(e)}"
        )

@router.post("/batch-delete", response_model=Dict[str, Any])
async def batch_delete_slave_servers(
    server_ids: List[int],
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """批量删除从服务器及其所有关联数据"""
    if not server_ids:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请提供要删除的服务器ID列表"
        )

    if len(server_ids) > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="单次批量删除不能超过100台服务器"
        )

    try:
        batch_results = {
            "total_requested": len(server_ids),
            "successful_deletions": 0,
            "failed_deletions": 0,
            "deletion_details": [],
            "overall_stats": {
                "total_devices_deleted": 0,
                "total_group_assignments_removed": 0,
                "total_empty_groups_cleaned": 0,
                "total_occupation_records_updated": 0
            }
        }

        logger.info(f"开始批量删除 {len(server_ids)} 台从服务器")

        # 逐个删除服务器（确保事务安全）
        for server_id in server_ids:
            try:
                # 为每个服务器创建独立的事务
                deletion_stats = await deep_delete_slave_server(db, server_id)

                batch_results["successful_deletions"] += 1
                batch_results["deletion_details"].append({
                    "server_id": server_id,
                    "status": "success",
                    "stats": deletion_stats
                })

                # 累计统计
                batch_results["overall_stats"]["total_devices_deleted"] += deletion_stats["devices_deleted"]
                batch_results["overall_stats"]["total_group_assignments_removed"] += deletion_stats["device_group_assignments_removed"]
                batch_results["overall_stats"]["total_empty_groups_cleaned"] += deletion_stats["empty_groups_cleaned"]
                batch_results["overall_stats"]["total_occupation_records_updated"] += deletion_stats["occupation_records_updated"]

                logger.info(f"成功删除服务器 {server_id}: {deletion_stats['server_name']}")

            except Exception as e:
                batch_results["failed_deletions"] += 1
                batch_results["deletion_details"].append({
                    "server_id": server_id,
                    "status": "failed",
                    "error": str(e)
                })
                logger.error(f"删除服务器 {server_id} 失败: {e}")

        # 提交所有成功的删除操作
        await db.commit()

        # 生成批量删除审计日志
        try:
            from models import AuditLog
            audit_log = AuditLog(
                user_id=current_user.id,
                action="batch_slave_server_deletion",
                target_type="slave_server",
                target_id=len(server_ids),
                details={
                    "requested_servers": server_ids,
                    "batch_results": batch_results,
                    "deletion_time": datetime.now().isoformat(),
                    "operator": current_user.username
                },
                ip_address="system",
                user_agent="batch_deletion_system"
            )
            db.add(audit_log)
            await db.commit()
        except Exception as e:
            logger.warning(f"创建批量删除审计日志失败: {e}")

        logger.info(f"批量删除完成: {batch_results['successful_deletions']}/{len(server_ids)} 成功")

        return {
            "status": "completed",
            "message": f"批量删除完成: {batch_results['successful_deletions']}/{len(server_ids)} 成功",
            "batch_results": batch_results
        }

    except Exception as e:
        await db.rollback()
        logger.error(f"批量删除失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量删除失败: {str(e)}"
        )

@router.post("/reset-sequence", summary="重置从服务器ID序列")
async def reset_sequence(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    重置从服务器ID序列

    用于清理测试数据后重置ID序列，确保新注册的服务器从1开始编号
    """
    try:
        # 检查权限
        if not current_user.get("is_admin", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以重置序列"
            )

        success = await reset_slave_server_sequence(db)

        if success:
            return {
                "success": True,
                "message": "从服务器ID序列重置成功"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="序列重置失败"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置序列API失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置序列失败: {str(e)}"
        )

# ==================== 从服务器统计 ====================

@router.get("/servers/stats", response_model=Dict[str, Any])
async def get_slave_server_stats(
    db: AsyncSession = Depends(get_db)
):
    """获取从服务器统计信息"""
    try:
        # 查询所有从服务器
        stmt = select(SlaveServer)
        result = await db.execute(stmt)
        slave_servers = result.scalars().all()

        # 统计从服务器状态
        total_servers = len(slave_servers)
        online_servers = len([s for s in slave_servers if s.status == "online"])
        offline_servers = total_servers - online_servers

        # 统计设备信息（从SlaveServer表的新字段获取）
        total_devices = sum(getattr(server, 'device_count', 0) for server in slave_servers)
        total_hubs = sum(getattr(server, 'hub_count', 0) for server in slave_servers)
        total_ports = sum(getattr(server, 'total_ports', 0) for server in slave_servers)
        occupied_ports = sum(getattr(server, 'occupied_ports', 0) for server in slave_servers)
        free_ports = sum(getattr(server, 'free_ports', 0) for server in slave_servers)

        # 构建统计响应
        stats_data = {
            "total_servers": total_servers,
            "online_servers": online_servers,
            "offline_servers": offline_servers,
            "total_devices": total_devices,
            "total_hubs": total_hubs,
            "total_ports": total_ports,
            "occupied_ports": occupied_ports,
            "free_ports": free_ports,
            "server_details": [
                {
                    "id": server.id,
                    "name": server.name,
                    "status": server.status,
                    "device_count": getattr(server, 'device_count', 0),
                    "hub_count": getattr(server, 'hub_count', 0),
                    "total_ports": getattr(server, 'total_ports', 0),
                    "occupied_ports": getattr(server, 'occupied_ports', 0),
                    "free_ports": getattr(server, 'free_ports', 0)
                }
                for server in slave_servers
            ]
        }

        logger.info(f"获取从服务器统计成功: {total_servers}台服务器, {total_devices}个设备")
        return {
            "success": True,
            "data": stats_data,
            "message": "获取统计信息成功"
        }

    except Exception as e:
        logger.error(f"获取从服务器统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )

# ==================== 从服务器详情查询 ====================
# 注意：这个路由必须放在/stats路由之后，避免路径冲突

@router.get("/{server_id}", response_model=Dict[str, Any])
async def get_slave_server_detail(
    server_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取指定从服务器的详细信息"""
    try:
        # 查询从服务器信息
        stmt = select(SlaveServer).where(SlaveServer.id == server_id)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()

        if not slave_server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"从服务器 {server_id} 不存在"
            )

        # 检查从服务器实时在线状态
        is_online = await check_slave_server_online(slave_server)
        real_time_status = "online" if is_online else "offline"

        # 计算离线时长
        offline_duration = None
        if not is_online and slave_server.last_heartbeat:
            offline_time = datetime.now() - slave_server.last_heartbeat
            offline_minutes = int(offline_time.total_seconds() / 60)
            if offline_minutes < 60:
                offline_duration = f"{offline_minutes}分钟"
            else:
                offline_hours = offline_minutes // 60
                offline_duration = f"{offline_hours}小时{offline_minutes % 60}分钟"

        # 查询该从服务器的设备数量
        device_stmt = select(Device).where(Device.slave_server_id == server_id)
        device_result = await db.execute(device_stmt)
        devices = device_result.scalars().all()
        device_count = len(devices)

        # 构建响应数据
        response_data = {
            "id": slave_server.id,
            "server_id": slave_server.server_id,
            "name": slave_server.name,
            "ip_address": slave_server.ip_address,
            "port": slave_server.port,
            "vh_port": slave_server.vh_port,  # 使用数据库中的VH端口
            "status": real_time_status,  # 使用实时状态
            "last_seen": slave_server.last_heartbeat.isoformat() if slave_server.last_heartbeat else None,  # 使用心跳时间
            "description": slave_server.description,
            "location": None,  # 暂时设为None，后续可以添加到模型中
            "created_at": slave_server.created_at.isoformat() if slave_server.created_at else None,
            "device_count": device_count,
            "hardware_uuid": slave_server.hardware_uuid,
            "config_version": slave_server.config_version,
            "hardware_info": slave_server.hardware_info,
            "offline_duration": offline_duration,  # 添加离线时长信息
            "is_online": is_online  # 添加在线状态标识
        }

        logger.info(f"获取从服务器 {server_id} 详情成功")
        return {
            "success": True,
            "data": response_data,
            "message": "获取从服务器详情成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取从服务器 {server_id} 详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取从服务器详情失败: {str(e)}"
        )

# ==================== 从服务器设备查询 ====================

@router.get("/{server_id}/devices", response_model=Dict[str, Any])
async def get_slave_server_devices(
    server_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取指定从服务器的设备列表"""
    try:
        
        # 检查从服务器是否存在
        stmt = select(SlaveServer).where(SlaveServer.id == server_id)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()
        
        if not slave_server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器不存在"
            )
        
        # 查询设备列表
        stmt = select(Device).where(Device.slave_server_id == server_id)
        result = await db.execute(stmt)
        devices = result.scalars().all()
        
        device_list = [
            DeviceResponse(
                id=device.id,
                device_name=device.device_name,
                device_type=device.device_type,
                vendor_id=device.vendor_id,
                product_id=device.product_id,
                serial_number=device.serial_number,
                status=device.status,
                slave_server_id=device.slave_server_id,
                created_at=device.created_at
            )
            for device in devices
        ]

        return {
            "success": True,
            "data": device_list,
            "message": f"成功获取 {len(device_list)} 个设备"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取设备列表失败: {str(e)}"
        )

# ==================== 从服务器控制 ====================

@router.post("/{server_id}/control", response_model=Dict[str, Any])
async def control_slave_server(
    server_id: int,
    request: SlaveServerControlRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """远程控制从服务器"""
    try:
        # 权限检查：登录用户即可控制
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="需要登录"
            )
        
        # 检查从服务器是否存在
        stmt = select(SlaveServer).where(SlaveServer.id == server_id)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()
        
        if not slave_server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器不存在"
            )
        
        # 实现实际的远程控制逻辑
        control_result = await execute_slave_control(slave_server, request.action, request.parameters)

        logger.info(f"用户 {current_user.username} 对从服务器 {slave_server.name} 执行了 {request.action} 操作")

        return {
            "status": "success" if control_result["success"] else "error",
            "message": control_result["message"],
            "server_id": server_id,
            "action": request.action,
            "data": control_result.get("data", {})
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"控制从服务器失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"控制从服务器失败: {str(e)}"
        )



# ==================== 从服务器控制辅助函数 ====================

async def execute_slave_control(slave_server, action: str, parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """执行从服务器控制操作"""
    try:
        # 构建从服务器控制API URL
        control_url = f"http://{slave_server.ip_address}:{slave_server.port}/api/control"

        # 准备控制请求数据
        control_data = {
            "action": action,
            "parameters": parameters or {}
        }

        # 发送控制请求到从服务器
        timeout = aiohttp.ClientTimeout(total=10)  # 10秒超时
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.post(control_url, json=control_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "success": True,
                            "message": f"控制操作 '{action}' 执行成功",
                            "data": result
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "message": f"从服务器返回错误 {response.status}: {error_text}"
                        }
            except aiohttp.ClientConnectorError:
                return {
                    "success": False,
                    "message": f"无法连接到从服务器 {slave_server.ip_address}:{slave_server.port}"
                }
            except asyncio.TimeoutError:
                return {
                    "success": False,
                    "message": "控制操作超时，从服务器可能无响应"
                }
    except Exception as e:
        logger.error(f"执行从服务器控制失败: {e}")
        return {
            "success": False,
            "message": f"控制操作失败: {str(e)}"
        }

# ==================== 设备配置同步相关模型 ====================

class DeviceIdentityInfo(BaseModel):
    """设备身份信息"""
    hardware_signature: str = Field(..., description="设备硬件签名")
    physical_port: str = Field(..., description="设备物理端口")
    vendor_id: str = Field(..., description="厂商ID")
    product_id: str = Field(..., description="产品ID")
    serial_number: Optional[str] = Field(None, description="序列号")
    description: str = Field(..., description="设备描述")
    device_path: Optional[str] = Field(None, description="设备路径")
    custom_name: Optional[str] = Field(None, description="自定义名称")
    notes: Optional[str] = Field(None, description="设备备注")
    config_data: Optional[Dict[str, Any]] = Field(None, description="设备配置数据")

class DeviceSyncRequest(BaseModel):
    """设备同步请求"""
    hardware_uuid: str = Field(..., description="从服务器硬件UUID")
    devices: List[DeviceIdentityInfo] = Field(..., description="设备列表")
    sync_timestamp: str = Field(..., description="同步时间戳")

class DeviceConfigResponse(BaseModel):
    """设备配置响应"""
    hardware_signature: str = Field(..., description="设备硬件签名")
    custom_name: Optional[str] = Field(None, description="自定义名称")
    notes: Optional[str] = Field(None, description="设备备注")
    config_data: Optional[Dict[str, Any]] = Field(None, description="设备配置数据")
    device_group: Optional[str] = Field(None, description="设备分组")
    permissions: Optional[Dict[str, Any]] = Field(None, description="设备权限")

class SlaveConfigResponse(BaseModel):
    """从服务器配置响应"""
    config_version: int = Field(..., description="配置版本")
    devices: List[DeviceConfigResponse] = Field(..., description="设备配置列表")
    sync_timestamp: str = Field(..., description="同步时间戳")

# ==================== 设备配置同步API ====================

@router.get("/{hardware_uuid}/config", response_model=SlaveConfigResponse)
async def get_slave_config(
    hardware_uuid: str,
    db: AsyncSession = Depends(get_db)
):
    """获取从服务器配置"""
    try:
        # 查找从服务器
        stmt = select(SlaveServer).where(SlaveServer.hardware_uuid == hardware_uuid)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()

        if not slave_server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器未找到"
            )

        # 查找该从服务器的所有设备
        stmt = select(Device).where(Device.slave_server_id == slave_server.id)
        result = await db.execute(stmt)
        devices = result.scalars().all()

        # 构建设备配置列表
        device_configs = []
        for device in devices:
            device_config = DeviceConfigResponse(
                hardware_signature=device.hardware_signature or "",
                custom_name=device.custom_name,
                notes=device.notes,
                config_data=device.config_data,
                device_group=device.device_group,
                permissions={}  # TODO: 实现权限查询
            )
            device_configs.append(device_config)

        return SlaveConfigResponse(
            config_version=slave_server.config_version or 1,
            devices=device_configs,
            sync_timestamp=datetime.now().isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取从服务器配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配置失败"
        )

@router.post("/{hardware_uuid}/sync-devices")
async def sync_slave_devices(
    hardware_uuid: str,
    request: DeviceSyncRequest,
    db: AsyncSession = Depends(get_db)
):
    """同步从服务器设备清单"""
    try:
        # 查找从服务器
        stmt = select(SlaveServer).where(SlaveServer.hardware_uuid == hardware_uuid)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()

        if not slave_server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器未找到"
            )

        # 获取现有设备
        stmt = select(Device).where(Device.slave_server_id == slave_server.id)
        result = await db.execute(stmt)
        existing_devices = {device.hardware_signature: device for device in result.scalars().all() if device.hardware_signature}

        sync_results = {
            "created": 0,
            "updated": 0,
            "unchanged": 0,
            "conflicts": []
        }

        # 处理每个设备
        for device_info in request.devices:
            hardware_signature = device_info.hardware_signature

            if hardware_signature in existing_devices:
                # 更新现有设备
                existing_device = existing_devices[hardware_signature]

                # 检查是否有变更
                has_changes = False

                # 更新物理端口（设备可能移动）
                if existing_device.physical_port != device_info.physical_port:
                    existing_device.physical_port = device_info.physical_port
                    has_changes = True

                # 更新基本信息
                if existing_device.description != device_info.description:
                    existing_device.description = device_info.description
                    has_changes = True

                if existing_device.serial_number != device_info.serial_number:
                    existing_device.serial_number = device_info.serial_number
                    has_changes = True

                # 更新自定义配置（如果从服务器提供）
                if device_info.custom_name and existing_device.custom_name != device_info.custom_name:
                    existing_device.custom_name = device_info.custom_name
                    has_changes = True

                if device_info.notes and existing_device.notes != device_info.notes:
                    existing_device.notes = device_info.notes
                    has_changes = True

                if device_info.config_data and existing_device.config_data != device_info.config_data:
                    existing_device.config_data = device_info.config_data
                    has_changes = True

                # 只在有实际变化时才更新updated_at
                existing_device.last_used_at = datetime.now()  # 总是更新最后使用时间

                if has_changes:
                    existing_device.updated_at = datetime.now()  # 只在有变化时更新
                    sync_results["updated"] += 1
                    logger.debug(f"更新设备: {hardware_signature}")
                else:
                    sync_results["unchanged"] += 1
                    logger.debug(f"设备无变化: {hardware_signature}")

            else:
                # 创建新设备
                # 修复：使用与从服务器一致的device_id生成逻辑
                # 从physical_port提取bus和address信息
                # 真实格式："BUS:1|ADDR:3" 或测试格式："1-3"
                try:
                    bus = "unknown"
                    address = "unknown"

                    if "BUS:" in device_info.physical_port and "ADDR:" in device_info.physical_port:
                        # 真实从服务器格式：BUS:1|ADDR:3
                        parts = device_info.physical_port.split("|")
                        for part in parts:
                            if part.startswith("BUS:"):
                                bus = part.split(":", 1)[1]
                            elif part.startswith("ADDR:"):
                                address = part.split(":", 1)[1]
                    elif '-' in device_info.physical_port:
                        # 测试格式：1-3
                        bus, address = device_info.physical_port.split('-', 1)

                    # 生成与从服务器一致的device_id
                    device_id = f"{device_info.vendor_id}:{device_info.product_id}:{bus}:{address}"

                except Exception as e:
                    # 异常降级方案：使用hardware_signature确保唯一性
                    logger.warning(f"解析physical_port失败: {device_info.physical_port}, 错误: {e}")
                    device_id = f"{device_info.vendor_id}:{device_info.product_id}:{hardware_signature[:8]}"

                new_device = Device(
                    device_id=device_id,
                    hardware_signature=hardware_signature,
                    vendor_id=device_info.vendor_id,
                    product_id=device_info.product_id,
                    serial_number=device_info.serial_number,
                    physical_port=device_info.physical_port,
                    description=device_info.description,
                    custom_name=device_info.custom_name,
                    notes=device_info.notes,
                    config_data=device_info.config_data,
                    slave_server_id=slave_server.id,
                    status="online",
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )

                db.add(new_device)
                sync_results["created"] += 1

        # 只在有设备变更时才递增配置版本
        if sync_results["created"] > 0 or sync_results["updated"] > 0:
            slave_server.config_version = (slave_server.config_version or 0) + 1
            logger.info(f"设备同步有变更，配置版本递增至: {slave_server.config_version}")
        else:
            logger.debug(f"设备同步无变更，配置版本保持: {slave_server.config_version}")

        # 修复：更新从服务器的统计字段
        # 重新计算该从服务器的设备统计
        device_count_stmt = select(func.count(Device.id)).where(Device.slave_server_id == slave_server.id)
        device_count_result = await db.execute(device_count_stmt)
        actual_device_count = device_count_result.scalar()

        # 更新从服务器统计字段
        slave_server.device_count = actual_device_count
        slave_server.status = "online"  # 设备同步成功说明服务器在线
        slave_server.last_seen = datetime.now()
        slave_server.updated_at = datetime.now()

        logger.info(f"更新从服务器统计: {slave_server.name} - 设备数: {actual_device_count}")

        await db.commit()

        logger.info(f"设备同步完成: {sync_results}")

        return {
            "status": "success",
            "message": "设备同步完成",
            "sync_results": sync_results,
            "config_version": slave_server.config_version
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设备同步失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="设备同步失败"
        )

# ==================== 从服务器详情 ====================

@router.get("/{slave_id}", response_model=Dict[str, Any])
async def get_slave_server_detail(
    slave_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取从服务器详细信息"""
    try:
        stmt = select(SlaveServer).where(SlaveServer.id == slave_id)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()

        if not slave_server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器不存在"
            )

        return {
            "status": "success",
            "data": {
                "id": slave_server.id,
                "server_id": slave_server.server_id,
                "name": slave_server.name,
                "ip_address": slave_server.ip_address,
                "port": slave_server.port,
                "vh_port": slave_server.vh_port,
                "status": slave_server.status,
                "last_seen": slave_server.last_seen,
                "description": slave_server.description,
                "location": slave_server.location,
                "created_at": slave_server.created_at,
                "hardware_uuid": slave_server.hardware_uuid,
                "config_version": slave_server.config_version,
                "hardware_info": slave_server.hardware_info
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取从服务器详细信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取从服务器详细信息失败: {str(e)}"
        )

@router.get("/{slave_id}/system-detail", response_model=Dict[str, Any])
async def get_slave_server_system_detail(
    slave_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取从服务器系统详细信息（代理请求）"""
    try:
        # 首先获取从服务器基础信息
        stmt = select(SlaveServer).where(SlaveServer.id == slave_id)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()

        if not slave_server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器不存在"
            )

        # 多种网络环境的连接尝试策略
        import httpx

        # 定义多个可能的连接URL（按优先级排序）
        possible_urls = [
            # 1. 使用数据库中记录的IP（生产环境）
            f"http://{slave_server.ip_address}:{slave_server.port}/api/system/status",
            # 2. 使用localhost（开发环境，从服务器在同一主机）
            f"http://localhost:{slave_server.port}/api/system/status",
            # 3. 使用host.docker.internal（Docker Desktop环境）
            f"http://host.docker.internal:{slave_server.port}/api/system/status",
            # 4. 使用127.0.0.1（本地回环）
            f"http://127.0.0.1:{slave_server.port}/api/system/status"
        ]

        last_error = None

        # 使用优化的HTTP客户端和重试管理器
        from core.http_client_pool import get_http_client
        from core.http_retry_manager import get_retry_manager

        # 尝试每个URL，直到成功或全部失败
        try:
            http_client = await get_http_client()
            retry_manager = get_retry_manager()

            for url in possible_urls:
                try:
                    logger.info(f"尝试连接从服务器: {url}")

                    # 使用重试机制发送请求
                    response = await retry_manager.retry_request(
                        http_client.get,
                        url,
                        base_timeout=10.0
                    )

                    if response.status_code == 200:
                        detail_data = response.json()
                        logger.info(f"成功连接从服务器: {url}")
                        return {
                            "status": "success",
                            "data": detail_data
                        }
                    else:
                        last_error = f"HTTP {response.status_code}"
                        logger.warning(f"从服务器响应错误 {url}: {response.status_code}")

                except Exception as e:
                    last_error = str(e)
                    logger.warning(f"连接失败 {url}: {e}")
                    continue
        except Exception as e:
            logger.error(f"HTTP客户端初始化失败: {e}")
            last_error = str(e)

            # 所有连接尝试都失败 - 区分离线状态和系统错误
            # 检查从服务器是否在线（基于心跳）
            current_time = datetime.now()
            heartbeat_timeout = timedelta(seconds=15)

            if slave_server.last_heartbeat:
                time_since_heartbeat = current_time - slave_server.last_heartbeat
                if time_since_heartbeat > heartbeat_timeout:
                    # 从服务器离线（正常运维状态）
                    offline_duration = time_since_heartbeat.total_seconds()
                    if offline_duration < 300:  # 5分钟内
                        offline_reason = "网络连接中断"
                    elif offline_duration < 3600:  # 1小时内
                        offline_reason = "设备可能断电或网络故障"
                    else:
                        offline_reason = "长时间离线，请检查设备状态"

                    # 从服务器离线时返回默认数据，而不是抛出异常
                    offline_data = {
                        "server_info": {
                            "id": slave_server.id,
                            "server_id": slave_server.server_id,
                            "name": slave_server.name,
                            "ip_address": slave_server.ip_address,
                            "port": slave_server.port,
                            "vh_port": slave_server.vh_port,
                            "status": "offline",
                            "is_online": False,
                            "offline_duration": f"{int(offline_duration//60)}分钟",
                            "last_seen": slave_server.last_heartbeat.isoformat() if slave_server.last_heartbeat else None,
                            "description": slave_server.description,
                            "created_at": slave_server.created_at.isoformat() if slave_server.created_at else None,
                            "hardware_uuid": slave_server.hardware_uuid
                        },
                        "system_info": {
                            "cpu_usage": "N/A",
                            "memory_usage": "N/A",
                            "disk_usage": "N/A",
                            "uptime": "N/A"
                        },
                        "virtualhere_info": {
                            "status": "stopped",
                            "port": slave_server.vh_port,
                            "version": "N/A",
                            "process_id": "N/A"
                        },
                        "usb_info": {
                            "device_count": 0,
                            "devices": []
                        },
                        "timestamp": datetime.now().isoformat()
                    }

                    return {
                        "success": True,
                        "data": offline_data,
                        "message": f"从服务器离线: {offline_reason}（离线时长: {int(offline_duration//60)}分钟）"
                    }

            # 从未收到心跳或其他系统错误时也返回默认数据
            offline_data = {
                "server_info": {
                    "id": slave_server.id,
                    "server_id": slave_server.server_id,
                    "name": slave_server.name,
                    "ip_address": slave_server.ip_address,
                    "port": slave_server.port,
                    "vh_port": slave_server.vh_port,
                    "status": "offline",
                    "is_online": False,
                    "offline_duration": "未知",
                    "last_seen": slave_server.last_heartbeat.isoformat() if slave_server.last_heartbeat else None,
                    "description": slave_server.description,
                    "created_at": slave_server.created_at.isoformat() if slave_server.created_at else None,
                    "hardware_uuid": slave_server.hardware_uuid
                },
                "system_info": {
                    "cpu_usage": "N/A",
                    "memory_usage": "N/A",
                    "disk_usage": "N/A",
                    "uptime": "N/A"
                },
                "virtualhere_info": {
                    "status": "stopped",
                    "port": slave_server.vh_port,
                    "version": "N/A",
                    "process_id": "N/A"
                },
                "usb_info": {
                    "device_count": 0,
                    "devices": []
                },
                "timestamp": datetime.now().isoformat()
            }

            return {
                "success": True,
                "data": offline_data,
                "message": f"从服务器连接失败: {last_error}（请检查服务器状态或网络配置）"
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取从服务器系统详细信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取从服务器系统详细信息失败: {str(e)}"
        )

@router.post("/{server_id}/control", summary="远程控制从服务器")
async def control_slave_server(
    server_id: int,
    action: str = Body(..., description="控制动作: restart, stop, start, refresh"),
    current_user: User = Depends(require_global_admin),
    db: AsyncSession = Depends(get_db)
):
    """远程控制从服务器"""
    try:
        # 获取从服务器信息
        stmt = select(SlaveServer).where(SlaveServer.id == server_id)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()

        if not slave_server:
            raise HTTPException(status_code=404, detail="从服务器不存在")

        # 检查从服务器是否在线
        current_time = datetime.now()
        heartbeat_timeout = timedelta(seconds=15)
        is_online = False

        if slave_server.last_heartbeat:
            time_since_heartbeat = current_time - slave_server.last_heartbeat
            is_online = time_since_heartbeat <= heartbeat_timeout

        if not is_online and action in ['restart', 'stop']:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="从服务器离线，无法执行远程控制操作"
            )

        # 构建控制API URL
        possible_urls = [
            f"http://{slave_server.ip_address}:{slave_server.port}/api/system/control",
            f"http://{slave_server.ip_address}:8889/api/system/control"
        ]

        control_data = {"action": action}
        success = False
        last_error = None

        # 使用优化的HTTP客户端发送控制命令
        from core.http_client_pool import get_http_client

        try:
            http_client = await get_http_client()
            for url in possible_urls:
                try:
                    logger.info(f"发送控制命令到从服务器: {url}, action: {action}")
                    response = await http_client.post(url, json=control_data)

                    if response.status_code == 200:
                        result_data = response.json()
                        success = True
                        logger.info(f"控制命令执行成功: {url}")
                        break
                    else:
                        last_error = f"HTTP {response.status_code}"
                        logger.warning(f"控制命令失败 {url}: {response.status_code}")

                except Exception as e:
                    last_error = str(e)
                    logger.warning(f"控制命令连接失败 {url}: {e}")
                    continue
        except Exception as e:
            logger.error(f"HTTP客户端初始化失败: {e}")
            last_error = str(e)

        if success:
            # 记录操作日志
            logger.info(f"用户 {current_user.username} 对从服务器 {slave_server.name} 执行了 {action} 操作")

            # 如果是刷新操作，立即更新心跳时间
            if action == "refresh":
                slave_server.last_heartbeat = current_time
                await db.commit()

            return {
                "success": True,
                "message": f"控制命令 '{action}' 执行成功",
                "data": {
                    "action": action,
                    "server_id": server_id,
                    "timestamp": current_time.isoformat()
                }
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"控制命令执行失败: {last_error}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"控制从服务器失败: {e}")
        raise HTTPException(status_code=500, detail=f"控制从服务器失败: {str(e)}")

# ==================== 设备自动注册功能 ====================

async def auto_register_devices(db: AsyncSession, slave_server, device_details):
    """自动注册从服务器检测到的设备到Device表"""
    from models import Device

    created_count = 0
    updated_count = 0
    skipped_count = 0

    try:
        for device_info in device_details:
            # 生成设备唯一标识
            hardware_signature = device_info.get('hardware_signature', '')
            device_id = device_info.get('device_id', '')

            if not hardware_signature and not device_id:
                skipped_count += 1
                continue

            # 检查设备是否已存在
            stmt = select(Device).where(
                and_(
                    Device.slave_server_id == slave_server.id,
                    or_(
                        Device.hardware_signature == hardware_signature,
                        Device.device_id == device_id
                    )
                )
            )
            result = await db.execute(stmt)
            existing_device = result.scalar_one_or_none()

            if existing_device:
                # 更新现有设备状态和USB.IDS信息
                existing_device.status = device_info.get('status', 'online')
                existing_device.last_connected = datetime.now()
                existing_device.updated_at = datetime.now()
                # 更新USB.IDS增强信息
                existing_device.usb_ids_vendor_name = device_info.get('usb_ids_vendor_name')
                existing_device.usb_ids_device_name = device_info.get('usb_ids_device_name')
                existing_device.usb_ids_full_name = device_info.get('usb_ids_full_name')
                existing_device.identification_source = device_info.get('identification_source', 'local_rules')
                # 如果设备类型被USB.IDS增强识别改进，也更新设备类型
                if device_info.get('identification_source') == 'usb_ids_enhanced':
                    existing_device.device_type = device_info.get('device_type', existing_device.device_type)
                updated_count += 1
            else:
                # 创建新设备
                new_device = Device(
                    device_id=device_id or f"auto_{hardware_signature}",
                    device_name=device_info.get('device_name', device_info.get('description', '未知设备')),
                    device_type=device_info.get('device_type', 'unknown'),
                    vendor_id=str(device_info.get('vendor_id', '')),
                    product_id=str(device_info.get('product_id', '')),
                    hardware_signature=hardware_signature,
                    description=device_info.get('description', ''),
                    physical_port=device_info.get('port_location', ''),
                    slave_server_id=slave_server.id,
                    status=device_info.get('status', 'online'),
                    is_real_hardware=device_info.get('is_real_hardware', True),
                    auto_bind_eligible=device_info.get('auto_bind_eligible', False),
                    auto_generated_name=device_info.get('auto_generated_name', ''),
                    # USB.IDS增强字段
                    usb_ids_vendor_name=device_info.get('usb_ids_vendor_name'),
                    usb_ids_device_name=device_info.get('usb_ids_device_name'),
                    usb_ids_full_name=device_info.get('usb_ids_full_name'),
                    identification_source=device_info.get('identification_source', 'local_rules'),
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                    last_connected=datetime.now()
                )

                db.add(new_device)
                created_count += 1

        await db.commit()
        logger.info(f"设备自动注册完成: 创建{created_count}, 更新{updated_count}, 跳过{skipped_count}")

    except Exception as e:
        logger.error(f"设备自动注册失败: {e}")
        await db.rollback()

    return created_count, updated_count, skipped_count


# ==================== 数据一致性修复API ====================

@router.post("/{server_id}/repair-data")
async def repair_slave_data_consistency(
    server_id: int,
    current_user: User = Depends(require_global_admin),
    db: AsyncSession = Depends(get_db)
):
    """修复从服务器数据一致性"""
    try:
        # 查找从服务器
        stmt = select(SlaveServer).where(SlaveServer.id == server_id)
        result = await db.execute(stmt)
        slave_server = result.scalar_one_or_none()

        if not slave_server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="从服务器不存在"
            )

        # 获取从服务器当前设备数据
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                url = f"http://{slave_server.ip_address}:{slave_server.port}/api/devices"
                async with session.get(url, timeout=10) as response:
                    if response.status == 200:
                        slave_devices = await response.json()
                    else:
                        raise Exception(f"从服务器响应错误: {response.status}")
        except Exception as e:
            logger.error(f"获取从服务器设备数据失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="无法连接到从服务器"
            )

        # 同步设备数据到主服务器
        from models import Device
        repair_results = {
            'updated_devices': 0,
            'created_devices': 0,
            'removed_devices': 0,
            'topology_updated': False
        }

        # 获取主服务器中该从服务器的设备
        stmt = select(Device).where(Device.slave_server_id == server_id)
        result = await db.execute(stmt)
        main_devices = {device.device_id: device for device in result.scalars().all()}

        # 处理从服务器设备数据
        slave_device_ids = set()
        for device_data in slave_devices.get('devices', []):
            device_id = device_data.get('device_id')
            if not device_id:
                continue

            slave_device_ids.add(device_id)

            if device_id in main_devices:
                # 更新现有设备
                device = main_devices[device_id]
                device.status = device_data.get('status', 'offline')
                device.description = device_data.get('description', device.description)
                device.updated_at = datetime.now()
                repair_results['updated_devices'] += 1
            else:
                # 创建新设备
                new_device = Device(
                    device_id=device_id,
                    slave_server_id=server_id,
                    vendor_id=device_data.get('vendor_id'),
                    product_id=device_data.get('product_id'),
                    description=device_data.get('description'),
                    status=device_data.get('status', 'offline'),
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                db.add(new_device)
                repair_results['created_devices'] += 1

        # 删除主服务器中不存在于从服务器的设备
        for device_id, device in main_devices.items():
            if device_id not in slave_device_ids:
                await db.delete(device)
                repair_results['removed_devices'] += 1

        # 更新从服务器拓扑信息
        topology_data = slave_devices.get('topology', {})
        if topology_data:
            slave_server.device_count = len(slave_device_ids)
            slave_server.hub_count = topology_data.get('hub_count', 0)
            slave_server.total_ports = topology_data.get('total_ports', 0)
            slave_server.occupied_ports = topology_data.get('occupied_ports', 0)
            slave_server.free_ports = topology_data.get('free_ports', 0)
            slave_server.last_config_check = datetime.now()
            repair_results['topology_updated'] = True

        await db.commit()

        logger.info(f"数据一致性修复完成 - 服务器: {slave_server.name}, 结果: {repair_results}")
        return {
            "status": "success",
            "message": "数据一致性修复完成",
            "server_id": server_id,
            "server_name": slave_server.name,
            "repair_results": repair_results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"数据一致性修复失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"数据一致性修复失败: {str(e)}"
        )

# ==================== 强制同步API ====================

class ForceSyncRequest(BaseModel):
    slave_ids: List[int]

@router.post("/force-sync")
async def force_sync_slave_devices(
    request: ForceSyncRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """强制同步指定从服务器的设备数据"""
    try:
        # 检查权限
        if current_user.role_name not in ["全域管理员", "超级管理员", "管理员"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只有管理员可以强制同步设备数据"
            )

        results = []

        for slave_id in request.slave_ids:
            # 获取从服务器信息
            slave_server = await db.get(SlaveServer, slave_id)
            if not slave_server:
                results.append({
                    "slave_id": slave_id,
                    "success": False,
                    "message": f"从服务器 {slave_id} 不存在"
                })
                continue

            try:
                # 1. 检查从服务器在线状态
                is_online = await check_slave_server_online(slave_server)
                if not is_online:
                    # 计算离线时长
                    offline_duration = "未知"
                    if slave_server.last_heartbeat:
                        offline_time = datetime.now() - slave_server.last_heartbeat
                        offline_minutes = int(offline_time.total_seconds() / 60)
                        if offline_minutes < 60:
                            offline_duration = f"{offline_minutes}分钟"
                        else:
                            offline_hours = offline_minutes // 60
                            offline_duration = f"{offline_hours}小时{offline_minutes % 60}分钟"

                    results.append({
                        "slave_id": slave_id,
                        "success": False,
                        "message": f"从服务器 {slave_server.name} 当前离线（已离线 {offline_duration}），无法执行强制同步"
                    })
                    continue

                # 2. 清空该从服务器的现有设备数据
                delete_result = await db.execute(
                    select(Device).where(Device.slave_server_id == slave_id)
                )
                existing_devices = delete_result.scalars().all()

                for device in existing_devices:
                    await db.delete(device)

                logger.info(f"已清空从服务器 {slave_server.name} 的 {len(existing_devices)} 个设备记录")

                # 3. 向从服务器发送强制同步请求
                sync_success = await request_slave_device_sync(slave_server)

                if sync_success:
                    results.append({
                        "slave_id": slave_id,
                        "success": True,
                        "message": f"从服务器 {slave_server.name} 强制同步请求已发送",
                        "cleared_devices": len(existing_devices)
                    })
                else:
                    results.append({
                        "slave_id": slave_id,
                        "success": False,
                        "message": f"从服务器 {slave_server.name} 同步请求发送失败"
                    })

            except Exception as e:
                logger.error(f"强制同步从服务器 {slave_id} 失败: {e}")
                results.append({
                    "slave_id": slave_id,
                    "success": False,
                    "message": f"同步失败: {str(e)}"
                })

        await db.commit()

        return {
            "success": True,
            "message": f"强制同步请求已处理，共 {len(request.slave_ids)} 个从服务器",
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"强制同步设备数据失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="强制同步设备数据失败"
        )


async def request_slave_device_sync(slave_server: SlaveServer) -> bool:
    """向从服务器发送强制同步请求"""
    try:
        # 构建从服务器同步API URL
        sync_url = f"http://{slave_server.ip_address}:{slave_server.port}/api/force-sync"

        # 准备同步请求数据
        sync_data = {
            "action": "force_device_sync",
            "timestamp": datetime.now().isoformat(),
            "master_server_id": "main-server"
        }

        # 发送同步请求到从服务器
        timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.post(sync_url, json=sync_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"从服务器 {slave_server.name} 强制同步请求成功: {result}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"从服务器 {slave_server.name} 同步请求失败 {response.status}: {error_text}")
                        return False
            except aiohttp.ClientConnectorError as e:
                logger.error(f"无法连接到从服务器 {slave_server.name} ({slave_server.ip_address}:{slave_server.port}): {e}")
                return False
            except asyncio.TimeoutError:
                logger.error(f"从服务器 {slave_server.name} 同步请求超时")
                return False
    except Exception as e:
        logger.error(f"向从服务器 {slave_server.name} 发送同步请求失败: {e}")
        return False


async def check_slave_server_online(slave_server: SlaveServer) -> bool:
    """
    检查从服务器是否在线

    Args:
        slave_server: 从服务器对象

    Returns:
        bool: 是否在线
    """
    try:
        # 1. 检查心跳时间（5分钟内有心跳认为在线）
        if slave_server.last_heartbeat:
            time_diff = datetime.now() - slave_server.last_heartbeat
            if time_diff.total_seconds() <= 300:  # 5分钟
                return True

        # 2. 主动ping检查从服务器连通性
        ping_url = f"http://{slave_server.ip_address}:{slave_server.port}/api/health"

        timeout = aiohttp.ClientTimeout(total=5)  # 5秒超时
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.get(ping_url) as response:
                    if response.status == 200:
                        logger.info(f"从服务器 {slave_server.name} ping检查成功")
                        return True
                    else:
                        logger.warning(f"从服务器 {slave_server.name} ping检查失败: HTTP {response.status}")
                        return False
            except aiohttp.ClientConnectorError:
                logger.warning(f"从服务器 {slave_server.name} 连接失败")
                return False
            except asyncio.TimeoutError:
                logger.warning(f"从服务器 {slave_server.name} ping超时")
                return False
    except Exception as e:
        logger.error(f"检查从服务器 {slave_server.name} 在线状态失败: {e}")
        return False

@router.post("/repair-topology",
            summary="修复拓扑不一致",
            description="修复所有从服务器的拓扑信息不一致问题")
async def repair_topology_inconsistencies(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """修复拓扑不一致问题"""
    try:
        # 权限检查 - 需要管理员权限
        if not await check_permission(db, current_user.id, "system_management", "write"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限执行拓扑修复"
            )

        # 使用拓扑同步管理器执行修复
        from core.topology_sync_manager import get_topology_sync_manager

        topology_manager = get_topology_sync_manager()
        repair_results = await topology_manager.repair_topology_inconsistencies(db)

        if "error" in repair_results:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"拓扑修复失败: {repair_results['error']}"
            )

        return {
            "status": "success",
            "message": "拓扑修复完成",
            "data": repair_results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"拓扑修复失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="拓扑修复失败"
        )
