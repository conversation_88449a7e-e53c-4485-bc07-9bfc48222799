#!/usr/bin/env python3
"""
系统设置管理路由
版本: 1.0
创建日期: 2025-01-13
描述: 系统设置和内网穿透管理API
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from typing import List, Dict, Any, Optional
import json
import logging

from database import get_db
from models import SystemSettings, TunnelConfig, User
from dual_permission_system import require_global_admin, require_super_admin
from auth_utils import get_current_user
from tunnel_manager import tunnel_manager
from config_validator import config_validator
from domain_access_manager import domain_access_manager, AccessType

logger = logging.getLogger(__name__)



router = APIRouter(prefix="/api/v1/system", tags=["系统设置"])

# ==================== 公开API ====================

@router.get("/client-download-url", summary="获取客户端下载链接（公开接口）")
async def get_public_client_download_url(db: AsyncSession = Depends(get_db)):
    """获取客户端下载链接 - 公开接口，无需认证"""
    try:
        result = await db.execute(
            select(SystemSettings).where(SystemSettings.key == "client_download_url")
        )
        setting = result.scalar_one_or_none()

        if not setting:
            return {
                "success": False,
                "message": "客户端下载链接未配置"
            }

        return {
            "success": True,
            "data": {
                "url": setting.value,
                "description": setting.description
            }
        }
    except Exception as e:
        logger.error(f"获取客户端下载链接失败: {e}")
        return {
            "success": False,
            "message": "获取下载链接失败"
        }

# ==================== 系统设置管理 ====================

@router.get("/settings", summary="获取系统设置列表")
async def get_system_settings(
    category: Optional[str] = None,
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取系统设置列表（仅超级管理员及以上）"""
    try:
        query = select(SystemSettings)
        if category:
            query = query.where(SystemSettings.category == category)
        
        result = await db.execute(query.order_by(SystemSettings.category, SystemSettings.key))
        settings = result.scalars().all()
        
        return {
            "success": True,
            "data": [
                {
                    "id": setting.id,
                    "key": setting.key,
                    "value": setting.value,
                    "description": setting.description,
                    "category": setting.category,
                    "is_encrypted": setting.is_encrypted,
                    "created_at": setting.created_at,
                    "updated_at": setting.updated_at
                }
                for setting in settings
            ]
        }
    except Exception as e:
        logger.error(f"获取系统设置失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统设置失败")

@router.get("/settings/{key}", summary="获取单个系统设置")
async def get_system_setting(
    key: str,
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取单个系统设置"""
    try:
        result = await db.execute(
            select(SystemSettings).where(SystemSettings.key == key)
        )
        setting = result.scalar_one_or_none()
        
        if not setting:
            raise HTTPException(status_code=404, detail="设置项不存在")
        
        return {
            "success": True,
            "data": {
                "id": setting.id,
                "key": setting.key,
                "value": setting.value,
                "description": setting.description,
                "category": setting.category,
                "is_encrypted": setting.is_encrypted,
                "created_at": setting.created_at,
                "updated_at": setting.updated_at
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统设置失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统设置失败")

@router.post("/settings", summary="创建系统设置")
async def create_system_setting(
    setting_data: Dict[str, Any],
    current_user: User = Depends(require_global_admin),
    db: AsyncSession = Depends(get_db)
):
    """创建系统设置（仅全域管理员）"""
    try:
        # 检查设置键是否已存在
        result = await db.execute(
            select(SystemSettings).where(SystemSettings.key == setting_data["key"])
        )
        if result.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="设置键已存在")
        
        new_setting = SystemSettings(
            key=setting_data["key"],
            value=setting_data.get("value", ""),
            description=setting_data.get("description", ""),
            category=setting_data.get("category", "general"),
            is_encrypted=setting_data.get("is_encrypted", False)
        )
        
        db.add(new_setting)
        await db.commit()
        await db.refresh(new_setting)
        
        return {
            "success": True,
            "message": "系统设置创建成功",
            "data": {
                "id": new_setting.id,
                "key": new_setting.key,
                "value": new_setting.value,
                "description": new_setting.description,
                "category": new_setting.category
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建系统设置失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="创建系统设置失败")

@router.put("/settings/{key}", summary="更新系统设置")
async def update_system_setting(
    key: str,
    setting_data: Dict[str, Any],
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """更新系统设置"""
    try:
        result = await db.execute(
            select(SystemSettings).where(SystemSettings.key == key)
        )
        setting = result.scalar_one_or_none()
        
        if not setting:
            raise HTTPException(status_code=404, detail="设置项不存在")
        
        # 更新字段
        if "value" in setting_data:
            setting.value = setting_data["value"]
        if "description" in setting_data:
            setting.description = setting_data["description"]
        if "category" in setting_data:
            setting.category = setting_data["category"]
        if "is_encrypted" in setting_data:
            setting.is_encrypted = setting_data["is_encrypted"]
        
        await db.commit()
        await db.refresh(setting)
        
        return {
            "success": True,
            "message": "系统设置更新成功",
            "data": {
                "id": setting.id,
                "key": setting.key,
                "value": setting.value,
                "description": setting.description,
                "category": setting.category
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新系统设置失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="更新系统设置失败")

@router.delete("/settings/{key}", summary="删除系统设置")
async def delete_system_setting(
    key: str,
    current_user: User = Depends(require_global_admin),
    db: AsyncSession = Depends(get_db)
):
    """删除系统设置（仅全域管理员）"""
    try:
        result = await db.execute(
            select(SystemSettings).where(SystemSettings.key == key)
        )
        setting = result.scalar_one_or_none()
        
        if not setting:
            raise HTTPException(status_code=404, detail="设置项不存在")
        
        await db.delete(setting)
        await db.commit()
        
        return {
            "success": True,
            "message": "系统设置删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除系统设置失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="删除系统设置失败")

# ==================== 内网穿透管理 ====================

@router.get("/tunnels", summary="获取内网穿透配置列表")
async def get_tunnel_configs(
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取内网穿透配置列表"""
    try:
        result = await db.execute(
            select(TunnelConfig).order_by(TunnelConfig.tunnel_type, TunnelConfig.name)
        )
        configs = result.scalars().all()

        # 实时更新状态
        config_data = []
        for config in configs:
            # 获取实际运行状态
            actual_status = tunnel_manager.get_tunnel_status(f"{config.tunnel_type}_{config.id}")

            # 如果实际状态与数据库状态不一致，更新数据库
            if actual_status != config.status:
                config.status = actual_status
                config.is_running = (actual_status == "running")
                if actual_status == "stopped":
                    config.error_message = None
                await db.commit()

            config_data.append({
                "id": config.id,
                "name": config.name,
                "tunnel_type": config.tunnel_type,
                "config_data": config.config_data,
                "is_active": config.is_active,
                "is_running": config.is_running,
                "status": config.status,
                "error_message": config.error_message,
                "force_disabled": getattr(config, 'force_disabled', False),
                "created_at": config.created_at,
                "updated_at": config.updated_at
            })

        return {
            "success": True,
            "data": config_data
        }
    except Exception as e:
        logger.error(f"获取内网穿透配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取内网穿透配置失败")

@router.post("/tunnels", summary="创建内网穿透配置")
async def create_tunnel_config(
    config_data: Dict[str, Any],
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """创建内网穿透配置"""
    try:
        # 使用配置验证器验证配置
        validation_result = config_validator.validate_tunnel_config(config_data)
        if not validation_result["valid"]:
            raise HTTPException(
                status_code=400,
                detail=f"配置验证失败: {'; '.join(validation_result['errors'])}"
            )

        # 检查是否已存在相同类型的配置
        tunnel_type = config_data["tunnel_type"]
        result = await db.execute(
            select(TunnelConfig).where(
                TunnelConfig.tunnel_type == tunnel_type,
                TunnelConfig.name == name
            )
        )
        existing_config = result.scalar_one_or_none()

        if existing_config:
            raise HTTPException(
                status_code=400,
                detail=f"已存在名为 '{name}' 的 {tunnel_type.upper()} 配置"
            )

        new_config = TunnelConfig(
            name=name,
            tunnel_type=tunnel_type,
            config_data=config_data["config_data"],
            is_active=config_data.get("is_active", False),
            status="stopped",
            is_running=False
        )

        db.add(new_config)
        await db.commit()
        await db.refresh(new_config)

        return {
            "success": True,
            "message": "内网穿透配置创建成功",
            "data": {
                "id": new_config.id,
                "name": new_config.name,
                "tunnel_type": new_config.tunnel_type,
                "is_active": new_config.is_active
            }
        }
    except Exception as e:
        logger.error(f"创建内网穿透配置失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="创建内网穿透配置失败")

@router.put("/tunnels/{config_id}", summary="更新内网穿透配置")
async def update_tunnel_config(
    config_id: int,
    config_data: Dict[str, Any],
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """更新内网穿透配置"""
    try:
        result = await db.execute(
            select(TunnelConfig).where(TunnelConfig.id == config_id)
        )
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")

        # 更新字段
        if "name" in config_data:
            new_name = config_data["name"]
            # 验证新名称
            name_validation = config_validator.validate_config_name(new_name)
            if not name_validation["valid"]:
                raise HTTPException(
                    status_code=400,
                    detail=f"配置名称验证失败: {'; '.join(name_validation['errors'])}"
                )
            config.name = new_name
        if "config_data" in config_data:
            config.config_data = config_data["config_data"]
        if "is_active" in config_data:
            config.is_active = config_data["is_active"]
        if "force_disabled" in config_data:
            config.force_disabled = config_data["force_disabled"]
        
        await db.commit()
        await db.refresh(config)
        
        return {
            "success": True,
            "message": "内网穿透配置更新成功",
            "data": {
                "id": config.id,
                "name": config.name,
                "tunnel_type": config.tunnel_type,
                "is_active": config.is_active
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新内网穿透配置失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="更新内网穿透配置失败")

@router.delete("/tunnels/{config_id}", summary="删除内网穿透配置")
async def delete_tunnel_config(
    config_id: int,
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """删除内网穿透配置"""
    try:
        result = await db.execute(
            select(TunnelConfig).where(TunnelConfig.id == config_id)
        )
        config = result.scalar_one_or_none()

        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")

        await db.delete(config)
        await db.commit()

        return {
            "success": True,
            "message": "内网穿透配置删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除内网穿透配置失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="删除内网穿透配置失败")

@router.post("/tunnels/{config_id}/start", summary="启动内网穿透")
async def start_tunnel(
    config_id: int,
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """启动内网穿透服务"""
    try:
        result = await db.execute(
            select(TunnelConfig).where(TunnelConfig.id == config_id)
        )
        config = result.scalar_one_or_none()

        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")

        if not config.is_active:
            raise HTTPException(status_code=400, detail="配置未启用")

        if getattr(config, 'force_disabled', False):
            raise HTTPException(status_code=400, detail="配置已被强制禁用，无法启动")

        # 实际启动内网穿透服务
        success = await tunnel_manager.start_tunnel(
            config.tunnel_type,
            config.config_data,
            f"{config.tunnel_type}_{config.id}"
        )

        if success:
            config.is_running = True
            config.status = "running"
            config.error_message = None
        else:
            config.status = "error"
            config.error_message = "启动失败"
            await db.commit()
            raise HTTPException(status_code=500, detail="启动内网穿透失败")

        await db.commit()

        return {
            "success": True,
            "message": f"{config.tunnel_type} 内网穿透启动成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动内网穿透失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="启动内网穿透失败")

@router.post("/tunnels/{config_id}/stop", summary="停止内网穿透")
async def stop_tunnel(
    config_id: int,
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """停止内网穿透服务"""
    try:
        result = await db.execute(
            select(TunnelConfig).where(TunnelConfig.id == config_id)
        )
        config = result.scalar_one_or_none()

        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")

        # 实际停止内网穿透服务
        success = await tunnel_manager.stop_tunnel(f"{config.tunnel_type}_{config.id}")

        if success:
            config.is_running = False
            config.status = "stopped"
        else:
            config.status = "error"
            config.error_message = "停止失败"

        await db.commit()

        return {
            "success": True,
            "message": f"{config.tunnel_type} 内网穿透停止成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止内网穿透失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="停止内网穿透失败")

@router.get("/tunnels/status", summary="获取所有内网穿透状态")
async def get_tunnels_status(
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """获取所有内网穿透服务状态"""
    try:
        result = await db.execute(
            select(TunnelConfig).where(TunnelConfig.is_active == True)
        )
        configs = result.scalars().all()

        status_data = []
        for config in configs:
            status_data.append({
                "id": config.id,
                "name": config.name,
                "tunnel_type": config.tunnel_type,
                "is_running": config.is_running,
                "status": config.status,
                "error_message": config.error_message
            })

        return {
            "success": True,
            "data": status_data
        }
    except Exception as e:
        logger.error(f"获取内网穿透状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取内网穿透状态失败")

@router.post("/test-connection", summary="测试网络连接")
async def test_connection(
    test_data: Dict[str, Any],
    current_user: User = Depends(require_global_admin)  # 降低权限要求，允许全域管理员访问
):
    """测试指定主机和端口的网络连接"""
    import socket
    import time

    try:
        host = test_data.get("host", "localhost")
        port = test_data.get("port", 80)
        timeout = test_data.get("timeout", 5)

        start_time = time.time()

        # 测试TCP连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)

        try:
            result = sock.connect_ex((host, port))
            latency = int((time.time() - start_time) * 1000)  # 转换为毫秒

            if result == 0:
                return {
                    "success": True,
                    "host": host,
                    "port": port,
                    "latency": latency,
                    "status": "connected",
                    "message": f"成功连接到 {host}:{port}"
                }
            else:
                return {
                    "success": False,
                    "host": host,
                    "port": port,
                    "latency": latency,
                    "status": "failed",
                    "message": f"无法连接到 {host}:{port}"
                }
        finally:
            sock.close()

    except Exception as e:
        logger.error(f"连接测试失败: {e}")
        return {
            "success": False,
            "host": host,
            "port": port,
            "latency": -1,
            "status": "error",
            "message": f"连接测试异常: {str(e)}"
        }

@router.post("/test-compression", summary="测试数据压缩性能")
async def test_compression(
    test_data: Dict[str, Any],
    current_user: User = Depends(require_super_admin)
):
    """测试数据压缩性能，支持多种压缩算法"""
    import gzip
    import time
    import base64

    try:
        data = test_data.get("data", "")
        algorithm = test_data.get("algorithm", "snappy")
        level = test_data.get("level", "medium")
        enable_compression = test_data.get("enableCompression", True)

        if not enable_compression:
            # 不启用压缩，直接返回原始数据大小
            return {
                "success": True,
                "originalSize": len(data.encode('utf-8')),
                "compressedSize": len(data.encode('utf-8')),
                "compressedData": data,
                "compressionRatio": 0,
                "algorithm": "none",
                "compressionTime": 0,
                "message": "压缩已禁用"
            }

        # 将数据转换为字节
        data_bytes = data.encode('utf-8')
        original_size = len(data_bytes)

        start_time = time.time()

        # 根据算法选择压缩方法
        if algorithm == "snappy":
            compressed_data = compress_with_snappy(data_bytes, level)
        elif algorithm == "lz4":
            compressed_data = compress_with_lz4(data_bytes, level)
        elif algorithm == "zstd":
            compressed_data = compress_with_zstd(data_bytes, level)
        else:
            # 默认使用gzip
            compressed_data = gzip.compress(data_bytes)

        compression_time = int((time.time() - start_time) * 1000)  # 转换为毫秒
        compressed_size = len(compressed_data)
        compression_ratio = ((original_size - compressed_size) / original_size * 100) if original_size > 0 else 0

        # 将压缩数据转换为base64以便传输
        compressed_data_b64 = base64.b64encode(compressed_data).decode('utf-8')

        return {
            "success": True,
            "originalSize": original_size,
            "compressedSize": compressed_size,
            "compressedData": compressed_data_b64,
            "compressionRatio": round(compression_ratio, 2),
            "algorithm": algorithm,
            "compressionTime": compression_time,
            "message": f"使用 {algorithm} 算法压缩完成"
        }

    except Exception as e:
        logger.error(f"数据压缩测试失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"数据压缩测试异常: {str(e)}"
        }

def compress_with_snappy(data: bytes, level: str) -> bytes:
    """使用Snappy算法压缩数据"""
    try:
        import snappy
        return snappy.compress(data)
    except ImportError:
        # 如果snappy不可用，使用gzip模拟
        import gzip
        return gzip.compress(data, compresslevel=6)

def compress_with_lz4(data: bytes, level: str) -> bytes:
    """使用LZ4算法压缩数据"""
    try:
        import lz4.frame
        compression_level = {"low": 1, "medium": 4, "high": 9}.get(level, 4)
        return lz4.frame.compress(data, compression_level=compression_level)
    except ImportError:
        # 如果lz4不可用，使用gzip模拟
        import gzip
        compression_level = {"low": 1, "medium": 6, "high": 9}.get(level, 6)
        return gzip.compress(data, compresslevel=compression_level)

def compress_with_zstd(data: bytes, level: str) -> bytes:
    """使用Zstandard算法压缩数据"""
    try:
        import zstandard as zstd
        compression_level = {"low": 1, "medium": 3, "high": 19}.get(level, 3)
        cctx = zstd.ZstdCompressor(level=compression_level)
        return cctx.compress(data)
    except ImportError:
        # 如果zstd不可用，使用gzip模拟
        import gzip
        compression_level = {"low": 1, "medium": 6, "high": 9}.get(level, 6)
        return gzip.compress(data, compresslevel=compression_level)


@router.get("/tunnels/{config_id}/management-url")
async def get_tunnel_management_url(
    config_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取内网穿透工具的管理界面URL"""
    # 权限检查：只有全域管理员和超级管理员可以访问
    if current_user.permission_level > 1:
        raise HTTPException(status_code=403, detail="权限不足")

    # 获取配置
    result = await db.execute(select(TunnelConfig).where(TunnelConfig.id == config_id))
    config = result.scalar_one_or_none()

    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")

    # 根据不同的工具类型生成管理界面URL
    tunnel_type = config.tunnel_type.lower()

    try:
        if tunnel_type == "frp":
            # FRP Dashboard 默认端口7500
            config_data = config.config_data or {}
            dashboard_port = config_data.get("dashboard_port", 7500)
            dashboard_addr = config_data.get("dashboard_addr", "127.0.0.1")
            url = f"http://{dashboard_addr}:{dashboard_port}"

        elif tunnel_type == "nps":
            # NPS Web管理端 默认端口8080
            config_data = config.config_data or {}
            web_port = config_data.get("web_port", 8080)
            web_addr = config_data.get("web_addr", "127.0.0.1")
            url = f"http://{web_addr}:{web_port}"

        elif tunnel_type == "headscale":
            # Headscale Web管理界面
            config_data = config.config_data or {}
            server_url = config_data.get("server_url", "http://127.0.0.1:7100")
            url = server_url

        elif tunnel_type == "netbird":
            # NetBird Web管理界面
            config_data = config.config_data or {}
            management_url = config_data.get("management_url", "http://127.0.0.1:7200")
            url = management_url

        elif tunnel_type == "zerotier":
            # ZeroTier Web管理界面
            config_data = config.config_data or {}
            controller_url = config_data.get("controller_url", "http://127.0.0.1:7300")
            url = controller_url

        else:
            return {
                "success": False,
                "message": f"{tunnel_type.upper()} 工具暂不支持Web管理界面"
            }

        return {
            "success": True,
            "url": url,
            "message": f"获取 {tunnel_type.upper()} 管理界面地址成功"
        }

    except Exception as e:
        logger.error(f"获取管理界面URL失败: {str(e)}")
        return {
            "success": False,
            "message": "获取管理界面地址失败"
        }



@router.post("/domain-access/create", summary="创建多服务域名访问配置")
async def create_domain_access_config(
    config_data: Dict[str, Any],
    current_user: User = Depends(require_super_admin),
    db: AsyncSession = Depends(get_db)
):
    """创建多服务域名访问配置"""
    try:
        domain = config_data.get("domain")
        tunnel_type = config_data.get("tunnel_type", "headscale")
        services = config_data.get("services", [])

        if not domain:
            raise HTTPException(status_code=400, detail="域名不能为空")

        if not services:
            # 使用OmniLink默认服务模板
            services = domain_access_manager.get_omnilink_service_template()

        # 创建域名访问配置
        if tunnel_type == "headscale":
            config = domain_access_manager.create_headscale_config(domain, services)
            commands = domain_access_manager.generate_headscale_serve_commands(config)
        elif tunnel_type == "frp":
            access_type = AccessType(config_data.get("access_type", "path_routing"))
            config = domain_access_manager.create_frp_config(domain, services, access_type)
            commands = [json.dumps(domain_access_manager.generate_frp_client_config(config), indent=2)]
        elif tunnel_type == "netbird":
            config = domain_access_manager.create_netbird_config(domain, services)
            commands = [f"# NetBird配置已创建，域名: {domain}"]
        else:
            raise HTTPException(status_code=400, detail=f"不支持的隧道类型: {tunnel_type}")

        # 生成访问URL
        access_urls = domain_access_manager.generate_access_urls(config)

        return {
            "success": True,
            "message": f"成功创建 {tunnel_type.upper()} 多服务域名访问配置",
            "data": {
                "domain": domain,
                "tunnel_type": tunnel_type,
                "access_type": config.access_type.value,
                "services_count": len(config.services),
                "access_urls": access_urls,
                "setup_commands": commands,
                "config": domain_access_manager.export_config(f"{tunnel_type}_{domain}")
            }
        }

    except Exception as e:
        logger.error(f"创建域名访问配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建域名访问配置失败: {str(e)}")

@router.get("/domain-access/templates", summary="获取服务模板")
async def get_service_templates(
    current_user: User = Depends(require_super_admin)
):
    """获取预定义的服务模板"""
    try:
        omnilink_template = domain_access_manager.get_omnilink_service_template()

        return {
            "success": True,
            "message": "获取服务模板成功",
            "data": {
                "omnilink": {
                    "name": "OmniLink标准服务",
                    "description": "OmniLink系统的标准服务配置",
                    "services": omnilink_template
                },
                "custom": {
                    "name": "自定义服务",
                    "description": "用户自定义的服务配置",
                    "services": []
                }
            }
        }

    except Exception as e:
        logger.error(f"获取服务模板失败: {e}")
        raise HTTPException(status_code=500, detail="获取服务模板失败")

@router.get("/domain-access/examples", summary="获取配置示例")
async def get_domain_access_examples(
    current_user: User = Depends(require_super_admin)
):
    """获取多服务域名访问配置示例"""
    try:
        examples = {
            "headscale_path_routing": {
                "name": "Headscale路径路由",
                "description": "使用Headscale的路径路由功能，一个域名访问多个服务",
                "config": {
                    "domain": "omnilink.yourdomain.com",
                    "tunnel_type": "headscale",
                    "access_type": "path_routing",
                    "services": domain_access_manager.get_omnilink_service_template()
                },
                "access_urls": [
                    "https://omnilink.yourdomain.com/ → 主界面",
                    "https://omnilink.yourdomain.com/admin → 管理界面",
                    "https://omnilink.yourdomain.com/api → API服务",
                    "https://omnilink.yourdomain.com/org-users → 用户管理",
                    "https://omnilink.yourdomain.com/system-settings → 系统设置"
                ]
            },
            "frp_subdomain": {
                "name": "FRP子域名",
                "description": "使用FRP的子域名功能，不同子域名访问不同服务",
                "config": {
                    "domain": "yourdomain.com",
                    "tunnel_type": "frp",
                    "access_type": "subdomain",
                    "services": [
                        {"name": "main", "local_port": 8000, "subdomain": "omnilink"},
                        {"name": "admin", "local_port": 8000, "subdomain": "admin"},
                        {"name": "api", "local_port": 8000, "subdomain": "api"}
                    ]
                },
                "access_urls": [
                    "http://omnilink.yourdomain.com → 主界面",
                    "http://admin.yourdomain.com → 管理界面",
                    "http://api.yourdomain.com → API服务"
                ]
            },
            "netbird_port_mapping": {
                "name": "NetBird端口映射",
                "description": "使用NetBird的端口映射功能，不同端口访问不同服务",
                "config": {
                    "domain": "omnilink.local",
                    "tunnel_type": "netbird",
                    "access_type": "port_mapping",
                    "services": [
                        {"name": "web", "local_port": 8000, "remote_port": 8000},
                        {"name": "api", "local_port": 8000, "remote_port": 8001},
                        {"name": "admin", "local_port": 8000, "remote_port": 8002}
                    ]
                },
                "access_urls": [
                    "http://omnilink.local:8000 → 主界面",
                    "http://omnilink.local:8001 → API服务",
                    "http://omnilink.local:8002 → 管理界面"
                ]
            }
        }

        return {
            "success": True,
            "message": "获取配置示例成功",
            "data": examples
        }

    except Exception as e:
        logger.error(f"获取配置示例失败: {e}")
        raise HTTPException(status_code=500, detail="获取配置示例失败")

# ==================== 压缩配置API（兼容性端点） ====================

@router.get("/compression/status", summary="获取压缩状态")
async def get_compression_status(
    current_user: User = Depends(require_global_admin)
):
    """获取压缩状态 - 兼容性端点"""
    return {
        "success": True,
        "data": {
            "enabled": True,
            "algorithm": "gzip",
            "compression_ratio": 0.75,
            "status": "active"
        },
        "message": "压缩状态获取成功"
    }

@router.get("/compression/algorithms", summary="获取可用压缩算法")
async def get_compression_algorithms(
    current_user: User = Depends(require_global_admin)
):
    """获取可用压缩算法 - 兼容性端点"""
    return {
        "success": True,
        "data": [
            {"name": "gzip", "description": "GZIP压缩", "supported": True, "enabled": True},
            {"name": "deflate", "description": "Deflate压缩", "supported": True, "enabled": False},
            {"name": "lz4", "description": "LZ4快速压缩算法", "supported": True, "enabled": False},
            {"name": "snappy", "description": "Google开发的快速压缩", "supported": True, "enabled": False},
            {"name": "zstandard", "description": "高压缩率算法", "supported": True, "enabled": False},
            {"name": "brotli", "description": "Brotli压缩", "supported": False, "enabled": False}
        ],
        "message": "压缩算法列表获取成功"
    }

@router.get("/compression/stats", summary="获取压缩统计")
async def get_compression_stats(
    current_user: User = Depends(require_global_admin)
):
    """获取压缩统计 - 兼容性端点"""
    return {
        "success": True,
        "data": {
            "total_requests": 1000,
            "compressed_requests": 750,
            "compression_ratio": 0.75,
            "bytes_saved": 1024000
        },
        "message": "压缩统计获取成功"
    }

@router.get("/compression/recommendations", summary="获取智能推荐")
async def get_compression_recommendations(
    current_user: User = Depends(require_global_admin)
):
    """获取智能推荐 - 兼容性端点"""
    return {
        "success": True,
        "data": {
            "recommended_algorithm": "gzip",
            "recommended_level": 6,
            "reason": "平衡压缩率和性能"
        },
        "message": "智能推荐获取成功"
    }

@router.get("/compression/config", summary="获取压缩配置")
async def get_compression_config(
    current_user: User = Depends(require_global_admin)
):
    """获取压缩配置 - 兼容性端点"""
    return {
        "success": True,
        "data": {
            "enabled": True,
            "algorithm": "gzip",
            "level": 6,
            "min_size": 1024,
            "exclude_types": ["image/", "video/", "audio/"]
        },
        "message": "压缩配置获取成功"
    }

@router.post("/compression/config", summary="更新压缩配置")
async def update_compression_config(
    config_data: Dict[str, Any],
    current_user: User = Depends(require_global_admin)
):
    """更新压缩配置 - 兼容性端点"""
    return {
        "success": True,
        "data": config_data,
        "message": "压缩配置更新成功"
    }

@router.get("/compression/performance", summary="获取性能指标")
async def get_compression_performance(
    current_user: User = Depends(require_global_admin)
):
    """获取性能指标 - 兼容性端点"""
    return {
        "success": True,
        "data": {
            "cpu_usage": 15.5,
            "memory_usage": 128.0,
            "compression_time": 2.3,
            "throughput": 1024000
        },
        "message": "性能指标获取成功"
    }

@router.post("/compression/algorithms/{algorithm_name}/toggle", summary="切换压缩算法状态")
async def toggle_compression_algorithm(
    algorithm_name: str,
    current_user: User = Depends(require_global_admin)
):
    """切换压缩算法启用状态 - 兼容性端点"""
    return {
        "success": True,
        "data": {
            "algorithm": algorithm_name,
            "enabled": True,
            "message": f"{algorithm_name}算法已启用"
        },
        "message": "算法状态切换成功"
    }

@router.post("/compression/algorithms/{algorithm_name}/test", summary="测试压缩算法")
async def test_compression_algorithm(
    algorithm_name: str,
    current_user: User = Depends(require_global_admin)
):
    """测试压缩算法性能 - 兼容性端点"""
    return {
        "success": True,
        "data": {
            "algorithm": algorithm_name,
            "test_result": "通过",
            "compression_ratio": 0.75,
            "speed": "快速",
            "cpu_usage": 12.5
        },
        "message": f"{algorithm_name}算法测试完成"
    }
