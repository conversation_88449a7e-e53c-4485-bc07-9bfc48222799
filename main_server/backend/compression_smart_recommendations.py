#!/usr/bin/env python3
"""
数据压缩智能推荐系统
版本: 1.0
创建日期: 2025-01-15
描述: 基于网络环境、使用模式和性能指标提供智能的压缩配置推荐
"""

import logging
import time
import statistics
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import asyncio

logger = logging.getLogger(__name__)

class RecommendationType(str, Enum):
    """推荐类型"""
    PERFORMANCE = "performance"      # 性能优化
    BANDWIDTH = "bandwidth"          # 带宽优化
    STABILITY = "stability"          # 稳定性优化
    ENERGY = "energy"               # 能耗优化
    COMPATIBILITY = "compatibility"  # 兼容性优化

class NetworkCondition(str, Enum):
    """网络条件"""
    EXCELLENT = "excellent"  # 优秀 (>10Mbps, <10ms延迟)
    GOOD = "good"           # 良好 (5-10Mbps, 10-50ms延迟)
    FAIR = "fair"           # 一般 (1-5Mbps, 50-100ms延迟)
    POOR = "poor"           # 较差 (<1Mbps, >100ms延迟)

@dataclass
class RecommendationRule:
    """推荐规则"""
    name: str
    description: str
    conditions: Dict[str, Any]
    recommended_config: Dict[str, Any]
    priority: int  # 1-10, 数字越大优先级越高
    confidence: float  # 0-1, 推荐置信度

class CompressionSmartRecommendations:
    """数据压缩智能推荐系统"""
    
    def __init__(self):
        self.recommendation_rules = []
        self.network_history = []
        self.performance_history = []
        
        # 初始化推荐规则
        self._init_recommendation_rules()
        
        logger.info("数据压缩智能推荐系统初始化完成")
    
    def _init_recommendation_rules(self):
        """初始化推荐规则"""
        
        # 高性能网络环境推荐
        self.recommendation_rules.append(RecommendationRule(
            name="高性能网络优化",
            description="检测到优秀的网络环境，推荐使用高压缩率配置以最大化带宽利用",
            conditions={
                "network_condition": NetworkCondition.EXCELLENT,
                "cpu_usage": {"max": 70},
                "concurrent_users": {"max": 200}
            },
            recommended_config={
                "algorithm": "zstd",
                "level": "high",
                "stream_type": "web_https",
                "max_concurrent_users": 500,
                "bandwidth_limit_mbps": 10.0,
                "auto_fallback": True
            },
            priority=8,
            confidence=0.9
        ))
        
        # 低带宽网络环境推荐
        self.recommendation_rules.append(RecommendationRule(
            name="低带宽网络优化",
            description="检测到带宽受限环境，推荐使用高压缩率配置以节省带宽",
            conditions={
                "network_condition": NetworkCondition.POOR,
                "bandwidth_usage": {"min": 80}  # 带宽使用率超过80%
            },
            recommended_config={
                "algorithm": "zstd",
                "level": "high",
                "stream_type": "",
                "max_concurrent_users": 200,
                "bandwidth_limit_mbps": 1.0,
                "auto_fallback": True
            },
            priority=9,
            confidence=0.95
        ))
        
        # 实时应用推荐
        self.recommendation_rules.append(RecommendationRule(
            name="实时应用优化",
            description="检测到大量VirtualHere USB数据流，推荐使用低延迟配置",
            conditions={
                "stream_type_ratio": {"vh_usb": {"min": 60}},  # USB数据流占比超过60%
                "avg_latency": {"max": 20}  # 平均延迟小于20ms
            },
            recommended_config={
                "algorithm": "lz4",
                "level": "low",
                "stream_type": "vh_usb",
                "max_concurrent_users": 300,
                "bandwidth_limit_mbps": 5.0,
                "auto_fallback": True
            },
            priority=7,
            confidence=0.85
        ))
        
        # 高并发场景推荐
        self.recommendation_rules.append(RecommendationRule(
            name="高并发场景优化",
            description="检测到高并发连接，推荐使用平衡性能配置",
            conditions={
                "concurrent_users": {"min": 300},
                "cpu_usage": {"max": 80}
            },
            recommended_config={
                "algorithm": "snappy",
                "level": "medium",
                "stream_type": "",
                "max_concurrent_users": 500,
                "bandwidth_limit_mbps": 3.0,
                "auto_fallback": True
            },
            priority=6,
            confidence=0.8
        ))
        
        # CPU受限环境推荐
        self.recommendation_rules.append(RecommendationRule(
            name="CPU受限环境优化",
            description="检测到CPU使用率较高，推荐使用轻量级压缩算法",
            conditions={
                "cpu_usage": {"min": 85},
                "memory_usage": {"min": 80}
            },
            recommended_config={
                "algorithm": "lz4",
                "level": "low",
                "stream_type": "",
                "max_concurrent_users": 200,
                "bandwidth_limit_mbps": 2.0,
                "auto_fallback": True
            },
            priority=8,
            confidence=0.9
        ))
        
        # Web应用优化推荐
        self.recommendation_rules.append(RecommendationRule(
            name="Web应用优化",
            description="检测到大量Web数据流，推荐使用Web优化配置",
            conditions={
                "stream_type_ratio": {"web_http": {"min": 40}, "web_https": {"min": 30}},
                "data_size_avg": {"min": 1024}  # 平均数据包大小大于1KB
            },
            recommended_config={
                "algorithm": "zstd",
                "level": "medium",
                "stream_type": "web_https",
                "max_concurrent_users": 400,
                "bandwidth_limit_mbps": 5.0,
                "auto_fallback": True
            },
            priority=7,
            confidence=0.85
        ))
        
        # 稳定性优先推荐
        self.recommendation_rules.append(RecommendationRule(
            name="稳定性优先配置",
            description="检测到网络不稳定，推荐使用稳定性优先配置",
            conditions={
                "error_rate": {"min": 5},  # 错误率超过5%
                "connection_drops": {"min": 10}  # 连接断开次数超过10次/小时
            },
            recommended_config={
                "algorithm": "snappy",
                "level": "low",
                "stream_type": "",
                "max_concurrent_users": 300,
                "bandwidth_limit_mbps": 2.0,
                "auto_fallback": True
            },
            priority=9,
            confidence=0.9
        ))
    
    async def analyze_current_environment(self, stats: Dict[str, Any]) -> Dict[str, Any]:
        """分析当前环境状况"""
        analysis = {
            "network_condition": self._assess_network_condition(stats),
            "cpu_usage": stats.get("cpu_usage_percent", 0),
            "memory_usage": stats.get("memory_usage_percent", 0),
            "bandwidth_usage": stats.get("bandwidth_usage_percent", 0),
            "concurrent_users": stats.get("active_connections", 0),
            "error_rate": self._calculate_error_rate(stats),
            "avg_latency": stats.get("avg_latency_ms", 0),
            "stream_type_ratio": self._calculate_stream_type_ratio(stats),
            "data_size_avg": stats.get("avg_data_size_bytes", 0),
            "connection_drops": stats.get("connection_drops_per_hour", 0)
        }
        
        return analysis
    
    async def generate_recommendations(self, current_stats: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成智能推荐"""
        try:
            # 分析当前环境
            environment = await self.analyze_current_environment(current_stats)
            
            # 匹配推荐规则
            matched_rules = []
            for rule in self.recommendation_rules:
                if self._match_rule_conditions(rule, environment):
                    matched_rules.append(rule)
            
            # 按优先级和置信度排序
            matched_rules.sort(key=lambda r: (r.priority, r.confidence), reverse=True)
            
            # 生成推荐列表
            recommendations = []
            for rule in matched_rules[:3]:  # 最多返回3个推荐
                recommendation = {
                    "title": rule.name,
                    "description": rule.description,
                    "type": self._determine_recommendation_type(rule),
                    "priority": rule.priority,
                    "confidence": rule.confidence,
                    "config": rule.recommended_config,
                    "expected_benefits": self._calculate_expected_benefits(rule, environment),
                    "implementation_impact": self._assess_implementation_impact(rule, environment)
                }
                recommendations.append(recommendation)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"生成智能推荐失败: {e}")
            return []
    
    def _assess_network_condition(self, stats: Dict[str, Any]) -> NetworkCondition:
        """评估网络条件"""
        bandwidth_mbps = stats.get("bandwidth_limit_mbps", 3.0)
        latency_ms = stats.get("avg_latency_ms", 50)
        
        if bandwidth_mbps >= 10 and latency_ms <= 10:
            return NetworkCondition.EXCELLENT
        elif bandwidth_mbps >= 5 and latency_ms <= 50:
            return NetworkCondition.GOOD
        elif bandwidth_mbps >= 1 and latency_ms <= 100:
            return NetworkCondition.FAIR
        else:
            return NetworkCondition.POOR
    
    def _calculate_error_rate(self, stats: Dict[str, Any]) -> float:
        """计算错误率"""
        total_operations = stats.get("total_operations", 0)
        total_errors = stats.get("total_errors", 0)
        
        if total_operations == 0:
            return 0.0
        
        return (total_errors / total_operations) * 100
    
    def _calculate_stream_type_ratio(self, stats: Dict[str, Any]) -> Dict[str, float]:
        """计算数据流类型比例"""
        stream_stats = stats.get("stream_type_stats", {})
        total_connections = sum(stream_stats.values()) if stream_stats else 1
        
        ratios = {}
        for stream_type, count in stream_stats.items():
            ratios[stream_type] = (count / total_connections) * 100
        
        return ratios
    
    def _match_rule_conditions(self, rule: RecommendationRule, environment: Dict[str, Any]) -> bool:
        """匹配规则条件"""
        for condition_key, condition_value in rule.conditions.items():
            env_value = environment.get(condition_key)
            
            if env_value is None:
                continue
            
            if isinstance(condition_value, dict):
                # 范围条件
                if "min" in condition_value and env_value < condition_value["min"]:
                    return False
                if "max" in condition_value and env_value > condition_value["max"]:
                    return False
            elif isinstance(condition_value, (str, NetworkCondition)):
                # 精确匹配
                if env_value != condition_value:
                    return False
            elif isinstance(condition_value, dict) and condition_key == "stream_type_ratio":
                # 流类型比例条件
                for stream_type, ratio_condition in condition_value.items():
                    stream_ratio = environment.get("stream_type_ratio", {}).get(stream_type, 0)
                    if "min" in ratio_condition and stream_ratio < ratio_condition["min"]:
                        return False
                    if "max" in ratio_condition and stream_ratio > ratio_condition["max"]:
                        return False
        
        return True
    
    def _determine_recommendation_type(self, rule: RecommendationRule) -> RecommendationType:
        """确定推荐类型"""
        if "性能" in rule.name or "高并发" in rule.name:
            return RecommendationType.PERFORMANCE
        elif "带宽" in rule.name or "低带宽" in rule.name:
            return RecommendationType.BANDWIDTH
        elif "稳定" in rule.name:
            return RecommendationType.STABILITY
        elif "CPU" in rule.name:
            return RecommendationType.ENERGY
        else:
            return RecommendationType.COMPATIBILITY
    
    def _calculate_expected_benefits(self, rule: RecommendationRule, environment: Dict[str, Any]) -> Dict[str, Any]:
        """计算预期收益"""
        benefits = {}
        
        # 根据推荐配置计算预期收益
        config = rule.recommended_config
        
        if config["algorithm"] == "zstd":
            benefits["compression_improvement"] = "15-25%"
            benefits["bandwidth_savings"] = "20-30%"
        elif config["algorithm"] == "lz4":
            benefits["speed_improvement"] = "30-50%"
            benefits["latency_reduction"] = "10-20ms"
        elif config["algorithm"] == "snappy":
            benefits["balance_improvement"] = "平衡的性能提升"
            benefits["stability_improvement"] = "提高稳定性"
        
        if config["level"] == "high":
            benefits["compression_rate"] = "90%+"
        elif config["level"] == "low":
            benefits["processing_speed"] = "极快"
        
        return benefits
    
    def _assess_implementation_impact(self, rule: RecommendationRule, environment: Dict[str, Any]) -> Dict[str, Any]:
        """评估实施影响"""
        impact = {
            "difficulty": "低",  # 低/中/高
            "downtime": "无",    # 无/短暂/较长
            "risk": "低",        # 低/中/高
            "rollback_time": "立即"
        }
        
        # 根据当前环境和推荐配置评估影响
        current_users = environment.get("concurrent_users", 0)
        if current_users > 300:
            impact["difficulty"] = "中"
            impact["risk"] = "中"
        
        return impact

# 创建全局智能推荐系统实例
smart_recommendations = CompressionSmartRecommendations()
