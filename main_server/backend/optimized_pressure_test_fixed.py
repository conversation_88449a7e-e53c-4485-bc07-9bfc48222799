#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniLink优化后压力测试脚本
验证HTTP请求超时优化效果
"""

import asyncio
import aiohttp
import time
import random
import logging
import json
import argparse
from datetime import datetime
from typing import List, Dict, Any
import uuid
import threading
from concurrent.futures import ThreadPoolExecutor
import signal
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'pressure_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OptimizedSlaveSimulator:
    """优化的从服务器模拟器"""
    
    def __init__(self, server_id: int, master_url: str = "http://localhost:8000"):
        self.server_id = server_id
        self.master_url = master_url
        self.server_name = f"OmniLink-STRESS-{server_id:05d}"
        self.hardware_uuid = str(uuid.uuid4())
        self.is_running = False
        self.session: aiohttp.ClientSession = None
        
        # 模拟3-5个USB设备
        self.device_count = random.randint(3, 5)
        self.devices = self._generate_devices()
        
        # 统计信息
        self.stats = {
            'heartbeat_sent': 0,
            'heartbeat_success': 0,
            'heartbeat_failed': 0,
            'register_attempts': 0,
            'register_success': 0,
            'avg_response_time': 0.0,
            'total_response_time': 0.0,
            'max_response_time': 0.0,
            'timeout_count': 0
        }
        
        logger.info(f"初始化模拟器 {self.server_name}，设备数量: {self.device_count}")
    
    def _generate_devices(self) -> List[Dict[str, Any]]:
        """生成虚拟USB设备"""
        devices = []
        device_types = [
            {"vendor_id": "096e", "product_id": "031b", "name": "广联达加密锁"},
            {"vendor_id": "0471", "product_id": "0888", "name": "新点加密锁"},
            {"vendor_id": "1a86", "product_id": "7523", "name": "博威加密锁"},
            {"vendor_id": "0483", "product_id": "5740", "name": "CA证书锁"},
            {"vendor_id": "096e", "product_id": "0006", "name": "银行U盾"}
        ]
        
        for i in range(self.device_count):
            device_type = random.choice(device_types)
            device = {
                "device_id": f"usb-{device_type['vendor_id']}:{device_type['product_id']}-{i+1}",
                "vendor_id": device_type["vendor_id"],
                "product_id": device_type["product_id"],
                "serial_number": f"SN{self.server_id:05d}{i+1:02d}",
                "manufacturer": "模拟厂商",
                "product": device_type["name"],
                "port_number": i + 1,
                "hub_port": f"1-{i+1}",
                "device_path": f"/dev/bus/usb/001/{i+1:03d}",
                "is_available": True
            }
            devices.append(device)
        
        return devices
    
    async def start(self):
        """启动模拟器"""
        if self.is_running:
            return
        
        # 创建HTTP会话，使用优化配置
        timeout = aiohttp.ClientTimeout(
            total=30.0,
            connect=10.0,
            sock_read=30.0
        )
        
        connector = aiohttp.TCPConnector(
            limit=20,
            limit_per_host=10,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            connector=connector
        )
        
        self.is_running = True
        
        # 注册到主服务器
        success = await self._register()
        if success:
            # 发送完整数据同步（包含USB设备信息）
            sync_success = await self._send_data_sync()
            if sync_success:
                # 启动心跳任务
                asyncio.create_task(self._heartbeat_loop())
                logger.info(f"✅ {self.server_name} 启动成功（已注册 {self.device_count} 个设备）")
            else:
                logger.error(f"❌ {self.server_name} 数据同步失败")
        else:
            logger.error(f"❌ {self.server_name} 注册失败")
    
    async def stop(self):
        """停止模拟器"""
        self.is_running = False
        if self.session:
            await self.session.close()
        logger.info(f"🔄 {self.server_name} 已停止")
    
    async def _register(self) -> bool:
        """注册到主服务器"""
        self.stats['register_attempts'] += 1

        # 使用与真实从服务器完全一致的注册数据格式
        register_data = {
            "server_name": self.server_name,
            "server_ip": "auto-detect",  # 主服务器期望的字段名
            "server_port": 8889,
            "vh_port": 7575,
            "hardware_uuid": self.hardware_uuid,
            "hardware_info": {
                "os": "Ubuntu 20.04 LTS",
                "arch": "x86_64",
                "python_version": "3.11.0",
                "total_memory": "8GB",
                "cpu_cores": 4,
                "hostname": f"stress-test-{self.server_id}",
                "kernel": "5.4.0-generic"
            },
            "description": f"OmniLink Stress Test Simulator #{self.server_id}",
            "version": "2.0"
        }
        
        try:
            start_time = time.time()
            async with self.session.post(
                f"{self.master_url}/api/v1/slave/register",
                json=register_data
            ) as response:
                response_time = time.time() - start_time
                self._update_response_stats(response_time)
                
                if response.status == 200:
                    self.stats['register_success'] += 1
                    logger.info(f"✅ {self.server_name} 注册成功 ({response_time:.3f}s)")
                    return True
                else:
                    logger.error(f"❌ {self.server_name} 注册失败: HTTP {response.status}")
                    return False
                    
        except asyncio.TimeoutError:
            self.stats['timeout_count'] += 1
            logger.error(f"⏰ {self.server_name} 注册超时")
            return False
        except Exception as e:
            logger.error(f"❌ {self.server_name} 注册异常: {e}")
            return False

    async def _send_data_sync(self) -> bool:
        """发送完整数据同步（包含USB设备信息）"""
        try:
            # 构建设备详细信息
            device_details = []
            for i, device in enumerate(self.devices):
                device_detail = {
                    "hardware_signature": f"{device['vendor_id']}:{device['product_id']}:{device['serial_number']}",
                    "vendor_id": int(device['vendor_id'], 16),  # 转换为十进制
                    "product_id": int(device['product_id'], 16),  # 转换为十进制
                    "bus": 1,
                    "address": i + 1,
                    "description": device['product'],
                    "device_type": "encryption_dongle",
                    "device_id": device['device_id'],
                    "serial_number": device['serial_number'],
                    "manufacturer": device['manufacturer'],
                    "product": device['product'],
                    "port_number": device['port_number'],
                    "hub_port": device['hub_port'],
                    "device_path": device['device_path'],
                    "is_available": device['is_available'],
                    "is_real_hardware": True,
                    "auto_bind_eligible": True,
                    "usb_ids_vendor_name": device['manufacturer'],
                    "usb_ids_device_name": device['product'],
                    "usb_ids_full_name": f"{device['manufacturer']} {device['product']}",
                    "identification_source": "usb_ids_database"
                }
                device_details.append(device_detail)

            # 构建USB拓扑信息
            usb_topology = {
                "hub_count": 1,
                "total_ports": self.device_count + 2,
                "occupied_ports": self.device_count,
                "free_ports": 2,
                "root_hubs": [
                    {
                        "hub_id": "1-0:1.0",
                        "port_count": self.device_count + 2,
                        "occupied_ports": self.device_count,
                        "free_ports": 2
                    }
                ]
            }

            # 构建完整数据同步请求
            sync_data = {
                "timestamp": datetime.now().isoformat(),
                "sync_type": "full_data",
                "device_count": self.device_count,
                "usb_topology": usb_topology,
                "device_details": device_details,
                "device_summary": {
                    "total_devices": self.device_count,
                    "real_hardware_count": self.device_count,
                    "auto_bind_eligible_count": self.device_count,
                    "device_types": {
                        "encryption_dongle": self.device_count
                    }
                }
            }

            start_time = time.time()
            async with self.session.post(
                f"{self.master_url}/api/v1/slave/data-sync",
                json=sync_data,
                headers={'Content-Type': 'application/json; charset=utf-8'}
            ) as response:
                response_time = time.time() - start_time
                self._update_response_stats(response_time)

                if response.status == 200:
                    result = await response.json()
                    sync_result = result.get('sync_result', {})
                    created = sync_result.get('created', 0)
                    updated = sync_result.get('updated', 0)
                    logger.info(f"✅ {self.server_name} 数据同步成功 ({response_time:.3f}s) - 创建设备: {created}, 更新设备: {updated}")
                    return True
                else:
                    response_text = await response.text()
                    logger.error(f"❌ {self.server_name} 数据同步失败: HTTP {response.status}")
                    logger.error(f"错误详情: {response_text}")
                    return False

        except asyncio.TimeoutError:
            self.stats['timeout_count'] += 1
            logger.error(f"⏰ {self.server_name} 数据同步超时")
            return False
        except Exception as e:
            logger.error(f"❌ {self.server_name} 数据同步异常: {e}")
            return False
    
    async def _heartbeat_loop(self):
        """心跳循环"""
        while self.is_running:
            try:
                await self._send_heartbeat()
                # 心跳间隔30秒，添加随机抖动
                interval = 30 + random.uniform(-3, 3)
                await asyncio.sleep(interval)
            except Exception as e:
                logger.error(f"❌ {self.server_name} 心跳循环异常: {e}")
                await asyncio.sleep(5)
    
    async def _send_heartbeat(self):
        """发送心跳"""
        self.stats['heartbeat_sent'] += 1

        # 使用与真实从服务器完全一致的心跳数据格式
        heartbeat_data = {
            "heartbeat_type": "lightweight",  # 主服务器必需的字段
            "timestamp": datetime.now().isoformat(),
            "status": "online",  # 主服务器期望的状态值
            "device_count_summary": self.device_count,  # 主服务器期望的字段名
            "hub_count": 1,
            "total_ports": self.device_count + 2,
            "occupied_ports": self.device_count,
            "free_ports": 2,
            "server_name": self.server_name,
            "server_port": 8889,
            "vh_port": 7575,
            "server_ip": "*********",  # 模拟IP
            "vh_status": "running",
            "system_info": {
                "cpu_usage": round(random.uniform(10, 50), 2),
                "memory_usage": round(random.uniform(20, 60), 2),
                "disk_usage": round(random.uniform(30, 70), 2),
                "load_average": round(random.uniform(0.5, 2.0), 2),
                "uptime": random.randint(3600, 86400)
            }
        }
        
        try:
            start_time = time.time()
            async with self.session.post(
                f"{self.master_url}/api/v1/slave/heartbeat",
                json=heartbeat_data
            ) as response:
                response_time = time.time() - start_time
                self._update_response_stats(response_time)
                
                if response.status == 200:
                    self.stats['heartbeat_success'] += 1
                    logger.debug(f"💓 {self.server_name} 心跳成功 ({response_time:.3f}s)")
                else:
                    self.stats['heartbeat_failed'] += 1
                    logger.warning(f"💔 {self.server_name} 心跳失败: HTTP {response.status}")
                    
        except asyncio.TimeoutError:
            self.stats['heartbeat_failed'] += 1
            self.stats['timeout_count'] += 1
            logger.error(f"⏰ {self.server_name} 心跳超时")
        except Exception as e:
            self.stats['heartbeat_failed'] += 1
            logger.error(f"❌ {self.server_name} 心跳异常: {e}")
    
    def _update_response_stats(self, response_time: float):
        """更新响应时间统计"""
        self.stats['total_response_time'] += response_time
        self.stats['max_response_time'] = max(self.stats['max_response_time'], response_time)
        
        total_requests = self.stats['heartbeat_sent'] + self.stats['register_attempts']
        if total_requests > 0:
            self.stats['avg_response_time'] = self.stats['total_response_time'] / total_requests
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_heartbeats = self.stats['heartbeat_sent']
        success_rate = (self.stats['heartbeat_success'] / max(total_heartbeats, 1)) * 100
        
        return {
            **self.stats,
            'server_name': self.server_name,
            'device_count': self.device_count,
            'heartbeat_success_rate': success_rate,
            'is_running': self.is_running
        }

class PressureTestController:
    """压力测试控制器"""
    
    def __init__(self, master_url: str = "http://localhost:8000"):
        self.master_url = master_url
        self.simulators: List[OptimizedSlaveSimulator] = []
        self.is_running = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，正在停止测试...")
        asyncio.create_task(self.stop_all())
    
    async def run_stage_test(self, server_count: int, duration: int):
        """运行阶段测试"""
        logger.info("=" * 80)
        logger.info("🚀 OmniLink压力测试启动")
        logger.info("=" * 80)

        # 创建模拟器
        self.simulators = []
        total_devices = 0
        for i in range(server_count):
            simulator = OptimizedSlaveSimulator(i + 1, self.master_url)
            self.simulators.append(simulator)
            total_devices += simulator.device_count

        # 显示测试配置
        logger.info(f"📋 测试配置:")
        logger.info(f"   ├─ 从服务器数量: {server_count}")
        logger.info(f"   ├─ 测试持续时间: {duration}秒")
        logger.info(f"   ├─ 模拟Hub总数: {server_count}")
        logger.info(f"   ├─ 模拟USB设备总数: {total_devices}")
        logger.info(f"   ├─ 平均每服务器设备数: {total_devices/server_count:.1f}")
        logger.info(f"   └─ 主服务器地址: {self.master_url}")
        logger.info("=" * 80)

        self.is_running = True
        
        # 分批启动模拟器，避免雷鸣群体效应
        batch_size = 10
        logger.info(f"🔄 开始分批启动从服务器模拟器（批次大小: {batch_size}）")

        for i in range(0, len(self.simulators), batch_size):
            batch = self.simulators[i:i + batch_size]
            batch_devices = sum(s.device_count for s in batch)

            logger.info(f"📦 启动批次 {i//batch_size + 1}: 服务器 {i+1}-{min(i+batch_size, len(self.simulators))}")
            logger.info(f"   └─ 本批次设备数: {batch_devices}")

            # 并发启动当前批次
            tasks = [simulator.start() for simulator in batch]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计启动结果
            success_count = sum(1 for r in results if r is True)
            failed_count = len(results) - success_count

            completed = min(i + batch_size, len(self.simulators))
            logger.info(f"✅ 批次完成: 成功 {success_count}, 失败 {failed_count}")
            logger.info(f"📊 总进度: {completed}/{len(self.simulators)} ({completed/len(self.simulators)*100:.1f}%)")

            # 批次间延迟
            if i + batch_size < len(self.simulators):
                logger.info(f"⏳ 等待2秒后启动下一批次...")
                await asyncio.sleep(2)
        
        # 运行测试
        logger.info("=" * 80)
        logger.info(f"🔄 压力测试正式开始，持续 {duration} 秒")
        logger.info("=" * 80)

        # 立即显示初始状态
        await self._report_status()

        # 定期报告状态
        start_time = time.time()
        report_interval = 15  # 每15秒报告一次状态
        next_report_time = start_time + report_interval

        while time.time() - start_time < duration and self.is_running:
            await asyncio.sleep(1)  # 更频繁检查，但不频繁报告

            current_time = time.time()
            if current_time >= next_report_time:
                elapsed = current_time - start_time
                remaining = duration - elapsed
                logger.info(f"⏱️  测试进度: {elapsed:.0f}s / {duration}s ({elapsed/duration*100:.1f}%), 剩余: {remaining:.0f}s")
                await self._report_status()
                next_report_time = current_time + report_interval
        
        # 停止所有模拟器
        await self.stop_all()
        
        # 生成最终报告
        return self._generate_report()
    
    async def stop_all(self):
        """停止所有模拟器"""
        self.is_running = False
        if self.simulators:
            logger.info("🔄 正在停止所有模拟器...")
            tasks = [simulator.stop() for simulator in self.simulators]
            await asyncio.gather(*tasks, return_exceptions=True)
            logger.info("✅ 所有模拟器已停止")
    
    async def _report_status(self):
        """报告当前状态"""
        if not self.simulators:
            return

        # 基础统计
        total_servers = len(self.simulators)
        running_servers = sum(1 for s in self.simulators if s.is_running)
        offline_servers = total_servers - running_servers

        # 设备和Hub统计
        total_devices = sum(s.device_count for s in self.simulators)
        total_hubs = running_servers  # 每个运行中的从服务器有1个Hub

        # 注册统计
        total_register_attempts = sum(s.stats['register_attempts'] for s in self.simulators)
        total_register_success = sum(s.stats['register_success'] for s in self.simulators)
        register_success_rate = (total_register_success / max(total_register_attempts, 1)) * 100

        # 心跳统计
        total_heartbeats = sum(s.stats['heartbeat_sent'] for s in self.simulators)
        total_heartbeat_success = sum(s.stats['heartbeat_success'] for s in self.simulators)
        total_timeouts = sum(s.stats['timeout_count'] for s in self.simulators)

        heartbeat_success_rate = (total_heartbeat_success / max(total_heartbeats, 1)) * 100
        timeout_rate = (total_timeouts / max(total_heartbeats, 1)) * 100

        # 响应时间统计
        avg_response_times = [s.stats['avg_response_time'] for s in self.simulators if s.stats['avg_response_time'] > 0]
        overall_avg_response = sum(avg_response_times) / max(len(avg_response_times), 1)

        # 详细状态报告
        logger.info("=" * 80)
        logger.info("📊 OmniLink压力测试实时状态报告")
        logger.info("=" * 80)
        logger.info(f"🖥️  从服务器状态:")
        logger.info(f"   ├─ 总数量: {total_servers}")
        logger.info(f"   ├─ 运行中: {running_servers}")
        logger.info(f"   └─ 离线数: {offline_servers}")
        logger.info(f"")
        logger.info(f"🔌 硬件模拟状态:")
        logger.info(f"   ├─ 模拟Hub数量: {total_hubs}")
        logger.info(f"   ├─ 模拟USB设备: {total_devices}")
        logger.info(f"   └─ 平均每服务器设备数: {total_devices/max(total_servers, 1):.1f}")
        logger.info(f"")
        logger.info(f"📝 注册性能:")
        logger.info(f"   ├─ 注册尝试: {total_register_attempts}")
        logger.info(f"   ├─ 注册成功: {total_register_success}")
        logger.info(f"   └─ 注册成功率: {register_success_rate:.1f}%")
        logger.info(f"")
        logger.info(f"💓 心跳性能:")
        logger.info(f"   ├─ 心跳发送: {total_heartbeats}")
        logger.info(f"   ├─ 心跳成功: {total_heartbeat_success}")
        logger.info(f"   ├─ 心跳成功率: {heartbeat_success_rate:.1f}%")
        logger.info(f"   ├─ 超时次数: {total_timeouts}")
        logger.info(f"   ├─ 超时率: {timeout_rate:.1f}%")
        logger.info(f"   └─ 平均响应时间: {overall_avg_response:.3f}s")
        logger.info("=" * 80)
    
    def _generate_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        if not self.simulators:
            return {}
        
        # 聚合所有统计信息
        total_stats = {
            'total_servers': len(self.simulators),
            'total_devices': sum(s.device_count for s in self.simulators),
            'total_heartbeats': sum(s.stats['heartbeat_sent'] for s in self.simulators),
            'total_success': sum(s.stats['heartbeat_success'] for s in self.simulators),
            'total_failed': sum(s.stats['heartbeat_failed'] for s in self.simulators),
            'total_timeouts': sum(s.stats['timeout_count'] for s in self.simulators),
            'total_register_attempts': sum(s.stats['register_attempts'] for s in self.simulators),
            'total_register_success': sum(s.stats['register_success'] for s in self.simulators)
        }
        
        # 计算比率
        total_stats['heartbeat_success_rate'] = (
            total_stats['total_success'] / max(total_stats['total_heartbeats'], 1) * 100
        )
        total_stats['timeout_rate'] = (
            total_stats['total_timeouts'] / max(total_stats['total_heartbeats'], 1) * 100
        )
        total_stats['register_success_rate'] = (
            total_stats['total_register_success'] / max(total_stats['total_register_attempts'], 1) * 100
        )
        
        # 响应时间统计
        response_times = [s.stats['avg_response_time'] for s in self.simulators if s.stats['avg_response_time'] > 0]
        if response_times:
            total_stats['avg_response_time'] = sum(response_times) / len(response_times)
            total_stats['max_response_time'] = max(s.stats['max_response_time'] for s in self.simulators)
        else:
            total_stats['avg_response_time'] = 0
            total_stats['max_response_time'] = 0
        
        return total_stats

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='OmniLink优化压力测试')
    parser.add_argument('--stage', type=int, default=1, help='测试阶段 (1-4)')
    parser.add_argument('--servers', type=int, help='服务器数量')
    parser.add_argument('--duration', type=int, default=120, help='测试持续时间(秒)')
    parser.add_argument('--master-url', default='http://localhost:8000', help='主服务器URL')
    
    args = parser.parse_args()
    
    # 预定义的测试阶段
    stages = {
        1: 50,   # 阶段1: 50台
        2: 100,  # 阶段2: 100台
        3: 200,  # 阶段3: 200台
        4: 500   # 阶段4: 500台
    }
    
    server_count = args.servers or stages.get(args.stage, 50)
    
    logger.info(f"🎯 开始OmniLink优化压力测试")
    logger.info(f"📋 测试配置: 阶段{args.stage}, {server_count}台服务器, {args.duration}秒")
    
    controller = PressureTestController(args.master_url)
    
    try:
        report = await controller.run_stage_test(server_count, args.duration)
        
        # 输出详细测试报告
        logger.info("=" * 80)
        logger.info("📊 OmniLink压力测试最终报告")
        logger.info("=" * 80)
        logger.info(f"🖥️  从服务器统计:")
        logger.info(f"   ├─ 总服务器数: {report.get('total_servers', 0)}")
        logger.info(f"   ├─ 注册尝试: {report.get('total_register_attempts', 0)}")
        logger.info(f"   ├─ 注册成功: {report.get('total_register_success', 0)}")
        logger.info(f"   └─ 注册成功率: {report.get('register_success_rate', 0):.2f}%")
        logger.info(f"")
        logger.info(f"🔌 硬件模拟统计:")
        logger.info(f"   ├─ 总Hub数: {report.get('total_servers', 0)}")
        logger.info(f"   ├─ 总USB设备数: {report.get('total_devices', 0)}")
        logger.info(f"   └─ 平均每服务器设备数: {report.get('total_devices', 0)/max(report.get('total_servers', 1), 1):.1f}")
        logger.info(f"")
        logger.info(f"💓 心跳性能统计:")
        logger.info(f"   ├─ 心跳发送总数: {report.get('total_heartbeats', 0)}")
        logger.info(f"   ├─ 心跳成功数: {report.get('total_success', 0)}")
        logger.info(f"   ├─ 心跳失败数: {report.get('total_failed', 0)}")
        logger.info(f"   ├─ 心跳成功率: {report.get('heartbeat_success_rate', 0):.2f}%")
        logger.info(f"   ├─ 超时次数: {report.get('total_timeouts', 0)}")
        logger.info(f"   └─ 超时率: {report.get('timeout_rate', 0):.2f}%")
        logger.info(f"")
        logger.info(f"⚡ 响应时间统计:")
        logger.info(f"   ├─ 平均响应时间: {report.get('avg_response_time', 0):.3f}s")
        logger.info(f"   └─ 最大响应时间: {report.get('max_response_time', 0):.3f}s")
        
        # 判断测试结果
        success_rate = report.get('heartbeat_success_rate', 0)
        register_rate = report.get('register_success_rate', 0)
        timeout_rate = report.get('timeout_rate', 0)
        avg_response = report.get('avg_response_time', 0)

        logger.info("=" * 80)
        logger.info("🎯 测试结果评估")
        logger.info("=" * 80)

        # 详细的性能评估
        criteria_passed = 0
        total_criteria = 4

        logger.info(f"📋 性能指标评估:")

        # 1. 注册成功率
        if register_rate >= 95:
            logger.info(f"   ✅ 注册成功率: {register_rate:.2f}% (优秀 ≥95%)")
            criteria_passed += 1
        elif register_rate >= 90:
            logger.info(f"   ⚠️  注册成功率: {register_rate:.2f}% (良好 ≥90%)")
            criteria_passed += 0.5
        else:
            logger.info(f"   ❌ 注册成功率: {register_rate:.2f}% (不达标 <90%)")

        # 2. 心跳成功率
        if success_rate >= 95:
            logger.info(f"   ✅ 心跳成功率: {success_rate:.2f}% (优秀 ≥95%)")
            criteria_passed += 1
        elif success_rate >= 90:
            logger.info(f"   ⚠️  心跳成功率: {success_rate:.2f}% (良好 ≥90%)")
            criteria_passed += 0.5
        else:
            logger.info(f"   ❌ 心跳成功率: {success_rate:.2f}% (不达标 <90%)")

        # 3. 超时率
        if timeout_rate <= 5:
            logger.info(f"   ✅ 超时率: {timeout_rate:.2f}% (优秀 ≤5%)")
            criteria_passed += 1
        elif timeout_rate <= 10:
            logger.info(f"   ⚠️  超时率: {timeout_rate:.2f}% (良好 ≤10%)")
            criteria_passed += 0.5
        else:
            logger.info(f"   ❌ 超时率: {timeout_rate:.2f}% (不达标 >10%)")

        # 4. 响应时间
        if avg_response <= 0.1:
            logger.info(f"   ✅ 平均响应时间: {avg_response:.3f}s (优秀 ≤0.1s)")
            criteria_passed += 1
        elif avg_response <= 0.5:
            logger.info(f"   ⚠️  平均响应时间: {avg_response:.3f}s (良好 ≤0.5s)")
            criteria_passed += 0.5
        else:
            logger.info(f"   ❌ 平均响应时间: {avg_response:.3f}s (不达标 >0.5s)")

        logger.info(f"")
        logger.info(f"📊 综合评分: {criteria_passed}/{total_criteria} ({criteria_passed/total_criteria*100:.1f}%)")

        # 最终结论
        if criteria_passed >= 3.5:
            logger.info("🎉 测试结果: 优秀！系统性能表现卓越")
        elif criteria_passed >= 2.5:
            logger.info("✅ 测试结果: 良好！系统性能达到预期")
        elif criteria_passed >= 1.5:
            logger.info("⚠️  测试结果: 一般，部分指标需要优化")
        else:
            logger.info("❌ 测试结果: 不达标，系统需要重大优化")

        logger.info("=" * 80)
        
    except KeyboardInterrupt:
        logger.info("🔄 测试被用户中断")
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
    finally:
        await controller.stop_all()

if __name__ == "__main__":
    asyncio.run(main())
