#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个从服务器模拟器测试脚本
用于验证数据包格式正确性
"""

import asyncio
import aiohttp
import time
import logging
import json
from datetime import datetime
import uuid

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SingleSlaveTest:
    """单个从服务器测试"""
    
    def __init__(self, master_url: str = "http://localhost:8000"):
        self.master_url = master_url
        self.server_name = "OmniLink-STRESS-TEST-00001"
        self.hardware_uuid = str(uuid.uuid4())
        self.session = None
        
    async def start(self):
        """启动测试"""
        # 创建HTTP会话
        timeout = aiohttp.ClientTimeout(total=30.0)
        self.session = aiohttp.ClientSession(timeout=timeout)
        
        logger.info("🚀 开始单个从服务器测试")
        
        # 测试注册
        logger.info("📝 测试注册...")
        register_success = await self._test_register()
        
        if register_success:
            logger.info("✅ 注册成功，测试数据同步...")
            # 测试数据同步（包含USB设备自动注册）
            sync_success = await self._test_data_sync()

            if sync_success:
                logger.info("✅ 数据同步成功，测试心跳...")
                # 测试心跳
                heartbeat_success = await self._test_heartbeat()

                if heartbeat_success:
                    logger.info("✅ 完整工作流程测试成功")
                    return True
                else:
                    logger.error("❌ 心跳失败")
                    return False
            else:
                logger.error("❌ 数据同步失败")
                return False
        else:
            logger.error("❌ 注册失败")
            return False
    
    async def stop(self):
        """停止测试"""
        if self.session:
            await self.session.close()
    
    async def _test_register(self) -> bool:
        """测试注册"""
        register_data = {
            "server_name": self.server_name,
            "server_ip": "auto-detect",
            "server_port": 8889,
            "vh_port": 7575,
            "hardware_uuid": self.hardware_uuid,
            "hardware_info": {
                "os": "Ubuntu 20.04 LTS",
                "arch": "x86_64",
                "python_version": "3.11.0",
                "total_memory": "8GB",
                "cpu_cores": 4,
                "hostname": "stress-test-1",
                "kernel": "5.4.0-generic"
            },
            "description": "OmniLink Stress Test Simulator #1",
            "version": "2.0"
        }
        
        try:
            logger.info(f"发送注册请求到: {self.master_url}/api/v1/slave/register")
            logger.info(f"注册数据: {json.dumps(register_data, indent=2, ensure_ascii=False)}")
            
            async with self.session.post(
                f"{self.master_url}/api/v1/slave/register",
                json=register_data,
                headers={'Content-Type': 'application/json; charset=utf-8'}
            ) as response:
                response_text = await response.text()
                
                logger.info(f"注册响应状态: {response.status}")
                logger.info(f"注册响应内容: {response_text}")
                
                if response.status == 200:
                    logger.info("✅ 注册成功")
                    return True
                else:
                    logger.error(f"❌ 注册失败: HTTP {response.status}")
                    logger.error(f"错误详情: {response_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 注册异常: {e}")
            return False

    async def _test_data_sync(self) -> bool:
        """测试数据同步（包含USB设备自动注册）"""
        # 模拟3个USB设备
        device_details = [
            {
                "hardware_signature": "096e:031b:SN00001001",
                "vendor_id": 2414,  # 0x096e
                "product_id": 795,   # 0x031b
                "bus": 1,
                "address": 1,
                "description": "广联达加密锁",
                "device_type": "encryption_dongle",
                "device_id": "usb-096e:031b-1",
                "serial_number": "SN00001001",
                "manufacturer": "广联达",
                "product": "广联达加密锁",
                "port_number": 1,
                "hub_port": "1-1",
                "device_path": "/dev/bus/usb/001/001",
                "is_available": True,
                "is_real_hardware": True,
                "auto_bind_eligible": True,
                "usb_ids_vendor_name": "广联达",
                "usb_ids_device_name": "广联达加密锁",
                "usb_ids_full_name": "广联达 广联达加密锁",
                "identification_source": "usb_ids_database"
            },
            {
                "hardware_signature": "0471:0888:SN00001002",
                "vendor_id": 1137,  # 0x0471
                "product_id": 2184,  # 0x0888
                "bus": 1,
                "address": 2,
                "description": "新点加密锁",
                "device_type": "encryption_dongle",
                "device_id": "usb-0471:0888-2",
                "serial_number": "SN00001002",
                "manufacturer": "新点",
                "product": "新点加密锁",
                "port_number": 2,
                "hub_port": "1-2",
                "device_path": "/dev/bus/usb/001/002",
                "is_available": True,
                "is_real_hardware": True,
                "auto_bind_eligible": True,
                "usb_ids_vendor_name": "新点",
                "usb_ids_device_name": "新点加密锁",
                "usb_ids_full_name": "新点 新点加密锁",
                "identification_source": "usb_ids_database"
            },
            {
                "hardware_signature": "1a86:7523:SN00001003",
                "vendor_id": 6790,  # 0x1a86
                "product_id": 29987, # 0x7523
                "bus": 1,
                "address": 3,
                "description": "博威加密锁",
                "device_type": "encryption_dongle",
                "device_id": "usb-1a86:7523-3",
                "serial_number": "SN00001003",
                "manufacturer": "博威",
                "product": "博威加密锁",
                "port_number": 3,
                "hub_port": "1-3",
                "device_path": "/dev/bus/usb/001/003",
                "is_available": True,
                "is_real_hardware": True,
                "auto_bind_eligible": True,
                "usb_ids_vendor_name": "博威",
                "usb_ids_device_name": "博威加密锁",
                "usb_ids_full_name": "博威 博威加密锁",
                "identification_source": "usb_ids_database"
            }
        ]

        # USB拓扑信息
        usb_topology = {
            "hub_count": 1,
            "total_ports": 5,
            "occupied_ports": 3,
            "free_ports": 2,
            "root_hubs": [
                {
                    "hub_id": "1-0:1.0",
                    "port_count": 5,
                    "occupied_ports": 3,
                    "free_ports": 2
                }
            ]
        }

        sync_data = {
            "timestamp": datetime.now().isoformat(),
            "sync_type": "full_data",
            "device_count": 3,
            "usb_topology": usb_topology,
            "device_details": device_details,
            "device_summary": {
                "total_devices": 3,
                "real_hardware_count": 3,
                "auto_bind_eligible_count": 3,
                "device_types": {
                    "encryption_dongle": 3
                }
            }
        }

        try:
            logger.info(f"发送数据同步请求到: {self.master_url}/api/v1/slave/data-sync")
            logger.info(f"同步数据: 设备数量={len(device_details)}, Hub数量={usb_topology['hub_count']}")

            async with self.session.post(
                f"{self.master_url}/api/v1/slave/data-sync",
                json=sync_data,
                headers={'Content-Type': 'application/json; charset=utf-8'}
            ) as response:
                response_text = await response.text()

                logger.info(f"数据同步响应状态: {response.status}")
                logger.info(f"数据同步响应内容: {response_text}")

                if response.status == 200:
                    result = await response.json()
                    sync_result = result.get('sync_result', {})
                    created = sync_result.get('created', 0)
                    updated = sync_result.get('updated', 0)
                    logger.info(f"✅ 数据同步成功 - 创建设备: {created}, 更新设备: {updated}")
                    return True
                else:
                    logger.error(f"❌ 数据同步失败: HTTP {response.status}")
                    logger.error(f"错误详情: {response_text}")
                    return False

        except Exception as e:
            logger.error(f"❌ 数据同步异常: {e}")
            return False
    
    async def _test_heartbeat(self) -> bool:
        """测试心跳"""
        heartbeat_data = {
            "heartbeat_type": "lightweight",
            "timestamp": datetime.now().isoformat(),
            "status": "online",
            "device_count_summary": 3,
            "hub_count": 1,
            "total_ports": 5,
            "occupied_ports": 3,
            "free_ports": 2,
            "server_name": self.server_name,
            "server_port": 8889,
            "vh_port": 7575,
            "server_ip": "*********",
            "vh_status": "running",
            "system_info": {
                "cpu_usage": 25.5,
                "memory_usage": 45.2,
                "disk_usage": 60.1,
                "load_average": 1.2,
                "uptime": 7200
            }
        }
        
        try:
            logger.info(f"发送心跳请求到: {self.master_url}/api/v1/slave/heartbeat")
            logger.info(f"心跳数据: {json.dumps(heartbeat_data, indent=2, ensure_ascii=False)}")
            
            async with self.session.post(
                f"{self.master_url}/api/v1/slave/heartbeat",
                json=heartbeat_data,
                headers={'Content-Type': 'application/json; charset=utf-8'}
            ) as response:
                response_text = await response.text()
                
                logger.info(f"心跳响应状态: {response.status}")
                logger.info(f"心跳响应内容: {response_text}")
                
                if response.status == 200:
                    logger.info("✅ 心跳成功")
                    return True
                else:
                    logger.error(f"❌ 心跳失败: HTTP {response.status}")
                    logger.error(f"错误详情: {response_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 心跳异常: {e}")
            return False

async def main():
    """主函数"""
    test = SingleSlaveTest()
    
    try:
        success = await test.start()
        if success:
            logger.info("🎉 单个从服务器测试通过，数据包格式正确")
        else:
            logger.error("💥 单个从服务器测试失败，需要检查数据包格式")
    finally:
        await test.stop()

if __name__ == "__main__":
    asyncio.run(main())
