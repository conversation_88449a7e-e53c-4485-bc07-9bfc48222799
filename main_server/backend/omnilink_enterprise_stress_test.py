#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniLink企业级压力测试系统 v3.0
支持大规模从服务器部署测试（最高10,000台）
解决注册唯一性问题并实现完整的USB设备管理工作流程
"""

import asyncio
import aiohttp
import time
import random
import logging
import json
import argparse
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import uuid
import threading
from concurrent.futures import ThreadPoolExecutor
import signal
import sys
import hashlib
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'enterprise_stress_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class USBDeviceDatabase:
    """USB设备数据库 - 模拟真实的USB设备类型"""
    
    DEVICE_TYPES = {
        "encryption_dongle": [
            {"vendor_id": 0x096e, "product_id": 0x031b, "name": "广联达加密锁", "manufacturer": "广联达"},
            {"vendor_id": 0x0471, "product_id": 0x0888, "name": "新点加密锁", "manufacturer": "新点"},
            {"vendor_id": 0x1a86, "product_id": 0x7523, "name": "博威加密锁", "manufacturer": "博威"},
            {"vendor_id": 0x0483, "product_id": 0x5740, "name": "CA证书锁", "manufacturer": "CA厂商"},
            {"vendor_id": 0x096e, "product_id": 0x0006, "name": "银行U盾", "manufacturer": "银行"},
            {"vendor_id": 0x1234, "product_id": 0x5678, "name": "通用加密锁", "manufacturer": "通用"},
            {"vendor_id": 0x2468, "product_id": 0x1357, "name": "财务加密锁", "manufacturer": "财务"},
            {"vendor_id": 0x1111, "product_id": 0x2222, "name": "税务加密锁", "manufacturer": "税务"},
        ],
        "storage_device": [
            {"vendor_id": 0x0781, "product_id": 0x5567, "name": "SanDisk USB", "manufacturer": "SanDisk"},
            {"vendor_id": 0x0930, "product_id": 0x6545, "name": "Toshiba USB", "manufacturer": "Toshiba"},
        ],
        "input_device": [
            {"vendor_id": 0x046d, "product_id": 0xc52b, "name": "Logitech Mouse", "manufacturer": "Logitech"},
            {"vendor_id": 0x413c, "product_id": 0x2113, "name": "Dell Keyboard", "manufacturer": "Dell"},
        ]
    }
    
    @classmethod
    def generate_random_device(cls, port_number: int, server_id: int) -> Dict[str, Any]:
        """生成随机USB设备"""
        # 优先生成加密锁设备（80%概率）
        if random.random() < 0.8:
            device_info = random.choice(cls.DEVICE_TYPES["encryption_dongle"])
        else:
            category = random.choice(["storage_device", "input_device"])
            device_info = random.choice(cls.DEVICE_TYPES[category])
        
        # 生成唯一的序列号
        serial_number = f"SN{server_id:05d}{port_number:02d}{random.randint(1000, 9999)}"
        
        # 生成硬件签名
        hardware_signature = f"{device_info['vendor_id']:04x}:{device_info['product_id']:04x}:{serial_number}"
        
        return {
            "hardware_signature": hardware_signature,
            "vendor_id": device_info["vendor_id"],
            "product_id": device_info["product_id"],
            "bus": 1,
            "address": port_number,
            "description": device_info["name"],
            "device_type": "encryption_dongle" if "加密锁" in device_info["name"] or "U盾" in device_info["name"] else "other",
            "device_id": f"usb-{device_info['vendor_id']:04x}:{device_info['product_id']:04x}-{port_number}",
            "serial_number": serial_number,
            "manufacturer": device_info["manufacturer"],
            "product": device_info["name"],
            "port_number": port_number,
            "hub_port": f"1-{port_number}",
            "device_path": f"/dev/bus/usb/001/{port_number:03d}",
            "is_available": True,
            "is_real_hardware": True,
            "auto_bind_eligible": True,
            "usb_ids_vendor_name": device_info["manufacturer"],
            "usb_ids_device_name": device_info["name"],
            "usb_ids_full_name": f"{device_info['manufacturer']} {device_info['name']}",
            "identification_source": "usb_ids_database"
        }

class EnterpriseSlaveSimulator:
    """企业级从服务器模拟器"""
    
    def __init__(self, server_id: int, master_url: str = "http://localhost:8000", 
                 behavior_mode: str = "stable"):
        self.server_id = server_id
        self.master_url = master_url
        self.behavior_mode = behavior_mode  # stable, restart, dynamic_devices, offline
        
        # 生成唯一标识
        self.server_name = f"OmniLink-STRESS-{server_id:05d}"
        self.hardware_uuid = str(uuid.uuid4())
        
        # 生成唯一的IP地址（使用127.0.x.x网段）
        ip_third = ((server_id - 1) // 254) + 1
        ip_fourth = ((server_id - 1) % 254) + 2
        self.server_ip = f"127.0.{ip_third}.{ip_fourth}"
        
        # 生成唯一的端口
        self.server_port = 8889 + server_id
        self.vh_port = 7575 + server_id
        
        # 生成MAC地址
        mac_bytes = [0x02, 0x00, 0x00] + [(server_id >> 16) & 0xff, (server_id >> 8) & 0xff, server_id & 0xff]
        self.mac_address = ":".join([f"{b:02x}" for b in mac_bytes])
        
        # USB设备配置
        self.device_count = random.randint(3, 8)  # 每台服务器3-8个设备
        self.devices = self._generate_devices()
        
        # 运行状态
        self.is_running = False
        self.session: Optional[aiohttp.ClientSession] = None
        self.last_heartbeat_time = None
        self.restart_cycle_count = 0
        
        # 统计信息
        self.stats = {
            'heartbeat_sent': 0,
            'heartbeat_success': 0,
            'heartbeat_failed': 0,
            'register_attempts': 0,
            'register_success': 0,
            'data_sync_attempts': 0,
            'data_sync_success': 0,
            'avg_response_time': 0.0,
            'total_response_time': 0.0,
            'max_response_time': 0.0,
            'timeout_count': 0,
            'device_changes': 0
        }
        
        logger.info(f"初始化企业级模拟器 {self.server_name}: IP={self.server_ip}:{self.server_port}, "
                   f"设备数量={self.device_count}, 行为模式={behavior_mode}")
    
    def _generate_devices(self) -> List[Dict[str, Any]]:
        """生成USB设备列表"""
        devices = []
        for port in range(1, self.device_count + 1):
            device = USBDeviceDatabase.generate_random_device(port, self.server_id)
            devices.append(device)
        return devices
    
    def _generate_hardware_info(self) -> Dict[str, Any]:
        """生成硬件信息"""
        return {
            "os": "Ubuntu 20.04 LTS",
            "arch": "x86_64",
            "python_version": "3.11.0",
            "total_memory": f"{random.choice([4, 8, 16, 32])}GB",
            "cpu_cores": random.choice([2, 4, 8, 16]),
            "hostname": f"omnilink-stress-{self.server_id:05d}",
            "kernel": "5.4.0-generic",
            "mac_address": self.mac_address,
            "disk_space": f"{random.choice([128, 256, 512, 1024])}GB",
            "network_interfaces": [
                {
                    "name": "eth0",
                    "mac": self.mac_address,
                    "ip": self.server_ip,
                    "status": "up"
                }
            ]
        }
    
    async def start(self):
        """启动模拟器"""
        if self.is_running:
            return
        
        # 创建HTTP会话
        timeout = aiohttp.ClientTimeout(
            total=30.0,
            connect=10.0,
            sock_read=30.0
        )
        
        connector = aiohttp.TCPConnector(
            limit=20,
            limit_per_host=10,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            connector=connector
        )
        
        self.is_running = True
        
        # 注册到主服务器
        success = await self._register()
        if success:
            # 发送完整数据同步
            sync_success = await self._send_data_sync()
            if sync_success:
                # 启动行为模式任务
                asyncio.create_task(self._behavior_loop())
                logger.info(f"✅ {self.server_name} 启动成功（已注册 {self.device_count} 个设备）")
                return True
            else:
                logger.error(f"❌ {self.server_name} 数据同步失败")
        else:
            logger.error(f"❌ {self.server_name} 注册失败")
        
        return False
    
    async def stop(self):
        """停止模拟器"""
        self.is_running = False
        if self.session:
            await self.session.close()
        logger.info(f"🔄 {self.server_name} 已停止")
    
    async def _register(self) -> bool:
        """注册到主服务器"""
        self.stats['register_attempts'] += 1
        
        register_data = {
            "server_name": self.server_name,
            "server_ip": self.server_ip,  # 使用唯一IP
            "server_port": self.server_port,  # 使用唯一端口
            "vh_port": self.vh_port,
            "hardware_uuid": self.hardware_uuid,
            "hardware_info": self._generate_hardware_info(),
            "description": f"OmniLink Enterprise Stress Test Server #{self.server_id} (IP: {self.server_ip})",
            "version": "2.0"
        }
        
        try:
            start_time = time.time()
            async with self.session.post(
                f"{self.master_url}/api/v1/slave/register",
                json=register_data,
                headers={'Content-Type': 'application/json; charset=utf-8'}
            ) as response:
                response_time = time.time() - start_time
                self._update_response_stats(response_time)
                
                if response.status == 200:
                    self.stats['register_success'] += 1
                    result = await response.json()
                    logger.info(f"✅ {self.server_name} 注册成功 ({response_time:.3f}s) - {result.get('action', 'unknown')}")
                    return True
                else:
                    response_text = await response.text()
                    logger.error(f"❌ {self.server_name} 注册失败: HTTP {response.status}")
                    logger.error(f"错误详情: {response_text}")
                    return False
                    
        except asyncio.TimeoutError:
            self.stats['timeout_count'] += 1
            logger.error(f"⏰ {self.server_name} 注册超时")
            return False
        except Exception as e:
            logger.error(f"❌ {self.server_name} 注册异常: {e}")
            return False

    async def _send_data_sync(self) -> bool:
        """发送完整数据同步（包含USB设备信息）"""
        self.stats['data_sync_attempts'] += 1

        try:
            # 构建USB拓扑信息
            usb_topology = {
                "hub_count": 1,
                "total_ports": self.device_count + 2,
                "occupied_ports": self.device_count,
                "free_ports": 2,
                "root_hubs": [
                    {
                        "hub_id": "1-0:1.0",
                        "port_count": self.device_count + 2,
                        "occupied_ports": self.device_count,
                        "free_ports": 2
                    }
                ]
            }

            # 构建设备摘要
            device_summary = {
                "total_devices": self.device_count,
                "real_hardware_count": self.device_count,
                "auto_bind_eligible_count": len([d for d in self.devices if d.get("auto_bind_eligible", False)]),
                "device_types": {}
            }

            # 统计设备类型
            for device in self.devices:
                device_type = device.get("device_type", "other")
                device_summary["device_types"][device_type] = device_summary["device_types"].get(device_type, 0) + 1

            # 构建完整数据同步请求
            sync_data = {
                "timestamp": datetime.now().isoformat(),
                "sync_type": "full_data",
                "device_count": self.device_count,
                "usb_topology": usb_topology,
                "device_details": self.devices,
                "device_summary": device_summary
            }

            start_time = time.time()
            async with self.session.post(
                f"{self.master_url}/api/v1/slave/data-sync",
                json=sync_data,
                headers={'Content-Type': 'application/json; charset=utf-8'}
            ) as response:
                response_time = time.time() - start_time
                self._update_response_stats(response_time)

                if response.status == 200:
                    self.stats['data_sync_success'] += 1
                    result = await response.json()
                    sync_result = result.get('sync_result', {})
                    created = sync_result.get('created', 0)
                    updated = sync_result.get('updated', 0)
                    logger.info(f"✅ {self.server_name} 数据同步成功 ({response_time:.3f}s) - 创建设备: {created}, 更新设备: {updated}")
                    return True
                else:
                    response_text = await response.text()
                    logger.error(f"❌ {self.server_name} 数据同步失败: HTTP {response.status}")
                    logger.error(f"错误详情: {response_text}")
                    return False

        except asyncio.TimeoutError:
            self.stats['timeout_count'] += 1
            logger.error(f"⏰ {self.server_name} 数据同步超时")
            return False
        except Exception as e:
            logger.error(f"❌ {self.server_name} 数据同步异常: {e}")
            return False

    async def _behavior_loop(self):
        """行为模式循环"""
        while self.is_running:
            try:
                if self.behavior_mode == "stable":
                    await self._stable_behavior()
                elif self.behavior_mode == "restart":
                    await self._restart_behavior()
                elif self.behavior_mode == "dynamic_devices":
                    await self._dynamic_devices_behavior()
                elif self.behavior_mode == "offline":
                    await self._offline_behavior()
                else:
                    await self._stable_behavior()  # 默认稳定模式

            except Exception as e:
                logger.error(f"❌ {self.server_name} 行为循环异常: {e}")
                await asyncio.sleep(5)

    async def _stable_behavior(self):
        """稳定模式：正常心跳"""
        await self._send_heartbeat()
        # 心跳间隔30秒，添加随机抖动
        interval = 30 + random.uniform(-3, 3)
        await asyncio.sleep(interval)

    async def _restart_behavior(self):
        """重启模式：2次心跳后掉线，等待1个心跳周期后重新上线"""
        if self.restart_cycle_count < 2:
            await self._send_heartbeat()
            self.restart_cycle_count += 1
            await asyncio.sleep(30 + random.uniform(-3, 3))
        else:
            # 模拟掉线
            logger.info(f"🔄 {self.server_name} 模拟重启掉线")
            await asyncio.sleep(30)  # 掉线1个心跳周期

            # 重新注册
            logger.info(f"🔄 {self.server_name} 重启后重新上线")
            await self._register()
            await self._send_data_sync()
            self.restart_cycle_count = 0

    async def _dynamic_devices_behavior(self):
        """动态设备模式：2次心跳后随机增减设备"""
        if self.restart_cycle_count < 2:
            await self._send_heartbeat()
            self.restart_cycle_count += 1
            await asyncio.sleep(30 + random.uniform(-3, 3))
        else:
            # 随机增减设备
            change_count = random.randint(1, 5)
            if random.random() < 0.5 and len(self.devices) > 1:
                # 减少设备
                removed_devices = random.sample(self.devices, min(change_count, len(self.devices) - 1))
                for device in removed_devices:
                    self.devices.remove(device)
                logger.info(f"🔄 {self.server_name} 移除了 {len(removed_devices)} 个设备")
            else:
                # 增加设备
                for i in range(change_count):
                    new_port = len(self.devices) + 1
                    new_device = USBDeviceDatabase.generate_random_device(new_port, self.server_id)
                    self.devices.append(new_device)
                logger.info(f"🔄 {self.server_name} 添加了 {change_count} 个设备")

            self.device_count = len(self.devices)
            self.stats['device_changes'] += 1

            # 重新同步设备信息
            await self._send_data_sync()
            self.restart_cycle_count = 0

    async def _offline_behavior(self):
        """离线模式：不发送心跳"""
        await asyncio.sleep(60)  # 长时间等待

    async def _send_heartbeat(self):
        """发送心跳"""
        self.stats['heartbeat_sent'] += 1

        heartbeat_data = {
            "heartbeat_type": "lightweight",
            "timestamp": datetime.now().isoformat(),
            "status": "online",
            "device_count_summary": self.device_count,
            "hub_count": 1,
            "total_ports": self.device_count + 2,
            "occupied_ports": self.device_count,
            "free_ports": 2,
            "server_name": self.server_name,
            "server_port": self.server_port,
            "vh_port": self.vh_port,
            "server_ip": self.server_ip,
            "vh_status": "running",
            "system_info": {
                "cpu_usage": round(random.uniform(10, 50), 2),
                "memory_usage": round(random.uniform(20, 60), 2),
                "disk_usage": round(random.uniform(30, 70), 2),
                "load_average": round(random.uniform(0.5, 2.0), 2),
                "uptime": random.randint(3600, 86400),
                "network_status": "connected"
            }
        }

        try:
            start_time = time.time()
            async with self.session.post(
                f"{self.master_url}/api/v1/slave/heartbeat",
                json=heartbeat_data,
                headers={'Content-Type': 'application/json; charset=utf-8'}
            ) as response:
                response_time = time.time() - start_time
                self._update_response_stats(response_time)
                self.last_heartbeat_time = datetime.now()

                if response.status == 200:
                    self.stats['heartbeat_success'] += 1
                    logger.debug(f"💓 {self.server_name} 心跳成功 ({response_time:.3f}s)")
                else:
                    self.stats['heartbeat_failed'] += 1
                    logger.warning(f"💔 {self.server_name} 心跳失败: HTTP {response.status}")

        except asyncio.TimeoutError:
            self.stats['heartbeat_failed'] += 1
            self.stats['timeout_count'] += 1
            logger.error(f"⏰ {self.server_name} 心跳超时")
        except Exception as e:
            self.stats['heartbeat_failed'] += 1
            logger.error(f"❌ {self.server_name} 心跳异常: {e}")

    def _update_response_stats(self, response_time: float):
        """更新响应时间统计"""
        self.stats['total_response_time'] += response_time
        self.stats['max_response_time'] = max(self.stats['max_response_time'], response_time)

        total_requests = (self.stats['heartbeat_sent'] + self.stats['register_attempts'] +
                         self.stats['data_sync_attempts'])
        if total_requests > 0:
            self.stats['avg_response_time'] = self.stats['total_response_time'] / total_requests

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_heartbeats = self.stats['heartbeat_sent']
        heartbeat_success_rate = (self.stats['heartbeat_success'] / max(total_heartbeats, 1)) * 100

        register_success_rate = (self.stats['register_success'] / max(self.stats['register_attempts'], 1)) * 100
        sync_success_rate = (self.stats['data_sync_success'] / max(self.stats['data_sync_attempts'], 1)) * 100

        return {
            **self.stats,
            'server_name': self.server_name,
            'server_ip': self.server_ip,
            'server_port': self.server_port,
            'device_count': self.device_count,
            'behavior_mode': self.behavior_mode,
            'heartbeat_success_rate': heartbeat_success_rate,
            'register_success_rate': register_success_rate,
            'sync_success_rate': sync_success_rate,
            'is_running': self.is_running,
            'last_heartbeat': self.last_heartbeat_time.isoformat() if self.last_heartbeat_time else None
        }

class EnterpriseStressTestController:
    """企业级压力测试控制器"""

    def __init__(self, master_url: str = "http://localhost:8000"):
        self.master_url = master_url
        self.simulators: List[EnterpriseSlaveSimulator] = []
        self.is_running = False
        self.test_start_time = None
        self.test_phases = []

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，正在停止测试...")
        asyncio.create_task(self.stop_all())

    async def run_enterprise_test(self, phase_config: Dict[str, Any]):
        """运行企业级测试"""
        phase_name = phase_config.get("name", "未命名阶段")
        server_count = phase_config.get("server_count", 50)
        duration = phase_config.get("duration", 300)
        behavior_distribution = phase_config.get("behavior_distribution", {"stable": 1.0})

        logger.info("=" * 100)
        logger.info(f"🚀 OmniLink企业级压力测试 - {phase_name}")
        logger.info("=" * 100)

        self.test_start_time = datetime.now()

        # 创建模拟器
        await self._create_simulators(server_count, behavior_distribution)

        # 显示测试配置
        await self._display_test_configuration(phase_name, server_count, duration)

        self.is_running = True

        # 分批启动模拟器
        await self._batch_start_simulators()

        # 运行测试
        await self._run_test_monitoring(duration)

        # 停止所有模拟器
        await self.stop_all()

        # 生成最终报告
        return self._generate_enterprise_report()

    async def _create_simulators(self, server_count: int, behavior_distribution: Dict[str, float]):
        """创建模拟器"""
        self.simulators = []

        # 计算每种行为模式的服务器数量
        behavior_counts = {}
        remaining_count = server_count

        for behavior, ratio in behavior_distribution.items():
            count = int(server_count * ratio)
            behavior_counts[behavior] = count
            remaining_count -= count

        # 将剩余的服务器分配给第一种行为模式
        if remaining_count > 0:
            first_behavior = list(behavior_distribution.keys())[0]
            behavior_counts[first_behavior] += remaining_count

        # 创建模拟器
        server_id = 1
        total_devices = 0

        for behavior, count in behavior_counts.items():
            for _ in range(count):
                simulator = EnterpriseSlaveSimulator(server_id, self.master_url, behavior)
                self.simulators.append(simulator)
                total_devices += simulator.device_count
                server_id += 1

        logger.info(f"📋 创建了 {len(self.simulators)} 个模拟器，总设备数: {total_devices}")

        # 显示行为模式分布
        for behavior, count in behavior_counts.items():
            logger.info(f"   ├─ {behavior} 模式: {count} 台服务器")

    async def _display_test_configuration(self, phase_name: str, server_count: int, duration: int):
        """显示测试配置"""
        total_devices = sum(s.device_count for s in self.simulators)
        total_hubs = len(self.simulators)

        logger.info(f"📋 {phase_name} 测试配置:")
        logger.info(f"   ├─ 从服务器数量: {server_count}")
        logger.info(f"   ├─ 测试持续时间: {duration}秒 ({duration//60}分{duration%60}秒)")
        logger.info(f"   ├─ 模拟Hub总数: {total_hubs}")
        logger.info(f"   ├─ 模拟USB设备总数: {total_devices}")
        logger.info(f"   ├─ 平均每服务器设备数: {total_devices/server_count:.1f}")
        logger.info(f"   ├─ IP地址范围: ********* - 127.0.{((server_count-1)//254)+1}.{((server_count-1)%254)+2}")
        logger.info(f"   ├─ 端口范围: {8889+1} - {8889+server_count}")
        logger.info(f"   └─ 主服务器地址: {self.master_url}")
        logger.info("=" * 100)

    async def _batch_start_simulators(self):
        """分批启动模拟器"""
        batch_size = 20  # 增加批次大小以提高效率
        logger.info(f"🔄 开始分批启动从服务器模拟器（批次大小: {batch_size}）")

        for i in range(0, len(self.simulators), batch_size):
            batch = self.simulators[i:i + batch_size]
            batch_devices = sum(s.device_count for s in batch)

            logger.info(f"📦 启动批次 {i//batch_size + 1}: 服务器 {i+1}-{min(i+batch_size, len(self.simulators))}")
            logger.info(f"   └─ 本批次设备数: {batch_devices}")

            # 并发启动当前批次
            tasks = [simulator.start() for simulator in batch]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计启动结果
            success_count = sum(1 for r in results if r is True)
            failed_count = len(results) - success_count

            completed = min(i + batch_size, len(self.simulators))
            logger.info(f"✅ 批次完成: 成功 {success_count}, 失败 {failed_count}")
            logger.info(f"📊 总进度: {completed}/{len(self.simulators)} ({completed/len(self.simulators)*100:.1f}%)")

            # 批次间延迟
            if i + batch_size < len(self.simulators):
                logger.info(f"⏳ 等待3秒后启动下一批次...")
                await asyncio.sleep(3)

    async def _run_test_monitoring(self, duration: int):
        """运行测试监控"""
        logger.info("=" * 100)
        logger.info(f"🔄 企业级压力测试正式开始，持续 {duration} 秒")
        logger.info("=" * 100)

        # 立即显示初始状态
        await self._report_enterprise_status()

        # 定期报告状态
        start_time = time.time()
        report_interval = 30  # 每30秒报告一次状态
        next_report_time = start_time + report_interval

        while time.time() - start_time < duration and self.is_running:
            await asyncio.sleep(1)

            current_time = time.time()
            if current_time >= next_report_time:
                elapsed = current_time - start_time
                remaining = duration - elapsed
                logger.info(f"⏱️  测试进度: {elapsed:.0f}s / {duration}s ({elapsed/duration*100:.1f}%), 剩余: {remaining:.0f}s")
                await self._report_enterprise_status()
                next_report_time = current_time + report_interval

    async def _report_enterprise_status(self):
        """报告企业级状态"""
        if not self.simulators:
            return

        # 基础统计
        total_servers = len(self.simulators)
        running_servers = sum(1 for s in self.simulators if s.is_running)
        offline_servers = total_servers - running_servers

        # 按行为模式分组统计
        behavior_stats = {}
        for simulator in self.simulators:
            mode = simulator.behavior_mode
            if mode not in behavior_stats:
                behavior_stats[mode] = {"count": 0, "running": 0, "devices": 0}
            behavior_stats[mode]["count"] += 1
            if simulator.is_running:
                behavior_stats[mode]["running"] += 1
            behavior_stats[mode]["devices"] += simulator.device_count

        # 设备和Hub统计
        total_devices = sum(s.device_count for s in self.simulators)
        total_hubs = running_servers

        # 聚合性能统计
        total_register_attempts = sum(s.stats['register_attempts'] for s in self.simulators)
        total_register_success = sum(s.stats['register_success'] for s in self.simulators)
        total_sync_attempts = sum(s.stats['data_sync_attempts'] for s in self.simulators)
        total_sync_success = sum(s.stats['data_sync_success'] for s in self.simulators)
        total_heartbeats = sum(s.stats['heartbeat_sent'] for s in self.simulators)
        total_heartbeat_success = sum(s.stats['heartbeat_success'] for s in self.simulators)
        total_timeouts = sum(s.stats['timeout_count'] for s in self.simulators)
        total_device_changes = sum(s.stats['device_changes'] for s in self.simulators)

        # 计算成功率
        register_success_rate = (total_register_success / max(total_register_attempts, 1)) * 100
        sync_success_rate = (total_sync_success / max(total_sync_attempts, 1)) * 100
        heartbeat_success_rate = (total_heartbeat_success / max(total_heartbeats, 1)) * 100
        timeout_rate = (total_timeouts / max(total_heartbeats, 1)) * 100

        # 响应时间统计
        avg_response_times = [s.stats['avg_response_time'] for s in self.simulators if s.stats['avg_response_time'] > 0]
        overall_avg_response = sum(avg_response_times) / max(len(avg_response_times), 1)
        max_response_time = max([s.stats['max_response_time'] for s in self.simulators], default=0)

        # 详细状态报告
        logger.info("=" * 100)
        logger.info("📊 OmniLink企业级压力测试实时状态报告")
        logger.info("=" * 100)
        logger.info(f"🖥️  从服务器状态:")
        logger.info(f"   ├─ 总数量: {total_servers}")
        logger.info(f"   ├─ 运行中: {running_servers}")
        logger.info(f"   └─ 离线数: {offline_servers}")

        logger.info(f"")
        logger.info(f"🎭 行为模式分布:")
        for mode, stats in behavior_stats.items():
            logger.info(f"   ├─ {mode}: {stats['running']}/{stats['count']} 台运行中, {stats['devices']} 个设备")

        logger.info(f"")
        logger.info(f"🔌 硬件模拟状态:")
        logger.info(f"   ├─ 模拟Hub数量: {total_hubs}")
        logger.info(f"   ├─ 模拟USB设备: {total_devices}")
        logger.info(f"   ├─ 设备动态变化次数: {total_device_changes}")
        logger.info(f"   └─ 平均每服务器设备数: {total_devices/max(total_servers, 1):.1f}")

        logger.info(f"")
        logger.info(f"📝 注册性能:")
        logger.info(f"   ├─ 注册尝试: {total_register_attempts}")
        logger.info(f"   ├─ 注册成功: {total_register_success}")
        logger.info(f"   └─ 注册成功率: {register_success_rate:.1f}%")

        logger.info(f"")
        logger.info(f"🔄 数据同步性能:")
        logger.info(f"   ├─ 同步尝试: {total_sync_attempts}")
        logger.info(f"   ├─ 同步成功: {total_sync_success}")
        logger.info(f"   └─ 同步成功率: {sync_success_rate:.1f}%")

        logger.info(f"")
        logger.info(f"💓 心跳性能:")
        logger.info(f"   ├─ 心跳发送: {total_heartbeats}")
        logger.info(f"   ├─ 心跳成功: {total_heartbeat_success}")
        logger.info(f"   ├─ 心跳成功率: {heartbeat_success_rate:.1f}%")
        logger.info(f"   ├─ 超时次数: {total_timeouts}")
        logger.info(f"   └─ 超时率: {timeout_rate:.1f}%")

        logger.info(f"")
        logger.info(f"⚡ 响应时间统计:")
        logger.info(f"   ├─ 平均响应时间: {overall_avg_response:.3f}s")
        logger.info(f"   └─ 最大响应时间: {max_response_time:.3f}s")
        logger.info("=" * 100)

    async def stop_all(self):
        """停止所有模拟器"""
        self.is_running = False
        if self.simulators:
            logger.info("🔄 正在停止所有模拟器...")
            tasks = [simulator.stop() for simulator in self.simulators]
            await asyncio.gather(*tasks, return_exceptions=True)
            logger.info("✅ 所有模拟器已停止")

    def _generate_enterprise_report(self) -> Dict[str, Any]:
        """生成企业级测试报告"""
        if not self.simulators:
            return {}

        # 聚合所有统计信息
        total_stats = {
            'test_duration': (datetime.now() - self.test_start_time).total_seconds() if self.test_start_time else 0,
            'total_servers': len(self.simulators),
            'total_devices': sum(s.device_count for s in self.simulators),
            'total_heartbeats': sum(s.stats['heartbeat_sent'] for s in self.simulators),
            'total_heartbeat_success': sum(s.stats['heartbeat_success'] for s in self.simulators),
            'total_heartbeat_failed': sum(s.stats['heartbeat_failed'] for s in self.simulators),
            'total_timeouts': sum(s.stats['timeout_count'] for s in self.simulators),
            'total_register_attempts': sum(s.stats['register_attempts'] for s in self.simulators),
            'total_register_success': sum(s.stats['register_success'] for s in self.simulators),
            'total_sync_attempts': sum(s.stats['data_sync_attempts'] for s in self.simulators),
            'total_sync_success': sum(s.stats['data_sync_success'] for s in self.simulators),
            'total_device_changes': sum(s.stats['device_changes'] for s in self.simulators)
        }

        # 计算比率
        total_stats['heartbeat_success_rate'] = (
            total_stats['total_heartbeat_success'] / max(total_stats['total_heartbeats'], 1) * 100
        )
        total_stats['timeout_rate'] = (
            total_stats['total_timeouts'] / max(total_stats['total_heartbeats'], 1) * 100
        )
        total_stats['register_success_rate'] = (
            total_stats['total_register_success'] / max(total_stats['total_register_attempts'], 1) * 100
        )
        total_stats['sync_success_rate'] = (
            total_stats['total_sync_success'] / max(total_stats['total_sync_attempts'], 1) * 100
        )

        # 响应时间统计
        response_times = [s.stats['avg_response_time'] for s in self.simulators if s.stats['avg_response_time'] > 0]
        if response_times:
            total_stats['avg_response_time'] = sum(response_times) / len(response_times)
            total_stats['max_response_time'] = max(s.stats['max_response_time'] for s in self.simulators)
        else:
            total_stats['avg_response_time'] = 0
            total_stats['max_response_time'] = 0

        # 行为模式统计
        behavior_stats = {}
        for simulator in self.simulators:
            mode = simulator.behavior_mode
            if mode not in behavior_stats:
                behavior_stats[mode] = 0
            behavior_stats[mode] += 1
        total_stats['behavior_distribution'] = behavior_stats

        return total_stats

# 预定义的测试阶段配置
ENTERPRISE_TEST_PHASES = {
    "phase1": {
        "name": "第一阶段：基础功能验证（50台）",
        "server_count": 50,
        "duration": 300,  # 5分钟
        "behavior_distribution": {
            "stable": 1.0  # 100%稳定模式
        }
    },
    "phase2": {
        "name": "第二阶段：中等规模测试（150台）",
        "server_count": 150,
        "duration": 600,  # 10分钟
        "behavior_distribution": {
            "stable": 0.95,  # 95%稳定模式
            "restart": 0.03,  # 3%重启模式
            "dynamic_devices": 0.02  # 2%动态设备模式
        }
    },
    "phase3": {
        "name": "第三阶段：大规模稳定性测试（350台）",
        "server_count": 350,
        "duration": 900,  # 15分钟
        "behavior_distribution": {
            "stable": 0.90,  # 90%稳定模式
            "restart": 0.05,  # 5%重启模式
            "dynamic_devices": 0.03,  # 3%动态设备模式
            "offline": 0.02  # 2%离线模式
        }
    },
    "phase4": {
        "name": "第四阶段：极限压力测试（1000台）",
        "server_count": 1000,
        "duration": 1800,  # 30分钟
        "behavior_distribution": {
            "stable": 0.85,  # 85%稳定模式
            "restart": 0.08,  # 8%重启模式
            "dynamic_devices": 0.05,  # 5%动态设备模式
            "offline": 0.02  # 2%离线模式
        }
    },
    "ultimate": {
        "name": "终极测试：万台服务器压力测试（10000台）",
        "server_count": 10000,
        "duration": 3600,  # 60分钟
        "behavior_distribution": {
            "stable": 0.95,  # 95%稳定模式
            "restart": 0.01,  # 1%重启模式
            "dynamic_devices": 0.01,  # 1%动态设备模式
            "offline": 0.02,  # 2%离线模式
            "restart": 0.01  # 1%反复重启模式
        }
    }
}

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='OmniLink企业级压力测试系统 v3.0')
    parser.add_argument('--phase', choices=list(ENTERPRISE_TEST_PHASES.keys()),
                       default='phase1', help='测试阶段')
    parser.add_argument('--servers', type=int, help='自定义服务器数量（覆盖阶段配置）')
    parser.add_argument('--duration', type=int, help='自定义测试持续时间（秒）')
    parser.add_argument('--master-url', default='http://localhost:8000', help='主服务器URL')
    parser.add_argument('--stable-ratio', type=float, help='稳定模式比例（0.0-1.0）')

    args = parser.parse_args()

    # 获取测试配置
    phase_config = ENTERPRISE_TEST_PHASES[args.phase].copy()

    # 应用自定义参数
    if args.servers:
        phase_config['server_count'] = args.servers
    if args.duration:
        phase_config['duration'] = args.duration
    if args.stable_ratio:
        phase_config['behavior_distribution'] = {
            "stable": args.stable_ratio,
            "restart": (1 - args.stable_ratio) * 0.5,
            "dynamic_devices": (1 - args.stable_ratio) * 0.3,
            "offline": (1 - args.stable_ratio) * 0.2
        }

    logger.info(f"🎯 启动OmniLink企业级压力测试系统 v3.0")
    logger.info(f"📋 测试阶段: {args.phase}")
    logger.info(f"🎯 目标: {phase_config['name']}")

    controller = EnterpriseStressTestController(args.master_url)

    try:
        report = await controller.run_enterprise_test(phase_config)

        # 输出详细测试报告
        logger.info("=" * 100)
        logger.info("📊 OmniLink企业级压力测试最终报告")
        logger.info("=" * 100)
        logger.info(f"🖥️  从服务器统计:")
        logger.info(f"   ├─ 总服务器数: {report.get('total_servers', 0)}")
        logger.info(f"   ├─ 注册尝试: {report.get('total_register_attempts', 0)}")
        logger.info(f"   ├─ 注册成功: {report.get('total_register_success', 0)}")
        logger.info(f"   └─ 注册成功率: {report.get('register_success_rate', 0):.2f}%")

        logger.info(f"")
        logger.info(f"🔌 硬件模拟统计:")
        logger.info(f"   ├─ 总Hub数: {report.get('total_servers', 0)}")
        logger.info(f"   ├─ 总USB设备数: {report.get('total_devices', 0)}")
        logger.info(f"   ├─ 设备动态变化: {report.get('total_device_changes', 0)} 次")
        logger.info(f"   └─ 平均每服务器设备数: {report.get('total_devices', 0)/max(report.get('total_servers', 1), 1):.1f}")

        logger.info(f"")
        logger.info(f"🔄 数据同步统计:")
        logger.info(f"   ├─ 同步尝试: {report.get('total_sync_attempts', 0)}")
        logger.info(f"   ├─ 同步成功: {report.get('total_sync_success', 0)}")
        logger.info(f"   └─ 同步成功率: {report.get('sync_success_rate', 0):.2f}%")

        logger.info(f"")
        logger.info(f"💓 心跳性能统计:")
        logger.info(f"   ├─ 心跳发送总数: {report.get('total_heartbeats', 0)}")
        logger.info(f"   ├─ 心跳成功数: {report.get('total_heartbeat_success', 0)}")
        logger.info(f"   ├─ 心跳失败数: {report.get('total_heartbeat_failed', 0)}")
        logger.info(f"   ├─ 心跳成功率: {report.get('heartbeat_success_rate', 0):.2f}%")
        logger.info(f"   ├─ 超时次数: {report.get('total_timeouts', 0)}")
        logger.info(f"   └─ 超时率: {report.get('timeout_rate', 0):.2f}%")

        logger.info(f"")
        logger.info(f"⚡ 响应时间统计:")
        logger.info(f"   ├─ 平均响应时间: {report.get('avg_response_time', 0):.3f}s")
        logger.info(f"   └─ 最大响应时间: {report.get('max_response_time', 0):.3f}s")

        logger.info(f"")
        logger.info(f"⏱️  测试时长: {report.get('test_duration', 0):.0f}秒")

        # 性能评估
        logger.info("=" * 100)
        logger.info("🎯 企业级性能评估")
        logger.info("=" * 100)

        # 评估标准
        criteria_passed = 0
        total_criteria = 5

        register_rate = report.get('register_success_rate', 0)
        sync_rate = report.get('sync_success_rate', 0)
        heartbeat_rate = report.get('heartbeat_success_rate', 0)
        timeout_rate = report.get('timeout_rate', 0)
        avg_response = report.get('avg_response_time', 0)

        logger.info(f"📋 企业级性能指标评估:")

        # 1. 注册成功率
        if register_rate >= 99:
            logger.info(f"   ✅ 注册成功率: {register_rate:.2f}% (企业级 ≥99%)")
            criteria_passed += 1
        elif register_rate >= 95:
            logger.info(f"   ⚠️  注册成功率: {register_rate:.2f}% (良好 ≥95%)")
            criteria_passed += 0.5
        else:
            logger.info(f"   ❌ 注册成功率: {register_rate:.2f}% (不达标 <95%)")

        # 2. 数据同步成功率
        if sync_rate >= 99:
            logger.info(f"   ✅ 数据同步成功率: {sync_rate:.2f}% (企业级 ≥99%)")
            criteria_passed += 1
        elif sync_rate >= 95:
            logger.info(f"   ⚠️  数据同步成功率: {sync_rate:.2f}% (良好 ≥95%)")
            criteria_passed += 0.5
        else:
            logger.info(f"   ❌ 数据同步成功率: {sync_rate:.2f}% (不达标 <95%)")

        # 3. 心跳成功率
        if heartbeat_rate >= 95:
            logger.info(f"   ✅ 心跳成功率: {heartbeat_rate:.2f}% (企业级 ≥95%)")
            criteria_passed += 1
        elif heartbeat_rate >= 90:
            logger.info(f"   ⚠️  心跳成功率: {heartbeat_rate:.2f}% (良好 ≥90%)")
            criteria_passed += 0.5
        else:
            logger.info(f"   ❌ 心跳成功率: {heartbeat_rate:.2f}% (不达标 <90%)")

        # 4. 超时率
        if timeout_rate <= 5:
            logger.info(f"   ✅ 超时率: {timeout_rate:.2f}% (企业级 ≤5%)")
            criteria_passed += 1
        elif timeout_rate <= 10:
            logger.info(f"   ⚠️  超时率: {timeout_rate:.2f}% (良好 ≤10%)")
            criteria_passed += 0.5
        else:
            logger.info(f"   ❌ 超时率: {timeout_rate:.2f}% (不达标 >10%)")

        # 5. 响应时间
        if avg_response <= 0.5:
            logger.info(f"   ✅ 平均响应时间: {avg_response:.3f}s (企业级 ≤0.5s)")
            criteria_passed += 1
        elif avg_response <= 1.0:
            logger.info(f"   ⚠️  平均响应时间: {avg_response:.3f}s (良好 ≤1.0s)")
            criteria_passed += 0.5
        else:
            logger.info(f"   ❌ 平均响应时间: {avg_response:.3f}s (不达标 >1.0s)")

        logger.info(f"")
        logger.info(f"📊 企业级综合评分: {criteria_passed}/{total_criteria} ({criteria_passed/total_criteria*100:.1f}%)")

        # 最终结论
        if criteria_passed >= 4.5:
            logger.info("🎉 测试结果: 企业级优秀！系统完全满足大规模部署要求")
        elif criteria_passed >= 3.5:
            logger.info("✅ 测试结果: 企业级良好！系统基本满足大规模部署要求")
        elif criteria_passed >= 2.5:
            logger.info("⚠️  测试结果: 需要优化，部分指标未达到企业级标准")
        else:
            logger.info("❌ 测试结果: 不满足企业级要求，需要重大优化")

        logger.info("=" * 100)

    except KeyboardInterrupt:
        logger.info("🔄 测试被用户中断")
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await controller.stop_all()

if __name__ == "__main__":
    asyncio.run(main())
