#!/usr/bin/env python3
"""
数据压缩配置版本管理器
版本: 1.0
创建日期: 2025-01-15
描述: 实现压缩配置的版本管理、回滚功能和变更追踪
"""

import json
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass, asdict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from models import CompressionConfig

logger = logging.getLogger(__name__)

@dataclass
class ConfigVersion:
    """配置版本信息"""
    version_id: int
    config_id: int
    version_number: int
    config_data: Dict[str, Any]
    created_at: datetime
    created_by: int
    change_summary: str
    is_active: bool = False
    rollback_reason: Optional[str] = None

class CompressionVersionManager:
    """压缩配置版本管理器"""
    
    def __init__(self):
        self.version_storage: Dict[int, List[ConfigVersion]] = {}
        self.max_versions_per_config = 50  # 每个配置最多保留50个版本
        
        logger.info("压缩配置版本管理器初始化完成")
    
    async def create_version(self, db: AsyncSession, config_id: int, config_data: Dict[str, Any], 
                           created_by: int, change_summary: str = "配置更新") -> ConfigVersion:
        """
        创建新的配置版本
        
        Args:
            db: 数据库会话
            config_id: 配置ID
            config_data: 配置数据
            created_by: 创建者ID
            change_summary: 变更摘要
            
        Returns:
            新创建的版本信息
        """
        try:
            # 获取下一个版本号
            next_version = await self._get_next_version_number(config_id)
            
            # 创建版本对象
            version = ConfigVersion(
                version_id=int(time.time() * 1000),  # 使用时间戳作为版本ID
                config_id=config_id,
                version_number=next_version,
                config_data=config_data.copy(),
                created_at=datetime.now(),
                created_by=created_by,
                change_summary=change_summary,
                is_active=True
            )
            
            # 存储版本
            if config_id not in self.version_storage:
                self.version_storage[config_id] = []
            
            # 将之前的版本设为非活跃
            for v in self.version_storage[config_id]:
                v.is_active = False
            
            # 添加新版本
            self.version_storage[config_id].append(version)
            
            # 清理旧版本
            await self._cleanup_old_versions(config_id)
            
            logger.info(f"创建配置版本: config_id={config_id}, version={next_version}")
            return version
            
        except Exception as e:
            logger.error(f"创建配置版本失败: {e}")
            raise
    
    async def get_version_history(self, config_id: int, limit: int = 20) -> List[ConfigVersion]:
        """
        获取配置版本历史
        
        Args:
            config_id: 配置ID
            limit: 返回数量限制
            
        Returns:
            版本历史列表
        """
        if config_id not in self.version_storage:
            return []
        
        versions = self.version_storage[config_id]
        # 按版本号降序排列
        versions.sort(key=lambda v: v.version_number, reverse=True)
        
        return versions[:limit]
    
    async def get_version_by_id(self, config_id: int, version_id: int) -> Optional[ConfigVersion]:
        """
        根据版本ID获取特定版本
        
        Args:
            config_id: 配置ID
            version_id: 版本ID
            
        Returns:
            版本信息，如果不存在返回None
        """
        if config_id not in self.version_storage:
            return None
        
        for version in self.version_storage[config_id]:
            if version.version_id == version_id:
                return version
        
        return None
    
    async def get_active_version(self, config_id: int) -> Optional[ConfigVersion]:
        """
        获取当前活跃版本
        
        Args:
            config_id: 配置ID
            
        Returns:
            活跃版本信息
        """
        if config_id not in self.version_storage:
            return None
        
        for version in self.version_storage[config_id]:
            if version.is_active:
                return version
        
        return None
    
    async def rollback_to_version(self, db: AsyncSession, config_id: int, version_id: int, 
                                 rollback_by: int, rollback_reason: str = "用户回滚") -> bool:
        """
        回滚到指定版本
        
        Args:
            db: 数据库会话
            config_id: 配置ID
            version_id: 目标版本ID
            rollback_by: 回滚操作者ID
            rollback_reason: 回滚原因
            
        Returns:
            是否回滚成功
        """
        try:
            # 获取目标版本
            target_version = await self.get_version_by_id(config_id, version_id)
            if not target_version:
                logger.error(f"目标版本不存在: config_id={config_id}, version_id={version_id}")
                return False
            
            # 获取当前数据库配置
            result = await db.execute(
                select(CompressionConfig).where(CompressionConfig.id == config_id)
            )
            config = result.scalar_one_or_none()
            
            if not config:
                logger.error(f"配置不存在: config_id={config_id}")
                return False
            
            # 创建回滚版本（当前配置的备份）
            current_config_data = {
                'name': config.name,
                'algorithm': config.algorithm,
                'level': config.level,
                'stream_type': config.stream_type,
                'max_concurrent_users': config.max_concurrent_users,
                'bandwidth_limit_mbps': config.bandwidth_limit_mbps,
                'auto_fallback': config.auto_fallback,
                'config_data': config.config_data
            }
            
            await self.create_version(
                db, config_id, current_config_data, rollback_by, 
                f"回滚前备份 (回滚到版本 {target_version.version_number})"
            )
            
            # 应用目标版本的配置
            target_data = target_version.config_data
            config.name = target_data.get('name', config.name)
            config.algorithm = target_data.get('algorithm', config.algorithm)
            config.level = target_data.get('level', config.level)
            config.stream_type = target_data.get('stream_type', config.stream_type)
            config.max_concurrent_users = target_data.get('max_concurrent_users', config.max_concurrent_users)
            config.bandwidth_limit_mbps = target_data.get('bandwidth_limit_mbps', config.bandwidth_limit_mbps)
            config.auto_fallback = target_data.get('auto_fallback', config.auto_fallback)
            config.config_data = target_data.get('config_data', config.config_data)
            
            await db.commit()
            
            # 创建回滚版本记录
            rollback_version = await self.create_version(
                db, config_id, target_data, rollback_by, 
                f"回滚到版本 {target_version.version_number}: {rollback_reason}"
            )
            rollback_version.rollback_reason = rollback_reason
            
            logger.info(f"配置回滚成功: config_id={config_id}, 回滚到版本={target_version.version_number}")
            return True
            
        except Exception as e:
            logger.error(f"配置回滚失败: {e}")
            await db.rollback()
            return False
    
    async def compare_versions(self, config_id: int, version_id1: int, version_id2: int) -> Dict[str, Any]:
        """
        比较两个版本的差异
        
        Args:
            config_id: 配置ID
            version_id1: 版本1 ID
            version_id2: 版本2 ID
            
        Returns:
            版本差异信息
        """
        version1 = await self.get_version_by_id(config_id, version_id1)
        version2 = await self.get_version_by_id(config_id, version_id2)
        
        if not version1 or not version2:
            return {'error': '版本不存在'}
        
        differences = {}
        all_keys = set(version1.config_data.keys()) | set(version2.config_data.keys())
        
        for key in all_keys:
            val1 = version1.config_data.get(key)
            val2 = version2.config_data.get(key)
            
            if val1 != val2:
                differences[key] = {
                    f'version_{version1.version_number}': val1,
                    f'version_{version2.version_number}': val2
                }
        
        return {
            'config_id': config_id,
            'version1': {
                'version_id': version1.version_id,
                'version_number': version1.version_number,
                'created_at': version1.created_at.isoformat(),
                'change_summary': version1.change_summary
            },
            'version2': {
                'version_id': version2.version_id,
                'version_number': version2.version_number,
                'created_at': version2.created_at.isoformat(),
                'change_summary': version2.change_summary
            },
            'differences': differences
        }
    
    async def get_version_statistics(self, config_id: int) -> Dict[str, Any]:
        """
        获取版本统计信息
        
        Args:
            config_id: 配置ID
            
        Returns:
            版本统计信息
        """
        if config_id not in self.version_storage:
            return {
                'total_versions': 0,
                'active_version': None,
                'latest_change': None,
                'rollback_count': 0
            }
        
        versions = self.version_storage[config_id]
        rollback_count = sum(1 for v in versions if v.rollback_reason)
        active_version = next((v for v in versions if v.is_active), None)
        latest_version = max(versions, key=lambda v: v.created_at) if versions else None
        
        return {
            'total_versions': len(versions),
            'active_version': {
                'version_number': active_version.version_number,
                'created_at': active_version.created_at.isoformat(),
                'change_summary': active_version.change_summary
            } if active_version else None,
            'latest_change': latest_version.created_at.isoformat() if latest_version else None,
            'rollback_count': rollback_count
        }
    
    async def export_version_history(self, config_id: int) -> Dict[str, Any]:
        """
        导出版本历史
        
        Args:
            config_id: 配置ID
            
        Returns:
            版本历史导出数据
        """
        versions = await self.get_version_history(config_id, limit=100)
        
        export_data = {
            'config_id': config_id,
            'export_time': datetime.now().isoformat(),
            'total_versions': len(versions),
            'versions': []
        }
        
        for version in versions:
            version_data = asdict(version)
            version_data['created_at'] = version.created_at.isoformat()
            export_data['versions'].append(version_data)
        
        return export_data
    
    async def import_version_history(self, import_data: Dict[str, Any]) -> bool:
        """
        导入版本历史
        
        Args:
            import_data: 导入数据
            
        Returns:
            是否导入成功
        """
        try:
            config_id = import_data['config_id']
            versions_data = import_data['versions']
            
            imported_versions = []
            for version_data in versions_data:
                version = ConfigVersion(
                    version_id=version_data['version_id'],
                    config_id=version_data['config_id'],
                    version_number=version_data['version_number'],
                    config_data=version_data['config_data'],
                    created_at=datetime.fromisoformat(version_data['created_at']),
                    created_by=version_data['created_by'],
                    change_summary=version_data['change_summary'],
                    is_active=version_data.get('is_active', False),
                    rollback_reason=version_data.get('rollback_reason')
                )
                imported_versions.append(version)
            
            # 按创建时间排序
            imported_versions.sort(key=lambda v: v.created_at)
            
            # 存储导入的版本
            self.version_storage[config_id] = imported_versions
            
            logger.info(f"导入版本历史成功: config_id={config_id}, 版本数={len(imported_versions)}")
            return True
            
        except Exception as e:
            logger.error(f"导入版本历史失败: {e}")
            return False
    
    async def _get_next_version_number(self, config_id: int) -> int:
        """获取下一个版本号"""
        if config_id not in self.version_storage or not self.version_storage[config_id]:
            return 1
        
        max_version = max(v.version_number for v in self.version_storage[config_id])
        return max_version + 1
    
    async def _cleanup_old_versions(self, config_id: int):
        """清理旧版本"""
        if config_id not in self.version_storage:
            return
        
        versions = self.version_storage[config_id]
        if len(versions) > self.max_versions_per_config:
            # 保留最新的版本，删除最旧的
            versions.sort(key=lambda v: v.created_at, reverse=True)
            self.version_storage[config_id] = versions[:self.max_versions_per_config]
            
            logger.debug(f"清理旧版本: config_id={config_id}, 保留={self.max_versions_per_config}")

# 创建全局版本管理器实例
version_manager = CompressionVersionManager()
