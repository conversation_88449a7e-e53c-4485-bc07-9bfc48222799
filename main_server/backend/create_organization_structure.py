#!/usr/bin/env python3
"""
创建组织架构脚本
版本: 1.0
创建日期: 2025-01-08
描述: 创建完整的组织架构和测试用户
"""

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from database import AsyncSessionLocal
from models import User, Organization
from auth_utils import get_password_hash
from datetime import datetime

async def create_organization_structure():
    """创建完整的组织架构"""
    print("🏢 开始创建组织架构...")
    print("="*80)
    
    # 组织架构配置
    organizations = [
        # 大区级别
        {"name": "华东大区", "parent_name": "集团总部", "level": 1, "description": "华东地区业务管理"},
        {"name": "华北大区", "parent_name": "集团总部", "level": 1, "description": "华北地区业务管理"},
        {"name": "华南大区", "parent_name": "集团总部", "level": 1, "description": "华南地区业务管理"},
        
        # 华东大区分公司
        {"name": "上海分公司", "parent_name": "华东大区", "level": 2, "description": "上海地区分公司"},
        {"name": "南京分公司", "parent_name": "华东大区", "level": 2, "description": "南京地区分公司"},
        {"name": "杭州分公司", "parent_name": "华东大区", "level": 2, "description": "杭州地区分公司"},
        {"name": "苏州分公司", "parent_name": "华东大区", "level": 2, "description": "苏州地区分公司"},
        
        # 华北大区分公司
        {"name": "北京分公司", "parent_name": "华北大区", "level": 2, "description": "北京地区分公司"},
        {"name": "天津分公司", "parent_name": "华北大区", "level": 2, "description": "天津地区分公司"},
        {"name": "济南分公司", "parent_name": "华北大区", "level": 2, "description": "济南地区分公司"},
        {"name": "石家庄分公司", "parent_name": "华北大区", "level": 2, "description": "石家庄地区分公司"},
        
        # 华南大区分公司
        {"name": "广州分公司", "parent_name": "华南大区", "level": 2, "description": "广州地区分公司"},
        {"name": "深圳分公司", "parent_name": "华南大区", "level": 2, "description": "深圳地区分公司"},
        {"name": "厦门分公司", "parent_name": "华南大区", "level": 2, "description": "厦门地区分公司"},
        {"name": "海口分公司", "parent_name": "华南大区", "level": 2, "description": "海口地区分公司"},
    ]
    
    async with AsyncSessionLocal() as db:
        created_count = 0
        
        for org_config in organizations:
            # 检查组织是否已存在
            existing_org = await db.execute(
                select(Organization).where(Organization.name == org_config["name"])
            )
            if existing_org.scalar_one_or_none():
                print(f"⚠️ 组织 {org_config['name']} 已存在，跳过创建")
                continue
            
            # 查找父组织ID
            parent_id = None
            if org_config["parent_name"]:
                parent_result = await db.execute(
                    select(Organization.id).where(Organization.name == org_config["parent_name"])
                )
                parent_id = parent_result.scalar_one_or_none()
                
                if not parent_id:
                    print(f"❌ 父组织 {org_config['parent_name']} 不存在，跳过组织 {org_config['name']}")
                    continue
            
            # 计算路径
            if parent_id:
                parent_org = await db.execute(
                    select(Organization).where(Organization.id == parent_id)
                )
                parent = parent_org.scalar_one()
                path = f"{parent.path}/{org_config['name']}"
            else:
                path = f"/{org_config['name']}"
            
            # 创建组织
            new_org = Organization(
                name=org_config["name"],
                parent_id=parent_id,
                level=org_config["level"],
                path=path,
                description=org_config["description"],
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.add(new_org)
            print(f"✅ 创建组织: {org_config['name']} (Level: {org_config['level']}) -> {org_config.get('parent_name', '根级')}")
            created_count += 1
        
        # 提交更改
        await db.commit()
        print(f"\n🎉 成功创建 {created_count} 个组织")

async def create_test_users():
    """创建测试用户"""
    print("\n👥 开始创建测试用户...")
    print("="*80)
    
    # 测试用户配置
    test_users = [
        {
            "username": "huabei_manager",
            "password": "huabei123",
            "email": "<EMAIL>",
            "full_name": "华北大区管理员",
            "role_name": "华北大区管理员",
            "organization_name": "华北大区"
        },
        {
            "username": "beijing_manager", 
            "password": "beijing123",
            "email": "<EMAIL>",
            "full_name": "北京分公司管理员",
            "role_name": "北京分公司管理员",
            "organization_name": "北京分公司"
        },
        {
            "username": "huadong_manager",
            "password": "huadong123", 
            "email": "<EMAIL>",
            "full_name": "华东大区管理员",
            "role_name": "华东大区管理员",
            "organization_name": "华东大区"
        },
        {
            "username": "shanghai_manager",
            "password": "shanghai123",
            "email": "<EMAIL>", 
            "full_name": "上海分公司管理员",
            "role_name": "上海分公司管理员",
            "organization_name": "上海分公司"
        },
        {
            "username": "huanan_manager",
            "password": "huanan123",
            "email": "<EMAIL>",
            "full_name": "华南大区管理员", 
            "role_name": "华南大区管理员",
            "organization_name": "华南大区"
        },
        {
            "username": "guangzhou_manager",
            "password": "guangzhou123",
            "email": "<EMAIL>",
            "full_name": "广州分公司管理员",
            "role_name": "广州分公司管理员", 
            "organization_name": "广州分公司"
        },
        {
            "username": "normal_user",
            "password": "normal123",
            "email": "<EMAIL>",
            "full_name": "普通用户",
            "role_name": "普通用户",
            "organization_name": "北京分公司"
        }
    ]
    
    async with AsyncSessionLocal() as db:
        created_count = 0
        
        for user_config in test_users:
            # 检查用户是否已存在
            existing_user = await db.execute(
                select(User).where(User.username == user_config["username"])
            )
            if existing_user.scalar_one_or_none():
                print(f"⚠️ 用户 {user_config['username']} 已存在，跳过创建")
                continue
            
            # 查找组织ID
            org_result = await db.execute(
                select(Organization.id).where(Organization.name == user_config["organization_name"])
            )
            organization_id = org_result.scalar_one_or_none()
            
            if not organization_id:
                print(f"❌ 组织 {user_config['organization_name']} 不存在，跳过用户 {user_config['username']}")
                continue
            
            # 创建用户
            hashed_password = get_password_hash(user_config["password"])

            new_user = User(
                username=user_config["username"],
                email=user_config["email"],
                hashed_password=hashed_password,
                full_name=user_config["full_name"],
                role_name=user_config["role_name"],
                organization_id=organization_id,
                is_active=True,
                is_superuser=False,
                is_hardcoded_admin=False,
                login_count=0
            )
            
            db.add(new_user)
            print(f"✅ 创建用户: {user_config['username']} ({user_config['role_name']}) -> {user_config['organization_name']}")
            created_count += 1
        
        # 提交更改
        await db.commit()
        print(f"\n🎉 成功创建 {created_count} 个测试用户")

async def list_final_structure():
    """列出最终的组织架构和用户"""
    print("\n📊 最终的组织架构和用户分布:")
    print("="*80)
    
    async with AsyncSessionLocal() as db:
        # 获取所有组织
        org_result = await db.execute(
            select(Organization).order_by(Organization.level, Organization.name)
        )
        organizations = org_result.scalars().all()
        
        # 获取所有用户
        user_result = await db.execute(
            select(User).order_by(User.organization_id, User.username)
        )
        users = user_result.scalars().all()
        
        # 按组织分组显示
        for org in organizations:
            org_users = [u for u in users if u.organization_id == org.id]
            print(f"🏢 {org.name} (ID: {org.id}, Level: {org.level})")
            
            if org_users:
                for user in org_users:
                    print(f"   👤 {user.username} ({user.role_name})")
            else:
                print(f"   📭 暂无用户")
            print()

async def main():
    """主函数"""
    # 1. 创建组织架构
    await create_organization_structure()
    
    # 2. 创建测试用户
    await create_test_users()
    
    # 3. 显示最终结构
    await list_final_structure()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"程序执行异常: {e}")
        import traceback
        traceback.print_exc()
