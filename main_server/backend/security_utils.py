"""
安全工具类
提供统一的安全防护措施，包括防暴力破解、输入验证等
"""

import time
import hashlib
from typing import Dict, Optional
from datetime import datetime, timedelta
from fastapi import HTTPException, status, Request
import logging
import re

logger = logging.getLogger(__name__)


class LoginAttemptTracker:
    """登录尝试跟踪器 - 防暴力破解"""
    
    def __init__(self):
        self.attempts: Dict[str, Dict] = {}
        self.max_attempts = 5
        self.lockout_duration = 300  # 5分钟
        self.cleanup_interval = 3600  # 1小时清理一次
        self.last_cleanup = time.time()
    
    def _cleanup_old_attempts(self):
        """清理过期的尝试记录"""
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            expired_keys = []
            for key, data in self.attempts.items():
                if current_time - data.get('last_attempt', 0) > self.lockout_duration * 2:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.attempts[key]
            
            self.last_cleanup = current_time
    
    def _get_client_key(self, request: Request, username: str) -> str:
        """生成客户端唯一标识"""
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent", "")
        # 使用IP+用户名+User-Agent的哈希作为唯一标识
        key_data = f"{client_ip}:{username}:{user_agent}"
        return hashlib.sha256(key_data.encode()).hexdigest()[:16]
    
    def check_login_attempts(self, request: Request, username: str) -> bool:
        """
        检查登录尝试是否被锁定
        
        Args:
            request: FastAPI请求对象
            username: 用户名
            
        Returns:
            bool: True表示可以尝试登录，False表示被锁定
        """
        self._cleanup_old_attempts()
        
        client_key = self._get_client_key(request, username)
        current_time = time.time()
        
        if client_key not in self.attempts:
            return True
        
        attempt_data = self.attempts[client_key]
        
        # 检查是否在锁定期内
        if attempt_data['count'] >= self.max_attempts:
            if current_time - attempt_data['last_attempt'] < self.lockout_duration:
                return False
            else:
                # 锁定期已过，重置计数
                self.attempts[client_key] = {'count': 0, 'last_attempt': current_time}
                return True
        
        return True
    
    def record_failed_attempt(self, request: Request, username: str):
        """记录失败的登录尝试"""
        client_key = self._get_client_key(request, username)
        current_time = time.time()
        
        if client_key not in self.attempts:
            self.attempts[client_key] = {'count': 0, 'last_attempt': current_time}
        
        self.attempts[client_key]['count'] += 1
        self.attempts[client_key]['last_attempt'] = current_time
        
        logger.warning(f"登录失败尝试: {username}, IP: {request.client.host}, 尝试次数: {self.attempts[client_key]['count']}")
    
    def record_successful_login(self, request: Request, username: str):
        """记录成功的登录，清除失败记录"""
        client_key = self._get_client_key(request, username)
        if client_key in self.attempts:
            del self.attempts[client_key]


class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def validate_username(username: str) -> bool:
        """验证用户名格式"""
        if not username or len(username) < 3 or len(username) > 50:
            return False
        # 只允许字母、数字、下划线、连字符
        return re.match(r'^[a-zA-Z0-9_-]+$', username) is not None
    
    @staticmethod
    def validate_password(password: str) -> bool:
        """验证密码强度"""
        if not password or len(password) < 6:
            return False
        # 至少包含字母和数字
        has_letter = re.search(r'[a-zA-Z]', password) is not None
        has_digit = re.search(r'\d', password) is not None
        return has_letter and has_digit
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """验证邮箱格式"""
        if not email or len(email) > 254:  # RFC 5321 限制
            return False

        # 基本格式检查
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, email):
            return False

        # 检查本地部分和域名部分长度
        local, domain = email.rsplit('@', 1)
        if len(local) > 64 or len(domain) > 253:
            return False

        # 检查是否包含连续的点
        if '..' in email:
            return False

        return True

    @staticmethod
    def validate_integer_range(value: any, min_val: int = None, max_val: int = None) -> bool:
        """验证整数范围"""
        try:
            int_val = int(value)
            if min_val is not None and int_val < min_val:
                return False
            if max_val is not None and int_val > max_val:
                return False
            return True
        except (ValueError, TypeError):
            return False

    @staticmethod
    def validate_string_length(value: str, min_len: int = 0, max_len: int = 1000) -> bool:
        """验证字符串长度"""
        if not isinstance(value, str):
            return False
        return min_len <= len(value) <= max_len
    
    @staticmethod
    def sanitize_input(input_str: str, max_length: int = 1000) -> str:
        """增强的输入清理，防止XSS和注入攻击"""
        if not input_str:
            return ""

        # 长度限制
        if len(input_str) > max_length:
            input_str = input_str[:max_length]

        # 移除控制字符和潜在危险字符
        import re
        # 移除所有控制字符（除了换行、回车、制表符）
        input_str = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', input_str)

        # 移除潜在的脚本标签和危险字符
        dangerous_patterns = [
            r'<script[^>]*>.*?</script>',
            r'<iframe[^>]*>.*?</iframe>',
            r'javascript:',
            r'vbscript:',
            r'on\w+\s*=',
            r'expression\s*\(',
            r'@import',
            r'url\s*\('
        ]

        for pattern in dangerous_patterns:
            input_str = re.sub(pattern, '', input_str, flags=re.IGNORECASE | re.DOTALL)

        # 转义HTML特殊字符
        html_escape_table = {
            "&": "&amp;",
            "<": "&lt;",
            ">": "&gt;",
            '"': "&quot;",
            "'": "&#x27;",
            "/": "&#x2F;"
        }

        for char, escape in html_escape_table.items():
            input_str = input_str.replace(char, escape)

        return input_str.strip()


class SecurityHeaders:
    """安全头部管理"""
    
    @staticmethod
    def get_security_headers() -> Dict[str, str]:
        """获取安全头部"""
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }


class SessionSecurity:
    """会话安全管理"""
    
    @staticmethod
    def is_session_expired(expires_at: datetime) -> bool:
        """检查会话是否过期"""
        return datetime.utcnow() > expires_at
    
    @staticmethod
    def validate_session_ip(session_ip: str, current_ip: str) -> bool:
        """验证会话IP是否匹配（可选的安全措施）"""
        # 在某些情况下，用户IP可能会变化（移动网络等）
        # 这里可以根据安全需求决定是否严格验证
        return True  # 暂时不严格验证IP
    
    @staticmethod
    def generate_session_token() -> str:
        """生成安全的会话令牌"""
        import secrets
        return secrets.token_urlsafe(32)


# 全局登录尝试跟踪器实例
login_tracker = LoginAttemptTracker()


def check_login_rate_limit(request: Request, username: str):
    """检查登录频率限制装饰器"""
    if not login_tracker.check_login_attempts(request, username):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="登录尝试次数过多，请稍后再试"
        )


def record_login_attempt(request: Request, username: str, success: bool):
    """记录登录尝试结果"""
    if success:
        login_tracker.record_successful_login(request, username)
    else:
        login_tracker.record_failed_attempt(request, username)
