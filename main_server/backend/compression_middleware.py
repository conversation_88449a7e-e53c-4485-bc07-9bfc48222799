#!/usr/bin/env python3
"""
数据压缩中间件
版本: 1.0
创建日期: 2025-01-15
描述: 提供Snappy、LZ4、Zstandard三种压缩算法的统一接口，用于网络穿透工具的数据传输优化
"""

import time
import logging
import asyncio
from typing import Dict, Any, Optional, Callable, Tuple, Union, List
from enum import Enum
import io
import threading
import queue

# 导入压缩库
try:
    import snappy
    SNAPPY_AVAILABLE = True
except ImportError:
    SNAPPY_AVAILABLE = False
    logging.warning("⚠️ Snappy库未安装")

try:
    import lz4.frame
    LZ4_AVAILABLE = True
except ImportError:
    LZ4_AVAILABLE = False
    logging.warning("⚠️ LZ4库未安装")

try:
    import zstandard as zstd
    ZSTD_AVAILABLE = True
except ImportError:
    ZSTD_AVAILABLE = False
    logging.warning("⚠️ Zstandard库未安装")

logger = logging.getLogger(__name__)

class CompressionAlgorithm(str, Enum):
    """压缩算法枚举"""
    NONE = "none"
    SNAPPY = "snappy"
    LZ4 = "lz4"
    ZSTD = "zstd"

class CompressionLevel(str, Enum):
    """压缩级别枚举"""
    LOW = "low"       # 低压缩率，高速度
    MEDIUM = "medium" # 平衡压缩率和速度
    HIGH = "high"     # 高压缩率，低速度

class CompressionStats:
    """压缩统计信息"""
    
    def __init__(self):
        self.total_original_bytes = 0
        self.total_compressed_bytes = 0
        self.total_compress_time = 0
        self.total_decompress_time = 0
        self.compression_count = 0
        self.decompression_count = 0
        self.errors = 0
        self.start_time = time.time()
    
    def update_compression(self, original_size: int, compressed_size: int, duration: float):
        """更新压缩统计"""
        self.total_original_bytes += original_size
        self.total_compressed_bytes += compressed_size
        self.total_compress_time += duration
        self.compression_count += 1

    def update_decompression(self, compressed_size: int, original_size: int, duration: float):
        """更新解压统计"""
        self.total_decompress_time += duration
        self.decompression_count += 1

    def record_error(self):
        """记录错误"""
        self.errors += 1

    def get_compression_ratio(self) -> float:
        """获取压缩比率"""
        if self.total_original_bytes == 0:
            return 0.0
        return self.total_compressed_bytes / self.total_original_bytes

    def get_average_compression_time(self) -> float:
        """获取平均压缩时间"""
        if self.compression_count == 0:
            return 0.0
        return self.total_compress_time / self.compression_count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        uptime = time.time() - self.start_time
        
        if self.total_original_bytes > 0:
            compression_ratio = self.total_compressed_bytes / self.total_original_bytes
            compression_rate = (1 - compression_ratio) * 100
        else:
            compression_ratio = 0
            compression_rate = 0
        
        if self.compression_count > 0:
            avg_compress_time = self.total_compress_time / self.compression_count
        else:
            avg_compress_time = 0
        
        if self.decompression_count > 0:
            avg_decompress_time = self.total_decompress_time / self.decompression_count
        else:
            avg_decompress_time = 0
        
        return {
            "uptime_seconds": uptime,
            "total_original_bytes": self.total_original_bytes,
            "total_compressed_bytes": self.total_compressed_bytes,
            "compression_ratio": compression_ratio,
            "compression_rate": compression_rate,
            "compression_count": self.compression_count,
            "decompression_count": self.decompression_count,
            "avg_compress_time_ms": avg_compress_time * 1000,
            "avg_decompress_time_ms": avg_decompress_time * 1000,
            "errors": self.errors
        }
    
    def reset(self):
        """重置统计信息"""
        self.__init__()

class CompressionMiddleware:
    """通用压缩中间件"""
    
    def __init__(self, algorithm: CompressionAlgorithm = CompressionAlgorithm.SNAPPY, 
                 level: CompressionLevel = CompressionLevel.MEDIUM):
        """
        初始化压缩中间件
        
        Args:
            algorithm: 压缩算法
            level: 压缩级别
        """
        self.algorithm = algorithm
        self.level = level
        self.stats = CompressionStats()
        
        # 初始化压缩器和解压器
        self._init_compressors()
    
    def _init_compressors(self):
        """初始化压缩器和解压器"""
        # 检查算法可用性
        if self.algorithm == CompressionAlgorithm.SNAPPY and not SNAPPY_AVAILABLE:
            logger.warning("Snappy不可用，切换到LZ4")
            self.algorithm = CompressionAlgorithm.LZ4 if LZ4_AVAILABLE else CompressionAlgorithm.NONE
        
        if self.algorithm == CompressionAlgorithm.LZ4 and not LZ4_AVAILABLE:
            logger.warning("LZ4不可用，切换到Zstandard")
            self.algorithm = CompressionAlgorithm.ZSTD if ZSTD_AVAILABLE else CompressionAlgorithm.NONE
        
        if self.algorithm == CompressionAlgorithm.ZSTD and not ZSTD_AVAILABLE:
            logger.warning("Zstandard不可用，切换到Snappy")
            self.algorithm = CompressionAlgorithm.SNAPPY if SNAPPY_AVAILABLE else CompressionAlgorithm.NONE
        
        if self.algorithm == CompressionAlgorithm.NONE:
            logger.warning("没有可用的压缩算法，将不进行压缩")
            self.compress = lambda data: data
            self.decompress = lambda data: data
            return
        
        # 根据算法和级别初始化压缩器
        if self.algorithm == CompressionAlgorithm.SNAPPY:
            self.compress = snappy.compress
            self.decompress = snappy.decompress
        
        elif self.algorithm == CompressionAlgorithm.LZ4:
            # LZ4压缩级别映射
            level_map = {
                CompressionLevel.LOW: {'compression_level': 0, 'block_size': lz4.frame.BLOCKSIZE_DEFAULT},
                CompressionLevel.MEDIUM: {'compression_level': 3, 'block_size': lz4.frame.BLOCKSIZE_DEFAULT},
                CompressionLevel.HIGH: {'compression_level': 9, 'block_size': lz4.frame.BLOCKSIZE_DEFAULT}
            }
            
            params = level_map.get(self.level, level_map[CompressionLevel.MEDIUM])
            
            self.compress = lambda data: lz4.frame.compress(
                data, 
                compression_level=params['compression_level'],
                block_size=params['block_size']
            )
            self.decompress = lz4.frame.decompress
        
        elif self.algorithm == CompressionAlgorithm.ZSTD:
            # Zstandard压缩级别映射
            level_map = {
                CompressionLevel.LOW: 1,
                CompressionLevel.MEDIUM: 3,
                CompressionLevel.HIGH: 9
            }
            
            zstd_level = level_map.get(self.level, level_map[CompressionLevel.MEDIUM])
            
            # 创建压缩器和解压器
            self.zstd_compressor = zstd.ZstdCompressor(level=zstd_level)
            self.zstd_decompressor = zstd.ZstdDecompressor()
            
            self.compress = self.zstd_compressor.compress
            self.decompress = self.zstd_decompressor.decompress
    
    def compress_data(self, data: bytes) -> bytes:
        """
        压缩数据
        
        Args:
            data: 原始数据
            
        Returns:
            压缩后的数据
        """
        if self.algorithm == CompressionAlgorithm.NONE or not data:
            return data
        
        try:
            start_time = time.perf_counter()
            compressed = self.compress(data)
            duration = time.perf_counter() - start_time
            
            # 更新统计信息
            self.stats.update_compression(len(data), len(compressed), duration)
            
            return compressed
        except Exception as e:
            logger.error(f"压缩数据失败: {e}")
            self.stats.record_error()
            return data  # 失败时返回原始数据
    
    def decompress_data(self, data: bytes) -> bytes:
        """
        解压数据
        
        Args:
            data: 压缩数据
            
        Returns:
            解压后的数据
        """
        if self.algorithm == CompressionAlgorithm.NONE or not data:
            return data
        
        try:
            start_time = time.perf_counter()
            decompressed = self.decompress(data)
            duration = time.perf_counter() - start_time
            
            # 更新统计信息
            self.stats.update_decompression(len(data), len(decompressed), duration)
            
            return decompressed
        except Exception as e:
            logger.error(f"解压数据失败: {e}")
            self.stats.record_error()
            return data  # 失败时返回原始数据
    
    async def compress_data_async(self, data: bytes) -> bytes:
        """异步压缩数据"""
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(None, self.compress_data, data)
    
    async def decompress_data_async(self, data: bytes) -> bytes:
        """异步解压数据"""
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(None, self.decompress_data, data)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取压缩统计信息"""
        stats = self.stats.get_stats()
        stats["algorithm"] = self.algorithm
        stats["level"] = self.level
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats.reset()
    
    def change_algorithm(self, algorithm: CompressionAlgorithm, level: CompressionLevel = None):
        """
        更改压缩算法
        
        Args:
            algorithm: 新的压缩算法
            level: 新的压缩级别（可选）
        """
        self.algorithm = algorithm
        if level:
            self.level = level
        
        # 重新初始化压缩器
        self._init_compressors()
        
        # 重置统计信息
        self.reset_stats()
        
        logger.info(f"压缩算法已更改为: {self.algorithm}, 级别: {self.level}")

# 创建全局压缩中间件实例
default_middleware = CompressionMiddleware(
    algorithm=CompressionAlgorithm.SNAPPY,
    level=CompressionLevel.MEDIUM
)
