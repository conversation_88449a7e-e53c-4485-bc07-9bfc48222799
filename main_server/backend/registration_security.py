"""
用户注册安全模块
实施注册频率限制和反垃圾机制
"""
import time
import hashlib
from typing import Dict, Optional
from datetime import datetime, timedelta
from fastapi import HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from models import User
import re

class RegistrationSecurity:
    """注册安全管理器"""
    
    # 内存缓存，用于存储IP和用户的注册尝试记录
    _ip_attempts: Dict[str, list] = {}
    _phone_attempts: Dict[str, list] = {}
    _email_attempts: Dict[str, list] = {}
    
    # 配置参数
    MAX_ATTEMPTS_PER_IP_PER_HOUR = 3  # 每IP每小时最大注册次数
    MAX_FAILED_ATTEMPTS_PER_PHONE_PER_DAY = 3  # 每手机号每天最大失败尝试次数
    MAX_FAILED_ATTEMPTS_PER_EMAIL_PER_DAY = 3  # 每邮箱每天最大失败尝试次数
    CLEANUP_INTERVAL = 3600  # 清理间隔（秒）
    
    @classmethod
    def _get_client_ip(cls, request: Request) -> str:
        """获取客户端真实IP地址"""
        # 优先从代理头获取真实IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        # 回退到直接连接IP
        return request.client.host if request.client else "unknown"
    
    @classmethod
    def _cleanup_old_attempts(cls):
        """清理过期的尝试记录"""
        current_time = time.time()
        
        # 清理IP尝试记录（保留1小时内的）
        for ip in list(cls._ip_attempts.keys()):
            cls._ip_attempts[ip] = [
                attempt_time for attempt_time in cls._ip_attempts[ip]
                if current_time - attempt_time < 3600
            ]
            if not cls._ip_attempts[ip]:
                del cls._ip_attempts[ip]
        
        # 清理手机号尝试记录（保留24小时内的）
        for phone in list(cls._phone_attempts.keys()):
            cls._phone_attempts[phone] = [
                attempt_time for attempt_time in cls._phone_attempts[phone]
                if current_time - attempt_time < 86400
            ]
            if not cls._phone_attempts[phone]:
                del cls._phone_attempts[phone]
        
        # 清理邮箱尝试记录（保留24小时内的）
        for email in list(cls._email_attempts.keys()):
            cls._email_attempts[email] = [
                attempt_time for attempt_time in cls._email_attempts[email]
                if current_time - attempt_time < 86400
            ]
            if not cls._email_attempts[email]:
                del cls._email_attempts[email]
    
    @classmethod
    def _record_attempt(cls, ip: str, phone: str, email: Optional[str] = None):
        """记录注册尝试"""
        current_time = time.time()
        
        # 记录IP尝试
        if ip not in cls._ip_attempts:
            cls._ip_attempts[ip] = []
        cls._ip_attempts[ip].append(current_time)
        
        # 记录手机号尝试
        if phone not in cls._phone_attempts:
            cls._phone_attempts[phone] = []
        cls._phone_attempts[phone].append(current_time)
        
        # 记录邮箱尝试
        if email:
            if email not in cls._email_attempts:
                cls._email_attempts[email] = []
            cls._email_attempts[email].append(current_time)

    @classmethod
    def record_failed_attempt(cls, ip: str, phone: str, email: Optional[str] = None):
        """记录失败的注册尝试（用于频率限制）"""
        current_time = time.time()

        # 记录IP失败尝试
        if ip not in cls._ip_attempts:
            cls._ip_attempts[ip] = []
        cls._ip_attempts[ip].append(current_time)

        # 记录手机号失败尝试
        if phone not in cls._phone_attempts:
            cls._phone_attempts[phone] = []
        cls._phone_attempts[phone].append(current_time)

        # 记录邮箱失败尝试
        if email:
            if email not in cls._email_attempts:
                cls._email_attempts[email] = []
            cls._email_attempts[email].append(current_time)

    @classmethod
    async def check_registration_limits(
        cls, 
        request: Request, 
        username: str, 
        phone: str, 
        email: Optional[str],
        db: AsyncSession
    ):
        """
        检查注册限制
        """
        # 清理过期记录
        cls._cleanup_old_attempts()
        
        client_ip = cls._get_client_ip(request)
        current_time = time.time()
        
        # 1. 检查IP频率限制
        ip_attempts = cls._ip_attempts.get(client_ip, [])
        recent_ip_attempts = [
            attempt for attempt in ip_attempts 
            if current_time - attempt < 3600
        ]
        
        if len(recent_ip_attempts) >= cls.MAX_ATTEMPTS_PER_IP_PER_HOUR:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"注册过于频繁，每小时最多允许{cls.MAX_ATTEMPTS_PER_IP_PER_HOUR}次注册，请稍后再试"
            )
        
        # 2. 检查手机号失败尝试频率限制（防止暴力尝试）
        phone_attempts = cls._phone_attempts.get(phone, [])
        recent_phone_attempts = [
            attempt for attempt in phone_attempts
            if current_time - attempt < 86400
        ]

        if len(recent_phone_attempts) >= cls.MAX_FAILED_ATTEMPTS_PER_PHONE_PER_DAY:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"该手机号今日尝试注册次数过多，请明天再试"
            )

        # 3. 检查邮箱失败尝试频率限制（防止暴力尝试）
        if email:
            email_attempts = cls._email_attempts.get(email, [])
            recent_email_attempts = [
                attempt for attempt in email_attempts
                if current_time - attempt < 86400
            ]

            if len(recent_email_attempts) >= cls.MAX_FAILED_ATTEMPTS_PER_EMAIL_PER_DAY:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail=f"该邮箱今日尝试注册次数过多，请明天再试"
                )
        
        # 4. 检查数据库中的重复注册（24小时内）
        yesterday = datetime.utcnow() - timedelta(days=1)
        
        # 检查同一IP的注册次数
        ip_hash = hashlib.md5(client_ip.encode()).hexdigest()
        recent_registrations = await db.execute(
            select(func.count(User.id)).where(
                and_(
                    User.created_at >= yesterday,
                    # 这里简化处理，实际应该有IP记录表
                )
            )
        )
        
        # 5. 反垃圾检查
        await cls._check_spam_patterns(username, phone, email, db)
        
        # 注意：这里不记录尝试，只有失败时才记录
        # 成功注册时不需要记录到限制缓存中
    
    @classmethod
    async def _check_spam_patterns(
        cls, 
        username: str, 
        phone: str, 
        email: Optional[str],
        db: AsyncSession
    ):
        """检查垃圾注册模式"""
        
        # 1. 检查用户名模式
        if cls._is_suspicious_username(username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名格式可疑，请使用正常的用户名"
            )
        
        # 2. 检查手机号模式
        if cls._is_suspicious_phone(phone):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="手机号格式可疑，请使用真实的手机号"
            )
        
        # 3. 检查邮箱模式
        if email and cls._is_suspicious_email(email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱格式可疑，请使用真实的邮箱地址"
            )
        
        # 4. 检查相似用户名
        similar_users = await db.execute(
            select(func.count(User.id)).where(
                User.username.like(f"{username[:5]}%")
            )
        )
        similar_count = similar_users.scalar() or 0
        
        if similar_count > 10:  # 如果有超过10个相似用户名
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="检测到大量相似用户名，请选择更独特的用户名"
            )
    
    @classmethod
    def _is_suspicious_username(cls, username: str) -> bool:
        """检查可疑用户名模式"""
        # 检查是否包含大量数字
        digit_count = sum(1 for c in username if c.isdigit())
        if digit_count > len(username) * 0.7:  # 超过70%是数字
            return True
        
        # 检查是否是纯数字
        if username.isdigit():
            return True
        
        # 检查是否包含常见垃圾模式
        spam_patterns = [
            r'test\d+', r'user\d+', r'admin\d+', 
            r'\d{6,}', r'[a-z]{1,2}\d{6,}'
        ]
        
        for pattern in spam_patterns:
            if re.match(pattern, username.lower()):
                return True
        
        return False
    
    @classmethod
    def _is_suspicious_phone(cls, phone: str) -> bool:
        """检查可疑手机号模式"""
        # 检查是否是连续数字
        if len(set(phone)) <= 3:  # 只有3种或更少不同数字
            return True
        
        # 检查是否是递增或递减序列
        digits = [int(d) for d in phone if d.isdigit()]
        if len(digits) >= 8:
            is_ascending = all(digits[i] <= digits[i+1] for i in range(len(digits)-1))
            is_descending = all(digits[i] >= digits[i+1] for i in range(len(digits)-1))
            if is_ascending or is_descending:
                return True
        
        return False
    
    @classmethod
    def _is_suspicious_email(cls, email: str) -> bool:
        """检查可疑邮箱模式"""
        # 检查是否使用临时邮箱域名
        temp_domains = [
            '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
            'mailinator.com', 'yopmail.com', 'temp-mail.org'
        ]
        
        domain = email.split('@')[-1].lower()
        if domain in temp_domains:
            return True
        
        # 检查邮箱用户名是否可疑
        username_part = email.split('@')[0]
        if cls._is_suspicious_username(username_part):
            return True
        
        return False
