#!/usr/bin/env python3
"""
压缩集成管理器
版本: 1.0
创建日期: 2025-01-18
描述: 统一管理网络穿透工具与压缩功能的集成，确保数据流经过压缩处理
"""

import asyncio
import logging
import time
import json
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from compression_proxy_server import CompressionProxyManager, TunnelType, ProtocolType

logger = logging.getLogger(__name__)

class IntegrationStatus(str, Enum):
    """集成状态"""
    DISABLED = "disabled"
    STARTING = "starting"
    RUNNING = "running"
    FAILED = "failed"
    STOPPING = "stopping"

@dataclass
class CompressionIntegration:
    """压缩集成配置"""
    tunnel_type: str
    config_name: str
    original_local_port: int
    compression_proxy_port: int
    target_host: str
    target_port: int
    status: IntegrationStatus
    proxy_id: str
    created_at: float
    last_health_check: float

class CompressionIntegrationManager:
    """压缩集成管理器"""
    
    def __init__(self):
        self.proxy_manager = CompressionProxyManager()
        self.integrations: Dict[str, CompressionIntegration] = {}
        self.port_allocator = PortAllocator(start_port=9000, end_port=9999)
        self.health_check_interval = 30  # 秒
        self.health_check_task = None
        
    async def start(self):
        """启动集成管理器"""
        logger.info("启动压缩集成管理器")
        
        # 启动健康检查任务
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        
    async def stop(self):
        """停止集成管理器"""
        logger.info("停止压缩集成管理器")
        
        # 停止健康检查任务
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        # 停止所有集成
        for integration_id in list(self.integrations.keys()):
            await self.stop_integration(integration_id)
    
    async def create_integration(self, tunnel_type: str, config_name: str, 
                               original_config: Dict[str, Any]) -> Optional[CompressionIntegration]:
        """创建压缩集成"""
        integration_id = f"{tunnel_type}_{config_name}"
        
        if integration_id in self.integrations:
            logger.warning(f"集成已存在: {integration_id}")
            return self.integrations[integration_id]
        
        try:
            # 分析原始配置，提取端口信息
            port_info = self._extract_port_info(tunnel_type, original_config)
            if not port_info:
                logger.error(f"无法提取端口信息: {tunnel_type}")
                return None
            
            original_local_port, target_host, target_port = port_info
            
            # 分配压缩代理端口
            compression_proxy_port = self.port_allocator.allocate_port()
            if not compression_proxy_port:
                logger.error("无法分配压缩代理端口")
                return None
            
            # 创建集成配置
            integration = CompressionIntegration(
                tunnel_type=tunnel_type,
                config_name=config_name,
                original_local_port=original_local_port,
                compression_proxy_port=compression_proxy_port,
                target_host=target_host,
                target_port=target_port,
                status=IntegrationStatus.STARTING,
                proxy_id=f"compression_proxy_{integration_id}",
                created_at=time.time(),
                last_health_check=time.time()
            )
            
            # 创建压缩代理
            success = await self.proxy_manager.create_proxy(
                proxy_id=integration.proxy_id,
                listen_port=compression_proxy_port,
                target_host=target_host,
                target_port=target_port,
                tunnel_type=TunnelType(tunnel_type.lower()),
                protocol=ProtocolType.TCP
            )
            
            if success:
                integration.status = IntegrationStatus.RUNNING
                self.integrations[integration_id] = integration
                logger.info(f"创建压缩集成成功: {integration_id}")
                return integration
            else:
                integration.status = IntegrationStatus.FAILED
                self.port_allocator.release_port(compression_proxy_port)
                logger.error(f"创建压缩代理失败: {integration_id}")
                return None
                
        except Exception as e:
            logger.error(f"创建压缩集成异常 {integration_id}: {e}")
            return None
    
    async def stop_integration(self, integration_id: str) -> bool:
        """停止压缩集成"""
        if integration_id not in self.integrations:
            logger.warning(f"集成不存在: {integration_id}")
            return False
        
        try:
            integration = self.integrations[integration_id]
            integration.status = IntegrationStatus.STOPPING
            
            # 停止压缩代理
            success = await self.proxy_manager.stop_proxy(integration.proxy_id)
            
            # 释放端口
            self.port_allocator.release_port(integration.compression_proxy_port)
            
            # 移除集成
            del self.integrations[integration_id]
            
            logger.info(f"停止压缩集成: {integration_id}")
            return success
            
        except Exception as e:
            logger.error(f"停止压缩集成异常 {integration_id}: {e}")
            return False
    
    def modify_tunnel_config(self, tunnel_type: str, original_config: Dict[str, Any], 
                           integration: CompressionIntegration) -> Dict[str, Any]:
        """修改网络穿透工具配置，使其通过压缩代理"""
        modified_config = original_config.copy()
        
        if tunnel_type == "frp":
            # 修改FRP配置，将local_port指向压缩代理
            for proxy in modified_config.get('proxies', []):
                if proxy.get('local_port') == integration.original_local_port:
                    proxy['local_port'] = integration.compression_proxy_port
                    logger.info(f"FRP配置已修改: {integration.original_local_port} -> {integration.compression_proxy_port}")
        
        elif tunnel_type == "nps":
            # 修改NPS配置
            if 'local_port' in modified_config:
                modified_config['local_port'] = integration.compression_proxy_port
                logger.info(f"NPS配置已修改: {integration.original_local_port} -> {integration.compression_proxy_port}")
        
        elif tunnel_type == "rathole":
            # 修改Rathole配置
            services = modified_config.get('services', {})
            for service_name, service_config in services.items():
                if service_config.get('local_addr', '').endswith(f":{integration.original_local_port}"):
                    service_config['local_addr'] = f"127.0.0.1:{integration.compression_proxy_port}"
                    logger.info(f"Rathole配置已修改: {integration.original_local_port} -> {integration.compression_proxy_port}")
        
        return modified_config
    
    def _extract_port_info(self, tunnel_type: str, config: Dict[str, Any]) -> Optional[Tuple[int, str, int]]:
        """提取端口信息"""
        try:
            if tunnel_type == "frp":
                proxies = config.get('proxies', [])
                if proxies:
                    proxy = proxies[0]
                    local_port = proxy.get('local_port', 80)
                    return local_port, '127.0.0.1', local_port
            
            elif tunnel_type == "nps":
                local_port = config.get('local_port', 80)
                return local_port, '127.0.0.1', local_port
            
            elif tunnel_type == "rathole":
                services = config.get('services', {})
                if services:
                    service = list(services.values())[0]
                    local_addr = service.get('local_addr', '127.0.0.1:80')
                    if ':' in local_addr:
                        host, port = local_addr.rsplit(':', 1)
                        return int(port), host, int(port)
            
            # 默认返回
            return 80, '127.0.0.1', 80
            
        except Exception as e:
            logger.error(f"提取端口信息失败 {tunnel_type}: {e}")
            return None
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
    
    async def _perform_health_check(self):
        """执行健康检查"""
        current_time = time.time()
        
        for integration_id, integration in self.integrations.items():
            try:
                # 检查代理是否运行
                is_running = self.proxy_manager.is_proxy_running(integration.proxy_id)
                
                if is_running:
                    integration.status = IntegrationStatus.RUNNING
                else:
                    integration.status = IntegrationStatus.FAILED
                    logger.warning(f"压缩集成健康检查失败: {integration_id}")
                
                integration.last_health_check = current_time
                
            except Exception as e:
                logger.error(f"健康检查异常 {integration_id}: {e}")
                integration.status = IntegrationStatus.FAILED
    
    def get_integration_stats(self) -> Dict[str, Any]:
        """获取集成统计信息"""
        stats = {
            "total_integrations": len(self.integrations),
            "running_integrations": 0,
            "failed_integrations": 0,
            "integrations": {}
        }
        
        for integration_id, integration in self.integrations.items():
            if integration.status == IntegrationStatus.RUNNING:
                stats["running_integrations"] += 1
            elif integration.status == IntegrationStatus.FAILED:
                stats["failed_integrations"] += 1
            
            # 获取代理统计信息
            proxy_stats = self.proxy_manager.get_proxy_stats(integration.proxy_id)
            
            stats["integrations"][integration_id] = {
                "status": integration.status,
                "tunnel_type": integration.tunnel_type,
                "compression_proxy_port": integration.compression_proxy_port,
                "target": f"{integration.target_host}:{integration.target_port}",
                "uptime_seconds": time.time() - integration.created_at,
                "proxy_stats": proxy_stats
            }
        
        return stats

class PortAllocator:
    """端口分配器"""
    
    def __init__(self, start_port: int = 9000, end_port: int = 9999):
        self.start_port = start_port
        self.end_port = end_port
        self.allocated_ports: set = set()
    
    def allocate_port(self) -> Optional[int]:
        """分配端口"""
        for port in range(self.start_port, self.end_port + 1):
            if port not in self.allocated_ports:
                self.allocated_ports.add(port)
                return port
        return None
    
    def release_port(self, port: int):
        """释放端口"""
        self.allocated_ports.discard(port)
    
    def is_port_allocated(self, port: int) -> bool:
        """检查端口是否已分配"""
        return port in self.allocated_ports

# 创建全局集成管理器实例
compression_integration_manager = CompressionIntegrationManager()
