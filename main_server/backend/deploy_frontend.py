#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniLink前端构建和部署脚本

功能：
1. 构建前端项目
2. 清理容器内旧的前端文件
3. 部署新的前端文件到容器
4. 清空服务器和浏览器缓存
5. 验证部署结果

使用方法：
1. 在主机上运行：
   python scripts/deploy_frontend.py

2. 仅构建不部署：
   python scripts/deploy_frontend.py --build-only

3. 仅部署已构建的文件：
   python scripts/deploy_frontend.py --deploy-only

4. 强制重新构建：
   python scripts/deploy_frontend.py --force-rebuild
"""

import os
import sys
import subprocess
import shutil
import time
import argparse
import logging
from pathlib import Path
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OmniLinkFrontendDeployer:
    """OmniLink前端部署器"""
    
    def __init__(self, project_root: str = None):
        # 项目路径配置
        self.project_root = Path(project_root or os.getcwd())
        self.frontend_dir = self.project_root / "main_server" / "frontend"
        self.dist_dir = self.frontend_dir / "dist"
        
        # 容器配置
        self.container_name = "omnilink-main-server-1"
        self.container_static_path = "/app/static"
        
        # 构建配置
        self.node_modules_dir = self.frontend_dir / "node_modules"
        self.package_json = self.frontend_dir / "package.json"
        
        logger.info(f"项目根目录: {self.project_root}")
        logger.info(f"前端目录: {self.frontend_dir}")
        logger.info(f"构建输出目录: {self.dist_dir}")
    
    def check_prerequisites(self) -> bool:
        """检查前置条件"""
        logger.info("🔍 检查前置条件...")
        
        # 检查前端目录
        if not self.frontend_dir.exists():
            logger.error(f"❌ 前端目录不存在: {self.frontend_dir}")
            return False
        
        # 检查package.json
        if not self.package_json.exists():
            logger.error(f"❌ package.json不存在: {self.package_json}")
            return False
        
        # 检查Node.js
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ Node.js版本: {result.stdout.strip()}")
            else:
                logger.error("❌ Node.js未安装或不可用")
                return False
        except FileNotFoundError:
            logger.error("❌ Node.js未安装")
            return False
        
        # 检查npm
        try:
            result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ npm版本: {result.stdout.strip()}")
            else:
                logger.error("❌ npm不可用")
                return False
        except FileNotFoundError:
            logger.error("❌ npm未安装")
            return False
        
        # 检查Docker容器
        try:
            result = subprocess.run(
                ["docker", "ps", "--filter", f"name={self.container_name}", "--format", "{{.Names}}"],
                capture_output=True, text=True
            )
            if self.container_name in result.stdout:
                logger.info(f"✅ Docker容器运行中: {self.container_name}")
            else:
                logger.error(f"❌ Docker容器未运行: {self.container_name}")
                return False
        except FileNotFoundError:
            logger.error("❌ Docker未安装或不可用")
            return False
        
        logger.info("✅ 前置条件检查通过")
        return True
    
    def install_dependencies(self) -> bool:
        """安装依赖"""
        logger.info("📦 安装前端依赖...")
        
        try:
            # 切换到前端目录
            os.chdir(self.frontend_dir)
            
            # 检查是否需要安装依赖
            if not self.node_modules_dir.exists():
                logger.info("node_modules不存在，执行npm install...")
                result = subprocess.run(["npm", "install"], check=True)
            else:
                logger.info("node_modules已存在，跳过依赖安装")
            
            logger.info("✅ 依赖安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 依赖安装失败: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 依赖安装异常: {e}")
            return False
    
    def build_frontend(self, force_rebuild: bool = False) -> bool:
        """构建前端"""
        logger.info("🔨 构建前端项目...")
        
        try:
            # 切换到前端目录
            os.chdir(self.frontend_dir)
            
            # 检查是否需要重新构建
            if self.dist_dir.exists() and not force_rebuild:
                logger.info("dist目录已存在，检查是否需要重新构建...")
                
                # 简单的时间戳检查
                dist_mtime = os.path.getmtime(self.dist_dir)
                src_mtime = max(
                    os.path.getmtime(self.frontend_dir / "src"),
                    os.path.getmtime(self.package_json)
                )
                
                if dist_mtime > src_mtime:
                    logger.info("构建文件是最新的，跳过构建")
                    return True
            
            # 清理旧的构建文件
            if self.dist_dir.exists():
                logger.info("清理旧的构建文件...")
                shutil.rmtree(self.dist_dir)
            
            # 执行构建
            logger.info("执行npm run build...")
            result = subprocess.run(["npm", "run", "build"], check=True)
            
            # 验证构建结果
            if not self.dist_dir.exists():
                logger.error("❌ 构建完成但dist目录不存在")
                return False
            
            # 统计构建文件
            build_files = list(self.dist_dir.rglob("*"))
            file_count = len([f for f in build_files if f.is_file()])
            
            logger.info(f"✅ 前端构建完成，生成 {file_count} 个文件")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 前端构建失败: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 前端构建异常: {e}")
            return False
    
    def clear_container_static(self) -> bool:
        """清理容器内的静态文件"""
        logger.info("🗑️  清理容器内旧的前端文件...")
        
        try:
            # 备份重要文件（如果有的话）
            backup_commands = [
                f"docker exec {self.container_name} find {self.container_static_path} -name '*.log' -type f",
                f"docker exec {self.container_name} find {self.container_static_path} -name 'config.*' -type f"
            ]
            
            important_files = []
            for cmd in backup_commands:
                try:
                    result = subprocess.run(cmd.split(), capture_output=True, text=True)
                    if result.returncode == 0 and result.stdout.strip():
                        important_files.extend(result.stdout.strip().split('\n'))
                except:
                    pass
            
            if important_files:
                logger.info(f"发现 {len(important_files)} 个重要文件，将保留")
            
            # 清理静态文件目录（保留重要文件）
            clear_cmd = f"docker exec {self.container_name} find {self.container_static_path} -type f ! -name '*.log' ! -name 'config.*' -delete"
            result = subprocess.run(clear_cmd.split(), check=True)
            
            # 清理空目录
            clear_dirs_cmd = f"docker exec {self.container_name} find {self.container_static_path} -type d -empty -delete"
            subprocess.run(clear_dirs_cmd.split(), check=False)  # 忽略错误
            
            logger.info("✅ 容器内旧文件清理完成")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 清理容器文件失败: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 清理容器文件异常: {e}")
            return False
    
    def deploy_to_container(self) -> bool:
        """部署到容器"""
        logger.info("🚀 部署前端文件到容器...")
        
        try:
            # 检查构建文件
            if not self.dist_dir.exists():
                logger.error("❌ 构建文件不存在，请先执行构建")
                return False
            
            # 复制文件到容器
            copy_cmd = f"docker cp {self.dist_dir}/. {self.container_name}:{self.container_static_path}/"
            result = subprocess.run(copy_cmd.split(), check=True)
            
            # 设置文件权限
            chmod_cmd = f"docker exec {self.container_name} chmod -R 644 {self.container_static_path}"
            subprocess.run(chmod_cmd.split(), check=False)  # 忽略权限错误
            
            # 设置目录权限
            chmod_dirs_cmd = f"docker exec {self.container_name} find {self.container_static_path} -type d -exec chmod 755 {{}} +"
            subprocess.run(chmod_dirs_cmd.split(), check=False)  # 忽略权限错误
            
            # 验证部署
            verify_cmd = f"docker exec {self.container_name} ls -la {self.container_static_path}"
            result = subprocess.run(verify_cmd.split(), capture_output=True, text=True)
            
            if result.returncode == 0:
                file_count = len(result.stdout.strip().split('\n')) - 3  # 减去 ., .., total
                logger.info(f"✅ 前端文件部署完成，容器内有 {file_count} 个项目")
            else:
                logger.warning("⚠️ 无法验证部署结果")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ 部署到容器失败: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 部署到容器异常: {e}")
            return False
    
    def clear_caches(self) -> bool:
        """清理缓存"""
        logger.info("🧹 清理服务器和浏览器缓存...")
        
        try:
            # 重启容器内的Web服务（如果有的话）
            restart_cmd = f"docker exec {self.container_name} pkill -f 'uvicorn|gunicorn|nginx' || true"
            subprocess.run(restart_cmd, shell=True, check=False)
            
            # 等待服务重启
            time.sleep(2)
            
            # 添加缓存清理标记文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            cache_buster_cmd = f"docker exec {self.container_name} echo '{timestamp}' > {self.container_static_path}/cache_buster.txt"
            subprocess.run(cache_buster_cmd, shell=True, check=False)
            
            logger.info("✅ 缓存清理完成")
            logger.info("💡 建议手动刷新浏览器缓存（Ctrl+F5 或 Cmd+Shift+R）")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 清理缓存异常: {e}")
            return False
    
    def verify_deployment(self) -> bool:
        """验证部署结果"""
        logger.info("🔍 验证部署结果...")
        
        try:
            # 检查容器内文件
            check_cmd = f"docker exec {self.container_name} find {self.container_static_path} -name '*.html' -o -name '*.js' -o -name '*.css'"
            result = subprocess.run(check_cmd.split(), capture_output=True, text=True)
            
            if result.returncode == 0:
                files = result.stdout.strip().split('\n')
                html_files = [f for f in files if f.endswith('.html')]
                js_files = [f for f in files if f.endswith('.js')]
                css_files = [f for f in files if f.endswith('.css')]
                
                logger.info(f"✅ 部署验证通过:")
                logger.info(f"   ├─ HTML文件: {len(html_files)} 个")
                logger.info(f"   ├─ JavaScript文件: {len(js_files)} 个")
                logger.info(f"   └─ CSS文件: {len(css_files)} 个")
                
                return True
            else:
                logger.error("❌ 无法验证部署文件")
                return False
                
        except Exception as e:
            logger.error(f"❌ 验证部署异常: {e}")
            return False
    
    def deploy(self, build_only: bool = False, deploy_only: bool = False, force_rebuild: bool = False) -> bool:
        """执行完整部署流程"""
        logger.info("=" * 80)
        logger.info("🚀 OmniLink前端部署开始")
        logger.info("=" * 80)
        
        start_time = time.time()
        
        try:
            # 检查前置条件
            if not self.check_prerequisites():
                return False
            
            if not deploy_only:
                # 安装依赖
                if not self.install_dependencies():
                    return False
                
                # 构建前端
                if not self.build_frontend(force_rebuild):
                    return False
            
            if build_only:
                logger.info("✅ 仅构建模式完成")
                return True
            
            # 清理容器静态文件
            if not self.clear_container_static():
                return False
            
            # 部署到容器
            if not self.deploy_to_container():
                return False
            
            # 清理缓存
            if not self.clear_caches():
                return False
            
            # 验证部署
            if not self.verify_deployment():
                return False
            
            duration = time.time() - start_time
            logger.info("=" * 80)
            logger.info(f"🎉 OmniLink前端部署成功完成！耗时: {duration:.1f}秒")
            logger.info("=" * 80)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 部署流程异常: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OmniLink前端构建和部署工具")
    parser.add_argument("--build-only", action="store_true", help="仅构建，不部署")
    parser.add_argument("--deploy-only", action="store_true", help="仅部署已构建的文件")
    parser.add_argument("--force-rebuild", action="store_true", help="强制重新构建")
    parser.add_argument("--project-root", help="项目根目录路径")
    
    args = parser.parse_args()
    
    # 创建部署器
    deployer = OmniLinkFrontendDeployer(args.project_root)
    
    # 执行部署
    success = deployer.deploy(
        build_only=args.build_only,
        deploy_only=args.deploy_only,
        force_rebuild=args.force_rebuild
    )
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
