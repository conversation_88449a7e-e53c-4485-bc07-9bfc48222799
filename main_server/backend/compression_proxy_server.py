#!/usr/bin/env python3
"""
压缩代理服务器
版本: 1.0
创建日期: 2025-01-15
描述: 为网络穿透工具提供透明的数据压缩代理服务，支持TCP/UDP协议
"""

import asyncio
import logging
import time
import socket
import struct
import hashlib
import json
from typing import Dict, Any, Optional, Tuple, List
from tunnel_compression_adapter import TunnelCompressionAdapter, TunnelType, ProtocolType

logger = logging.getLogger(__name__)

class CompressionProxyServer:
    """压缩代理服务器"""
    
    def __init__(self, listen_port: int, target_host: str, target_port: int,
                 tunnel_type: TunnelType = TunnelType.FRP, 
                 protocol: ProtocolType = ProtocolType.TCP):
        """
        初始化压缩代理服务器
        
        Args:
            listen_port: 监听端口
            target_host: 目标主机
            target_port: 目标端口
            tunnel_type: 穿透工具类型
            protocol: 协议类型
        """
        self.listen_port = listen_port
        self.target_host = target_host
        self.target_port = target_port
        self.tunnel_type = tunnel_type
        self.protocol = protocol
        
        # 创建压缩适配器
        self.adapter = TunnelCompressionAdapter(
            tunnel_type=tunnel_type,
            protocol=protocol,
            max_concurrent_users=500,
            bandwidth_limit_mbps=3.0
        )
        
        # 连接管理
        self.connections: Dict[str, Dict[str, Any]] = {}
        self.server = None
        self.running = False
        
        # 统计信息
        self.stats = {
            'start_time': time.time(),
            'total_connections': 0,
            'active_connections': 0,
            'total_bytes_proxied': 0,
            'total_bytes_compressed': 0,
            'connection_errors': 0
        }
        
        logger.info(f"初始化压缩代理服务器: {listen_port} -> {target_host}:{target_port}")
    
    async def start(self):
        """启动代理服务器"""
        try:
            # 创建服务器但不立即启动
            self.server = await asyncio.start_server(
                self._handle_client,
                '0.0.0.0',
                self.listen_port,
                reuse_address=True,
                reuse_port=True
            )

            self.running = True

            addr = self.server.sockets[0].getsockname()
            logger.info(f"压缩代理服务器启动成功: {addr[0]}:{addr[1]}")

            # 启动服务器监听
            await self.server.start_serving()

            return True

        except Exception as e:
            logger.error(f"启动代理服务器失败: {e}")
            self.running = False
            raise

    async def serve_forever(self):
        """持续运行服务器"""
        try:
            if self.server and self.running:
                await self.server.serve_forever()
        except asyncio.CancelledError:
            logger.info("压缩代理服务器被取消")
        except Exception as e:
            logger.error(f"压缩代理服务器运行异常: {e}")
            self.running = False
            raise

    async def wait_closed(self):
        """等待服务器关闭"""
        if self.server:
            await self.server.wait_closed()
    
    async def stop(self):
        """停止代理服务器"""
        try:
            self.running = False
            if self.server:
                self.server.close()
                await self.server.wait_closed()
                self.server = None
            logger.info(f"压缩代理服务器已停止 (端口: {self.listen_port})")
        except Exception as e:
            logger.error(f"停止代理服务器异常: {e}")

    def is_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                sock.bind(('0.0.0.0', port))
                return True
        except OSError:
            return False

    async def verify_listening(self) -> bool:
        """验证服务器是否正在监听"""
        if not self.server or not self.running:
            return False

        try:
            # 尝试连接到自己的端口
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection('127.0.0.1', self.listen_port),
                timeout=1.0
            )
            writer.close()
            await writer.wait_closed()
            return True
        except Exception:
            return False
    
    async def _handle_client(self, client_reader: asyncio.StreamReader, 
                           client_writer: asyncio.StreamWriter):
        """处理客户端连接"""
        client_addr = client_writer.get_extra_info('peername')
        connection_id = f"{client_addr[0]}:{client_addr[1]}_{int(time.time())}"
        
        logger.info(f"新客户端连接: {connection_id}")
        
        try:
            # 连接到目标服务器
            target_reader, target_writer = await asyncio.open_connection(
                self.target_host, self.target_port
            )
            
            # 创建压缩连接
            success = await self.adapter.create_connection(
                connection_id,
                {
                    'client_addr': client_addr,
                    'target_addr': (self.target_host, self.target_port),
                    'protocol': self.protocol,
                    'tunnel_type': self.tunnel_type
                }
            )
            
            if not success:
                logger.error(f"创建压缩连接失败: {connection_id}")
                client_writer.close()
                target_writer.close()
                return
            
            # 记录连接信息
            self.connections[connection_id] = {
                'client_reader': client_reader,
                'client_writer': client_writer,
                'target_reader': target_reader,
                'target_writer': target_writer,
                'created_at': time.time(),
                'bytes_client_to_target': 0,
                'bytes_target_to_client': 0
            }
            
            # 更新统计信息
            self.stats['total_connections'] += 1
            self.stats['active_connections'] += 1
            
            # 启动双向数据转发
            await asyncio.gather(
                self._forward_data(connection_id, 'client_to_target'),
                self._forward_data(connection_id, 'target_to_client'),
                return_exceptions=True
            )
            
        except Exception as e:
            logger.error(f"处理客户端连接失败 {connection_id}: {e}")
            self.stats['connection_errors'] += 1
        
        finally:
            await self._cleanup_connection(connection_id)
    
    async def _forward_data(self, connection_id: str, direction: str):
        """转发数据"""
        if connection_id not in self.connections:
            return
        
        conn = self.connections[connection_id]
        
        if direction == 'client_to_target':
            reader = conn['client_reader']
            writer = conn['target_writer']
            compress = True
        else:
            reader = conn['target_reader']
            writer = conn['client_writer']
            compress = False
        
        try:
            while True:
                # 读取数据
                data = await reader.read(8192)
                if not data:
                    break
                
                # 处理数据
                if compress:
                    # 客户端到目标：压缩数据
                    processed_data = await self.adapter.compress_data(connection_id, data)
                    conn['bytes_client_to_target'] += len(data)
                else:
                    # 目标到客户端：解压数据
                    processed_data = await self.adapter.decompress_data(connection_id, data)
                    conn['bytes_target_to_client'] += len(data)
                
                if processed_data:
                    # 发送处理后的数据
                    writer.write(processed_data)
                    await writer.drain()
                    
                    self.stats['total_bytes_proxied'] += len(data)
                    if compress:
                        self.stats['total_bytes_compressed'] += len(processed_data)
                
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"转发数据失败 {connection_id} {direction}: {e}")
        
        finally:
            try:
                writer.close()
                await writer.wait_closed()
            except:
                pass
    
    async def _cleanup_connection(self, connection_id: str):
        """清理连接"""
        if connection_id not in self.connections:
            return
        
        conn = self.connections[connection_id]
        
        # 关闭所有连接
        for writer_key in ['client_writer', 'target_writer']:
            if writer_key in conn:
                try:
                    conn[writer_key].close()
                    await conn[writer_key].wait_closed()
                except:
                    pass
        
        # 关闭压缩连接
        await self.adapter.close_connection(connection_id)
        
        # 移除连接记录
        del self.connections[connection_id]
        
        # 更新统计信息
        self.stats['active_connections'] -= 1
        
        logger.info(f"清理连接: {connection_id}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        uptime = time.time() - self.stats['start_time']
        
        # 计算压缩率
        if self.stats['total_bytes_proxied'] > 0:
            compression_ratio = self.stats['total_bytes_compressed'] / self.stats['total_bytes_proxied']
            compression_rate = (1 - compression_ratio) * 100
        else:
            compression_ratio = 1.0
            compression_rate = 0.0
        
        # 计算吞吐量
        throughput_mbps = (self.stats['total_bytes_proxied'] * 8 / 1024 / 1024) / uptime if uptime > 0 else 0
        
        return {
            'proxy_server': {
                'listen_port': self.listen_port,
                'target_host': self.target_host,
                'target_port': self.target_port,
                'tunnel_type': self.tunnel_type,
                'protocol': self.protocol,
                'running': self.running,
                'uptime_seconds': uptime
            },
            'connections': {
                'total': self.stats['total_connections'],
                'active': self.stats['active_connections'],
                'errors': self.stats['connection_errors']
            },
            'traffic': {
                'total_bytes_proxied': self.stats['total_bytes_proxied'],
                'total_bytes_compressed': self.stats['total_bytes_compressed'],
                'compression_ratio': compression_ratio,
                'compression_rate': compression_rate,
                'throughput_mbps': throughput_mbps
            },
            'adapter_stats': self.adapter.get_stats()
        }

class CompressionProxyManager:
    """压缩代理管理器"""
    
    def __init__(self):
        self.proxies: Dict[str, CompressionProxyServer] = {}
        self.tasks: Dict[str, asyncio.Task] = {}
    
    async def create_proxy(self, proxy_id: str, listen_port: int,
                          target_host: str, target_port: int,
                          tunnel_type: TunnelType = TunnelType.FRP,
                          protocol: ProtocolType = ProtocolType.TCP) -> bool:
        """创建压缩代理"""
        if proxy_id in self.proxies:
            logger.warning(f"代理已存在: {proxy_id}")
            return False

        try:
            proxy = CompressionProxyServer(
                listen_port=listen_port,
                target_host=target_host,
                target_port=target_port,
                tunnel_type=tunnel_type,
                protocol=protocol
            )

            # 启动代理服务器
            await proxy.start()

            # 验证服务器是否真正启动
            await asyncio.sleep(0.1)  # 短暂等待
            if not proxy.running:
                raise Exception("代理服务器启动失败")

            self.proxies[proxy_id] = proxy

            # 创建服务器运行任务，添加错误处理
            async def run_proxy_with_error_handling():
                try:
                    await proxy.serve_forever()
                except Exception as e:
                    logger.error(f"代理服务器运行异常 {proxy_id}: {e}")
                    proxy.running = False
                    # 尝试重启
                    await self._restart_proxy(proxy_id)

            task = asyncio.create_task(run_proxy_with_error_handling())
            self.tasks[proxy_id] = task

            logger.info(f"创建压缩代理成功: {proxy_id} (端口: {listen_port})")
            return True

        except Exception as e:
            logger.error(f"创建压缩代理失败 {proxy_id}: {e}")
            # 清理失败的代理
            if proxy_id in self.proxies:
                del self.proxies[proxy_id]
            return False
    
    async def stop_proxy(self, proxy_id: str) -> bool:
        """停止压缩代理"""
        if proxy_id not in self.proxies:
            logger.warning(f"代理不存在: {proxy_id}")
            return False
        
        try:
            # 停止代理服务器
            await self.proxies[proxy_id].stop()
            
            # 取消任务
            if proxy_id in self.tasks:
                self.tasks[proxy_id].cancel()
                try:
                    await self.tasks[proxy_id]
                except asyncio.CancelledError:
                    pass
                del self.tasks[proxy_id]
            
            # 移除代理
            del self.proxies[proxy_id]
            
            logger.info(f"停止压缩代理: {proxy_id}")
            return True
            
        except Exception as e:
            logger.error(f"停止压缩代理失败 {proxy_id}: {e}")
            return False
    
    def get_proxy_stats(self, proxy_id: str) -> Optional[Dict[str, Any]]:
        """获取代理统计信息"""
        if proxy_id not in self.proxies:
            return None

        return self.proxies[proxy_id].get_stats()

    async def _restart_proxy(self, proxy_id: str) -> bool:
        """重启代理服务器"""
        if proxy_id not in self.proxies:
            return False

        try:
            proxy = self.proxies[proxy_id]
            logger.info(f"尝试重启代理服务器: {proxy_id}")

            # 停止旧的服务器
            if proxy.server:
                proxy.server.close()
                await proxy.wait_closed()

            # 重新启动
            await proxy.start()

            if proxy.running:
                logger.info(f"代理服务器重启成功: {proxy_id}")
                return True
            else:
                logger.error(f"代理服务器重启失败: {proxy_id}")
                return False

        except Exception as e:
            logger.error(f"重启代理服务器异常 {proxy_id}: {e}")
            return False

    def is_proxy_running(self, proxy_id: str) -> bool:
        """检查代理是否运行中"""
        if proxy_id not in self.proxies:
            return False

        return self.proxies[proxy_id].running

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            "total_proxies": len(self.proxies),
            "running_proxies": 0,
            "failed_proxies": [],
            "proxy_details": {}
        }

        for proxy_id, proxy in self.proxies.items():
            if proxy.running:
                health_status["running_proxies"] += 1
            else:
                health_status["failed_proxies"].append(proxy_id)

            health_status["proxy_details"][proxy_id] = {
                "running": proxy.running,
                "listen_port": proxy.listen_port,
                "target": f"{proxy.target_host}:{proxy.target_port}"
            }

        return health_status
    
    def get_all_stats(self) -> Dict[str, Any]:
        """获取所有代理统计信息"""
        return {
            proxy_id: proxy.get_stats()
            for proxy_id, proxy in self.proxies.items()
        }
    
    async def stop_all(self):
        """停止所有代理"""
        proxy_ids = list(self.proxies.keys())
        for proxy_id in proxy_ids:
            await self.stop_proxy(proxy_id)

# 创建全局代理管理器
proxy_manager = CompressionProxyManager()

async def main():
    """测试主函数"""
    # 创建测试代理
    await proxy_manager.create_proxy(
        proxy_id="test_proxy",
        listen_port=8080,
        target_host="127.0.0.1",
        target_port=8000,
        tunnel_type=TunnelType.FRP,
        protocol=ProtocolType.HTTP
    )
    
    print("压缩代理服务器已启动，监听端口 8080")
    print("将HTTP请求转发到 127.0.0.1:8000")
    
    try:
        # 保持运行
        while True:
            await asyncio.sleep(10)
            stats = proxy_manager.get_proxy_stats("test_proxy")
            if stats:
                print(f"活跃连接: {stats['connections']['active']}, "
                      f"总流量: {stats['traffic']['total_bytes_proxied']} bytes, "
                      f"压缩率: {stats['traffic']['compression_rate']:.1f}%")
    except KeyboardInterrupt:
        print("停止代理服务器...")
        await proxy_manager.stop_all()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
