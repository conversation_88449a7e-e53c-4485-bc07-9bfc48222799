import{_ as g}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                *//* empty css                *//* empty css                    *//* empty css                  */import{u as v}from"./useOrganizationTree-BP0VBfMv.js";import{o as h,c as E,e as o,w as n,ah as y,i as z,cR as b,p as B,d as a,t as d,m as N,n as r,s as i}from"./index-DlnWH8GN.js";import"./permission-assignment-K1GAuJSC.js";import"./index-B1XWH8MS.js";const T={class:"organizations-container"},$={class:"card-header"},V={class:"custom-tree-node"},w={__name:"index",setup(D){const{organizationTree:p}=v({autoLoad:!0,enableCache:!0}),c={children:"children",label:"name"},_=()=>{i.info("添加组织功能开发中...")},m=t=>{i.info(`编辑组织: ${t.name}`)},u=t=>{i.info(`删除组织: ${t.name}`)};return h(()=>{}),(t,e)=>{const s=N,f=b,k=y;return z(),E("div",T,[o(k,null,{header:n(()=>[a("div",$,[e[1]||(e[1]=a("span",null,"组织架构管理",-1)),o(s,{type:"primary",onClick:_},{default:n(()=>e[0]||(e[0]=[r("添加组织",-1)])),_:1,__:[0]})])]),default:n(()=>[o(f,{data:B(p),props:c,"node-key":"id","default-expand-all":"","expand-on-click-node":!1},{default:n(({node:x,data:l})=>[a("span",V,[a("span",null,d(x.label)+" ("+d(t.getLevelName(l.level))+")",1),a("span",null,[o(s,{size:"mini",onClick:C=>m(l)},{default:n(()=>e[2]||(e[2]=[r("编辑",-1)])),_:2,__:[2]},1032,["onClick"]),o(s,{size:"mini",type:"danger",onClick:C=>u(l)},{default:n(()=>e[3]||(e[3]=[r("删除",-1)])),_:2,__:[3]},1032,["onClick"])])])]),_:1},8,["data"])]),_:1})])}}},q=g(w,[["__scopeId","data-v-46e8e4c2"]]);export{q as default};
