import{_ as ls}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                 *//* empty css               *//* empty css                *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                  *//* empty css                         */import{u as is,r as S,g as ds,a as z,o as cs,bL as us,c as _,d as e,a8 as _s,e as t,w as n,m as rs,t as l,a6 as ps,M as fs,a9 as ms,y as p,z as y,cJ as vs,s as x,b as ys,h as gs,i,n as u,E as bs,p as c,da as ks,L as O,S as L,dx as hs,cY as ws,cS as xs,O as Ms,P as Cs,Q as Ds,cK as Es,ah as Ns,k as Ts,ac as Is,ad as Ss,dk as $s,d3 as As,d0 as Bs,R as Vs,df as Us,dh as zs,cU as Os,a4 as Ls,a5 as Rs,d5 as R,$ as Gs,a3 as js}from"./index-DlnWH8GN.js";const Ps={class:"device-detail"},Fs={class:"detail-header"},qs={class:"header-left"},Hs={class:"device-title"},Js={class:"header-right"},Ks={class:"detail-content"},Qs={class:"card-header"},Ys={class:"info-content"},Ws={class:"info-item"},Xs={class:"info-value"},Zs={class:"info-item"},se={key:1,class:"info-value"},ee={key:0,class:"info-item"},te={class:"info-value secondary"},ne={class:"info-item"},oe={class:"info-item"},ae={class:"info-item"},le={key:1,class:"info-value"},ie={class:"card-header"},de={class:"info-content"},ce={class:"info-item"},ue={class:"info-value code"},_e={class:"info-item"},re={class:"info-value code"},pe={class:"info-item"},fe={class:"info-value code"},me={class:"info-item"},ve={class:"info-value"},ye={class:"info-item"},ge={class:"info-value code"},be={class:"info-item"},ke={class:"info-value code"},he={key:0,class:"usb-ids-section"},we={key:0,class:"info-item"},xe={class:"info-value highlight"},Me={key:1,class:"info-item"},Ce={class:"info-value highlight"},De={key:2,class:"info-item"},Ee={class:"info-value"},Ne={key:3,class:"info-item"},Te={class:"card-header"},Ie={class:"info-content"},Se={class:"info-item"},$e={class:"info-value"},Ae={class:"info-item"},Be={class:"info-value code"},Ve={class:"info-item"},Ue={class:"info-value code"},ze={class:"info-item"},Oe={class:"card-header"},Le={class:"info-content"},Re={class:"info-item"},Ge={class:"info-value"},je={class:"info-item"},Pe={class:"info-value"},Fe={class:"info-item"},qe={class:"info-value"},He={class:"info-item"},Je={class:"info-value"},Ke={class:"info-item"},Qe={class:"info-value"},Ye={class:"info-item"},We={class:"info-value"},Xe={class:"card-header"},Ze={class:"occupied-content"},st={class:"occupied-user"},et={class:"user-avatar"},tt={class:"user-info"},nt={class:"user-name"},ot={class:"user-contact"},at={class:"occupied-details"},lt={class:"detail-item"},it={class:"detail-value"},dt={class:"detail-item"},ct={class:"detail-value"},ut={class:"detail-item"},_t={class:"detail-value"},rt={class:"occupied-actions"},pt={class:"card-header"},ft={class:"header-actions"},mt={class:"groups-content"},vt={key:0,class:"empty-groups"},yt={key:1,class:"groups-list"},gt={class:"group-device-count"},bt={__name:"DeviceDetail",setup(kt){const G=ds(),M=ys(),j=is(),C=S(!1),m=S(!1),P=G.params.id,o=z({id:null,device_id:"",device_name:"",custom_name:"",device_type:"unknown",status:"idle",vendor_id:"",product_id:"",hardware_signature:"",description:"",physical_port:"",port_location_code:"",server_name:"",server_ip:"",server_port:"",server_status:"online",remark:"",created_at:null,updated_at:null,last_connected:null,last_connected_user:"",connection_count:0,total_usage_time:0,current_user_name:"",current_user_contact:"",occupied_start_time:null,occupied_duration:0,estimated_end_time:null}),f=z({custom_name:"",device_type:"unknown",remark:""}),N=S([]);let T=null;const F=()=>{M.push("/device-center")},$=a=>({idle:"success",occupied:"warning",damaged:"danger",offline:"info"})[a]||"info",A=a=>({idle:"空闲",occupied:"被占用",damaged:"硬件损坏",offline:"离线"})[a]||"未知",q=a=>({encryption_key:"danger",storage:"warning",input:"info",communication:"success",hardware:"primary",unknown:""})[a]||"",H=a=>({encryption_key:"加密锁",storage:"存储设备",input:"输入设备",communication:"通信设备",hardware:"硬件设备",unknown:"未知设备"})[a]||"待补充",J=a=>({usb_ids_enhanced:"success",usb_ids:"primary",local_rules:"warning"})[a]||"info",K=a=>({usb_ids_enhanced:"USB.IDS增强识别",usb_ids:"USB.IDS标准识别",local_rules:"本地规则识别"})[a]||"未知来源",w=a=>a?new Date(a).toLocaleString():"N/A",B=a=>{if(!a)return"0分钟";const s=Math.floor(a/3600),d=Math.floor(a%3600/60);return s>0?`${s}小时${d}分钟`:`${d}分钟`},D=async()=>{C.value=!0;try{const a=await fetch(`/api/v1/devices/${P}`,{method:"GET",headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const s=await a.json();Object.assign(o,s),f.custom_name=o.custom_name||"",f.device_type=o.device_type||"unknown",f.remark=o.remark||"",N.value=[]}catch(a){console.error("获取设备详情失败:",a),x.error(`获取设备详情失败: ${a.message}`),a.message.includes("404")&&M.push("/device-center")}finally{C.value=!1}},Q=async a=>{try{await js.confirm(`确定要执行 "${V(a)}" 操作吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),x.success(`${V(a)} 操作已执行`),a==="delete"?M.push("/device-center"):D()}catch{}},V=a=>({unbind:"解除绑定",reset:"重置设备",delete:"删除设备"})[a]||a,Y=()=>{x.info(`联系用户: ${o.current_user_name} (${o.current_user_contact})`)},W=()=>{x.info("已发送设备释放请求")},X=()=>{x.info("管理设备分组")},Z=a=>{M.push(`/device-center/device-group/${a.id}`)};return cs(()=>{D(),T=setInterval(D,3e4)}),us(()=>{T&&clearInterval(T)}),(a,s)=>{const d=bs,v=rs,ss=gs("OfflineOutlined"),g=ps,I=Ds,es=Cs,ts=fs,U=Ts,b=Ss,ns=Is,k=Ns,h=Es,E=vs,os=Os,as=ms;return i(),_("div",Ps,[e("div",Fs,[e("div",qs,[t(v,{onClick:F,type:"text",class:"back-button"},{default:n(()=>[t(d,null,{default:n(()=>[t(c(ks))]),_:1}),s[4]||(s[4]=u(" 返回设备列表 ",-1))]),_:1,__:[4]}),e("div",Hs,[e("h2",null,l(o.custom_name||o.device_name||"设备详情"),1),t(g,{type:$(o.status),size:"large"},{default:n(()=>[t(d,{class:"status-icon"},{default:n(()=>[o.status==="idle"?(i(),p(c(O),{key:0})):o.status==="occupied"?(i(),p(c(L),{key:1})):o.status==="damaged"?(i(),p(c(hs),{key:2})):(i(),p(ss,{key:3}))]),_:1}),u(" "+l(A(o.status)),1)]),_:1},8,["type"])])]),e("div",Js,[t(v,{onClick:D,loading:C.value,type:"primary"},{default:n(()=>[t(d,null,{default:n(()=>[t(c(ws))]),_:1}),s[5]||(s[5]=u(" 刷新数据 ",-1))]),_:1,__:[5]},8,["loading"]),t(v,{onClick:s[0]||(s[0]=r=>m.value=!m.value),type:m.value?"success":"warning"},{default:n(()=>[t(d,null,{default:n(()=>[t(c(xs))]),_:1}),u(" "+l(m.value?"保存修改":"修改信息"),1)]),_:1},8,["type"]),t(ts,{onCommand:Q},{dropdown:n(()=>[t(es,null,{default:n(()=>[t(I,{command:"unbind"},{default:n(()=>s[7]||(s[7]=[u("解除绑定",-1)])),_:1,__:[7]}),t(I,{command:"reset"},{default:n(()=>s[8]||(s[8]=[u("重置设备",-1)])),_:1,__:[8]}),t(I,{divided:"",command:"delete"},{default:n(()=>s[9]||(s[9]=[u("删除设备",-1)])),_:1,__:[9]})]),_:1})]),default:n(()=>[t(v,{type:"danger"},{default:n(()=>[s[6]||(s[6]=u(" 设备操作 ",-1)),t(d,{class:"el-icon--right"},{default:n(()=>[t(c(Ms))]),_:1})]),_:1,__:[6]})]),_:1})])]),_s((i(),_("div",Ks,[t(E,{gutter:20,class:"content-sections"},{default:n(()=>[t(h,{span:12},{default:n(()=>[t(k,{class:"info-card"},{header:n(()=>[e("div",Qs,[t(d,null,{default:n(()=>[t(c($s))]),_:1}),s[10]||(s[10]=e("span",null,"基础信息",-1))])]),default:n(()=>[e("div",Ys,[e("div",Ws,[s[11]||(s[11]=e("span",{class:"info-label"},"设备ID：",-1)),e("span",Xs,l(o.device_id),1)]),e("div",Zs,[s[12]||(s[12]=e("span",{class:"info-label"},"设备名称：",-1)),m.value?(i(),p(U,{key:0,modelValue:f.custom_name,"onUpdate:modelValue":s[1]||(s[1]=r=>f.custom_name=r),placeholder:"自定义设备名称",class:"edit-input"},null,8,["modelValue"])):(i(),_("span",se,l(o.custom_name||o.device_name),1))]),o.custom_name?(i(),_("div",ee,[s[13]||(s[13]=e("span",{class:"info-label"},"自动生成名称：",-1)),e("span",te,l(o.device_name),1)])):y("",!0),e("div",ne,[s[14]||(s[14]=e("span",{class:"info-label"},"设备类型：",-1)),m.value?(i(),p(ns,{key:0,modelValue:f.device_type,"onUpdate:modelValue":s[2]||(s[2]=r=>f.device_type=r),placeholder:"选择设备类型",class:"edit-input"},{default:n(()=>[t(b,{label:"加密锁",value:"encryption_key"}),t(b,{label:"存储设备",value:"storage"}),t(b,{label:"输入设备",value:"input"}),t(b,{label:"通信设备",value:"communication"}),t(b,{label:"硬件设备",value:"hardware"}),t(b,{label:"未知设备",value:"unknown"})]),_:1},8,["modelValue"])):(i(),p(g,{key:1,type:q(o.device_type),size:"small"},{default:n(()=>[u(l(H(o.device_type)),1)]),_:1},8,["type"]))]),e("div",oe,[s[15]||(s[15]=e("span",{class:"info-label"},"设备状态：",-1)),t(g,{type:$(o.status),size:"small"},{default:n(()=>[u(l(A(o.status)),1)]),_:1},8,["type"])]),e("div",ae,[s[16]||(s[16]=e("span",{class:"info-label"},"备注信息：",-1)),m.value?(i(),p(U,{key:0,modelValue:f.remark,"onUpdate:modelValue":s[3]||(s[3]=r=>f.remark=r),type:"textarea",rows:3,placeholder:"设备备注信息（最长100字符）",maxlength:"100","show-word-limit":"",class:"edit-input"},null,8,["modelValue"])):(i(),_("span",le,l(o.remark||"无备注"),1))])])]),_:1})]),_:1}),t(h,{span:12},{default:n(()=>[t(k,{class:"info-card"},{header:n(()=>[e("div",ie,[t(d,null,{default:n(()=>[t(c(As))]),_:1}),s[17]||(s[17]=e("span",null,"硬件信息",-1))])]),default:n(()=>[e("div",de,[e("div",ce,[s[18]||(s[18]=e("span",{class:"info-label"},"厂商ID：",-1)),e("span",ue,l(o.vendor_id||"N/A"),1)]),e("div",_e,[s[19]||(s[19]=e("span",{class:"info-label"},"产品ID：",-1)),e("span",re,l(o.product_id||"N/A"),1)]),e("div",pe,[s[20]||(s[20]=e("span",{class:"info-label"},"硬件签名：",-1)),e("span",fe,l(o.hardware_signature||"N/A"),1)]),e("div",me,[s[21]||(s[21]=e("span",{class:"info-label"},"设备描述：",-1)),e("span",ve,l(o.description||"N/A"),1)]),e("div",ye,[s[22]||(s[22]=e("span",{class:"info-label"},"物理端口：",-1)),e("span",ge,l(o.physical_port||"N/A"),1)]),e("div",be,[s[23]||(s[23]=e("span",{class:"info-label"},"端口位置码：",-1)),e("span",ke,l(o.port_location_code||"N/A"),1)]),o.usb_ids_vendor_name||o.usb_ids_device_name?(i(),_("div",he,[s[28]||(s[28]=e("div",{class:"info-divider"},[e("span",{class:"divider-text"},"USB.IDS 识别信息")],-1)),o.usb_ids_vendor_name?(i(),_("div",we,[s[24]||(s[24]=e("span",{class:"info-label"},"厂商名称：",-1)),e("span",xe,l(o.usb_ids_vendor_name),1)])):y("",!0),o.usb_ids_device_name?(i(),_("div",Me,[s[25]||(s[25]=e("span",{class:"info-label"},"产品名称：",-1)),e("span",Ce,l(o.usb_ids_device_name),1)])):y("",!0),o.usb_ids_full_name?(i(),_("div",De,[s[26]||(s[26]=e("span",{class:"info-label"},"完整名称：",-1)),e("span",Ee,l(o.usb_ids_full_name),1)])):y("",!0),o.identification_source?(i(),_("div",Ne,[s[27]||(s[27]=e("span",{class:"info-label"},"识别来源：",-1)),t(g,{type:J(o.identification_source),size:"small"},{default:n(()=>[u(l(K(o.identification_source)),1)]),_:1},8,["type"])])):y("",!0)])):y("",!0)])]),_:1})]),_:1})]),_:1}),t(E,{gutter:20,class:"content-sections"},{default:n(()=>[t(h,{span:12},{default:n(()=>[t(k,{class:"info-card"},{header:n(()=>[e("div",Te,[t(d,null,{default:n(()=>[t(c(O))]),_:1}),s[29]||(s[29]=e("span",null,"所属服务器",-1))])]),default:n(()=>[e("div",Ie,[e("div",Se,[s[30]||(s[30]=e("span",{class:"info-label"},"服务器名称：",-1)),e("span",$e,l(o.server_name||"N/A"),1)]),e("div",Ae,[s[31]||(s[31]=e("span",{class:"info-label"},"服务器IP：",-1)),e("span",Be,l(o.server_ip||"N/A"),1)]),e("div",Ve,[s[32]||(s[32]=e("span",{class:"info-label"},"服务器端口：",-1)),e("span",Ue,l(o.server_port||"N/A"),1)]),e("div",ze,[s[33]||(s[33]=e("span",{class:"info-label"},"服务器状态：",-1)),t(g,{type:o.server_status==="online"?"success":"danger",size:"small"},{default:n(()=>[u(l(o.server_status==="online"?"在线":"离线"),1)]),_:1},8,["type"])])])]),_:1})]),_:1}),t(h,{span:12},{default:n(()=>[t(k,{class:"info-card"},{header:n(()=>[e("div",Oe,[t(d,null,{default:n(()=>[t(c(Bs))]),_:1}),s[34]||(s[34]=e("span",null,"使用记录",-1))])]),default:n(()=>[e("div",Le,[e("div",Re,[s[35]||(s[35]=e("span",{class:"info-label"},"创建时间：",-1)),e("span",Ge,l(w(o.created_at)),1)]),e("div",je,[s[36]||(s[36]=e("span",{class:"info-label"},"更新时间：",-1)),e("span",Pe,l(w(o.updated_at)),1)]),e("div",Fe,[s[37]||(s[37]=e("span",{class:"info-label"},"最后连接：",-1)),e("span",qe,l(w(o.last_connected)),1)]),e("div",He,[s[38]||(s[38]=e("span",{class:"info-label"},"连接用户：",-1)),e("span",Je,l(o.last_connected_user||"N/A"),1)]),e("div",Ke,[s[39]||(s[39]=e("span",{class:"info-label"},"连接次数：",-1)),e("span",Qe,l(o.connection_count||0)+" 次",1)]),e("div",Ye,[s[40]||(s[40]=e("span",{class:"info-label"},"总使用时长：",-1)),e("span",We,l(B(o.total_usage_time)),1)])])]),_:1})]),_:1})]),_:1}),o.status==="occupied"?(i(),p(E,{key:0,gutter:20,class:"content-sections"},{default:n(()=>[t(h,{span:24},{default:n(()=>[t(k,{class:"info-card occupied-info"},{header:n(()=>[e("div",Xe,[t(d,null,{default:n(()=>[t(c(L))]),_:1}),s[41]||(s[41]=e("span",null,"当前占用信息",-1))])]),default:n(()=>[e("div",Ze,[e("div",st,[e("div",et,[t(d,null,{default:n(()=>[t(c(Vs))]),_:1})]),e("div",tt,[e("div",nt,l(o.current_user_name||"N/A"),1),e("div",ot,l(o.current_user_contact||"N/A"),1)])]),e("div",at,[e("div",lt,[s[42]||(s[42]=e("span",{class:"detail-label"},"占用开始：",-1)),e("span",it,l(w(o.occupied_start_time)),1)]),e("div",dt,[s[43]||(s[43]=e("span",{class:"detail-label"},"持续时间：",-1)),e("span",ct,l(B(o.occupied_duration)),1)]),e("div",ut,[s[44]||(s[44]=e("span",{class:"detail-label"},"预计结束：",-1)),e("span",_t,l(w(o.estimated_end_time)||"未设置"),1)])]),e("div",rt,[t(v,{type:"primary",onClick:Y},{default:n(()=>[t(d,null,{default:n(()=>[t(c(Us))]),_:1}),s[45]||(s[45]=u(" 联系用户 ",-1))]),_:1,__:[45]}),t(v,{type:"warning",onClick:W},{default:n(()=>[t(d,null,{default:n(()=>[t(c(zs))]),_:1}),s[46]||(s[46]=u(" 请求释放 ",-1))]),_:1,__:[46]})])])]),_:1})]),_:1})]),_:1})):y("",!0),t(E,{gutter:20,class:"content-sections"},{default:n(()=>[t(h,{span:24},{default:n(()=>[t(k,{class:"info-card"},{header:n(()=>[e("div",pt,[t(d,null,{default:n(()=>[t(c(R))]),_:1}),s[48]||(s[48]=e("span",null,"所属分组",-1)),e("div",ft,[t(v,{size:"small",onClick:X},{default:n(()=>[t(d,null,{default:n(()=>[t(c(Gs))]),_:1}),s[47]||(s[47]=u(" 管理分组 ",-1))]),_:1,__:[47]})])])]),default:n(()=>[e("div",mt,[N.value.length===0?(i(),_("div",vt,[t(os,{description:"该设备未加入任何分组"})])):(i(),_("div",yt,[(i(!0),_(Ls,null,Rs(N.value,r=>(i(),p(g,{key:r.id,size:"large",class:"group-tag",onClick:ht=>Z(r)},{default:n(()=>[t(d,null,{default:n(()=>[t(c(R))]),_:1}),u(" "+l(r.name)+" ",1),e("span",gt,"("+l(r.device_count)+"个设备)",1)]),_:2},1032,["onClick"]))),128))]))])]),_:1})]),_:1})]),_:1})])),[[as,C.value]])])}}},Bt=ls(bt,[["__scopeId","data-v-5e9cb1fd"]]);export{Bt as default};
