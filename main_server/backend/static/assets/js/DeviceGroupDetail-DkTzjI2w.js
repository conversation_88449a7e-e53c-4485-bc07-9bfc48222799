import{_ as re}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css                 *//* empty css               *//* empty css                *//* empty css                  *//* empty css                         */import{r as B,g as ce,a as _e,aM as I,o as ue,bL as ve,c as V,d as s,a8 as pe,e as t,w as o,m as fe,t as l,a6 as me,M as ge,a9 as he,ah as be,cJ as we,s as u,b as ye,i as D,n as c,E as ke,p as _,da as De,y as J,d5 as xe,dn as Ce,cY as Ee,O as Te,P as $e,Q as Be,L as F,D as Ie,d3 as Se,R as Me,cK as Ae,dk as ze,dw as Ve,ab as Ne,ae as je,z as Le,k as Ue,c$ as Ge,ac as Re,ad as Oe,a3 as N}from"./index-BMqDoMFQ.js";import m from"./websocket-3gA8TYE1.js";const Pe={class:"device-group-detail"},He={class:"detail-header"},Je={class:"header-left"},Fe={class:"group-title"},Ke={class:"header-right"},Qe={class:"detail-content"},Ye={class:"overview-cards"},qe={class:"card-content"},We={class:"card-icon devices"},Xe={class:"card-info"},Ze={class:"card-value"},es={class:"card-content"},ss={class:"card-icon online"},ts={class:"card-info"},as={class:"card-value"},os={class:"card-content"},ns={class:"card-icon servers"},ls={class:"card-info"},is={class:"card-value"},ds={class:"card-content"},rs={class:"card-icon users"},cs={class:"card-info"},_s={class:"card-value"},us={class:"card-header"},vs={class:"info-content"},ps={class:"info-item"},fs={class:"info-value"},ms={class:"info-item"},gs={class:"info-value"},hs={class:"info-item"},bs={class:"info-value"},ws={class:"info-item"},ys={class:"info-value"},ks={class:"info-item"},Ds={class:"card-header"},xs={class:"info-content"},Cs={class:"info-item"},Es={class:"info-value"},Ts={class:"info-item"},$s={class:"info-value"},Bs={class:"info-item"},Is={class:"info-value"},Ss={class:"info-item"},Ms={class:"info-value"},As={class:"info-item"},zs={class:"info-value"},Vs={class:"card-header"},Ns={class:"header-actions"},js={class:"device-list"},Ls={class:"device-name"},Us={class:"primary-name"},Gs={key:0,class:"secondary-name"},Rs={class:"device-source"},Os={class:"source-primary"},Ps={class:"source-secondary"},Hs={class:"vid-pid"},Js={__name:"DeviceGroupDetail",setup(Fs){const j=ce(),S=ye(),y=B(!1),g=j.params.groupId,x=B(""),C=B(""),r=_e({id:null,name:"",description:"",device_count:0,user_count:0,status:"active",created_at:null,updated_at:null}),h=B([]);let M=null;const L=I(()=>h.value.filter(a=>a.status==="online").length),K=I(()=>h.value.filter(a=>a.status==="offline").length),U=I(()=>new Set(h.value.map(e=>e.server_id)).size),Q=I(()=>{let a=h.value;if(x.value){const e=x.value.toLowerCase();a=a.filter(i=>i.device_name&&i.device_name.toLowerCase().includes(e))}return C.value&&(a=a.filter(e=>e.status===C.value)),a}),Y=()=>{S.push("/device-center?tab=groups")},G=a=>({active:"success",inactive:"info",disabled:"danger"})[a]||"info",q=a=>({active:"活跃",inactive:"非活跃",disabled:"已禁用"})[a]||"未知",W=a=>({online:"success",offline:"info",busy:"warning",error:"danger"})[a]||"info",X=a=>({online:"在线",offline:"离线",busy:"占用中",error:"错误"})[a]||"未知",A=a=>a?new Date(a).toLocaleString():"N/A",R=a=>a?`0x${a.toString(16).toUpperCase().padStart(4,"0")}`:"N/A",b=async()=>{y.value=!0;try{const a=await fetch(`/api/v1/device-groups/${g}`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const e=await a.json();Object.assign(r,{id:e.id||g,name:e.name||"未命名分组",description:e.description||"",device_count:e.device_count||0,user_count:e.user_count||0,status:e.status||"active",created_at:e.created_at,updated_at:e.updated_at});const i=await fetch(`/api/v1/device-groups/${g}/devices`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(i.ok){const p=await i.json(),k=p.devices||p||[];h.value=k.map(d=>{var E,T,v;return{id:d.id,device_id:d.device_id,device_name:d.device_name,custom_name:d.custom_name,server_id:d.slave_server_id,server_name:((E=d.slave_server)==null?void 0:E.name)||"Unknown Server",server_ip:((T=d.slave_server)==null?void 0:T.ip_address)||"N/A",server_port:((v=d.slave_server)==null?void 0:v.port)||"N/A",physical_port:d.physical_port,usb_port:d.physical_port,vendor_id:d.vendor_id?parseInt(d.vendor_id,16):null,product_id:d.product_id?parseInt(d.product_id,16):null,status:d.status,last_connected:d.last_used_at||d.connected_at}})}else h.value=[];y.value=!1}catch(a){console.error("获取分组详情失败:",a),u.error("获取分组详情失败"),y.value=!1}},Z=async a=>{switch(a){case"edit":u.info("编辑分组功能开发中...");break;case"add-device":await ee();break;case"remove-device":u.info("请在设备列表中选择要移除的设备");break;case"delete":await se();break}},ee=async()=>{try{const a=await fetch(`/api/v1/devices?exclude_group=${g}`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error("获取设备列表失败");const e=await a.json(),i=e.devices||e||[];if(i.length===0){u.warning("没有可添加的设备");return}await N.confirm(`找到 ${i.length} 个可添加的设备，是否继续？`,"添加设备",{confirmButtonText:"继续",cancelButtonText:"取消",type:"info"}),u.info("设备选择对话框功能开发中...")}catch(a){a.message&&a.message!=="cancel"&&u.error(a.message)}},se=async()=>{try{if(await N.confirm(`确定要删除分组 "${r.name}" 吗？此操作不可恢复！`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error"}),!(await fetch(`/api/v1/device-groups/${g}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}})).ok)throw new Error("删除分组失败");u.success("分组删除成功"),S.push("/device-center/device-groups")}catch(a){a.message&&a.message!=="cancel"&&u.error(a.message)}},te=a=>{u.info(`查看设备详情: ${a.device_name}`)},ae=a=>{S.push(`/device-center/slave-server/${a.server_id}`)},oe=async a=>{try{await N.confirm(`确定要从分组中移除设备 "${a.custom_name||a.device_name}" 吗？`,"确认移除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await fetch(`/api/v1/device-groups/${g}/devices`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify({device_ids:[a.id]})});if(!e.ok)throw new Error(`移除失败: ${e.statusText}`);u.success(`设备 "${a.custom_name||a.device_name}" 已从分组中移除`),b()}catch(e){e.message&&e.message!=="cancel"&&u.error(e.message)}},O=a=>{console.log("收到设备分配更新事件:",a),a.assignments&&a.assignments.find(i=>i.group_id===parseInt(j.params.id))&&(console.log("当前分组有设备分配更新，刷新数据"),b())},w=a=>{console.log("收到设备更新事件:",a),b()};return ue(()=>{b(),M=setInterval(b,3e4),m.on("devices_batch_assigned",O),m.on("device_updates",w),m.on("device_added",w),m.on("device_removed",w)}),ve(()=>{M&&clearInterval(M),m.off("devices_batch_assigned",O),m.off("device_updates",w),m.off("device_added",w),m.off("device_removed",w)}),(a,e)=>{const i=ke,p=fe,k=me,d=Be,E=$e,T=ge,v=be,z=Ae,P=we,ne=Ue,$=Oe,le=Re,f=je,ie=Ne,de=he;return D(),V("div",Pe,[s("div",He,[s("div",Je,[t(p,{onClick:Y,type:"text",class:"back-button"},{default:o(()=>[t(i,null,{default:o(()=>[t(_(De))]),_:1}),e[2]||(e[2]=c(" 返回分组列表 ",-1))]),_:1,__:[2]}),s("div",Fe,[s("h2",null,l(r.name||"设备分组详情"),1),t(k,{type:G(r.status),size:"large"},{default:o(()=>[t(i,{class:"status-icon"},{default:o(()=>[r.device_count>0?(D(),J(_(xe),{key:0})):(D(),J(_(Ce),{key:1}))]),_:1}),c(" "+l(r.device_count||0)+" 个设备 ",1)]),_:1},8,["type"])])]),s("div",Ke,[t(p,{onClick:b,loading:y.value,type:"primary"},{default:o(()=>[t(i,null,{default:o(()=>[t(_(Ee))]),_:1}),e[3]||(e[3]=c(" 刷新数据 ",-1))]),_:1,__:[3]},8,["loading"]),t(T,{onCommand:Z},{dropdown:o(()=>[t(E,null,{default:o(()=>[t(d,{command:"edit"},{default:o(()=>e[5]||(e[5]=[c("编辑分组",-1)])),_:1,__:[5]}),t(d,{command:"add-device"},{default:o(()=>e[6]||(e[6]=[c("添加设备",-1)])),_:1,__:[6]}),t(d,{command:"remove-device"},{default:o(()=>e[7]||(e[7]=[c("移除设备",-1)])),_:1,__:[7]}),t(d,{divided:"",command:"delete"},{default:o(()=>e[8]||(e[8]=[c("删除分组",-1)])),_:1,__:[8]})]),_:1})]),default:o(()=>[t(p,{type:"success"},{default:o(()=>[e[4]||(e[4]=c(" 管理操作 ",-1)),t(i,{class:"el-icon--right"},{default:o(()=>[t(_(Te))]),_:1})]),_:1,__:[4]})]),_:1})])]),pe((D(),V("div",Qe,[s("div",Ye,[t(v,{class:"overview-card"},{default:o(()=>[s("div",qe,[s("div",We,[t(i,null,{default:o(()=>[t(_(F))]),_:1})]),s("div",Xe,[s("div",Ze,l(r.device_count||0),1),e[9]||(e[9]=s("div",{class:"card-label"},"设备总数",-1))])])]),_:1}),t(v,{class:"overview-card"},{default:o(()=>[s("div",es,[s("div",ss,[t(i,null,{default:o(()=>[t(_(Ie))]),_:1})]),s("div",ts,[s("div",as,l(L.value),1),e[10]||(e[10]=s("div",{class:"card-label"},"在线设备",-1))])])]),_:1}),t(v,{class:"overview-card"},{default:o(()=>[s("div",os,[s("div",ns,[t(i,null,{default:o(()=>[t(_(Se))]),_:1})]),s("div",ls,[s("div",is,l(U.value),1),e[11]||(e[11]=s("div",{class:"card-label"},"涉及服务器",-1))])])]),_:1}),t(v,{class:"overview-card"},{default:o(()=>[s("div",ds,[s("div",rs,[t(i,null,{default:o(()=>[t(_(Me))]),_:1})]),s("div",cs,[s("div",_s,l(r.user_count||0),1),e[12]||(e[12]=s("div",{class:"card-label"},"授权用户",-1))])])]),_:1})]),t(P,{gutter:20,class:"detail-sections"},{default:o(()=>[t(z,{span:12},{default:o(()=>[t(v,{class:"info-card"},{header:o(()=>[s("div",us,[t(i,null,{default:o(()=>[t(_(ze))]),_:1}),e[13]||(e[13]=s("span",null,"基础信息",-1))])]),default:o(()=>[s("div",vs,[s("div",ps,[e[14]||(e[14]=s("span",{class:"info-label"},"分组名称：",-1)),s("span",fs,l(r.name),1)]),s("div",ms,[e[15]||(e[15]=s("span",{class:"info-label"},"分组描述：",-1)),s("span",gs,l(r.description||"暂无描述"),1)]),s("div",hs,[e[16]||(e[16]=s("span",{class:"info-label"},"创建时间：",-1)),s("span",bs,l(A(r.created_at)),1)]),s("div",ws,[e[17]||(e[17]=s("span",{class:"info-label"},"更新时间：",-1)),s("span",ys,l(A(r.updated_at)),1)]),s("div",ks,[e[18]||(e[18]=s("span",{class:"info-label"},"分组状态：",-1)),t(k,{type:G(r.status)},{default:o(()=>[c(l(q(r.status)),1)]),_:1},8,["type"])])])]),_:1})]),_:1}),t(z,{span:12},{default:o(()=>[t(v,{class:"info-card"},{header:o(()=>[s("div",Ds,[t(i,null,{default:o(()=>[t(_(Ve))]),_:1}),e[19]||(e[19]=s("span",null,"统计信息",-1))])]),default:o(()=>[s("div",xs,[s("div",Cs,[e[20]||(e[20]=s("span",{class:"info-label"},"设备总数：",-1)),s("span",Es,l(r.device_count||0)+" 个",1)]),s("div",Ts,[e[21]||(e[21]=s("span",{class:"info-label"},"在线设备：",-1)),s("span",$s,l(L.value)+" 个",1)]),s("div",Bs,[e[22]||(e[22]=s("span",{class:"info-label"},"离线设备：",-1)),s("span",Is,l(K.value)+" 个",1)]),s("div",Ss,[e[23]||(e[23]=s("span",{class:"info-label"},"涉及服务器：",-1)),s("span",Ms,l(U.value)+" 台",1)]),s("div",As,[e[24]||(e[24]=s("span",{class:"info-label"},"授权用户：",-1)),s("span",zs,l(r.user_count||0)+" 人",1)])])]),_:1})]),_:1})]),_:1}),t(P,{gutter:20,class:"detail-sections"},{default:o(()=>[t(z,{span:24},{default:o(()=>[t(v,{class:"info-card"},{header:o(()=>[s("div",Vs,[t(i,null,{default:o(()=>[t(_(F))]),_:1}),e[25]||(e[25]=s("span",null,"设备列表",-1)),s("div",Ns,[t(ne,{modelValue:x.value,"onUpdate:modelValue":e[0]||(e[0]=n=>x.value=n),placeholder:"搜索设备名称...",style:{width:"200px","margin-right":"10px"},clearable:""},{prefix:o(()=>[t(i,null,{default:o(()=>[t(_(Ge))]),_:1})]),_:1},8,["modelValue"]),t(le,{modelValue:C.value,"onUpdate:modelValue":e[1]||(e[1]=n=>C.value=n),placeholder:"筛选状态",style:{width:"120px"},clearable:""},{default:o(()=>[t($,{label:"全部",value:""}),t($,{label:"在线",value:"online"}),t($,{label:"离线",value:"offline"}),t($,{label:"占用中",value:"busy"})]),_:1},8,["modelValue"])])])]),default:o(()=>[s("div",js,[t(ie,{data:Q.value,stripe:""},{default:o(()=>[t(f,{prop:"device_id",label:"设备ID",width:"120"}),t(f,{label:"设备名称","min-width":"150"},{default:o(({row:n})=>[s("div",Ls,[s("span",Us,l(n.custom_name||n.device_name),1),n.custom_name&&n.device_name!==n.custom_name?(D(),V("span",Gs," ("+l(n.device_name)+") ",1)):Le("",!0)])]),_:1}),t(f,{label:"设备来源",width:"200"},{default:o(({row:n})=>[s("div",Rs,[s("div",Os,l(n.server_name)+" - 端口"+l(n.physical_port||n.usb_port),1),s("div",Ps,l(n.server_ip)+":"+l(n.server_port),1)])]),_:1}),t(f,{label:"USB端口",width:"100"},{default:o(({row:n})=>[s("code",null,l(n.usb_port||"N/A"),1)]),_:1}),t(f,{label:"VID/PID",width:"150"},{default:o(({row:n})=>[s("div",Hs,[s("code",null,l(R(n.vendor_id)),1),e[26]||(e[26]=s("span",null,"/",-1)),s("code",null,l(R(n.product_id)),1)])]),_:1}),t(f,{label:"状态",width:"100"},{default:o(({row:n})=>[t(k,{type:W(n.status),size:"small"},{default:o(()=>[c(l(X(n.status)),1)]),_:2},1032,["type"])]),_:1}),t(f,{label:"最后连接",width:"150"},{default:o(({row:n})=>[c(l(A(n.last_connected)),1)]),_:1}),t(f,{label:"操作",width:"200",fixed:"right"},{default:o(({row:n})=>[t(p,{size:"small",onClick:H=>te(n)},{default:o(()=>e[27]||(e[27]=[c(" 详情 ",-1)])),_:2,__:[27]},1032,["onClick"]),t(p,{size:"small",onClick:H=>ae(n)},{default:o(()=>e[28]||(e[28]=[c(" 服务器 ",-1)])),_:2,__:[28]},1032,["onClick"]),t(p,{size:"small",type:"danger",onClick:H=>oe(n)},{default:o(()=>e[29]||(e[29]=[c(" 移除 ",-1)])),_:2,__:[29]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),_:1})]),_:1})]),_:1})])),[[de,y.value]])])}}},dt=re(Js,[["__scopeId","data-v-183df3ef"]]);export{dt as default};
