import{_}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                  */import{c,d as t,e,w as n,m as l,b as p,i as m,n as a}from"./index-BMqDoMFQ.js";const u={class:"error-container"},f={class:"error-content"},v={class:"error-actions"},k={__name:"404",setup(x){const s=p(),i=()=>{s.go(-1)},d=()=>{s.push("/")};return(B,o)=>{const r=l;return m(),c("div",u,[t("div",f,[o[2]||(o[2]=t("div",{class:"error-code"},"404",-1)),o[3]||(o[3]=t("div",{class:"error-title"},"页面未找到",-1)),o[4]||(o[4]=t("div",{class:"error-description"}," 抱歉，您访问的页面不存在。可能是页面已被删除或URL输入错误。 ",-1)),t("div",v,[e(r,{type:"primary",onClick:i},{default:n(()=>o[0]||(o[0]=[a("返回上一页",-1)])),_:1,__:[0]}),e(r,{onClick:d},{default:n(()=>o[1]||(o[1]=[a("回到首页",-1)])),_:1,__:[1]})])])])}}},N=_(k,[["__scopeId","data-v-29735cbf"]]);export{N as default};
