import{_ as st}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                 *//* empty css                    *//* empty css                  *//* empty css               *//* empty css                */import{r as _,u as Ze,aM as oe,o as Qe,bL as $t,s as g,a as Ee,cz as rt,y as O,i as h,w as s,a8 as Je,c as G,d as o,z as W,e,E as lt,p as $,L as pt,t as y,a6 as dt,n as p,S as Ht,R as ht,de as gt,df as qt,m as ot,dg as Kt,d0 as Mt,Y as Jt,dh as St,a9 as ut,x as _t,a3 as Ae,b as kt,cY as tt,di as Wt,O as Yt,P as Qt,Q as Zt,M as Xt,k as vt,c$ as Xe,ac as mt,ad as ft,a4 as Ge,a5 as et,ah as zt,cM as es,cN as Gt,dj as wt,dk as ts,dl as ss,ab as Ct,ae as Tt,a1 as yt,aa as At,cQ as Et,cT as ls,D as os,dm as as,f as Lt,j as Pt,cP as ns,h as xt,dn as is,cR as Nt,ai as rs,by as bt,d5 as Dt,cW as Ut,cV as It,cw as Bt,cJ as jt,cK as Ot,B as ds,Z as us,af as cs,d2 as ps,dd as _s,bY as vs,cU as ms}from"./index-s7ypaCFZ.js";import{e as fs,g as Rt,f as gs}from"./slaveServers-Bqw0pV_K.js";import{g as Ft}from"./permission-assignment-CFTF_gcF.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                          *//* empty css                         */import ee from"./websocket-miwP1Pnw.js";/* empty css                   *//* empty css                     *//* empty css                        *//* empty css                 *//* empty css                *//* empty css                       *//* empty css                        */import{u as ys}from"./useOrgUsersAdapter-DXqNqg4u.js";import"./index-BwfO7vYM.js";import"./users-CNnK4pQx.js";import"./useOrganizationTree-CQfzXUGA.js";function hs(){const ue=_(!1),N=_([]),P=_([]),se=_([]),Q=_({slaveServers:{total:0,online:0,offline:0},devices:{total:0,available:0,connected:0,virtual:0},groups:{total:0,withDevices:0},permissions:{totalAssignments:0,activeUsers:0}});let j=null;const M=Ze(),I=oe(()=>M.canAccessDeviceManagementTab),U=oe(()=>M.canAccessSlaveServerTab),x=oe(()=>M.canAccessDeviceGroupTab),F=oe(()=>M.canAccessPermissionAssignmentTab),B=oe(()=>{const k=[];return I.value&&k.push({name:"devices",label:"USB设备管理",icon:"Monitor"}),U.value&&k.push({name:"slaves",label:"分布式节点管理",icon:"Monitor"}),x.value&&k.push({name:"groups",label:"资源调度分组",icon:"Collection"}),F.value&&k.push({name:"permissions",label:"授权范围管理",icon:"Key"}),k}),L=async()=>{try{const k=await Rt();k.success&&(N.value=k.data||[],D())}catch(k){console.error("获取从服务器列表失败:",k)}},K=async()=>{try{const k=await Ft();k.success&&(P.value=k.data||[],b())}catch(k){console.error("获取设备分组列表失败:",k)}},J=async()=>{try{const k=await fs();if(k.success){const R=k.data;Q.value.slaveServers={total:R.total_servers||0,online:R.online_servers||0,offline:R.offline_servers||0},Q.value.devices={total:R.total_devices||0,available:R.available_devices||0,connected:R.connected_devices||0,virtual:R.virtual_devices||0},console.log("统计数据已更新:",{servers:Q.value.slaveServers,devices:Q.value.devices})}}catch(k){console.error("获取统计数据失败:",k),D()}},D=()=>{const k=N.value.length,R=N.value.filter(V=>V.status==="online"||V.is_online).length;Q.value.slaveServers={total:k,online:R,offline:k-R}},b=()=>{const k=P.value.length,R=P.value.filter(V=>V.device_count>0).length;Q.value.groups={total:k,withDevices:R}},r=async()=>{ue.value=!0;try{await Promise.all([L(),K(),J()]),g.success("数据刷新成功")}catch(k){console.error("刷新数据失败:",k),g.error("数据刷新失败")}finally{ue.value=!1}},v=()=>{const k=()=>{const R=Q.value.slaveServers.total;return R>1e3?1e4:R>100?15e3:3e4};J(),j=setInterval(()=>{J()},k())},C=()=>{j&&(clearInterval(j),j=null)};return Qe(()=>{r(),v()}),$t(()=>{C()}),{loading:ue,slaveServers:N,deviceGroups:P,devices:se,statistics:Q,hasDeviceManagePermission:I,hasSlaveServerPermission:U,hasDeviceGroupPermission:x,hasPermissionAssignPermission:F,visibleTabs:B,refreshAllData:r,fetchSlaveServers:L,fetchDeviceGroups:K,fetchStatistics:J,startAutoRefresh:v,stopAutoRefresh:C}}const bs={class:"occupied-dialog-content"},$s={class:"device-info"},ks={class:"device-header"},ws={class:"device-details"},Cs={class:"device-name"},Ts={class:"device-id"},Ss={class:"occupier-info"},Vs={class:"section-title"},xs={class:"user-card"},Ds={class:"user-avatar"},zs={class:"user-details"},As={class:"user-name"},Es={class:"user-contact"},Ls={key:0,class:"user-email"},Ps={class:"contact-actions"},Us={class:"time-info"},Is={class:"section-title"},js={class:"time-details"},Os={class:"time-item"},Ms={class:"time-value"},Bs={class:"time-item"},Gs={class:"time-value"},Ns={class:"time-item"},Rs={class:"time-value"},Fs={key:0,class:"occupation-note"},Hs={class:"section-title"},qs={class:"note-content"},Ks={class:"action-suggestions"},Js={class:"section-title"},Ws={class:"suggestions-list"},Ys={class:"suggestion-item"},Qs={class:"suggestion-item"},Zs={class:"suggestion-item"},Xs={class:"dialog-footer"},el={__name:"DeviceOccupiedDialog",props:{modelValue:{type:Boolean,default:!1},deviceId:{type:[String,Number],default:null}},emits:["update:modelValue","release-requested"],setup(ue,{emit:N}){const P=ue,se=N,Q=Ze(),j=_(!1),M=_(!1),I=_(!1),U=Ee({device_id:"",device_name:"",custom_name:"",device_type:"unknown"}),x=Ee({user_name:"",user_contact:"",user_email:"",start_time:null,duration:0,estimated_end_time:null,note:""});rt(()=>P.modelValue,b=>{I.value=b,b&&P.deviceId&&F()}),rt(I,b=>{se("update:modelValue",b)});const F=async()=>{j.value=!0;try{const b=await fetch(`/api/v1/devices/${P.deviceId}`,{method:"GET",headers:{Authorization:`Bearer ${Q.token}`,"Content-Type":"application/json"}});if(!b.ok)throw new Error(`获取设备信息失败: HTTP ${b.status}`);const r=await b.json();if(Object.assign(U,{device_id:r.device_id,device_name:r.device_name,custom_name:r.custom_name,device_type:r.device_type}),r.status==="occupied"){const v=await fetch(`/api/v1/devices/${P.deviceId}/occupation`,{method:"GET",headers:{Authorization:`Bearer ${Q.token}`,"Content-Type":"application/json"}});if(v.ok){const C=await v.json();Object.assign(x,{user_name:C.user_name,user_contact:C.user_contact,user_email:C.user_email,start_time:C.start_time,duration:C.current_duration,estimated_end_time:C.estimated_end_time,note:C.note})}else Object.assign(x,{user_name:r.current_user_name,user_contact:r.current_user_contact,user_email:"",start_time:r.occupied_start_time,duration:r.occupied_duration,estimated_end_time:r.estimated_end_time,note:r.occupation_note})}else throw new Error("设备未被占用")}catch(b){console.error("获取设备占用信息失败:",b),g.error(`获取设备占用信息失败: ${b.message}`)}finally{j.value=!1}},B=b=>b?new Date(b).toLocaleString():"N/A",L=b=>{if(!b)return"0分钟";const r=Math.floor(b/3600),v=Math.floor(b%3600/60);return r>0?`${r}小时${v}分钟`:`${v}分钟`},K=async b=>{try{b==="phone"?(await Ae.confirm(`确定要拨打电话 ${x.user_contact} 联系 ${x.user_name} 吗？`,"确认拨打电话",{confirmButtonText:"拨打",cancelButtonText:"取消",type:"info"}),g.success("正在为您拨打电话...")):b==="message"&&(await Ae.prompt(`请输入要发送给 ${x.user_name} 的消息：`,"发送消息",{confirmButtonText:"发送",cancelButtonText:"取消",inputPlaceholder:"请输入消息内容...",inputType:"textarea"}),g.success("消息已发送"))}catch{}},J=async()=>{try{await Ae.confirm(`确定要向 ${x.user_name} 发送设备释放请求吗？`,"确认发送释放请求",{confirmButtonText:"发送",cancelButtonText:"取消",type:"warning"}),M.value=!0;const b=await fetch(`/api/v1/devices/${P.deviceId}/release-request`,{method:"POST",headers:{Authorization:`Bearer ${Q.token}`,"Content-Type":"application/json"}});if(!b.ok){const v=await b.json();throw new Error(v.detail||`HTTP ${b.status}`)}const r=await b.json();g.success(r.message||"释放请求已发送"),se("release-requested",P.deviceId)}catch(b){b.message!=="cancel"&&(console.error("发送释放请求失败:",b),g.error(`发送释放请求失败: ${b.message}`))}finally{M.value=!1}},D=()=>{I.value=!1};return(b,r)=>{const v=lt,C=dt,k=ot,R=_t,V=ut;return h(),O(R,{modelValue:I.value,"onUpdate:modelValue":r[3]||(r[3]=S=>I.value=S),title:"设备占用信息",width:"500px","before-close":D},{footer:s(()=>[o("div",Xs,[e(k,{onClick:D},{default:s(()=>r[17]||(r[17]=[p("关闭",-1)])),_:1,__:[17]}),e(k,{type:"warning",onClick:J,loading:M.value},{default:s(()=>[e(v,null,{default:s(()=>[e($(St))]),_:1}),r[18]||(r[18]=p(" 请求释放 ",-1))]),_:1,__:[18]},8,["loading"]),e(k,{type:"primary",onClick:r[2]||(r[2]=S=>K("phone")),disabled:!x.user_contact},{default:s(()=>[e(v,null,{default:s(()=>[e($(gt))]),_:1}),r[19]||(r[19]=p(" 联系用户 ",-1))]),_:1,__:[19]},8,["disabled"])])]),default:s(()=>[Je((h(),G("div",bs,[o("div",$s,[o("div",ks,[e(v,{class:"device-icon"},{default:s(()=>[e($(pt))]),_:1}),o("div",ws,[o("div",Cs,y(U.custom_name||U.device_name),1),o("div",Ts,"设备ID: "+y(U.device_id),1)]),e(C,{type:"warning",size:"large"},{default:s(()=>[e(v,null,{default:s(()=>[e($(Ht))]),_:1}),r[4]||(r[4]=p(" 被占用 ",-1))]),_:1,__:[4]})])]),o("div",Ss,[o("div",Vs,[e(v,null,{default:s(()=>[e($(ht))]),_:1}),r[5]||(r[5]=o("span",null,"占用用户信息",-1))]),o("div",xs,[o("div",Ds,[e(v,null,{default:s(()=>[e($(ht))]),_:1})]),o("div",zs,[o("div",As,y(x.user_name||"N/A"),1),o("div",Es,[e(v,null,{default:s(()=>[e($(gt))]),_:1}),o("span",null,y(x.user_contact||"N/A"),1)]),x.user_email?(h(),G("div",Ls,[e(v,null,{default:s(()=>[e($(qt))]),_:1}),o("span",null,y(x.user_email),1)])):W("",!0)]),o("div",Ps,[e(k,{type:"primary",size:"small",onClick:r[0]||(r[0]=S=>K("phone")),disabled:!x.user_contact},{default:s(()=>[e(v,null,{default:s(()=>[e($(gt))]),_:1}),r[6]||(r[6]=p(" 拨打电话 ",-1))]),_:1,__:[6]},8,["disabled"]),e(k,{type:"success",size:"small",onClick:r[1]||(r[1]=S=>K("message")),disabled:!x.user_contact},{default:s(()=>[e(v,null,{default:s(()=>[e($(Kt))]),_:1}),r[7]||(r[7]=p(" 发送消息 ",-1))]),_:1,__:[7]},8,["disabled"])])])]),o("div",Us,[o("div",Is,[e(v,null,{default:s(()=>[e($(Mt))]),_:1}),r[8]||(r[8]=o("span",null,"占用时间信息",-1))]),o("div",js,[o("div",Os,[r[9]||(r[9]=o("span",{class:"time-label"},"开始时间：",-1)),o("span",Ms,y(B(x.start_time)),1)]),o("div",Bs,[r[10]||(r[10]=o("span",{class:"time-label"},"持续时间：",-1)),o("span",Gs,y(L(x.duration)),1)]),o("div",Ns,[r[11]||(r[11]=o("span",{class:"time-label"},"预计结束：",-1)),o("span",Rs,y(B(x.estimated_end_time)||"未设置"),1)])])]),x.note?(h(),G("div",Fs,[o("div",Hs,[e(v,null,{default:s(()=>[e($(Jt))]),_:1}),r[12]||(r[12]=o("span",null,"占用说明",-1))]),o("div",qs,y(x.note),1)])):W("",!0),o("div",Ks,[o("div",Js,[e(v,null,{default:s(()=>[e($(St))]),_:1}),r[13]||(r[13]=o("span",null,"操作建议",-1))]),o("div",Ws,[o("div",Ys,[e(v,{class:"suggestion-icon"},{default:s(()=>[e($(gt))]),_:1}),r[14]||(r[14]=o("span",null,"联系占用用户协商使用时间",-1))]),o("div",Qs,[e(v,{class:"suggestion-icon"},{default:s(()=>[e($(Mt))]),_:1}),r[15]||(r[15]=o("span",null,"等待设备自动释放（如有预计结束时间）",-1))]),o("div",Zs,[e(v,{class:"suggestion-icon"},{default:s(()=>[e($(St))]),_:1}),r[16]||(r[16]=o("span",null,"发送释放请求通知",-1))])])])])),[[V,j.value]])]),_:1},8,["modelValue"])}}},tl=st(el,[["__scopeId","data-v-eabf33b8"]]),sl={class:"device-management"},ll={class:"management-header"},ol={class:"header-right"},al={class:"search-filters"},nl={class:"filter-row"},il={class:"filter-group"},rl={class:"filter-group"},dl={class:"filter-group"},ul={class:"filter-group"},cl={class:"filter-group"},pl={class:"device-type-filter-panel"},_l={class:"panel-header"},vl={class:"panel-title"},ml={class:"panel-actions"},fl={class:"device-type-checkboxes"},gl={class:"checkbox-grid"},yl={class:"checkbox-content"},hl={class:"type-icon"},bl={class:"type-label"},$l={class:"type-count"},kl={class:"device-list"},wl={class:"card-header"},Cl={class:"header-actions"},Tl={class:"selected-count"},Sl={class:"device-name"},Vl={class:"name-primary"},xl={key:0,class:"name-secondary"},Dl={class:"server-info"},zl={class:"server-name"},Al={class:"server-ip"},El={class:"last-connected"},Ll={key:0},Pl={class:"time"},Ul={class:"device-remark"},Il={class:"pagination-wrapper"},jl={__name:"DeviceManagement",setup(ue){const N=kt(),P=Ze(),se=_(!1),Q=_(!1),j=_(""),M=_(""),I=_(""),U=_(""),x=_("created_at"),F=_(!1),B=_([]),L=_(1),K=_(20),J=_(!1),D=_(null),b=_(!1),r=_(null),v=_([]),C=_([]),k=Ee({total_devices:0,online_devices:0,hardware_devices:0,occupied_devices:0}),R=Ee({total_devices:0,available_devices:0,online_servers:0,total_servers:0,data_freshness:"unknown",last_update:null}),V=_("hybrid");_(!0);const S=_({}),pe=_([]);let ge=null;const Z=oe(()=>{const t=Object.keys(S.value).filter(l=>S.value[l]);return t.length===0?[]:v.value.filter(l=>{const u=Se(l);return t.includes(u)})}),te=oe(()=>{let t=Z.value;if(j.value){const l=j.value.toLowerCase();t=t.filter(u=>u.device_name&&u.device_name.toLowerCase().includes(l)||u.custom_name&&u.custom_name.toLowerCase().includes(l)||u.device_id&&u.device_id.toLowerCase().includes(l)||u.description&&u.description.toLowerCase().includes(l))}return M.value&&(t=t.filter(l=>l.server_id===M.value)),I.value&&(t=t.filter(l=>l.device_type===I.value)),U.value&&(t=t.filter(l=>l.status===U.value)),t.sort((l,u)=>{switch(x.value){case"device_name":return(l.device_name||"").localeCompare(u.device_name||"");case"server_group":return(l.server_name||"").localeCompare(u.server_name||"");case"last_connected":return new Date(u.last_connected||0)-new Date(l.last_connected||0);case"created_at":default:return new Date(u.created_at||0)-new Date(l.created_at||0)}}),t}),Le=oe(()=>te.value.length),X=async()=>{se.value=!0;try{await Promise.all([ye(),_e()])}catch(t){console.error("数据刷新失败:",t),g.error("数据刷新失败")}finally{se.value=!1}},ye=async()=>{try{const t={page:L.value,page_size:K.value,search:j.value||void 0,server_id:M.value||void 0,device_type:I.value||void 0,status:U.value||void 0,sort_by:x.value,sort_order:"desc"};Object.keys(t).forEach(A=>{t[A]===void 0&&delete t[A]});const l=await fetch("/api/v1/devices?"+new URLSearchParams(t),{method:"GET",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!l.ok)throw new Error(`HTTP ${l.status}: ${l.statusText}`);const u=await l.json();v.value=u.devices||[],i(),r.value=new Date().toLocaleString()}catch(t){console.error("获取设备列表失败:",t),g.error(`获取设备列表失败: ${t.message}`)}},_e=async()=>{try{const t=await fetch(`/api/v1/devices/stats/summary?mode=${V.value}`,{method:"GET",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const l=await t.json();Object.assign(k,{total_devices:l.total_devices||0,online_devices:l.online_devices||0,hardware_devices:l.hardware_devices||0,occupied_devices:l.occupied_devices||0}),V.value!=="database"&&Object.assign(R,{total_devices:l.total_devices||0,available_devices:l.available_devices||0,online_servers:l.online_servers||0,total_servers:l.total_servers||0,data_freshness:l.data_freshness||"unknown",last_update:l.last_update}),r.value=new Date().toLocaleString(),console.log("统计数据获取成功:",l)}catch(t){console.error("获取统计数据失败:",t)}},he={ca_lock:{label:"CA锁",color:"danger",icon:"🔐",description:"CA证书锁、银行U盾、数字证书设备",defaultVisible:!0},encryption_key:{label:"加密锁",color:"warning",icon:"🔒",description:"软件加密锁、硬件加密锁设备",defaultVisible:!0},bank_ukey:{label:"银行U盾",color:"primary",icon:"🏦",description:"银行数字证书U盾设备",defaultVisible:!0},financial_lock:{label:"财务锁",color:"warning",icon:"💰",description:"用友、金蝶等财务软件加密锁",defaultVisible:!0},cost_lock:{label:"造价加密锁",color:"primary",icon:"🏗️",description:"广联达、新点等造价软件加密锁",defaultVisible:!0},other_lock:{label:"其他加密锁",color:"danger",icon:"🔒",description:"其他类型的加密锁设备",defaultVisible:!1},storage:{label:"存储设备",color:"info",icon:"💾",description:"U盘、移动硬盘等存储设备",defaultVisible:!1},peripheral_device:{label:"外设",color:"success",icon:"🖨️",description:"打印机、扫描仪等外围设备",defaultVisible:!1},video_device:{label:"视频设备",color:"primary",icon:"📹",description:"摄像头、采集卡等视频设备",defaultVisible:!1},audio_device:{label:"音频设备",color:"warning",icon:"🎵",description:"麦克风、耳机、声卡等音频设备",defaultVisible:!1},bluetooth_device:{label:"蓝牙设备",color:"info",icon:"📶",description:"蓝牙适配器、蓝牙设备",defaultVisible:!1},hub:{label:"Hub设备",color:"",icon:"🔌",description:"USB Hub、集线器等",defaultVisible:!1},input:{label:"输入设备",color:"",icon:"⌨️",description:"键盘、鼠标等输入设备",defaultVisible:!1},communication:{label:"通信设备",color:"",icon:"📡",description:"网络适配器等通信设备",defaultVisible:!1},hardware:{label:"硬件设备",color:"",icon:"🔧",description:"其他硬件设备",defaultVisible:!1},unknown:{label:"未知设备",color:"",icon:"❓",description:"未识别的设备类型",defaultVisible:!1}},Pe=t=>{var l;return((l=he[t])==null?void 0:l.color)||""},Te=t=>{var l;return((l=he[t])==null?void 0:l.label)||"待补充"},Se=t=>t.final_device_type?ae(t.final_device_type,t):ae(t.device_type||"unknown",t),ae=(t,l=null)=>{if(l){if(l.identification_source==="usb_ids_enhanced")return"encryption_key";if(l.usb_ids_vendor_name&&l.usb_ids_vendor_name.trim()){const A=l.usb_ids_vendor_name.toLowerCase();if(A.includes("senseshield")||A.includes("rockey")||A.includes("hasp")||A.includes("sentinel")||A.includes("safenet")||A.includes("aladdin"))return"encryption_key";if(l.usb_ids_device_name){const de=l.usb_ids_device_name.toLowerCase();if(de.includes("dongle")||de.includes("key")||de.includes("lock")||de.includes("token"))return"encryption_key"}}}return{ca_lock:"ca_lock",encryption_key:"encryption_key",bank_ukey:"bank_ukey",financial_lock:"financial_lock",cost_lock:"cost_lock",other_lock:"other_lock",video_device:"video_device",audio_device:"audio_device",bluetooth_device:"bluetooth_device",peripheral_device:"peripheral_device",storage:"storage",input:"input",communication:"communication",hub:"hub",hardware:"hardware",unknown:"unknown",CA锁:"ca_lock",加密锁:"encryption_key",银行U盾:"bank_ukey",encryption_lock:"encryption_key",encryption:"encryption_key",printer_scanner:"peripheral_device",printer:"peripheral_device",unknown_error:"unknown",virtual:"unknown",system:"unknown"}[t]||"unknown"},Me=()=>{const t=localStorage.getItem("omnilink-device-type-filter");if(t)try{S.value=JSON.parse(t)}catch(l){console.warn("Failed to parse saved device type filter:",l),xe()}else xe();pe.value=Object.keys(S.value).filter(l=>S.value[l])},Ve=()=>{localStorage.setItem("omnilink-device-type-filter",JSON.stringify(S.value))},Ne=t=>{S.value={},Object.keys(he).forEach(l=>{S.value[l]=t.includes(l)}),Ve()},Re=()=>{Object.keys(he).forEach(t=>{S.value[t]=!0}),pe.value=Object.keys(he),Ve()},xe=()=>{S.value={},Object.keys(he).forEach(t=>{S.value[t]=he[t].defaultVisible}),pe.value=Object.keys(S.value).filter(t=>S.value[t]),Ve()},Fe=t=>{if(!v.value||v.value.length===0)return 0;let l=v.value;if(j.value){const u=j.value.toLowerCase();l=l.filter(A=>{var de,we,We,Ke;return((de=A.device_name)==null?void 0:de.toLowerCase().includes(u))||((we=A.custom_name)==null?void 0:we.toLowerCase().includes(u))||((We=A.device_id)==null?void 0:We.toLowerCase().includes(u))||((Ke=A.serial_number)==null?void 0:Ke.toLowerCase().includes(u))})}return M.value&&(l=l.filter(u=>u.slave_server_id===M.value)),U.value&&(l=l.filter(u=>u.status===U.value)),l.filter(u=>Se(u)===t).length},He=t=>({idle:"success",occupied:"warning",damaged:"danger",offline:"info"})[t]||"info",w=t=>({idle:"空闲",occupied:"被占用",damaged:"硬件损坏",offline:"离线"})[t]||"未知",f=t=>t?new Date(t).toLocaleString():"N/A",H=t=>{t?B.value=[...te.value]:B.value=[]},z=t=>{B.value=t,F.value=t.length===te.value.length},ie=async t=>{switch(t){case"batch-edit":case"batch-group":case"batch-delete":if(B.value.length===0){g.warning("请先选择要操作的设备");return}g.info(`批量操作: ${t}, 选中 ${B.value.length} 个设备`);break;default:g.warning("未知的批量操作")}},le=async()=>{try{await Ae.confirm("此操作将使用智能分类器重新分析所有设备的类型，支持精准识别CA锁、财务锁、造价加密锁等。是否继续？","智能重新分类设备类型",{confirmButtonText:"开始分类",cancelButtonText:"取消",type:"warning"}),Q.value=!0;try{const t=await fetch("/api/v1/devices/batch-reclassify-types",{method:"POST",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const l=await t.json();g.success(`智能分类完成！总计: ${l.statistics.total}, 成功: ${l.statistics.updated}`),await X()}finally{Q.value=!1}}catch(t){Q.value=!1,t!=="cancel"&&(console.error("智能重新分类失败:",t),g.error("智能重新分类失败: "+t.message))}},re=t=>{K.value=t,L.value=1},m=t=>{L.value=t},c=t=>{N.push(`/device-center/device/${t.id}`)},ne=t=>{N.push(`/device-center/device/${t.id}`)},Y=async t=>{try{await Ae.confirm(`确定要删除设备 "${t.device_name}" 吗？此操作将解除USB设备自动绑定。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=await fetch(`/api/v1/devices/${t.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!l.ok){const u=await l.json();throw new Error(u.detail||`HTTP ${l.status}`)}g.success(`设备 "${t.device_name}" 已删除`),X()}catch(l){l.message!=="cancel"&&(console.error("删除设备失败:",l),g.error(`删除设备失败: ${l.message}`))}},ve=t=>{t.status==="occupied"&&(D.value=t.id,J.value=!0)},ke=async t=>{try{const l=await fetch(`/api/v1/devices/${t}/release-request`,{method:"POST",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!l.ok){const A=await l.json();throw new Error(A.detail||`HTTP ${l.status}`)}const u=await l.json();g.success(u.message||"设备释放请求已发送"),X()}catch(l){console.error("发送释放请求失败:",l),g.error(`发送释放请求失败: ${l.message}`)}},Be=()=>{ee.setConnectionChangeCallback(l=>{b.value=l,console.log(l?"WebSocket连接已建立":"WebSocket连接已断开")}),ee.subscribe("device_updates"),ee.addEventListener("device_status_update",me),ee.addEventListener("device_connected",ce),ee.addEventListener("device_disconnected",fe),ee.addEventListener("device_occupied",T),ee.addEventListener("device_released",Ue),ee.addEventListener("device_added",Ie),ee.addEventListener("device_updated",De),ee.addEventListener("device_deleted",qe),ee.addEventListener("device_release_requested",d);const t=P.token;t&&ee.connect(t)},me=t=>{console.log("设备状态更新:",t);const l=v.value.findIndex(u=>u.id===t.device_id);l!==-1&&(v.value[l].status=t.new_status,r.value=new Date().toLocaleString(),i(),g.info(`设备状态更新: ${v.value[l].device_name} -> ${w(t.new_status)}`))},ce=t=>{console.log("设备连接:",t);const l=v.value.findIndex(u=>u.id===t.device_id);l!==-1&&(v.value[l].status="occupied",v.value[l].last_connected=t.timestamp,v.value[l].last_connected_user=t.user_name||"Unknown",i(),g.success(`设备已连接: ${v.value[l].device_name}`))},fe=t=>{console.log("设备断开:",t);const l=v.value.findIndex(u=>u.id===t.device_id);l!==-1&&(v.value[l].status="idle",i(),g.info(`设备已断开: ${v.value[l].device_name}`))},T=t=>{console.log("设备被占用:",t);const l=v.value.findIndex(u=>u.id===t.device_id);l!==-1&&(v.value[l].status="occupied",v.value[l].last_connected_user=t.user_name,v.value[l].last_connected=t.timestamp,i(),g.warning(`设备被占用: ${v.value[l].device_name} (${t.user_name})`))},Ue=t=>{console.log("设备已释放:",t);const l=v.value.findIndex(u=>u.id===t.device_id);l!==-1&&(v.value[l].status="idle",i(),g.success(`设备已释放: ${v.value[l].device_name}`))},Ie=t=>{console.log("设备已添加:",t),X(),g.success(`新设备已添加: ${t.device_name}`)},De=t=>{console.log("设备已更新:",t),X(),g.info("设备信息已更新")},qe=t=>{console.log("设备已删除:",t);const l=v.value.findIndex(u=>u.id===t.device_id);l!==-1&&(v.value.splice(l,1),i(),g.info(`设备已删除: ${t.device_name}`))},d=t=>{var l;console.log("收到设备释放请求:",t),t.requester_id!==((l=P.user)==null?void 0:l.id)&&g({type:"warning",message:`${t.requester_name} 请求您释放设备: ${t.device_name}`,duration:1e4,showClose:!0})},i=()=>{k.total_devices=v.value.length,k.online_devices=v.value.filter(t=>t.status!=="offline").length,k.hardware_devices=v.value.filter(t=>t.device_type!=="unknown").length,k.occupied_devices=v.value.filter(t=>t.status==="occupied").length},a=()=>{ee.removeEventListener("device_status_update",me),ee.removeEventListener("device_connected",ce),ee.removeEventListener("device_disconnected",fe),ee.removeEventListener("device_occupied",T),ee.removeEventListener("device_released",Ue),ee.removeEventListener("device_added",Ie),ee.removeEventListener("device_updated",De),ee.removeEventListener("device_deleted",qe),ee.removeEventListener("device_release_requested",d),ee.unsubscribe("device_updates")};return Qe(()=>{Me(),X(),Be(),ge=setInterval(X,6e4)}),$t(()=>{ge&&clearInterval(ge),a()}),(t,l)=>{const u=lt,A=ot,de=Zt,we=Qt,We=Xt,Ke=vt,q=ft,$e=mt,Ye=zt,je=dt,Oe=wt,ze=Gt,at=es,be=Tt,nt=Ct,it=At,E=ut;return h(),G(Ge,null,[o("div",sl,[o("div",ll,[l[16]||(l[16]=o("div",{class:"header-left"},[o("div",{class:"page-title"},[o("h2",null,"USB设备管理")])],-1)),o("div",ol,[e(A,{onClick:X,loading:se.value,type:"primary"},{default:s(()=>[e(u,null,{default:s(()=>[e($(tt))]),_:1}),l[10]||(l[10]=p(" 刷新数据 ",-1))]),_:1,__:[10]},8,["loading"]),e(A,{onClick:le,type:"warning",loading:Q.value},{default:s(()=>[e(u,null,{default:s(()=>[e($(Wt))]),_:1}),l[11]||(l[11]=p(" 智能重新分类 ",-1))]),_:1,__:[11]},8,["loading"]),e(We,{onCommand:ie},{dropdown:s(()=>[e(we,null,{default:s(()=>[e(de,{command:"batch-edit"},{default:s(()=>l[13]||(l[13]=[p("批量编辑",-1)])),_:1,__:[13]}),e(de,{command:"batch-group"},{default:s(()=>l[14]||(l[14]=[p("批量分组",-1)])),_:1,__:[14]}),e(de,{divided:"",command:"batch-delete"},{default:s(()=>l[15]||(l[15]=[p("批量删除",-1)])),_:1,__:[15]})]),_:1})]),default:s(()=>[e(A,{type:"success"},{default:s(()=>[l[12]||(l[12]=p(" 批量操作 ",-1)),e(u,{class:"el-icon--right"},{default:s(()=>[e($(Yt))]),_:1})]),_:1,__:[12]})]),_:1})])]),o("div",al,[e(Ye,null,{default:s(()=>[o("div",nl,[o("div",il,[e(Ke,{modelValue:j.value,"onUpdate:modelValue":l[0]||(l[0]=n=>j.value=n),placeholder:"搜索设备名称、ID、描述...",style:{width:"300px"},clearable:""},{prefix:s(()=>[e(u,null,{default:s(()=>[e($(Xe))]),_:1})]),_:1},8,["modelValue"])]),o("div",rl,[e($e,{modelValue:M.value,"onUpdate:modelValue":l[1]||(l[1]=n=>M.value=n),placeholder:"筛选服务器",style:{width:"200px"},clearable:""},{default:s(()=>[e(q,{label:"全部服务器",value:""}),(h(!0),G(Ge,null,et(C.value,n=>(h(),O(q,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),o("div",dl,[e($e,{modelValue:I.value,"onUpdate:modelValue":l[2]||(l[2]=n=>I.value=n),placeholder:"筛选类型",style:{width:"150px"},clearable:""},{default:s(()=>[e(q,{label:"全部类型",value:""}),e(q,{label:"加密锁",value:"encryption_key"}),e(q,{label:"存储设备",value:"storage"}),e(q,{label:"输入设备",value:"input"}),e(q,{label:"通信设备",value:"communication"}),e(q,{label:"硬件设备",value:"hardware"}),e(q,{label:"未知设备",value:"unknown"})]),_:1},8,["modelValue"])]),o("div",ul,[e($e,{modelValue:U.value,"onUpdate:modelValue":l[3]||(l[3]=n=>U.value=n),placeholder:"筛选状态",style:{width:"120px"},clearable:""},{default:s(()=>[e(q,{label:"全部状态",value:""}),e(q,{label:"空闲",value:"idle"}),e(q,{label:"被占用",value:"occupied"}),e(q,{label:"硬件损坏",value:"damaged"}),e(q,{label:"离线",value:"offline"})]),_:1},8,["modelValue"])]),o("div",cl,[e($e,{modelValue:x.value,"onUpdate:modelValue":l[4]||(l[4]=n=>x.value=n),placeholder:"排序方式",style:{width:"150px"}},{default:s(()=>[e(q,{label:"注册时间",value:"created_at"}),e(q,{label:"设备名称",value:"device_name"}),e(q,{label:"服务器分组",value:"server_group"}),e(q,{label:"最后连接",value:"last_connected"})]),_:1},8,["modelValue"])])])]),_:1})]),o("div",pl,[e(Ye,null,{header:s(()=>[o("div",_l,[o("div",vl,[e(u,null,{default:s(()=>[e($(ss))]),_:1}),l[17]||(l[17]=o("span",null,"设备类型过滤",-1)),e(je,{size:"small",type:"info"},{default:s(()=>[p(" 显示"+y(Object.keys(S.value).filter(n=>S.value[n]).length)+"种类型，共"+y(Z.value.length)+"个设备 ",1)]),_:1})]),o("div",ml,[e(A,{size:"small",onClick:Re},{default:s(()=>l[18]||(l[18]=[p("全选",-1)])),_:1,__:[18]}),e(A,{size:"small",onClick:xe},{default:s(()=>l[19]||(l[19]=[p("重置为默认",-1)])),_:1,__:[19]})])])]),default:s(()=>[o("div",fl,[e(at,{modelValue:pe.value,"onUpdate:modelValue":l[5]||(l[5]=n=>pe.value=n),onChange:Ne},{default:s(()=>[o("div",gl,[(h(),G(Ge,null,et(he,(n,Ce)=>e(ze,{key:Ce,label:Ce,class:"device-type-checkbox"},{default:s(()=>[o("div",yl,[o("span",hl,y(n.icon),1),o("span",bl,[p(y(n.label.replace(/^[🔐💾⌨️📡🖨️🔌🔧❓]\s/,""))+" ",1),o("span",$l,"（"+y(Fe(Ce))+"）",1)]),e(Oe,{content:n.description,placement:"top"},{default:s(()=>[e(u,{class:"info-icon"},{default:s(()=>[e($(ts))]),_:1})]),_:2},1032,["content"])])]),_:2},1032,["label"])),64))])]),_:1},8,["modelValue"])])]),_:1})]),o("div",kl,[e(Ye,null,{header:s(()=>[o("div",wl,[l[21]||(l[21]=o("span",null,"设备列表",-1)),o("div",Cl,[e(ze,{modelValue:F.value,"onUpdate:modelValue":l[6]||(l[6]=n=>F.value=n),onChange:H},{default:s(()=>l[20]||(l[20]=[p("全选",-1)])),_:1,__:[20]},8,["modelValue"]),o("span",Tl,"已选择 "+y(B.value.length)+" 个设备",1)])])]),default:s(()=>[Je((h(),O(nt,{data:te.value,onSelectionChange:z,stripe:"",style:{width:"100%"}},{default:s(()=>[e(be,{type:"selection",width:"55"}),e(be,{prop:"device_id",label:"设备ID",width:"120"}),e(be,{label:"设备名称","min-width":"200"},{default:s(({row:n})=>[o("div",Sl,[o("div",Vl,y(n.custom_name||n.device_name),1),n.custom_name?(h(),G("div",xl,y(n.device_name),1)):W("",!0)])]),_:1}),e(be,{label:"设备类型",width:"120"},{default:s(({row:n})=>[e(je,{type:Pe(Se(n)),size:"small"},{default:s(()=>[p(y(Te(Se(n))),1)]),_:2},1032,["type"])]),_:1}),e(be,{label:"状态",width:"100"},{default:s(({row:n})=>[e(je,{type:He(n.status),size:"small",class:yt({"clickable-status":n.status==="occupied"}),onClick:Ce=>n.status==="occupied"?ve(n):null},{default:s(()=>[p(y(w(n.status)),1)]),_:2},1032,["type","class","onClick"])]),_:1}),e(be,{label:"所属服务器",width:"180"},{default:s(({row:n})=>[o("div",Dl,[o("div",zl,y(n.server_name),1),o("div",Al,y(n.server_ip),1)])]),_:1}),e(be,{label:"位置",width:"120"},{default:s(({row:n})=>[o("code",null,y(n.physical_port||"N/A"),1)]),_:1}),e(be,{label:"最后连接",width:"150"},{default:s(({row:n})=>[o("div",El,[n.last_connected_user?(h(),G("div",Ll,y(n.last_connected_user),1)):W("",!0),o("div",Pl,y(f(n.last_connected)),1)])]),_:1}),e(be,{label:"备注","min-width":"150"},{default:s(({row:n})=>[o("div",Ul,y(n.remark||"无备注"),1)]),_:1}),e(be,{label:"操作",width:"200",fixed:"right"},{default:s(({row:n})=>[e(A,{size:"small",onClick:Ce=>c(n)},{default:s(()=>l[22]||(l[22]=[p(" 详情 ",-1)])),_:2,__:[22]},1032,["onClick"]),e(A,{size:"small",onClick:Ce=>ne(n)},{default:s(()=>l[23]||(l[23]=[p(" 修改 ",-1)])),_:2,__:[23]},1032,["onClick"]),e(A,{size:"small",type:"danger",onClick:Ce=>Y(n)},{default:s(()=>l[24]||(l[24]=[p(" 删除 ",-1)])),_:2,__:[24]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[E,se.value]]),o("div",Il,[e(it,{"current-page":L.value,"onUpdate:currentPage":l[7]||(l[7]=n=>L.value=n),"page-size":K.value,"onUpdate:pageSize":l[8]||(l[8]=n=>K.value=n),"page-sizes":[20,50,100,200],total:Le.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:re,onCurrentChange:m},null,8,["current-page","page-size","total"])])]),_:1})])]),e(tl,{modelValue:J.value,"onUpdate:modelValue":l[9]||(l[9]=n=>J.value=n),"device-id":D.value,onReleaseRequested:ke},null,8,["modelValue","device-id"])],64)}}},Ol=st(jl,[["__scopeId","data-v-5232573a"]]),Ml={class:"slave-server-management"},Bl={class:"toolbar"},Gl={class:"toolbar-left"},Nl={class:"toolbar-right"},Rl={class:"server-name"},Fl={class:"server-name-text"},Hl={class:"server-name-display"},ql={class:"status-tags"},Kl={class:"ip-address"},Jl={class:"device-count"},Wl={key:0,class:"heartbeat-time"},Yl={key:1,class:"text-muted"},Ql={key:0,class:"config-check-time"},Zl={key:1,class:"text-muted"},Xl={class:"pagination-container"},eo={__name:"SlaveServerManagement",emits:["stats-update"],setup(ue,{expose:N,emit:P}){const se=P,Q=kt(),j=Ze(),M=_(!1),I=_(!1),U=_(!1),x=_(!1),F=_(!1),B=_([]),L=_([]),K=_(""),J=_(""),D=_(1),b=_(20),r=_(0),v=_(),C=Ee({server_name:"",server_ip:"",server_port:8889,vh_port:7575,location:"",description:""}),k={server_name:[{required:!0,message:"请输入服务器名称",trigger:"blur"}],server_ip:[{required:!0,message:"请输入IP地址",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"请输入正确的IP地址格式",trigger:"blur"}],server_port:[{required:!0,message:"请输入服务端口",trigger:"blur"}],vh_port:[{required:!0,message:"请输入VH端口",trigger:"blur"}]},R=oe(()=>!0),V=oe(()=>!0),S=oe(()=>!0),pe=oe(()=>!0),ge=oe(()=>{let w=B.value;if(K.value&&(w=w.filter(H=>H.status===K.value)),J.value){const H=J.value.toLowerCase();w=w.filter(z=>z.name&&z.name.toLowerCase().includes(H)||z.ip_address&&z.ip_address.toLowerCase().includes(H))}const f=w.sort((H,z)=>H.status==="online"&&z.status!=="online"?-1:H.status!=="online"&&z.status==="online"?1:H.id-z.id);return r.value=f.length,f}),Z=oe(()=>{const w=(D.value-1)*b.value,f=w+b.value;return ge.value.slice(w,f)}),te=w=>w==="online"?"success":"danger",Le=w=>w?new Date(w).toLocaleString():"",X=async()=>{M.value=!0;try{const w=await Rt();B.value=w.data||[],r.value=B.value.length,console.log("从服务器列表数据:",w.data),w.data&&w.data.length>0&&(console.log("第一个服务器的数据:",w.data[0]),console.log("第一个服务器的last_seen字段:",w.data[0].last_seen)),ye()}catch(w){console.error("加载从服务器数据失败:",w),g.error("加载从服务器数据失败")}finally{M.value=!1}},ye=()=>{const w={total:B.value.length,online:B.value.filter(f=>f.status==="online").length,offline:B.value.filter(f=>f.status==="offline").length};se("stats-update",w)},_e=()=>{},he=w=>{b.value=w,X()},Pe=w=>{D.value=w,X()},Te=w=>{Q.push(`/device-center/slave-server/${w.id}`)},Se=async(w,f)=>{try{await Ae.confirm(`确定要${f==="restart"?"重启":"控制"}服务器 "${w.name}" 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),g.success(`服务器 "${w.name}" ${f==="restart"?"重启":"控制"}成功`)}catch{}},ae=async()=>{try{await v.value.validate(),I.value=!0,await gs(C),g.success("从服务器添加成功"),F.value=!1,Me(),X(),X()}catch(w){console.error("添加从服务器失败:",w),g.error("添加从服务器失败")}finally{I.value=!1}},Me=()=>{var w;Object.assign(C,{server_name:"",server_ip:"",server_port:8889,vh_port:7575,location:"",description:""}),(w=v.value)==null||w.clearValidate()},Ve=w=>{L.value=w},Ne=async()=>{if(L.value.length===0){g.warning("请先选择要同步的从服务器");return}try{await Ae.confirm(`确定要强制同步选中的 ${L.value.length} 个从服务器的设备数据吗？

此操作将：
1. 清空选中从服务器的现有设备数据
2. 重新从从服务器获取最新设备信息
3. 应用USB.IDS增强识别`,"确认强制同步",{confirmButtonText:"确定同步",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}),U.value=!0;const w=L.value.map(z=>z.id),f=await fetch("/api/v1/slave/force-sync",{method:"POST",headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"},body:JSON.stringify({slave_ids:w})});if(!f.ok)throw new Error(`HTTP ${f.status}: ${f.statusText}`);const H=await f.json();if(H.success){g.success(`强制同步完成！${H.message}`);const z=H.results.filter(le=>le.success).length,ie=H.results.filter(le=>!le.success).length;if(ie>0){const le=H.results.filter(re=>!re.success).map(re=>`服务器${re.slave_id}: ${re.message}`).join(`
`);g.warning(`同步完成，但有 ${ie} 个服务器同步失败：
${le}`)}await X(),L.value=[]}else throw new Error(H.message||"强制同步失败")}catch(w){console.error("强制同步失败:",w),g.error(`强制同步失败: ${w.message}`)}finally{U.value=!1}},Re=async()=>{if(L.value.length===0){g.warning("请先选择要删除的从服务器");return}try{const w=L.value.map(ie=>ie.name).join("、"),f=`
      <div style="text-align: left;">
        <p><strong>确定要删除以下 ${L.value.length} 个从服务器吗？</strong></p>
        <p style="color: #666; font-size: 14px; margin: 10px 0;">${w}</p>

        <p><strong>此操作将执行深度清理：</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px; color: #666;">
          <li>🗑️ 删除从服务器记录</li>
          <li>🔌 删除所有关联的USB设备记录</li>
          <li>👥 从设备分组中移除这些设备</li>
          <li>👤 清除设备的直接用户分配关系</li>
          <li>🔄 更新设备占用记录状态</li>
          <li>🧹 清理空的设备分组</li>
          <li>📋 保留审计日志（合规要求）</li>
        </ul>

        <p style="color: #e74c3c; font-weight: bold;">⚠️ 此操作不可撤销！</p>
      </div>
    `;await Ae.confirm(f,"确认深度删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error",dangerouslyUseHTMLString:!0,customClass:"bulk-delete-confirm"}),x.value=!0;const H=L.value.map(ie=>ie.id),z=await Fe(H);if(z.batch_results.successful_deletions>0){const ie=`✅ 成功删除 ${z.batch_results.successful_deletions} 个从服务器`,le=`📊 清理统计：设备 ${z.batch_results.overall_stats.total_devices_deleted} 个，分组关联 ${z.batch_results.overall_stats.total_group_assignments_removed} 个，空分组清理 ${z.batch_results.overall_stats.total_empty_groups_cleaned} 个`;g.success(`${ie}
${le}`)}if(z.batch_results.failed_deletions>0){const ie=z.batch_results.deletion_details.filter(le=>le.status==="failed").map(le=>`服务器 ${le.server_id}: ${le.error}`).join(`
`);g.error(`❌ ${z.batch_results.failed_deletions} 个服务器删除失败：
${ie}`)}await X(),L.value=[]}catch(w){w!=="cancel"&&(console.error("批量删除失败:",w),g.error(`批量删除失败: ${w.message}`))}finally{x.value=!1}},xe=async w=>{try{const f=await fetch(`/api/v1/slave/${w}`,{method:"DELETE",headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"}});if(!f.ok){const z=await f.json().catch(()=>({}));throw new Error(z.detail||`HTTP ${f.status}: ${f.statusText}`)}return await f.json()}catch(f){throw console.error(`删除从服务器 ${w} 失败:`,f),f}},Fe=async w=>{try{const f=await fetch("/api/v1/slave/batch-delete",{method:"POST",headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"},body:JSON.stringify(w)});if(!f.ok){const z=await f.json().catch(()=>({}));throw new Error(z.detail||`HTTP ${f.status}: ${f.statusText}`)}return await f.json()}catch(f){throw console.error("批量删除从服务器失败:",f),f}},He=async w=>{try{const f=`
      <div style="text-align: left;">
        <p><strong>确定要删除从服务器 "${w.name}" 吗？</strong></p>

        <p><strong>此操作将执行深度清理：</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px; color: #666;">
          <li>🗑️ 删除从服务器记录</li>
          <li>🔌 删除所有关联的USB设备记录</li>
          <li>👥 从设备分组中移除这些设备</li>
          <li>👤 清除设备的直接用户分配关系</li>
          <li>🔄 更新设备占用记录状态</li>
          <li>🧹 清理空的设备分组</li>
          <li>📋 保留审计日志（合规要求）</li>
        </ul>

        <p style="color: #e74c3c; font-weight: bold;">⚠️ 此操作不可撤销！</p>
      </div>
    `;await Ae.confirm(f,"确认深度删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error",dangerouslyUseHTMLString:!0});const H=await xe(w.id);if(H.deletion_stats){const z=H.deletion_stats,ie=`✅ 从服务器 "${w.name}" 删除成功`;let le=`📊 清理统计：设备 ${z.devices_deleted} 个，分组关联 ${z.device_group_assignments_removed} 个`;if(z.empty_groups_cleaned>0&&(le+=`，清理空分组 ${z.empty_groups_cleaned} 个`),z.affected_groups&&z.affected_groups.length>0){const re=z.affected_groups.map(m=>m.name).join("、");le+=`
🔗 受影响的设备分组：${re}`}if(z.affected_users&&z.affected_users.length>0){const re=z.affected_users.map(m=>m.username).join("、");le+=`
👤 受影响的用户：${re}`}g.success(`${ie}
${le}`)}else g.success(`从服务器 "${w.name}" 删除成功`);await X()}catch(f){f!=="cancel"&&(console.error("删除从服务器失败:",f),g.error(`删除失败: ${f.message}`))}};return N({refreshData:X}),Qe(()=>{X(),console.log("用户权限调试信息:",{user:j.user,canManageSlaves:R.value,canDeleteSlaves:V.value,canCreateSlaves:S.value,canControlSlaves:pe.value})}),(w,f)=>{const H=lt,z=ot,ie=ft,le=mt,re=vt,m=Tt,c=wt,ne=dt,Y=Ct,ve=At,ke=Pt,Be=ns,me=Lt,ce=_t,fe=ut;return h(),G("div",Ml,[o("div",Bl,[o("div",Gl,[e(z,{type:"primary",onClick:X,loading:M.value},{default:s(()=>[e(H,null,{default:s(()=>[e($(tt))]),_:1}),f[13]||(f[13]=p(" 刷新列表 ",-1))]),_:1,__:[13]},8,["loading"]),S.value?(h(),O(z,{key:0,type:"success",onClick:f[0]||(f[0]=T=>F.value=!0)},{default:s(()=>[e(H,null,{default:s(()=>[e($(Et))]),_:1}),f[14]||(f[14]=p(" 添加分布式节点 ",-1))]),_:1,__:[14]})):W("",!0),R.value?(h(),O(z,{key:1,type:"warning",onClick:Ne,loading:U.value,disabled:L.value.length===0},{default:s(()=>[e(H,null,{default:s(()=>[e($(tt))]),_:1}),p(" 强制同步数据 ("+y(L.value.length)+") ",1)]),_:1},8,["loading","disabled"])):W("",!0),V.value?(h(),O(z,{key:2,type:"danger",onClick:Re,loading:x.value,disabled:L.value.length===0},{default:s(()=>[e(H,null,{default:s(()=>[e($(ls))]),_:1}),p(" 批量删除节点 ("+y(L.value.length)+") ",1)]),_:1},8,["loading","disabled"])):W("",!0)]),o("div",Nl,[e(le,{modelValue:K.value,"onUpdate:modelValue":f[1]||(f[1]=T=>K.value=T),placeholder:"筛选状态",style:{width:"120px","margin-right":"8px"},clearable:"",onChange:_e},{default:s(()=>[e(ie,{label:"全部",value:""}),e(ie,{label:"在线",value:"online"}),e(ie,{label:"离线",value:"offline"})]),_:1},8,["modelValue"]),e(re,{modelValue:J.value,"onUpdate:modelValue":f[2]||(f[2]=T=>J.value=T),placeholder:"搜索服务器名称...",style:{width:"200px"},clearable:"",onInput:_e},{prefix:s(()=>[e(H,null,{default:s(()=>[e($(Xe))]),_:1})]),_:1},8,["modelValue"])])]),Je((h(),O(Y,{data:Z.value,stripe:"",style:{width:"100%"},onSelectionChange:Ve},{default:s(()=>[e(m,{type:"selection",width:"50"}),e(m,{prop:"id",label:"ID",width:"50"}),e(m,{label:"分布式节点名称","min-width":"220",sortable:""},{default:s(({row:T})=>[o("div",Rl,[e(H,{class:"server-icon"},{default:s(()=>[e($(pt))]),_:1}),o("div",Fl,[e(c,{content:T.name,placement:"top",disabled:T.name.length<=25},{default:s(()=>[o("span",Hl,y(T.name),1)]),_:2},1032,["content","disabled"]),o("div",ql,[T.is_online?(h(),O(ne,{key:0,type:"success",size:"small"},{default:s(()=>f[15]||(f[15]=[p("在线",-1)])),_:1,__:[15]})):(h(),O(ne,{key:1,type:"danger",size:"small"},{default:s(()=>f[16]||(f[16]=[p("离线",-1)])),_:1,__:[16]}))])])])]),_:1}),e(m,{label:"IP地址",width:"120"},{default:s(({row:T})=>[o("span",Kl,y(T.ip_address)+":"+y(T.port),1)]),_:1}),e(m,{label:"VH端口",width:"65"},{default:s(({row:T})=>[o("span",null,y(T.vh_port),1)]),_:1}),e(m,{label:"状态",width:"85"},{default:s(({row:T})=>[e(ne,{type:te(T.status),size:"small"},{default:s(()=>[e(H,{class:"status-icon"},{default:s(()=>[T.status==="online"?(h(),O($(os),{key:0})):(h(),O($(as),{key:1}))]),_:2},1024),p(" "+y(T.status==="online"?"在线":"离线"),1)]),_:2},1032,["type"])]),_:1}),e(m,{label:"设备数量",width:"75"},{default:s(({row:T})=>[o("span",Jl,y(T.device_count||0),1)]),_:1}),e(m,{label:"最后心跳验证",width:"160","class-name":"no-wrap"},{default:s(({row:T})=>[T.last_seen&&T.last_seen!=="null"&&T.last_seen!==""?(h(),G("span",Wl,y(T.last_seen),1)):(h(),G("span",Yl," 无心跳记录 "))]),_:1}),e(m,{label:"最后配置核对",width:"160","class-name":"no-wrap"},{default:s(({row:T})=>[T.last_config_check?(h(),G("span",Ql,y(Le(T.last_config_check)),1)):(h(),G("span",Zl,"无核对记录"))]),_:1}),e(m,{label:"操作",width:"160",fixed:"right","class-name":"no-wrap"},{default:s(({row:T})=>[e(z,{type:"primary",size:"small",onClick:Ue=>Te(T)},{default:s(()=>f[17]||(f[17]=[p(" 详情 ",-1)])),_:2,__:[17]},1032,["onClick"]),pe.value?(h(),O(z,{key:0,type:"warning",size:"small",onClick:Ue=>Se(T,"restart")},{default:s(()=>f[18]||(f[18]=[p(" 重启 ",-1)])),_:2,__:[18]},1032,["onClick"])):W("",!0),V.value?(h(),O(z,{key:1,type:"danger",size:"small",onClick:Ue=>He(T)},{default:s(()=>f[19]||(f[19]=[p(" 删除 ",-1)])),_:2,__:[19]},1032,["onClick"])):W("",!0)]),_:1})]),_:1},8,["data"])),[[fe,M.value]]),o("div",Xl,[e(ve,{"current-page":D.value,"onUpdate:currentPage":f[3]||(f[3]=T=>D.value=T),"page-size":b.value,"onUpdate:pageSize":f[4]||(f[4]=T=>b.value=T),"page-sizes":[10,20,50,100],total:r.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:he,onCurrentChange:Pe},null,8,["current-page","page-size","total"])]),e(ce,{modelValue:F.value,"onUpdate:modelValue":f[12]||(f[12]=T=>F.value=T),title:"添加从服务器",width:"500px",onClose:Me},{footer:s(()=>[e(z,{onClick:f[11]||(f[11]=T=>F.value=!1)},{default:s(()=>f[20]||(f[20]=[p("取消",-1)])),_:1,__:[20]}),e(z,{type:"primary",onClick:ae,loading:I.value},{default:s(()=>f[21]||(f[21]=[p(" 确认添加 ",-1)])),_:1,__:[21]},8,["loading"])]),default:s(()=>[e(me,{ref_key:"addFormRef",ref:v,model:C,rules:k,"label-width":"100px"},{default:s(()=>[e(ke,{label:"服务器名称",prop:"server_name"},{default:s(()=>[e(re,{modelValue:C.server_name,"onUpdate:modelValue":f[5]||(f[5]=T=>C.server_name=T),placeholder:"请输入服务器名称"},null,8,["modelValue"])]),_:1}),e(ke,{label:"IP地址",prop:"server_ip"},{default:s(()=>[e(re,{modelValue:C.server_ip,"onUpdate:modelValue":f[6]||(f[6]=T=>C.server_ip=T),placeholder:"请输入IP地址"},null,8,["modelValue"])]),_:1}),e(ke,{label:"服务端口",prop:"server_port"},{default:s(()=>[e(Be,{modelValue:C.server_port,"onUpdate:modelValue":f[7]||(f[7]=T=>C.server_port=T),min:1,max:65535,placeholder:"8889"},null,8,["modelValue"])]),_:1}),e(ke,{label:"VH端口",prop:"vh_port"},{default:s(()=>[e(Be,{modelValue:C.vh_port,"onUpdate:modelValue":f[8]||(f[8]=T=>C.vh_port=T),min:1,max:65535,placeholder:"7575"},null,8,["modelValue"])]),_:1}),e(ke,{label:"位置"},{default:s(()=>[e(re,{modelValue:C.location,"onUpdate:modelValue":f[9]||(f[9]=T=>C.location=T),placeholder:"请输入服务器位置"},null,8,["modelValue"])]),_:1}),e(ke,{label:"描述"},{default:s(()=>[e(re,{modelValue:C.description,"onUpdate:modelValue":f[10]||(f[10]=T=>C.description=T),type:"textarea",placeholder:"请输入服务器描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},to=st(eo,[["__scopeId","data-v-c35ab19a"]]),so={class:"nested-group-tree"},lo={class:"tree-header"},oo={class:"header-actions"},ao={class:"tree-content"},no={class:"tree-node"},io={class:"node-content"},ro={class:"node-info"},uo={class:"node-name"},co={class:"node-tags"},po={class:"node-stats"},_o={class:"stat-item"},vo={key:0,class:"stat-item"},mo={class:"node-actions"},fo={class:"permission-indicators"},go={class:"action-buttons"},yo={key:1},ho={key:0,class:"level-warning"},bo={class:"dialog-footer"},$o={class:"device-details"},ko={class:"device-header"},wo={class:"device-actions"},Co={class:"device-name"},To={class:"primary-name"},So={key:0,class:"secondary-name"},Vo={class:"reorganize-content"},xo={class:"dialog-footer"},Do={class:"dialog-footer"},zo={__name:"NestedGroupTree",props:{refreshTrigger:{type:Number,default:0}},emits:["group-created","group-updated","group-deleted"],setup(ue,{emit:N}){const P=ue,se=N,Q=kt(),j=_(),M=_(),I=_(),U=_(),x=_([]),F=_(!1),B=_(!1),L=_(!1),K=_(!1),J=_(!1),D=_(!1),b=_(!1),r=_(!1),v=_(null),C=_([]),k=_([]),R={children:"children",label:"name"},V=Ee({name:"",group_type:"",description:"",parent_group_id:null}),S=Ee({group_name:"",group_description:"",device_ids:[],source_group_id:null}),pe={name:[{required:!0,message:"请输入分组名称",trigger:"blur"},{min:1,max:200,message:"分组名称长度在 1 到 200 个字符",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},ge={group_name:[{required:!0,message:"请输入新分组名称",trigger:"blur"},{min:1,max:200,message:"分组名称长度在 1 到 200 个字符",trigger:"blur"}]},Z=_(),te=Ee({id:null,name:"",group_type:"",description:""}),Le={name:[{required:!0,message:"请输入分组名称",trigger:"blur"},{min:1,max:200,message:"分组名称长度在 1 到 200 个字符",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},X=oe(()=>{if(!V.parent_group_id)return"";const m=(c,ne)=>{for(const Y of c){if(Y.id===ne)return Y.name;if(Y.children){const ve=m(Y.children,ne);if(ve)return ve}}return""};return m(x.value,V.parent_group_id)}),ye=oe(()=>{if(!V.parent_group_id)return 0;const m=(c,ne)=>{for(const Y of c){if(Y.id===ne)return Y.nesting_level+1;if(Y.children){const ve=m(Y.children,ne);if(ve!==null)return ve}}return null};return m(x.value,V.parent_group_id)||0}),_e=async()=>{try{const m=await fetch("/api/v1/device-groups/tree",{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(m.ok)x.value=await m.json(),console.log("嵌套分组树加载成功:",x.value);else throw new Error(`HTTP ${m.status}`)}catch(m){console.error("加载嵌套分组树失败:",m),g.error(`加载嵌套分组树失败: ${m.message}`)}},he=m=>{if(m.nesting_level>=3){g.warning("已达到最大嵌套深度（4层），无法创建子分组");return}V.parent_group_id=m.id,V.name="",V.group_type="nested",V.description="",F.value=!0},Pe=async()=>{try{await M.value.validate(),J.value=!0;const m=await fetch("/api/v1/device-groups/",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify(V)});if(m.ok){const c=await m.json();g.success("分组创建成功"),F.value=!1,Te(),_e(),se("group-created",c)}else{const c=await m.json();throw new Error(c.detail||`HTTP ${m.status}`)}}catch(m){console.error("创建分组失败:",m),g.error(`创建分组失败: ${m.message}`)}finally{J.value=!1}},Te=()=>{V.name="",V.group_type="",V.description="",V.parent_group_id=null,bt(()=>{var m;(m=M.value)==null||m.clearValidate()})},Se=m=>{Q.push({name:"DeviceCenterGroupDetail",params:{id:m.id}})},ae=m=>{te.id=m.id,te.name=m.name,te.group_type=m.group_type,te.description=m.description||"",K.value=!0},Me=async m=>{try{await Ae.confirm(`确定要删除分组 "${m.name}" 吗？此操作将同时删除所有子分组。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),g.info("删除功能开发中...")}catch{}},Ve=async m=>{try{v.value=m,D.value=!0,B.value=!0;const c=await fetch(`/api/v1/device-groups/${m.id}/devices`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(c.ok)C.value=await c.json();else throw new Error(`HTTP ${c.status}`)}catch(c){console.error("加载分组设备失败:",c),g.error(`加载分组设备失败: ${c.message}`)}finally{D.value=!1}},Ne=()=>{v.value=null,C.value=[],B.value=!1},Re=m=>{k.value=m,S.device_ids=m.map(c=>c.id)},xe=()=>{S.group_name="",S.group_description="",S.device_ids=[],S.source_group_id=null,k.value=[],L.value=!1},Fe=async()=>{try{await I.value.validate(),b.value=!0;const m={group_name:S.group_name,group_description:S.group_description,device_ids:S.device_ids,source_group_id:v.value.id},c=await fetch("/api/v1/device-groups/reorganize",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify(m)});if(c.ok){const ne=await c.json();g.success(`成功创建新分组: ${ne.new_group_name}`),L.value=!1,xe(),_e(),se("group-created",ne)}else{const ne=await c.json();throw new Error(ne.detail||`HTTP ${c.status}`)}}catch(m){console.error("设备重新分组失败:",m),g.error(`设备重新分组失败: ${m.message}`)}finally{b.value=!1}},He=async()=>{try{await Z.value.validate(),r.value=!0;const m=await fetch(`/api/v1/device-groups/${te.id}`,{method:"PUT",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify({name:te.name,group_type:te.group_type,description:te.description})});if(m.ok)g.success("分组信息更新成功"),K.value=!1,w(),_e();else{const c=await m.json();throw new Error(c.detail||`HTTP ${m.status}`)}}catch(m){console.error("更新分组失败:",m),g.error(`更新分组失败: ${m.message}`)}finally{r.value=!1}},w=()=>{te.id=null,te.name="",te.group_type="",te.description="",bt(()=>{var m;(m=Z.value)==null||m.clearValidate()})},f=m=>({server:"服务器",mixed:"混合",single:"单设备",nested:"嵌套"})[m]||m,H=m=>({server:"success",mixed:"primary",single:"warning",nested:"info",reorganized:"danger"})[m]||"info",z=m=>({online:"在线",offline:"离线",busy:"占用",available:"可用",error:"错误"})[m]||m,ie=m=>({online:"success",offline:"info",busy:"warning",available:"success",error:"danger"})[m]||"info",le=m=>({ca_lock:"CA锁",encryption_key:"加密锁",bank_ukey:"银行U盾"})[m]||m,re=m=>({ca_lock:"danger",encryption_key:"warning",bank_ukey:"primary"})[m]||"info";return rt(()=>P.refreshTrigger,()=>{_e()}),Qe(()=>{_e()}),(m,c)=>{const ne=lt,Y=ot,ve=dt,ke=wt,Be=Nt,me=vt,ce=Pt,fe=ft,T=mt,Ue=rs,Ie=Lt,De=_t,qe=xt("Operation"),d=Tt,i=Ct,a=ut;return h(),G("div",so,[o("div",lo,[c[19]||(c[19]=o("h3",null,"嵌套分组管理",-1)),o("div",oo,[e(Y,{type:"primary",onClick:_e},{default:s(()=>[e(ne,null,{default:s(()=>[e($(tt))]),_:1}),c[17]||(c[17]=p(" 刷新 ",-1))]),_:1,__:[17]}),e(Y,{type:"success",onClick:c[0]||(c[0]=t=>F.value=!0)},{default:s(()=>[e(ne,null,{default:s(()=>[e($(Et))]),_:1}),c[18]||(c[18]=p(" 创建分组 ",-1))]),_:1,__:[18]})])]),o("div",ao,[e(Be,{ref_key:"treeRef",ref:j,data:x.value,props:R,"expand-on-click-node":!1,"default-expand-all":!1,"node-key":"id",class:"group-tree"},{default:s(({node:t,data:l})=>[o("div",no,[o("div",io,[o("div",ro,[o("span",uo,y(l.name),1),o("div",co,[e(ve,{type:H(l.group_type),size:"small"},{default:s(()=>[p(y(f(l.group_type)),1)]),_:2},1032,["type"]),e(ve,{type:"info",size:"small"},{default:s(()=>[p(" L"+y(l.nesting_level),1)]),_:2},1024)])]),o("div",po,[o("span",_o,[e(ne,null,{default:s(()=>[e($(pt))]),_:1}),p(" "+y(l.device_count)+"设备 ",1)]),l.child_count>0?(h(),G("span",vo,[e(ne,null,{default:s(()=>[e($(is))]),_:1}),p(" "+y(l.child_count)+"子组 ",1)])):W("",!0)])]),o("div",mo,[o("div",fo,[l.is_readonly?(h(),O(ve,{key:0,type:"info",size:"small"},{default:s(()=>c[20]||(c[20]=[p(" 只读 ",-1)])),_:1,__:[20]})):W("",!0),l.can_view_devices?W("",!0):(h(),O(ve,{key:1,type:"warning",size:"small"},{default:s(()=>c[21]||(c[21]=[p(" 无设备权限 ",-1)])),_:1,__:[21]}))]),o("div",go,[e(Y,{type:"primary",size:"small",onClick:u=>Se(l)},{default:s(()=>c[22]||(c[22]=[p(" 详情 ",-1)])),_:2,__:[22]},1032,["onClick"]),l.can_view_devices?(h(),O(Y,{key:0,type:"info",size:"small",onClick:u=>Ve(l)},{default:s(()=>c[23]||(c[23]=[p(" 查看设备 ",-1)])),_:2,__:[23]},1032,["onClick"])):W("",!0),l.can_manage?(h(),G(Ge,{key:1},[e(Y,{type:"success",size:"small",onClick:u=>he(l),disabled:l.nesting_level>=3},{default:s(()=>[l.nesting_level>=3?(h(),O(ke,{key:0,content:"已达到最大嵌套深度（4层）",placement:"top"},{default:s(()=>c[24]||(c[24]=[o("span",null,"添加子组",-1)])),_:1,__:[24]})):(h(),G("span",yo,"添加子组"))]),_:2},1032,["onClick","disabled"]),e(Y,{type:"warning",size:"small",onClick:u=>ae(l)},{default:s(()=>c[25]||(c[25]=[p(" 编辑 ",-1)])),_:2,__:[25]},1032,["onClick"]),e(Y,{type:"danger",size:"small",onClick:u=>Me(l)},{default:s(()=>c[26]||(c[26]=[p(" 删除 ",-1)])),_:2,__:[26]},1032,["onClick"])],64)):W("",!0)])])])]),_:1},8,["data"])]),e(De,{modelValue:F.value,"onUpdate:modelValue":c[5]||(c[5]=t=>F.value=t),title:V.parent_group_id?"创建子分组":"创建顶级分组",width:"500px",onClose:Te},{footer:s(()=>[o("div",bo,[e(Y,{onClick:c[4]||(c[4]=t=>F.value=!1)},{default:s(()=>c[27]||(c[27]=[p("取消",-1)])),_:1,__:[27]}),e(Y,{type:"primary",onClick:Pe,loading:J.value,disabled:ye.value>=4},{default:s(()=>c[28]||(c[28]=[p(" 创建 ",-1)])),_:1,__:[28]},8,["loading","disabled"])])]),default:s(()=>[e(Ie,{ref_key:"createFormRef",ref:M,model:V,rules:pe,"label-width":"120px"},{default:s(()=>[V.parent_group_id?(h(),O(ce,{key:0,label:"父分组"},{default:s(()=>[e(me,{value:X.value,disabled:"",placeholder:"顶级分组"},null,8,["value"])]),_:1})):W("",!0),e(ce,{label:"分组名称",prop:"name"},{default:s(()=>[e(me,{modelValue:V.name,"onUpdate:modelValue":c[1]||(c[1]=t=>V.name=t),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(ce,{label:"分组类型",prop:"group_type"},{default:s(()=>[e(T,{modelValue:V.group_type,"onUpdate:modelValue":c[2]||(c[2]=t=>V.group_type=t),placeholder:"请选择分组类型",style:{width:"100%"}},{default:s(()=>[e(fe,{label:"服务器分组",value:"server"}),e(fe,{label:"混合分组",value:"mixed"}),e(fe,{label:"单设备分组",value:"single"}),e(fe,{label:"嵌套分组",value:"nested"})]),_:1},8,["modelValue"])]),_:1}),e(ce,{label:"分组描述",prop:"description"},{default:s(()=>[e(me,{modelValue:V.description,"onUpdate:modelValue":c[3]||(c[3]=t=>V.description=t),type:"textarea",rows:3,placeholder:"请输入分组描述"},null,8,["modelValue"])]),_:1}),V.parent_group_id?(h(),O(ce,{key:1,label:"嵌套层级"},{default:s(()=>[e(me,{value:`第 ${ye.value} 层`,disabled:""},null,8,["value"]),ye.value>=3?(h(),G("div",ho,[e(Ue,{title:"已达到最大嵌套深度（4层）",type:"warning",closable:!1,"show-icon":""})])):W("",!0)]),_:1})):W("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(De,{modelValue:B.value,"onUpdate:modelValue":c[7]||(c[7]=t=>B.value=t),title:"分组设备详情",width:"80%",onClose:Ne},{default:s(()=>{var t,l;return[o("div",$o,[o("div",ko,[o("h4",null,y((t=v.value)==null?void 0:t.name)+" - 设备列表",1),o("div",wo,[(l=v.value)!=null&&l.can_manage?(h(),O(Y,{key:0,type:"success",onClick:c[6]||(c[6]=u=>L.value=!0)},{default:s(()=>[e(ne,null,{default:s(()=>[e(qe)]),_:1}),c[29]||(c[29]=p(" 重新分组 ",-1))]),_:1,__:[29]})):W("",!0)])]),Je((h(),O(i,{data:C.value,stripe:"",style:{width:"100%"}},{default:s(()=>[e(d,{prop:"name",label:"设备名称","min-width":"150"},{default:s(({row:u})=>[o("div",Co,[o("span",To,y(u.custom_name||u.name),1),u.custom_name&&u.name!==u.custom_name?(h(),G("span",So," ("+y(u.name)+") ",1)):W("",!0)])]),_:1}),e(d,{prop:"device_type",label:"设备类型",width:"120"},{default:s(({row:u})=>[e(ve,{type:re(u.device_type),size:"small"},{default:s(()=>[p(y(le(u.device_type)),1)]),_:2},1032,["type"])]),_:1}),e(d,{prop:"status",label:"状态",width:"100"},{default:s(({row:u})=>[e(ve,{type:ie(u.status),size:"small"},{default:s(()=>[p(y(z(u.status)),1)]),_:2},1032,["type"])]),_:1}),e(d,{prop:"slave_server_name",label:"所属服务器",width:"150"}),e(d,{prop:"vid",label:"VID",width:"80"}),e(d,{prop:"pid",label:"PID",width:"80"}),e(d,{prop:"description",label:"描述","min-width":"200"})]),_:1},8,["data"])),[[a,D.value]])])]}),_:1},8,["modelValue"]),e(De,{modelValue:L.value,"onUpdate:modelValue":c[11]||(c[11]=t=>L.value=t),title:"设备重新分组",width:"60%",onClose:xe},{footer:s(()=>[o("div",xo,[e(Y,{onClick:c[10]||(c[10]=t=>L.value=!1)},{default:s(()=>c[30]||(c[30]=[p("取消",-1)])),_:1,__:[30]}),e(Y,{type:"primary",onClick:Fe,loading:b.value,disabled:k.value.length===0},{default:s(()=>c[31]||(c[31]=[p(" 创建新分组 ",-1)])),_:1,__:[31]},8,["loading","disabled"])])]),default:s(()=>[o("div",Vo,[e(Ie,{ref_key:"reorganizeFormRef",ref:I,model:S,rules:ge,"label-width":"120px"},{default:s(()=>[e(ce,{label:"新分组名称",prop:"group_name"},{default:s(()=>[e(me,{modelValue:S.group_name,"onUpdate:modelValue":c[8]||(c[8]=t=>S.group_name=t),placeholder:"请输入新分组名称"},null,8,["modelValue"])]),_:1}),e(ce,{label:"分组描述",prop:"group_description"},{default:s(()=>[e(me,{modelValue:S.group_description,"onUpdate:modelValue":c[9]||(c[9]=t=>S.group_description=t),type:"textarea",rows:3,placeholder:"请输入分组描述"},null,8,["modelValue"])]),_:1}),e(ce,{label:"选择设备"},{default:s(()=>[e(i,{ref_key:"deviceSelectionTable",ref:U,data:C.value,onSelectionChange:Re,"max-height":"300"},{default:s(()=>[e(d,{type:"selection",width:"55"}),e(d,{prop:"name",label:"设备名称","min-width":"150"}),e(d,{prop:"device_type",label:"类型",width:"100"}),e(d,{prop:"status",label:"状态",width:"80"})]),_:1},8,["data"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"]),e(De,{modelValue:K.value,"onUpdate:modelValue":c[16]||(c[16]=t=>K.value=t),title:"编辑分组",width:"500px",onClose:w},{footer:s(()=>[o("div",Do,[e(Y,{onClick:c[15]||(c[15]=t=>K.value=!1)},{default:s(()=>c[32]||(c[32]=[p("取消",-1)])),_:1,__:[32]}),e(Y,{type:"primary",onClick:He,loading:r.value},{default:s(()=>c[33]||(c[33]=[p(" 确认修改 ",-1)])),_:1,__:[33]},8,["loading"])])]),default:s(()=>[e(Ie,{ref_key:"editFormRef",ref:Z,model:te,rules:Le,"label-width":"100px"},{default:s(()=>[e(ce,{label:"分组名称",prop:"name"},{default:s(()=>[e(me,{modelValue:te.name,"onUpdate:modelValue":c[12]||(c[12]=t=>te.name=t),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(ce,{label:"分组类型",prop:"group_type"},{default:s(()=>[e(T,{modelValue:te.group_type,"onUpdate:modelValue":c[13]||(c[13]=t=>te.group_type=t),placeholder:"请选择分组类型",style:{width:"100%"}},{default:s(()=>[e(fe,{label:"标准分组",value:"standard"}),e(fe,{label:"临时分组",value:"temporary"}),e(fe,{label:"专用分组",value:"dedicated"}),e(fe,{label:"重组分组",value:"reorganized"})]),_:1},8,["modelValue"])]),_:1}),e(ce,{label:"描述信息",prop:"description"},{default:s(()=>[e(me,{modelValue:te.description,"onUpdate:modelValue":c[14]||(c[14]=t=>te.description=t),type:"textarea",rows:3,placeholder:"请输入分组描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Ao=st(zo,[["__scopeId","data-v-81ed4342"]]),Eo={class:"device-group-management"},Lo={class:"toolbar"},Po={class:"toolbar-left"},Uo={class:"toolbar-right"},Io={class:"view-tabs"},jo={class:"list-view"},Oo={class:"group-name"},Mo={class:"device-count"},Bo={key:1,class:"text-muted"},Go={class:"user-count"},No={class:"tree-view"},Ro={class:"pagination-container"},Fo={class:"assignment-content"},Ho={class:"assignment-stats"},qo={class:"device-selection",style:{"margin-top":"20px"}},Ko={class:"dialog-footer"},Jo={__name:"DeviceGroupManagement",emits:["stats-update"],setup(ue,{expose:N,emit:P}){const se=P,Q=kt(),j=Ze(),M=_(!1),I=_(!1),U=_(!1),x=_(!1),F=_(!1),B=_(!1),L=_([]),K=_(""),J=_(""),D=_("list"),b=_(0),r=_(!1),v=_(null),C=_([]),k=_([]),R=_(),V=_(1),S=_(20),pe=_(0),ge=_(),Z=Ee({name:"",group_type:"",description:""}),te={name:[{required:!0,message:"请输入分组名称",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},Le=_(),X=Ee({id:null,name:"",group_type:"",description:""}),ye={name:[{required:!0,message:"请输入分组名称",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},_e=oe(()=>{let d=L.value;if(K.value&&(d=d.filter(i=>i.group_type===K.value)),J.value){const i=J.value.toLowerCase();d=d.filter(a=>a.name.toLowerCase().includes(i))}return d}),he=d=>({single_device:"info",multi_device:"primary",server_group:"success",cross_group:"warning",batch_group:"danger"})[d]||"",Pe=d=>({single_device:"单设备分组",multi_device:"多设备分组",server_group:"服务器分组",cross_group:"交叉分组",batch_group:"批量分组"})[d]||"未知类型",Te=d=>{switch(d){case"single_device":g.info("单设备分组：每个分组只包含一个设备");break;case"multi_device":g.info("多设备分组：可以包含多个不同的设备");break;case"server_group":g.info("服务器分组：按从服务器整体进行分组");break;case"cross_group":g.info("交叉分组：设备可以同时属于多个分组");break;case"batch_group":g.info("批量分组：支持批量设备的分组操作");break}},Se=d=>d?new Date(d).toLocaleString():"",ae=async()=>{M.value=!0;try{const d=await fetch("/api/v1/device-groups/",{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(!d.ok)throw new Error(`HTTP ${d.status}: ${d.statusText}`);const i=await d.json();console.log("获取到的设备分组数据:",i),L.value=i.map(a=>({id:a.id,name:a.name,group_type:a.group_type,device_count:a.device_count||0,has_virtual_devices:a.has_virtual_devices||!1,user_count:a.user_count||0,description:a.description||"",created_at:a.created_at,updated_at:a.updated_at,is_active:a.is_active})),console.log("处理后的设备分组数据:",L.value),pe.value=L.value.length,Me()}catch(d){console.error("加载设备分组数据失败:",d),g.error(`加载设备分组数据失败: ${d.message}`),L.value=[],pe.value=0}finally{M.value=!1}},Me=()=>{const d={total:L.value.length,server:L.value.filter(i=>i.group_type==="server").length,mixed:L.value.filter(i=>i.group_type==="mixed").length};se("stats-update",d)},Ve=()=>{},Ne=d=>{S.value=d,ae()},Re=d=>{V.value=d,ae()},xe=d=>{try{console.log("点击详情按钮，分组信息:",d),console.log("准备跳转到路径:",`/device-center/device-group/${d.id}`),Q.push(`/device-center/device-group/${d.id}`).then(()=>{console.log("路由跳转成功")}).catch(i=>{console.error("路由跳转失败:",i),g.error(`跳转失败: ${i.message}`)})}catch(i){console.error("viewGroupDetail函数执行失败:",i),g.error(`操作失败: ${i.message}`)}},Fe=d=>{Object.assign(X,{id:d.id,name:d.name,group_type:d.group_type,description:d.description}),F.value=!0},He=async d=>{try{await Ae.confirm(`确定要删除分组 "${d.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}),g.success(`分组 "${d.name}" 删除成功`),ae()}catch{}},w=async()=>{try{await ge.value.validate(),I.value=!0;const d=await fetch("/api/v1/device-groups/",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify({name:Z.name,group_type:Z.type,description:Z.description,auto_virtual:!1})});if(!d.ok){const a=await d.json();throw new Error(a.detail||`HTTP ${d.status}`)}const i=await d.json();console.log("分组创建成功:",i),g.success("设备分组创建成功"),x.value=!1,H(),ae()}catch(d){console.error("创建设备分组失败:",d),g.error(`创建设备分组失败: ${d.message}`)}finally{I.value=!1}},f=async()=>{try{await Le.value.validate(),U.value=!0,await new Promise(d=>setTimeout(d,1e3)),g.success("设备分组修改成功"),F.value=!1,z(),ae()}catch(d){console.error("修改设备分组失败:",d),g.error("修改设备分组失败")}finally{U.value=!1}},H=()=>{var d;Object.assign(Z,{name:"",group_type:"",description:""}),(d=ge.value)==null||d.clearValidate()},z=()=>{var d;Object.assign(X,{id:null,name:"",group_type:"",description:""}),(d=Le.value)==null||d.clearValidate()};N({refreshData:ae});const ie=d=>{console.log("分组创建事件:",d),g.success(`分组 "${d.group_name}" 已创建`),ae()},le=d=>{console.log("分组更新事件:",d),ae()},re=d=>{console.log("设备分配事件:",d),g.info(`${d.added_count||d.total_assigned} 个设备已分配到分组`),ae()},m=async()=>{B.value=!0,await Promise.all([c(),ae()])},c=async()=>{try{const d=await fetch("/api/v1/devices",{headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"}});if(d.ok){const i=await d.json();k.value=i.devices||[]}else throw new Error(`HTTP ${d.status}`)}catch(d){console.error("加载可用设备失败:",d),g.error(`加载可用设备失败: ${d.message}`)}},ne=d=>{v.value=d,C.value=[],R.value&&R.value.clearSelection()},Y=d=>{C.value=d},ve=async()=>{try{r.value=!0;const d={assignments:[{group_id:v.value,device_ids:C.value.map(a=>a.id)}]},i=await fetch("/api/v1/device-groups/batch-assign",{method:"POST",headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"},body:JSON.stringify(d)});if(i.ok){const a=await i.json(),t=a.assignments?a.assignments.reduce((l,u)=>l+u.added_devices.length,0):0;g.success(`成功分配 ${t} 个设备`),B.value=!1,ae()}else{const a=await i.json();throw new Error(a.detail||`HTTP ${i.status}`)}}catch(d){console.error("设备分配失败:",d),g.error(`设备分配失败: ${d.message}`)}finally{r.value=!1}},ke=()=>{v.value=null,C.value=[],k.value=[],R.value&&R.value.clearSelection()},Be=d=>({ca_lock:"CA锁",encryption_key:"加密锁",bank_ukey:"银行U盾",other:"其他设备"})[d]||d,me=d=>({online:"在线",offline:"离线",busy:"占用",available:"可用",error:"错误"})[d]||d,ce=d=>({online:"success",offline:"info",busy:"warning",available:"success",error:"danger"})[d]||"info",fe=d=>{console.log("切换视图:",d),d==="tree"&&b.value++},T=d=>{console.log("嵌套分组创建:",d),ae(),b.value++},Ue=d=>{console.log("嵌套分组更新:",d),ae(),b.value++},Ie=d=>{console.log("嵌套分组删除:",d),ae(),b.value++},De=()=>{ee.subscribe("device_updates"),ee.on("device_group_created",ie),ee.on("device_groups_batch_created",le),ee.on("devices_assigned_to_group",re),ee.on("devices_batch_assigned",re)},qe=()=>{ee.off("device_group_created",ie),ee.off("device_groups_batch_created",le),ee.off("devices_assigned_to_group",re),ee.off("devices_batch_assigned",re),ee.unsubscribe("device_updates")};return Qe(()=>{ae(),De()}),$t(()=>{qe()}),(d,i)=>{const a=lt,t=ot,l=ft,u=mt,A=vt,de=xt("List"),we=Ut,We=xt("Operation"),Ke=It,q=Tt,$e=dt,Ye=wt,je=Ct,Oe=At,ze=Pt,at=Lt,be=_t,nt=Ot,it=jt,E=ut;return h(),G("div",Eo,[o("div",Lo,[o("div",Po,[e(t,{type:"primary",onClick:ae,loading:M.value},{default:s(()=>[e(a,null,{default:s(()=>[e($(tt))]),_:1}),i[19]||(i[19]=p(" 刷新列表 ",-1))]),_:1,__:[19]},8,["loading"]),$(j).hasPermission("device.group")?(h(),O(t,{key:0,type:"success",onClick:i[0]||(i[0]=n=>x.value=!0)},{default:s(()=>[e(a,null,{default:s(()=>[e($(Et))]),_:1}),i[20]||(i[20]=p(" 创建分组 ",-1))]),_:1,__:[20]})):W("",!0),$(j).hasPermission("device.assign")?(h(),O(t,{key:1,type:"primary",onClick:m},{default:s(()=>[e(a,null,{default:s(()=>[e($(Dt))]),_:1}),i[21]||(i[21]=p(" 设备分配 ",-1))]),_:1,__:[21]})):W("",!0)]),o("div",Uo,[e(u,{modelValue:K.value,"onUpdate:modelValue":i[1]||(i[1]=n=>K.value=n),placeholder:"筛选分组类型",style:{width:"180px","margin-right":"8px"},clearable:"",onChange:Ve},{default:s(()=>[e(l,{label:"全部分组",value:""}),e(l,{label:"单设备分组",value:"single_device"}),e(l,{label:"多设备分组",value:"multi_device"}),e(l,{label:"服务器分组",value:"server_group"}),e(l,{label:"交叉分组",value:"cross_group"}),e(l,{label:"批量分组",value:"batch_group"})]),_:1},8,["modelValue"]),e(A,{modelValue:J.value,"onUpdate:modelValue":i[2]||(i[2]=n=>J.value=n),placeholder:"搜索分组名称...",style:{width:"200px"},clearable:"",onInput:Ve},{prefix:s(()=>[e(a,null,{default:s(()=>[e($(Xe))]),_:1})]),_:1},8,["modelValue"])])]),o("div",Io,[e(Ke,{modelValue:D.value,"onUpdate:modelValue":i[3]||(i[3]=n=>D.value=n),onTabChange:fe},{default:s(()=>[e(we,{label:"列表视图",name:"list"},{label:s(()=>[o("span",null,[e(a,null,{default:s(()=>[e(de)]),_:1}),i[22]||(i[22]=p(" 列表视图 ",-1))])]),_:1}),e(we,{label:"嵌套树视图",name:"tree"},{label:s(()=>[o("span",null,[e(a,null,{default:s(()=>[e(We)]),_:1}),i[23]||(i[23]=p(" 嵌套树视图 ",-1))])]),_:1})]),_:1},8,["modelValue"])]),Je(o("div",jo,[Je((h(),O(je,{data:_e.value,stripe:"",style:{width:"100%"}},{default:s(()=>[e(q,{prop:"id",label:"ID",width:"80"}),e(q,{label:"分组名称","min-width":"150"},{default:s(({row:n})=>[o("div",Oo,[e(a,{class:"group-icon"},{default:s(()=>[e($(Dt))]),_:1}),o("span",null,y(n.name),1)])]),_:1}),e(q,{label:"分组类型",width:"120"},{default:s(({row:n})=>[e($e,{type:he(n.group_type),size:"small"},{default:s(()=>[p(y(Pe(n.group_type)),1)]),_:2},1032,["type"])]),_:1}),e(q,{label:"设备数量",width:"100"},{default:s(({row:n})=>[o("span",Mo,y(n.device_count||0),1)]),_:1}),e(q,{label:"虚拟设备",width:"100"},{default:s(({row:n})=>[n.has_virtual_devices?(h(),O($e,{key:0,type:"warning",size:"small"},{default:s(()=>i[24]||(i[24]=[p(" 有占位 ",-1)])),_:1,__:[24]})):(h(),G("span",Bo,"无"))]),_:1}),e(q,{label:"权限用户",width:"100"},{default:s(({row:n})=>[o("span",Go,y(n.user_count||0),1)]),_:1}),e(q,{label:"创建时间",width:"150"},{default:s(({row:n})=>[o("span",null,y(Se(n.created_at)),1)]),_:1}),e(q,{prop:"description",label:"描述","min-width":"150"}),e(q,{label:"操作",width:"280",fixed:"right"},{default:s(({row:n})=>[e(t,{type:"primary",size:"small",onClick:Ce=>xe(n)},{default:s(()=>i[25]||(i[25]=[p(" 详情 ",-1)])),_:2,__:[25]},1032,["onClick"]),n.is_readonly?(h(),O($e,{key:0,type:"info",size:"small",style:{margin:"0 5px"}},{default:s(()=>i[26]||(i[26]=[p(" 只读 ",-1)])),_:1,__:[26]})):W("",!0),n.can_manage?(h(),G(Ge,{key:1},[e(t,{type:"warning",size:"small",onClick:Ce=>Fe(n)},{default:s(()=>i[27]||(i[27]=[p(" 编辑 ",-1)])),_:2,__:[27]},1032,["onClick"]),e(t,{type:"danger",size:"small",onClick:Ce=>He(n)},{default:s(()=>i[28]||(i[28]=[p(" 删除 ",-1)])),_:2,__:[28]},1032,["onClick"])],64)):n.is_readonly?(h(),O(Ye,{key:2,content:"此分组由上级管理员创建，您只有查看权限",placement:"top"},{default:s(()=>[e(t,{type:"info",size:"small",disabled:""},{default:s(()=>i[29]||(i[29]=[p(" 无权限 ",-1)])),_:1,__:[29]})]),_:1})):W("",!0)]),_:1})]),_:1},8,["data"])),[[E,M.value]])],512),[[Bt,D.value==="list"]]),Je(o("div",No,[e(Ao,{"refresh-trigger":b.value,onGroupCreated:T,onGroupUpdated:Ue,onGroupDeleted:Ie},null,8,["refresh-trigger"])],512),[[Bt,D.value==="tree"]]),o("div",Ro,[e(Oe,{"current-page":V.value,"onUpdate:currentPage":i[4]||(i[4]=n=>V.value=n),"page-size":S.value,"onUpdate:pageSize":i[5]||(i[5]=n=>S.value=n),"page-sizes":[10,20,50,100],total:pe.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ne,onCurrentChange:Re},null,8,["current-page","page-size","total"])]),e(be,{modelValue:x.value,"onUpdate:modelValue":i[10]||(i[10]=n=>x.value=n),title:"创建设备分组",width:"500px",onClose:H},{footer:s(()=>[e(t,{onClick:i[9]||(i[9]=n=>x.value=!1)},{default:s(()=>i[35]||(i[35]=[p("取消",-1)])),_:1,__:[35]}),e(t,{type:"primary",onClick:w,loading:I.value},{default:s(()=>i[36]||(i[36]=[p(" 确认创建 ",-1)])),_:1,__:[36]},8,["loading"])]),default:s(()=>[e(at,{ref_key:"createFormRef",ref:ge,model:Z,rules:te,"label-width":"100px"},{default:s(()=>[e(ze,{label:"分组名称",prop:"name"},{default:s(()=>[e(A,{modelValue:Z.name,"onUpdate:modelValue":i[6]||(i[6]=n=>Z.name=n),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(ze,{label:"分组类型",prop:"group_type"},{default:s(()=>[e(u,{modelValue:Z.group_type,"onUpdate:modelValue":i[7]||(i[7]=n=>Z.group_type=n),placeholder:"请选择分组类型",onChange:Te},{default:s(()=>[e(l,{label:"单设备分组",value:"single_device"},{default:s(()=>i[30]||(i[30]=[o("div",{class:"option-content"},[o("div",{class:"option-title"},"单设备分组"),o("div",{class:"option-desc"},"一个设备独立成组")],-1)])),_:1,__:[30]}),e(l,{label:"多设备分组",value:"multi_device"},{default:s(()=>i[31]||(i[31]=[o("div",{class:"option-content"},[o("div",{class:"option-title"},"多设备分组"),o("div",{class:"option-desc"},"多个设备组成一个分组")],-1)])),_:1,__:[31]}),e(l,{label:"服务器分组",value:"server_group"},{default:s(()=>i[32]||(i[32]=[o("div",{class:"option-content"},[o("div",{class:"option-title"},"服务器分组"),o("div",{class:"option-desc"},"以从服务器为单位进行分组")],-1)])),_:1,__:[32]}),e(l,{label:"交叉分组",value:"cross_group"},{default:s(()=>i[33]||(i[33]=[o("div",{class:"option-content"},[o("div",{class:"option-title"},"交叉分组"),o("div",{class:"option-desc"},"一个设备可属于多个分组")],-1)])),_:1,__:[33]}),e(l,{label:"批量分组",value:"batch_group"},{default:s(()=>i[34]||(i[34]=[o("div",{class:"option-content"},[o("div",{class:"option-title"},"批量分组"),o("div",{class:"option-desc"},"支持批量设备的分组操作")],-1)])),_:1,__:[34]})]),_:1},8,["modelValue"])]),_:1}),e(ze,{label:"描述"},{default:s(()=>[e(A,{modelValue:Z.description,"onUpdate:modelValue":i[8]||(i[8]=n=>Z.description=n),type:"textarea",placeholder:"请输入分组描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(be,{modelValue:F.value,"onUpdate:modelValue":i[15]||(i[15]=n=>F.value=n),title:"编辑设备分组",width:"500px",onClose:z},{footer:s(()=>[e(t,{onClick:i[14]||(i[14]=n=>F.value=!1)},{default:s(()=>i[37]||(i[37]=[p("取消",-1)])),_:1,__:[37]}),e(t,{type:"primary",onClick:f,loading:U.value},{default:s(()=>i[38]||(i[38]=[p(" 确认修改 ",-1)])),_:1,__:[38]},8,["loading"])]),default:s(()=>[e(at,{ref_key:"editFormRef",ref:Le,model:X,rules:ye,"label-width":"100px"},{default:s(()=>[e(ze,{label:"分组名称",prop:"name"},{default:s(()=>[e(A,{modelValue:X.name,"onUpdate:modelValue":i[11]||(i[11]=n=>X.name=n),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(ze,{label:"分组类型",prop:"group_type"},{default:s(()=>[e(u,{modelValue:X.group_type,"onUpdate:modelValue":i[12]||(i[12]=n=>X.group_type=n),placeholder:"请选择分组类型"},{default:s(()=>[e(l,{label:"单设备分组",value:"single_device"}),e(l,{label:"多设备分组",value:"multi_device"}),e(l,{label:"服务器分组",value:"server_group"}),e(l,{label:"交叉分组",value:"cross_group"}),e(l,{label:"批量分组",value:"batch_group"})]),_:1},8,["modelValue"])]),_:1}),e(ze,{label:"描述"},{default:s(()=>[e(A,{modelValue:X.description,"onUpdate:modelValue":i[13]||(i[13]=n=>X.description=n),type:"textarea",placeholder:"请输入分组描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(be,{modelValue:B.value,"onUpdate:modelValue":i[18]||(i[18]=n=>B.value=n),title:"设备分配",width:"80%",onClose:ke},{footer:s(()=>[o("div",Ko,[e(t,{onClick:i[17]||(i[17]=n=>B.value=!1)},{default:s(()=>i[42]||(i[42]=[p("取消",-1)])),_:1,__:[42]}),e(t,{type:"primary",onClick:ve,loading:r.value,disabled:!v.value||C.value.length===0},{default:s(()=>i[43]||(i[43]=[p(" 分配设备 ",-1)])),_:1,__:[43]},8,["loading","disabled"])])]),default:s(()=>[o("div",Fo,[e(it,{gutter:20},{default:s(()=>[e(nt,{span:12},{default:s(()=>[i[39]||(i[39]=o("h4",null,"选择设备分组",-1)),e(u,{modelValue:v.value,"onUpdate:modelValue":i[16]||(i[16]=n=>v.value=n),placeholder:"请选择目标分组",style:{width:"100%"},onChange:ne},{default:s(()=>[(h(!0),G(Ge,null,et(L.value,n=>(h(),O(l,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1,__:[39]}),e(nt,{span:12},{default:s(()=>[i[40]||(i[40]=o("h4",null,"设备分配状态",-1)),o("div",Ho,[e($e,{type:"info"},{default:s(()=>[p("待分配: "+y(k.value.length),1)]),_:1}),e($e,{type:"success",style:{"margin-left":"10px"}},{default:s(()=>[p("已选择: "+y(C.value.length),1)]),_:1})])]),_:1,__:[40]})]),_:1}),o("div",qo,[i[41]||(i[41]=o("h4",null,"选择设备",-1)),e(je,{ref_key:"deviceSelectionTable",ref:R,data:k.value,onSelectionChange:Y,"max-height":"400"},{default:s(()=>[e(q,{type:"selection",width:"55"}),e(q,{label:"设备名称","min-width":"150"},{default:s(({row:n})=>[p(y(n.custom_name||n.device_name||n.name||"未知设备"),1)]),_:1}),e(q,{prop:"device_type",label:"设备类型",width:"120"},{default:s(({row:n})=>[e($e,{size:"small"},{default:s(()=>[p(y(Be(n.device_type)),1)]),_:2},1024)]),_:1}),e(q,{prop:"status",label:"状态",width:"100"},{default:s(({row:n})=>[e($e,{type:ce(n.status),size:"small"},{default:s(()=>[p(y(me(n.status)),1)]),_:2},1032,["type"])]),_:1}),e(q,{label:"所属服务器",width:"150"},{default:s(({row:n})=>{var Ce;return[p(y(n.server_name||((Ce=n.slave_server)==null?void 0:Ce.name)||n.slave_server_name||"未知服务器"),1)]}),_:1})]),_:1},8,["data"])])])]),_:1},8,["modelValue"])])}}},Wo=st(Jo,[["__scopeId","data-v-4316ad4b"]]),Vt="permission_audit_logs",Yo=1e3,Qo={ASSIGN:"assign",REVOKE:"revoke",MODIFY:"modify",BATCH_ASSIGN:"batch_assign",BATCH_REVOKE:"batch_revoke",COPY:"copy",TEMPLATE_APPLY:"template_apply"},Zo={DEVICE_GROUP:"device_group",DEVICE:"device",ORGANIZATION:"organization",USER:"user"};class ct{static getLogs(){try{const N=localStorage.getItem(Vt);return N?JSON.parse(N):[]}catch(N){return console.error("读取审计日志失败:",N),[]}}static saveLogs(N){try{const P=N.slice(-Yo);localStorage.setItem(Vt,JSON.stringify(P))}catch(P){console.error("保存审计日志失败:",P)}}static addLog(N){const P=this.getLogs();P.push({...N,id:Date.now()+Math.random(),timestamp:new Date().toISOString(),userAgent:navigator.userAgent,sessionId:this.getSessionId()}),this.saveLogs(P)}static getSessionId(){let N=sessionStorage.getItem("audit_session_id");return N||(N=Date.now()+"_"+Math.random().toString(36).substr(2,9),sessionStorage.setItem("audit_session_id",N)),N}static clearLogs(){localStorage.removeItem(Vt)}}function Xo(){const ue=Ze(),N=_([]),P=_(!1),se=_([]),Q=_([]),j=Ee({totalOperations:0,todayOperations:0,successRate:0,mostActiveUser:null,mostCommonOperation:null}),M=oe(()=>N.value.slice(-50).reverse()),I=oe(()=>{const D=new Date().toDateString();return N.value.filter(b=>new Date(b.timestamp).toDateString()===D)}),U=oe(()=>ue.hasPermission("audit.view")||ue.canAccessDeviceManagement),x=oe(()=>ue.hasPermission("permission.manage")||ue.canAccessDeviceManagement),F=async D=>{try{const b={operationType:D.type,operator:{id:ue.userInfo.id,username:ue.userInfo.username,role:ue.userInfo.role_name,permissionLevel:ue.userInfo.permission_level},targets:D.targets,permissions:D.permissions,details:D.details||{},result:"pending"};return ct.addLog(b),se.value.push(b),b}catch(b){throw console.error("记录操作失败:",b),g.error(`操作记录失败: ${b.message}`),b}},B=(D,b,r={})=>{const v=se.value.find(C=>C.id===D);if(v){v.result=b,v.completedAt=new Date().toISOString(),v.completionDetails=r;const C=ct.getLogs(),k=C.findIndex(V=>V.id===D);k!==-1&&(C[k]=v,ct.saveLogs(C));const R=se.value.findIndex(V=>V.id===D);R!==-1&&se.value.splice(R,1),Q.value.unshift(v),J()}},L=()=>{try{P.value=!0,N.value=ct.getLogs(),J()}catch(D){console.error("加载审计日志失败:",D),g.error("加载审计日志失败")}finally{P.value=!1}},K=async()=>{try{await Ae.confirm("确定要清除所有审计日志吗？此操作不可恢复。","确认清除",{confirmButtonText:"确定清除",cancelButtonText:"取消",type:"warning"}),ct.clearLogs(),N.value=[],Q.value=[],se.value=[],J(),g.success("审计日志已清除")}catch(D){D!=="cancel"&&(console.error("清除审计日志失败:",D),g.error("清除审计日志失败"))}},J=()=>{const D=N.value;j.totalOperations=D.length,j.todayOperations=I.value.length;const b=D.filter(C=>C.result==="success").length;j.successRate=D.length>0?(b/D.length*100).toFixed(2):0;const r={};D.forEach(C=>{var R;const k=(R=C.operator)==null?void 0:R.username;k&&(r[k]=(r[k]||0)+1)}),j.mostActiveUser=Object.keys(r).reduce((C,k)=>r[C]>r[k]?C:k,null);const v={};D.forEach(C=>{const k=C.operationType;k&&(v[k]=(v[k]||0)+1)}),j.mostCommonOperation=Object.keys(v).reduce((C,k)=>v[C]>v[k]?C:k,null)};return{auditLogs:N,loading:P,pendingOperations:se,operationHistory:Q,statistics:j,recentLogs:M,todayLogs:I,canViewAuditLogs:U,canManagePermissions:x,recordOperation:F,completeOperation:B,loadAuditLogs:L,clearAuditLogs:K,OPERATION_TYPES:Qo,PERMISSION_TYPES:Zo}}const ea={class:"permission-assignment"},ta={class:"toolbar"},sa={class:"toolbar-left"},la={class:"toolbar-right"},oa={key:0,class:"current-selection"},aa={class:"selection-info"},na={class:"selection-text"},ia={key:0,class:"batch-info"},ra={class:"selection-actions"},da={class:"main-content"},ua={class:"org-panel"},ca={class:"card-header"},pa={class:"header-controls"},_a={key:0,class:"batch-controls"},va={class:"batch-selection-info"},ma={key:0},fa={class:"node-info"},ga={class:"node-label"},ya={key:0,class:"org-type"},ha={key:1,class:"user-count"},ba={key:2,class:"user-role"},$a={class:"device-panel"},ka={class:"device-tabs"},wa={class:"tab-content"},Ca={class:"search-bar"},Ta={class:"device-list"},Sa=["onClick"],Va={class:"device-info"},xa={class:"device-name"},Da={class:"device-desc"},za={class:"device-meta"},Aa={class:"device-count"},Ea={class:"tab-content"},La={class:"device-filters"},Pa={class:"search-bar"},Ua={class:"device-list"},Ia=["onClick"],ja={class:"device-info"},Oa={class:"device-name"},Ma={class:"device-desc"},Ba={class:"device-meta"},Ga={class:"device-id"},Na={class:"assign-confirm"},Ra={__name:"PermissionAssignment",emits:["stats-update"],setup(ue,{expose:N,emit:P}){const se=P,Q=Ze(),j=ee,{organizationTreeData:M,selectedNode:I,loadOrganizationsWithUsers:U,getLevelName:x}=ys({autoLoad:!0,enableCache:!0});Xo();const F=_(!1),B=_(!1),L=_(!1),K=_(!1),J=_(!1),D=_(!1),b=_("single"),r=_([]),v=_(),C=_(0),k=_("groups"),R=_([]),V=_([]),S=_([]),pe=_(""),ge=_(""),Z=Ee({name:"",type:"",server:"",group:""}),te=_([]),Le=_([]),X=a=>!a||!Array.isArray(a)?[]:a.filter(t=>t.name==="新注册用户"?(console.log('🔧 PermissionAssignment - 过滤掉"新注册用户"组织:',t.name),!1):!0).map(t=>{const l={...t};return t.children&&t.children.length>0&&(l.children=X(t.children)),l}),ye=oe(()=>{console.log("🔍 PermissionAssignment - organizationTree computed"),console.log("🔍 PermissionAssignment - organizationTreeData.value:",M.value);const a=X(M.value);return console.log("🔍 PermissionAssignment - 过滤后数据:",a),console.log("🔍 PermissionAssignment - 过滤后数据长度:",a.length),a}),_e=oe(()=>{const a=[],t=(l,u=0)=>{Array.isArray(l)&&l.forEach(A=>{u<=2&&A.type==="organization"&&(a.push(A.id),A.children&&A.children.length>0&&t(A.children,u+1))})};return t(ye.value),a}),he={children:"children",label:"name"},Pe=oe(()=>r.value.reduce((a,t)=>a+(t.userCount||0),0)),Te=oe(()=>b.value==="single"?I.value?{name:I.value.name,type:I.value.type==="organization"?x(I.value.level):"用户",count:1}:null:r.value.length>0?{name:`${r.value.length} 个组织`,type:"批量选择",count:r.value.length}:null),Se=oe(()=>pe.value?R.value.filter(a=>a.name.toLowerCase().includes(pe.value.toLowerCase())||a.description&&a.description.toLowerCase().includes(pe.value.toLowerCase())):R.value),ae=oe(()=>ge.value?V.value.filter(a=>(a.name||a.device_name||"").toLowerCase().includes(ge.value.toLowerCase())):V.value),Me=a=>{switch(a){case"server":return"success";case"mixed":return"warning";case"single":return"info";default:return""}},Ve=a=>{switch(a){case"server":return"服务器分组";case"mixed":return"混合分组";case"single":return"单设备分组";default:return"未知类型"}},Ne=a=>{switch(a){case"在线":return"success";case"离线":return"danger";case"占用":return"warning";default:return"info"}},Re=a=>I.value&&I.value.id===a.id&&I.value.type===a.type,xe=a=>r.value.some(t=>t.id===a.id&&t.type===a.type),Fe=a=>{b.value==="single"&&(console.log("🔧 PermissionAssignment - 单选节点点击:",a),I.value=a,S.value=[],g.info(`已选择：${a.name}`))},He=(a,t)=>{console.log("🔧 PermissionAssignment - 节点复选框变化:",a,t),t.checkedNodes&&(r.value=t.checkedNodes.filter(l=>l.type==="organization"),console.log("🔧 PermissionAssignment - 更新多选列表:",r.value),r.value.length>0&&g.info(`已选择 ${r.value.length} 个组织`))},w=()=>{b.value==="single"?I.value=null:(r.value=[],v.value&&v.value.setCheckedKeys([])),S.value=[],g.info("已清除所有选择")},f=a=>{console.log("🔧 PermissionAssignment - 选择模式切换:",a),I.value=null,r.value=[],S.value=[],C.value++,g.info(`已切换到${a==="single"?"单选":"多选"}模式`)},H=()=>{if(!v.value)return;const a=[],t=u=>{u.forEach(A=>{A.type==="organization"&&a.push(A),A.children&&t(A.children)})};t(ye.value);const l=a.map(u=>u.id);v.value.setCheckedKeys(l),r.value=[...a],g.success(`已选择所有 ${a.length} 个组织`)},z=()=>{v.value&&(v.value.setCheckedKeys([]),r.value=[],g.info("已清空所有选择"))},ie=()=>{if(!v.value)return;const a=[],t=de=>{de.forEach(we=>{we.type==="organization"&&a.push(we),we.children&&t(we.children)})};t(ye.value);const l=r.value.map(de=>de.id),u=a.filter(de=>!l.includes(de.id)),A=u.map(de=>de.id);v.value.setCheckedKeys(A),r.value=[...u],g.info(`反选完成，当前选择 ${u.length} 个组织`)},le=()=>{J.value=!J.value,console.log("🔧 PermissionAssignment - 切换展开状态:",J.value),bt(()=>{v.value?J.value?(console.log("🔧 PermissionAssignment - 展开所有节点"),v.value.expandAll()):(console.log("🔧 PermissionAssignment - 收起所有节点"),v.value.collapseAll()):console.warn("⚠️ PermissionAssignment - orgTreeRef 未找到")})},re=a=>{S.value=[]},m=a=>{const t=S.value.indexOf(a);t>-1?S.value.splice(t,1):S.value.push(a)},c=()=>{},ne=()=>{},Y=()=>{let a=V.value;Z.name&&(a=a.filter(t=>(t.name||t.device_name||"").toLowerCase().includes(Z.name.toLowerCase()))),Z.type&&(a=a.filter(t=>Z.type==="other"?!["ca_lock","encryption_key","bank_ukey"].includes(t.device_type):t.device_type===Z.type)),Z.server&&(a=a.filter(t=>t.slave_server_id===Z.server)),ae.value=a},ve=()=>{if(!(b.value==="single"?I.value:r.value.length>0)){g.warning(`请先选择${b.value==="single"?"组织或用户":"要分配权限的组织"}`);return}if(S.value.length===0){g.warning("请选择要分配的设备或分组");return}D.value=!0},ke=async()=>{try{B.value=!0;const a=b.value==="single"?[I.value]:r.value;console.log("🔧 PermissionAssignment - 开始权限分配:",{模式:b.value,目标数量:a.length,设备数量:S.value.length,目标列表:a.map(l=>l.name)}),await new Promise(l=>setTimeout(l,1e3));const t=b.value==="single"?`成功为 ${a[0].name} 分配 ${S.value.length} 个设备权限`:`成功为 ${a.length} 个组织批量分配 ${S.value.length} 个设备权限`;g.success(t),D.value=!1,S.value=[],De()}catch(a){console.error("权限分配失败:",a),g.error("权限分配失败")}finally{B.value=!1}},Be=()=>{D.value=!1},me=async()=>{F.value=!0;try{console.log("🔧 PermissionAssignment - 开始刷新数据"),await Promise.all([U(),ce(),fe()]),C.value++,console.log("🔧 PermissionAssignment - 数据刷新完成，强制重新渲染，treeKey:",C.value),g.success("数据刷新成功")}catch(a){console.error("刷新数据失败:",a),g.error("刷新数据失败")}finally{F.value=!1}},ce=async()=>{try{L.value=!0;const a=await Ft();R.value=a.data||[]}catch(a){console.error("加载设备分组失败:",a),g.error("加载设备分组失败")}finally{L.value=!1}},fe=async()=>{try{K.value=!0;const a=await fetch("/api/v1/devices",{headers:{Authorization:`Bearer ${Q.token}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const t=await a.json();console.log("获取到的设备数据:",t);const l=t.devices||t||[];V.value=l.map(u=>{var A;return{id:u.id,name:u.custom_name||u.device_name,status:Ie(u.status),vid:u.vendor_id,pid:u.product_id,description:u.description||u.device_notes||"无描述",device_type:u.device_type,slave_server_name:((A=u.slave_server)==null?void 0:A.name)||"Unknown Server",physical_port:u.physical_port,is_shared:u.is_shared,is_virtual:u.is_virtual}}),console.log("处理后的设备数据:",V.value)}catch(a){console.error("加载设备列表失败:",a),g.error(`加载设备列表失败: ${a.message}`),V.value=[]}finally{K.value=!1}},T=async()=>{try{const a=await fetch("/api/v1/slave/list",{headers:{Authorization:`Bearer ${Q.token}`,"Content-Type":"application/json"}});if(a.ok){const t=await a.json();te.value=t.servers||[]}}catch(a){console.error("加载服务器列表失败:",a)}},Ue=async()=>{try{const a=await fetch("/api/v1/device-groups/",{headers:{Authorization:`Bearer ${Q.token}`,"Content-Type":"application/json"}});if(a.ok){const t=await a.json();Le.value=t||[]}}catch(a){console.error("加载设备分组列表失败:",a)}},Ie=a=>({online:"在线",offline:"离线",busy:"占用",available:"可用",error:"错误",maintenance:"维护中"})[a]||a||"未知",De=()=>{var t,l;const a={total:V.value.length,users:((t=I.value)==null?void 0:t.type)==="user"?1:0,orgs:((l=I.value)==null?void 0:l.type)==="organization"?1:0};se("stats-update",a)},qe=a=>{console.log("设备状态更新:",a);const t=V.value.findIndex(l=>l.id===a.device_id);t!==-1&&(V.value[t].status=Ie(a.new_status),g.info(`设备状态更新: ${V.value[t].name} -> ${V.value[t].status}`),De())},d=()=>{j.subscribe("device_updates"),j.on("device_status_update",qe);const a=Q.token;a&&j.connect(a)},i=()=>{j.off("device_status_update",qe),j.unsubscribe("device_updates")};return Qe(()=>{d()}),$t(()=>{i()}),N({refreshData:me}),rt(M,(a,t)=>{console.log("🔍 PermissionAssignment - organizationTreeData 数据变化:",{新数据:a,新数据长度:(a==null?void 0:a.length)||0,旧数据长度:(t==null?void 0:t.length)||0,数据类型:typeof a,是否数组:Array.isArray(a)}),a&&a.length>0&&(C.value++,console.log("🔧 PermissionAssignment - 强制重新渲染树组件, treeKey:",C.value),bt(()=>{_e.value&&_e.value.length>0&&console.log("🔧 PermissionAssignment - 设置默认展开节点:",_e.value)}))},{immediate:!0,deep:!0}),rt(ye,a=>{console.log("🔍 PermissionAssignment - organizationTree computed 变化:",a)},{immediate:!0}),rt(_e,a=>{console.log("🔍 PermissionAssignment - defaultExpandedKeys 变化:",a)},{immediate:!0}),Qe(()=>{console.log("🔧 PermissionAssignment - 组件挂载完成"),console.log("🔍 PermissionAssignment - 初始 organizationTreeData:",M.value),console.log("🔍 PermissionAssignment - 初始 organizationTree:",ye.value),console.log("🔍 PermissionAssignment - 初始 defaultExpandedKeys:",_e.value),ce(),fe(),T(),Ue()}),(a,t)=>{const l=lt,u=ot,A=dt,de=ps,we=cs,We=Nt,Ke=zt,q=vt,$e=Gt,Ye=Ut,je=Ot,Oe=ft,ze=mt,at=jt,be=It,nt=_t,it=ut;return h(),G("div",ea,[o("div",ta,[o("div",sa,[e(u,{type:"primary",onClick:me,loading:F.value},{default:s(()=>[e(l,null,{default:s(()=>[e($(tt))]),_:1}),t[9]||(t[9]=p(" 刷新数据 ",-1))]),_:1,__:[9]},8,["loading"])]),o("div",la,[e(u,{type:"success",onClick:ve,disabled:(b.value==="single"?!$(I):r.value.length===0)||S.value.length===0,loading:B.value},{default:s(()=>[e(l,null,{default:s(()=>[e($(ds))]),_:1}),t[10]||(t[10]=p(" 分配权限 ",-1))]),_:1,__:[10]},8,["disabled","loading"])])]),Te.value?(h(),G("div",oa,[o("div",aa,[e(l,{class:"selection-icon"},{default:s(()=>[e($(ht))]),_:1}),o("span",na,[t[11]||(t[11]=p(" 当前选中： ",-1)),o("strong",null,y(Te.value.name),1),e(A,{type:"info",size:"small"},{default:s(()=>[p(y(Te.value.type),1)]),_:1}),b.value==="multiple"&&r.value.length>0?(h(),G("span",ia," (共"+y(Pe.value)+"人) ",1)):W("",!0)])]),o("div",ra,[e(u,{size:"small",onClick:w},{default:s(()=>t[12]||(t[12]=[p("清除选择",-1)])),_:1,__:[12]})])])):W("",!0),o("div",da,[o("div",ua,[e(Ke,null,{header:s(()=>[o("div",ca,[t[15]||(t[15]=o("span",null,"组织架构",-1)),o("div",pa,[e(we,{modelValue:b.value,"onUpdate:modelValue":t[0]||(t[0]=E=>b.value=E),size:"small",onChange:f},{default:s(()=>[e(de,{value:"single"},{default:s(()=>t[13]||(t[13]=[p("单选",-1)])),_:1,__:[13]}),e(de,{value:"multiple"},{default:s(()=>t[14]||(t[14]=[p("多选",-1)])),_:1,__:[14]})]),_:1},8,["modelValue"]),e(u,{size:"small",onClick:le},{default:s(()=>[p(y(J.value?"收起全部":"展开全部"),1)]),_:1})])])]),default:s(()=>[b.value==="multiple"?(h(),G("div",_a,[e(u,{size:"small",onClick:H},{default:s(()=>t[16]||(t[16]=[p("全选",-1)])),_:1,__:[16]}),e(u,{size:"small",onClick:z},{default:s(()=>t[17]||(t[17]=[p("全不选",-1)])),_:1,__:[17]}),e(u,{size:"small",onClick:ie},{default:s(()=>t[18]||(t[18]=[p("反选",-1)])),_:1,__:[18]}),o("span",va,[p(" 已选择 "+y(r.value.length)+" 个组织 ",1),Pe.value>0?(h(),G("span",ma,"，共 "+y(Pe.value)+" 人",1)):W("",!0)])])):W("",!0),(h(),O(We,{ref_key:"orgTreeRef",ref:v,data:ye.value,props:he,"node-key":"id","default-expanded-keys":_e.value,"expand-on-click-node":!1,onNodeClick:Fe,"highlight-current":b.value==="single","show-checkbox":b.value==="multiple","check-strictly":!0,onCheck:He,key:C.value,class:"org-tree","empty-text":"暂无组织架构数据"},{default:s(({node:E,data:n})=>[o("div",{class:yt(["tree-node",{"is-selected":Re(n),"is-multi-selected":xe(n)}])},[o("div",fa,[e(l,{class:"node-icon"},{default:s(()=>[n.type==="organization"?(h(),O($(us),{key:0})):(h(),O($(ht),{key:1}))]),_:2},1024),o("span",ga,[p(y(n.name)+" ",1),n.type==="organization"?(h(),G("span",ya," ("+y($(x)(n.level))+") ",1)):W("",!0),n.type==="organization"?(h(),G("span",ha," - "+y(n.userCount||0)+"人 ",1)):(h(),G("span",ba," - "+y(n.role_name),1))])])],2)]),_:1},8,["data","default-expanded-keys","highlight-current","show-checkbox"]))]),_:1})]),o("div",$a,[e(Ke,null,{header:s(()=>t[19]||(t[19]=[o("div",{class:"card-header"},[o("span",null,"设备权限分配")],-1)])),default:s(()=>[o("div",ka,[e(be,{modelValue:k.value,"onUpdate:modelValue":t[6]||(t[6]=E=>k.value=E),onTabChange:re},{default:s(()=>[e(Ye,{label:"分组",name:"groups"},{default:s(()=>[o("div",wa,[o("div",Ca,[e(q,{modelValue:pe.value,"onUpdate:modelValue":t[1]||(t[1]=E=>pe.value=E),placeholder:"搜索设备分组...","prefix-icon":$(Xe),clearable:"",onInput:c},null,8,["modelValue","prefix-icon"])]),Je((h(),G("div",Ta,[(h(!0),G(Ge,null,et(Se.value,E=>(h(),G("div",{key:E.id,class:yt(["device-item",{"is-selected":S.value.includes(E.id)}]),onClick:n=>m(E.id)},[e($e,{"model-value":S.value.includes(E.id),onChange:n=>m(E.id)},null,8,["model-value","onChange"]),o("div",Va,[o("div",xa,y(E.name),1),o("div",Da,y(E.description||"无描述"),1),o("div",za,[e(A,{type:Me(E.group_type),size:"small"},{default:s(()=>[p(y(Ve(E.group_type)),1)]),_:2},1032,["type"]),o("span",Aa,y(E.device_count||0)+"个设备",1)])])],10,Sa))),128))])),[[it,L.value]])])]),_:1}),e(Ye,{label:"设备",name:"devices"},{default:s(()=>[o("div",Ea,[o("div",La,[e(at,{gutter:12},{default:s(()=>[e(je,{span:8},{default:s(()=>[e(q,{modelValue:Z.name,"onUpdate:modelValue":t[2]||(t[2]=E=>Z.name=E),placeholder:"设备名称搜索",clearable:"",onInput:Y},{prefix:s(()=>[e(l,null,{default:s(()=>[e($(Xe))]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(je,{span:5},{default:s(()=>[e(ze,{modelValue:Z.type,"onUpdate:modelValue":t[3]||(t[3]=E=>Z.type=E),placeholder:"设备类型",clearable:"",onChange:Y},{default:s(()=>[e(Oe,{label:"全部类型",value:""}),e(Oe,{label:"CA锁",value:"ca_lock"}),e(Oe,{label:"加密锁",value:"encryption_key"}),e(Oe,{label:"银行U盾",value:"bank_ukey"}),e(Oe,{label:"其他设备",value:"other"})]),_:1},8,["modelValue"])]),_:1}),e(je,{span:6},{default:s(()=>[e(ze,{modelValue:Z.server,"onUpdate:modelValue":t[4]||(t[4]=E=>Z.server=E),placeholder:"所属服务器",clearable:"",onChange:Y},{default:s(()=>[e(Oe,{label:"全部服务器",value:""}),(h(!0),G(Ge,null,et(te.value,E=>(h(),O(Oe,{key:E.id,label:E.name,value:E.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(je,{span:5},{default:s(()=>[e(u,{type:"primary",onClick:Y},{default:s(()=>[e(l,null,{default:s(()=>[e($(Xe))]),_:1}),t[20]||(t[20]=p(" 搜索 ",-1))]),_:1,__:[20]})]),_:1})]),_:1})]),o("div",Pa,[e(q,{modelValue:ge.value,"onUpdate:modelValue":t[5]||(t[5]=E=>ge.value=E),placeholder:"搜索USB设备...","prefix-icon":$(Xe),clearable:"",onInput:ne},null,8,["modelValue","prefix-icon"])]),Je((h(),G("div",Ua,[(h(!0),G(Ge,null,et(ae.value,E=>(h(),G("div",{key:E.id,class:yt(["device-item",{"is-selected":S.value.includes(E.id)}]),onClick:n=>m(E.id)},[e($e,{"model-value":S.value.includes(E.id),onChange:n=>m(E.id)},null,8,["model-value","onChange"]),o("div",ja,[o("div",Oa,y(E.name||E.device_name),1),o("div",Ma,y(E.description||"无描述"),1),o("div",Ba,[e(A,{type:Ne(E.status),size:"small"},{default:s(()=>[p(y(E.status),1)]),_:2},1032,["type"]),o("span",Ga,"VID:"+y(E.vid)+" PID:"+y(E.pid),1)])])],10,Ia))),128))])),[[it,K.value]])])]),_:1})]),_:1},8,["modelValue"])])]),_:1})])]),e(nt,{modelValue:D.value,"onUpdate:modelValue":t[8]||(t[8]=E=>D.value=E),title:"确认权限分配",width:"500px",onClose:Be},{footer:s(()=>[e(u,{onClick:t[7]||(t[7]=E=>D.value=!1)},{default:s(()=>t[25]||(t[25]=[p("取消",-1)])),_:1,__:[25]}),e(u,{type:"primary",onClick:ke,loading:B.value},{default:s(()=>t[26]||(t[26]=[p(" 确认分配 ",-1)])),_:1,__:[26]},8,["loading"])]),default:s(()=>{var E;return[o("div",Na,[o("p",null,[t[21]||(t[21]=o("strong",null,"分配对象：",-1)),p(y((E=$(I))==null?void 0:E.name),1)]),o("p",null,[t[22]||(t[22]=o("strong",null,"分配类型：",-1)),p(y(k.value==="groups"?"设备分组":"独立设备"),1)]),o("p",null,[t[23]||(t[23]=o("strong",null,"选中数量：",-1)),p(y(S.value.length)+"个",1)]),t[24]||(t[24]=o("p",null,[o("strong",null,"权限类型："),p("使用权限")],-1))])]}),_:1},8,["modelValue"])])}}},Fa=st(Ra,[["__scopeId","data-v-a7ecdd2f"]]),Ha={class:"device-center"},qa={class:"statistics-panel"},Ka={class:"stat-content"},Ja={class:"stat-icon server-icon"},Wa={class:"stat-info"},Ya={class:"stat-value"},Qa={class:"stat-detail"},Za={class:"stat-content"},Xa={class:"stat-icon device-icon"},en={class:"stat-info"},tn={class:"stat-value"},sn={class:"stat-detail"},ln={class:"stat-content"},on={class:"stat-icon group-icon"},an={class:"stat-info"},nn={class:"stat-value"},rn={class:"stat-detail"},dn={class:"stat-content"},un={class:"stat-icon permission-icon"},cn={class:"stat-info"},pn={class:"stat-value"},_n={class:"stat-detail"},vn={class:"toolbar"},mn={class:"toolbar-right"},fn={class:"tab-label"},gn={key:1,class:"no-permission"},yn={__name:"index",setup(ue){const{loading:N,statistics:P,visibleTabs:se,refreshAllData:Q}=hs(),j=Ze(),M=_("");return Qe(()=>{console.log("DeviceCenter 组件已挂载"),se.value.length>0&&(M.value=se.value[0].name)}),(I,U)=>{const x=lt,F=zt,B=Ot,L=jt,K=ot,J=Ut,D=It,b=ms;return h(),G("div",Ha,[o("div",qa,[e(L,{gutter:20},{default:s(()=>[$(j).canViewSlaveServerStats?(h(),O(B,{key:0,span:6},{default:s(()=>[e(F,{class:"stat-card"},{default:s(()=>[o("div",Ka,[o("div",Ja,[e(x,null,{default:s(()=>[e($(pt))]),_:1})]),o("div",Wa,[U[2]||(U[2]=o("div",{class:"stat-title"},"分布式节点",-1)),o("div",Ya,y($(P).slaveServers.total),1),o("div",Qa," 在线: "+y($(P).slaveServers.online)+" | 离线: "+y($(P).slaveServers.offline),1)])])]),_:1})]),_:1})):W("",!0),$(j).canViewUSBDeviceStats?(h(),O(B,{key:1,span:6},{default:s(()=>[e(F,{class:"stat-card"},{default:s(()=>[o("div",Za,[o("div",Xa,[e(x,null,{default:s(()=>[e($(pt))]),_:1})]),o("div",en,[U[3]||(U[3]=o("div",{class:"stat-title"},"USB设备管理中心",-1)),o("div",tn,y($(P).devices.total),1),o("div",sn," 可用: "+y($(P).devices.available)+" | 已连接: "+y($(P).devices.connected),1)])])]),_:1})]),_:1})):W("",!0),$(j).canViewDeviceGroupStats?(h(),O(B,{key:2,span:6},{default:s(()=>[e(F,{class:"stat-card"},{default:s(()=>[o("div",ln,[o("div",on,[e(x,null,{default:s(()=>[e($(Dt))]),_:1})]),o("div",an,[U[4]||(U[4]=o("div",{class:"stat-title"},"资源调度分组",-1)),o("div",nn,y($(P).groups.total),1),o("div",rn," 有设备: "+y($(P).groups.withDevices),1)])])]),_:1})]),_:1})):W("",!0),$(j).canViewPermissionAssignmentStats?(h(),O(B,{key:3,span:6},{default:s(()=>[e(F,{class:"stat-card"},{default:s(()=>[o("div",dn,[o("div",un,[e(x,null,{default:s(()=>[e($(_s))]),_:1})]),o("div",cn,[U[5]||(U[5]=o("div",{class:"stat-title"},"授权范围管理",-1)),o("div",pn,y($(P).permissions.totalAssignments),1),o("div",_n," 活跃用户: "+y($(P).permissions.activeUsers),1)])])]),_:1})]),_:1})):W("",!0)]),_:1})]),o("div",vn,[U[7]||(U[7]=o("div",{class:"toolbar-left"},[o("h2",null,"USB设备管理中心")],-1)),o("div",mn,[e(K,{type:"primary",onClick:$(Q),loading:$(N)},{default:s(()=>[e(x,null,{default:s(()=>[e($(tt))]),_:1}),U[6]||(U[6]=p(" 刷新数据 ",-1))]),_:1,__:[6]},8,["onClick","loading"])])]),e(F,{class:"main-content"},{default:s(()=>[$(se).length>0?(h(),O(D,{key:0,modelValue:M.value,"onUpdate:modelValue":U[0]||(U[0]=r=>M.value=r),type:"border-card"},{default:s(()=>[(h(!0),G(Ge,null,et($(se),r=>(h(),O(J,{key:r.name,name:r.name,label:r.label},{label:s(()=>[o("span",fn,[e(x,null,{default:s(()=>[(h(),O(vs(r.icon)))]),_:2},1024),p(" "+y(r.label),1)])]),default:s(()=>[r.name==="devices"?(h(),O(Ol,{key:0})):W("",!0),r.name==="slaves"?(h(),O(to,{key:1})):W("",!0),r.name==="groups"?(h(),O(Wo,{key:2})):W("",!0),r.name==="permissions"?(h(),O(Fa,{key:3})):W("",!0)]),_:2},1032,["name","label"]))),128))]),_:1},8,["modelValue"])):(h(),G("div",gn,[e(b,{description:"您暂无权限访问设备管理功能"},{default:s(()=>[e(K,{type:"primary",onClick:U[1]||(U[1]=r=>I.$router.push("/dashboard"))},{default:s(()=>U[8]||(U[8]=[p(" 返回工作台 ",-1)])),_:1,__:[8]})]),_:1})]))]),_:1})])}}},Qn=st(yn,[["__scopeId","data-v-9f168561"]]);export{Qn as default};
