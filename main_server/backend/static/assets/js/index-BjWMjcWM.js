const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/websocket-DHEyfvkN.js","assets/js/index-DZHxQJ7O.js","assets/css/index-I0ivxmt2.css"])))=>i.map(i=>d[i]);
import{u as Z,r as V,aM as ee,a as te,o as se,c as P,d as r,e as t,y as E,z as T,w as s,m as ae,p as c,cJ as le,ah as oe,x as re,s as m,d7 as ne,b as ie,i as f,n as v,E as de,cY as ue,cQ as ce,cK as _e,t as _,L as $,d3 as pe,C as me,a8 as ve,ab as fe,ae as ge,a6 as be,d6 as ye,a9 as we,f as Ve,j as he,k as ke,cP as xe,a3 as Ee}from"./index-DZHxQJ7O.js";import{_ as Ie}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css                 *//* empty css               *//* empty css               *//* empty css                *//* empty css                  */import{g as U,c as Se,d as Ce,r as Pe}from"./slaveServers-pWVfZGpH.js";const Te={class:"slave-servers-container"},$e={class:"toolbar"},De={class:"stats-cards"},Be={class:"stat-content"},Me={class:"stat-number"},Ue={class:"stat-content"},Le={class:"stat-number"},Re={class:"stat-content"},Ae={class:"stat-number"},ze={class:"stat-content"},Fe={class:"stat-number"},Ne={key:0},qe={key:1,class:"text-muted"},He={__name:"index",setup(We){const L=ie(),I=Z(),h=V(!1),S=V(!1),y=V(!1),p=V([]),k=ee(()=>{const l=p.value.length,e=p.value.filter(n=>n.status==="online").length,o=l-e,d=p.value.reduce((n,g)=>n+(g.device_count||0),0);return{total:l,online:e,offline:o,totalDevices:d}}),C=V(),i=te({server_name:"",server_ip:"",server_port:8889,vh_port:7575,description:"",location:""}),R={server_name:[{required:!0,message:"请输入服务器名称",trigger:"blur"}],server_ip:[{required:!0,message:"请输入IP地址",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"IP地址格式不正确",trigger:"blur"}],server_port:[{required:!0,message:"请输入API端口",trigger:"blur"}],vh_port:[{required:!0,message:"请输入VH端口",trigger:"blur"}]},w=async()=>{h.value=!0;try{const l=await U();p.value=l.data||[],A()}catch(l){m.error("获取从服务器列表失败"),console.error("获取从服务器列表失败:",l)}finally{h.value=!1}},A=()=>{try{ne(async()=>{const{WebSocketManager:l}=await import("./websocket-DHEyfvkN.js");return{WebSocketManager:l}},__vite__mapDeps([0,1,2])).then(({WebSocketManager:l})=>{const e=new l;e.subscribe("slave_status"),e.on("slave_status_change",o=>{console.log("从服务器状态变化:",o);const d=p.value.findIndex(n=>n.id===o.server_id);if(d!==-1){p.value[d].status=o.new_status;const n=o.new_status==="online"?"上线":"离线";m({message:`从服务器 ${o.server_name} 已${n}`,type:o.new_status==="online"?"success":"warning",duration:3e3})}}),e.connect()}).catch(l=>{console.warn("WebSocket模块加载失败，使用轮询模式:",l),D()})}catch(l){console.warn("WebSocket初始化失败，使用轮询模式:",l),D()}},D=()=>{setInterval(async()=>{try{const e=(await U()).data||[];e.forEach(o=>{const d=p.value.find(n=>n.id===o.id);if(d&&d.status!==o.status){const n=o.status==="online"?"上线":"离线";m({message:`从服务器 ${o.name} 已${n}`,type:o.status==="online"?"success":"warning",duration:3e3})}}),p.value=e}catch(l){console.error("轮询更新失败:",l)}},3e4)},z=l=>{switch(l){case"online":return"success";case"offline":return"danger";default:return"warning"}},F=l=>{switch(l){case"online":return"在线";case"offline":return"离线";default:return"未知"}},N=l=>new Date(l).toLocaleString(),q=l=>{L.push(`/slave-servers/${l.id}`)},H=async(l,e)=>{try{await Se(l.id,e),m.success(`${e==="restart"?"重启":"操作"}命令已发送`),w()}catch(o){m.error("操作失败"),console.error("控制从服务器失败:",o)}},W=async l=>{try{await Ee.confirm(`确定要删除从服务器 "${l.name}" 吗？此操作将同时删除该服务器上的所有设备记录。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}),await Ce(l.id),m.success("从服务器删除成功"),w()}catch(e){e!=="cancel"&&(m.error("删除失败"),console.error("删除从服务器失败:",e))}},j=async()=>{if(!(!C.value||!await C.value.validate().catch(()=>!1))){S.value=!0;try{await Pe(i),m.success("添加成功"),y.value=!1,w(),Object.assign(i,{server_name:"",server_ip:"",server_port:8889,vh_port:7575,description:"",location:""})}catch(e){m.error("添加失败"),console.error("添加从服务器失败:",e)}finally{S.value=!1}}};return se(()=>{w()}),(l,e)=>{const o=de,d=ae,n=oe,g=_e,O=le,u=ge,J=be,K=ye,Q=fe,x=ke,b=he,B=xe,Y=Ve,G=re,X=we;return f(),P("div",Te,[e[20]||(e[20]=r("div",{class:"page-header"},[r("h2",null,"从服务器管理"),r("p",null,"管理和监控所有从服务器的状态和设备")],-1)),r("div",$e,[t(d,{type:"primary",onClick:w,loading:h.value},{default:s(()=>[t(o,null,{default:s(()=>[t(c(ue))]),_:1}),e[9]||(e[9]=v(" 刷新列表 ",-1))]),_:1,__:[9]},8,["loading"]),c(I).hasPermission("slave.create")?(f(),E(d,{key:0,type:"success",onClick:e[0]||(e[0]=a=>y.value=!0)},{default:s(()=>[t(o,null,{default:s(()=>[t(c(ce))]),_:1}),e[10]||(e[10]=v(" 添加从服务器 ",-1))]),_:1,__:[10]})):T("",!0)]),r("div",De,[t(O,{gutter:20},{default:s(()=>[t(g,{span:6},{default:s(()=>[t(n,{class:"stat-card"},{default:s(()=>[r("div",Be,[r("div",Me,_(k.value.total),1),e[11]||(e[11]=r("div",{class:"stat-label"},"总数",-1))]),t(o,{class:"stat-icon"},{default:s(()=>[t(c($))]),_:1})]),_:1})]),_:1}),t(g,{span:6},{default:s(()=>[t(n,{class:"stat-card online"},{default:s(()=>[r("div",Ue,[r("div",Le,_(k.value.online),1),e[12]||(e[12]=r("div",{class:"stat-label"},"在线",-1))]),t(o,{class:"stat-icon"},{default:s(()=>[t(c(pe))]),_:1})]),_:1})]),_:1}),t(g,{span:6},{default:s(()=>[t(n,{class:"stat-card offline"},{default:s(()=>[r("div",Re,[r("div",Ae,_(k.value.offline),1),e[13]||(e[13]=r("div",{class:"stat-label"},"离线",-1))]),t(o,{class:"stat-icon"},{default:s(()=>[t(c(me))]),_:1})]),_:1})]),_:1}),t(g,{span:6},{default:s(()=>[t(n,{class:"stat-card devices"},{default:s(()=>[r("div",ze,[r("div",Fe,_(k.value.totalDevices),1),e[14]||(e[14]=r("div",{class:"stat-label"},"设备总数",-1))]),t(o,{class:"stat-icon"},{default:s(()=>[t(c($))]),_:1})]),_:1})]),_:1})]),_:1})]),t(n,{class:"list-card"},{default:s(()=>[ve((f(),E(Q,{data:p.value,stripe:"",style:{width:"100%"}},{default:s(()=>[t(u,{prop:"id",label:"ID",width:"80"}),t(u,{prop:"server_id",label:"服务器ID",width:"150"}),t(u,{label:"名称",width:"200"},{default:s(({row:a})=>[r("span",null,_(a.name||"未设置名称"),1)]),_:1}),t(u,{prop:"ip_address",label:"IP地址",width:"120"}),t(u,{prop:"port",label:"API端口",width:"80"}),t(u,{label:"VH端口",width:"80"},{default:s(({row:a})=>[r("span",null,_(a.vh_port||"7575"),1)]),_:1}),t(u,{label:"状态",width:"100"},{default:s(({row:a})=>[t(J,{type:z(a.status),size:"small"},{default:s(()=>[v(_(F(a.status)),1)]),_:2},1032,["type"])]),_:1}),t(u,{label:"最后心跳",width:"160"},{default:s(({row:a})=>[a.last_seen?(f(),P("span",Ne,_(N(a.last_seen)),1)):(f(),P("span",qe,"从未连接"))]),_:1}),t(u,{label:"设备数量",width:"100"},{default:s(({row:a})=>[t(K,{value:a.device_count||0,max:99},{default:s(()=>[t(o,null,{default:s(()=>[t(c($))]),_:1})]),_:2},1032,["value"])]),_:1}),t(u,{label:"位置",width:"120"},{default:s(({row:a})=>[r("span",null,_(a.location||"未设置"),1)]),_:1}),t(u,{prop:"description",label:"描述","min-width":"150"},{default:s(({row:a})=>[r("span",null,_(a.description||"无描述"),1)]),_:1}),t(u,{label:"操作",width:"200",fixed:"right"},{default:s(({row:a})=>[t(d,{type:"primary",size:"small",onClick:M=>q(a)},{default:s(()=>e[15]||(e[15]=[v(" 详情 ",-1)])),_:2,__:[15]},1032,["onClick"]),c(I).hasPermission("slave.control")?(f(),E(d,{key:0,type:"warning",size:"small",onClick:M=>H(a,"restart")},{default:s(()=>e[16]||(e[16]=[v(" 重启 ",-1)])),_:2,__:[16]},1032,["onClick"])):T("",!0),c(I).hasPermission("slave.delete")?(f(),E(d,{key:1,type:"danger",size:"small",onClick:M=>W(a)},{default:s(()=>e[17]||(e[17]=[v(" 删除 ",-1)])),_:2,__:[17]},1032,["onClick"])):T("",!0)]),_:1})]),_:1},8,["data"])),[[X,h.value]])]),_:1}),t(G,{modelValue:y.value,"onUpdate:modelValue":e[8]||(e[8]=a=>y.value=a),title:"添加从服务器",width:"500px","close-on-click-modal":!1},{footer:s(()=>[t(d,{onClick:e[7]||(e[7]=a=>y.value=!1)},{default:s(()=>e[18]||(e[18]=[v("取消",-1)])),_:1,__:[18]}),t(d,{type:"primary",onClick:j,loading:S.value},{default:s(()=>e[19]||(e[19]=[v(" 确定 ",-1)])),_:1,__:[19]},8,["loading"])]),default:s(()=>[t(Y,{ref_key:"addFormRef",ref:C,model:i,rules:R,"label-width":"100px"},{default:s(()=>[t(b,{label:"服务器名称",prop:"server_name"},{default:s(()=>[t(x,{modelValue:i.server_name,"onUpdate:modelValue":e[1]||(e[1]=a=>i.server_name=a),placeholder:"请输入服务器名称"},null,8,["modelValue"])]),_:1}),t(b,{label:"IP地址",prop:"server_ip"},{default:s(()=>[t(x,{modelValue:i.server_ip,"onUpdate:modelValue":e[2]||(e[2]=a=>i.server_ip=a),placeholder:"请输入IP地址"},null,8,["modelValue"])]),_:1}),t(b,{label:"API端口",prop:"server_port"},{default:s(()=>[t(B,{modelValue:i.server_port,"onUpdate:modelValue":e[3]||(e[3]=a=>i.server_port=a),min:1,max:65535},null,8,["modelValue"])]),_:1}),t(b,{label:"VH端口",prop:"vh_port"},{default:s(()=>[t(B,{modelValue:i.vh_port,"onUpdate:modelValue":e[4]||(e[4]=a=>i.vh_port=a),min:1,max:65535},null,8,["modelValue"])]),_:1}),t(b,{label:"描述",prop:"description"},{default:s(()=>[t(x,{modelValue:i.description,"onUpdate:modelValue":e[5]||(e[5]=a=>i.description=a),type:"textarea",placeholder:"请输入描述信息",rows:3},null,8,["modelValue"])]),_:1}),t(b,{label:"位置",prop:"location"},{default:s(()=>[t(x,{modelValue:i.location,"onUpdate:modelValue":e[6]||(e[6]=a=>i.location=a),placeholder:"请输入服务器位置"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},it=Ie(He,[["__scopeId","data-v-5aac3fbe"]]);export{it as default};
