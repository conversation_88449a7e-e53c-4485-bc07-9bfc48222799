import{_ as Ct}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                    *//* empty css                    *//* empty css               *//* empty css                       *//* empty css                 *//* empty css                   *//* empty css                    *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                 *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                  *//* empty css                     */import{r as y,aM as Tt,o as St,c as z,i as b,e as s,w as l,d as a,cJ as kt,cK as Vt,dz as ta,dA as sa,ak as aa,y as B,z as U,al as la,n as m,t as c,dB as na,a6 as xt,f as $t,j as Dt,k as zt,m as Ut,ai as Mt,E as Bt,p as K,cY as Rt,ah as Et,am as I,s as u,a3 as X,a as A,x as oa,cV as ra,cW as ia,ab as da,ae as ua,cL as ca,cT as gt,cQ as wt,a4 as Ae,a5 as Pe,cU as ma,dC as pa,cP as _a,ac as fa,ad as va,bA as ya,q as ba,a8 as ht,B as ga,a9 as wa,bY as ha,dD as Ca,af as Ta,ag as Sa,cN as ka,dt as Va,du as xa,Y as $a,d3 as Da,dE as za,dw as Ua,L as Ma,$ as Ba,dn as Ra}from"./index-DlnWH8GN.js";/* empty css                             */import{a as Ea,b as Ia,u as Oa,c as Le,s as Aa,d as Pa,e as La,f as Fa}from"./systemSettings-BRvASBFN.js";const Na={class:"usb-database-manager"},ja={class:"card-header"},Wa={class:"database-info"},Ja={class:"device-lookup"},Ha={key:0,class:"device-info"},qa={__name:"USBDatabaseManager",setup(W){const M=y({}),le=y(!1),fe=y(!1),J=y(null),N=y({vendor_id:"",product_id:""}),Fe=Tt(()=>!0),Ve=async()=>{try{const C=await I.get("/api/v1/usb-database/stats");M.value=C.data}catch(C){console.error("加载USB数据库统计信息失败:",C),u.error("加载数据库信息失败")}},Ne=async()=>{try{await X.confirm("更新USB数据库将从网络下载最新的usb.ids文件，这可能需要几分钟时间。是否继续？","确认更新",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),le.value=!0;const C=await I.post("/api/v1/usb-database/update");C.data.success?(u.success("USB数据库更新任务已启动，请稍后刷新查看结果"),setTimeout(()=>{Ve()},5e3)):u.error(C.data.message||"更新失败")}catch(C){C!=="cancel"&&(console.error("更新USB数据库失败:",C),u.error("更新数据库失败"))}finally{le.value=!1}},je=async()=>{try{fe.value=!0,J.value=null;const C=await I.get(`/api/v1/usb-database/device-info/${N.value.vendor_id}/${N.value.product_id}`);J.value=C.data}catch(C){console.error("查询设备信息失败:",C),u.error("查询设备信息失败")}finally{fe.value=!1}},ve=C=>{if(!C)return"未知";try{return new Date(C).toLocaleString("zh-CN")}catch{return C}},ee=C=>{if(!C)return"0 B";const g=["B","KB","MB","GB"],H=Math.floor(Math.log(C)/Math.log(1024));return Math.round(C/Math.pow(1024,H)*100)/100+" "+g[H]};return St(()=>{Ve()}),(C,g)=>{const H=Bt,ne=Ut,te=ta,xe=Vt,oe=kt,V=sa,q=la,re=na,$e=xt,d=aa,i=zt,se=Dt,ye=$t,h=Mt,D=Et;return b(),z("div",Na,[s(D,{class:"box-card"},{header:l(()=>[a("div",ja,[g[3]||(g[3]=a("span",null,"USB数据库管理",-1)),s(ne,{type:"primary",loading:le.value,onClick:Ne,disabled:!Fe.value},{default:l(()=>[s(H,null,{default:l(()=>[s(K(Rt))]),_:1}),g[2]||(g[2]=m(" 更新USB.IDS数据库 ",-1))]),_:1,__:[2]},8,["loading","disabled"])])]),default:l(()=>[a("div",Wa,[s(oe,{gutter:20},{default:l(()=>[s(xe,{span:12},{default:l(()=>[s(te,{title:"厂商数量",value:M.value.vendor_count||0},null,8,["value"])]),_:1}),s(xe,{span:12},{default:l(()=>[s(te,{title:"设备数量",value:M.value.device_count||0},null,8,["value"])]),_:1})]),_:1}),s(V),s(d,{column:2,border:""},{default:l(()=>{var Y,P;return[s(q,{label:"数据库版本"},{default:l(()=>[m(c(M.value.version||"未知"),1)]),_:1}),s(q,{label:"最后更新"},{default:l(()=>[m(c(ve(M.value.last_updated)),1)]),_:1}),s(q,{label:"数据来源"},{default:l(()=>[s(re,{href:M.value.source_url,target:"_blank",type:"primary"},{default:l(()=>[m(c(M.value.source_url),1)]),_:1},8,["href"])]),_:1}),s(q,{label:"缓存状态"},{default:l(()=>[s($e,{type:M.value.cache_loaded?"success":"warning"},{default:l(()=>[m(c(M.value.cache_loaded?"已加载":"未加载"),1)]),_:1},8,["type"])]),_:1}),(Y=M.value.file_info)!=null&&Y.file_exists?(b(),B(q,{key:0,label:"文件大小"},{default:l(()=>[m(c(ee(M.value.file_info.file_size)),1)]),_:1})):U("",!0),(P=M.value.file_info)!=null&&P.file_exists?(b(),B(q,{key:1,label:"文件修改时间"},{default:l(()=>[m(c(ve(M.value.file_info.file_modified)),1)]),_:1})):U("",!0)]}),_:1})]),s(V),a("div",Ja,[g[9]||(g[9]=a("h4",null,"设备信息查询",-1)),s(ye,{model:N.value,inline:""},{default:l(()=>[s(se,{label:"厂商ID (VID)"},{default:l(()=>[s(i,{modelValue:N.value.vendor_id,"onUpdate:modelValue":g[0]||(g[0]=Y=>N.value.vendor_id=Y),placeholder:"例如: 1BC0 或 0x1BC0",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),s(se,{label:"产品ID (PID)"},{default:l(()=>[s(i,{modelValue:N.value.product_id,"onUpdate:modelValue":g[1]||(g[1]=Y=>N.value.product_id=Y),placeholder:"例如: 0055 或 0x0055",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),s(se,null,{default:l(()=>[s(ne,{type:"primary",onClick:je,loading:fe.value,disabled:!N.value.vendor_id||!N.value.product_id},{default:l(()=>g[4]||(g[4]=[m(" 查询 ",-1)])),_:1,__:[4]},8,["loading","disabled"])]),_:1})]),_:1},8,["model"]),J.value?(b(),z("div",Ha,[s(h,{title:`设备信息: ${J.value.full_name||"未知设备"}`,type:"info",closable:!1},{default:l(()=>[a("p",null,[g[5]||(g[5]=a("strong",null,"厂商:",-1)),m(" "+c(J.value.vendor_name||"未知"),1)]),a("p",null,[g[6]||(g[6]=a("strong",null,"设备:",-1)),m(" "+c(J.value.device_name||"未知"),1)]),a("p",null,[g[7]||(g[7]=a("strong",null,"VID:",-1)),m(" "+c(J.value.vendor_id),1)]),a("p",null,[g[8]||(g[8]=a("strong",null,"PID:",-1)),m(" "+c(J.value.product_id),1)])]),_:1},8,["title"])])):U("",!0)])]),_:1})])}}},Ga=Ct(qa,[["__scopeId","data-v-140c62e8"]]);function Za(){return I({url:"/api/v1/system/cache-info",method:"get"})}function It(W){return I({url:"/api/v1/system/clear-cache",method:"post",params:{cache_type:W}})}function Ka(){return It("all")}function Ya(){try{return localStorage.clear(),sessionStorage.clear(),"caches"in window&&caches.keys().then(W=>{W.forEach(M=>{caches.delete(M)})}),Promise.resolve({success:!0,message:"Web页面缓存清除成功"})}catch(W){return Promise.reject({success:!1,message:`Web页面缓存清除失败: ${W.message}`})}}function Qa(){const W=new URL(window.location);W.searchParams.set("_refresh",Date.now()),window.location.href=W.toString()}const Xa={class:"system-settings-container"},el={class:"settings-section"},tl={class:"settings-section"},sl={class:"standard-container"},al={class:"card-header"},ll={class:"tunnel-action-buttons"},nl={class:"standard-container"},ol={class:"card-header"},rl={class:"connection-status-grid"},il={class:"connection-header"},dl={class:"connection-details"},ul={class:"detail-item"},cl={class:"value"},ml={class:"detail-item"},pl={class:"value"},_l={class:"detail-item"},fl={class:"value"},vl={class:"connection-actions"},yl={class:"card-header"},bl={class:"test-controls"},gl={class:"test-suite"},wl={class:"test-section"},hl={class:"test-header"},Cl={class:"test-results"},Tl={class:"metric-grid"},Sl={class:"metric-item"},kl={class:"metric-value"},Vl={class:"metric-item"},xl={class:"metric-value"},$l={class:"metric-item"},Dl={class:"metric-value"},zl={class:"metric-item"},Ul={class:"metric-value"},Ml={key:0,class:"test-progress"},Bl={class:"progress-text"},Rl={class:"test-section"},El={class:"test-header"},Il={class:"test-results"},Ol={class:"metric-grid"},Al={class:"metric-item"},Pl={class:"metric-value"},Ll={class:"metric-item"},Fl={class:"metric-value"},Nl={class:"metric-item"},jl={class:"metric-value"},Wl={class:"metric-item"},Jl={class:"metric-value"},Hl={key:0,class:"test-progress"},ql={class:"progress-text"},Gl={class:"test-section"},Zl={class:"test-header"},Kl={class:"concurrent-controls"},Yl={class:"test-results"},Ql={class:"metric-grid"},Xl={class:"metric-item"},en={class:"metric-value"},tn={class:"metric-item"},sn={class:"metric-value"},an={class:"metric-item"},ln={class:"metric-value"},nn={class:"metric-item"},on={class:"metric-value"},rn={key:0,class:"test-progress"},dn={class:"progress-text"},un={class:"test-section"},cn={class:"test-header"},mn={class:"stability-controls"},pn={class:"test-results"},_n={class:"metric-grid"},fn={class:"metric-item"},vn={class:"metric-value"},yn={class:"metric-item"},bn={class:"metric-value"},gn={class:"metric-item"},wn={class:"metric-value"},hn={class:"metric-item"},Cn={class:"metric-value"},Tn={key:0,class:"test-progress"},Sn={class:"progress-text"},kn={class:"test-section"},Vn={class:"test-header"},xn={class:"compression-controls"},$n={class:"compression-config"},Dn={class:"config-row"},zn={class:"config-item"},Un={class:"config-item"},Mn={class:"config-item"},Bn={class:"config-item"},Rn={class:"config-row"},En={class:"config-item"},In={key:0,class:"config-item"},On={key:1,class:"config-item"},An={class:"test-results"},Pn={class:"metric-grid"},Ln={class:"metric-item"},Fn={class:"metric-value"},Nn={class:"metric-item"},jn={class:"metric-value"},Wn={class:"metric-item"},Jn={class:"metric-value"},Hn={class:"metric-item"},qn={class:"metric-value"},Gn={class:"metric-item"},Zn={class:"metric-value"},Kn={class:"metric-item"},Yn={class:"metric-value"},Qn={class:"metric-item"},Xn={class:"metric-value"},eo={class:"metric-item"},to={class:"metric-value"},so={key:0,class:"test-progress"},ao={class:"progress-text"},lo={key:1,class:"compression-charts"},no={class:"chart-container"},oo={class:"chart-placeholder"},ro={class:"chart-label"},io={class:"chart-value"},uo={class:"card-header"},co={class:"report-actions"},mo={class:"test-report"},po={class:"report-summary"},_o={class:"summary-grid"},fo={class:"summary-item"},vo={class:"value"},yo={class:"summary-item"},bo={class:"value"},go={class:"summary-item"},wo={class:"value"},ho={class:"report-details"},Co={class:"settings-section"},To={class:"standard-container"},So={class:"card-header"},ko={class:"standard-container"},Vo={class:"cache-info-section"},xo={class:"cache-name"},$o={class:"cache-description"},Do={class:"cache-size"},zo={class:"cache-actions",style:{"margin-top":"20px"}},Uo={class:"algorithm-option"},Mo={class:"algorithm-desc"},Bo={key:0,class:"form-help"},Ro={class:"preview-content"},Eo={class:"preview-item"},Io={class:"preview-value"},Oo={class:"preview-item"},Ao={class:"preview-value"},Po={class:"preview-item"},Lo={class:"preview-value"},Fo={class:"dialog-footer"},No={class:"dialog-footer"},jo={class:"preview-section"},Wo={class:"network-impact"},Jo={class:"impact-item"},Ho={class:"impact-value"},qo={class:"impact-item"},Go={class:"impact-value"},Zo={class:"impact-item"},Ko={class:"impact-value"},Yo={class:"dialog-footer"},Qo={__name:"SystemSettings",setup(W){const M=()=>I.get("/api/v1/compression/configs"),le=t=>I.post("/api/v1/compression/configs",t),fe=(t,e)=>I.put(`/api/v1/compression/configs/${t}`,e),J=t=>I.delete(`/api/v1/compression/configs/${t}`),N=t=>I.post(`/api/v1/compression/configs/${t}/activate`),Fe=t=>I.post(`/api/v1/compression/configs/${t}/deactivate`),Ve=()=>I.get("/compression/status"),Ne=()=>I.get("/compression/algorithms"),je=()=>I.get("/compression/recommendations"),ve=y("general"),ee=y(!1),C=y(!1),g=A({system_name:"",system_version:""}),H=A({client_download_url:""}),ne=y([]),te=y({}),xe=y({}),oe=y([]),V=y(null),q=y(!1),re=y(!1),$e=y(!1),d=A({latency:{running:!1,progress:0,currentStep:""},bandwidth:{running:!1,progress:0,currentStep:""},concurrent:{running:!1,progress:0,currentStep:""},stability:{running:!1,progress:0,currentStep:""},compression:{running:!1,progress:0,currentStep:""}}),i=A({latency:{average:null,min:null,max:null,packetLoss:null},bandwidth:{download:null,upload:null,totalData:null,duration:null},concurrent:{successful:null,failed:null,avgResponseTime:null,successRate:null},stability:{elapsed:null,disconnections:null,avgLatency:null,score:null},compression:{originalSize:null,compressedSize:null,compressionRatio:null,transferSpeed:null,compressionTime:null,decompressionTime:null,totalTransferred:null,bandwidthSaved:null,chartData:null}}),se=A({connections:10}),ye=A({duration:"15"}),h=A({dataSize:"10",concurrency:"10",duration:"30",dataType:"json",enableCompression:!0,algorithm:"snappy",compressionLevel:"medium"}),D=A({startTime:null,endTime:null,overallScore:0,details:[]}),Y=Tt(()=>i.latency.average!==null||i.bandwidth.download!==null||i.concurrent.successful!==null||i.stability.score!==null),P=A({name:"",tunnel_type:"",is_active:!1}),be=y(""),R=A({id:null,name:"",tunnel_type:"",is_active:!1,force_disabled:!1}),ge=y(""),we=y(!1),ie=y(!1),Xe=y(!1),he=y(!1),We=y(!1),De=y(!1),ze=y(!1),Ue=y(!1),Q=y(null),L=y([]),ae=y(null),de=y([]),Me=y(null),Be=y([]),k=A({name:"",algorithm:"",level:"medium",stream_type:"",max_concurrent_users:500,bandwidth_limit_mbps:3,auto_fallback:!0,is_default:!1}),x=A({id:null,name:"",algorithm:"",level:"medium",stream_type:"",max_concurrent_users:500,bandwidth_limit_mbps:3,auto_fallback:!0,is_default:!1}),ue=y(null),Ce=y(null),Ot=y([]),Je=y({bandwidth_saved:0,latency_increase:0,cpu_usage:0}),et={name:[{required:!0,message:"请输入配置名称",trigger:"blur"}],algorithm:[{required:!0,message:"请选择压缩算法",trigger:"change"}],level:[{required:!0,message:"请选择压缩级别",trigger:"change"}]},He=y(),tt=y(),st=y([]),qe=y(!1),G=A({all:!1,web:!1,server_cache:!1,redis_cache:!1,query_cache:!1,compression_cache:!1,tunnel_cache:!1,api_cache:!1}),At=async()=>{try{const t=await Ea();t.success&&t.data.forEach(e=>{e.key==="system_name"?g.system_name=e.value:e.key==="system_version"?g.system_version=e.value:e.key==="client_download_url"&&(H.client_download_url=e.value)})}catch{u.error("加载设置失败")}},Ge=async(t,e)=>{try{await Oa(t,{value:e}),u.success("设置更新成功")}catch{u.error("设置更新失败")}},Te=async()=>{try{const t=await Ia();t.success&&(ne.value=t.data)}catch{u.error("加载内网穿透配置失败")}},Pt=async()=>{try{let t={};be.value&&(t=JSON.parse(be.value)),await Fa({...P,config_data:t}),u.success("内网穿透配置创建成功"),ee.value=!1,Object.assign(P,{name:"",tunnel_type:"",is_active:!1}),be.value="",await Te()}catch{u.error("创建内网穿透配置失败")}},Lt=async t=>{try{await Le(t.id,{is_active:t.is_active}),u.success("配置状态更新成功")}catch{u.error("配置状态更新失败"),t.is_active=!t.is_active}},Ft=async t=>{try{await Aa(t.id),u.success(`${t.tunnel_type} 启动成功`),t.is_running=!0,t.status="running"}catch{u.error("启动内网穿透失败")}},Nt=async t=>{try{await Pa(t.id),u.success(`${t.tunnel_type} 停止成功`),t.is_running=!1,t.status="stopped"}catch{u.error("停止内网穿透失败")}},jt=t=>{Object.assign(R,{id:t.id,name:t.name,tunnel_type:t.tunnel_type,is_active:t.is_active,force_disabled:t.force_disabled||!1}),ge.value=JSON.stringify(t.config_data,null,2),C.value=!0},Wt=async()=>{try{let t={};ge.value&&(t=JSON.parse(ge.value)),await Le(R.id,{name:R.name,config_data:t,is_active:R.is_active,force_disabled:R.force_disabled}),u.success("内网穿透配置更新成功"),C.value=!1,await Te()}catch{u.error("更新内网穿透配置失败")}},Jt=async t=>{try{te.value[t.id]=!0;const e=await at(t);xe.value[t.id]=e,e.success?u.success(`${t.name} 连接测试成功！延迟: ${e.latency}ms`):u.error(`${t.name} 连接测试失败: ${e.error}`)}catch(e){u.error(`连接测试失败: ${e.message}`)}finally{te.value[t.id]=!1}},at=async t=>{const e=Ze(t);try{const o=await Ht(e.host,e.port);let r=null;pt(t.tunnel_type)&&(r=await qt(e.webUrl));const _=await Gt(e);return{success:o.success&&_.success,latency:o.latency,portConnectivity:o,httpResponse:r,dataTransfer:_,timestamp:new Date().toISOString()}}catch(o){return{success:!1,error:o.message,timestamp:new Date().toISOString()}}},Ze=t=>{const e={host:"skfirefly.cn",name:t.name,type:t.tunnel_type};switch(t.tunnel_type.toLowerCase()){case"frp":return{...e,port:28e3,webUrl:"http://skfirefly.cn:28000"};case"linker":return{...e,port:28005,webUrl:null};default:return{...e,port:8080,webUrl:null}}},Ht=async(t,e)=>{const o=Date.now();try{const r=await fetch("/api/v1/system/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({host:t,port:e}),timeout:5e3}),_=Date.now()-o;return r.ok?{success:!0,latency:_,status:"connected"}:{success:!1,latency:_,status:"failed",error:"Connection refused"}}catch(r){return{success:!1,latency:Date.now()-o,status:"timeout",error:r.message}}},qt=async t=>{if(!t)return null;try{const e=await fetch(t,{method:"HEAD",timeout:3e3,mode:"no-cors"});return{success:!0,status:e.status,statusText:e.statusText}}catch(e){return{success:!1,error:e.message}}},Gt=async t=>{try{const e="test-data-"+Date.now(),o=Date.now();await new Promise(_=>setTimeout(_,100));const r=Date.now()-o;return{success:!0,transferTime:r,dataSize:e.length,throughput:(e.length/r*1e3).toFixed(2)+" bytes/s"}}catch(e){return{success:!1,error:e.message}}},lt=async()=>{q.value=!0;try{await Te(),oe.value=ne.value.filter(t=>t.is_active);for(const t of oe.value)await Zt(t);u.success("连接状态刷新完成")}catch{u.error("刷新连接状态失败")}finally{q.value=!1}},Zt=async t=>{try{const e=await at(t);t.service_status=e.success?"运行中":"连接失败",t.last_check=new Date().toISOString(),t.uptime=e.success?"正常运行":"0分钟"}catch{t.service_status="检查失败",t.last_check=new Date().toISOString(),t.uptime="0分钟"}},Kt=t=>{switch(t){case"运行中":return"success";case"连接失败":return"danger";case"检查失败":return"warning";default:return"info"}},Yt=t=>t||"未知",nt=t=>t.service_status==="运行中",Qt=t=>{V.value=t,u.info(`已选择 ${t.name} 进行性能测试`)},Xt=async()=>{if(!V.value){u.warning("请先选择要测试的配置");return}re.value=!0,D.startTime=new Date().toISOString();try{await ot(),await it(),await ut(),ls(),u.success("全面测试完成")}catch{u.error("测试过程中出现错误")}finally{re.value=!1,D.endTime=new Date().toISOString()}},es=()=>{d.latency.running=!1,d.bandwidth.running=!1,d.concurrent.running=!1,d.stability.running=!1,re.value=!1,$e.value=!1,u.info("已停止所有测试")},ot=async()=>{if(V.value){d.latency.running=!0,d.latency.progress=0,d.latency.currentStep="开始延迟测试...";try{const t=[];for(let o=0;o<10;o++){d.latency.currentStep=`第 ${o+1}/10 次 ping 测试`,d.latency.progress=Math.round(o/10*100);const r=await rt(V.value);r.success&&t.push(r.latency),await new Promise(_=>setTimeout(_,500))}t.length>0&&(i.latency.average=Math.round(t.reduce((o,r)=>o+r,0)/t.length),i.latency.min=Math.min(...t),i.latency.max=Math.max(...t),i.latency.packetLoss=Math.round((10-t.length)/10*100)),d.latency.progress=100,d.latency.currentStep="延迟测试完成"}catch{u.error("延迟测试失败")}finally{d.latency.running=!1}}},rt=async t=>{const e=Date.now();try{const o=Ze(t),r=await fetch("/api/v1/system/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({host:o.host,port:o.port}),timeout:3e3}),_=Date.now()-e;return{success:r.ok,latency:_}}catch{return{success:!1,latency:-1}}},it=async()=>{if(V.value){d.bandwidth.running=!0,d.bandwidth.progress=0,d.bandwidth.currentStep="开始带宽测试...";try{const t=Date.now();d.bandwidth.currentStep="测试下载速度...",d.bandwidth.progress=25;const e=await dt("download");d.bandwidth.currentStep="测试上传速度...",d.bandwidth.progress=75;const o=await dt("upload"),r=Math.round((Date.now()-t)/1e3);i.bandwidth.download=e.toFixed(2),i.bandwidth.upload=o.toFixed(2),i.bandwidth.totalData=((e+o)*r/8).toFixed(2),i.bandwidth.duration=r,d.bandwidth.progress=100,d.bandwidth.currentStep="带宽测试完成"}catch{u.error("带宽测试失败")}finally{d.bandwidth.running=!1}}},dt=async t=>(await new Promise(e=>setTimeout(e,2e3)),Math.random()*50+10),ut=async()=>{if(V.value){d.concurrent.running=!0,d.concurrent.progress=0,d.concurrent.currentStep="开始并发连接测试...";try{const t=se.connections,e=[],o=[];d.concurrent.currentStep=`创建 ${t} 个并发连接...`;for(let p=0;p<t;p++)e.push(ts(V.value)),d.concurrent.progress=Math.round(p/t*50),await new Promise(v=>setTimeout(v,50));d.concurrent.currentStep="等待连接结果...";const r=await Promise.allSettled(e);let _=0,w=0,S=0;r.forEach(p=>{p.status==="fulfilled"&&p.value.success?(_++,S+=p.value.responseTime):w++}),i.concurrent.successful=_,i.concurrent.failed=w,i.concurrent.avgResponseTime=_>0?Math.round(S/_):0,i.concurrent.successRate=Math.round(_/t*100),d.concurrent.progress=100,d.concurrent.currentStep="并发测试完成"}catch{u.error("并发测试失败")}finally{d.concurrent.running=!1}}},ts=async t=>{const e=Date.now();try{const o=Ze(t),r=await fetch("/api/v1/system/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({host:o.host,port:o.port}),timeout:5e3}),_=Date.now()-e;return{success:r.ok,responseTime:_}}catch{return{success:!1,responseTime:Date.now()-e}}},ss=async()=>{if(!V.value)return;d.stability.running=!0,d.stability.progress=0,d.stability.currentStep="开始稳定性测试...";const t=parseInt(ye.duration)*60*1e3,e=Date.now();let o=0;const r=[];try{const _=setInterval(async()=>{if(!d.stability.running){clearInterval(_);return}const w=Date.now()-e,S=Math.min(Math.round(w/t*100),100);d.stability.progress=S,d.stability.currentStep=`稳定性测试进行中... ${Math.round(w/1e3)}s / ${Math.round(t/1e3)}s`;const p=await rt(V.value);if(p.success?r.push(p.latency):o++,i.stability.elapsed=as(w),i.stability.disconnections=o,i.stability.avgLatency=r.length>0?Math.round(r.reduce((v,$)=>v+$,0)/r.length):0,w>=t){clearInterval(_);const v=r.length/(r.length+o),$=i.stability.avgLatency;let E=Math.round(v*70);$<50?E+=30:$<100?E+=20:$<200&&(E+=10),i.stability.score=Math.min(E,100),d.stability.progress=100,d.stability.currentStep="稳定性测试完成",d.stability.running=!1}},2e3)}catch{u.error("稳定性测试失败"),d.stability.running=!1}},as=t=>{const e=Math.floor(t/1e3),o=Math.floor(e/60),r=Math.floor(o/60);return r>0?`${r}小时${o%60}分钟`:o>0?`${o}分钟${e%60}秒`:`${e}秒`},ls=()=>{D.details=[];let t=0,e=0;if(i.latency.average!==null){const o=i.latency.average<50?90:i.latency.average<100?75:i.latency.average<200?60:40;D.details.push({testType:"延迟测试",result:`平均 ${i.latency.average}ms，丢包率 ${i.latency.packetLoss}%`,score:o,status:o>=70?"passed":"failed",notes:i.latency.average<100?"延迟表现良好":"延迟较高，可能影响实时性"}),t+=o,e++}if(i.bandwidth.download!==null){const o=parseFloat(i.bandwidth.download)>20?90:parseFloat(i.bandwidth.download)>10?75:parseFloat(i.bandwidth.download)>5?60:40;D.details.push({testType:"带宽测试",result:`下载 ${i.bandwidth.download}Mbps，上传 ${i.bandwidth.upload}Mbps`,score:o,status:o>=70?"passed":"failed",notes:parseFloat(i.bandwidth.download)>10?"带宽充足":"带宽较低"}),t+=o,e++}if(i.concurrent.successful!==null){const o=i.concurrent.successRate>90?90:i.concurrent.successRate>80?75:i.concurrent.successRate>70?60:40;D.details.push({testType:"并发测试",result:`成功率 ${i.concurrent.successRate}%，平均响应 ${i.concurrent.avgResponseTime}ms`,score:o,status:o>=70?"passed":"failed",notes:i.concurrent.successRate>85?"并发性能优秀":"并发性能需要优化"}),t+=o,e++}i.stability.score!==null&&(D.details.push({testType:"稳定性测试",result:`评分 ${i.stability.score}/100，中断 ${i.stability.disconnections} 次`,score:i.stability.score,status:i.stability.score>=70?"passed":"failed",notes:i.stability.score>85?"连接非常稳定":"连接稳定性一般"}),t+=i.stability.score,e++),D.overallScore=e>0?Math.round(t/e):0},Ke=t=>t?new Date(t).toLocaleString():"--",ns=()=>{var _;const t={config:V.value,testResults:i,report:D,timestamp:new Date().toISOString()},e=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),o=URL.createObjectURL(e),r=document.createElement("a");r.href=o,r.download=`datalink-test-report-${(_=V.value)==null?void 0:_.name}-${new Date().toISOString().split("T")[0]}.json`,r.click(),URL.revokeObjectURL(o),u.success("测试报告已导出")},os=()=>{Object.keys(i.latency).forEach(t=>i.latency[t]=null),Object.keys(i.bandwidth).forEach(t=>i.bandwidth[t]=null),Object.keys(i.concurrent).forEach(t=>i.concurrent[t]=null),Object.keys(i.stability).forEach(t=>i.stability[t]=null),Object.keys(i.compression).forEach(t=>i.compression[t]=null),D.details=[],D.overallScore=0,D.startTime=null,D.endTime=null,u.success("测试结果已清除")},rs=async()=>{if(!V.value){u.warning("请先选择要测试的配置");return}d.compression.running=!0,d.compression.progress=0,d.compression.currentStep="初始化数据压缩测试...";try{d.compression.currentStep="生成测试数据...",d.compression.progress=10;const t=ds(h.dataType,parseInt(h.dataSize));d.compression.currentStep="执行压缩性能测试...",d.compression.progress=30;const e=await cs(t);d.compression.currentStep="执行数据传输测试...",d.compression.progress=60;const o=await ps(e.compressedData);d.compression.currentStep="计算测试结果...",d.compression.progress=90,vs(t,e,o),d.compression.progress=100,d.compression.currentStep="数据压缩测试完成",u.success("数据压缩测试完成")}catch(t){u.error(`数据压缩测试失败: ${t.message}`)}finally{d.compression.running=!1}},is=()=>{d.compression.running=!1,u.info("数据压缩测试已停止")},ds=(t,e)=>{const o=e*1024*1024;let r="";switch(t){case"json":r=Ye(o);break;case"binary":r=ct(o);break;case"text":r=mt(o);break;case"mixed":r=us(o);break;default:r=Ye(o)}return{type:t,size:o,data:r,timestamp:new Date().toISOString()}},Ye=t=>{const e={system:{version:"1.0.0",environment:"production",features:["compression","encryption","monitoring"],settings:{maxConnections:1e3,timeout:3e4,retryAttempts:3,enableLogging:!0}},users:[],devices:[],tunnels:[],logs:[]};let o=JSON.stringify(e);for(;o.length<t;)e.users.push({id:Math.random().toString(36).substr(2,9),name:`User_${Math.random().toString(36).substr(2,8)}`,email:`user${Math.random().toString(36).substr(2,5)}@example.com`,role:["admin","user","guest"][Math.floor(Math.random()*3)],permissions:["read","write","execute"],lastLogin:new Date().toISOString(),settings:{theme:"dark",language:"zh-CN",notifications:!0}}),e.devices.push({id:Math.random().toString(36).substr(2,9),name:`Device_${Math.random().toString(36).substr(2,8)}`,type:["usb","network","serial"][Math.floor(Math.random()*3)],status:["online","offline","error"][Math.floor(Math.random()*3)],lastSeen:new Date().toISOString(),properties:{vendor:"Generic",model:"Model_"+Math.random().toString(36).substr(2,6),version:"1.0.0"}}),o=JSON.stringify(e);return o.substring(0,t)},ct=t=>{const e=new ArrayBuffer(t),o=new Uint8Array(e);for(let r=0;r<t;r++)o[r]=Math.floor(Math.random()*256);return btoa(String.fromCharCode.apply(null,o))},mt=t=>{const e=["[INFO] System startup completed successfully","[WARN] High memory usage detected: 85%","[ERROR] Connection timeout to remote server","[DEBUG] Processing user request: GET /api/v1/users","[INFO] Database connection established","[WARN] SSL certificate expires in 30 days","[ERROR] Failed to authenticate user session","[INFO] Backup process completed successfully","[DEBUG] Cache hit ratio: 92%","[WARN] Disk space usage: 78%"];let o="";for(;o.length<t;){const r=new Date().toISOString(),_=e[Math.floor(Math.random()*e.length)],w=Math.random().toString(36).substr(2,16),S=`${r} [${w}] ${_}
`;o+=S}return o.substring(0,t)},us=t=>{const e=Ye(Math.floor(t*.4)),o=ct(Math.floor(t*.3)),r=mt(Math.floor(t*.3));return JSON.stringify({json:e,binary:o,text:r,metadata:{type:"mixed",timestamp:new Date().toISOString(),components:["json","binary","text"]}})},cs=async t=>{const e=Date.now();try{const o=await fetch("/api/v1/system/test-compression",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({data:t.data,algorithm:h.algorithm,level:h.compressionLevel,enableCompression:h.enableCompression})});if(!o.ok)throw new Error("压缩测试API调用失败");const r=await o.json(),_=Date.now()-e;return{originalSize:t.size,compressedSize:r.compressedSize,compressedData:r.compressedData,compressionTime:_,algorithm:h.algorithm,compressionRatio:((t.size-r.compressedSize)/t.size*100).toFixed(2)}}catch{return ms(t)}},ms=t=>{const e={snappy:{json:.65,binary:.85,text:.45,mixed:.6},lz4:{json:.6,binary:.8,text:.4,mixed:.55},zstd:{json:.5,binary:.7,text:.3,mixed:.45}},o=h.algorithm,r=h.dataType,_=e[o][r]||.7,w=Math.floor(t.size*_),S=Math.floor(t.size/1024/1024*50);return{originalSize:t.size,compressedSize:w,compressedData:t.data.substring(0,w),compressionTime:S,algorithm:o,compressionRatio:((t.size-w)/t.size*100).toFixed(2)}},ps=async t=>{const e=Date.now(),o=parseInt(h.concurrency),r=h.duration==="continuous"?60:parseInt(h.duration);let _=0,w=0;const S=[];for(let p=0;p<o;p++)S.push(_s(t,r));try{(await Promise.allSettled(S)).forEach(E=>{E.status==="fulfilled"&&(_+=E.value.bytesTransferred,w+=E.value.transferCount)});const v=Date.now()-e,$=_/1024/1024/(v/1e3);return{totalTransferred:_,transferCount:w,transferSpeed:$.toFixed(2),totalTime:v,concurrency:o}}catch(p){throw new Error(`传输测试失败: ${p.message}`)}},_s=async(t,e)=>{const o=Date.now(),r=o+e*1e3;let _=0,w=0;for(;Date.now()<r&&d.compression.running;)try{const S=await fs(t);_+=S.bytes,w++;const p=Date.now()-o,v=Math.min(p/(e*1e3)*100,100);d.compression.progress=Math.max(d.compression.progress,60+v*.3),await new Promise($=>setTimeout($,10))}catch(S){console.error("单次传输失败:",S);break}return{bytesTransferred:_,transferCount:w}},fs=async t=>{const e=Math.random()*50+10;return await new Promise(o=>setTimeout(o,e)),{bytes:t.length,latency:e,success:!0}},vs=(t,e,o)=>{i.compression.originalSize=Re(t.size),i.compression.compressedSize=Re(e.compressedSize),i.compression.compressionRatio=e.compressionRatio,i.compression.transferSpeed=o.transferSpeed,i.compression.compressionTime=e.compressionTime,i.compression.decompressionTime=Math.floor(e.compressionTime*.3),i.compression.totalTransferred=Re(o.totalTransferred);const r=t.size*o.transferCount,_=o.totalTransferred,w=Re(r-_);i.compression.bandwidthSaved=w,i.compression.chartData=[{algorithm:e.algorithm.toUpperCase(),ratio:parseFloat(e.compressionRatio),size:e.compressedSize}]},Re=t=>{if(t===0)return"0 B";const e=1024,o=["B","KB","MB","GB","TB"],r=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,r)).toFixed(2))+" "+o[r]},ys=async t=>{try{await X.confirm(`确定要升级 ${t.name} 的内网穿透工具吗？这将下载并安装最新版本。`,"升级确认",{confirmButtonText:"确定升级",cancelButtonText:"取消",type:"warning"}),u.info("升级功能正在开发中，敬请期待")}catch{}},bs=async t=>{try{const e=t.force_disabled?"解锁":"锁定";await X.confirm(`确定要${e} ${t.name} 吗？${t.force_disabled?"解锁后可以正常启用配置。":"锁定后将完全禁止启动，用于安全防护。"}`,`${e}确认`,{confirmButtonText:`确定${e}`,cancelButtonText:"取消",type:t.force_disabled?"success":"warning"}),t.force_disabled=!t.force_disabled,await Le(t.id,{force_disabled:t.force_disabled}),u.success(`${t.name} 已${e}`),t.force_disabled&&t.is_active&&(t.is_active=!1,await Le(t.id,{is_active:!1}))}catch(e){e!=="cancel"&&(u.error("操作失败"),t.force_disabled=!t.force_disabled)}},gs=async t=>{try{await X.confirm("确定要删除此配置吗？","确认删除",{type:"warning"}),await La(t.id),u.success("配置删除成功"),await Te()}catch(e){e!=="cancel"&&u.error("删除配置失败")}},ws=t=>({headscale:"primary",netbird:"success",zerotier:"warning",nebula:"info",frp:"primary",nps:"success",rathole:"info",linker:"danger"})[t]||"default",hs=t=>({running:"success",stopped:"info",error:"danger"})[t]||"default",Cs=t=>({running:"运行中",stopped:"已停止",error:"错误"})[t]||"未知",pt=t=>["frp","nps","headscale","netbird","zerotier"].includes(t.toLowerCase()),Ts=t=>{if(t.tunnel_type.toLowerCase()==="frp"){const e=t.config_data||{};return e.dashboard_port||e.web_port||e.bind_port}return!0},Ss=async t=>{try{const o=await(await fetch(`/api/v1/system/tunnels/${t.id}/management-url`)).json();o.success&&o.url?window.open(o.url,"_blank","width=1200,height=800,scrollbars=yes,resizable=yes"):u.warning(o.message||"无法获取管理界面地址")}catch(e){console.error("获取管理界面地址失败:",e),u.error("获取管理界面地址失败")}},F=async()=>{try{We.value=!0;const t=await M();t&&typeof t=="object"?t.success===!0?(L.value=Array.isArray(t.data)?t.data:[],ae.value=L.value.find(e=>e==null?void 0:e.is_active)||null):t.data&&t.data.success===!0?(L.value=Array.isArray(t.data.data)?t.data.data:[],ae.value=L.value.find(e=>e==null?void 0:e.is_active)||null):Array.isArray(t)?(L.value=t,ae.value=L.value.find(e=>e==null?void 0:e.is_active)||null):Array.isArray(t.data)?(L.value=t.data,ae.value=L.value.find(e=>e==null?void 0:e.is_active)||null):(console.warn("未知的响应格式:",t),L.value=[],ae.value=null):(console.warn("无效的响应:",t),L.value=[],ae.value=null)}catch(t){console.error("加载压缩配置失败:",t),u.error(`加载压缩配置失败: ${t.message||"未知错误"}`),L.value=[],ae.value=null}finally{We.value=!1}},ks=async()=>{try{const t=await Ne();let e=t;t&&t.success&&t.data&&(console.log("🔧 loadAvailableAlgorithms - 检测到API中间件包装格式，提取data字段"),e=t.data),console.log("loadAvailableAlgorithms - 处理后的算法数据:",e);let o=null;if(e&&e.algorithms?o=e.algorithms:Array.isArray(e)?o=e:e&&e.data&&e.data.algorithms&&(o=e.data.algorithms),o)de.value=o.map(r=>({...r,enabled:r.available}));else throw new Error("无法解析算法数据")}catch(t){console.error("加载可用算法失败:",t),de.value=[{name:"snappy",display_name:"Snappy",description:"Google开发的快速压缩算法，速度优先",available:!0,enabled:!0},{name:"lz4",display_name:"LZ4",description:"超高速压缩算法，极低延迟",available:!0,enabled:!0},{name:"zstd",display_name:"Zstandard",description:"Facebook开发的高效压缩算法，压缩率优先",available:!0,enabled:!1}]}},ce=async()=>{try{const t=await Ve();t.success&&t.data&&t.data.global_stats?Me.value=t.data.global_stats:t.data&&t.data.success&&t.data.data&&t.data.data.global_stats?Me.value=t.data.data.global_stats:t.global_stats?Me.value=t.global_stats:t.data&&t.data.global_stats?Me.value=t.data.global_stats:console.warn("无法解析压缩统计数据格式:",t)}catch(t){console.error("加载压缩统计失败:",t)}},_t=async()=>{try{const t=await je();t.success&&t.data?Be.value=t.data:t.data&&t.data.success&&t.data.data?Be.value=t.data.data:t.recommendations?Be.value=t.recommendations:t.data&&t.data.recommendations?Be.value=t.data.recommendations:console.warn("无法解析智能推荐数据格式:",t)}catch(t){console.error("加载智能推荐失败:",t)}},Vs=()=>{const t=de.value.find(e=>e.name===k.algorithm);Ce.value=t,t&&xs()},xs=()=>{const t=Ce.value;if(!t)return;const o={snappy:{rate:85,speed:520,memory:440},lz4:{rate:78,speed:610,memory:410},zstd:{rate:92,speed:380,memory:520}}[t.name]||{rate:80,speed:400,memory:500};ue.value={estimated_rate:o.rate,estimated_speed:o.speed,estimated_memory:o.memory}},$s=async()=>{var t;try{await He.value.validate(),De.value=!0;const e=await le(k);if(e&&typeof e=="object")if(e.success===!0||e.data&&e.data.success===!0)u.success("压缩配置创建成功"),we.value=!1,Qe(),await F();else{const o=e.message||((t=e.data)==null?void 0:t.message)||"创建压缩配置失败";u.error(o)}else u.error("服务器响应格式错误")}catch(e){console.error("创建压缩配置失败:",e),u.error("创建压缩配置失败")}finally{De.value=!1}},Ds=t=>{Object.assign(x,t),ie.value=!0},zs=async()=>{try{await tt.value.validate(),ze.value=!0;const t=await fe(x.id,x);t.success?(u.success("压缩配置更新成功"),ie.value=!1,await F()):t.data&&t.data.success?(u.success("压缩配置更新成功"),ie.value=!1,await F()):u.error("更新压缩配置失败")}catch(t){console.error("更新压缩配置失败:",t),u.error("更新压缩配置失败")}finally{ze.value=!1}},Us=async t=>{try{Q.value=t;const e=u({message:"正在切换压缩配置，请稍候...",type:"info",duration:0}),o=await N(t);e.close(),o.success?(u.success("压缩配置已激活，新连接将使用新配置"),await F(),await ce()):o.data&&o.data.success?(u.success("压缩配置已激活，新连接将使用新配置"),await F(),await ce()):u.error("激活压缩配置失败")}catch(e){console.error("激活压缩配置失败:",e),u.error("激活压缩配置失败")}finally{Q.value=null}},Ms=async t=>{try{Q.value=t;const e=await Fe(t);e.success?(u.success("压缩配置已停用"),await F(),await ce()):e.data&&e.data.success?(u.success("压缩配置已停用"),await F(),await ce()):u.error("停用压缩配置失败")}catch(e){console.error("停用压缩配置失败:",e),u.error("停用压缩配置失败")}finally{Q.value=null}},Bs=async t=>{try{await X.confirm("确定要删除这个压缩配置吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await J(t);e.success?(u.success("压缩配置已删除"),await F()):e.data&&e.data.success?(u.success("压缩配置已删除"),await F()):u.error("删除压缩配置失败")}catch(e){e!=="cancel"&&(console.error("删除压缩配置失败:",e),u.error("删除压缩配置失败"))}},Rs=t=>{const e=new FileReader;e.onload=o=>{try{const r=JSON.parse(o.target.result);Object.assign(k,r),u.success("配置文件读取成功")}catch{u.error("配置文件格式错误")}},e.readAsText(t.raw)},Es=async()=>{try{Ue.value=!0;const t=await le(k);t.success?(u.success("配置导入成功"),he.value=!1,Qe(),await F()):t.data&&t.data.success&&(u.success("配置导入成功"),he.value=!1,Qe(),await F())}catch(t){console.error("导入配置失败:",t),u.error("导入配置失败")}finally{Ue.value=!1}},Qe=()=>{var t;Object.assign(k,{name:"",algorithm:"",level:"medium",stream_type:"",max_concurrent_users:500,bandwidth_limit_mbps:3,auto_fallback:!0,is_default:!1}),ue.value=null,Ce.value=null,(t=He.value)==null||t.resetFields()},Is=t=>({snappy:"success",lz4:"warning",zstd:"primary"})[t]||"info",Os=t=>({snappy:"Snappy",lz4:"LZ4",zstd:"Zstandard"})[t]||t.toUpperCase(),As=t=>({low:"success",medium:"warning",high:"danger"})[t]||"info",Ps=t=>({low:"低",medium:"中",high:"高"})[t]||t,Ls=t=>({web_data:"Web数据",client_data:"专属客户端数据",general_data:"通用数据",web_http:"Web HTTP (旧)",web_https:"Web HTTPS (旧)",vh_usb:"VirtualHere USB (旧)",vh_ctrl:"VirtualHere 控制 (旧)"})[t]||"通用数据",Fs=async t=>{try{u.success(`${t.display_name} ${t.enabled?"已启用":"已禁用"}`)}catch{u.error("算法状态切换失败"),t.enabled=!t.enabled}},Ns=t=>{u.info(`配置 ${t.display_name} 算法`)},js=async t=>{try{u.info(`正在测试 ${t.display_name} 算法...`),setTimeout(()=>{u.success(`${t.display_name} 算法测试通过`)},2e3)}catch{u.error("算法测试失败")}};St(()=>{At(),Te(),F(),ks(),ce(),_t(),lt(),setInterval(ce,5e3),setInterval(_t,3e4),Ee()});const Ee=async()=>{qe.value=!0;try{const t=await Za();t.success?st.value=t.data:u.error(t.message||"获取缓存信息失败")}catch(t){console.error("获取缓存信息失败:",t),u.error("获取缓存信息失败")}finally{qe.value=!1}},Ws=async t=>{try{await X.confirm(`确定要清除 ${Gs(t)} 吗？`,"确认清除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),G[t]=!0;const e=await It(t);e.success?(u.success(e.message||"缓存清除成功"),await Ee()):u.error(e.message||"缓存清除失败")}catch(e){e!=="cancel"&&(console.error("清除缓存失败:",e),u.error("清除缓存失败"))}finally{G[t]=!1}},Js=async()=>{try{await X.confirm("确定要清除所有服务器缓存吗？这可能会导致短暂的性能下降。","确认清除所有缓存",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),G.all=!0;const t=await Ka();t.success?(u.success(t.message||"所有缓存清除成功"),await Ee()):u.error(t.message||"清除缓存失败")}catch(t){t!=="cancel"&&(console.error("清除所有缓存失败:",t),u.error("清除所有缓存失败"))}finally{G.all=!1}},Hs=async()=>{try{await X.confirm("确定要清除Web页面缓存吗？页面将会自动刷新。","确认清除Web缓存",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),G.web=!0;try{await Ya(),u.success("Web页面缓存清除成功，页面即将刷新"),setTimeout(()=>{Qa()},1e3)}catch{u.error("Web页面缓存清除失败")}}catch(t){t!=="cancel"&&(console.error("清除Web缓存失败:",t),u.error("清除Web缓存失败"))}finally{G.web=!1}},qs=()=>{Ee()},Gs=t=>({server_cache:"服务器程序缓存",redis_cache:"Redis数据缓存",query_cache:"数据库查询缓存",compression_cache:"压缩统计缓存",tunnel_cache:"隧道状态缓存",api_cache:"API响应缓存"})[t]||t,Zs=t=>({server_cache:Ba,redis_cache:Ma,query_cache:Ua,compression_cache:za,tunnel_cache:Da,api_cache:$a})[t]||Ra,Ks=t=>({active:"活跃",inactive:"未激活",error:"错误"})[t]||t;return(t,e)=>{const o=zt,r=Dt,_=$t,w=ia,S=Bt,p=Ut,v=ua,$=xt,E=ca,me=da,j=Et,ft=ma,Se=pa,ke=_a,f=va,O=fa,Ys=Mt,Qs=Ca,Xs=ra,pe=oa,_e=Sa,vt=Ta,Z=Vt,Ie=kt,Oe=ka,ea=Va,yt=wa;return b(),z("div",Xa,[s(j,{class:"main-card"},{header:l(()=>e[55]||(e[55]=[a("div",{class:"card-header"},[a("span",{class:"header-title"},"系统设置"),a("span",{class:"header-subtitle"},"管理系统配置、内网穿透和数据压缩功能")],-1)])),default:l(()=>[s(Xs,{modelValue:ve.value,"onUpdate:modelValue":e[18]||(e[18]=n=>ve.value=n),type:"border-card"},{default:l(()=>[s(w,{label:"基础设置",name:"general"},{default:l(()=>[a("div",el,[e[56]||(e[56]=a("h3",null,"系统信息",-1)),s(_,{model:g,"label-width":"150px"},{default:l(()=>[s(r,{label:"系统名称"},{default:l(()=>[s(o,{modelValue:g.system_name,"onUpdate:modelValue":e[0]||(e[0]=n=>g.system_name=n),onBlur:e[1]||(e[1]=n=>Ge("system_name",g.system_name))},null,8,["modelValue"])]),_:1}),s(r,{label:"系统版本"},{default:l(()=>[s(o,{modelValue:g.system_version,"onUpdate:modelValue":e[2]||(e[2]=n=>g.system_version=n),onBlur:e[3]||(e[3]=n=>Ge("system_version",g.system_version))},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1}),s(w,{label:"USB数据库",name:"usb-database"},{default:l(()=>[s(Ga)]),_:1}),s(w,{label:"客户端设置",name:"client"},{default:l(()=>[a("div",tl,[e[58]||(e[58]=a("h3",null,"专属客户端",-1)),s(_,{model:H,"label-width":"150px"},{default:l(()=>[s(r,{label:"下载链接"},{default:l(()=>[s(o,{modelValue:H.client_download_url,"onUpdate:modelValue":e[4]||(e[4]=n=>H.client_download_url=n),placeholder:"请输入客户端下载链接",onBlur:e[5]||(e[5]=n=>Ge("client_download_url",H.client_download_url))},null,8,["modelValue"]),e[57]||(e[57]=a("div",{class:"setting-help"}," 此链接将在登录页面显示，用户可点击下载专属客户端 ",-1))]),_:1,__:[57]})]),_:1},8,["model"])])]),_:1}),s(w,{label:"公网链接",name:"tunnel"},{default:l(()=>[a("div",sl,[s(j,{class:"standard-card"},{header:l(()=>[a("div",al,[e[60]||(e[60]=a("span",null,"公网链接管理",-1)),s(p,{type:"primary",onClick:e[6]||(e[6]=n=>ee.value=!0)},{default:l(()=>[s(S,null,{default:l(()=>[s(K(wt))]),_:1}),e[59]||(e[59]=m(" 新增配置 ",-1))]),_:1,__:[59]})])]),default:l(()=>[s(me,{data:ne.value,style:{width:"100%"},size:"small"},{default:l(()=>[s(v,{prop:"name",label:"配置名称",width:"120","show-overflow-tooltip":""}),s(v,{prop:"tunnel_type",label:"类型",width:"240",align:"center"},{default:l(n=>[s($,{type:ws(n.row.tunnel_type),size:"small"},{default:l(()=>[m(c(n.row.tunnel_type.toUpperCase()),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"is_active",label:"启用",width:"80",align:"center"},{default:l(n=>[s(E,{modelValue:n.row.is_active,"onUpdate:modelValue":T=>n.row.is_active=T,onChange:T=>Lt(n.row),disabled:n.row.force_disabled,size:"small"},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),s(v,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(n=>[s($,{type:hs(n.row.status),size:"small"},{default:l(()=>[m(c(Cs(n.row.status)),1)]),_:2},1032,["type"])]),_:1}),s(v,{label:"操作","min-width":"400",align:"left"},{default:l(n=>[a("div",ll,[n.row.is_running?(b(),B(p,{key:1,type:"warning",size:"small",onClick:T=>Nt(n.row)},{default:l(()=>e[62]||(e[62]=[m(" 停止 ",-1)])),_:2,__:[62]},1032,["onClick"])):(b(),B(p,{key:0,type:"success",size:"small",onClick:T=>Ft(n.row),disabled:!n.row.is_active||n.row.force_disabled},{default:l(()=>e[61]||(e[61]=[m(" 启动 ",-1)])),_:2,__:[61]},1032,["onClick","disabled"])),s(p,{type:"primary",size:"small",onClick:T=>jt(n.row)},{default:l(()=>e[63]||(e[63]=[m(" 编辑 ",-1)])),_:2,__:[63]},1032,["onClick"]),s(p,{type:"info",size:"small",onClick:T=>ys(n.row)},{default:l(()=>e[64]||(e[64]=[m(" 升级 ",-1)])),_:2,__:[64]},1032,["onClick"]),s(p,{type:n.row.force_disabled?"success":"danger",size:"small",onClick:T=>bs(n.row)},{default:l(()=>[m(c(n.row.force_disabled?"解锁":"锁定"),1)]),_:2},1032,["type","onClick"]),s(p,{type:"success",size:"small",onClick:T=>Jt(n.row),loading:te.value[n.row.id],icon:"Connection"},{default:l(()=>[m(c(te.value[n.row.id]?"测试中":"测试连接"),1)]),_:2},1032,["onClick","loading"]),pt(n.row.tunnel_type)&&Ts(n.row)?(b(),B(p,{key:2,type:"info",size:"small",onClick:T=>Ss(n.row)},{default:l(()=>e[65]||(e[65]=[m(" 管理 ",-1)])),_:2,__:[65]},1032,["onClick"])):U("",!0)]),s(p,{type:"danger",size:"small",onClick:T=>gs(n.row),icon:K(gt),disabled:n.row.is_running},{default:l(()=>e[66]||(e[66]=[m(" 删除 ",-1)])),_:2,__:[66]},1032,["onClick","icon","disabled"])]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1}),s(w,{label:"数据链路验证",name:"datalink"},{default:l(()=>[a("div",nl,[s(j,{class:"standard-card"},{header:l(()=>[a("div",ol,[e[68]||(e[68]=a("span",null,"连接状态检查",-1)),s(p,{type:"primary",onClick:lt,loading:q.value},{default:l(()=>[s(S,null,{default:l(()=>[s(K(Rt))]),_:1}),e[67]||(e[67]=m(" 刷新状态 ",-1))]),_:1,__:[67]},8,["loading"])])]),default:l(()=>[a("div",rl,[(b(!0),z(Ae,null,Pe(oe.value,n=>(b(),z("div",{key:n.id,class:"connection-card"},[a("div",il,[a("h4",null,c(n.name)+" ("+c(n.tunnel_type.toUpperCase())+")",1),s($,{type:Kt(n.status),size:"large"},{default:l(()=>[m(c(Yt(n.status)),1)]),_:2},1032,["type"])]),a("div",dl,[a("div",ul,[e[69]||(e[69]=a("span",{class:"label"},"服务状态:",-1)),a("span",cl,c(n.service_status||"未知"),1)]),a("div",ml,[e[70]||(e[70]=a("span",{class:"label"},"最后检查:",-1)),a("span",pl,c(Ke(n.last_check)),1)]),a("div",_l,[e[71]||(e[71]=a("span",{class:"label"},"运行时长:",-1)),a("span",fl,c(n.uptime||"0分钟"),1)])]),a("div",vl,[s(p,{type:"success",size:"small",onClick:T=>Qt(n),disabled:!nt(n)},{default:l(()=>e[72]||(e[72]=[m(" 开始性能测试 ",-1)])),_:2,__:[72]},1032,["onClick","disabled"])])]))),128))]),oe.value.length===0?(b(),B(ft,{key:0,description:"没有活跃的内网穿透连接"},{default:l(()=>[s(p,{type:"primary",onClick:e[7]||(e[7]=n=>t.$emit("switch-tab","tunnel"))},{default:l(()=>e[73]||(e[73]=[m(" 前往配置管理 ",-1)])),_:1,__:[73]})]),_:1})):U("",!0)]),_:1}),V.value?(b(),B(j,{key:0,class:"standard-card"},{header:l(()=>[a("div",yl,[a("span",null,c(V.value.name)+" - 数据性能测试",1),a("div",bl,[s(p,{type:"primary",onClick:Xt,loading:re.value,disabled:!nt(V.value)},{default:l(()=>e[74]||(e[74]=[m(" 开始全面测试 ",-1)])),_:1,__:[74]},8,["loading","disabled"]),s(p,{type:"danger",onClick:es,disabled:!$e.value},{default:l(()=>e[75]||(e[75]=[m(" 停止测试 ",-1)])),_:1,__:[75]},8,["disabled"])])])]),default:l(()=>[a("div",gl,[a("div",wl,[a("div",hl,[e[76]||(e[76]=a("h4",null,"连接延迟测试",-1)),s(p,{size:"small",onClick:ot,loading:d.latency.running},{default:l(()=>[m(c(d.latency.running?"测试中...":"开始测试"),1)]),_:1},8,["loading"])]),a("div",Cl,[a("div",Tl,[a("div",Sl,[e[77]||(e[77]=a("span",{class:"metric-label"},"平均延迟",-1)),a("span",kl,c(i.latency.average||"--")+"ms",1)]),a("div",Vl,[e[78]||(e[78]=a("span",{class:"metric-label"},"最小延迟",-1)),a("span",xl,c(i.latency.min||"--")+"ms",1)]),a("div",$l,[e[79]||(e[79]=a("span",{class:"metric-label"},"最大延迟",-1)),a("span",Dl,c(i.latency.max||"--")+"ms",1)]),a("div",zl,[e[80]||(e[80]=a("span",{class:"metric-label"},"丢包率",-1)),a("span",Ul,c(i.latency.packetLoss||"--")+"%",1)])]),d.latency.running?(b(),z("div",Ml,[s(Se,{percentage:d.latency.progress,status:d.latency.progress===100?"success":""},null,8,["percentage","status"]),a("span",Bl,c(d.latency.currentStep),1)])):U("",!0)])]),a("div",Rl,[a("div",El,[e[81]||(e[81]=a("h4",null,"带宽速率测试",-1)),s(p,{size:"small",onClick:it,loading:d.bandwidth.running},{default:l(()=>[m(c(d.bandwidth.running?"测试中...":"开始测试"),1)]),_:1},8,["loading"])]),a("div",Il,[a("div",Ol,[a("div",Al,[e[82]||(e[82]=a("span",{class:"metric-label"},"下载速度",-1)),a("span",Pl,c(i.bandwidth.download||"--")+" Mbps",1)]),a("div",Ll,[e[83]||(e[83]=a("span",{class:"metric-label"},"上传速度",-1)),a("span",Fl,c(i.bandwidth.upload||"--")+" Mbps",1)]),a("div",Nl,[e[84]||(e[84]=a("span",{class:"metric-label"},"数据传输量",-1)),a("span",jl,c(i.bandwidth.totalData||"--")+" MB",1)]),a("div",Wl,[e[85]||(e[85]=a("span",{class:"metric-label"},"测试时长",-1)),a("span",Jl,c(i.bandwidth.duration||"--")+"s",1)])]),d.bandwidth.running?(b(),z("div",Hl,[s(Se,{percentage:d.bandwidth.progress,status:d.bandwidth.progress===100?"success":""},null,8,["percentage","status"]),a("span",ql,c(d.bandwidth.currentStep),1)])):U("",!0)])]),a("div",Gl,[a("div",Zl,[e[87]||(e[87]=a("h4",null,"多并发连接测试",-1)),a("div",Kl,[s(ke,{modelValue:se.connections,"onUpdate:modelValue":e[8]||(e[8]=n=>se.connections=n),min:1,max:100,size:"small",style:{width:"120px","margin-right":"10px"}},null,8,["modelValue"]),e[86]||(e[86]=a("span",{style:{"margin-right":"10px"}},"个并发连接",-1)),s(p,{size:"small",onClick:ut,loading:d.concurrent.running},{default:l(()=>[m(c(d.concurrent.running?"测试中...":"开始测试"),1)]),_:1},8,["loading"])])]),a("div",Yl,[a("div",Ql,[a("div",Xl,[e[88]||(e[88]=a("span",{class:"metric-label"},"成功连接",-1)),a("span",en,c(i.concurrent.successful||"--"),1)]),a("div",tn,[e[89]||(e[89]=a("span",{class:"metric-label"},"失败连接",-1)),a("span",sn,c(i.concurrent.failed||"--"),1)]),a("div",an,[e[90]||(e[90]=a("span",{class:"metric-label"},"平均响应时间",-1)),a("span",ln,c(i.concurrent.avgResponseTime||"--")+"ms",1)]),a("div",nn,[e[91]||(e[91]=a("span",{class:"metric-label"},"连接成功率",-1)),a("span",on,c(i.concurrent.successRate||"--")+"%",1)])]),d.concurrent.running?(b(),z("div",rn,[s(Se,{percentage:d.concurrent.progress,status:d.concurrent.progress===100?"success":""},null,8,["percentage","status"]),a("span",dn,c(d.concurrent.currentStep),1)])):U("",!0)])]),a("div",un,[a("div",cn,[e[92]||(e[92]=a("h4",null,"长期稳定性测试",-1)),a("div",mn,[s(O,{modelValue:ye.duration,"onUpdate:modelValue":e[9]||(e[9]=n=>ye.duration=n),size:"small",style:{width:"120px","margin-right":"10px"}},{default:l(()=>[s(f,{label:"5分钟",value:"5"}),s(f,{label:"15分钟",value:"15"}),s(f,{label:"30分钟",value:"30"}),s(f,{label:"1小时",value:"60"})]),_:1},8,["modelValue"]),s(p,{size:"small",onClick:ss,loading:d.stability.running},{default:l(()=>[m(c(d.stability.running?"测试中...":"开始测试"),1)]),_:1},8,["loading"])])]),a("div",pn,[a("div",_n,[a("div",fn,[e[93]||(e[93]=a("span",{class:"metric-label"},"运行时长",-1)),a("span",vn,c(i.stability.elapsed||"--"),1)]),a("div",yn,[e[94]||(e[94]=a("span",{class:"metric-label"},"连接中断次数",-1)),a("span",bn,c(i.stability.disconnections||"--"),1)]),a("div",gn,[e[95]||(e[95]=a("span",{class:"metric-label"},"平均延迟",-1)),a("span",wn,c(i.stability.avgLatency||"--")+"ms",1)]),a("div",hn,[e[96]||(e[96]=a("span",{class:"metric-label"},"稳定性评分",-1)),a("span",Cn,c(i.stability.score||"--")+"/100",1)])]),d.stability.running?(b(),z("div",Tn,[s(Se,{percentage:d.stability.progress,status:d.stability.progress===100?"success":""},null,8,["percentage","status"]),a("span",Sn,c(d.stability.currentStep),1)])):U("",!0)])]),a("div",kn,[a("div",Vn,[e[98]||(e[98]=a("h4",null,"数据压缩性能测试",-1)),a("div",xn,[s(p,{size:"small",onClick:rs,loading:d.compression.running,type:"primary"},{default:l(()=>[m(c(d.compression.running?"测试中...":"开始压缩测试"),1)]),_:1},8,["loading"]),s(p,{size:"small",onClick:is,disabled:!d.compression.running,type:"danger"},{default:l(()=>e[97]||(e[97]=[m(" 停止测试 ",-1)])),_:1,__:[97]},8,["disabled"])])]),a("div",$n,[a("div",Dn,[a("div",zn,[e[99]||(e[99]=a("span",{class:"config-label"},"数据大小:",-1)),s(O,{modelValue:h.dataSize,"onUpdate:modelValue":e[10]||(e[10]=n=>h.dataSize=n),size:"small",style:{width:"100px"}},{default:l(()=>[s(f,{label:"1MB",value:"1"}),s(f,{label:"10MB",value:"10"}),s(f,{label:"50MB",value:"50"}),s(f,{label:"100MB",value:"100"})]),_:1},8,["modelValue"])]),a("div",Un,[e[100]||(e[100]=a("span",{class:"config-label"},"并发数:",-1)),s(O,{modelValue:h.concurrency,"onUpdate:modelValue":e[11]||(e[11]=n=>h.concurrency=n),size:"small",style:{width:"100px"}},{default:l(()=>[s(f,{label:"1",value:"1"}),s(f,{label:"5",value:"5"}),s(f,{label:"10",value:"10"}),s(f,{label:"20",value:"20"}),s(f,{label:"50",value:"50"}),s(f,{label:"100",value:"100"}),s(f,{label:"400",value:"400"})]),_:1},8,["modelValue"])]),a("div",Mn,[e[101]||(e[101]=a("span",{class:"config-label"},"持续时间:",-1)),s(O,{modelValue:h.duration,"onUpdate:modelValue":e[12]||(e[12]=n=>h.duration=n),size:"small",style:{width:"120px"}},{default:l(()=>[s(f,{label:"1秒",value:"1"}),s(f,{label:"5秒",value:"5"}),s(f,{label:"10秒",value:"10"}),s(f,{label:"30秒",value:"30"}),s(f,{label:"60秒",value:"60"}),s(f,{label:"5分钟",value:"300"}),s(f,{label:"10分钟",value:"600"}),s(f,{label:"长期持续",value:"continuous"})]),_:1},8,["modelValue"])]),a("div",Bn,[e[102]||(e[102]=a("span",{class:"config-label"},"数据类型:",-1)),s(O,{modelValue:h.dataType,"onUpdate:modelValue":e[13]||(e[13]=n=>h.dataType=n),size:"small",style:{width:"140px"}},{default:l(()=>[s(f,{label:"JSON配置数据",value:"json"}),s(f,{label:"二进制设备数据",value:"binary"}),s(f,{label:"文本日志数据",value:"text"}),s(f,{label:"混合数据包",value:"mixed"})]),_:1},8,["modelValue"])])]),a("div",Rn,[a("div",En,[e[103]||(e[103]=a("span",{class:"config-label"},"启用压缩:",-1)),s(E,{modelValue:h.enableCompression,"onUpdate:modelValue":e[14]||(e[14]=n=>h.enableCompression=n)},null,8,["modelValue"])]),h.enableCompression?(b(),z("div",In,[e[104]||(e[104]=a("span",{class:"config-label"},"压缩算法:",-1)),s(O,{modelValue:h.algorithm,"onUpdate:modelValue":e[15]||(e[15]=n=>h.algorithm=n),size:"small",style:{width:"120px"}},{default:l(()=>[s(f,{label:"Snappy",value:"snappy"}),s(f,{label:"LZ4",value:"lz4"}),s(f,{label:"Zstandard",value:"zstd"})]),_:1},8,["modelValue"])])):U("",!0),h.enableCompression?(b(),z("div",On,[e[105]||(e[105]=a("span",{class:"config-label"},"压缩级别:",-1)),s(O,{modelValue:h.compressionLevel,"onUpdate:modelValue":e[16]||(e[16]=n=>h.compressionLevel=n),size:"small",style:{width:"80px"}},{default:l(()=>[s(f,{label:"低",value:"low"}),s(f,{label:"中",value:"medium"}),s(f,{label:"高",value:"high"})]),_:1},8,["modelValue"])])):U("",!0)])]),a("div",An,[a("div",Pn,[a("div",Ln,[e[106]||(e[106]=a("span",{class:"metric-label"},"原始数据大小",-1)),a("span",Fn,c(i.compression.originalSize||"--"),1)]),a("div",Nn,[e[107]||(e[107]=a("span",{class:"metric-label"},"压缩后大小",-1)),a("span",jn,c(i.compression.compressedSize||"--"),1)]),a("div",Wn,[e[108]||(e[108]=a("span",{class:"metric-label"},"压缩率",-1)),a("span",Jn,c(i.compression.compressionRatio||"--")+"%",1)]),a("div",Hn,[e[109]||(e[109]=a("span",{class:"metric-label"},"传输速度",-1)),a("span",qn,c(i.compression.transferSpeed||"--")+" MB/s",1)]),a("div",Gn,[e[110]||(e[110]=a("span",{class:"metric-label"},"压缩时间",-1)),a("span",Zn,c(i.compression.compressionTime||"--")+"ms",1)]),a("div",Kn,[e[111]||(e[111]=a("span",{class:"metric-label"},"解压时间",-1)),a("span",Yn,c(i.compression.decompressionTime||"--")+"ms",1)]),a("div",Qn,[e[112]||(e[112]=a("span",{class:"metric-label"},"总传输量",-1)),a("span",Xn,c(i.compression.totalTransferred||"--"),1)]),a("div",eo,[e[113]||(e[113]=a("span",{class:"metric-label"},"节省带宽",-1)),a("span",to,c(i.compression.bandwidthSaved||"--"),1)])]),d.compression.running?(b(),z("div",so,[s(Se,{percentage:d.compression.progress,status:d.compression.progress===100?"success":""},null,8,["percentage","status"]),a("span",ao,c(d.compression.currentStep),1)])):U("",!0),i.compression.chartData?(b(),z("div",lo,[a("div",no,[e[114]||(e[114]=a("h5",null,"压缩率对比",-1)),a("div",oo,[(b(!0),z(Ae,null,Pe(i.compression.chartData,(n,T)=>(b(),z("div",{class:"chart-bar",key:T,style:ya({height:n.ratio+"%"})},[a("span",ro,c(n.algorithm),1),a("span",io,c(n.ratio)+"%",1)],4))),128))])])])):U("",!0)])])])]),_:1})):U("",!0),Y.value?(b(),B(j,{key:1,class:"standard-card"},{header:l(()=>[a("div",uo,[e[117]||(e[117]=a("span",null,"测试报告",-1)),a("div",co,[s(p,{size:"small",onClick:ns},{default:l(()=>[s(S,null,{default:l(()=>[s(K(ba))]),_:1}),e[115]||(e[115]=m(" 导出报告 ",-1))]),_:1,__:[115]}),s(p,{size:"small",onClick:os},{default:l(()=>[s(S,null,{default:l(()=>[s(K(gt))]),_:1}),e[116]||(e[116]=m(" 清除结果 ",-1))]),_:1,__:[116]})])])]),default:l(()=>{var n,T;return[a("div",mo,[a("div",po,[e[121]||(e[121]=a("h4",null,"测试摘要",-1)),a("div",_o,[a("div",fo,[e[118]||(e[118]=a("span",{class:"label"},"测试配置:",-1)),a("span",vo,c((n=V.value)==null?void 0:n.name)+" ("+c((T=V.value)==null?void 0:T.tunnel_type.toUpperCase())+")",1)]),a("div",yo,[e[119]||(e[119]=a("span",{class:"label"},"测试时间:",-1)),a("span",bo,c(Ke(D.startTime))+" - "+c(Ke(D.endTime)),1)]),a("div",go,[e[120]||(e[120]=a("span",{class:"label"},"总体评分:",-1)),a("span",wo,c(D.overallScore)+"/100",1)])])]),a("div",ho,[e[122]||(e[122]=a("h4",null,"详细结果",-1)),s(me,{data:D.details,style:{width:"100%"}},{default:l(()=>[s(v,{prop:"testType",label:"测试类型",width:"150"}),s(v,{prop:"result",label:"测试结果",width:"200"}),s(v,{prop:"score",label:"评分",width:"100"}),s(v,{prop:"status",label:"状态",width:"100"},{default:l(bt=>[s($,{type:bt.row.status==="passed"?"success":"danger"},{default:l(()=>[m(c(bt.row.status==="passed"?"通过":"失败"),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"notes",label:"备注"})]),_:1},8,["data"])])])]}),_:1})):U("",!0)])]),_:1}),s(w,{label:"从设备管理",name:"slave"},{default:l(()=>[a("div",Co,[s(ft,{description:"从设备管理功能开发中，敬请期待"})])]),_:1}),s(w,{label:"数据压缩",name:"compression"},{default:l(()=>[a("div",To,[s(j,{class:"standard-card"},{header:l(()=>e[123]||(e[123]=[a("div",{class:"card-header"},[a("span",null,"压缩算法管理")],-1)])),default:l(()=>[s(me,{data:de.value,style:{width:"100%"}},{default:l(()=>[s(v,{prop:"display_name",label:"算法名称",width:"150"}),s(v,{prop:"description",label:"描述","min-width":"200"}),s(v,{prop:"available",label:"可用状态",width:"100",align:"center"},{default:l(n=>[s($,{type:n.row.available?"success":"danger"},{default:l(()=>[m(c(n.row.available?"可用":"不可用"),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"enabled",label:"启用状态",width:"100",align:"center"},{default:l(n=>[s(E,{modelValue:n.row.enabled,"onUpdate:modelValue":T=>n.row.enabled=T,onChange:T=>Fs(n.row),disabled:!n.row.available},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),s(v,{label:"操作",width:"200"},{default:l(n=>[s(p,{size:"small",onClick:T=>Ns(n.row),disabled:!n.row.available},{default:l(()=>e[124]||(e[124]=[m(" 配置 ",-1)])),_:2,__:[124]},1032,["onClick","disabled"]),s(p,{size:"small",type:"info",onClick:T=>js(n.row),disabled:!n.row.available||!n.row.enabled},{default:l(()=>e[125]||(e[125]=[m(" 测试 ",-1)])),_:2,__:[125]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])]),_:1}),s(j,{class:"standard-card",style:{"margin-top":"20px"}},{header:l(()=>[a("div",So,[e[127]||(e[127]=a("span",null,"压缩配置管理",-1)),s(p,{type:"primary",onClick:e[17]||(e[17]=n=>we.value=!0)},{default:l(()=>[s(S,null,{default:l(()=>[s(K(wt))]),_:1}),e[126]||(e[126]=m(" 新增配置 ",-1))]),_:1,__:[126]})])]),default:l(()=>[ht((b(),B(me,{data:L.value,style:{width:"100%"}},{default:l(()=>[s(v,{prop:"name",label:"配置名称",width:"150"}),s(v,{prop:"algorithm",label:"压缩算法",width:"120"},{default:l(n=>[s($,{type:Is(n.row.algorithm)},{default:l(()=>[m(c(Os(n.row.algorithm)),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"level",label:"压缩级别",width:"120"},{default:l(n=>[s($,{type:As(n.row.level)},{default:l(()=>[m(c(Ps(n.row.level)),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"stream_type",label:"数据流类型",width:"150"},{default:l(n=>[m(c(Ls(n.row.stream_type)),1)]),_:1}),s(v,{prop:"is_active",label:"状态",width:"100"},{default:l(n=>[s($,{type:n.row.is_active?"success":"info"},{default:l(()=>[m(c(n.row.is_active?"已启用":"未启用"),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"is_default",label:"默认",width:"80",align:"center"},{default:l(n=>[n.row.is_default?(b(),B(S,{key:0,color:"#67C23A"},{default:l(()=>[s(K(ga))]),_:1})):U("",!0)]),_:1}),s(v,{label:"操作",width:"280"},{default:l(n=>[n.row.is_active?(b(),B(p,{key:1,type:"warning",size:"small",onClick:T=>Ms(n.row.id),loading:Q.value===n.row.id},{default:l(()=>[m(c(Q.value===n.row.id?"停用中":"停用"),1)]),_:2},1032,["onClick","loading"])):(b(),B(p,{key:0,type:"success",size:"small",onClick:T=>Us(n.row.id),loading:Q.value===n.row.id},{default:l(()=>[m(c(Q.value===n.row.id?"切换中":"启用"),1)]),_:2},1032,["onClick","loading"])),s(p,{type:"primary",size:"small",onClick:T=>Ds(n.row)},{default:l(()=>e[128]||(e[128]=[m(" 编辑 ",-1)])),_:2,__:[128]},1032,["onClick"]),s(p,{type:"danger",size:"small",onClick:T=>Bs(n.row.id),disabled:n.row.is_default||n.row.is_active},{default:l(()=>e[129]||(e[129]=[m(" 删除 ",-1)])),_:2,__:[129]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[yt,We.value]])]),_:1})])]),_:1}),s(w,{label:"缓存清除",name:"cache"},{default:l(()=>[a("div",ko,[s(j,{class:"standard-card"},{header:l(()=>e[130]||(e[130]=[a("div",{class:"card-header"},[a("span",{class:"header-title"},"系统缓存管理"),a("span",{class:"header-subtitle"},"查看和清除各类系统缓存，解决页面显示异常问题")],-1)])),default:l(()=>[a("div",Vo,[s(Ys,{title:"缓存清除说明",type:"info",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}},{default:l(()=>e[131]||(e[131]=[a("p",null,"如果遇到页面显示异常、数据不更新等问题，可以尝试清除相应的缓存。",-1),a("p",null,[a("strong",null,"注意："),m("清除缓存可能会导致短暂的性能下降，建议在系统空闲时操作。")],-1)])),_:1}),ht((b(),B(me,{data:st.value,style:{width:"100%"},class:"standard-table"},{default:l(()=>[s(v,{prop:"name",label:"缓存类型",width:"200"},{default:l(({row:n})=>[a("div",xo,[s(S,{class:"cache-icon"},{default:l(()=>[(b(),B(ha(Zs(n.type))))]),_:2},1024),a("span",null,c(n.name),1)])]),_:1}),s(v,{prop:"description",label:"描述","min-width":"300"},{default:l(({row:n})=>[a("span",$o,c(n.description),1)]),_:1}),s(v,{prop:"status",label:"状态",width:"100"},{default:l(({row:n})=>[s($,{type:n.status==="active"?"success":n.status==="error"?"danger":"info",size:"small"},{default:l(()=>[m(c(Ks(n.status)),1)]),_:2},1032,["type"])]),_:1}),s(v,{prop:"size",label:"大小",width:"100"},{default:l(({row:n})=>[a("span",Do,c(n.size),1)]),_:1}),s(v,{label:"操作",width:"150"},{default:l(({row:n})=>[s(p,{type:"primary",size:"small",loading:G[n.type],onClick:T=>Ws(n.type),disabled:n.status==="inactive"},{default:l(()=>e[132]||(e[132]=[m(" 清除 ",-1)])),_:2,__:[132]},1032,["loading","onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[yt,qe.value]]),a("div",zo,[s(Qs,null,{default:l(()=>[s(p,{type:"warning",loading:G.all,onClick:Js,icon:"Delete"},{default:l(()=>e[133]||(e[133]=[m(" 清除所有服务器缓存 ",-1)])),_:1,__:[133]},8,["loading"]),s(p,{type:"info",loading:G.web,onClick:Hs,icon:"Refresh"},{default:l(()=>e[134]||(e[134]=[m(" 清除Web页面缓存 ",-1)])),_:1,__:[134]},8,["loading"]),s(p,{type:"success",onClick:qs,icon:"Refresh"},{default:l(()=>e[135]||(e[135]=[m(" 刷新缓存信息 ",-1)])),_:1,__:[135]})]),_:1})])])]),_:1})])]),_:1})]),_:1},8,["modelValue"])]),_:1}),s(pe,{modelValue:ee.value,"onUpdate:modelValue":e[24]||(e[24]=n=>ee.value=n),title:"创建内网穿透配置",width:"600px"},{footer:l(()=>[s(p,{onClick:e[23]||(e[23]=n=>ee.value=!1)},{default:l(()=>e[136]||(e[136]=[m("取消",-1)])),_:1,__:[136]}),s(p,{type:"primary",onClick:Pt},{default:l(()=>e[137]||(e[137]=[m("确定",-1)])),_:1,__:[137]})]),default:l(()=>[s(_,{model:P,"label-width":"120px"},{default:l(()=>[s(r,{label:"配置名称",required:""},{default:l(()=>[s(o,{modelValue:P.name,"onUpdate:modelValue":e[19]||(e[19]=n=>P.name=n),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),s(r,{label:"穿透类型",required:""},{default:l(()=>[s(O,{modelValue:P.tunnel_type,"onUpdate:modelValue":e[20]||(e[20]=n=>P.tunnel_type=n),placeholder:"请选择穿透类型"},{default:l(()=>[s(f,{label:"FRP",value:"frp"}),s(f,{label:"Linker",value:"linker"})]),_:1},8,["modelValue"])]),_:1}),s(r,{label:"配置数据"},{default:l(()=>[s(o,{modelValue:be.value,"onUpdate:modelValue":e[21]||(e[21]=n=>be.value=n),type:"textarea",rows:8,placeholder:"请输入JSON格式的配置数据"},null,8,["modelValue"])]),_:1}),s(r,{label:"启用配置"},{default:l(()=>[s(E,{modelValue:P.is_active,"onUpdate:modelValue":e[22]||(e[22]=n=>P.is_active=n)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(pe,{modelValue:C.value,"onUpdate:modelValue":e[31]||(e[31]=n=>C.value=n),title:"编辑内网穿透配置",width:"600px"},{footer:l(()=>[s(p,{onClick:e[30]||(e[30]=n=>C.value=!1)},{default:l(()=>e[139]||(e[139]=[m("取消",-1)])),_:1,__:[139]}),s(p,{type:"primary",onClick:Wt},{default:l(()=>e[140]||(e[140]=[m("保存",-1)])),_:1,__:[140]})]),default:l(()=>[s(_,{model:R,"label-width":"120px"},{default:l(()=>[s(r,{label:"配置名称",required:""},{default:l(()=>[s(o,{modelValue:R.name,"onUpdate:modelValue":e[25]||(e[25]=n=>R.name=n),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),s(r,{label:"穿透类型",required:""},{default:l(()=>[s(O,{modelValue:R.tunnel_type,"onUpdate:modelValue":e[26]||(e[26]=n=>R.tunnel_type=n),placeholder:"请选择穿透类型",disabled:""},{default:l(()=>[s(f,{label:"FRP",value:"frp"}),s(f,{label:"Linker",value:"linker"})]),_:1},8,["modelValue"])]),_:1}),s(r,{label:"配置数据"},{default:l(()=>[s(o,{modelValue:ge.value,"onUpdate:modelValue":e[27]||(e[27]=n=>ge.value=n),type:"textarea",rows:8,placeholder:"请输入JSON格式的配置数据"},null,8,["modelValue"])]),_:1}),s(r,{label:"启用配置"},{default:l(()=>[s(E,{modelValue:R.is_active,"onUpdate:modelValue":e[28]||(e[28]=n=>R.is_active=n),disabled:R.force_disabled},null,8,["modelValue","disabled"])]),_:1}),s(r,{label:"强制禁用"},{default:l(()=>[s(E,{modelValue:R.force_disabled,"onUpdate:modelValue":e[29]||(e[29]=n=>R.force_disabled=n)},null,8,["modelValue"]),e[138]||(e[138]=a("div",{class:"form-tip"},"启用后将完全阻止此配置被启动，用于安全防护",-1))]),_:1,__:[138]})]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(pe,{modelValue:we.value,"onUpdate:modelValue":e[41]||(e[41]=n=>we.value=n),title:"创建数据压缩配置",width:"700px"},{footer:l(()=>[a("span",Fo,[s(p,{onClick:e[40]||(e[40]=n=>we.value=!1)},{default:l(()=>e[149]||(e[149]=[m("取消",-1)])),_:1,__:[149]}),s(p,{type:"primary",onClick:$s,loading:De.value},{default:l(()=>[m(c(De.value?"创建中...":"创建配置"),1)]),_:1},8,["loading"])])]),default:l(()=>[s(_,{model:k,rules:et,ref_key:"compressionFormRef",ref:He,"label-width":"140px"},{default:l(()=>[s(r,{label:"配置名称",prop:"name"},{default:l(()=>[s(o,{modelValue:k.name,"onUpdate:modelValue":e[32]||(e[32]=n=>k.name=n),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),s(r,{label:"压缩算法",prop:"algorithm"},{default:l(()=>[s(O,{modelValue:k.algorithm,"onUpdate:modelValue":e[33]||(e[33]=n=>k.algorithm=n),placeholder:"请选择压缩算法",onChange:Vs},{default:l(()=>[(b(!0),z(Ae,null,Pe(de.value.filter(n=>n.available),n=>(b(),B(f,{key:n.name,label:n.display_name,value:n.name},{default:l(()=>[a("div",Uo,[a("span",null,c(n.display_name),1),a("span",Mo,c(n.description),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),Ce.value?(b(),z("div",Bo,[s($,{size:"small",type:"info"},{default:l(()=>[m(c(Ce.value.description),1)]),_:1})])):U("",!0)]),_:1}),s(r,{label:"压缩级别",prop:"level"},{default:l(()=>[s(vt,{modelValue:k.level,"onUpdate:modelValue":e[34]||(e[34]=n=>k.level=n)},{default:l(()=>[s(_e,{label:"low"},{default:l(()=>e[141]||(e[141]=[a("span",{class:"level-option"},[a("strong",null,"低压缩率"),m(" - 高速度，适合实时传输 ")],-1)])),_:1,__:[141]}),s(_e,{label:"medium"},{default:l(()=>e[142]||(e[142]=[a("span",{class:"level-option"},[a("strong",null,"中等压缩率"),m(" - 平衡速度与压缩效果 ")],-1)])),_:1,__:[142]}),s(_e,{label:"high"},{default:l(()=>e[143]||(e[143]=[a("span",{class:"level-option"},[a("strong",null,"高压缩率"),m(" - 低速度，适合大文件传输 ")],-1)])),_:1,__:[143]})]),_:1},8,["modelValue"])]),_:1}),s(r,{label:"数据流类型",prop:"stream_type"},{default:l(()=>[s(O,{modelValue:k.stream_type,"onUpdate:modelValue":e[35]||(e[35]=n=>k.stream_type=n),placeholder:"请选择数据流类型"},{default:l(()=>[s(f,{label:"Web数据 (包含HTTP和HTTPS流量)",value:"web_data"}),s(f,{label:"专属客户端数据 (VirtualHere USB和客户端通信)",value:"client_data"}),s(f,{label:"通用数据 (所有其他类型数据流)",value:"general_data"})]),_:1},8,["modelValue"])]),_:1}),s(Ie,{gutter:20},{default:l(()=>[s(Z,{span:12},{default:l(()=>[s(r,{label:"最大并发用户"},{default:l(()=>[s(ke,{modelValue:k.max_concurrent_users,"onUpdate:modelValue":e[36]||(e[36]=n=>k.max_concurrent_users=n),min:1,max:1e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),s(Z,{span:12},{default:l(()=>[s(r,{label:"带宽限制(Mbps)"},{default:l(()=>[s(ke,{modelValue:k.bandwidth_limit_mbps,"onUpdate:modelValue":e[37]||(e[37]=n=>k.bandwidth_limit_mbps=n),min:.1,max:100,step:.1,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(r,{label:"高级选项"},{default:l(()=>[s(Oe,{modelValue:k.auto_fallback,"onUpdate:modelValue":e[38]||(e[38]=n=>k.auto_fallback=n)},{default:l(()=>e[144]||(e[144]=[m("自动降级",-1)])),_:1,__:[144]},8,["modelValue"]),s(Oe,{modelValue:k.is_default,"onUpdate:modelValue":e[39]||(e[39]=n=>k.is_default=n)},{default:l(()=>e[145]||(e[145]=[m("设为默认配置",-1)])),_:1,__:[145]},8,["modelValue"])]),_:1}),ue.value?(b(),B(r,{key:0,label:"压缩效果预览"},{default:l(()=>[s(j,{class:"preview-card"},{default:l(()=>[a("div",Ro,[s(Ie,{gutter:20},{default:l(()=>[s(Z,{span:8},{default:l(()=>[a("div",Eo,[e[146]||(e[146]=a("span",{class:"preview-label"},"预估压缩率:",-1)),a("span",Io,c(ue.value.estimated_rate)+"%",1)])]),_:1}),s(Z,{span:8},{default:l(()=>[a("div",Oo,[e[147]||(e[147]=a("span",{class:"preview-label"},"预估速度:",-1)),a("span",Ao,c(ue.value.estimated_speed)+" MB/s",1)])]),_:1}),s(Z,{span:8},{default:l(()=>[a("div",Po,[e[148]||(e[148]=a("span",{class:"preview-label"},"内存使用:",-1)),a("span",Lo,c(ue.value.estimated_memory)+" KB",1)])]),_:1})]),_:1})])]),_:1})]),_:1})):U("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(pe,{modelValue:ie.value,"onUpdate:modelValue":e[51]||(e[51]=n=>ie.value=n),title:"编辑数据压缩配置",width:"700px"},{footer:l(()=>[a("span",No,[s(p,{onClick:e[50]||(e[50]=n=>ie.value=!1)},{default:l(()=>e[155]||(e[155]=[m("取消",-1)])),_:1,__:[155]}),s(p,{type:"primary",onClick:zs,loading:ze.value},{default:l(()=>[m(c(ze.value?"更新中...":"更新配置"),1)]),_:1},8,["loading"])])]),default:l(()=>[s(_,{model:x,rules:et,ref_key:"editCompressionFormRef",ref:tt,"label-width":"140px"},{default:l(()=>[s(r,{label:"配置名称",prop:"name"},{default:l(()=>[s(o,{modelValue:x.name,"onUpdate:modelValue":e[42]||(e[42]=n=>x.name=n),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1}),s(r,{label:"压缩算法",prop:"algorithm"},{default:l(()=>[s(O,{modelValue:x.algorithm,"onUpdate:modelValue":e[43]||(e[43]=n=>x.algorithm=n),placeholder:"请选择压缩算法"},{default:l(()=>[(b(!0),z(Ae,null,Pe(de.value.filter(n=>n.available),n=>(b(),B(f,{key:n.name,label:n.display_name,value:n.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(r,{label:"压缩级别",prop:"level"},{default:l(()=>[s(vt,{modelValue:x.level,"onUpdate:modelValue":e[44]||(e[44]=n=>x.level=n)},{default:l(()=>[s(_e,{label:"low"},{default:l(()=>e[150]||(e[150]=[m("低压缩率 (高速度)",-1)])),_:1,__:[150]}),s(_e,{label:"medium"},{default:l(()=>e[151]||(e[151]=[m("中等压缩率 (平衡)",-1)])),_:1,__:[151]}),s(_e,{label:"high"},{default:l(()=>e[152]||(e[152]=[m("高压缩率 (低速度)",-1)])),_:1,__:[152]})]),_:1},8,["modelValue"])]),_:1}),s(r,{label:"数据流类型",prop:"stream_type"},{default:l(()=>[s(O,{modelValue:x.stream_type,"onUpdate:modelValue":e[45]||(e[45]=n=>x.stream_type=n),placeholder:"请选择数据流类型"},{default:l(()=>[s(f,{label:"Web数据 (包含HTTP和HTTPS流量)",value:"web_data"}),s(f,{label:"专属客户端数据 (VirtualHere USB和客户端通信)",value:"client_data"}),s(f,{label:"通用数据 (所有其他类型数据流)",value:"general_data"})]),_:1},8,["modelValue"])]),_:1}),s(Ie,{gutter:20},{default:l(()=>[s(Z,{span:12},{default:l(()=>[s(r,{label:"最大并发用户"},{default:l(()=>[s(ke,{modelValue:x.max_concurrent_users,"onUpdate:modelValue":e[46]||(e[46]=n=>x.max_concurrent_users=n),min:1,max:1e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),s(Z,{span:12},{default:l(()=>[s(r,{label:"带宽限制(Mbps)"},{default:l(()=>[s(ke,{modelValue:x.bandwidth_limit_mbps,"onUpdate:modelValue":e[47]||(e[47]=n=>x.bandwidth_limit_mbps=n),min:.1,max:100,step:.1,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(r,{label:"高级选项"},{default:l(()=>[s(Oe,{modelValue:x.auto_fallback,"onUpdate:modelValue":e[48]||(e[48]=n=>x.auto_fallback=n)},{default:l(()=>e[153]||(e[153]=[m("自动降级",-1)])),_:1,__:[153]},8,["modelValue"]),s(Oe,{modelValue:x.is_default,"onUpdate:modelValue":e[49]||(e[49]=n=>x.is_default=n)},{default:l(()=>e[154]||(e[154]=[m("设为默认配置",-1)])),_:1,__:[154]},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(pe,{modelValue:Xe.value,"onUpdate:modelValue":e[52]||(e[52]=n=>Xe.value=n),title:"压缩效果预览",width:"900px"},{default:l(()=>[a("div",jo,[s(Ie,{gutter:20},{default:l(()=>[s(Z,{span:12},{default:l(()=>[s(j,null,{header:l(()=>e[156]||(e[156]=[a("span",null,"算法性能对比",-1)])),default:l(()=>[s(me,{data:Ot.value,style:{width:"100%"}},{default:l(()=>[s(v,{prop:"algorithm",label:"算法",width:"100"}),s(v,{prop:"compression_rate",label:"压缩率",width:"80"},{default:l(n=>[m(c(n.row.compression_rate)+"% ",1)]),_:1}),s(v,{prop:"speed",label:"速度",width:"100"},{default:l(n=>[m(c(n.row.speed)+" MB/s ",1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),s(Z,{span:12},{default:l(()=>[s(j,null,{header:l(()=>e[157]||(e[157]=[a("span",null,"网络影响分析",-1)])),default:l(()=>[a("div",Wo,[a("div",Jo,[e[158]||(e[158]=a("span",{class:"impact-label"},"带宽节省:",-1)),a("span",Ho,c(Je.value.bandwidth_saved)+"%",1)]),a("div",qo,[e[159]||(e[159]=a("span",{class:"impact-label"},"延迟增加:",-1)),a("span",Go,c(Je.value.latency_increase)+"ms",1)]),a("div",Zo,[e[160]||(e[160]=a("span",{class:"impact-label"},"CPU使用:",-1)),a("span",Ko,c(Je.value.cpu_usage)+"%",1)])])]),_:1})]),_:1})]),_:1})])]),_:1},8,["modelValue"]),s(pe,{modelValue:he.value,"onUpdate:modelValue":e[54]||(e[54]=n=>he.value=n),title:"导入压缩配置",width:"600px"},{footer:l(()=>[a("span",Yo,[s(p,{onClick:e[53]||(e[53]=n=>he.value=!1)},{default:l(()=>e[163]||(e[163]=[m("取消",-1)])),_:1,__:[163]}),s(p,{type:"primary",onClick:Es,loading:Ue.value},{default:l(()=>[m(c(Ue.value?"导入中...":"导入配置"),1)]),_:1},8,["loading"])])]),default:l(()=>[s(ea,{class:"upload-demo",drag:"","auto-upload":!1,"on-change":Rs,accept:".json"},{tip:l(()=>e[161]||(e[161]=[a("div",{class:"el-upload__tip"}," 只能上传 JSON 格式的配置文件 ",-1)])),default:l(()=>[s(S,{class:"el-icon--upload"},{default:l(()=>[s(K(xa))]),_:1}),e[162]||(e[162]=a("div",{class:"el-upload__text"},[m(" 将配置文件拖到此处，或"),a("em",null,"点击上传")],-1))]),_:1,__:[162]})]),_:1},8,["modelValue"])])}}},Sr=Ct(Qo,[["__scopeId","data-v-1efc7b7a"]]);export{Sr as default};
