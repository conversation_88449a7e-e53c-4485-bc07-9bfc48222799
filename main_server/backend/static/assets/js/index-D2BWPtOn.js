import{_ as Se}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                     *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css                 *//* empty css                 *//* empty css               *//* empty css                *//* empty css                  *//* empty css                 *//* empty css                       *//* empty css                        */import{u as Ee,r,aM as R,cz as $e,o as ze,c as _,d as t,e as l,y as N,z as C,w as s,af as Ue,k as Be,m as Ge,p as m,cJ as Ie,x as Le,s as u,b as Ne,i as d,d2 as Te,n as i,E as Me,c$ as Fe,cY as Re,cQ as Ae,cK as Pe,ah as qe,L as ne,t as n,d3 as Ke,d4 as ie,d5 as je,a4 as A,a5 as P,a1 as Je,a6 as Oe,cU as Qe,d6 as Ye,ab as He,ae as We,ac as Xe,ad as Ze,v as el,f as ll,j as tl,a3 as sl}from"./index-DygbpG-y.js";import{a as z}from"./index-DW19OD6L.js";const al={class:"slave-server-management"},ol={class:"page-header"},nl={class:"header-actions"},il={class:"stats-section"},dl={class:"stat-content"},rl={class:"stat-icon online"},ul={class:"stat-info"},vl={class:"stat-number"},cl={class:"stat-content"},pl={class:"stat-icon success"},_l={class:"stat-info"},ml={class:"stat-number"},fl={class:"stat-content"},gl={class:"stat-icon primary"},yl={class:"stat-info"},bl={class:"stat-number"},wl={class:"stat-content"},hl={class:"stat-icon warning"},kl={class:"stat-info"},Cl={class:"stat-number"},Vl={class:"main-content"},xl={class:"card-header"},Dl={class:"server-list"},Sl=["onClick"],El={class:"server-info"},$l={class:"server-name"},zl={class:"server-address"},Ul={class:"server-status"},Bl={key:0,class:"empty-state"},Gl={class:"card-header"},Il={class:"header-actions"},Ll={class:"device-list"},Nl={class:"device-name"},Tl={key:0,class:"server-detail"},Ml={class:"detail-item"},Fl={class:"detail-item"},Rl={class:"detail-item"},Al={class:"detail-item"},Pl={class:"detail-item"},ql={class:"server-actions"},Kl={key:1,class:"device-detail"},jl={class:"detail-item"},Jl={class:"detail-item"},Ol={class:"detail-item"},Ql={class:"detail-item"},Yl={class:"detail-item"},Hl={class:"detail-item"},Wl={key:2,class:"batch-operations"},Xl={class:"batch-info"},Zl={class:"batch-actions"},et={class:"group-management"},lt={class:"group-list"},tt=["onClick"],st={class:"group-info"},at={class:"group-name"},ot={class:"group-count"},nt={key:3,class:"empty-detail"},it={class:"selected-devices"},dt={__name:"index",setup(rt){const q=Ee(),de=Ne(),K=r(!1),re=r(!1),S=r(!1),b=r(""),T=r(""),U=r(null),v=r(null),g=r([]),B=r(null),H=r("group"),E=r(!1),G=r(!1),k=r([]),M=r([]),F=r([]),w=r({name:"",description:"",group_type:"custom"}),ue={name:[{required:!0,message:"请输入分组名称",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},c=R(()=>k.value.find(a=>a.id===U.value)),W=R(()=>b.value?k.value.filter(a=>a.name.toLowerCase().includes(b.value.toLowerCase())||a.ip_address.includes(b.value)):k.value),ve=R(()=>{let a=M.value;return U.value&&(a=a.filter(e=>e.slave_server_id===U.value)),T.value&&(a=a.filter(e=>e.device_type===T.value)),b.value&&(a=a.filter(e=>(e.device_name||"").toLowerCase().includes(b.value.toLowerCase())||(e.vendor_id||"").includes(b.value)||(e.product_id||"").includes(b.value))),a}),I=R(()=>({totalServers:k.value.length,onlineServers:k.value.filter(a=>a.status==="online").length,totalDevices:M.value.length,deviceGroups:F.value.length})),X=async()=>{K.value=!0;try{await Promise.all([Z(),ee(),j()]),u.success("数据刷新成功")}catch(a){console.error("刷新数据失败:",a),u.error("刷新数据失败")}finally{K.value=!1}},Z=async()=>{try{const a=await z.get("/api/v1/slave/list");a.data.success&&(k.value=a.data.data||[])}catch(a){console.error("加载从服务器列表失败:",a),u.error("加载从服务器列表失败")}},ee=async()=>{try{const a=[];for(const e of k.value)try{const y=await z.get(`/api/v1/slave/${e.id}/devices`);if(y.data.success&&y.data.data){const J=y.data.data.map(f=>({...f,slave_server_id:e.id,slave_server_name:e.name}));a.push(...J)}}catch(y){console.error(`加载服务器 ${e.name} 的设备失败:`,y)}M.value=a}catch(a){console.error("加载设备列表失败:",a)}},j=async()=>{try{const a=await z.get("/api/v1/device-groups/");a.data&&(F.value=a.data||[])}catch(a){console.error("加载设备分组失败:",a)}},ce=a=>{U.value=a.id,v.value=null,g.value=[]},pe=()=>{},_e=a=>{g.value=a,a.length===1?v.value=a[0]:v.value=null},me=a=>{v.value=a,g.value=[a]},fe=a=>{de.push(`/slave-servers/${a.id}`)},ge=async a=>{try{await sl.confirm(`确定要重启从服务器 "${a.name}" 吗？`,"确认重启",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await z.post(`/api/v1/slave/${a.id}/control`,{action:"restart",parameters:{}});e.data.status==="success"?(u.success(`从服务器 "${a.name}" 重启指令已发送`),await Z()):u.error(e.data.message||"重启失败")}catch(e){e!=="cancel"&&(console.error("重启从服务器失败:",e),u.error("重启失败"))}},le=a=>({dongle:"加密锁",printer:"打印机",camera:"摄像头",scanner:"扫描仪",other:"其他"})[a]||a||"未知",ye=a=>M.value.filter(e=>e.slave_server_id===a).length,be=a=>a?new Date(a).toLocaleString("zh-CN"):"N/A",we=async()=>{try{S.value=!0,(await z.post("/api/v1/device-groups/",w.value)).data.status==="success"&&(u.success("设备分组创建成功"),E.value=!1,w.value={name:"",description:"",group_type:"custom"},await j())}catch(a){console.error("创建设备分组失败:",a),u.error("创建设备分组失败")}finally{S.value=!1}},he=async()=>{if(!B.value||g.value.length===0){u.warning("请选择分组和设备");return}try{S.value=!0;const a=g.value.map(y=>y.id);(await z.post(`/api/v1/device-groups/${B.value}/devices/`,{device_ids:a})).data.status==="success"&&(u.success("设备已添加到分组"),G.value=!1,B.value=null,g.value=[],await j())}catch(a){console.error("添加设备到分组失败:",a),u.error("添加设备到分组失败")}finally{S.value=!1}},ke=a=>{u.info(`选择了分组: ${a.name}`)},Ce=a=>{u.info(`编辑分组: ${a.name}`)},Ve=()=>{u.info("批量更新状态功能开发中...")};return $e(k,async a=>{a.length>0&&await ee()},{deep:!0}),ze(()=>{X()}),(a,e)=>{const y=Te,J=Ue,f=Me,O=Be,p=Ge,V=qe,x=Pe,te=Ie,xe=Ye,$=Oe,se=Qe,h=Ze,Q=Xe,D=We,De=He,L=tl,ae=ll,oe=Le;return d(),_("div",al,[t("div",ol,[e[20]||(e[20]=t("div",{class:"header-content"},[t("h2",null,"从服务器管理中心"),t("p",null,"统一管理所有从服务器和USB设备")],-1)),t("div",nl,[l(J,{modelValue:H.value,"onUpdate:modelValue":e[0]||(e[0]=o=>H.value=o),class:"mode-switch",style:{"margin-right":"12px"}},{default:s(()=>[l(y,{label:"group"},{default:s(()=>e[16]||(e[16]=[i("按分组模式",-1)])),_:1,__:[16]}),l(y,{label:"server"},{default:s(()=>e[17]||(e[17]=[i("按从服务器模式",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"]),l(O,{modelValue:b.value,"onUpdate:modelValue":e[1]||(e[1]=o=>b.value=o),placeholder:"搜索设备或从服务器...",style:{width:"300px","margin-right":"12px"},clearable:"",onInput:pe},{prefix:s(()=>[l(f,null,{default:s(()=>[l(m(Fe))]),_:1})]),_:1},8,["modelValue"]),l(p,{type:"primary",onClick:X,loading:K.value},{default:s(()=>[l(f,null,{default:s(()=>[l(m(Re))]),_:1}),e[18]||(e[18]=i(" 刷新 ",-1))]),_:1,__:[18]},8,["loading"]),m(q).hasPermission("device.group")?(d(),N(p,{key:0,type:"success",onClick:e[2]||(e[2]=o=>E.value=!0)},{default:s(()=>[l(f,null,{default:s(()=>[l(m(Ae))]),_:1}),e[19]||(e[19]=i(" 创建分组 ",-1))]),_:1,__:[19]})):C("",!0)])]),t("div",il,[l(te,{gutter:16},{default:s(()=>[l(x,{span:6},{default:s(()=>[l(V,{class:"stat-card"},{default:s(()=>[t("div",dl,[t("div",rl,[l(f,null,{default:s(()=>[l(m(ne))]),_:1})]),t("div",ul,[t("div",vl,n(I.value.totalServers),1),e[21]||(e[21]=t("div",{class:"stat-label"},"从服务器总数",-1))])])]),_:1})]),_:1}),l(x,{span:6},{default:s(()=>[l(V,{class:"stat-card"},{default:s(()=>[t("div",cl,[t("div",pl,[l(f,null,{default:s(()=>[l(m(Ke))]),_:1})]),t("div",_l,[t("div",ml,n(I.value.onlineServers),1),e[22]||(e[22]=t("div",{class:"stat-label"},"在线服务器",-1))])])]),_:1})]),_:1}),l(x,{span:6},{default:s(()=>[l(V,{class:"stat-card"},{default:s(()=>[t("div",fl,[t("div",gl,[l(f,null,{default:s(()=>[l(m(ie))]),_:1})]),t("div",yl,[t("div",bl,n(I.value.totalDevices),1),e[23]||(e[23]=t("div",{class:"stat-label"},"USB设备总数",-1))])])]),_:1})]),_:1}),l(x,{span:6},{default:s(()=>[l(V,{class:"stat-card"},{default:s(()=>[t("div",wl,[t("div",hl,[l(f,null,{default:s(()=>[l(m(je))]),_:1})]),t("div",kl,[t("div",Cl,n(I.value.deviceGroups),1),e[24]||(e[24]=t("div",{class:"stat-label"},"设备分组",-1))])])]),_:1})]),_:1})]),_:1})]),t("div",Vl,[l(te,{gutter:16},{default:s(()=>[l(x,{span:6},{default:s(()=>[l(V,{class:"server-list-card"},{header:s(()=>[t("div",xl,[e[25]||(e[25]=t("span",null,"从服务器列表",-1)),l(xe,{value:I.value.onlineServers,max:99,class:"badge"},{default:s(()=>[l(f,null,{default:s(()=>[l(m(ne))]),_:1})]),_:1},8,["value"])])]),default:s(()=>[t("div",Dl,[(d(!0),_(A,null,P(W.value,o=>(d(),_("div",{key:o.id,class:Je(["server-item",{active:U.value===o.id,offline:o.status!=="online"}]),onClick:Y=>ce(o)},[t("div",El,[t("div",$l,n(o.name),1),t("div",zl,n(o.ip_address)+":"+n(o.port),1)]),t("div",Ul,[l($,{type:o.status==="online"?"success":"danger",size:"small"},{default:s(()=>[i(n(o.status==="online"?"在线":"离线"),1)]),_:2},1032,["type"])])],10,Sl))),128)),W.value.length===0?(d(),_("div",Bl,[l(se,{description:"暂无从服务器"})])):C("",!0)])]),_:1})]),_:1}),l(x,{span:12},{default:s(()=>[l(V,{class:"device-list-card"},{header:s(()=>[t("div",Gl,[t("span",null,n(c.value?`${c.value.name} - 设备列表`:"所有设备"),1),t("div",Il,[l(Q,{modelValue:T.value,"onUpdate:modelValue":e[3]||(e[3]=o=>T.value=o),placeholder:"筛选设备类型",style:{width:"150px","margin-right":"8px"},clearable:""},{default:s(()=>[l(h,{label:"全部",value:""}),l(h,{label:"加密锁",value:"dongle"}),l(h,{label:"打印机",value:"printer"}),l(h,{label:"摄像头",value:"camera"}),l(h,{label:"其他",value:"other"})]),_:1},8,["modelValue"])])])]),default:s(()=>[t("div",Ll,[l(De,{data:ve.value,style:{width:"100%"},loading:re.value,onSelectionChange:_e},{default:s(()=>[l(D,{type:"selection",width:"55"}),l(D,{prop:"device_name",label:"设备名称","min-width":"120"},{default:s(o=>[t("div",Nl,[l(f,{class:"device-icon"},{default:s(()=>[l(m(ie))]),_:1}),i(" "+n(o.row.device_name||"未知设备"),1)])]),_:1}),l(D,{prop:"device_type",label:"类型",width:"100"},{default:s(o=>[l($,{size:"small"},{default:s(()=>[i(n(le(o.row.device_type)),1)]),_:2},1024)]),_:1}),l(D,{prop:"status",label:"状态",width:"80"},{default:s(o=>[l($,{type:o.row.status==="available"?"success":"warning",size:"small"},{default:s(()=>[i(n(o.row.status==="available"?"可用":"占用"),1)]),_:2},1032,["type"])]),_:1}),l(D,{prop:"vendor_id",label:"VID",width:"80"}),l(D,{prop:"product_id",label:"PID",width:"80"}),l(D,{label:"操作",width:"120"},{default:s(o=>[l(p,{size:"small",onClick:Y=>me(o.row)},{default:s(()=>e[26]||(e[26]=[i(" 详情 ",-1)])),_:2,__:[26]},1032,["onClick"])]),_:1})]),_:1},8,["data","loading"])])]),_:1})]),_:1}),l(x,{span:6},{default:s(()=>[l(V,{class:"detail-panel"},{header:s(()=>e[27]||(e[27]=[t("div",{class:"card-header"},[t("span",null,"详情面板")],-1)])),default:s(()=>[c.value?(d(),_("div",Tl,[e[35]||(e[35]=t("h4",null,"从服务器信息",-1)),t("div",Ml,[e[28]||(e[28]=t("label",null,"名称：",-1)),t("span",null,n(c.value.name),1)]),t("div",Fl,[e[29]||(e[29]=t("label",null,"地址：",-1)),t("span",null,n(c.value.ip_address)+":"+n(c.value.port),1)]),t("div",Rl,[e[30]||(e[30]=t("label",null,"状态：",-1)),l($,{type:c.value.status==="online"?"success":"danger"},{default:s(()=>[i(n(c.value.status==="online"?"在线":"离线"),1)]),_:1},8,["type"])]),t("div",Al,[e[31]||(e[31]=t("label",null,"最后心跳：",-1)),t("span",null,n(be(c.value.last_seen)),1)]),t("div",Pl,[e[32]||(e[32]=t("label",null,"设备数量：",-1)),t("span",null,n(ye(c.value.id)),1)]),t("div",ql,[l(p,{type:"primary",size:"small",onClick:e[4]||(e[4]=o=>fe(c.value))},{default:s(()=>e[33]||(e[33]=[i(" 查看详情 ",-1)])),_:1,__:[33]}),m(q).hasPermission("slave.manage")?(d(),N(p,{key:0,type:"warning",size:"small",onClick:e[5]||(e[5]=o=>ge(c.value))},{default:s(()=>e[34]||(e[34]=[i(" 重启服务 ",-1)])),_:1,__:[34]})):C("",!0)])])):C("",!0),v.value?(d(),_("div",Kl,[e[42]||(e[42]=t("h4",null,"设备信息",-1)),t("div",jl,[e[36]||(e[36]=t("label",null,"设备名称：",-1)),t("span",null,n(v.value.device_name||"未知设备"),1)]),t("div",Jl,[e[37]||(e[37]=t("label",null,"设备类型：",-1)),t("span",null,n(le(v.value.device_type)),1)]),t("div",Ol,[e[38]||(e[38]=t("label",null,"厂商ID：",-1)),t("span",null,n(v.value.vendor_id||"N/A"),1)]),t("div",Ql,[e[39]||(e[39]=t("label",null,"产品ID：",-1)),t("span",null,n(v.value.product_id||"N/A"),1)]),t("div",Yl,[e[40]||(e[40]=t("label",null,"状态：",-1)),l($,{type:v.value.status==="available"?"success":"warning"},{default:s(()=>[i(n(v.value.status==="available"?"可用":"占用"),1)]),_:1},8,["type"])]),t("div",Hl,[e[41]||(e[41]=t("label",null,"物理位置：",-1)),t("span",null,n(v.value.physical_address||"N/A"),1)])])):C("",!0),g.value.length>0?(d(),_("div",Wl,[e[45]||(e[45]=t("h4",null,"批量操作",-1)),t("div",Xl," 已选择 "+n(g.value.length)+" 个设备 ",1),t("div",Zl,[l(p,{type:"primary",size:"small",onClick:e[6]||(e[6]=o=>G.value=!0)},{default:s(()=>e[43]||(e[43]=[i(" 添加到分组 ",-1)])),_:1,__:[43]}),l(p,{type:"warning",size:"small",onClick:Ve},{default:s(()=>e[44]||(e[44]=[i(" 批量更新状态 ",-1)])),_:1,__:[44]})])])):C("",!0),t("div",et,[e[48]||(e[48]=t("h4",null,"设备分组",-1)),t("div",lt,[(d(!0),_(A,null,P(F.value,o=>(d(),_("div",{key:o.id,class:"group-item",onClick:Y=>ke(o)},[t("div",st,[t("div",at,n(o.name),1),t("div",ot,n(o.device_count||0)+" 个设备",1)]),l(p,{size:"small",type:"text",onClick:el(Y=>Ce(o),["stop"])},{default:s(()=>e[46]||(e[46]=[i(" 编辑 ",-1)])),_:2,__:[46]},1032,["onClick"])],8,tt))),128))]),m(q).hasPermission("device.group")?(d(),N(p,{key:0,type:"primary",size:"small",style:{width:"100%","margin-top":"12px"},onClick:e[7]||(e[7]=o=>E.value=!0)},{default:s(()=>e[47]||(e[47]=[i(" 创建新分组 ",-1)])),_:1,__:[47]})):C("",!0)]),!c.value&&!v.value&&g.value.length===0?(d(),_("div",nt,[l(se,{description:"请选择从服务器或设备查看详情"})])):C("",!0)]),_:1})]),_:1})]),_:1})]),l(oe,{modelValue:E.value,"onUpdate:modelValue":e[12]||(e[12]=o=>E.value=o),title:"创建设备分组",width:"600px"},{footer:s(()=>[l(p,{onClick:e[11]||(e[11]=o=>E.value=!1)},{default:s(()=>e[49]||(e[49]=[i("取消",-1)])),_:1,__:[49]}),l(p,{type:"primary",onClick:we,loading:S.value},{default:s(()=>e[50]||(e[50]=[i("创建",-1)])),_:1,__:[50]},8,["loading"])]),default:s(()=>[l(ae,{model:w.value,rules:ue,ref:"groupFormRef","label-width":"100px"},{default:s(()=>[l(L,{label:"分组名称",prop:"name"},{default:s(()=>[l(O,{modelValue:w.value.name,"onUpdate:modelValue":e[8]||(e[8]=o=>w.value.name=o),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),l(L,{label:"分组描述",prop:"description"},{default:s(()=>[l(O,{modelValue:w.value.description,"onUpdate:modelValue":e[9]||(e[9]=o=>w.value.description=o),type:"textarea",placeholder:"请输入分组描述",rows:3},null,8,["modelValue"])]),_:1}),l(L,{label:"分组类型",prop:"group_type"},{default:s(()=>[l(Q,{modelValue:w.value.group_type,"onUpdate:modelValue":e[10]||(e[10]=o=>w.value.group_type=o),placeholder:"请选择分组类型"},{default:s(()=>[l(h,{label:"按服务器分组",value:"server"}),l(h,{label:"按设备类型分组",value:"type"}),l(h,{label:"自定义分组",value:"custom"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(oe,{modelValue:G.value,"onUpdate:modelValue":e[15]||(e[15]=o=>G.value=o),title:"添加设备到分组",width:"500px"},{footer:s(()=>[l(p,{onClick:e[14]||(e[14]=o=>G.value=!1)},{default:s(()=>e[51]||(e[51]=[i("取消",-1)])),_:1,__:[51]}),l(p,{type:"primary",onClick:he,loading:S.value},{default:s(()=>e[52]||(e[52]=[i("确定",-1)])),_:1,__:[52]},8,["loading"])]),default:s(()=>[l(ae,{"label-width":"100px"},{default:s(()=>[l(L,{label:"选择分组"},{default:s(()=>[l(Q,{modelValue:B.value,"onUpdate:modelValue":e[13]||(e[13]=o=>B.value=o),placeholder:"请选择设备分组"},{default:s(()=>[(d(!0),_(A,null,P(F.value,o=>(d(),N(h,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(L,{label:"选中设备"},{default:s(()=>[t("div",it,[(d(!0),_(A,null,P(g.value,o=>(d(),N($,{key:o.id,style:{margin:"2px"}},{default:s(()=>[i(n(o.device_name||"未知设备"),1)]),_:2},1024))),128))])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}},$t=Se(dt,[["__scopeId","data-v-8c8cce9d"]]);export{$t as default};
