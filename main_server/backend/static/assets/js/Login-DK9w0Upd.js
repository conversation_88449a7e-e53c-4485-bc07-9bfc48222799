import{_ as L}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                     *//* empty css                  *//* empty css                 */import{u as h,r as w,a as R,o as U,b as B,c as F,d as n,e as o,w as s,E as I,f as q,g as M,h as S,i as K,j as N,k as D,l as y,m as T,n as p,t as j,p as z,q as O,s as v}from"./index-CZgshpeE.js";import{g as A}from"./systemSettings-CLK8x4hn.js";const G={class:"login-container"},H={class:"login-box"},J={class:"login-header"},P={class:"logo"},Q={class:"register-section"},W={__name:"Login",setup(X){const i=B(),b=M(),m=h(),a=w(!1),_=w(),t=R({username:"",password:""}),k={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}]},u=async()=>{try{if(await _.value.validate(),a.value=!0,await m.userLogin({username:t.username,password:t.password})){const e=b.query.redirect||"/";i.push(e)}}catch(l){console.error("登录失败:",l)}finally{a.value=!1}},x=()=>{i.push("/register")},V=async()=>{var l;try{const e=await A();e.success&&((l=e.data)!=null&&l.url)?window.open(e.data.url,"_blank"):v.warning(e.message||"客户端下载链接未配置")}catch(e){console.error("获取下载链接失败:",e),v.error("获取下载链接失败，请稍后重试")}};return U(()=>{m.isLoggedIn&&i.push("/")}),(l,e)=>{const E=S("Monitor"),g=I,f=D,r=N,d=T,C=q;return K(),F("div",G,[n("div",H,[n("div",J,[n("div",P,[o(g,{class:"logo-icon"},{default:s(()=>[o(E)]),_:1}),e[2]||(e[2]=n("h1",{class:"logo-text"},"OmniLink 全联通系统",-1))]),e[3]||(e[3]=n("p",{class:"subtitle"},"USB设备远程共享与管理平台",-1))]),o(C,{ref_key:"loginFormRef",ref:_,model:t,rules:k,class:"login-form",size:"large"},{default:s(()=>[o(r,{prop:"username"},{default:s(()=>[o(f,{modelValue:t.username,"onUpdate:modelValue":e[0]||(e[0]=c=>t.username=c),placeholder:"请输入用户名","prefix-icon":"User",clearable:"",onKeyup:y(u,["enter"])},null,8,["modelValue"])]),_:1}),o(r,{prop:"password"},{default:s(()=>[o(f,{modelValue:t.password,"onUpdate:modelValue":e[1]||(e[1]=c=>t.password=c),type:"password",placeholder:"请输入密码","prefix-icon":"Lock","show-password":"",clearable:"",onKeyup:y(u,["enter"])},null,8,["modelValue"])]),_:1}),o(r,null,{default:s(()=>[o(d,{type:"primary",class:"login-button",loading:a.value,onClick:u},{default:s(()=>[p(j(a.value?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1}),o(r,null,{default:s(()=>[o(d,{type:"success",class:"download-button",onClick:V},{default:s(()=>[o(g,null,{default:s(()=>[o(z(O))]),_:1}),e[4]||(e[4]=p(" 下载专属客户端 ",-1))]),_:1,__:[4]})]),_:1}),o(r,null,{default:s(()=>[n("div",Q,[e[6]||(e[6]=n("span",null,"还没有账户？",-1)),o(d,{type:"text",onClick:x},{default:s(()=>e[5]||(e[5]=[p("注册新用户",-1)])),_:1,__:[5]})])]),_:1})]),_:1},8,["model"])])])}}},te=L(W,[["__scopeId","data-v-abdc02d5"]]);export{te as default};
