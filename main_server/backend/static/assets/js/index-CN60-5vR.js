import{_ as pe}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                     *//* empty css                *//* empty css                    *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                *//* empty css                       *//* empty css                  */import{aM as H,y,w as a,c as C,z as $,d as l,e as t,al as st,N as me,n as i,t as r,ak as it,a6 as ve,a4 as L,a5 as W,cZ as rt,c_ as dt,m as ge,E as fe,p as x,B as te,C as ae,R as ze,x as he,s as j,i as u,r as h,a as ie,cz as Ce,j as xe,af as ut,ag as $e,k as je,cO as ct,ac as Ee,ad as De,cM as _t,cN as Re,cL as pt,f as Se,am as re,o as mt,ah as vt,c$ as gt,aj as ft,cY as ht,d0 as yt,ab as bt,ae as wt,aa as kt,cR as Vt,d1 as Ct,O as zt,a3 as _e}from"./index-DZHxQJ7O.js";/* empty css                         *//* empty css                             *//* empty css                  *//* empty css                          *//* empty css                       */const xt={key:0,class:"registration-detail"},$t={class:"detail-section"},jt={class:"user-info"},Et={class:"user-name"},Dt={class:"detail-section"},Rt={class:"role-tags"},St={class:"reason-text"},Tt={class:"detail-section"},Ut={class:"review-reason"},qt={key:0,class:"detail-section"},At={class:"user-agent"},Bt={key:1,class:"detail-section"},It={class:"history-item"},Pt={class:"history-action"},Mt={class:"history-operator"},Nt={key:0,class:"history-note"},Lt={class:"dialog-footer"},Ot={__name:"RegistrationDetailDialog",props:{modelValue:{type:Boolean,default:!1},registration:{type:Object,default:()=>null}},emits:["update:modelValue","approve","reject"],setup(n,{emit:D}){const p=n,S=D,z=H({get:()=>p.modelValue,set:w=>S("update:modelValue",w)}),I=w=>({pending:"warning",approved:"success",rejected:"danger"})[w]||"info",f=w=>({pending:"待审核",approved:"已通过",rejected:"已拒绝"})[w]||"未知",P=w=>({提交申请:"primary",审核通过:"success",审核拒绝:"danger",修改申请:"warning"})[w]||"info",E=w=>w?new Date(w).toLocaleString("zh-CN"):"未知",T=()=>{S("approve",p.registration)},K=()=>{S("reject",p.registration)},O=()=>{j.info("跳转到用户详情页面")};return(w,_)=>{const F=me,b=st,m=it,d=ve,k=rt,q=dt,M=ge,G=fe,R=he;return u(),y(R,{modelValue:z.value,"onUpdate:modelValue":_[1]||(_[1]=c=>z.value=c),title:"注册申请详情",width:"700px","close-on-click-modal":!1},{footer:a(()=>[l("div",Lt,[t(M,{onClick:_[0]||(_[0]=c=>z.value=!1)},{default:a(()=>_[7]||(_[7]=[i("关闭",-1)])),_:1,__:[7]}),n.registration&&n.registration.status==="pending"?(u(),C(L,{key:0},[t(M,{type:"success",onClick:T},{default:a(()=>[t(G,null,{default:a(()=>[t(x(te))]),_:1}),_[8]||(_[8]=i(" 通过申请 ",-1))]),_:1,__:[8]}),t(M,{type:"danger",onClick:K},{default:a(()=>[t(G,null,{default:a(()=>[t(x(ae))]),_:1}),_[9]||(_[9]=i(" 拒绝申请 ",-1))]),_:1,__:[9]})],64)):n.registration&&n.registration.status==="approved"?(u(),y(M,{key:1,type:"info",onClick:O},{default:a(()=>[t(G,null,{default:a(()=>[t(x(ze))]),_:1}),_[10]||(_[10]=i(" 查看用户 ",-1))]),_:1,__:[10]})):$("",!0)])]),default:a(()=>[n.registration?(u(),C("div",xt,[l("div",$t,[_[2]||(_[2]=l("h4",null,"申请人信息",-1)),t(m,{column:2,border:""},{default:a(()=>[t(b,{label:"用户姓名"},{default:a(()=>[l("div",jt,[t(F,{size:32,src:n.registration.avatar},{default:a(()=>[i(r(n.registration.full_name.charAt(0)),1)]),_:1},8,["src"]),l("span",Et,r(n.registration.full_name),1)])]),_:1}),t(b,{label:"用户名"},{default:a(()=>[i(r(n.registration.username),1)]),_:1}),t(b,{label:"邮箱地址"},{default:a(()=>[i(r(n.registration.email),1)]),_:1}),t(b,{label:"手机号码"},{default:a(()=>[i(r(n.registration.phone),1)]),_:1})]),_:1})]),l("div",Dt,[_[3]||(_[3]=l("h4",null,"申请信息",-1)),t(m,{column:2,border:""},{default:a(()=>[t(b,{label:"申请组织"},{default:a(()=>[t(d,{type:"info"},{default:a(()=>[i(r(n.registration.requested_organization),1)]),_:1})]),_:1}),t(b,{label:"申请角色"},{default:a(()=>[l("div",Rt,[(u(!0),C(L,null,W(n.registration.requested_roles,c=>(u(),y(d,{key:c,size:"small",class:"role-tag"},{default:a(()=>[i(r(c),1)]),_:2},1024))),128))])]),_:1}),t(b,{label:"申请时间",span:2},{default:a(()=>[i(r(E(n.registration.created_at)),1)]),_:1}),t(b,{label:"申请理由",span:2},{default:a(()=>[l("div",St,r(n.registration.reason||"无"),1)]),_:1})]),_:1})]),l("div",Tt,[_[4]||(_[4]=l("h4",null,"审核状态",-1)),t(m,{column:2,border:""},{default:a(()=>[t(b,{label:"当前状态"},{default:a(()=>[t(d,{type:I(n.registration.status)},{default:a(()=>[i(r(f(n.registration.status)),1)]),_:1},8,["type"])]),_:1}),n.registration.approved_at||n.registration.rejected_at?(u(),y(b,{key:0,label:"审核时间"},{default:a(()=>[i(r(E(n.registration.approved_at||n.registration.rejected_at)),1)]),_:1})):$("",!0),n.registration.approved_by||n.registration.rejected_by?(u(),y(b,{key:1,label:"审核人员"},{default:a(()=>[i(r(n.registration.approved_by||n.registration.rejected_by),1)]),_:1})):$("",!0),n.registration.approve_reason||n.registration.reject_reason?(u(),y(b,{key:2,label:"审核意见",span:2},{default:a(()=>[l("div",Ut,r(n.registration.approve_reason||n.registration.reject_reason),1)]),_:1})):$("",!0)]),_:1})]),n.registration.additional_info?(u(),C("div",qt,[_[5]||(_[5]=l("h4",null,"附加信息",-1)),t(m,{border:""},{default:a(()=>[n.registration.ip_address?(u(),y(b,{key:0,label:"IP地址"},{default:a(()=>[i(r(n.registration.ip_address),1)]),_:1})):$("",!0),n.registration.user_agent?(u(),y(b,{key:1,label:"用户代理"},{default:a(()=>[l("div",At,r(n.registration.user_agent),1)]),_:1})):$("",!0),n.registration.referrer?(u(),y(b,{key:2,label:"推荐人"},{default:a(()=>[i(r(n.registration.referrer),1)]),_:1})):$("",!0)]),_:1})])):$("",!0),n.registration.history&&n.registration.history.length>0?(u(),C("div",Bt,[_[6]||(_[6]=l("h4",null,"操作历史",-1)),t(q,null,{default:a(()=>[(u(!0),C(L,null,W(n.registration.history,(c,N)=>(u(),y(k,{key:N,timestamp:E(c.timestamp),type:P(c.action)},{default:a(()=>[l("div",It,[l("div",Pt,r(c.action),1),l("div",Mt,"操作人："+r(c.operator),1),c.note?(u(),C("div",Nt,"备注："+r(c.note),1)):$("",!0)])]),_:2},1032,["timestamp","type"]))),128))]),_:1})])):$("",!0)])):$("",!0)]),_:1},8,["modelValue"])}}},Ft=pe(Ot,[["__scopeId","data-v-604989a9"]]),Gt={key:0,class:"review-content"},Ht={class:"applicant-summary"},Kt={class:"summary-header"},Yt={class:"summary-info"},Zt={class:"applicant-name"},Jt={class:"applicant-detail"},Qt={class:"summary-request"},Wt={class:"request-item"},Xt={class:"request-item"},ea={class:"form-hint"},ta={class:"form-hint"},aa={class:"form-hint"},la={class:"form-hint"},oa={class:"dialog-footer"},na={__name:"ReviewDialog",props:{modelValue:{type:Boolean,default:!1},registration:{type:Object,default:()=>null},action:{type:String,default:"approve"}},emits:["update:modelValue","confirm"],setup(n,{emit:D}){const p=n,S=D,z=h(null),I=h(!1),f=ie({result:"",reason:"",assigned_organization:null,assigned_roles:[],send_notification:!0,notes:""}),P=h([{id:1,name:"集团总部"},{id:2,name:"华北大区"},{id:3,name:"北京分公司"},{id:4,name:"技术部"},{id:5,name:"销售部"}]),E=h([{label:"普通用户",value:"普通用户",disabled:!1},{label:"管理员",value:"管理员",disabled:!1},{label:"超级管理员",value:"超级管理员",disabled:!0}]),T=H({get:()=>p.modelValue,set:m=>S("update:modelValue",m)}),K=H(()=>p.action==="approve"?"审核通过":"审核拒绝"),O=ie({reason:[{validator:(m,d,k)=>{p.action==="reject"&&!d.trim()?k(new Error("拒绝申请时必须填写拒绝理由")):k()},trigger:"blur"}],assigned_organization:[{validator:(m,d,k)=>{p.action==="approve"&&!d?k(new Error("请选择分配的组织")):k()},trigger:"change"}],assigned_roles:[{validator:(m,d,k)=>{p.action==="approve"&&(!d||d.length===0)?k(new Error("请选择至少一个角色")):k()},trigger:"change"}]}),w=()=>{Object.assign(f,{result:p.action==="approve"?"approved":"rejected",reason:"",assigned_organization:null,assigned_roles:[],send_notification:!0,notes:""}),z.value&&z.value.clearValidate()},_=()=>{if(p.registration&&p.action==="approve"){const m=P.value.find(d=>d.name===p.registration.requested_organization);m&&(f.assigned_organization=m.id),f.assigned_roles=[...p.registration.requested_roles]}},F=()=>{T.value=!1,w()},b=async()=>{if(z.value)try{await z.value.validate(),I.value=!0;const m={registration:p.registration,action:p.action,reason:f.reason,send_notification:f.send_notification,notes:f.notes};p.action==="approve"&&(m.assigned_organization=f.assigned_organization,m.assigned_roles=f.assigned_roles),await new Promise(d=>setTimeout(d,1e3)),S("confirm",m),T.value=!1,w()}catch(m){console.error("表单验证失败:",m)}finally{I.value=!1}};return Ce(()=>p.modelValue,m=>{m&&(w(),_())}),Ce(()=>p.action,()=>{p.modelValue&&(w(),_())}),(m,d)=>{const k=me,q=ve,M=$e,G=ut,R=xe,c=je,N=ct,Y=De,le=Ee,oe=Re,de=_t,X=pt,Z=Se,J=ge,ue=fe,ce=he;return u(),y(ce,{modelValue:T.value,"onUpdate:modelValue":d[6]||(d[6]=v=>T.value=v),title:K.value,width:"500px","close-on-click-modal":!1,onClose:F},{footer:a(()=>[l("div",oa,[t(J,{onClick:F},{default:a(()=>d[12]||(d[12]=[i("取消",-1)])),_:1,__:[12]}),t(J,{type:n.action==="approve"?"success":"danger",onClick:b,loading:I.value},{default:a(()=>[t(ue,null,{default:a(()=>[n.action==="approve"?(u(),y(x(te),{key:0})):(u(),y(x(ae),{key:1}))]),_:1}),i(" "+r(n.action==="approve"?"确认通过":"确认拒绝"),1)]),_:1},8,["type","loading"])])]),default:a(()=>[n.registration?(u(),C("div",Gt,[l("div",Ht,[l("div",Kt,[t(k,{size:40,src:n.registration.avatar},{default:a(()=>[i(r(n.registration.full_name.charAt(0)),1)]),_:1},8,["src"]),l("div",Yt,[l("div",Zt,r(n.registration.full_name),1),l("div",Jt,r(n.registration.username)+" | "+r(n.registration.email),1)])]),l("div",Qt,[l("div",Wt,[d[7]||(d[7]=l("span",{class:"request-label"},"申请组织：",-1)),t(q,{type:"info",size:"small"},{default:a(()=>[i(r(n.registration.requested_organization),1)]),_:1})]),l("div",Xt,[d[8]||(d[8]=l("span",{class:"request-label"},"申请角色：",-1)),(u(!0),C(L,null,W(n.registration.requested_roles,v=>(u(),y(q,{key:v,size:"small",class:"role-tag"},{default:a(()=>[i(r(v),1)]),_:2},1024))),128))])])]),t(Z,{ref_key:"formRef",ref:z,model:f,rules:O,"label-width":"100px","label-position":"left"},{default:a(()=>[t(R,{label:"审核结果",prop:"result"},{default:a(()=>[t(G,{modelValue:f.result,"onUpdate:modelValue":d[0]||(d[0]=v=>f.result=v),disabled:!0},{default:a(()=>[t(M,{label:n.action==="approve"?"approved":"rejected"},{default:a(()=>[i(r(n.action==="approve"?"通过申请":"拒绝申请"),1)]),_:1},8,["label"])]),_:1},8,["modelValue"])]),_:1}),t(R,{label:n.action==="approve"?"通过理由":"拒绝理由",prop:"reason"},{default:a(()=>[t(c,{modelValue:f.reason,"onUpdate:modelValue":d[1]||(d[1]=v=>f.reason=v),type:"textarea",rows:4,placeholder:n.action==="approve"?"请输入通过理由（可选）":"请输入拒绝理由"},null,8,["modelValue","placeholder"]),l("div",ea,[t(N,{size:"small",type:"info"},{default:a(()=>[i(r(n.action==="approve"?"通过理由将发送给申请人":"拒绝理由将发送给申请人，请详细说明原因"),1)]),_:1})])]),_:1},8,["label"]),n.action==="approve"?(u(),y(R,{key:0,label:"分配组织",prop:"assigned_organization"},{default:a(()=>[t(le,{modelValue:f.assigned_organization,"onUpdate:modelValue":d[2]||(d[2]=v=>f.assigned_organization=v),placeholder:"选择实际分配的组织",style:{width:"100%"}},{default:a(()=>[(u(!0),C(L,null,W(P.value,v=>(u(),y(Y,{key:v.id,label:v.name,value:v.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),l("div",ta,[t(N,{size:"small",type:"info"},{default:a(()=>d[9]||(d[9]=[i(" 可以分配到与申请不同的组织 ",-1)])),_:1,__:[9]})])]),_:1})):$("",!0),n.action==="approve"?(u(),y(R,{key:1,label:"分配角色",prop:"assigned_roles"},{default:a(()=>[t(de,{modelValue:f.assigned_roles,"onUpdate:modelValue":d[3]||(d[3]=v=>f.assigned_roles=v)},{default:a(()=>[(u(!0),C(L,null,W(E.value,v=>(u(),y(oe,{key:v.value,label:v.value,disabled:v.disabled},{default:a(()=>[i(r(v.label),1)]),_:2},1032,["label","disabled"]))),128))]),_:1},8,["modelValue"]),l("div",aa,[t(N,{size:"small",type:"info"},{default:a(()=>d[10]||(d[10]=[i(" 可以分配与申请不同的角色 ",-1)])),_:1,__:[10]})])]),_:1})):$("",!0),t(R,{label:"发送通知",prop:"send_notification"},{default:a(()=>[t(X,{modelValue:f.send_notification,"onUpdate:modelValue":d[4]||(d[4]=v=>f.send_notification=v),"active-text":"发送邮件通知","inactive-text":"不发送通知"},null,8,["modelValue"]),l("div",la,[t(N,{size:"small",type:"info"},{default:a(()=>d[11]||(d[11]=[i(" 审核结果将通过邮件通知申请人 ",-1)])),_:1,__:[11]})])]),_:1}),t(R,{label:"备注",prop:"notes"},{default:a(()=>[t(c,{modelValue:f.notes,"onUpdate:modelValue":d[5]||(d[5]=v=>f.notes=v),type:"textarea",rows:2,placeholder:"内部备注（申请人不可见）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])):$("",!0)]),_:1},8,["modelValue","title"])}}},sa=pe(na,[["__scopeId","data-v-f0a4070d"]]);function ia(n={}){return re({url:"/api/v1/user-registration/pending-users",method:"get",params:n,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}function ra(){return re({url:"/api/v1/user-registration/stats",method:"get",headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}function da(n,D={}){return re({url:`/api/v1/user-registration/approve/${n}`,method:"post",data:D,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}function ua(n,D){return re({url:`/api/v1/user-registration/reject/${n}`,method:"post",data:D,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}const ca={class:"user-registration-container"},_a={class:"page-header"},pa={class:"header-right"},ma={class:"filter-bar"},va={class:"filter-left"},ga={class:"filter-right"},fa={class:"stats-cards"},ha={class:"stat-content"},ya={class:"stat-icon pending"},ba={class:"stat-info"},wa={class:"stat-value"},ka={class:"stat-content"},Va={class:"stat-icon approved"},Ca={class:"stat-info"},za={class:"stat-value"},xa={class:"stat-content"},$a={class:"stat-icon rejected"},ja={class:"stat-info"},Ea={class:"stat-value"},Da={class:"stat-content"},Ra={class:"stat-icon total"},Sa={class:"stat-info"},Ta={class:"stat-value"},Ua={class:"card-header"},qa={class:"header-actions"},Aa={class:"table-wrapper"},Ba={class:"user-info"},Ia={class:"user-name"},Pa={class:"remark-content"},Ma={key:0,class:"remark-text"},Na={key:1,class:"no-remark"},La={class:"action-buttons-container"},Oa={class:"pagination-wrapper"},Fa={key:0,class:"approve-form"},Ga={class:"user-info"},Ha={class:"organization-selector"},Ka={key:0,class:"selected-organization"},Ya={class:"tree-container"},Za={class:"tree-node-content"},Ja=["onClick"],Qa={class:"node-text"},Wa=["onClick"],Xa={class:"dialog-footer"},el={__name:"index",setup(n){const D=h(""),p=h(""),S=h([]),z=h(1),I=h(20),f=h(0),P=h(!1),E=h([]),T=h(!1),K=h(!1),O=h(null),w=h(null),_=h(""),F=h(!1),b=h(!1),m=ie({pending:0,approved:0,rejected:0,total:0}),d=h([]),k=h(!1),q=h(null),M=h([]),G=h(null),R=h(null),c=ie({target_organization_id:null,permission_level:3,target_role_name:"普通用户",remark:""}),N={children:"children",label:"name"},Y=H(()=>{let o=d.value;if(p.value&&(o=o.filter(e=>e.status===p.value)),D.value){const e=D.value.toLowerCase();o=o.filter(g=>g.full_name.toLowerCase().includes(e)||g.username.toLowerCase().includes(e)||g.email.toLowerCase().includes(e))}if(S.value&&S.value.length===2){const[e,g]=S.value;o=o.filter(V=>{const A=new Date(V.created_at);return A>=e&&A<=g})}return o}),le=H(()=>E.value.length>0),oe=H(()=>c.target_organization_id&&c.permission_level&&c.target_role_name.trim()),de=H(()=>{const o=E.value.length,e=Y.value.length;return o>0&&o<e}),X=async()=>{F.value=!0;try{const o={page:z.value,page_size:I.value,search:D.value||void 0,status_filter:p.value||void 0},e=await ia(o);let g=e;e&&e.success&&e.data&&(console.log("🔧 loadRegistrations - 检测到API中间件包装格式，提取data字段"),g=e.data),console.log("loadRegistrations - 处理后的用户数据:",g);const V=Array.isArray(g)?g:(g==null?void 0:g.users)||[];d.value=V.map(A=>({...A,status:A.is_active?"approved":"pending",requested_organization:A.organization_name,requested_roles:[A.role_name],remark:A.approval_remark||null})),f.value=d.value.length}catch(o){console.error("加载用户列表失败:",o),j.error("加载用户列表失败")}finally{F.value=!1}},Z=async()=>{b.value=!0;try{const o=await ra();let e=o;o&&o.success&&o.data&&(console.log("🔧 loadStats - 检测到API中间件包装格式，提取data字段:",o.data),e=o.data),console.log("🔧 loadStats - 最终统计数据:",e),Object.assign(m,e)}catch(o){console.error("加载统计信息失败:",o),j.error("加载统计信息失败")}finally{b.value=!1}},J=()=>{Z()},ue=async()=>{try{M.value=[{id:1,name:"总部",children:[{id:2,name:"技术部",children:[{id:3,name:"前端组"},{id:4,name:"后端组"}]},{id:5,name:"销售部",children:[{id:6,name:"华北区"},{id:7,name:"华南区"}]}]}]}catch(o){console.error("加载组织树失败:",o),j.error("加载组织树失败")}},ce=o=>({pending:"warning",approved:"success",rejected:"danger"})[o]||"info",v=o=>({pending:"待审核",approved:"已通过",rejected:"已拒绝"})[o]||"未知",Te=o=>new Date(o).toLocaleString("zh-CN"),Ue=()=>{z.value=1},qe=()=>{D.value="",p.value="",S.value=[],z.value=1},ye=async()=>{await Promise.all([X(),Z()]),j.success("数据已刷新")},Ae=o=>{o?E.value=Y.value.slice():E.value=[]},Be=o=>{E.value=o,P.value=o.length===Y.value.length},Ie=o=>{O.value=o,T.value=!0},Pe=async o=>{q.value=o,c.target_organization_id=null,c.permission_level=3,c.target_role_name="普通用户",c.remark="",R.value=null,await ue(),k.value=!0},be=o=>{c.target_organization_id=o.id,R.value=o},Me=()=>{c.target_organization_id=null,R.value=null},Ne=o=>{o.childNodes&&o.childNodes.length>0&&(o.expanded=!o.expanded)},Le=async()=>{if(!oe.value){j.warning("请完整填写必填项");return}try{await da(q.value.id,{target_organization_id:c.target_organization_id,permission_level:c.permission_level,target_role_name:c.target_role_name,remark:c.remark}),j.success("用户审核通过"),k.value=!1,await X(),await Z()}catch{j.error("审核失败")}},we=async o=>{try{const{value:e}=await _e.prompt("请输入拒绝理由","拒绝申请",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/.+/,inputErrorMessage:"请输入拒绝理由"});await ua(o.id,{reason:e}),j.success("已拒绝用户申请"),await ye()}catch(e){e!=="cancel"&&(console.error("拒绝失败:",e),j.error("拒绝失败"))}},Oe=()=>{const o=E.value.filter(e=>e.status==="pending");if(o.length===0){j.warning("请选择待审核的申请");return}_e.confirm(`确定要批量通过 ${o.length} 个申请吗？`,"批量审核确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{o.forEach(e=>{e.status="approved",e.approved_at=new Date().toLocaleString("zh-CN"),e.approved_by="系统管理员"}),j.success(`已批量通过 ${o.length} 个申请`),E.value=[],P.value=!1,J()}).catch(()=>{})},Fe=()=>{const o=E.value.filter(e=>e.status==="pending");if(o.length===0){j.warning("请选择待审核的申请");return}_e.confirm(`确定要批量拒绝 ${o.length} 个申请吗？`,"批量审核确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{o.forEach(e=>{e.status="rejected",e.rejected_at=new Date().toLocaleString("zh-CN"),e.rejected_by="系统管理员"}),j.success(`已批量拒绝 ${o.length} 个申请`),E.value=[],P.value=!1,J()}).catch(()=>{})},Ge=o=>{handleApprove(o),T.value=!1},He=o=>{we(o),T.value=!1},Ke=o=>{const{registration:e,action:g,reason:V}=o;g==="approve"?(e.status="approved",e.approved_at=new Date().toLocaleString("zh-CN"),e.approved_by="系统管理员",e.approve_reason=V,j.success("申请已通过")):(e.status="rejected",e.rejected_at=new Date().toLocaleString("zh-CN"),e.rejected_by="系统管理员",e.reject_reason=V,j.success("申请已拒绝")),J()},Ye=o=>{O.value=o,T.value=!0},Ze=o=>{I.value=o,z.value=1},Je=o=>{z.value=o};return mt(async()=>{await Promise.all([X(),Z()])}),(o,e)=>{const g=fe,V=ge,A=je,ee=De,ke=Ee,Qe=ft,Q=vt,We=Re,B=wt,Xe=me,ne=ve,et=bt,tt=kt,at=$e,lt=Vt,se=xe,ot=Se,nt=he;return u(),C("div",ca,[l("div",_a,[e[15]||(e[15]=l("div",{class:"header-left"},[l("h2",null,"新用户注册审核"),l("p",{class:"header-desc"},"审核和管理新用户注册申请，支持批量操作")],-1)),l("div",pa,[t(V,{type:"success",disabled:!le.value,onClick:Oe},{default:a(()=>[t(g,null,{default:a(()=>[t(x(te))]),_:1}),e[13]||(e[13]=i(" 批量通过 ",-1))]),_:1,__:[13]},8,["disabled"]),t(V,{type:"danger",disabled:!le.value,onClick:Fe},{default:a(()=>[t(g,null,{default:a(()=>[t(x(ae))]),_:1}),e[14]||(e[14]=i(" 批量拒绝 ",-1))]),_:1,__:[14]},8,["disabled"])])]),t(Q,{class:"filter-card",shadow:"never"},{default:a(()=>[l("div",ma,[l("div",va,[t(A,{modelValue:D.value,"onUpdate:modelValue":e[0]||(e[0]=s=>D.value=s),placeholder:"搜索用户姓名、用户名或邮箱...",style:{width:"300px"},clearable:"",onInput:Ue},{prefix:a(()=>[t(g,null,{default:a(()=>[t(x(gt))]),_:1})]),_:1},8,["modelValue"]),t(ke,{modelValue:p.value,"onUpdate:modelValue":e[1]||(e[1]=s=>p.value=s),placeholder:"筛选状态",clearable:"",style:{width:"150px"}},{default:a(()=>[t(ee,{label:"待审核",value:"pending"}),t(ee,{label:"已通过",value:"approved"}),t(ee,{label:"已拒绝",value:"rejected"})]),_:1},8,["modelValue"]),t(Qe,{modelValue:S.value,"onUpdate:modelValue":e[2]||(e[2]=s=>S.value=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"240px"}},null,8,["modelValue"])]),l("div",ga,[t(V,{onClick:qe},{default:a(()=>e[16]||(e[16]=[i("重置筛选",-1)])),_:1,__:[16]}),t(V,{type:"primary",onClick:ye},{default:a(()=>[t(g,null,{default:a(()=>[t(x(ht))]),_:1}),e[17]||(e[17]=i(" 刷新 ",-1))]),_:1,__:[17]})])])]),_:1}),l("div",fa,[t(Q,{class:"stat-card"},{default:a(()=>[l("div",ha,[l("div",ya,[t(g,null,{default:a(()=>[t(x(yt))]),_:1})]),l("div",ba,[l("div",wa,r(m.pending),1),e[18]||(e[18]=l("div",{class:"stat-label"},"待审核",-1))])])]),_:1}),t(Q,{class:"stat-card"},{default:a(()=>[l("div",ka,[l("div",Va,[t(g,null,{default:a(()=>[t(x(te))]),_:1})]),l("div",Ca,[l("div",za,r(m.approved),1),e[19]||(e[19]=l("div",{class:"stat-label"},"已通过",-1))])])]),_:1}),t(Q,{class:"stat-card"},{default:a(()=>[l("div",xa,[l("div",$a,[t(g,null,{default:a(()=>[t(x(ae))]),_:1})]),l("div",ja,[l("div",Ea,r(m.rejected),1),e[20]||(e[20]=l("div",{class:"stat-label"},"已拒绝",-1))])])]),_:1}),t(Q,{class:"stat-card"},{default:a(()=>[l("div",Da,[l("div",Ra,[t(g,null,{default:a(()=>[t(x(ze))]),_:1})]),l("div",Sa,[l("div",Ta,r(m.total),1),e[21]||(e[21]=l("div",{class:"stat-label"},"总申请",-1))])])]),_:1})]),t(Q,null,{header:a(()=>[l("div",Ua,[e[23]||(e[23]=l("span",null,"注册申请列表",-1)),l("div",qa,[t(We,{modelValue:P.value,"onUpdate:modelValue":e[3]||(e[3]=s=>P.value=s),indeterminate:de.value,onChange:Ae},{default:a(()=>e[22]||(e[22]=[i(" 全选 ",-1)])),_:1,__:[22]},8,["modelValue","indeterminate"])])])]),default:a(()=>[l("div",Aa,[t(et,{data:Y.value,style:{width:"100%"},onSelectionChange:Be,"table-layout":"auto"},{default:a(()=>[t(B,{type:"selection",width:"50"}),t(B,{prop:"full_name",label:"用户姓名",width:"120"},{default:a(s=>[l("div",Ba,[t(Xe,{size:28,src:s.row.avatar},{default:a(()=>[i(r(s.row.full_name.charAt(0)),1)]),_:2},1032,["src"]),l("span",Ia,r(s.row.full_name),1)])]),_:1}),t(B,{prop:"username",label:"用户名",width:"140","show-overflow-tooltip":""}),t(B,{prop:"email",label:"邮箱",width:"180","show-overflow-tooltip":""}),t(B,{prop:"phone",label:"手机号",width:"120"}),t(B,{prop:"requested_organization",label:"申请组织",width:"100"},{default:a(s=>[t(ne,{type:"info",size:"small"},{default:a(()=>[i(r(s.row.requested_organization),1)]),_:2},1024)]),_:1}),t(B,{prop:"requested_roles",label:"申请角色",width:"90"},{default:a(s=>[(u(!0),C(L,null,W(s.row.requested_roles,U=>(u(),y(ne,{key:U,size:"small",class:"role-tag"},{default:a(()=>[i(r(U),1)]),_:2},1024))),128))]),_:1}),t(B,{prop:"status",label:"状态",width:"80"},{default:a(s=>[t(ne,{type:ce(s.row.status),size:"small"},{default:a(()=>[i(r(v(s.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(B,{prop:"created_at",label:"申请时间",width:"140"},{default:a(s=>[i(r(Te(s.row.created_at)),1)]),_:1}),t(B,{prop:"remark",label:"审核备注","min-width":"300","show-overflow-tooltip":""},{default:a(s=>[l("div",Pa,[s.row.remark?(u(),C("span",Ma,r(s.row.remark),1)):(u(),C("span",Na,"暂无备注"))])]),_:1}),t(B,{label:"操作",width:"200",fixed:"right"},{default:a(s=>[l("div",La,[t(V,{size:"small",onClick:U=>Ie(s.row),class:"action-btn view-btn"},{default:a(()=>e[24]||(e[24]=[i(" 查看 ",-1)])),_:2,__:[24]},1032,["onClick"]),s.row.status==="pending"?(u(),C(L,{key:0},[t(V,{size:"small",type:"success",onClick:U=>Pe(s.row),class:"action-btn approve-btn"},{default:a(()=>e[25]||(e[25]=[i(" 审核通过 ",-1)])),_:2,__:[25]},1032,["onClick"]),t(V,{size:"small",type:"danger",onClick:U=>we(s.row),class:"action-btn reject-btn"},{default:a(()=>e[26]||(e[26]=[i(" 拒绝 ",-1)])),_:2,__:[26]},1032,["onClick"])],64)):(u(),y(V,{key:1,size:"small",type:"info",onClick:U=>Ye(s.row),class:"action-btn result-btn"},{default:a(()=>e[27]||(e[27]=[i(" 查看结果 ",-1)])),_:2,__:[27]},1032,["onClick"]))])]),_:1})]),_:1},8,["data"])]),l("div",Oa,[t(tt,{"current-page":z.value,"onUpdate:currentPage":e[4]||(e[4]=s=>z.value=s),"page-size":I.value,"onUpdate:pageSize":e[5]||(e[5]=s=>I.value=s),"page-sizes":[10,20,50,100],total:f.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ze,onCurrentChange:Je},null,8,["current-page","page-size","total"])])]),_:1}),t(Ft,{modelValue:T.value,"onUpdate:modelValue":e[6]||(e[6]=s=>T.value=s),registration:O.value,onApprove:Ge,onReject:He},null,8,["modelValue","registration"]),t(sa,{modelValue:K.value,"onUpdate:modelValue":e[7]||(e[7]=s=>K.value=s),registration:w.value,action:_.value,onConfirm:Ke},null,8,["modelValue","registration","action"]),t(nt,{modelValue:k.value,"onUpdate:modelValue":e[12]||(e[12]=s=>k.value=s),title:"用户审核 - 分配角色和组织",width:"600px","close-on-click-modal":!1},{footer:a(()=>[l("span",Xa,[t(V,{onClick:e[11]||(e[11]=s=>k.value=!1)},{default:a(()=>e[37]||(e[37]=[i("取消",-1)])),_:1,__:[37]}),t(V,{type:"primary",onClick:Le,disabled:!oe.value},{default:a(()=>e[38]||(e[38]=[i(" 确认通过 ",-1)])),_:1,__:[38]},8,["disabled"])])]),default:a(()=>[q.value?(u(),C("div",Fa,[l("div",Ga,[e[32]||(e[32]=l("h4",null,"用户信息",-1)),l("p",null,[e[28]||(e[28]=l("strong",null,"姓名：",-1)),i(r(q.value.full_name),1)]),l("p",null,[e[29]||(e[29]=l("strong",null,"用户名：",-1)),i(r(q.value.username),1)]),l("p",null,[e[30]||(e[30]=l("strong",null,"邮箱：",-1)),i(r(q.value.email),1)]),l("p",null,[e[31]||(e[31]=l("strong",null,"手机号：",-1)),i(r(q.value.phone),1)])]),t(ot,{model:c,"label-width":"120px",class:"approve-form-content"},{default:a(()=>[t(se,{label:"目标组织",required:""},{default:a(()=>[l("div",Ha,[R.value?(u(),C("div",Ka,[t(ne,{type:"success",size:"large"},{default:a(()=>[t(g,null,{default:a(()=>[t(x(te))]),_:1}),i(" "+r(R.value.name),1)]),_:1}),t(V,{type:"text",size:"small",onClick:Me,class:"clear-btn"},{default:a(()=>[t(g,null,{default:a(()=>[t(x(ae))]),_:1})]),_:1})])):$("",!0),l("div",Ya,[t(lt,{ref_key:"organizationTreeRef",ref:G,data:M.value,props:N,"node-key":"id","expand-on-click-node":!1,"check-on-click-node":!1,"highlight-current":!1,class:"organization-tree"},{default:a(({node:s,data:U})=>[l("div",Za,[l("div",{class:"node-label",onClick:Ve=>Ne(s)},[U.children&&U.children.length>0?(u(),y(g,{key:0,class:"expand-icon"},{default:a(()=>[s.expanded?(u(),y(x(zt),{key:1})):(u(),y(x(Ct),{key:0}))]),_:2},1024)):$("",!0),l("span",Qa,r(U.name),1)],8,Ja),l("div",{class:"node-selector",onClick:Ve=>be(U)},[t(at,{"model-value":c.target_organization_id,label:U.id,onChange:Ve=>be(U),class:"org-radio"},{default:a(()=>e[33]||(e[33]=[l("span",null,null,-1)])),_:2,__:[33]},1032,["model-value","label","onChange"])],8,Wa)])]),_:1},8,["data"])])]),e[34]||(e[34]=l("div",{class:"form-help"},"必须为用户分配具体的组织层级",-1))]),_:1,__:[34]}),t(se,{label:"权限级别",required:""},{default:a(()=>[t(ke,{modelValue:c.permission_level,"onUpdate:modelValue":e[8]||(e[8]=s=>c.permission_level=s),placeholder:"请选择权限级别",style:{width:"100%"}},{default:a(()=>[t(ee,{label:"管理员 (级别2)",value:2}),t(ee,{label:"普通用户 (级别3)",value:3})]),_:1},8,["modelValue"]),e[35]||(e[35]=l("div",{class:"form-help"},"权限级别决定用户在系统中的操作范围",-1))]),_:1,__:[35]}),t(se,{label:"角色名称",required:""},{default:a(()=>[t(A,{modelValue:c.target_role_name,"onUpdate:modelValue":e[9]||(e[9]=s=>c.target_role_name=s),placeholder:"请输入角色名称"},null,8,["modelValue"]),e[36]||(e[36]=l("div",{class:"form-help"},"角色名称应与权限级别匹配",-1))]),_:1,__:[36]}),t(se,{label:"审核备注"},{default:a(()=>[t(A,{modelValue:c.remark,"onUpdate:modelValue":e[10]||(e[10]=s=>c.remark=s),type:"textarea",rows:3,placeholder:"可选：审核备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])):$("",!0)]),_:1},8,["modelValue"])])}}},Cl=pe(el,[["__scopeId","data-v-46761b79"]]);export{Cl as default};
