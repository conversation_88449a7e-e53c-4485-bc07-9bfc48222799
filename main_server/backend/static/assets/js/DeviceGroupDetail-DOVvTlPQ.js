import{_ as le}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css                 *//* empty css               *//* empty css                *//* empty css                  *//* empty css                         */import{r as T,g as ie,a as de,aM as $,o as re,bL as ce,c as z,d as t,a8 as _e,e as s,w as o,m as ue,t as l,a6 as ve,M as pe,a9 as fe,ah as me,cJ as he,s as u,b as ge,i as w,n as c,E as be,p as _,da as we,y as O,d5 as ye,dn as ke,cY as De,O as xe,P as Ce,Q as Ee,L as G,D as Te,d3 as $e,R as Be,cK as Ie,dk as Se,dw as Me,ab as ze,ae as Ae,z as Ve,k as Ne,c$ as je,ac as Le,ad as Ue,a3 as A}from"./index-DZHxQJ7O.js";const Re={class:"device-group-detail"},Oe={class:"detail-header"},Ge={class:"header-left"},Pe={class:"group-title"},He={class:"header-right"},Je={class:"detail-content"},Fe={class:"overview-cards"},Ke={class:"card-content"},Qe={class:"card-icon devices"},Ye={class:"card-info"},qe={class:"card-value"},We={class:"card-content"},Xe={class:"card-icon online"},Ze={class:"card-info"},et={class:"card-value"},tt={class:"card-content"},st={class:"card-icon servers"},at={class:"card-info"},ot={class:"card-value"},nt={class:"card-content"},lt={class:"card-icon users"},it={class:"card-info"},dt={class:"card-value"},rt={class:"card-header"},ct={class:"info-content"},_t={class:"info-item"},ut={class:"info-value"},vt={class:"info-item"},pt={class:"info-value"},ft={class:"info-item"},mt={class:"info-value"},ht={class:"info-item"},gt={class:"info-value"},bt={class:"info-item"},wt={class:"card-header"},yt={class:"info-content"},kt={class:"info-item"},Dt={class:"info-value"},xt={class:"info-item"},Ct={class:"info-value"},Et={class:"info-item"},Tt={class:"info-value"},$t={class:"info-item"},Bt={class:"info-value"},It={class:"info-item"},St={class:"info-value"},Mt={class:"card-header"},zt={class:"header-actions"},At={class:"device-list"},Vt={class:"device-name"},Nt={class:"primary-name"},jt={key:0,class:"secondary-name"},Lt={class:"device-source"},Ut={class:"source-primary"},Rt={class:"source-secondary"},Ot={class:"vid-pid"},Gt={__name:"DeviceGroupDetail",setup(Pt){const P=ie(),B=ge(),g=T(!1),m=P.params.groupId,y=T(""),k=T(""),r=de({id:null,name:"",description:"",device_count:0,user_count:0,status:"active",created_at:null,updated_at:null}),h=T([]);let I=null;const V=$(()=>h.value.filter(a=>a.status==="online").length),H=$(()=>h.value.filter(a=>a.status==="offline").length),N=$(()=>new Set(h.value.map(e=>e.server_id)).size),J=$(()=>{let a=h.value;if(y.value){const e=y.value.toLowerCase();a=a.filter(d=>d.device_name&&d.device_name.toLowerCase().includes(e))}return k.value&&(a=a.filter(e=>e.status===k.value)),a}),F=()=>{B.push("/device-center?tab=groups")},j=a=>({active:"success",inactive:"info",disabled:"danger"})[a]||"info",K=a=>({active:"活跃",inactive:"非活跃",disabled:"已禁用"})[a]||"未知",Q=a=>({online:"success",offline:"info",busy:"warning",error:"danger"})[a]||"info",Y=a=>({online:"在线",offline:"离线",busy:"占用中",error:"错误"})[a]||"未知",S=a=>a?new Date(a).toLocaleString():"N/A",L=a=>a?`0x${a.toString(16).toUpperCase().padStart(4,"0")}`:"N/A",D=async()=>{g.value=!0;try{const a=await fetch(`/api/v1/device-groups/${m}`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const e=await a.json();Object.assign(r,{id:e.id||m,name:e.name||"未命名分组",description:e.description||"",device_count:e.device_count||0,user_count:e.user_count||0,status:e.status||"active",created_at:e.created_at,updated_at:e.updated_at});const d=await fetch(`/api/v1/device-groups/${m}/devices`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(d.ok){const p=await d.json(),b=p.devices||p||[];h.value=b.map(i=>{var x,C,v;return{id:i.id,device_id:i.device_id,device_name:i.device_name,custom_name:i.custom_name,server_id:i.slave_server_id,server_name:((x=i.slave_server)==null?void 0:x.name)||"Unknown Server",server_ip:((C=i.slave_server)==null?void 0:C.ip_address)||"N/A",server_port:((v=i.slave_server)==null?void 0:v.port)||"N/A",physical_port:i.physical_port,usb_port:i.physical_port,vendor_id:i.vendor_id?parseInt(i.vendor_id,16):null,product_id:i.product_id?parseInt(i.product_id,16):null,status:i.status,last_connected:i.last_used_at||i.connected_at}})}else h.value=[];g.value=!1}catch(a){console.error("获取分组详情失败:",a),u.error("获取分组详情失败"),g.value=!1}},q=async a=>{switch(a){case"edit":u.info("编辑分组功能开发中...");break;case"add-device":await W();break;case"remove-device":u.info("请在设备列表中选择要移除的设备");break;case"delete":await X();break}},W=async()=>{try{const a=await fetch(`/api/v1/devices?exclude_group=${m}`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error("获取设备列表失败");const e=await a.json(),d=e.devices||e||[];if(d.length===0){u.warning("没有可添加的设备");return}await A.confirm(`找到 ${d.length} 个可添加的设备，是否继续？`,"添加设备",{confirmButtonText:"继续",cancelButtonText:"取消",type:"info"}),u.info("设备选择对话框功能开发中...")}catch(a){a.message&&a.message!=="cancel"&&u.error(a.message)}},X=async()=>{try{if(await A.confirm(`确定要删除分组 "${r.name}" 吗？此操作不可恢复！`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error"}),!(await fetch(`/api/v1/device-groups/${m}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}})).ok)throw new Error("删除分组失败");u.success("分组删除成功"),B.push("/device-center/device-groups")}catch(a){a.message&&a.message!=="cancel"&&u.error(a.message)}},Z=a=>{u.info(`查看设备详情: ${a.device_name}`)},ee=a=>{B.push(`/device-center/slave-server/${a.server_id}`)},te=async a=>{try{await A.confirm(`确定要从分组中移除设备 "${a.custom_name||a.device_name}" 吗？`,"确认移除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await fetch(`/api/v1/device-groups/${m}/devices`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify({device_ids:[a.id]})});if(!e.ok)throw new Error(`移除失败: ${e.statusText}`);u.success(`设备 "${a.custom_name||a.device_name}" 已从分组中移除`),D()}catch(e){e.message&&e.message!=="cancel"&&u.error(e.message)}};return re(()=>{D(),I=setInterval(D,3e4)}),ce(()=>{I&&clearInterval(I)}),(a,e)=>{const d=be,p=ue,b=ve,i=Ee,x=Ce,C=pe,v=me,M=Ie,U=he,se=Ne,E=Ue,ae=Le,f=Ae,oe=ze,ne=fe;return w(),z("div",Re,[t("div",Oe,[t("div",Ge,[s(p,{onClick:F,type:"text",class:"back-button"},{default:o(()=>[s(d,null,{default:o(()=>[s(_(we))]),_:1}),e[2]||(e[2]=c(" 返回分组列表 ",-1))]),_:1,__:[2]}),t("div",Pe,[t("h2",null,l(r.name||"设备分组详情"),1),s(b,{type:j(r.status),size:"large"},{default:o(()=>[s(d,{class:"status-icon"},{default:o(()=>[r.device_count>0?(w(),O(_(ye),{key:0})):(w(),O(_(ke),{key:1}))]),_:1}),c(" "+l(r.device_count||0)+" 个设备 ",1)]),_:1},8,["type"])])]),t("div",He,[s(p,{onClick:D,loading:g.value,type:"primary"},{default:o(()=>[s(d,null,{default:o(()=>[s(_(De))]),_:1}),e[3]||(e[3]=c(" 刷新数据 ",-1))]),_:1,__:[3]},8,["loading"]),s(C,{onCommand:q},{dropdown:o(()=>[s(x,null,{default:o(()=>[s(i,{command:"edit"},{default:o(()=>e[5]||(e[5]=[c("编辑分组",-1)])),_:1,__:[5]}),s(i,{command:"add-device"},{default:o(()=>e[6]||(e[6]=[c("添加设备",-1)])),_:1,__:[6]}),s(i,{command:"remove-device"},{default:o(()=>e[7]||(e[7]=[c("移除设备",-1)])),_:1,__:[7]}),s(i,{divided:"",command:"delete"},{default:o(()=>e[8]||(e[8]=[c("删除分组",-1)])),_:1,__:[8]})]),_:1})]),default:o(()=>[s(p,{type:"success"},{default:o(()=>[e[4]||(e[4]=c(" 管理操作 ",-1)),s(d,{class:"el-icon--right"},{default:o(()=>[s(_(xe))]),_:1})]),_:1,__:[4]})]),_:1})])]),_e((w(),z("div",Je,[t("div",Fe,[s(v,{class:"overview-card"},{default:o(()=>[t("div",Ke,[t("div",Qe,[s(d,null,{default:o(()=>[s(_(G))]),_:1})]),t("div",Ye,[t("div",qe,l(r.device_count||0),1),e[9]||(e[9]=t("div",{class:"card-label"},"设备总数",-1))])])]),_:1}),s(v,{class:"overview-card"},{default:o(()=>[t("div",We,[t("div",Xe,[s(d,null,{default:o(()=>[s(_(Te))]),_:1})]),t("div",Ze,[t("div",et,l(V.value),1),e[10]||(e[10]=t("div",{class:"card-label"},"在线设备",-1))])])]),_:1}),s(v,{class:"overview-card"},{default:o(()=>[t("div",tt,[t("div",st,[s(d,null,{default:o(()=>[s(_($e))]),_:1})]),t("div",at,[t("div",ot,l(N.value),1),e[11]||(e[11]=t("div",{class:"card-label"},"涉及服务器",-1))])])]),_:1}),s(v,{class:"overview-card"},{default:o(()=>[t("div",nt,[t("div",lt,[s(d,null,{default:o(()=>[s(_(Be))]),_:1})]),t("div",it,[t("div",dt,l(r.user_count||0),1),e[12]||(e[12]=t("div",{class:"card-label"},"授权用户",-1))])])]),_:1})]),s(U,{gutter:20,class:"detail-sections"},{default:o(()=>[s(M,{span:12},{default:o(()=>[s(v,{class:"info-card"},{header:o(()=>[t("div",rt,[s(d,null,{default:o(()=>[s(_(Se))]),_:1}),e[13]||(e[13]=t("span",null,"基础信息",-1))])]),default:o(()=>[t("div",ct,[t("div",_t,[e[14]||(e[14]=t("span",{class:"info-label"},"分组名称：",-1)),t("span",ut,l(r.name),1)]),t("div",vt,[e[15]||(e[15]=t("span",{class:"info-label"},"分组描述：",-1)),t("span",pt,l(r.description||"暂无描述"),1)]),t("div",ft,[e[16]||(e[16]=t("span",{class:"info-label"},"创建时间：",-1)),t("span",mt,l(S(r.created_at)),1)]),t("div",ht,[e[17]||(e[17]=t("span",{class:"info-label"},"更新时间：",-1)),t("span",gt,l(S(r.updated_at)),1)]),t("div",bt,[e[18]||(e[18]=t("span",{class:"info-label"},"分组状态：",-1)),s(b,{type:j(r.status)},{default:o(()=>[c(l(K(r.status)),1)]),_:1},8,["type"])])])]),_:1})]),_:1}),s(M,{span:12},{default:o(()=>[s(v,{class:"info-card"},{header:o(()=>[t("div",wt,[s(d,null,{default:o(()=>[s(_(Me))]),_:1}),e[19]||(e[19]=t("span",null,"统计信息",-1))])]),default:o(()=>[t("div",yt,[t("div",kt,[e[20]||(e[20]=t("span",{class:"info-label"},"设备总数：",-1)),t("span",Dt,l(r.device_count||0)+" 个",1)]),t("div",xt,[e[21]||(e[21]=t("span",{class:"info-label"},"在线设备：",-1)),t("span",Ct,l(V.value)+" 个",1)]),t("div",Et,[e[22]||(e[22]=t("span",{class:"info-label"},"离线设备：",-1)),t("span",Tt,l(H.value)+" 个",1)]),t("div",$t,[e[23]||(e[23]=t("span",{class:"info-label"},"涉及服务器：",-1)),t("span",Bt,l(N.value)+" 台",1)]),t("div",It,[e[24]||(e[24]=t("span",{class:"info-label"},"授权用户：",-1)),t("span",St,l(r.user_count||0)+" 人",1)])])]),_:1})]),_:1})]),_:1}),s(U,{gutter:20,class:"detail-sections"},{default:o(()=>[s(M,{span:24},{default:o(()=>[s(v,{class:"info-card"},{header:o(()=>[t("div",Mt,[s(d,null,{default:o(()=>[s(_(G))]),_:1}),e[25]||(e[25]=t("span",null,"设备列表",-1)),t("div",zt,[s(se,{modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=n=>y.value=n),placeholder:"搜索设备名称...",style:{width:"200px","margin-right":"10px"},clearable:""},{prefix:o(()=>[s(d,null,{default:o(()=>[s(_(je))]),_:1})]),_:1},8,["modelValue"]),s(ae,{modelValue:k.value,"onUpdate:modelValue":e[1]||(e[1]=n=>k.value=n),placeholder:"筛选状态",style:{width:"120px"},clearable:""},{default:o(()=>[s(E,{label:"全部",value:""}),s(E,{label:"在线",value:"online"}),s(E,{label:"离线",value:"offline"}),s(E,{label:"占用中",value:"busy"})]),_:1},8,["modelValue"])])])]),default:o(()=>[t("div",At,[s(oe,{data:J.value,stripe:""},{default:o(()=>[s(f,{prop:"device_id",label:"设备ID",width:"120"}),s(f,{label:"设备名称","min-width":"150"},{default:o(({row:n})=>[t("div",Vt,[t("span",Nt,l(n.custom_name||n.device_name),1),n.custom_name&&n.device_name!==n.custom_name?(w(),z("span",jt," ("+l(n.device_name)+") ",1)):Ve("",!0)])]),_:1}),s(f,{label:"设备来源",width:"200"},{default:o(({row:n})=>[t("div",Lt,[t("div",Ut,l(n.server_name)+" - 端口"+l(n.physical_port||n.usb_port),1),t("div",Rt,l(n.server_ip)+":"+l(n.server_port),1)])]),_:1}),s(f,{label:"USB端口",width:"100"},{default:o(({row:n})=>[t("code",null,l(n.usb_port||"N/A"),1)]),_:1}),s(f,{label:"VID/PID",width:"150"},{default:o(({row:n})=>[t("div",Ot,[t("code",null,l(L(n.vendor_id)),1),e[26]||(e[26]=t("span",null,"/",-1)),t("code",null,l(L(n.product_id)),1)])]),_:1}),s(f,{label:"状态",width:"100"},{default:o(({row:n})=>[s(b,{type:Q(n.status),size:"small"},{default:o(()=>[c(l(Y(n.status)),1)]),_:2},1032,["type"])]),_:1}),s(f,{label:"最后连接",width:"150"},{default:o(({row:n})=>[c(l(S(n.last_connected)),1)]),_:1}),s(f,{label:"操作",width:"200",fixed:"right"},{default:o(({row:n})=>[s(p,{size:"small",onClick:R=>Z(n)},{default:o(()=>e[27]||(e[27]=[c(" 详情 ",-1)])),_:2,__:[27]},1032,["onClick"]),s(p,{size:"small",onClick:R=>ee(n)},{default:o(()=>e[28]||(e[28]=[c(" 服务器 ",-1)])),_:2,__:[28]},1032,["onClick"]),s(p,{size:"small",type:"danger",onClick:R=>te(n)},{default:o(()=>e[29]||(e[29]=[c(" 移除 ",-1)])),_:2,__:[29]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),_:1})]),_:1})]),_:1})])),[[ne,g.value]])])}}},os=le(Gt,[["__scopeId","data-v-b04848d1"]]);export{os as default};
