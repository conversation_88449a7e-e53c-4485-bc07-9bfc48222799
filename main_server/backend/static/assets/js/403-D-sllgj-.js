import{_ as u}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                  */import{u as _,c as p,d as e,e as i,w as a,m as f,b as m,i as g,n as d}from"./index-s7ypaCFZ.js";const v={class:"error-container"},b={class:"error-content"},k={class:"error-actions"},x={__name:"403",setup(B){const r=m(),t=_(),l=()=>{if(window.history.length>1){const s=document.referrer;if(s&&!s.includes("/403")){r.go(-1);return}}r.push("/login")},c=()=>{t.isLoggedIn&&t.userInfo?r.push("/dashboard"):r.push("/login")};return(s,o)=>{const n=f;return g(),p("div",v,[e("div",b,[o[2]||(o[2]=e("div",{class:"error-code"},"403",-1)),o[3]||(o[3]=e("div",{class:"error-title"},"访问被拒绝",-1)),o[4]||(o[4]=e("div",{class:"error-description"}," 抱歉，您没有权限访问此页面。请联系管理员获取相应权限。 ",-1)),e("div",k,[i(n,{type:"primary",onClick:l},{default:a(()=>o[0]||(o[0]=[d("返回上一页",-1)])),_:1,__:[0]}),i(n,{onClick:c},{default:a(()=>o[1]||(o[1]=[d("回到首页",-1)])),_:1,__:[1]})])])])}}},I=u(x,[["__scopeId","data-v-89a98bb8"]]);export{I as default};
