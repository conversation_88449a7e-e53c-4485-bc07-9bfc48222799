import{_ as at}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                 *//* empty css                    *//* empty css                  *//* empty css               *//* empty css                */import{r as c,u as Xe,aM as oe,o as Ze,bL as wt,s as m,a as Le,cz as dt,y as O,i as g,w as s,a8 as Qe,c as N,d as a,z as J,e,E as nt,p as h,L as vt,t as f,a6 as ut,n as u,S as Jt,R as $t,de as ht,df as Wt,m as it,dg as Yt,d0 as Gt,Y as Qt,dh as xt,a9 as ct,x as mt,a3 as Ee,b as Ct,cY as ot,di as Zt,O as Xt,P as es,Q as ts,M as ss,k as ft,c$ as st,ac as gt,ad as yt,a4 as Ne,a5 as lt,ah as Et,cM as ls,cN as Rt,dj as Tt,dk as os,dl as as,ab as St,ae as Vt,a1 as bt,aa as Lt,cQ as Pt,cT as ns,D as is,dm as rs,f as Ut,j as It,cP as ds,h as zt,dn as us,cR as Ft,ai as Ht,by as kt,d5 as At,cW as jt,cV as Ot,cw as Nt,cJ as Mt,cK as Bt,B as cs,Z as ps,af as _s,d2 as vs,dd as ms,bY as fs,cU as gs}from"./index-DlnWH8GN.js";import{e as ys,g as qt,f as hs}from"./slaveServers-zP5ev---.js";import{g as Kt}from"./permission-assignment-K1GAuJSC.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                          *//* empty css                         */import X from"./websocket-NUfEWQtQ.js";/* empty css                   *//* empty css                     *//* empty css                        *//* empty css                 *//* empty css                *//* empty css                       *//* empty css                        */import{u as bs}from"./useOrgUsersAdapter-DLAIC2XI.js";import"./index-B1XWH8MS.js";import"./users-BWxIdTAe.js";import"./useOrganizationTree-BP0VBfMv.js";function $s(){const ue=c(!1),R=c([]),P=c([]),te=c([]),Y=c({slaveServers:{total:0,online:0,offline:0},devices:{total:0,available:0,connected:0,virtual:0},groups:{total:0,withDevices:0},permissions:{totalAssignments:0,activeUsers:0}});let j=null;const B=Xe(),I=oe(()=>B.canAccessDeviceManagementTab),U=oe(()=>B.canAccessSlaveServerTab),x=oe(()=>B.canAccessDeviceGroupTab),F=oe(()=>B.canAccessPermissionAssignmentTab),G=oe(()=>{const $=[];return I.value&&$.push({name:"devices",label:"USB设备管理",icon:"Monitor"}),U.value&&$.push({name:"slaves",label:"分布式节点管理",icon:"Monitor"}),x.value&&$.push({name:"groups",label:"资源调度分组",icon:"Collection"}),F.value&&$.push({name:"permissions",label:"授权范围管理",icon:"Key"}),$}),L=async()=>{try{const $=await qt();$.success&&(R.value=$.data||[],D())}catch($){console.error("获取从服务器列表失败:",$)}},q=async()=>{try{const $=await Kt();$.success&&(P.value=$.data||[],y())}catch($){console.error("获取设备分组列表失败:",$)}},K=async()=>{try{const $=await ys();if($.success){const M=$.data;Y.value.slaveServers={total:M.total_servers||0,online:M.online_servers||0,offline:M.offline_servers||0},Y.value.devices={total:M.total_devices||0,available:M.available_devices||0,connected:M.connected_devices||0,virtual:M.virtual_devices||0},console.log("统计数据已更新:",{servers:Y.value.slaveServers,devices:Y.value.devices})}}catch($){console.error("获取统计数据失败:",$),D()}},D=()=>{const $=R.value.length,M=R.value.filter(V=>V.status==="online"||V.is_online).length;Y.value.slaveServers={total:$,online:M,offline:$-M}},y=()=>{const $=P.value.length,M=P.value.filter(V=>V.device_count>0).length;Y.value.groups={total:$,withDevices:M}},i=async()=>{ue.value=!0;try{await Promise.all([L(),q(),K()]),m.success("数据刷新成功")}catch($){console.error("刷新数据失败:",$),m.error("数据刷新失败")}finally{ue.value=!1}},p=()=>{const $=()=>{const M=Y.value.slaveServers.total;return M>1e3?1e4:M>100?15e3:3e4};K(),j=setInterval(()=>{K()},$())},C=()=>{j&&(clearInterval(j),j=null)};return Ze(()=>{i(),p()}),wt(()=>{C()}),{loading:ue,slaveServers:R,deviceGroups:P,devices:te,statistics:Y,hasDeviceManagePermission:I,hasSlaveServerPermission:U,hasDeviceGroupPermission:x,hasPermissionAssignPermission:F,visibleTabs:G,refreshAllData:i,fetchSlaveServers:L,fetchDeviceGroups:q,fetchStatistics:K,startAutoRefresh:p,stopAutoRefresh:C}}const ks={class:"occupied-dialog-content"},ws={class:"device-info"},Cs={class:"device-header"},Ts={class:"device-details"},Ss={class:"device-name"},Vs={class:"device-id"},xs={class:"occupier-info"},Ds={class:"section-title"},zs={class:"user-card"},As={class:"user-avatar"},Es={class:"user-details"},Ls={class:"user-name"},Ps={class:"user-contact"},Us={key:0,class:"user-email"},Is={class:"contact-actions"},js={class:"time-info"},Os={class:"section-title"},Ms={class:"time-details"},Bs={class:"time-item"},Gs={class:"time-value"},Ns={class:"time-item"},Rs={class:"time-value"},Fs={class:"time-item"},Hs={class:"time-value"},qs={key:0,class:"occupation-note"},Ks={class:"section-title"},Js={class:"note-content"},Ws={class:"action-suggestions"},Ys={class:"section-title"},Qs={class:"suggestions-list"},Zs={class:"suggestion-item"},Xs={class:"suggestion-item"},el={class:"suggestion-item"},tl={class:"dialog-footer"},sl={__name:"DeviceOccupiedDialog",props:{modelValue:{type:Boolean,default:!1},deviceId:{type:[String,Number],default:null}},emits:["update:modelValue","release-requested"],setup(ue,{emit:R}){const P=ue,te=R,Y=Xe(),j=c(!1),B=c(!1),I=c(!1),U=Le({device_id:"",device_name:"",custom_name:"",device_type:"unknown"}),x=Le({user_name:"",user_contact:"",user_email:"",start_time:null,duration:0,estimated_end_time:null,note:""});dt(()=>P.modelValue,y=>{I.value=y,y&&P.deviceId&&F()}),dt(I,y=>{te("update:modelValue",y)});const F=async()=>{j.value=!0;try{const y=await fetch(`/api/v1/devices/${P.deviceId}`,{method:"GET",headers:{Authorization:`Bearer ${Y.token}`,"Content-Type":"application/json"}});if(!y.ok)throw new Error(`获取设备信息失败: HTTP ${y.status}`);const i=await y.json();if(Object.assign(U,{device_id:i.device_id,device_name:i.device_name,custom_name:i.custom_name,device_type:i.device_type}),i.status==="occupied"){const p=await fetch(`/api/v1/devices/${P.deviceId}/occupation`,{method:"GET",headers:{Authorization:`Bearer ${Y.token}`,"Content-Type":"application/json"}});if(p.ok){const C=await p.json();Object.assign(x,{user_name:C.user_name,user_contact:C.user_contact,user_email:C.user_email,start_time:C.start_time,duration:C.current_duration,estimated_end_time:C.estimated_end_time,note:C.note})}else Object.assign(x,{user_name:i.current_user_name,user_contact:i.current_user_contact,user_email:"",start_time:i.occupied_start_time,duration:i.occupied_duration,estimated_end_time:i.estimated_end_time,note:i.occupation_note})}else throw new Error("设备未被占用")}catch(y){console.error("获取设备占用信息失败:",y),m.error(`获取设备占用信息失败: ${y.message}`)}finally{j.value=!1}},G=y=>y?new Date(y).toLocaleString():"N/A",L=y=>{if(!y)return"0分钟";const i=Math.floor(y/3600),p=Math.floor(y%3600/60);return i>0?`${i}小时${p}分钟`:`${p}分钟`},q=async y=>{try{y==="phone"?(await Ee.confirm(`确定要拨打电话 ${x.user_contact} 联系 ${x.user_name} 吗？`,"确认拨打电话",{confirmButtonText:"拨打",cancelButtonText:"取消",type:"info"}),m.success("正在为您拨打电话...")):y==="message"&&(await Ee.prompt(`请输入要发送给 ${x.user_name} 的消息：`,"发送消息",{confirmButtonText:"发送",cancelButtonText:"取消",inputPlaceholder:"请输入消息内容...",inputType:"textarea"}),m.success("消息已发送"))}catch{}},K=async()=>{try{await Ee.confirm(`确定要向 ${x.user_name} 发送设备释放请求吗？`,"确认发送释放请求",{confirmButtonText:"发送",cancelButtonText:"取消",type:"warning"}),B.value=!0;const y=await fetch(`/api/v1/devices/${P.deviceId}/release-request`,{method:"POST",headers:{Authorization:`Bearer ${Y.token}`,"Content-Type":"application/json"}});if(!y.ok){const p=await y.json();throw new Error(p.detail||`HTTP ${y.status}`)}const i=await y.json();m.success(i.message||"释放请求已发送"),te("release-requested",P.deviceId)}catch(y){y.message!=="cancel"&&(console.error("发送释放请求失败:",y),m.error(`发送释放请求失败: ${y.message}`))}finally{B.value=!1}},D=()=>{I.value=!1};return(y,i)=>{const p=nt,C=ut,$=it,M=mt,V=ct;return g(),O(M,{modelValue:I.value,"onUpdate:modelValue":i[3]||(i[3]=S=>I.value=S),title:"设备占用信息",width:"500px","before-close":D},{footer:s(()=>[a("div",tl,[e($,{onClick:D},{default:s(()=>i[17]||(i[17]=[u("关闭",-1)])),_:1,__:[17]}),e($,{type:"warning",onClick:K,loading:B.value},{default:s(()=>[e(p,null,{default:s(()=>[e(h(xt))]),_:1}),i[18]||(i[18]=u(" 请求释放 ",-1))]),_:1,__:[18]},8,["loading"]),e($,{type:"primary",onClick:i[2]||(i[2]=S=>q("phone")),disabled:!x.user_contact},{default:s(()=>[e(p,null,{default:s(()=>[e(h(ht))]),_:1}),i[19]||(i[19]=u(" 联系用户 ",-1))]),_:1,__:[19]},8,["disabled"])])]),default:s(()=>[Qe((g(),N("div",ks,[a("div",ws,[a("div",Cs,[e(p,{class:"device-icon"},{default:s(()=>[e(h(vt))]),_:1}),a("div",Ts,[a("div",Ss,f(U.custom_name||U.device_name),1),a("div",Vs,"设备ID: "+f(U.device_id),1)]),e(C,{type:"warning",size:"large"},{default:s(()=>[e(p,null,{default:s(()=>[e(h(Jt))]),_:1}),i[4]||(i[4]=u(" 被占用 ",-1))]),_:1,__:[4]})])]),a("div",xs,[a("div",Ds,[e(p,null,{default:s(()=>[e(h($t))]),_:1}),i[5]||(i[5]=a("span",null,"占用用户信息",-1))]),a("div",zs,[a("div",As,[e(p,null,{default:s(()=>[e(h($t))]),_:1})]),a("div",Es,[a("div",Ls,f(x.user_name||"N/A"),1),a("div",Ps,[e(p,null,{default:s(()=>[e(h(ht))]),_:1}),a("span",null,f(x.user_contact||"N/A"),1)]),x.user_email?(g(),N("div",Us,[e(p,null,{default:s(()=>[e(h(Wt))]),_:1}),a("span",null,f(x.user_email),1)])):J("",!0)]),a("div",Is,[e($,{type:"primary",size:"small",onClick:i[0]||(i[0]=S=>q("phone")),disabled:!x.user_contact},{default:s(()=>[e(p,null,{default:s(()=>[e(h(ht))]),_:1}),i[6]||(i[6]=u(" 拨打电话 ",-1))]),_:1,__:[6]},8,["disabled"]),e($,{type:"success",size:"small",onClick:i[1]||(i[1]=S=>q("message")),disabled:!x.user_contact},{default:s(()=>[e(p,null,{default:s(()=>[e(h(Yt))]),_:1}),i[7]||(i[7]=u(" 发送消息 ",-1))]),_:1,__:[7]},8,["disabled"])])])]),a("div",js,[a("div",Os,[e(p,null,{default:s(()=>[e(h(Gt))]),_:1}),i[8]||(i[8]=a("span",null,"占用时间信息",-1))]),a("div",Ms,[a("div",Bs,[i[9]||(i[9]=a("span",{class:"time-label"},"开始时间：",-1)),a("span",Gs,f(G(x.start_time)),1)]),a("div",Ns,[i[10]||(i[10]=a("span",{class:"time-label"},"持续时间：",-1)),a("span",Rs,f(L(x.duration)),1)]),a("div",Fs,[i[11]||(i[11]=a("span",{class:"time-label"},"预计结束：",-1)),a("span",Hs,f(G(x.estimated_end_time)||"未设置"),1)])])]),x.note?(g(),N("div",qs,[a("div",Ks,[e(p,null,{default:s(()=>[e(h(Qt))]),_:1}),i[12]||(i[12]=a("span",null,"占用说明",-1))]),a("div",Js,f(x.note),1)])):J("",!0),a("div",Ws,[a("div",Ys,[e(p,null,{default:s(()=>[e(h(xt))]),_:1}),i[13]||(i[13]=a("span",null,"操作建议",-1))]),a("div",Qs,[a("div",Zs,[e(p,{class:"suggestion-icon"},{default:s(()=>[e(h(ht))]),_:1}),i[14]||(i[14]=a("span",null,"联系占用用户协商使用时间",-1))]),a("div",Xs,[e(p,{class:"suggestion-icon"},{default:s(()=>[e(h(Gt))]),_:1}),i[15]||(i[15]=a("span",null,"等待设备自动释放（如有预计结束时间）",-1))]),a("div",el,[e(p,{class:"suggestion-icon"},{default:s(()=>[e(h(xt))]),_:1}),i[16]||(i[16]=a("span",null,"发送释放请求通知",-1))])])])])),[[V,j.value]])]),_:1},8,["modelValue"])}}},ll=at(sl,[["__scopeId","data-v-eabf33b8"]]),ol={class:"device-management"},al={class:"management-header"},nl={class:"header-right"},il={class:"search-filters"},rl={class:"filter-row"},dl={class:"filter-group"},ul={class:"filter-group"},cl={class:"filter-group"},pl={class:"filter-group"},_l={class:"filter-group"},vl={class:"device-type-filter-panel"},ml={class:"panel-header"},fl={class:"panel-title"},gl={class:"panel-actions"},yl={class:"device-type-checkboxes"},hl={class:"checkbox-grid"},bl={class:"checkbox-content"},$l={class:"type-icon"},kl={class:"type-label"},wl={class:"type-count"},Cl={class:"device-list"},Tl={class:"card-header"},Sl={class:"header-actions"},Vl={class:"selected-count"},xl={class:"device-name"},Dl={class:"name-primary"},zl={key:0,class:"name-secondary"},Al={class:"server-info"},El={class:"server-name"},Ll={class:"server-ip"},Pl={class:"last-connected"},Ul={key:0},Il={class:"time"},jl={class:"device-remark"},Ol={class:"pagination-wrapper"},Ml={__name:"DeviceManagement",setup(ue){const R=Ct(),P=Xe(),te=c(!1),Y=c(!1),j=c(""),B=c(""),I=c(""),U=c(""),x=c("created_at"),F=c(!1),G=c([]),L=c(1),q=c(20),K=c(!1),D=c(null),y=c(!1),i=c(null),p=c([]),C=c([]),$=Le({total_devices:0,online_devices:0,hardware_devices:0,occupied_devices:0}),M=Le({total_devices:0,available_devices:0,online_servers:0,total_servers:0,data_freshness:"unknown",last_update:null}),V=c("hybrid");c(!0);const S=c({}),_e=c([]);let he=null;const Q=oe(()=>{const t=Object.keys(S.value).filter(o=>S.value[o]);return t.length===0?[]:p.value.filter(o=>{const n=Ve(o);return t.includes(n)})}),ee=oe(()=>{let t=Q.value;if(j.value){const o=j.value.toLowerCase();t=t.filter(n=>n.device_name&&n.device_name.toLowerCase().includes(o)||n.custom_name&&n.custom_name.toLowerCase().includes(o)||n.device_id&&n.device_id.toLowerCase().includes(o)||n.description&&n.description.toLowerCase().includes(o))}return B.value&&(t=t.filter(o=>o.server_id===B.value)),I.value&&(t=t.filter(o=>o.device_type===I.value)),U.value&&(t=t.filter(o=>o.status===U.value)),t.sort((o,n)=>{switch(x.value){case"device_name":return(o.device_name||"").localeCompare(n.device_name||"");case"server_group":return(o.server_name||"").localeCompare(n.server_name||"");case"last_connected":return new Date(n.last_connected||0)-new Date(o.last_connected||0);case"created_at":default:return new Date(n.created_at||0)-new Date(o.created_at||0)}}),t}),Pe=oe(()=>ee.value.length),Z=async()=>{te.value=!0;try{await Promise.all([be(),ve()])}catch(t){console.error("数据刷新失败:",t),m.error("数据刷新失败")}finally{te.value=!1}},be=async()=>{try{const t={page:L.value,page_size:q.value,search:j.value||void 0,server_id:B.value||void 0,device_type:I.value||void 0,status:U.value||void 0,sort_by:x.value,sort_order:"desc"};Object.keys(t).forEach(A=>{t[A]===void 0&&delete t[A]});const o=await fetch("/api/v1/devices?"+new URLSearchParams(t),{method:"GET",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!o.ok)throw new Error(`HTTP ${o.status}: ${o.statusText}`);const n=await o.json();p.value=n.devices||[],r(),i.value=new Date().toLocaleString()}catch(t){console.error("获取设备列表失败:",t),m.error(`获取设备列表失败: ${t.message}`)}},ve=async()=>{try{const t=await fetch(`/api/v1/devices/stats/summary?mode=${V.value}`,{method:"GET",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const o=await t.json();Object.assign($,{total_devices:o.total_devices||0,online_devices:o.online_devices||0,hardware_devices:o.hardware_devices||0,occupied_devices:o.occupied_devices||0}),V.value!=="database"&&Object.assign(M,{total_devices:o.total_devices||0,available_devices:o.available_devices||0,online_servers:o.online_servers||0,total_servers:o.total_servers||0,data_freshness:o.data_freshness||"unknown",last_update:o.last_update}),i.value=new Date().toLocaleString(),console.log("统计数据获取成功:",o)}catch(t){console.error("获取统计数据失败:",t)}},$e={ca_lock:{label:"CA锁",color:"danger",icon:"🔐",description:"CA证书锁、银行U盾、数字证书设备",defaultVisible:!0},encryption_key:{label:"加密锁",color:"warning",icon:"🔒",description:"软件加密锁、硬件加密锁设备",defaultVisible:!0},bank_ukey:{label:"银行U盾",color:"primary",icon:"🏦",description:"银行数字证书U盾设备",defaultVisible:!0},financial_lock:{label:"财务锁",color:"warning",icon:"💰",description:"用友、金蝶等财务软件加密锁",defaultVisible:!0},cost_lock:{label:"造价加密锁",color:"primary",icon:"🏗️",description:"广联达、新点等造价软件加密锁",defaultVisible:!0},other_lock:{label:"其他加密锁",color:"danger",icon:"🔒",description:"其他类型的加密锁设备",defaultVisible:!1},storage:{label:"存储设备",color:"info",icon:"💾",description:"U盘、移动硬盘等存储设备",defaultVisible:!1},peripheral_device:{label:"外设",color:"success",icon:"🖨️",description:"打印机、扫描仪等外围设备",defaultVisible:!1},video_device:{label:"视频设备",color:"primary",icon:"📹",description:"摄像头、采集卡等视频设备",defaultVisible:!1},audio_device:{label:"音频设备",color:"warning",icon:"🎵",description:"麦克风、耳机、声卡等音频设备",defaultVisible:!1},bluetooth_device:{label:"蓝牙设备",color:"info",icon:"📶",description:"蓝牙适配器、蓝牙设备",defaultVisible:!1},hub:{label:"Hub设备",color:"",icon:"🔌",description:"USB Hub、集线器等",defaultVisible:!1},input:{label:"输入设备",color:"",icon:"⌨️",description:"键盘、鼠标等输入设备",defaultVisible:!1},communication:{label:"通信设备",color:"",icon:"📡",description:"网络适配器等通信设备",defaultVisible:!1},hardware:{label:"硬件设备",color:"",icon:"🔧",description:"其他硬件设备",defaultVisible:!1},unknown:{label:"未知设备",color:"",icon:"❓",description:"未识别的设备类型",defaultVisible:!1}},Ue=t=>{var o;return((o=$e[t])==null?void 0:o.color)||""},Se=t=>{var o;return((o=$e[t])==null?void 0:o.label)||"待补充"},Ve=t=>t.final_device_type?ae(t.final_device_type,t):ae(t.device_type||"unknown",t),ae=(t,o=null)=>{if(o){if(o.identification_source==="usb_ids_enhanced")return"encryption_key";if(o.usb_ids_vendor_name&&o.usb_ids_vendor_name.trim()){const A=o.usb_ids_vendor_name.toLowerCase();if(A.includes("senseshield")||A.includes("rockey")||A.includes("hasp")||A.includes("sentinel")||A.includes("safenet")||A.includes("aladdin"))return"encryption_key";if(o.usb_ids_device_name){const le=o.usb_ids_device_name.toLowerCase();if(le.includes("dongle")||le.includes("key")||le.includes("lock")||le.includes("token"))return"encryption_key"}}}return{ca_lock:"ca_lock",encryption_key:"encryption_key",bank_ukey:"bank_ukey",financial_lock:"financial_lock",cost_lock:"cost_lock",other_lock:"other_lock",video_device:"video_device",audio_device:"audio_device",bluetooth_device:"bluetooth_device",peripheral_device:"peripheral_device",storage:"storage",input:"input",communication:"communication",hub:"hub",hardware:"hardware",unknown:"unknown",CA锁:"ca_lock",加密锁:"encryption_key",银行U盾:"bank_ukey",encryption_lock:"encryption_key",encryption:"encryption_key",printer_scanner:"peripheral_device",printer:"peripheral_device",unknown_error:"unknown",virtual:"unknown",system:"unknown"}[t]||"unknown"},Oe=()=>{const t=localStorage.getItem("omnilink-device-type-filter");if(t)try{S.value=JSON.parse(t)}catch(o){console.warn("Failed to parse saved device type filter:",o),De()}else De();_e.value=Object.keys(S.value).filter(o=>S.value[o])},xe=()=>{localStorage.setItem("omnilink-device-type-filter",JSON.stringify(S.value))},Re=t=>{S.value={},Object.keys($e).forEach(o=>{S.value[o]=t.includes(o)}),xe()},Fe=()=>{Object.keys($e).forEach(t=>{S.value[t]=!0}),_e.value=Object.keys($e),xe()},De=()=>{S.value={},Object.keys($e).forEach(t=>{S.value[t]=$e[t].defaultVisible}),_e.value=Object.keys(S.value).filter(t=>S.value[t]),xe()},He=t=>{if(!p.value||p.value.length===0)return 0;let o=p.value;if(j.value){const n=j.value.toLowerCase();o=o.filter(A=>{var le,Te,Je,We;return((le=A.device_name)==null?void 0:le.toLowerCase().includes(n))||((Te=A.custom_name)==null?void 0:Te.toLowerCase().includes(n))||((Je=A.device_id)==null?void 0:Je.toLowerCase().includes(n))||((We=A.serial_number)==null?void 0:We.toLowerCase().includes(n))})}return B.value&&(o=o.filter(n=>n.slave_server_id===B.value)),U.value&&(o=o.filter(n=>n.status===U.value)),o.filter(n=>Ve(n)===t).length},qe=t=>({idle:"success",occupied:"warning",damaged:"danger",offline:"info"})[t]||"info",k=t=>({idle:"空闲",occupied:"被占用",damaged:"硬件损坏",offline:"离线"})[t]||"未知",v=t=>t?new Date(t).toLocaleString():"N/A",H=t=>{t?G.value=[...ee.value]:G.value=[]},z=t=>{G.value=t,F.value=t.length===ee.value.length},ie=async t=>{switch(t){case"batch-edit":case"batch-group":case"batch-delete":if(G.value.length===0){m.warning("请先选择要操作的设备");return}m.info(`批量操作: ${t}, 选中 ${G.value.length} 个设备`);break;default:m.warning("未知的批量操作")}},se=async()=>{try{await Ee.confirm("此操作将使用智能分类器重新分析所有设备的类型，支持精准识别CA锁、财务锁、造价加密锁等。是否继续？","智能重新分类设备类型",{confirmButtonText:"开始分类",cancelButtonText:"取消",type:"warning"}),Y.value=!0;try{const t=await fetch("/api/v1/devices/batch-reclassify-types",{method:"POST",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const o=await t.json();m.success(`智能分类完成！总计: ${o.statistics.total}, 成功: ${o.statistics.updated}`),await Z()}finally{Y.value=!1}}catch(t){Y.value=!1,t!=="cancel"&&(console.error("智能重新分类失败:",t),m.error("智能重新分类失败: "+t.message))}},re=t=>{q.value=t,L.value=1},_=t=>{L.value=t},d=t=>{R.push(`/device-center/device/${t.id}`)},ne=t=>{R.push(`/device-center/device/${t.id}`)},W=async t=>{try{await Ee.confirm(`确定要删除设备 "${t.device_name}" 吗？此操作将解除USB设备自动绑定。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const o=await fetch(`/api/v1/devices/${t.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!o.ok){const n=await o.json();throw new Error(n.detail||`HTTP ${o.status}`)}m.success(`设备 "${t.device_name}" 已删除`),Z()}catch(o){o.message!=="cancel"&&(console.error("删除设备失败:",o),m.error(`删除设备失败: ${o.message}`))}},me=t=>{t.status==="occupied"&&(D.value=t.id,K.value=!0)},we=async t=>{try{const o=await fetch(`/api/v1/devices/${t}/release-request`,{method:"POST",headers:{Authorization:`Bearer ${P.token}`,"Content-Type":"application/json"}});if(!o.ok){const A=await o.json();throw new Error(A.detail||`HTTP ${o.status}`)}const n=await o.json();m.success(n.message||"设备释放请求已发送"),Z()}catch(o){console.error("发送释放请求失败:",o),m.error(`发送释放请求失败: ${o.message}`)}},Me=()=>{X.setConnectionChangeCallback(o=>{y.value=o,console.log(o?"WebSocket连接已建立":"WebSocket连接已断开")}),X.subscribe("device_updates"),X.addEventListener("device_status_update",fe),X.addEventListener("device_connected",pe),X.addEventListener("device_disconnected",ge),X.addEventListener("device_occupied",T),X.addEventListener("device_released",Ie),X.addEventListener("device_added",je),X.addEventListener("device_updated",ze),X.addEventListener("device_deleted",Ke),X.addEventListener("device_release_requested",ye);const t=P.token;t&&X.connect(t)},fe=t=>{console.log("设备状态更新:",t);const o=p.value.findIndex(n=>n.id===t.device_id);o!==-1&&(p.value[o].status=t.new_status,i.value=new Date().toLocaleString(),r(),m.info(`设备状态更新: ${p.value[o].device_name} -> ${k(t.new_status)}`))},pe=t=>{console.log("设备连接:",t);const o=p.value.findIndex(n=>n.id===t.device_id);o!==-1&&(p.value[o].status="occupied",p.value[o].last_connected=t.timestamp,p.value[o].last_connected_user=t.user_name||"Unknown",r(),m.success(`设备已连接: ${p.value[o].device_name}`))},ge=t=>{console.log("设备断开:",t);const o=p.value.findIndex(n=>n.id===t.device_id);o!==-1&&(p.value[o].status="idle",r(),m.info(`设备已断开: ${p.value[o].device_name}`))},T=t=>{console.log("设备被占用:",t);const o=p.value.findIndex(n=>n.id===t.device_id);o!==-1&&(p.value[o].status="occupied",p.value[o].last_connected_user=t.user_name,p.value[o].last_connected=t.timestamp,r(),m.warning(`设备被占用: ${p.value[o].device_name} (${t.user_name})`))},Ie=t=>{console.log("设备已释放:",t);const o=p.value.findIndex(n=>n.id===t.device_id);o!==-1&&(p.value[o].status="idle",r(),m.success(`设备已释放: ${p.value[o].device_name}`))},je=t=>{console.log("设备已添加:",t),Z(),m.success(`新设备已添加: ${t.device_name}`)},ze=t=>{console.log("设备已更新:",t),Z(),m.info("设备信息已更新")},Ke=t=>{console.log("设备已删除:",t);const o=p.value.findIndex(n=>n.id===t.device_id);o!==-1&&(p.value.splice(o,1),r(),m.info(`设备已删除: ${t.device_name}`))},ye=t=>{var o;console.log("收到设备释放请求:",t),t.requester_id!==((o=P.user)==null?void 0:o.id)&&m({type:"warning",message:`${t.requester_name} 请求您释放设备: ${t.device_name}`,duration:1e4,showClose:!0})},r=()=>{$.total_devices=p.value.length,$.online_devices=p.value.filter(t=>t.status!=="offline").length,$.hardware_devices=p.value.filter(t=>t.device_type!=="unknown").length,$.occupied_devices=p.value.filter(t=>t.status==="occupied").length},l=()=>{X.removeEventListener("device_status_update",fe),X.removeEventListener("device_connected",pe),X.removeEventListener("device_disconnected",ge),X.removeEventListener("device_occupied",T),X.removeEventListener("device_released",Ie),X.removeEventListener("device_added",je),X.removeEventListener("device_updated",ze),X.removeEventListener("device_deleted",Ke),X.removeEventListener("device_release_requested",ye),X.unsubscribe("device_updates")};return Ze(()=>{Oe(),Z(),Me(),he=setInterval(Z,6e4)}),wt(()=>{he&&clearInterval(he),l()}),(t,o)=>{const n=nt,A=it,le=ts,Te=es,Je=ss,We=ft,de=yt,ce=gt,Ce=Et,Be=ut,Ae=Tt,et=Rt,Ge=ls,ke=Vt,tt=St,rt=Lt,E=ct;return g(),N(Ne,null,[a("div",ol,[a("div",al,[o[16]||(o[16]=a("div",{class:"header-left"},[a("div",{class:"page-title"},[a("h2",null,"USB设备管理")])],-1)),a("div",nl,[e(A,{onClick:Z,loading:te.value,type:"primary"},{default:s(()=>[e(n,null,{default:s(()=>[e(h(ot))]),_:1}),o[10]||(o[10]=u(" 刷新数据 ",-1))]),_:1,__:[10]},8,["loading"]),e(A,{onClick:se,type:"warning",loading:Y.value},{default:s(()=>[e(n,null,{default:s(()=>[e(h(Zt))]),_:1}),o[11]||(o[11]=u(" 智能重新分类 ",-1))]),_:1,__:[11]},8,["loading"]),e(Je,{onCommand:ie},{dropdown:s(()=>[e(Te,null,{default:s(()=>[e(le,{command:"batch-edit"},{default:s(()=>o[13]||(o[13]=[u("批量编辑",-1)])),_:1,__:[13]}),e(le,{command:"batch-group"},{default:s(()=>o[14]||(o[14]=[u("批量分组",-1)])),_:1,__:[14]}),e(le,{divided:"",command:"batch-delete"},{default:s(()=>o[15]||(o[15]=[u("批量删除",-1)])),_:1,__:[15]})]),_:1})]),default:s(()=>[e(A,{type:"success"},{default:s(()=>[o[12]||(o[12]=u(" 批量操作 ",-1)),e(n,{class:"el-icon--right"},{default:s(()=>[e(h(Xt))]),_:1})]),_:1,__:[12]})]),_:1})])]),a("div",il,[e(Ce,null,{default:s(()=>[a("div",rl,[a("div",dl,[e(We,{modelValue:j.value,"onUpdate:modelValue":o[0]||(o[0]=b=>j.value=b),placeholder:"搜索设备名称、ID、描述...",style:{width:"300px"},clearable:""},{prefix:s(()=>[e(n,null,{default:s(()=>[e(h(st))]),_:1})]),_:1},8,["modelValue"])]),a("div",ul,[e(ce,{modelValue:B.value,"onUpdate:modelValue":o[1]||(o[1]=b=>B.value=b),placeholder:"筛选服务器",style:{width:"200px"},clearable:""},{default:s(()=>[e(de,{label:"全部服务器",value:""}),(g(!0),N(Ne,null,lt(C.value,b=>(g(),O(de,{key:b.id,label:b.name,value:b.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),a("div",cl,[e(ce,{modelValue:I.value,"onUpdate:modelValue":o[2]||(o[2]=b=>I.value=b),placeholder:"筛选类型",style:{width:"150px"},clearable:""},{default:s(()=>[e(de,{label:"全部类型",value:""}),e(de,{label:"加密锁",value:"encryption_key"}),e(de,{label:"存储设备",value:"storage"}),e(de,{label:"输入设备",value:"input"}),e(de,{label:"通信设备",value:"communication"}),e(de,{label:"硬件设备",value:"hardware"}),e(de,{label:"未知设备",value:"unknown"})]),_:1},8,["modelValue"])]),a("div",pl,[e(ce,{modelValue:U.value,"onUpdate:modelValue":o[3]||(o[3]=b=>U.value=b),placeholder:"筛选状态",style:{width:"120px"},clearable:""},{default:s(()=>[e(de,{label:"全部状态",value:""}),e(de,{label:"空闲",value:"idle"}),e(de,{label:"被占用",value:"occupied"}),e(de,{label:"硬件损坏",value:"damaged"}),e(de,{label:"离线",value:"offline"})]),_:1},8,["modelValue"])]),a("div",_l,[e(ce,{modelValue:x.value,"onUpdate:modelValue":o[4]||(o[4]=b=>x.value=b),placeholder:"排序方式",style:{width:"150px"}},{default:s(()=>[e(de,{label:"注册时间",value:"created_at"}),e(de,{label:"设备名称",value:"device_name"}),e(de,{label:"服务器分组",value:"server_group"}),e(de,{label:"最后连接",value:"last_connected"})]),_:1},8,["modelValue"])])])]),_:1})]),a("div",vl,[e(Ce,null,{header:s(()=>[a("div",ml,[a("div",fl,[e(n,null,{default:s(()=>[e(h(as))]),_:1}),o[17]||(o[17]=a("span",null,"设备类型过滤",-1)),e(Be,{size:"small",type:"info"},{default:s(()=>[u(" 显示"+f(Object.keys(S.value).filter(b=>S.value[b]).length)+"种类型，共"+f(Q.value.length)+"个设备 ",1)]),_:1})]),a("div",gl,[e(A,{size:"small",onClick:Fe},{default:s(()=>o[18]||(o[18]=[u("全选",-1)])),_:1,__:[18]}),e(A,{size:"small",onClick:De},{default:s(()=>o[19]||(o[19]=[u("重置为默认",-1)])),_:1,__:[19]})])])]),default:s(()=>[a("div",yl,[e(Ge,{modelValue:_e.value,"onUpdate:modelValue":o[5]||(o[5]=b=>_e.value=b),onChange:Re},{default:s(()=>[a("div",hl,[(g(),N(Ne,null,lt($e,(b,Ye)=>e(et,{key:Ye,label:Ye,class:"device-type-checkbox"},{default:s(()=>[a("div",bl,[a("span",$l,f(b.icon),1),a("span",kl,[u(f(b.label.replace(/^[🔐💾⌨️📡🖨️🔌🔧❓]\s/,""))+" ",1),a("span",wl,"（"+f(He(Ye))+"）",1)]),e(Ae,{content:b.description,placement:"top"},{default:s(()=>[e(n,{class:"info-icon"},{default:s(()=>[e(h(os))]),_:1})]),_:2},1032,["content"])])]),_:2},1032,["label"])),64))])]),_:1},8,["modelValue"])])]),_:1})]),a("div",Cl,[e(Ce,null,{header:s(()=>[a("div",Tl,[o[21]||(o[21]=a("span",null,"设备列表",-1)),a("div",Sl,[e(et,{modelValue:F.value,"onUpdate:modelValue":o[6]||(o[6]=b=>F.value=b),onChange:H},{default:s(()=>o[20]||(o[20]=[u("全选",-1)])),_:1,__:[20]},8,["modelValue"]),a("span",Vl,"已选择 "+f(G.value.length)+" 个设备",1)])])]),default:s(()=>[Qe((g(),O(tt,{data:ee.value,onSelectionChange:z,stripe:"",style:{width:"100%"}},{default:s(()=>[e(ke,{type:"selection",width:"55"}),e(ke,{prop:"device_id",label:"设备ID",width:"120"}),e(ke,{label:"设备名称","min-width":"200"},{default:s(({row:b})=>[a("div",xl,[a("div",Dl,f(b.custom_name||b.device_name),1),b.custom_name?(g(),N("div",zl,f(b.device_name),1)):J("",!0)])]),_:1}),e(ke,{label:"设备类型",width:"120"},{default:s(({row:b})=>[e(Be,{type:Ue(Ve(b)),size:"small"},{default:s(()=>[u(f(Se(Ve(b))),1)]),_:2},1032,["type"])]),_:1}),e(ke,{label:"状态",width:"100"},{default:s(({row:b})=>[e(Be,{type:qe(b.status),size:"small",class:bt({"clickable-status":b.status==="occupied"}),onClick:Ye=>b.status==="occupied"?me(b):null},{default:s(()=>[u(f(k(b.status)),1)]),_:2},1032,["type","class","onClick"])]),_:1}),e(ke,{label:"所属服务器",width:"180"},{default:s(({row:b})=>[a("div",Al,[a("div",El,f(b.server_name),1),a("div",Ll,f(b.server_ip),1)])]),_:1}),e(ke,{label:"位置",width:"120"},{default:s(({row:b})=>[a("code",null,f(b.physical_port||"N/A"),1)]),_:1}),e(ke,{label:"最后连接",width:"150"},{default:s(({row:b})=>[a("div",Pl,[b.last_connected_user?(g(),N("div",Ul,f(b.last_connected_user),1)):J("",!0),a("div",Il,f(v(b.last_connected)),1)])]),_:1}),e(ke,{label:"备注","min-width":"150"},{default:s(({row:b})=>[a("div",jl,f(b.remark||"无备注"),1)]),_:1}),e(ke,{label:"操作",width:"200",fixed:"right"},{default:s(({row:b})=>[e(A,{size:"small",onClick:Ye=>d(b)},{default:s(()=>o[22]||(o[22]=[u(" 详情 ",-1)])),_:2,__:[22]},1032,["onClick"]),e(A,{size:"small",onClick:Ye=>ne(b)},{default:s(()=>o[23]||(o[23]=[u(" 修改 ",-1)])),_:2,__:[23]},1032,["onClick"]),e(A,{size:"small",type:"danger",onClick:Ye=>W(b)},{default:s(()=>o[24]||(o[24]=[u(" 删除 ",-1)])),_:2,__:[24]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[E,te.value]]),a("div",Ol,[e(rt,{"current-page":L.value,"onUpdate:currentPage":o[7]||(o[7]=b=>L.value=b),"page-size":q.value,"onUpdate:pageSize":o[8]||(o[8]=b=>q.value=b),"page-sizes":[20,50,100,200],total:Pe.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:re,onCurrentChange:_},null,8,["current-page","page-size","total"])])]),_:1})])]),e(ll,{modelValue:K.value,"onUpdate:modelValue":o[9]||(o[9]=b=>K.value=b),"device-id":D.value,onReleaseRequested:we},null,8,["modelValue","device-id"])],64)}}},Bl=at(Ml,[["__scopeId","data-v-5232573a"]]),Gl={class:"slave-server-management"},Nl={class:"toolbar"},Rl={class:"toolbar-left"},Fl={class:"toolbar-right"},Hl={class:"server-name"},ql={class:"server-name-text"},Kl={class:"server-name-display"},Jl={class:"status-tags"},Wl={class:"ip-address"},Yl={class:"device-count"},Ql={key:0,class:"heartbeat-time"},Zl={key:1,class:"text-muted"},Xl={key:0,class:"config-check-time"},eo={key:1,class:"text-muted"},to={class:"pagination-container"},so={__name:"SlaveServerManagement",emits:["stats-update"],setup(ue,{expose:R,emit:P}){const te=P,Y=Ct(),j=Xe(),B=c(!1),I=c(!1),U=c(!1),x=c(!1),F=c(!1),G=c([]),L=c([]),q=c(""),K=c(""),D=c(1),y=c(20),i=c(0),p=c(),C=Le({server_name:"",server_ip:"",server_port:8889,vh_port:7575,location:"",description:""}),$={server_name:[{required:!0,message:"请输入服务器名称",trigger:"blur"}],server_ip:[{required:!0,message:"请输入IP地址",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"请输入正确的IP地址格式",trigger:"blur"}],server_port:[{required:!0,message:"请输入服务端口",trigger:"blur"}],vh_port:[{required:!0,message:"请输入VH端口",trigger:"blur"}]},M=oe(()=>!0),V=oe(()=>!0),S=oe(()=>!0),_e=oe(()=>!0),he=oe(()=>{let k=G.value;if(q.value&&(k=k.filter(H=>H.status===q.value)),K.value){const H=K.value.toLowerCase();k=k.filter(z=>z.name&&z.name.toLowerCase().includes(H)||z.ip_address&&z.ip_address.toLowerCase().includes(H))}const v=k.sort((H,z)=>H.status==="online"&&z.status!=="online"?-1:H.status!=="online"&&z.status==="online"?1:H.id-z.id);return i.value=v.length,v}),Q=oe(()=>{const k=(D.value-1)*y.value,v=k+y.value;return he.value.slice(k,v)}),ee=k=>k==="online"?"success":"danger",Pe=k=>k?new Date(k).toLocaleString():"",Z=async()=>{B.value=!0;try{const k=await qt();G.value=k.data||[],i.value=G.value.length,console.log("从服务器列表数据:",k.data),k.data&&k.data.length>0&&(console.log("第一个服务器的数据:",k.data[0]),console.log("第一个服务器的last_seen字段:",k.data[0].last_seen)),be()}catch(k){console.error("加载从服务器数据失败:",k),m.error("加载从服务器数据失败")}finally{B.value=!1}},be=()=>{const k={total:G.value.length,online:G.value.filter(v=>v.status==="online").length,offline:G.value.filter(v=>v.status==="offline").length};te("stats-update",k)},ve=()=>{},$e=k=>{y.value=k,Z()},Ue=k=>{D.value=k,Z()},Se=k=>{Y.push(`/device-center/slave-server/${k.id}`)},Ve=async(k,v)=>{try{await Ee.confirm(`确定要${v==="restart"?"重启":"控制"}服务器 "${k.name}" 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),m.success(`服务器 "${k.name}" ${v==="restart"?"重启":"控制"}成功`)}catch{}},ae=async()=>{try{await p.value.validate(),I.value=!0,await hs(C),m.success("从服务器添加成功"),F.value=!1,Oe(),Z(),Z()}catch(k){console.error("添加从服务器失败:",k),m.error("添加从服务器失败")}finally{I.value=!1}},Oe=()=>{var k;Object.assign(C,{server_name:"",server_ip:"",server_port:8889,vh_port:7575,location:"",description:""}),(k=p.value)==null||k.clearValidate()},xe=k=>{L.value=k},Re=async()=>{if(L.value.length===0){m.warning("请先选择要同步的从服务器");return}try{await Ee.confirm(`确定要强制同步选中的 ${L.value.length} 个从服务器的设备数据吗？

此操作将：
1. 清空选中从服务器的现有设备数据
2. 重新从从服务器获取最新设备信息
3. 应用USB.IDS增强识别`,"确认强制同步",{confirmButtonText:"确定同步",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}),U.value=!0;const k=L.value.map(z=>z.id),v=await fetch("/api/v1/slave/force-sync",{method:"POST",headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"},body:JSON.stringify({slave_ids:k})});if(!v.ok)throw new Error(`HTTP ${v.status}: ${v.statusText}`);const H=await v.json();if(H.success){m.success(`强制同步完成！${H.message}`);const z=H.results.filter(se=>se.success).length,ie=H.results.filter(se=>!se.success).length;if(ie>0){const se=H.results.filter(re=>!re.success).map(re=>`服务器${re.slave_id}: ${re.message}`).join(`
`);m.warning(`同步完成，但有 ${ie} 个服务器同步失败：
${se}`)}await Z(),L.value=[]}else throw new Error(H.message||"强制同步失败")}catch(k){console.error("强制同步失败:",k),m.error(`强制同步失败: ${k.message}`)}finally{U.value=!1}},Fe=async()=>{if(L.value.length===0){m.warning("请先选择要删除的从服务器");return}try{const k=L.value.map(ie=>ie.name).join("、"),v=`
      <div style="text-align: left;">
        <p><strong>确定要删除以下 ${L.value.length} 个从服务器吗？</strong></p>
        <p style="color: #666; font-size: 14px; margin: 10px 0;">${k}</p>

        <p><strong>此操作将执行深度清理：</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px; color: #666;">
          <li>🗑️ 删除从服务器记录</li>
          <li>🔌 删除所有关联的USB设备记录</li>
          <li>👥 从设备分组中移除这些设备</li>
          <li>👤 清除设备的直接用户分配关系</li>
          <li>🔄 更新设备占用记录状态</li>
          <li>🧹 清理空的设备分组</li>
          <li>📋 保留审计日志（合规要求）</li>
        </ul>

        <p style="color: #e74c3c; font-weight: bold;">⚠️ 此操作不可撤销！</p>
      </div>
    `;await Ee.confirm(v,"确认深度删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error",dangerouslyUseHTMLString:!0,customClass:"bulk-delete-confirm"}),x.value=!0;const H=L.value.map(ie=>ie.id),z=await He(H);if(z.batch_results.successful_deletions>0){const ie=`✅ 成功删除 ${z.batch_results.successful_deletions} 个从服务器`,se=`📊 清理统计：设备 ${z.batch_results.overall_stats.total_devices_deleted} 个，分组关联 ${z.batch_results.overall_stats.total_group_assignments_removed} 个，空分组清理 ${z.batch_results.overall_stats.total_empty_groups_cleaned} 个`;m.success(`${ie}
${se}`)}if(z.batch_results.failed_deletions>0){const ie=z.batch_results.deletion_details.filter(se=>se.status==="failed").map(se=>`服务器 ${se.server_id}: ${se.error}`).join(`
`);m.error(`❌ ${z.batch_results.failed_deletions} 个服务器删除失败：
${ie}`)}await Z(),L.value=[]}catch(k){k!=="cancel"&&(console.error("批量删除失败:",k),m.error(`批量删除失败: ${k.message}`))}finally{x.value=!1}},De=async k=>{try{const v=await fetch(`/api/v1/slave/${k}`,{method:"DELETE",headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"}});if(!v.ok){const z=await v.json().catch(()=>({}));throw new Error(z.detail||`HTTP ${v.status}: ${v.statusText}`)}return await v.json()}catch(v){throw console.error(`删除从服务器 ${k} 失败:`,v),v}},He=async k=>{try{const v=await fetch("/api/v1/slave/batch-delete",{method:"POST",headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"},body:JSON.stringify(k)});if(!v.ok){const z=await v.json().catch(()=>({}));throw new Error(z.detail||`HTTP ${v.status}: ${v.statusText}`)}return await v.json()}catch(v){throw console.error("批量删除从服务器失败:",v),v}},qe=async k=>{try{const v=`
      <div style="text-align: left;">
        <p><strong>确定要删除从服务器 "${k.name}" 吗？</strong></p>

        <p><strong>此操作将执行深度清理：</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px; color: #666;">
          <li>🗑️ 删除从服务器记录</li>
          <li>🔌 删除所有关联的USB设备记录</li>
          <li>👥 从设备分组中移除这些设备</li>
          <li>👤 清除设备的直接用户分配关系</li>
          <li>🔄 更新设备占用记录状态</li>
          <li>🧹 清理空的设备分组</li>
          <li>📋 保留审计日志（合规要求）</li>
        </ul>

        <p style="color: #e74c3c; font-weight: bold;">⚠️ 此操作不可撤销！</p>
      </div>
    `;await Ee.confirm(v,"确认深度删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error",dangerouslyUseHTMLString:!0});const H=await De(k.id);if(H.deletion_stats){const z=H.deletion_stats,ie=`✅ 从服务器 "${k.name}" 删除成功`;let se=`📊 清理统计：设备 ${z.devices_deleted} 个，分组关联 ${z.device_group_assignments_removed} 个`;if(z.empty_groups_cleaned>0&&(se+=`，清理空分组 ${z.empty_groups_cleaned} 个`),z.affected_groups&&z.affected_groups.length>0){const re=z.affected_groups.map(_=>_.name).join("、");se+=`
🔗 受影响的设备分组：${re}`}if(z.affected_users&&z.affected_users.length>0){const re=z.affected_users.map(_=>_.username).join("、");se+=`
👤 受影响的用户：${re}`}m.success(`${ie}
${se}`)}else m.success(`从服务器 "${k.name}" 删除成功`);await Z()}catch(v){v!=="cancel"&&(console.error("删除从服务器失败:",v),m.error(`删除失败: ${v.message}`))}};return R({refreshData:Z}),Ze(()=>{Z(),console.log("用户权限调试信息:",{user:j.user,canManageSlaves:M.value,canDeleteSlaves:V.value,canCreateSlaves:S.value,canControlSlaves:_e.value})}),(k,v)=>{const H=nt,z=it,ie=yt,se=gt,re=ft,_=Vt,d=Tt,ne=ut,W=St,me=Lt,we=It,Me=ds,fe=Ut,pe=mt,ge=ct;return g(),N("div",Gl,[a("div",Nl,[a("div",Rl,[e(z,{type:"primary",onClick:Z,loading:B.value},{default:s(()=>[e(H,null,{default:s(()=>[e(h(ot))]),_:1}),v[13]||(v[13]=u(" 刷新列表 ",-1))]),_:1,__:[13]},8,["loading"]),S.value?(g(),O(z,{key:0,type:"success",onClick:v[0]||(v[0]=T=>F.value=!0)},{default:s(()=>[e(H,null,{default:s(()=>[e(h(Pt))]),_:1}),v[14]||(v[14]=u(" 添加分布式节点 ",-1))]),_:1,__:[14]})):J("",!0),M.value?(g(),O(z,{key:1,type:"warning",onClick:Re,loading:U.value,disabled:L.value.length===0},{default:s(()=>[e(H,null,{default:s(()=>[e(h(ot))]),_:1}),u(" 强制同步数据 ("+f(L.value.length)+") ",1)]),_:1},8,["loading","disabled"])):J("",!0),V.value?(g(),O(z,{key:2,type:"danger",onClick:Fe,loading:x.value,disabled:L.value.length===0},{default:s(()=>[e(H,null,{default:s(()=>[e(h(ns))]),_:1}),u(" 批量删除节点 ("+f(L.value.length)+") ",1)]),_:1},8,["loading","disabled"])):J("",!0)]),a("div",Fl,[e(se,{modelValue:q.value,"onUpdate:modelValue":v[1]||(v[1]=T=>q.value=T),placeholder:"筛选状态",style:{width:"120px","margin-right":"8px"},clearable:"",onChange:ve},{default:s(()=>[e(ie,{label:"全部",value:""}),e(ie,{label:"在线",value:"online"}),e(ie,{label:"离线",value:"offline"})]),_:1},8,["modelValue"]),e(re,{modelValue:K.value,"onUpdate:modelValue":v[2]||(v[2]=T=>K.value=T),placeholder:"搜索服务器名称...",style:{width:"200px"},clearable:"",onInput:ve},{prefix:s(()=>[e(H,null,{default:s(()=>[e(h(st))]),_:1})]),_:1},8,["modelValue"])])]),Qe((g(),O(W,{data:Q.value,stripe:"",style:{width:"100%"},onSelectionChange:xe},{default:s(()=>[e(_,{type:"selection",width:"50"}),e(_,{prop:"id",label:"ID",width:"50"}),e(_,{label:"分布式节点名称","min-width":"220",sortable:""},{default:s(({row:T})=>[a("div",Hl,[e(H,{class:"server-icon"},{default:s(()=>[e(h(vt))]),_:1}),a("div",ql,[e(d,{content:T.name,placement:"top",disabled:T.name.length<=25},{default:s(()=>[a("span",Kl,f(T.name),1)]),_:2},1032,["content","disabled"]),a("div",Jl,[T.is_online?(g(),O(ne,{key:0,type:"success",size:"small"},{default:s(()=>v[15]||(v[15]=[u("在线",-1)])),_:1,__:[15]})):(g(),O(ne,{key:1,type:"danger",size:"small"},{default:s(()=>v[16]||(v[16]=[u("离线",-1)])),_:1,__:[16]}))])])])]),_:1}),e(_,{label:"IP地址",width:"120"},{default:s(({row:T})=>[a("span",Wl,f(T.ip_address)+":"+f(T.port),1)]),_:1}),e(_,{label:"VH端口",width:"65"},{default:s(({row:T})=>[a("span",null,f(T.vh_port),1)]),_:1}),e(_,{label:"状态",width:"85"},{default:s(({row:T})=>[e(ne,{type:ee(T.status),size:"small"},{default:s(()=>[e(H,{class:"status-icon"},{default:s(()=>[T.status==="online"?(g(),O(h(is),{key:0})):(g(),O(h(rs),{key:1}))]),_:2},1024),u(" "+f(T.status==="online"?"在线":"离线"),1)]),_:2},1032,["type"])]),_:1}),e(_,{label:"设备数量",width:"75"},{default:s(({row:T})=>[a("span",Yl,f(T.device_count||0),1)]),_:1}),e(_,{label:"最后心跳验证",width:"160","class-name":"no-wrap"},{default:s(({row:T})=>[T.last_seen&&T.last_seen!=="null"&&T.last_seen!==""?(g(),N("span",Ql,f(T.last_seen),1)):(g(),N("span",Zl," 无心跳记录 "))]),_:1}),e(_,{label:"最后配置核对",width:"160","class-name":"no-wrap"},{default:s(({row:T})=>[T.last_config_check?(g(),N("span",Xl,f(Pe(T.last_config_check)),1)):(g(),N("span",eo,"无核对记录"))]),_:1}),e(_,{label:"操作",width:"160",fixed:"right","class-name":"no-wrap"},{default:s(({row:T})=>[e(z,{type:"primary",size:"small",onClick:Ie=>Se(T)},{default:s(()=>v[17]||(v[17]=[u(" 详情 ",-1)])),_:2,__:[17]},1032,["onClick"]),_e.value?(g(),O(z,{key:0,type:"warning",size:"small",onClick:Ie=>Ve(T,"restart")},{default:s(()=>v[18]||(v[18]=[u(" 重启 ",-1)])),_:2,__:[18]},1032,["onClick"])):J("",!0),V.value?(g(),O(z,{key:1,type:"danger",size:"small",onClick:Ie=>qe(T)},{default:s(()=>v[19]||(v[19]=[u(" 删除 ",-1)])),_:2,__:[19]},1032,["onClick"])):J("",!0)]),_:1})]),_:1},8,["data"])),[[ge,B.value]]),a("div",to,[e(me,{"current-page":D.value,"onUpdate:currentPage":v[3]||(v[3]=T=>D.value=T),"page-size":y.value,"onUpdate:pageSize":v[4]||(v[4]=T=>y.value=T),"page-sizes":[10,20,50,100],total:i.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:$e,onCurrentChange:Ue},null,8,["current-page","page-size","total"])]),e(pe,{modelValue:F.value,"onUpdate:modelValue":v[12]||(v[12]=T=>F.value=T),title:"添加从服务器",width:"500px",onClose:Oe},{footer:s(()=>[e(z,{onClick:v[11]||(v[11]=T=>F.value=!1)},{default:s(()=>v[20]||(v[20]=[u("取消",-1)])),_:1,__:[20]}),e(z,{type:"primary",onClick:ae,loading:I.value},{default:s(()=>v[21]||(v[21]=[u(" 确认添加 ",-1)])),_:1,__:[21]},8,["loading"])]),default:s(()=>[e(fe,{ref_key:"addFormRef",ref:p,model:C,rules:$,"label-width":"100px"},{default:s(()=>[e(we,{label:"服务器名称",prop:"server_name"},{default:s(()=>[e(re,{modelValue:C.server_name,"onUpdate:modelValue":v[5]||(v[5]=T=>C.server_name=T),placeholder:"请输入服务器名称"},null,8,["modelValue"])]),_:1}),e(we,{label:"IP地址",prop:"server_ip"},{default:s(()=>[e(re,{modelValue:C.server_ip,"onUpdate:modelValue":v[6]||(v[6]=T=>C.server_ip=T),placeholder:"请输入IP地址"},null,8,["modelValue"])]),_:1}),e(we,{label:"服务端口",prop:"server_port"},{default:s(()=>[e(Me,{modelValue:C.server_port,"onUpdate:modelValue":v[7]||(v[7]=T=>C.server_port=T),min:1,max:65535,placeholder:"8889"},null,8,["modelValue"])]),_:1}),e(we,{label:"VH端口",prop:"vh_port"},{default:s(()=>[e(Me,{modelValue:C.vh_port,"onUpdate:modelValue":v[8]||(v[8]=T=>C.vh_port=T),min:1,max:65535,placeholder:"7575"},null,8,["modelValue"])]),_:1}),e(we,{label:"位置"},{default:s(()=>[e(re,{modelValue:C.location,"onUpdate:modelValue":v[9]||(v[9]=T=>C.location=T),placeholder:"请输入服务器位置"},null,8,["modelValue"])]),_:1}),e(we,{label:"描述"},{default:s(()=>[e(re,{modelValue:C.description,"onUpdate:modelValue":v[10]||(v[10]=T=>C.description=T),type:"textarea",placeholder:"请输入服务器描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},lo=at(so,[["__scopeId","data-v-c35ab19a"]]),oo={class:"nested-group-tree"},ao={class:"tree-header"},no={class:"header-actions"},io={class:"tree-content"},ro={class:"tree-node"},uo={class:"node-content"},co={class:"node-info"},po={class:"node-name"},_o={class:"node-tags"},vo={class:"node-stats"},mo={class:"stat-item"},fo={key:0,class:"stat-item"},go={class:"node-actions"},yo={class:"permission-indicators"},ho={class:"action-buttons"},bo={key:1},$o={key:0,class:"level-warning"},ko={class:"dialog-footer"},wo={class:"device-details"},Co={class:"device-header"},To={class:"device-actions"},So={class:"device-name"},Vo={class:"primary-name"},xo={key:0,class:"secondary-name"},Do={class:"reorganize-content"},zo={class:"dialog-footer"},Ao={class:"dialog-footer"},Eo={__name:"NestedGroupTree",props:{refreshTrigger:{type:Number,default:0}},emits:["group-created","group-updated","group-deleted"],setup(ue,{emit:R}){const P=ue,te=R,Y=Ct(),j=c(),B=c(),I=c(),U=c(),x=c([]),F=c(!1),G=c(!1),L=c(!1),q=c(!1),K=c(!1),D=c(!1),y=c(!1),i=c(!1),p=c(null),C=c([]),$=c([]),M={children:"children",label:"name"},V=Le({name:"",group_type:"",description:"",parent_group_id:null}),S=Le({group_name:"",group_description:"",device_ids:[],source_group_id:null}),_e={name:[{required:!0,message:"请输入分组名称",trigger:"blur"},{min:1,max:200,message:"分组名称长度在 1 到 200 个字符",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},he={group_name:[{required:!0,message:"请输入新分组名称",trigger:"blur"},{min:1,max:200,message:"分组名称长度在 1 到 200 个字符",trigger:"blur"}]},Q=c(),ee=Le({id:null,name:"",group_type:"",description:""}),Pe={name:[{required:!0,message:"请输入分组名称",trigger:"blur"},{min:1,max:200,message:"分组名称长度在 1 到 200 个字符",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},Z=oe(()=>{if(!V.parent_group_id)return"";const _=(d,ne)=>{for(const W of d){if(W.id===ne)return W.name;if(W.children){const me=_(W.children,ne);if(me)return me}}return""};return _(x.value,V.parent_group_id)}),be=oe(()=>{if(!V.parent_group_id)return 0;const _=(d,ne)=>{for(const W of d){if(W.id===ne)return W.nesting_level+1;if(W.children){const me=_(W.children,ne);if(me!==null)return me}}return null};return _(x.value,V.parent_group_id)||0}),ve=async()=>{try{const _=await fetch("/api/v1/device-groups/tree",{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(_.ok)x.value=await _.json(),console.log("嵌套分组树加载成功:",x.value);else throw new Error(`HTTP ${_.status}`)}catch(_){console.error("加载嵌套分组树失败:",_),m.error(`加载嵌套分组树失败: ${_.message}`)}},$e=_=>{if(_.nesting_level>=3){m.warning("已达到最大嵌套深度（4层），无法创建子分组");return}V.parent_group_id=_.id,V.name="",V.group_type="nested",V.description="",F.value=!0},Ue=async()=>{try{await B.value.validate(),K.value=!0;const _=await fetch("/api/v1/device-groups/",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify(V)});if(_.ok){const d=await _.json();m.success("分组创建成功"),F.value=!1,Se(),ve(),te("group-created",d)}else{const d=await _.json();throw new Error(d.detail||`HTTP ${_.status}`)}}catch(_){console.error("创建分组失败:",_),m.error(`创建分组失败: ${_.message}`)}finally{K.value=!1}},Se=()=>{V.name="",V.group_type="",V.description="",V.parent_group_id=null,kt(()=>{var _;(_=B.value)==null||_.clearValidate()})},Ve=_=>{Y.push({name:"DeviceCenterGroupDetail",params:{id:_.id}})},ae=_=>{ee.id=_.id,ee.name=_.name,ee.group_type=_.group_type,ee.description=_.description||"",q.value=!0},Oe=async _=>{try{await Ee.confirm(`确定要删除分组 "${_.name}" 吗？此操作将同时删除所有子分组。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),m.info("删除功能开发中...")}catch{}},xe=async _=>{try{p.value=_,D.value=!0,G.value=!0;const d=await fetch(`/api/v1/device-groups/${_.id}/devices`,{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(d.ok)C.value=await d.json();else throw new Error(`HTTP ${d.status}`)}catch(d){console.error("加载分组设备失败:",d),m.error(`加载分组设备失败: ${d.message}`)}finally{D.value=!1}},Re=()=>{p.value=null,C.value=[],G.value=!1},Fe=_=>{$.value=_,S.device_ids=_.map(d=>d.id)},De=()=>{S.group_name="",S.group_description="",S.device_ids=[],S.source_group_id=null,$.value=[],L.value=!1},He=async()=>{try{await I.value.validate(),y.value=!0;const _={group_name:S.group_name,group_description:S.group_description,device_ids:S.device_ids,source_group_id:p.value.id},d=await fetch("/api/v1/device-groups/reorganize",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify(_)});if(d.ok){const ne=await d.json();m.success(`成功创建新分组: ${ne.new_group_name}`),L.value=!1,De(),ve(),te("group-created",ne)}else{const ne=await d.json();throw new Error(ne.detail||`HTTP ${d.status}`)}}catch(_){console.error("设备重新分组失败:",_),m.error(`设备重新分组失败: ${_.message}`)}finally{y.value=!1}},qe=async()=>{try{await Q.value.validate(),i.value=!0;const _=await fetch(`/api/v1/device-groups/${ee.id}`,{method:"PUT",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify({name:ee.name,group_type:ee.group_type,description:ee.description})});if(_.ok)m.success("分组信息更新成功"),q.value=!1,k(),ve();else{const d=await _.json();throw new Error(d.detail||`HTTP ${_.status}`)}}catch(_){console.error("更新分组失败:",_),m.error(`更新分组失败: ${_.message}`)}finally{i.value=!1}},k=()=>{ee.id=null,ee.name="",ee.group_type="",ee.description="",kt(()=>{var _;(_=Q.value)==null||_.clearValidate()})},v=_=>({server:"服务器",mixed:"混合",single:"单设备",nested:"嵌套"})[_]||_,H=_=>({server:"success",mixed:"primary",single:"warning",nested:"info",reorganized:"danger"})[_]||"info",z=_=>({online:"在线",offline:"离线",busy:"占用",available:"可用",error:"错误"})[_]||_,ie=_=>({online:"success",offline:"info",busy:"warning",available:"success",error:"danger"})[_]||"info",se=_=>({ca_lock:"CA锁",encryption_key:"加密锁",bank_ukey:"银行U盾"})[_]||_,re=_=>({ca_lock:"danger",encryption_key:"warning",bank_ukey:"primary"})[_]||"info";return dt(()=>P.refreshTrigger,()=>{ve()}),Ze(()=>{ve()}),(_,d)=>{const ne=nt,W=it,me=ut,we=Tt,Me=Ft,fe=ft,pe=It,ge=yt,T=gt,Ie=Ht,je=Ut,ze=mt,Ke=zt("Operation"),ye=Vt,r=St,l=ct;return g(),N("div",oo,[a("div",ao,[d[19]||(d[19]=a("h3",null,"嵌套分组管理",-1)),a("div",no,[e(W,{type:"primary",onClick:ve},{default:s(()=>[e(ne,null,{default:s(()=>[e(h(ot))]),_:1}),d[17]||(d[17]=u(" 刷新 ",-1))]),_:1,__:[17]}),e(W,{type:"success",onClick:d[0]||(d[0]=t=>F.value=!0)},{default:s(()=>[e(ne,null,{default:s(()=>[e(h(Pt))]),_:1}),d[18]||(d[18]=u(" 创建分组 ",-1))]),_:1,__:[18]})])]),a("div",io,[e(Me,{ref_key:"treeRef",ref:j,data:x.value,props:M,"expand-on-click-node":!1,"default-expand-all":!1,"node-key":"id",class:"group-tree"},{default:s(({node:t,data:o})=>[a("div",ro,[a("div",uo,[a("div",co,[a("span",po,f(o.name),1),a("div",_o,[e(me,{type:H(o.group_type),size:"small"},{default:s(()=>[u(f(v(o.group_type)),1)]),_:2},1032,["type"]),e(me,{type:"info",size:"small"},{default:s(()=>[u(" L"+f(o.nesting_level),1)]),_:2},1024)])]),a("div",vo,[a("span",mo,[e(ne,null,{default:s(()=>[e(h(vt))]),_:1}),u(" "+f(o.device_count)+"设备 ",1)]),o.child_count>0?(g(),N("span",fo,[e(ne,null,{default:s(()=>[e(h(us))]),_:1}),u(" "+f(o.child_count)+"子组 ",1)])):J("",!0)])]),a("div",go,[a("div",yo,[o.is_readonly?(g(),O(me,{key:0,type:"info",size:"small"},{default:s(()=>d[20]||(d[20]=[u(" 只读 ",-1)])),_:1,__:[20]})):J("",!0),o.can_view_devices?J("",!0):(g(),O(me,{key:1,type:"warning",size:"small"},{default:s(()=>d[21]||(d[21]=[u(" 无设备权限 ",-1)])),_:1,__:[21]}))]),a("div",ho,[e(W,{type:"primary",size:"small",onClick:n=>Ve(o)},{default:s(()=>d[22]||(d[22]=[u(" 详情 ",-1)])),_:2,__:[22]},1032,["onClick"]),o.can_view_devices?(g(),O(W,{key:0,type:"info",size:"small",onClick:n=>xe(o)},{default:s(()=>d[23]||(d[23]=[u(" 查看设备 ",-1)])),_:2,__:[23]},1032,["onClick"])):J("",!0),o.can_manage?(g(),N(Ne,{key:1},[e(W,{type:"success",size:"small",onClick:n=>$e(o),disabled:o.nesting_level>=3},{default:s(()=>[o.nesting_level>=3?(g(),O(we,{key:0,content:"已达到最大嵌套深度（4层）",placement:"top"},{default:s(()=>d[24]||(d[24]=[a("span",null,"添加子组",-1)])),_:1,__:[24]})):(g(),N("span",bo,"添加子组"))]),_:2},1032,["onClick","disabled"]),e(W,{type:"warning",size:"small",onClick:n=>ae(o)},{default:s(()=>d[25]||(d[25]=[u(" 编辑 ",-1)])),_:2,__:[25]},1032,["onClick"]),e(W,{type:"danger",size:"small",onClick:n=>Oe(o)},{default:s(()=>d[26]||(d[26]=[u(" 删除 ",-1)])),_:2,__:[26]},1032,["onClick"])],64)):J("",!0)])])])]),_:1},8,["data"])]),e(ze,{modelValue:F.value,"onUpdate:modelValue":d[5]||(d[5]=t=>F.value=t),title:V.parent_group_id?"创建子分组":"创建顶级分组",width:"500px",onClose:Se},{footer:s(()=>[a("div",ko,[e(W,{onClick:d[4]||(d[4]=t=>F.value=!1)},{default:s(()=>d[27]||(d[27]=[u("取消",-1)])),_:1,__:[27]}),e(W,{type:"primary",onClick:Ue,loading:K.value,disabled:be.value>=4},{default:s(()=>d[28]||(d[28]=[u(" 创建 ",-1)])),_:1,__:[28]},8,["loading","disabled"])])]),default:s(()=>[e(je,{ref_key:"createFormRef",ref:B,model:V,rules:_e,"label-width":"120px"},{default:s(()=>[V.parent_group_id?(g(),O(pe,{key:0,label:"父分组"},{default:s(()=>[e(fe,{value:Z.value,disabled:"",placeholder:"顶级分组"},null,8,["value"])]),_:1})):J("",!0),e(pe,{label:"分组名称",prop:"name"},{default:s(()=>[e(fe,{modelValue:V.name,"onUpdate:modelValue":d[1]||(d[1]=t=>V.name=t),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(pe,{label:"分组类型",prop:"group_type"},{default:s(()=>[e(T,{modelValue:V.group_type,"onUpdate:modelValue":d[2]||(d[2]=t=>V.group_type=t),placeholder:"请选择分组类型",style:{width:"100%"}},{default:s(()=>[e(ge,{label:"服务器分组",value:"server"}),e(ge,{label:"混合分组",value:"mixed"}),e(ge,{label:"单设备分组",value:"single"}),e(ge,{label:"嵌套分组",value:"nested"})]),_:1},8,["modelValue"])]),_:1}),e(pe,{label:"分组描述",prop:"description"},{default:s(()=>[e(fe,{modelValue:V.description,"onUpdate:modelValue":d[3]||(d[3]=t=>V.description=t),type:"textarea",rows:3,placeholder:"请输入分组描述"},null,8,["modelValue"])]),_:1}),V.parent_group_id?(g(),O(pe,{key:1,label:"嵌套层级"},{default:s(()=>[e(fe,{value:`第 ${be.value} 层`,disabled:""},null,8,["value"]),be.value>=3?(g(),N("div",$o,[e(Ie,{title:"已达到最大嵌套深度（4层）",type:"warning",closable:!1,"show-icon":""})])):J("",!0)]),_:1})):J("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(ze,{modelValue:G.value,"onUpdate:modelValue":d[7]||(d[7]=t=>G.value=t),title:"分组设备详情",width:"80%",onClose:Re},{default:s(()=>{var t,o;return[a("div",wo,[a("div",Co,[a("h4",null,f((t=p.value)==null?void 0:t.name)+" - 设备列表",1),a("div",To,[(o=p.value)!=null&&o.can_manage?(g(),O(W,{key:0,type:"success",onClick:d[6]||(d[6]=n=>L.value=!0)},{default:s(()=>[e(ne,null,{default:s(()=>[e(Ke)]),_:1}),d[29]||(d[29]=u(" 重新分组 ",-1))]),_:1,__:[29]})):J("",!0)])]),Qe((g(),O(r,{data:C.value,stripe:"",style:{width:"100%"}},{default:s(()=>[e(ye,{prop:"name",label:"设备名称","min-width":"150"},{default:s(({row:n})=>[a("div",So,[a("span",Vo,f(n.custom_name||n.name),1),n.custom_name&&n.name!==n.custom_name?(g(),N("span",xo," ("+f(n.name)+") ",1)):J("",!0)])]),_:1}),e(ye,{prop:"device_type",label:"设备类型",width:"120"},{default:s(({row:n})=>[e(me,{type:re(n.device_type),size:"small"},{default:s(()=>[u(f(se(n.device_type)),1)]),_:2},1032,["type"])]),_:1}),e(ye,{prop:"status",label:"状态",width:"100"},{default:s(({row:n})=>[e(me,{type:ie(n.status),size:"small"},{default:s(()=>[u(f(z(n.status)),1)]),_:2},1032,["type"])]),_:1}),e(ye,{prop:"slave_server_name",label:"所属服务器",width:"150"}),e(ye,{prop:"vid",label:"VID",width:"80"}),e(ye,{prop:"pid",label:"PID",width:"80"}),e(ye,{prop:"description",label:"描述","min-width":"200"})]),_:1},8,["data"])),[[l,D.value]])])]}),_:1},8,["modelValue"]),e(ze,{modelValue:L.value,"onUpdate:modelValue":d[11]||(d[11]=t=>L.value=t),title:"设备重新分组",width:"60%",onClose:De},{footer:s(()=>[a("div",zo,[e(W,{onClick:d[10]||(d[10]=t=>L.value=!1)},{default:s(()=>d[30]||(d[30]=[u("取消",-1)])),_:1,__:[30]}),e(W,{type:"primary",onClick:He,loading:y.value,disabled:$.value.length===0},{default:s(()=>d[31]||(d[31]=[u(" 创建新分组 ",-1)])),_:1,__:[31]},8,["loading","disabled"])])]),default:s(()=>[a("div",Do,[e(je,{ref_key:"reorganizeFormRef",ref:I,model:S,rules:he,"label-width":"120px"},{default:s(()=>[e(pe,{label:"新分组名称",prop:"group_name"},{default:s(()=>[e(fe,{modelValue:S.group_name,"onUpdate:modelValue":d[8]||(d[8]=t=>S.group_name=t),placeholder:"请输入新分组名称"},null,8,["modelValue"])]),_:1}),e(pe,{label:"分组描述",prop:"group_description"},{default:s(()=>[e(fe,{modelValue:S.group_description,"onUpdate:modelValue":d[9]||(d[9]=t=>S.group_description=t),type:"textarea",rows:3,placeholder:"请输入分组描述"},null,8,["modelValue"])]),_:1}),e(pe,{label:"选择设备"},{default:s(()=>[e(r,{ref_key:"deviceSelectionTable",ref:U,data:C.value,onSelectionChange:Fe,"max-height":"300"},{default:s(()=>[e(ye,{type:"selection",width:"55"}),e(ye,{prop:"name",label:"设备名称","min-width":"150"}),e(ye,{prop:"device_type",label:"类型",width:"100"}),e(ye,{prop:"status",label:"状态",width:"80"})]),_:1},8,["data"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"]),e(ze,{modelValue:q.value,"onUpdate:modelValue":d[16]||(d[16]=t=>q.value=t),title:"编辑分组",width:"500px",onClose:k},{footer:s(()=>[a("div",Ao,[e(W,{onClick:d[15]||(d[15]=t=>q.value=!1)},{default:s(()=>d[32]||(d[32]=[u("取消",-1)])),_:1,__:[32]}),e(W,{type:"primary",onClick:qe,loading:i.value},{default:s(()=>d[33]||(d[33]=[u(" 确认修改 ",-1)])),_:1,__:[33]},8,["loading"])])]),default:s(()=>[e(je,{ref_key:"editFormRef",ref:Q,model:ee,rules:Pe,"label-width":"100px"},{default:s(()=>[e(pe,{label:"分组名称",prop:"name"},{default:s(()=>[e(fe,{modelValue:ee.name,"onUpdate:modelValue":d[12]||(d[12]=t=>ee.name=t),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(pe,{label:"分组类型",prop:"group_type"},{default:s(()=>[e(T,{modelValue:ee.group_type,"onUpdate:modelValue":d[13]||(d[13]=t=>ee.group_type=t),placeholder:"请选择分组类型",style:{width:"100%"}},{default:s(()=>[e(ge,{label:"标准分组",value:"standard"}),e(ge,{label:"临时分组",value:"temporary"}),e(ge,{label:"专用分组",value:"dedicated"}),e(ge,{label:"重组分组",value:"reorganized"})]),_:1},8,["modelValue"])]),_:1}),e(pe,{label:"描述信息",prop:"description"},{default:s(()=>[e(fe,{modelValue:ee.description,"onUpdate:modelValue":d[14]||(d[14]=t=>ee.description=t),type:"textarea",rows:3,placeholder:"请输入分组描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Lo=at(Eo,[["__scopeId","data-v-81ed4342"]]),Po={class:"device-group-management"},Uo={class:"toolbar"},Io={class:"toolbar-left"},jo={class:"toolbar-right"},Oo={class:"view-tabs"},Mo={class:"list-view"},Bo={class:"group-name"},Go={class:"device-count"},No={key:1,class:"text-muted"},Ro={class:"user-count"},Fo={class:"tree-view"},Ho={class:"pagination-container"},qo={class:"assignment-content"},Ko={class:"assignment-stats"},Jo={class:"device-selection",style:{"margin-top":"20px"}},Wo={class:"dialog-footer"},Yo={__name:"DeviceGroupManagement",emits:["stats-update"],setup(ue,{expose:R,emit:P}){const te=P,Y=Ct(),j=Xe(),B=c(!1),I=c(!1),U=c(!1),x=c(!1),F=c(!1),G=c(!1),L=c([]),q=c(""),K=c(""),D=c("list"),y=c(0),i=c(!1),p=c(null),C=c([]),$=c([]),M=c(),V=c(1),S=c(20),_e=c(0),he=c(),Q=Le({name:"",group_type:"",description:""}),ee={name:[{required:!0,message:"请输入分组名称",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},Pe=c(),Z=Le({id:null,name:"",group_type:"",description:""}),be={name:[{required:!0,message:"请输入分组名称",trigger:"blur"}],group_type:[{required:!0,message:"请选择分组类型",trigger:"change"}]},ve=oe(()=>{let r=L.value;if(q.value&&(r=r.filter(l=>l.group_type===q.value)),K.value){const l=K.value.toLowerCase();r=r.filter(t=>t.name.toLowerCase().includes(l))}return r}),$e=r=>({single_device:"info",multi_device:"primary",server_group:"success",cross_group:"warning",batch_group:"danger"})[r]||"",Ue=r=>({single_device:"单设备分组",multi_device:"多设备分组",server_group:"服务器分组",cross_group:"交叉分组",batch_group:"批量分组"})[r]||"未知类型",Se=r=>{switch(r){case"single_device":m.info("单设备分组：每个分组只包含一个设备");break;case"multi_device":m.info("多设备分组：可以包含多个不同的设备");break;case"server_group":m.info("服务器分组：按从服务器整体进行分组");break;case"cross_group":m.info("交叉分组：设备可以同时属于多个分组");break;case"batch_group":m.info("批量分组：支持批量设备的分组操作");break}},Ve=r=>r?new Date(r).toLocaleString():"",ae=async()=>{B.value=!0;try{const r=await fetch("/api/v1/device-groups/",{headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"}});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const l=await r.json();console.log("获取到的设备分组数据:",l),L.value=l.map(t=>({id:t.id,name:t.name,group_type:t.group_type,device_count:t.device_count||0,has_virtual_devices:t.has_virtual_devices||!1,user_count:t.user_count||0,description:t.description||"",created_at:t.created_at,updated_at:t.updated_at,is_active:t.is_active})),console.log("处理后的设备分组数据:",L.value),_e.value=L.value.length,Oe()}catch(r){console.error("加载设备分组数据失败:",r),m.error(`加载设备分组数据失败: ${r.message}`),L.value=[],_e.value=0}finally{B.value=!1}},Oe=()=>{const r={total:L.value.length,server:L.value.filter(l=>l.group_type==="server").length,mixed:L.value.filter(l=>l.group_type==="mixed").length};te("stats-update",r)},xe=()=>{},Re=r=>{S.value=r,ae()},Fe=r=>{V.value=r,ae()},De=r=>{try{console.log("点击详情按钮，分组信息:",r),console.log("准备跳转到路径:",`/device-center/device-group/${r.id}`),Y.push(`/device-center/device-group/${r.id}`).then(()=>{console.log("路由跳转成功")}).catch(l=>{console.error("路由跳转失败:",l),m.error(`跳转失败: ${l.message}`)})}catch(l){console.error("viewGroupDetail函数执行失败:",l),m.error(`操作失败: ${l.message}`)}},He=r=>{Object.assign(Z,{id:r.id,name:r.name,group_type:r.group_type,description:r.description}),F.value=!0},qe=async r=>{try{await Ee.confirm(`确定要删除分组 "${r.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}),m.success(`分组 "${r.name}" 删除成功`),ae()}catch{}},k=async()=>{try{await he.value.validate(),I.value=!0;const r=await fetch("/api/v1/device-groups/",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("omnilink_token")}`,"Content-Type":"application/json"},body:JSON.stringify({name:Q.name,group_type:Q.type,description:Q.description,auto_virtual:!1})});if(!r.ok){const t=await r.json();throw new Error(t.detail||`HTTP ${r.status}`)}const l=await r.json();console.log("分组创建成功:",l),m.success("设备分组创建成功"),x.value=!1,H(),ae()}catch(r){console.error("创建设备分组失败:",r),m.error(`创建设备分组失败: ${r.message}`)}finally{I.value=!1}},v=async()=>{try{await Pe.value.validate(),U.value=!0,await new Promise(r=>setTimeout(r,1e3)),m.success("设备分组修改成功"),F.value=!1,z(),ae()}catch(r){console.error("修改设备分组失败:",r),m.error("修改设备分组失败")}finally{U.value=!1}},H=()=>{var r;Object.assign(Q,{name:"",group_type:"",description:""}),(r=he.value)==null||r.clearValidate()},z=()=>{var r;Object.assign(Z,{id:null,name:"",group_type:"",description:""}),(r=Pe.value)==null||r.clearValidate()};R({refreshData:ae});const ie=r=>{console.log("分组创建事件:",r),m.success(`分组 "${r.group_name}" 已创建`),ae()},se=r=>{console.log("分组更新事件:",r),ae()},re=r=>{console.log("设备分配事件:",r),m.info(`${r.added_count||r.total_assigned} 个设备已分配到分组`),ae()},_=async()=>{G.value=!0,await Promise.all([d(),ae()])},d=async()=>{try{const r=await fetch("/api/v1/devices",{headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"}});if(r.ok){const l=await r.json();$.value=l.devices||[]}else throw new Error(`HTTP ${r.status}`)}catch(r){console.error("加载可用设备失败:",r),m.error(`加载可用设备失败: ${r.message}`)}},ne=r=>{p.value=r,C.value=[],M.value&&M.value.clearSelection()},W=r=>{C.value=r},me=async()=>{try{i.value=!0;const r={assignments:[{group_id:p.value,device_ids:C.value.map(t=>t.id)}]},l=await fetch("/api/v1/device-groups/batch-assign",{method:"POST",headers:{Authorization:`Bearer ${j.token}`,"Content-Type":"application/json"},body:JSON.stringify(r)});if(l.ok){const t=await l.json(),o=t.assignments?t.assignments.reduce((n,A)=>n+A.added_devices.length,0):0;m.success(`成功分配 ${o} 个设备，可继续分配其他设备`),C.value=[],M.value&&M.value.clearSelection(),await d(),ae()}else{const t=await l.json();throw new Error(t.detail||`HTTP ${l.status}`)}}catch(r){console.error("设备分配失败:",r),m.error(`设备分配失败: ${r.message}`)}finally{i.value=!1}},we=()=>{G.value=!1,m.success("设备分配操作已完成")},Me=()=>{p.value=null,C.value=[],$.value=[],M.value&&M.value.clearSelection()},fe=r=>({ca_lock:"CA锁",encryption_key:"加密锁",bank_ukey:"银行U盾",other:"其他设备"})[r]||r,pe=r=>({online:"在线",offline:"离线",busy:"占用",available:"可用",error:"错误"})[r]||r,ge=r=>({online:"success",offline:"info",busy:"warning",available:"success",error:"danger"})[r]||"info",T=r=>{console.log("切换视图:",r),r==="tree"&&y.value++},Ie=r=>{console.log("嵌套分组创建:",r),ae(),y.value++},je=r=>{console.log("嵌套分组更新:",r),ae(),y.value++},ze=r=>{console.log("嵌套分组删除:",r),ae(),y.value++},Ke=()=>{X.subscribe("device_updates"),X.on("device_group_created",ie),X.on("device_groups_batch_created",se),X.on("devices_assigned_to_group",re),X.on("devices_batch_assigned",re)},ye=()=>{X.off("device_group_created",ie),X.off("device_groups_batch_created",se),X.off("devices_assigned_to_group",re),X.off("devices_batch_assigned",re),X.unsubscribe("device_updates")};return Ze(()=>{ae(),Ke()}),wt(()=>{ye()}),(r,l)=>{const t=nt,o=it,n=yt,A=gt,le=ft,Te=zt("List"),Je=jt,We=zt("Operation"),de=Ot,ce=Vt,Ce=ut,Be=Tt,Ae=St,et=Lt,Ge=It,ke=Ut,tt=mt,rt=Ht,E=Bt,b=Mt,Ye=ct;return g(),N("div",Po,[a("div",Uo,[a("div",Io,[e(o,{type:"primary",onClick:ae,loading:B.value},{default:s(()=>[e(t,null,{default:s(()=>[e(h(ot))]),_:1}),l[19]||(l[19]=u(" 刷新列表 ",-1))]),_:1,__:[19]},8,["loading"]),h(j).hasPermission("device.group")?(g(),O(o,{key:0,type:"success",onClick:l[0]||(l[0]=w=>x.value=!0)},{default:s(()=>[e(t,null,{default:s(()=>[e(h(Pt))]),_:1}),l[20]||(l[20]=u(" 创建分组 ",-1))]),_:1,__:[20]})):J("",!0),h(j).hasPermission("device.assign")?(g(),O(o,{key:1,type:"primary",onClick:_},{default:s(()=>[e(t,null,{default:s(()=>[e(h(At))]),_:1}),l[21]||(l[21]=u(" 设备分配 ",-1))]),_:1,__:[21]})):J("",!0)]),a("div",jo,[e(A,{modelValue:q.value,"onUpdate:modelValue":l[1]||(l[1]=w=>q.value=w),placeholder:"筛选分组类型",style:{width:"180px","margin-right":"8px"},clearable:"",onChange:xe},{default:s(()=>[e(n,{label:"全部分组",value:""}),e(n,{label:"单设备分组",value:"single_device"}),e(n,{label:"多设备分组",value:"multi_device"}),e(n,{label:"服务器分组",value:"server_group"}),e(n,{label:"交叉分组",value:"cross_group"}),e(n,{label:"批量分组",value:"batch_group"})]),_:1},8,["modelValue"]),e(le,{modelValue:K.value,"onUpdate:modelValue":l[2]||(l[2]=w=>K.value=w),placeholder:"搜索分组名称...",style:{width:"200px"},clearable:"",onInput:xe},{prefix:s(()=>[e(t,null,{default:s(()=>[e(h(st))]),_:1})]),_:1},8,["modelValue"])])]),a("div",Oo,[e(de,{modelValue:D.value,"onUpdate:modelValue":l[3]||(l[3]=w=>D.value=w),onTabChange:T},{default:s(()=>[e(Je,{label:"列表视图",name:"list"},{label:s(()=>[a("span",null,[e(t,null,{default:s(()=>[e(Te)]),_:1}),l[22]||(l[22]=u(" 列表视图 ",-1))])]),_:1}),e(Je,{label:"嵌套树视图",name:"tree"},{label:s(()=>[a("span",null,[e(t,null,{default:s(()=>[e(We)]),_:1}),l[23]||(l[23]=u(" 嵌套树视图 ",-1))])]),_:1})]),_:1},8,["modelValue"])]),Qe(a("div",Mo,[Qe((g(),O(Ae,{data:ve.value,stripe:"",style:{width:"100%"}},{default:s(()=>[e(ce,{prop:"id",label:"ID",width:"80"}),e(ce,{label:"分组名称","min-width":"150"},{default:s(({row:w})=>[a("div",Bo,[e(t,{class:"group-icon"},{default:s(()=>[e(h(At))]),_:1}),a("span",null,f(w.name),1)])]),_:1}),e(ce,{label:"分组类型",width:"120"},{default:s(({row:w})=>[e(Ce,{type:$e(w.group_type),size:"small"},{default:s(()=>[u(f(Ue(w.group_type)),1)]),_:2},1032,["type"])]),_:1}),e(ce,{label:"设备数量",width:"100"},{default:s(({row:w})=>[a("span",Go,f(w.device_count||0),1)]),_:1}),e(ce,{label:"虚拟设备",width:"100"},{default:s(({row:w})=>[w.has_virtual_devices?(g(),O(Ce,{key:0,type:"warning",size:"small"},{default:s(()=>l[24]||(l[24]=[u(" 有占位 ",-1)])),_:1,__:[24]})):(g(),N("span",No,"无"))]),_:1}),e(ce,{label:"权限用户",width:"100"},{default:s(({row:w})=>[a("span",Ro,f(w.user_count||0),1)]),_:1}),e(ce,{label:"创建时间",width:"150"},{default:s(({row:w})=>[a("span",null,f(Ve(w.created_at)),1)]),_:1}),e(ce,{prop:"description",label:"描述","min-width":"150"}),e(ce,{label:"操作",width:"280",fixed:"right"},{default:s(({row:w})=>[e(o,{type:"primary",size:"small",onClick:pt=>De(w)},{default:s(()=>l[25]||(l[25]=[u(" 详情 ",-1)])),_:2,__:[25]},1032,["onClick"]),w.is_readonly?(g(),O(Ce,{key:0,type:"info",size:"small",style:{margin:"0 5px"}},{default:s(()=>l[26]||(l[26]=[u(" 只读 ",-1)])),_:1,__:[26]})):J("",!0),w.can_manage?(g(),N(Ne,{key:1},[e(o,{type:"warning",size:"small",onClick:pt=>He(w)},{default:s(()=>l[27]||(l[27]=[u(" 编辑 ",-1)])),_:2,__:[27]},1032,["onClick"]),e(o,{type:"danger",size:"small",onClick:pt=>qe(w)},{default:s(()=>l[28]||(l[28]=[u(" 删除 ",-1)])),_:2,__:[28]},1032,["onClick"])],64)):w.is_readonly?(g(),O(Be,{key:2,content:"此分组由上级管理员创建，您只有查看权限",placement:"top"},{default:s(()=>[e(o,{type:"info",size:"small",disabled:""},{default:s(()=>l[29]||(l[29]=[u(" 无权限 ",-1)])),_:1,__:[29]})]),_:1})):J("",!0)]),_:1})]),_:1},8,["data"])),[[Ye,B.value]])],512),[[Nt,D.value==="list"]]),Qe(a("div",Fo,[e(Lo,{"refresh-trigger":y.value,onGroupCreated:Ie,onGroupUpdated:je,onGroupDeleted:ze},null,8,["refresh-trigger"])],512),[[Nt,D.value==="tree"]]),a("div",Ho,[e(et,{"current-page":V.value,"onUpdate:currentPage":l[4]||(l[4]=w=>V.value=w),"page-size":S.value,"onUpdate:pageSize":l[5]||(l[5]=w=>S.value=w),"page-sizes":[10,20,50,100],total:_e.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Re,onCurrentChange:Fe},null,8,["current-page","page-size","total"])]),e(tt,{modelValue:x.value,"onUpdate:modelValue":l[10]||(l[10]=w=>x.value=w),title:"创建设备分组",width:"500px",onClose:H},{footer:s(()=>[e(o,{onClick:l[9]||(l[9]=w=>x.value=!1)},{default:s(()=>l[35]||(l[35]=[u("取消",-1)])),_:1,__:[35]}),e(o,{type:"primary",onClick:k,loading:I.value},{default:s(()=>l[36]||(l[36]=[u(" 确认创建 ",-1)])),_:1,__:[36]},8,["loading"])]),default:s(()=>[e(ke,{ref_key:"createFormRef",ref:he,model:Q,rules:ee,"label-width":"100px"},{default:s(()=>[e(Ge,{label:"分组名称",prop:"name"},{default:s(()=>[e(le,{modelValue:Q.name,"onUpdate:modelValue":l[6]||(l[6]=w=>Q.name=w),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(Ge,{label:"分组类型",prop:"group_type"},{default:s(()=>[e(A,{modelValue:Q.group_type,"onUpdate:modelValue":l[7]||(l[7]=w=>Q.group_type=w),placeholder:"请选择分组类型",onChange:Se},{default:s(()=>[e(n,{label:"单设备分组",value:"single_device"},{default:s(()=>l[30]||(l[30]=[a("div",{class:"option-content"},[a("div",{class:"option-title"},"单设备分组"),a("div",{class:"option-desc"},"一个设备独立成组")],-1)])),_:1,__:[30]}),e(n,{label:"多设备分组",value:"multi_device"},{default:s(()=>l[31]||(l[31]=[a("div",{class:"option-content"},[a("div",{class:"option-title"},"多设备分组"),a("div",{class:"option-desc"},"多个设备组成一个分组")],-1)])),_:1,__:[31]}),e(n,{label:"服务器分组",value:"server_group"},{default:s(()=>l[32]||(l[32]=[a("div",{class:"option-content"},[a("div",{class:"option-title"},"服务器分组"),a("div",{class:"option-desc"},"以从服务器为单位进行分组")],-1)])),_:1,__:[32]}),e(n,{label:"交叉分组",value:"cross_group"},{default:s(()=>l[33]||(l[33]=[a("div",{class:"option-content"},[a("div",{class:"option-title"},"交叉分组"),a("div",{class:"option-desc"},"一个设备可属于多个分组")],-1)])),_:1,__:[33]}),e(n,{label:"批量分组",value:"batch_group"},{default:s(()=>l[34]||(l[34]=[a("div",{class:"option-content"},[a("div",{class:"option-title"},"批量分组"),a("div",{class:"option-desc"},"支持批量设备的分组操作")],-1)])),_:1,__:[34]})]),_:1},8,["modelValue"])]),_:1}),e(Ge,{label:"描述"},{default:s(()=>[e(le,{modelValue:Q.description,"onUpdate:modelValue":l[8]||(l[8]=w=>Q.description=w),type:"textarea",placeholder:"请输入分组描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(tt,{modelValue:F.value,"onUpdate:modelValue":l[15]||(l[15]=w=>F.value=w),title:"编辑设备分组",width:"500px",onClose:z},{footer:s(()=>[e(o,{onClick:l[14]||(l[14]=w=>F.value=!1)},{default:s(()=>l[37]||(l[37]=[u("取消",-1)])),_:1,__:[37]}),e(o,{type:"primary",onClick:v,loading:U.value},{default:s(()=>l[38]||(l[38]=[u(" 确认修改 ",-1)])),_:1,__:[38]},8,["loading"])]),default:s(()=>[e(ke,{ref_key:"editFormRef",ref:Pe,model:Z,rules:be,"label-width":"100px"},{default:s(()=>[e(Ge,{label:"分组名称",prop:"name"},{default:s(()=>[e(le,{modelValue:Z.name,"onUpdate:modelValue":l[11]||(l[11]=w=>Z.name=w),placeholder:"请输入分组名称"},null,8,["modelValue"])]),_:1}),e(Ge,{label:"分组类型",prop:"group_type"},{default:s(()=>[e(A,{modelValue:Z.group_type,"onUpdate:modelValue":l[12]||(l[12]=w=>Z.group_type=w),placeholder:"请选择分组类型"},{default:s(()=>[e(n,{label:"单设备分组",value:"single_device"}),e(n,{label:"多设备分组",value:"multi_device"}),e(n,{label:"服务器分组",value:"server_group"}),e(n,{label:"交叉分组",value:"cross_group"}),e(n,{label:"批量分组",value:"batch_group"})]),_:1},8,["modelValue"])]),_:1}),e(Ge,{label:"描述"},{default:s(()=>[e(le,{modelValue:Z.description,"onUpdate:modelValue":l[13]||(l[13]=w=>Z.description=w),type:"textarea",placeholder:"请输入分组描述",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(tt,{modelValue:G.value,"onUpdate:modelValue":l[18]||(l[18]=w=>G.value=w),title:"设备分配",width:"80%",onClose:Me},{footer:s(()=>[a("div",Wo,[e(o,{onClick:l[17]||(l[17]=w=>G.value=!1)},{default:s(()=>l[42]||(l[42]=[u("取消",-1)])),_:1,__:[42]}),e(o,{type:"success",onClick:we},{default:s(()=>l[43]||(l[43]=[u(" 完成分配 ",-1)])),_:1,__:[43]}),e(o,{type:"primary",onClick:me,loading:i.value,disabled:!p.value||C.value.length===0},{default:s(()=>l[44]||(l[44]=[u(" 分配设备 ",-1)])),_:1,__:[44]},8,["loading","disabled"])])]),default:s(()=>[a("div",qo,[e(rt,{title:"批量分配提示",description:"选择目标分组和设备后点击【分配设备】，分配成功后可继续选择其他设备进行下一轮分配，完成所有操作后点击【完成分配】关闭对话框。",type:"info",closable:!1,style:{"margin-bottom":"20px"}}),e(b,{gutter:20},{default:s(()=>[e(E,{span:12},{default:s(()=>[l[39]||(l[39]=a("h4",null,"选择设备分组",-1)),e(A,{modelValue:p.value,"onUpdate:modelValue":l[16]||(l[16]=w=>p.value=w),placeholder:"请选择目标分组",style:{width:"100%"},onChange:ne},{default:s(()=>[(g(!0),N(Ne,null,lt(L.value,w=>(g(),O(n,{key:w.id,label:w.name,value:w.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1,__:[39]}),e(E,{span:12},{default:s(()=>[l[40]||(l[40]=a("h4",null,"设备分配状态",-1)),a("div",Ko,[e(Ce,{type:"info"},{default:s(()=>[u("待分配: "+f($.value.length),1)]),_:1}),e(Ce,{type:"success",style:{"margin-left":"10px"}},{default:s(()=>[u("已选择: "+f(C.value.length),1)]),_:1})])]),_:1,__:[40]})]),_:1}),a("div",Jo,[l[41]||(l[41]=a("h4",null,"选择设备",-1)),e(Ae,{ref_key:"deviceSelectionTable",ref:M,data:$.value,onSelectionChange:W,"max-height":"400"},{default:s(()=>[e(ce,{type:"selection",width:"55"}),e(ce,{label:"设备名称","min-width":"150"},{default:s(({row:w})=>[u(f(w.custom_name||w.device_name||w.name||"未知设备"),1)]),_:1}),e(ce,{prop:"device_type",label:"设备类型",width:"120"},{default:s(({row:w})=>[e(Ce,{size:"small"},{default:s(()=>[u(f(fe(w.device_type)),1)]),_:2},1024)]),_:1}),e(ce,{prop:"status",label:"状态",width:"100"},{default:s(({row:w})=>[e(Ce,{type:ge(w.status),size:"small"},{default:s(()=>[u(f(pe(w.status)),1)]),_:2},1032,["type"])]),_:1}),e(ce,{label:"所属服务器",width:"150"},{default:s(({row:w})=>{var pt;return[u(f(w.server_name||((pt=w.slave_server)==null?void 0:pt.name)||w.slave_server_name||"未知服务器"),1)]}),_:1})]),_:1},8,["data"])])])]),_:1},8,["modelValue"])])}}},Qo=at(Yo,[["__scopeId","data-v-01709d2b"]]),Dt="permission_audit_logs",Zo=1e3,Xo={ASSIGN:"assign",REVOKE:"revoke",MODIFY:"modify",BATCH_ASSIGN:"batch_assign",BATCH_REVOKE:"batch_revoke",COPY:"copy",TEMPLATE_APPLY:"template_apply"},ea={DEVICE_GROUP:"device_group",DEVICE:"device",ORGANIZATION:"organization",USER:"user"};class _t{static getLogs(){try{const R=localStorage.getItem(Dt);return R?JSON.parse(R):[]}catch(R){return console.error("读取审计日志失败:",R),[]}}static saveLogs(R){try{const P=R.slice(-Zo);localStorage.setItem(Dt,JSON.stringify(P))}catch(P){console.error("保存审计日志失败:",P)}}static addLog(R){const P=this.getLogs();P.push({...R,id:Date.now()+Math.random(),timestamp:new Date().toISOString(),userAgent:navigator.userAgent,sessionId:this.getSessionId()}),this.saveLogs(P)}static getSessionId(){let R=sessionStorage.getItem("audit_session_id");return R||(R=Date.now()+"_"+Math.random().toString(36).substr(2,9),sessionStorage.setItem("audit_session_id",R)),R}static clearLogs(){localStorage.removeItem(Dt)}}function ta(){const ue=Xe(),R=c([]),P=c(!1),te=c([]),Y=c([]),j=Le({totalOperations:0,todayOperations:0,successRate:0,mostActiveUser:null,mostCommonOperation:null}),B=oe(()=>R.value.slice(-50).reverse()),I=oe(()=>{const D=new Date().toDateString();return R.value.filter(y=>new Date(y.timestamp).toDateString()===D)}),U=oe(()=>ue.hasPermission("audit.view")||ue.canAccessDeviceManagement),x=oe(()=>ue.hasPermission("permission.manage")||ue.canAccessDeviceManagement),F=async D=>{try{const y={operationType:D.type,operator:{id:ue.userInfo.id,username:ue.userInfo.username,role:ue.userInfo.role_name,permissionLevel:ue.userInfo.permission_level},targets:D.targets,permissions:D.permissions,details:D.details||{},result:"pending"};return _t.addLog(y),te.value.push(y),y}catch(y){throw console.error("记录操作失败:",y),m.error(`操作记录失败: ${y.message}`),y}},G=(D,y,i={})=>{const p=te.value.find(C=>C.id===D);if(p){p.result=y,p.completedAt=new Date().toISOString(),p.completionDetails=i;const C=_t.getLogs(),$=C.findIndex(V=>V.id===D);$!==-1&&(C[$]=p,_t.saveLogs(C));const M=te.value.findIndex(V=>V.id===D);M!==-1&&te.value.splice(M,1),Y.value.unshift(p),K()}},L=()=>{try{P.value=!0,R.value=_t.getLogs(),K()}catch(D){console.error("加载审计日志失败:",D),m.error("加载审计日志失败")}finally{P.value=!1}},q=async()=>{try{await Ee.confirm("确定要清除所有审计日志吗？此操作不可恢复。","确认清除",{confirmButtonText:"确定清除",cancelButtonText:"取消",type:"warning"}),_t.clearLogs(),R.value=[],Y.value=[],te.value=[],K(),m.success("审计日志已清除")}catch(D){D!=="cancel"&&(console.error("清除审计日志失败:",D),m.error("清除审计日志失败"))}},K=()=>{const D=R.value;j.totalOperations=D.length,j.todayOperations=I.value.length;const y=D.filter(C=>C.result==="success").length;j.successRate=D.length>0?(y/D.length*100).toFixed(2):0;const i={};D.forEach(C=>{var M;const $=(M=C.operator)==null?void 0:M.username;$&&(i[$]=(i[$]||0)+1)}),j.mostActiveUser=Object.keys(i).reduce((C,$)=>i[C]>i[$]?C:$,null);const p={};D.forEach(C=>{const $=C.operationType;$&&(p[$]=(p[$]||0)+1)}),j.mostCommonOperation=Object.keys(p).reduce((C,$)=>p[C]>p[$]?C:$,null)};return{auditLogs:R,loading:P,pendingOperations:te,operationHistory:Y,statistics:j,recentLogs:B,todayLogs:I,canViewAuditLogs:U,canManagePermissions:x,recordOperation:F,completeOperation:G,loadAuditLogs:L,clearAuditLogs:q,OPERATION_TYPES:Xo,PERMISSION_TYPES:ea}}const sa={class:"permission-assignment"},la={class:"toolbar"},oa={class:"toolbar-left"},aa={class:"toolbar-right"},na={key:0,class:"current-selection"},ia={class:"selection-info"},ra={class:"selection-text"},da={key:0,class:"batch-info"},ua={class:"selection-actions"},ca={class:"main-content"},pa={class:"org-panel"},_a={class:"card-header"},va={class:"header-controls"},ma={key:0,class:"batch-controls"},fa={class:"batch-selection-info"},ga={key:0},ya={class:"node-info"},ha={class:"node-label"},ba={key:0,class:"org-type"},$a={key:1,class:"user-count"},ka={key:2,class:"user-role"},wa={class:"device-panel"},Ca={class:"device-tabs"},Ta={class:"tab-content"},Sa={class:"search-bar"},Va={class:"device-list"},xa=["onClick"],Da={class:"device-info"},za={class:"device-name"},Aa={class:"device-desc"},Ea={class:"device-meta"},La={class:"device-count"},Pa={class:"tab-content"},Ua={class:"device-filters"},Ia={class:"search-bar"},ja={class:"device-list"},Oa=["onClick"],Ma={class:"device-info"},Ba={class:"device-name"},Ga={class:"device-desc"},Na={class:"device-meta"},Ra={class:"device-id"},Fa={class:"assign-confirm"},Ha={__name:"PermissionAssignment",emits:["stats-update"],setup(ue,{expose:R,emit:P}){const te=P,Y=Xe(),j=X,{organizationTreeData:B,selectedNode:I,loadOrganizationsWithUsers:U,getLevelName:x}=bs({autoLoad:!0,enableCache:!0});ta();const F=c(!1),G=c(!1),L=c(!1),q=c(!1),K=c(!1),D=c(!1),y=c("single"),i=c([]),p=c(),C=c(0),$=c("groups"),M=c([]),V=c([]),S=c([]),_e=c(""),he=c(""),Q=Le({name:"",type:"",server:"",group:""}),ee=c([]),Pe=c([]),Z=l=>!l||!Array.isArray(l)?[]:l.filter(t=>t.name==="新注册用户"?(console.log('🔧 PermissionAssignment - 过滤掉"新注册用户"组织:',t.name),!1):!0).map(t=>{const o={...t};return t.children&&t.children.length>0&&(o.children=Z(t.children)),o}),be=oe(()=>{console.log("🔍 PermissionAssignment - organizationTree computed"),console.log("🔍 PermissionAssignment - organizationTreeData.value:",B.value);const l=Z(B.value);return console.log("🔍 PermissionAssignment - 过滤后数据:",l),console.log("🔍 PermissionAssignment - 过滤后数据长度:",l.length),l}),ve=oe(()=>{const l=[],t=(o,n=0)=>{Array.isArray(o)&&o.forEach(A=>{n<=2&&A.type==="organization"&&(l.push(A.id),A.children&&A.children.length>0&&t(A.children,n+1))})};return t(be.value),l}),$e={children:"children",label:"name"},Ue=oe(()=>i.value.reduce((l,t)=>l+(t.userCount||0),0)),Se=oe(()=>y.value==="single"?I.value?{name:I.value.name,type:I.value.type==="organization"?x(I.value.level):"用户",count:1}:null:i.value.length>0?{name:`${i.value.length} 个组织`,type:"批量选择",count:i.value.length}:null),Ve=oe(()=>_e.value?M.value.filter(l=>l.name.toLowerCase().includes(_e.value.toLowerCase())||l.description&&l.description.toLowerCase().includes(_e.value.toLowerCase())):M.value),ae=oe(()=>he.value?V.value.filter(l=>(l.name||l.device_name||"").toLowerCase().includes(he.value.toLowerCase())):V.value),Oe=l=>{switch(l){case"server":return"success";case"mixed":return"warning";case"single":return"info";default:return""}},xe=l=>{switch(l){case"server":return"服务器分组";case"mixed":return"混合分组";case"single":return"单设备分组";default:return"未知类型"}},Re=l=>{switch(l){case"在线":return"success";case"离线":return"danger";case"占用":return"warning";default:return"info"}},Fe=l=>I.value&&I.value.id===l.id&&I.value.type===l.type,De=l=>i.value.some(t=>t.id===l.id&&t.type===l.type),He=l=>{y.value==="single"&&(console.log("🔧 PermissionAssignment - 单选节点点击:",l),I.value=l,S.value=[],m.info(`已选择：${l.name}`))},qe=(l,t)=>{console.log("🔧 PermissionAssignment - 节点复选框变化:",l,t),t.checkedNodes&&(i.value=t.checkedNodes.filter(o=>o.type==="organization"),console.log("🔧 PermissionAssignment - 更新多选列表:",i.value),i.value.length>0&&m.info(`已选择 ${i.value.length} 个组织`))},k=()=>{y.value==="single"?I.value=null:(i.value=[],p.value&&p.value.setCheckedKeys([])),S.value=[],m.info("已清除所有选择")},v=l=>{console.log("🔧 PermissionAssignment - 选择模式切换:",l),I.value=null,i.value=[],S.value=[],C.value++,m.info(`已切换到${l==="single"?"单选":"多选"}模式`)},H=()=>{if(!p.value)return;const l=[],t=n=>{n.forEach(A=>{A.type==="organization"&&l.push(A),A.children&&t(A.children)})};t(be.value);const o=l.map(n=>n.id);p.value.setCheckedKeys(o),i.value=[...l],m.success(`已选择所有 ${l.length} 个组织`)},z=()=>{p.value&&(p.value.setCheckedKeys([]),i.value=[],m.info("已清空所有选择"))},ie=()=>{if(!p.value)return;const l=[],t=le=>{le.forEach(Te=>{Te.type==="organization"&&l.push(Te),Te.children&&t(Te.children)})};t(be.value);const o=i.value.map(le=>le.id),n=l.filter(le=>!o.includes(le.id)),A=n.map(le=>le.id);p.value.setCheckedKeys(A),i.value=[...n],m.info(`反选完成，当前选择 ${n.length} 个组织`)},se=()=>{K.value=!K.value,console.log("🔧 PermissionAssignment - 切换展开状态:",K.value),kt(()=>{p.value?K.value?(console.log("🔧 PermissionAssignment - 展开所有节点"),p.value.expandAll()):(console.log("🔧 PermissionAssignment - 收起所有节点"),p.value.collapseAll()):console.warn("⚠️ PermissionAssignment - orgTreeRef 未找到")})},re=l=>{S.value=[]},_=l=>{const t=S.value.indexOf(l);t>-1?S.value.splice(t,1):S.value.push(l)},d=()=>{},ne=()=>{},W=()=>{let l=V.value;Q.name&&(l=l.filter(t=>(t.name||t.device_name||"").toLowerCase().includes(Q.name.toLowerCase()))),Q.type&&(l=l.filter(t=>Q.type==="other"?!["ca_lock","encryption_key","bank_ukey"].includes(t.device_type):t.device_type===Q.type)),Q.server&&(l=l.filter(t=>t.slave_server_id===Q.server)),ae.value=l},me=()=>{if(!(y.value==="single"?I.value:i.value.length>0)){m.warning(`请先选择${y.value==="single"?"组织或用户":"要分配权限的组织"}`);return}if(S.value.length===0){m.warning("请选择要分配的设备或分组");return}D.value=!0},we=async()=>{try{G.value=!0;const l=y.value==="single"?[I.value]:i.value;console.log("🔧 PermissionAssignment - 开始权限分配:",{模式:y.value,目标数量:l.length,设备数量:S.value.length,目标列表:l.map(o=>o.name)}),await new Promise(o=>setTimeout(o,1e3));const t=y.value==="single"?`成功为 ${l[0].name} 分配 ${S.value.length} 个设备权限`:`成功为 ${l.length} 个组织批量分配 ${S.value.length} 个设备权限`;m.success(t),D.value=!1,S.value=[],ze()}catch(l){console.error("权限分配失败:",l),m.error("权限分配失败")}finally{G.value=!1}},Me=()=>{D.value=!1},fe=async()=>{F.value=!0;try{console.log("🔧 PermissionAssignment - 开始刷新数据"),await Promise.all([U(),pe(),ge()]),C.value++,console.log("🔧 PermissionAssignment - 数据刷新完成，强制重新渲染，treeKey:",C.value),m.success("数据刷新成功")}catch(l){console.error("刷新数据失败:",l),m.error("刷新数据失败")}finally{F.value=!1}},pe=async()=>{try{L.value=!0;const l=await Kt();M.value=l.data||[]}catch(l){console.error("加载设备分组失败:",l),m.error("加载设备分组失败")}finally{L.value=!1}},ge=async()=>{try{q.value=!0;const l=await fetch("/api/v1/devices",{headers:{Authorization:`Bearer ${Y.token}`,"Content-Type":"application/json"}});if(!l.ok)throw new Error(`HTTP ${l.status}: ${l.statusText}`);const t=await l.json();console.log("获取到的设备数据:",t);const o=t.devices||t||[];V.value=o.map(n=>{var A;return{id:n.id,name:n.custom_name||n.device_name,status:je(n.status),vid:n.vendor_id,pid:n.product_id,description:n.description||n.device_notes||"无描述",device_type:n.device_type,slave_server_name:((A=n.slave_server)==null?void 0:A.name)||"Unknown Server",physical_port:n.physical_port,is_shared:n.is_shared,is_virtual:n.is_virtual}}),console.log("处理后的设备数据:",V.value)}catch(l){console.error("加载设备列表失败:",l),m.error(`加载设备列表失败: ${l.message}`),V.value=[]}finally{q.value=!1}},T=async()=>{try{const l=await fetch("/api/v1/slave/list",{headers:{Authorization:`Bearer ${Y.token}`,"Content-Type":"application/json"}});if(l.ok){const t=await l.json();ee.value=t.servers||[]}}catch(l){console.error("加载服务器列表失败:",l)}},Ie=async()=>{try{const l=await fetch("/api/v1/device-groups/",{headers:{Authorization:`Bearer ${Y.token}`,"Content-Type":"application/json"}});if(l.ok){const t=await l.json();Pe.value=t||[]}}catch(l){console.error("加载设备分组列表失败:",l)}},je=l=>({online:"在线",offline:"离线",busy:"占用",available:"可用",error:"错误",maintenance:"维护中"})[l]||l||"未知",ze=()=>{var t,o;const l={total:V.value.length,users:((t=I.value)==null?void 0:t.type)==="user"?1:0,orgs:((o=I.value)==null?void 0:o.type)==="organization"?1:0};te("stats-update",l)},Ke=l=>{console.log("设备状态更新:",l);const t=V.value.findIndex(o=>o.id===l.device_id);t!==-1&&(V.value[t].status=je(l.new_status),m.info(`设备状态更新: ${V.value[t].name} -> ${V.value[t].status}`),ze())},ye=()=>{j.subscribe("device_updates"),j.on("device_status_update",Ke);const l=Y.token;l&&j.connect(l)},r=()=>{j.off("device_status_update",Ke),j.unsubscribe("device_updates")};return Ze(()=>{ye()}),wt(()=>{r()}),R({refreshData:fe}),dt(B,(l,t)=>{console.log("🔍 PermissionAssignment - organizationTreeData 数据变化:",{新数据:l,新数据长度:(l==null?void 0:l.length)||0,旧数据长度:(t==null?void 0:t.length)||0,数据类型:typeof l,是否数组:Array.isArray(l)}),l&&l.length>0&&(C.value++,console.log("🔧 PermissionAssignment - 强制重新渲染树组件, treeKey:",C.value),kt(()=>{ve.value&&ve.value.length>0&&console.log("🔧 PermissionAssignment - 设置默认展开节点:",ve.value)}))},{immediate:!0,deep:!0}),dt(be,l=>{console.log("🔍 PermissionAssignment - organizationTree computed 变化:",l)},{immediate:!0}),dt(ve,l=>{console.log("🔍 PermissionAssignment - defaultExpandedKeys 变化:",l)},{immediate:!0}),Ze(()=>{console.log("🔧 PermissionAssignment - 组件挂载完成"),console.log("🔍 PermissionAssignment - 初始 organizationTreeData:",B.value),console.log("🔍 PermissionAssignment - 初始 organizationTree:",be.value),console.log("🔍 PermissionAssignment - 初始 defaultExpandedKeys:",ve.value),pe(),ge(),T(),Ie()}),(l,t)=>{const o=nt,n=it,A=ut,le=vs,Te=_s,Je=Ft,We=Et,de=ft,ce=Rt,Ce=jt,Be=Bt,Ae=yt,et=gt,Ge=Mt,ke=Ot,tt=mt,rt=ct;return g(),N("div",sa,[a("div",la,[a("div",oa,[e(n,{type:"primary",onClick:fe,loading:F.value},{default:s(()=>[e(o,null,{default:s(()=>[e(h(ot))]),_:1}),t[9]||(t[9]=u(" 刷新数据 ",-1))]),_:1,__:[9]},8,["loading"])]),a("div",aa,[e(n,{type:"success",onClick:me,disabled:(y.value==="single"?!h(I):i.value.length===0)||S.value.length===0,loading:G.value},{default:s(()=>[e(o,null,{default:s(()=>[e(h(cs))]),_:1}),t[10]||(t[10]=u(" 分配权限 ",-1))]),_:1,__:[10]},8,["disabled","loading"])])]),Se.value?(g(),N("div",na,[a("div",ia,[e(o,{class:"selection-icon"},{default:s(()=>[e(h($t))]),_:1}),a("span",ra,[t[11]||(t[11]=u(" 当前选中： ",-1)),a("strong",null,f(Se.value.name),1),e(A,{type:"info",size:"small"},{default:s(()=>[u(f(Se.value.type),1)]),_:1}),y.value==="multiple"&&i.value.length>0?(g(),N("span",da," (共"+f(Ue.value)+"人) ",1)):J("",!0)])]),a("div",ua,[e(n,{size:"small",onClick:k},{default:s(()=>t[12]||(t[12]=[u("清除选择",-1)])),_:1,__:[12]})])])):J("",!0),a("div",ca,[a("div",pa,[e(We,null,{header:s(()=>[a("div",_a,[t[15]||(t[15]=a("span",null,"组织架构",-1)),a("div",va,[e(Te,{modelValue:y.value,"onUpdate:modelValue":t[0]||(t[0]=E=>y.value=E),size:"small",onChange:v},{default:s(()=>[e(le,{value:"single"},{default:s(()=>t[13]||(t[13]=[u("单选",-1)])),_:1,__:[13]}),e(le,{value:"multiple"},{default:s(()=>t[14]||(t[14]=[u("多选",-1)])),_:1,__:[14]})]),_:1},8,["modelValue"]),e(n,{size:"small",onClick:se},{default:s(()=>[u(f(K.value?"收起全部":"展开全部"),1)]),_:1})])])]),default:s(()=>[y.value==="multiple"?(g(),N("div",ma,[e(n,{size:"small",onClick:H},{default:s(()=>t[16]||(t[16]=[u("全选",-1)])),_:1,__:[16]}),e(n,{size:"small",onClick:z},{default:s(()=>t[17]||(t[17]=[u("全不选",-1)])),_:1,__:[17]}),e(n,{size:"small",onClick:ie},{default:s(()=>t[18]||(t[18]=[u("反选",-1)])),_:1,__:[18]}),a("span",fa,[u(" 已选择 "+f(i.value.length)+" 个组织 ",1),Ue.value>0?(g(),N("span",ga,"，共 "+f(Ue.value)+" 人",1)):J("",!0)])])):J("",!0),(g(),O(Je,{ref_key:"orgTreeRef",ref:p,data:be.value,props:$e,"node-key":"id","default-expanded-keys":ve.value,"expand-on-click-node":!1,onNodeClick:He,"highlight-current":y.value==="single","show-checkbox":y.value==="multiple","check-strictly":!0,onCheck:qe,key:C.value,class:"org-tree","empty-text":"暂无组织架构数据"},{default:s(({node:E,data:b})=>[a("div",{class:bt(["tree-node",{"is-selected":Fe(b),"is-multi-selected":De(b)}])},[a("div",ya,[e(o,{class:"node-icon"},{default:s(()=>[b.type==="organization"?(g(),O(h(ps),{key:0})):(g(),O(h($t),{key:1}))]),_:2},1024),a("span",ha,[u(f(b.name)+" ",1),b.type==="organization"?(g(),N("span",ba," ("+f(h(x)(b.level))+") ",1)):J("",!0),b.type==="organization"?(g(),N("span",$a," - "+f(b.userCount||0)+"人 ",1)):(g(),N("span",ka," - "+f(b.role_name),1))])])],2)]),_:1},8,["data","default-expanded-keys","highlight-current","show-checkbox"]))]),_:1})]),a("div",wa,[e(We,null,{header:s(()=>t[19]||(t[19]=[a("div",{class:"card-header"},[a("span",null,"设备权限分配")],-1)])),default:s(()=>[a("div",Ca,[e(ke,{modelValue:$.value,"onUpdate:modelValue":t[6]||(t[6]=E=>$.value=E),onTabChange:re},{default:s(()=>[e(Ce,{label:"分组",name:"groups"},{default:s(()=>[a("div",Ta,[a("div",Sa,[e(de,{modelValue:_e.value,"onUpdate:modelValue":t[1]||(t[1]=E=>_e.value=E),placeholder:"搜索设备分组...","prefix-icon":h(st),clearable:"",onInput:d},null,8,["modelValue","prefix-icon"])]),Qe((g(),N("div",Va,[(g(!0),N(Ne,null,lt(Ve.value,E=>(g(),N("div",{key:E.id,class:bt(["device-item",{"is-selected":S.value.includes(E.id)}]),onClick:b=>_(E.id)},[e(ce,{"model-value":S.value.includes(E.id),onChange:b=>_(E.id)},null,8,["model-value","onChange"]),a("div",Da,[a("div",za,f(E.name),1),a("div",Aa,f(E.description||"无描述"),1),a("div",Ea,[e(A,{type:Oe(E.group_type),size:"small"},{default:s(()=>[u(f(xe(E.group_type)),1)]),_:2},1032,["type"]),a("span",La,f(E.device_count||0)+"个设备",1)])])],10,xa))),128))])),[[rt,L.value]])])]),_:1}),e(Ce,{label:"设备",name:"devices"},{default:s(()=>[a("div",Pa,[a("div",Ua,[e(Ge,{gutter:12},{default:s(()=>[e(Be,{span:8},{default:s(()=>[e(de,{modelValue:Q.name,"onUpdate:modelValue":t[2]||(t[2]=E=>Q.name=E),placeholder:"设备名称搜索",clearable:"",onInput:W},{prefix:s(()=>[e(o,null,{default:s(()=>[e(h(st))]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(Be,{span:5},{default:s(()=>[e(et,{modelValue:Q.type,"onUpdate:modelValue":t[3]||(t[3]=E=>Q.type=E),placeholder:"设备类型",clearable:"",onChange:W},{default:s(()=>[e(Ae,{label:"全部类型",value:""}),e(Ae,{label:"CA锁",value:"ca_lock"}),e(Ae,{label:"加密锁",value:"encryption_key"}),e(Ae,{label:"银行U盾",value:"bank_ukey"}),e(Ae,{label:"其他设备",value:"other"})]),_:1},8,["modelValue"])]),_:1}),e(Be,{span:6},{default:s(()=>[e(et,{modelValue:Q.server,"onUpdate:modelValue":t[4]||(t[4]=E=>Q.server=E),placeholder:"所属服务器",clearable:"",onChange:W},{default:s(()=>[e(Ae,{label:"全部服务器",value:""}),(g(!0),N(Ne,null,lt(ee.value,E=>(g(),O(Ae,{key:E.id,label:E.name,value:E.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(Be,{span:5},{default:s(()=>[e(n,{type:"primary",onClick:W},{default:s(()=>[e(o,null,{default:s(()=>[e(h(st))]),_:1}),t[20]||(t[20]=u(" 搜索 ",-1))]),_:1,__:[20]})]),_:1})]),_:1})]),a("div",Ia,[e(de,{modelValue:he.value,"onUpdate:modelValue":t[5]||(t[5]=E=>he.value=E),placeholder:"搜索USB设备...","prefix-icon":h(st),clearable:"",onInput:ne},null,8,["modelValue","prefix-icon"])]),Qe((g(),N("div",ja,[(g(!0),N(Ne,null,lt(ae.value,E=>(g(),N("div",{key:E.id,class:bt(["device-item",{"is-selected":S.value.includes(E.id)}]),onClick:b=>_(E.id)},[e(ce,{"model-value":S.value.includes(E.id),onChange:b=>_(E.id)},null,8,["model-value","onChange"]),a("div",Ma,[a("div",Ba,f(E.name||E.device_name),1),a("div",Ga,f(E.description||"无描述"),1),a("div",Na,[e(A,{type:Re(E.status),size:"small"},{default:s(()=>[u(f(E.status),1)]),_:2},1032,["type"]),a("span",Ra,"VID:"+f(E.vid)+" PID:"+f(E.pid),1)])])],10,Oa))),128))])),[[rt,q.value]])])]),_:1})]),_:1},8,["modelValue"])])]),_:1})])]),e(tt,{modelValue:D.value,"onUpdate:modelValue":t[8]||(t[8]=E=>D.value=E),title:"确认权限分配",width:"500px",onClose:Me},{footer:s(()=>[e(n,{onClick:t[7]||(t[7]=E=>D.value=!1)},{default:s(()=>t[25]||(t[25]=[u("取消",-1)])),_:1,__:[25]}),e(n,{type:"primary",onClick:we,loading:G.value},{default:s(()=>t[26]||(t[26]=[u(" 确认分配 ",-1)])),_:1,__:[26]},8,["loading"])]),default:s(()=>{var E;return[a("div",Fa,[a("p",null,[t[21]||(t[21]=a("strong",null,"分配对象：",-1)),u(f((E=h(I))==null?void 0:E.name),1)]),a("p",null,[t[22]||(t[22]=a("strong",null,"分配类型：",-1)),u(f($.value==="groups"?"设备分组":"独立设备"),1)]),a("p",null,[t[23]||(t[23]=a("strong",null,"选中数量：",-1)),u(f(S.value.length)+"个",1)]),t[24]||(t[24]=a("p",null,[a("strong",null,"权限类型："),u("使用权限")],-1))])]}),_:1},8,["modelValue"])])}}},qa=at(Ha,[["__scopeId","data-v-a7ecdd2f"]]),Ka={class:"device-center"},Ja={class:"statistics-panel"},Wa={class:"stat-content"},Ya={class:"stat-icon server-icon"},Qa={class:"stat-info"},Za={class:"stat-value"},Xa={class:"stat-detail"},en={class:"stat-content"},tn={class:"stat-icon device-icon"},sn={class:"stat-info"},ln={class:"stat-value"},on={class:"stat-detail"},an={class:"stat-content"},nn={class:"stat-icon group-icon"},rn={class:"stat-info"},dn={class:"stat-value"},un={class:"stat-detail"},cn={class:"stat-content"},pn={class:"stat-icon permission-icon"},_n={class:"stat-info"},vn={class:"stat-value"},mn={class:"stat-detail"},fn={class:"toolbar"},gn={class:"toolbar-right"},yn={class:"tab-label"},hn={key:1,class:"no-permission"},bn={__name:"index",setup(ue){const{loading:R,statistics:P,visibleTabs:te,refreshAllData:Y}=$s(),j=Xe(),B=c("");return Ze(()=>{console.log("DeviceCenter 组件已挂载"),te.value.length>0&&(B.value=te.value[0].name)}),(I,U)=>{const x=nt,F=Et,G=Bt,L=Mt,q=it,K=jt,D=Ot,y=gs;return g(),N("div",Ka,[a("div",Ja,[e(L,{gutter:20},{default:s(()=>[h(j).canViewSlaveServerStats?(g(),O(G,{key:0,span:6},{default:s(()=>[e(F,{class:"stat-card"},{default:s(()=>[a("div",Wa,[a("div",Ya,[e(x,null,{default:s(()=>[e(h(vt))]),_:1})]),a("div",Qa,[U[2]||(U[2]=a("div",{class:"stat-title"},"分布式节点",-1)),a("div",Za,f(h(P).slaveServers.total),1),a("div",Xa," 在线: "+f(h(P).slaveServers.online)+" | 离线: "+f(h(P).slaveServers.offline),1)])])]),_:1})]),_:1})):J("",!0),h(j).canViewUSBDeviceStats?(g(),O(G,{key:1,span:6},{default:s(()=>[e(F,{class:"stat-card"},{default:s(()=>[a("div",en,[a("div",tn,[e(x,null,{default:s(()=>[e(h(vt))]),_:1})]),a("div",sn,[U[3]||(U[3]=a("div",{class:"stat-title"},"USB设备管理中心",-1)),a("div",ln,f(h(P).devices.total),1),a("div",on," 可用: "+f(h(P).devices.available)+" | 已连接: "+f(h(P).devices.connected),1)])])]),_:1})]),_:1})):J("",!0),h(j).canViewDeviceGroupStats?(g(),O(G,{key:2,span:6},{default:s(()=>[e(F,{class:"stat-card"},{default:s(()=>[a("div",an,[a("div",nn,[e(x,null,{default:s(()=>[e(h(At))]),_:1})]),a("div",rn,[U[4]||(U[4]=a("div",{class:"stat-title"},"资源调度分组",-1)),a("div",dn,f(h(P).groups.total),1),a("div",un," 有设备: "+f(h(P).groups.withDevices),1)])])]),_:1})]),_:1})):J("",!0),h(j).canViewPermissionAssignmentStats?(g(),O(G,{key:3,span:6},{default:s(()=>[e(F,{class:"stat-card"},{default:s(()=>[a("div",cn,[a("div",pn,[e(x,null,{default:s(()=>[e(h(ms))]),_:1})]),a("div",_n,[U[5]||(U[5]=a("div",{class:"stat-title"},"授权范围管理",-1)),a("div",vn,f(h(P).permissions.totalAssignments),1),a("div",mn," 活跃用户: "+f(h(P).permissions.activeUsers),1)])])]),_:1})]),_:1})):J("",!0)]),_:1})]),a("div",fn,[U[7]||(U[7]=a("div",{class:"toolbar-left"},[a("h2",null,"USB设备管理中心")],-1)),a("div",gn,[e(q,{type:"primary",onClick:h(Y),loading:h(R)},{default:s(()=>[e(x,null,{default:s(()=>[e(h(ot))]),_:1}),U[6]||(U[6]=u(" 刷新数据 ",-1))]),_:1,__:[6]},8,["onClick","loading"])])]),e(F,{class:"main-content"},{default:s(()=>[h(te).length>0?(g(),O(D,{key:0,modelValue:B.value,"onUpdate:modelValue":U[0]||(U[0]=i=>B.value=i),type:"border-card"},{default:s(()=>[(g(!0),N(Ne,null,lt(h(te),i=>(g(),O(K,{key:i.name,name:i.name,label:i.label},{label:s(()=>[a("span",yn,[e(x,null,{default:s(()=>[(g(),O(fs(i.icon)))]),_:2},1024),u(" "+f(i.label),1)])]),default:s(()=>[i.name==="devices"?(g(),O(Bl,{key:0})):J("",!0),i.name==="slaves"?(g(),O(lo,{key:1})):J("",!0),i.name==="groups"?(g(),O(Qo,{key:2})):J("",!0),i.name==="permissions"?(g(),O(qa,{key:3})):J("",!0)]),_:2},1032,["name","label"]))),128))]),_:1},8,["modelValue"])):(g(),N("div",hn,[e(y,{description:"您暂无权限访问设备管理功能"},{default:s(()=>[e(q,{type:"primary",onClick:U[1]||(U[1]=i=>I.$router.push("/dashboard"))},{default:s(()=>U[8]||(U[8]=[u(" 返回工作台 ",-1)])),_:1,__:[8]})]),_:1})]))]),_:1})])}}},Xn=at(bn,[["__scopeId","data-v-9f168561"]]);export{Xn as default};
