import{_ as T}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css               */import{u as A,a as M,r as N,o as j,c,d as s,z as p,t as l,p as _,e as o,w as n,E,a4 as V,a5 as q,h as u,i as d,n as B,a6 as R,a7 as U}from"./index-s7ypaCFZ.js";import{a as k}from"./applicationRequests-C5RXgzhx.js";const z={class:"dashboard-container"},H={class:"page-header"},I={class:"welcome-text"},L={class:"stats-grid"},O={class:"stat-card"},F={class:"stat-icon pending"},G={class:"stat-content"},J={class:"stat-number"},K={class:"stat-card"},Q={class:"stat-icon approved"},W={class:"stat-content"},X={class:"stat-number"},Y={class:"stat-card"},Z={class:"stat-icon users"},ss={class:"stat-content"},ts={class:"stat-number"},es={class:"stat-card"},os={class:"stat-icon devices"},as={class:"stat-content"},ns={class:"stat-number"},is={class:"quick-actions"},cs={class:"action-grid"},ls={key:0,class:"recent-applications"},ds={class:"application-list"},rs=["onClick"],ps={class:"app-info"},_s={class:"app-title"},us={class:"app-meta"},vs={class:"app-applicant"},ms={class:"app-time"},hs={class:"app-status"},fs={key:0,class:"empty-state"},gs={__name:"Dashboard",setup(ks){const r=A(),v=M({pending_applications:0,approved_applications:0,total_users:0,online_devices:0}),m=N([]),y=async()=>{try{const e=await k.getApplicationStats(),t=e.data||e;Object.assign(v,t),console.log("统计数据加载成功:",t)}catch(e){console.error("加载统计数据失败:",e)}},b=async()=>{try{const e=await k.getApplicationRequests({page:1,size:5});m.value=e.data||[]}catch(e){console.error("加载最近申请失败:",e)}},C=e=>U(e).format("MM-DD HH:mm"),w=e=>({pending:"warning",approved:"success",rejected:"danger",cancelled:"info"})[e]||"info",$=e=>({pending:"待处理",approved:"已批准",rejected:"已拒绝",cancelled:"已取消"})[e]||e;return j(()=>{y(),r.hasPermission("application.view")&&b()}),(e,t)=>{const x=u("Clock"),i=E,D=u("Check"),h=u("User"),f=u("Monitor"),P=u("Plus"),g=u("Document"),S=R;return d(),c("div",z,[s("div",H,[t[4]||(t[4]=s("h2",{class:"page-title"},"工作台",-1)),s("p",I,"欢迎使用 OmniLink 全联通系统，"+l(_(r).userName)+"！",1)]),s("div",L,[s("div",O,[s("div",F,[o(i,null,{default:n(()=>[o(x)]),_:1})]),s("div",G,[s("div",J,l(v.pending_applications),1),t[5]||(t[5]=s("div",{class:"stat-label"},"待处理申请",-1))])]),s("div",K,[s("div",Q,[o(i,null,{default:n(()=>[o(D)]),_:1})]),s("div",W,[s("div",X,l(v.approved_applications),1),t[6]||(t[6]=s("div",{class:"stat-label"},"已批准申请",-1))])]),s("div",Y,[s("div",Z,[o(i,null,{default:n(()=>[o(h)]),_:1})]),s("div",ss,[s("div",ts,l(v.total_users),1),t[7]||(t[7]=s("div",{class:"stat-label"},"系统用户",-1))])]),s("div",es,[s("div",os,[o(i,null,{default:n(()=>[o(f)]),_:1})]),s("div",as,[s("div",ns,l(v.online_devices),1),t[8]||(t[8]=s("div",{class:"stat-label"},"在线设备",-1))])])]),s("div",is,[t[13]||(t[13]=s("h3",{class:"section-title"},"快速操作",-1)),s("div",cs,[_(r).hasPermission("application.submit")?(d(),c("div",{key:0,class:"action-card",onClick:t[0]||(t[0]=a=>e.$router.push("/applications/create"))},[o(i,{class:"action-icon"},{default:n(()=>[o(P)]),_:1}),t[9]||(t[9]=s("span",{class:"action-text"},"创建申请",-1))])):p("",!0),_(r).hasPermission("application.view")?(d(),c("div",{key:1,class:"action-card",onClick:t[1]||(t[1]=a=>e.$router.push("/applications"))},[o(i,{class:"action-icon"},{default:n(()=>[o(g)]),_:1}),t[10]||(t[10]=s("span",{class:"action-text"},"处理事项",-1))])):p("",!0),_(r).hasPermission("user.view")?(d(),c("div",{key:2,class:"action-card",onClick:t[2]||(t[2]=a=>e.$router.push("/users"))},[o(i,{class:"action-icon"},{default:n(()=>[o(h)]),_:1}),t[11]||(t[11]=s("span",{class:"action-text"},"用户管理",-1))])):p("",!0),_(r).hasPermission("device.view")?(d(),c("div",{key:3,class:"action-card",onClick:t[3]||(t[3]=a=>e.$router.push("/devices"))},[o(i,{class:"action-icon"},{default:n(()=>[o(f)]),_:1}),t[12]||(t[12]=s("span",{class:"action-text"},"设备管理",-1))])):p("",!0)])]),_(r).hasPermission("application.view")?(d(),c("div",ls,[t[15]||(t[15]=s("h3",{class:"section-title"},"最近申请",-1)),s("div",ds,[(d(!0),c(V,null,q(m.value,a=>(d(),c("div",{key:a.id,class:"application-item",onClick:ys=>e.$router.push(`/applications/${a.id}`)},[s("div",ps,[s("div",_s,l(a.title),1),s("div",us,[s("span",vs,l(a.applicant_name),1),s("span",ms,l(C(a.created_at)),1)])]),s("div",hs,[o(S,{type:w(a.status)},{default:n(()=>[B(l($(a.status)),1)]),_:2},1032,["type"])])],8,rs))),128)),m.value.length===0?(d(),c("div",fs,[o(i,{class:"empty-icon"},{default:n(()=>[o(g)]),_:1}),t[14]||(t[14]=s("p",{class:"empty-text"},"暂无申请记录",-1))])):p("",!0)])])):p("",!0)])}}},xs=T(gs,[["__scopeId","data-v-9898b239"]]);export{xs as default};
