import{_ as oe}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                             *//* empty css                   *//* empty css                     *//* empty css                          *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{dp as $,r as v,a as ne,aM as ie,o as re,c as C,d,e as a,w as l,p as P,cQ as ue,m as de,a8 as _e,a9 as ce,y as r,x as me,s as g,ab as pe,i,n as o,ae as ve,z as y,a6 as fe,t as u,dy as ge,cS as ye,cT as be,f as we,j as ke,k as Ve,ac as xe,ad as Ee,cM as Ce,cN as Re,a4 as j,a5 as A,ak as De,al as ze,a3 as <PERSON>e}from"./index-DygbpG-y.js";function Be(){return $.get("/api/v2/roles/")}function Fe(m){return $.post("/api/v2/roles/",m)}function $e(m,f){return $.put(`/api/v2/roles/${m}`,f)}function Te(m){return $.delete(`/api/v2/roles/${m}`)}function G(){return{"user.view":"查看用户","user.create":"创建用户","user.edit":"编辑用户","user.delete":"删除用户","user.manage_role":"管理用户角色","org.view":"查看组织","org.create":"创建组织","org.edit":"编辑组织","org.delete":"删除组织","device.view":"查看设备","device.manage":"管理设备","device.connect":"连接设备","application.view":"查看申请","application.submit":"提交申请","application.process":"处理申请","profile.view":"查看个人资料","profile.edit":"编辑个人资料","system.config":"系统配置","system.monitor":"系统监控","system.audit":"审计日志"}}function Ue(){return{用户管理:["user.view","user.create","user.edit","user.delete","user.manage_role"],组织管理:["org.view","org.create","org.edit","org.delete"],设备管理:["device.view","device.manage","device.connect"],申请处理:["application.view","application.submit","application.process"],个人资料:["profile.view","profile.edit"],系统管理:["system.config","system.monitor","system.audit"]}}function Ne(m){const f=Object.keys(G()),b=m.filter(p=>!f.includes(p));return{isValid:b.length===0,invalidPermissions:b}}const je={class:"role-management"},Ae={class:"page-header"},Ie={class:"header-actions"},Me={class:"role-list"},Le={class:"role-name"},Oe={class:"permission-tags"},Se={class:"dialog-footer"},qe={key:0,class:"role-detail"},Ge={class:"permission-list"},Qe={__name:"index",setup(m){const f=v(!1),b=v(!1),p=v(!1),T=v(!1),w=v(!1),k=v([]),_=v(null),B=v(),n=ne({name:"",description:"",level_scope:0,permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),I=ie({get(){const t=[];return n.can_manage_users&&t.push("can_manage_users"),n.can_manage_devices&&t.push("can_manage_devices"),n.can_view_reports&&t.push("can_view_reports"),t},set(t){n.can_manage_users=t.includes("can_manage_users"),n.can_manage_devices=t.includes("can_manage_devices"),n.can_view_reports=t.includes("can_view_reports")}}),Q={name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:50,message:"角色名称长度在 2 到 50 个字符",trigger:"blur"}],description:[{required:!0,message:"请输入角色描述",trigger:"blur"}]},h=Ue(),H=G(),U=async()=>{f.value=!0;try{const t=await Be();let e=t;t&&t.success&&t.data&&(console.log("🔧 loadRoles - 检测到API中间件包装格式，提取data字段"),e=t.data),console.log("loadRoles - 处理后的角色数据:",e),e&&e.roles?(k.value=Array.isArray(e.roles)?e.roles:[],console.log("加载角色列表成功:",k.value.length,"个角色"),e.filtered_by_permission&&console.log("权限过滤已生效")):Array.isArray(e)?(k.value=e,console.log("加载角色列表成功（数组格式）:",k.value.length,"个角色")):(k.value=[],console.log("角色数据格式异常，设置为空数组"))}catch(t){g.error("加载角色列表失败"),console.error("Load roles error:",t)}finally{f.value=!1}},J=()=>{w.value=!1,p.value=!0},K=t=>{w.value=!0,Object.assign(n,t),p.value=!0},W=t=>{_.value=t,T.value=!0},X=()=>{var t;Object.assign(n,{name:"",description:"",level_scope:0,permissions:[],can_manage_users:!1,can_manage_devices:!1,can_view_reports:!1}),(t=B.value)==null||t.resetFields()},Y=async()=>{if(B.value)try{await B.value.validate(),b.value=!0;const t={...n},e=Ne(t.permissions);if(!e.isValid){g.error(`无效的权限配置: ${e.invalidPermissions.join(", ")}`);return}w.value?(await $e(n.id,t),g.success("角色更新成功")):(await Fe(t),g.success("角色创建成功")),p.value=!1,await U()}catch(t){g.error(w.value?"角色更新失败":"角色创建失败"),console.error("Save role error:",t)}finally{b.value=!1}},Z=async t=>{try{await Pe.confirm(`确定要删除角色 "${t.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await Te(t.id),g.success("角色删除成功"),await U()}catch(e){e!=="cancel"&&(g.error("角色删除失败"),console.error("Delete role error:",e))}},M=t=>H[t]||t,L=t=>t?new Date(t).toLocaleString("zh-CN"):"-";return re(()=>{U()}),(t,e)=>{const V=de,c=fe,x=ve,ee=pe,O=Ve,R=ke,D=Ee,le=xe,F=Re,S=Ce,ae=we,q=me,E=ze,te=De,se=ce;return i(),C("div",je,[d("div",Ae,[e[9]||(e[9]=d("div",{class:"header-content"},[d("h2",null,"角色管理"),d("p",null,"管理系统角色和权限分配，仅超级管理员可访问")],-1)),d("div",Ie,[a(V,{type:"primary",onClick:J,icon:P(ue)},{default:l(()=>e[8]||(e[8]=[o(" 创建角色 ",-1)])),_:1,__:[8]},8,["icon"])])]),d("div",Me,[_e((i(),r(ee,{data:k.value,style:{width:"100%"}},{default:l(()=>[a(x,{prop:"name",label:"角色名称",width:"150"},{default:l(({row:s})=>[d("div",Le,[s.is_system_role?(i(),r(c,{key:0,type:"danger",size:"small"},{default:l(()=>e[10]||(e[10]=[o("系统",-1)])),_:1,__:[10]})):y("",!0),o(" "+u(s.name),1)])]),_:1}),a(x,{prop:"description",label:"角色描述","min-width":"200"}),a(x,{label:"权限范围",width:"120"},{default:l(({row:s})=>[s.level_scope===0?(i(),r(c,{key:0,type:"warning"},{default:l(()=>e[11]||(e[11]=[o("无限制",-1)])),_:1,__:[11]})):(i(),r(c,{key:1,type:"info"},{default:l(()=>[o(u(s.level_scope)+"级",1)]),_:2},1024))]),_:1}),a(x,{label:"管理权限",width:"200"},{default:l(({row:s})=>[d("div",Oe,[s.can_manage_users?(i(),r(c,{key:0,size:"small",type:"success"},{default:l(()=>e[12]||(e[12]=[o("用户管理",-1)])),_:1,__:[12]})):y("",!0),s.can_manage_devices?(i(),r(c,{key:1,size:"small",type:"primary"},{default:l(()=>e[13]||(e[13]=[o("设备管理",-1)])),_:1,__:[13]})):y("",!0),s.can_view_reports?(i(),r(c,{key:2,size:"small",type:"info"},{default:l(()=>e[14]||(e[14]=[o("报告查看",-1)])),_:1,__:[14]})):y("",!0)])]),_:1}),a(x,{label:"状态",width:"80"},{default:l(({row:s})=>[a(c,{type:s.is_active?"success":"danger"},{default:l(()=>[o(u(s.is_active?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(x,{label:"操作",width:"200",fixed:"right"},{default:l(({row:s})=>[a(V,{size:"small",onClick:z=>W(s),icon:P(ge)},{default:l(()=>e[15]||(e[15]=[o("查看",-1)])),_:2,__:[15]},1032,["onClick","icon"]),s.is_system_role?y("",!0):(i(),r(V,{key:0,size:"small",type:"primary",onClick:z=>K(s),icon:P(ye)},{default:l(()=>e[16]||(e[16]=[o(" 编辑 ",-1)])),_:2,__:[16]},1032,["onClick","icon"])),s.is_system_role?y("",!0):(i(),r(V,{key:1,size:"small",type:"danger",onClick:z=>Z(s),icon:P(be)},{default:l(()=>e[17]||(e[17]=[o(" 删除 ",-1)])),_:2,__:[17]},1032,["onClick","icon"]))]),_:1})]),_:1},8,["data"])),[[se,f.value]])]),a(q,{modelValue:p.value,"onUpdate:modelValue":e[6]||(e[6]=s=>p.value=s),title:w.value?"编辑角色":"创建角色",width:"600px",onClose:X},{footer:l(()=>[d("span",Se,[a(V,{onClick:e[5]||(e[5]=s=>p.value=!1)},{default:l(()=>e[21]||(e[21]=[o("取消",-1)])),_:1,__:[21]}),a(V,{type:"primary",onClick:Y,loading:b.value},{default:l(()=>[o(u(w.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:l(()=>[a(ae,{model:n,rules:Q,ref_key:"roleFormRef",ref:B,"label-width":"120px"},{default:l(()=>[a(R,{label:"角色名称",prop:"name"},{default:l(()=>[a(O,{modelValue:n.name,"onUpdate:modelValue":e[0]||(e[0]=s=>n.name=s),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),a(R,{label:"角色描述",prop:"description"},{default:l(()=>[a(O,{modelValue:n.description,"onUpdate:modelValue":e[1]||(e[1]=s=>n.description=s),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),a(R,{label:"权限层级"},{default:l(()=>[a(le,{modelValue:n.level_scope,"onUpdate:modelValue":e[2]||(e[2]=s=>n.level_scope=s),placeholder:"选择权限层级范围"},{default:l(()=>[a(D,{label:"无限制",value:0}),a(D,{label:"1级权限",value:1}),a(D,{label:"2级权限",value:2}),a(D,{label:"3级权限",value:3}),a(D,{label:"4级权限",value:4})]),_:1},8,["modelValue"])]),_:1}),a(R,{label:"管理权限"},{default:l(()=>[a(S,{modelValue:I.value,"onUpdate:modelValue":e[3]||(e[3]=s=>I.value=s)},{default:l(()=>[a(F,{label:"can_manage_users"},{default:l(()=>e[18]||(e[18]=[o("用户管理",-1)])),_:1,__:[18]}),a(F,{label:"can_manage_devices"},{default:l(()=>e[19]||(e[19]=[o("设备管理",-1)])),_:1,__:[19]}),a(F,{label:"can_view_reports"},{default:l(()=>e[20]||(e[20]=[o("报告查看",-1)])),_:1,__:[20]})]),_:1},8,["modelValue"])]),_:1}),a(R,{label:"功能权限"},{default:l(()=>[a(S,{modelValue:n.permissions,"onUpdate:modelValue":e[4]||(e[4]=s=>n.permissions=s)},{default:l(()=>[(i(!0),C(j,null,A(P(h),(s,z)=>(i(),C("div",{key:z,class:"permission-group"},[d("h4",null,u(z),1),(i(!0),C(j,null,A(s,N=>(i(),r(F,{key:N,label:N},{default:l(()=>[o(u(M(N)),1)]),_:2},1032,["label"]))),128))]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),a(q,{modelValue:T.value,"onUpdate:modelValue":e[7]||(e[7]=s=>T.value=s),title:"角色详情",width:"500px"},{default:l(()=>[_.value?(i(),C("div",qe,[a(te,{column:1,border:""},{default:l(()=>[a(E,{label:"角色名称"},{default:l(()=>[o(u(_.value.name),1)]),_:1}),a(E,{label:"角色描述"},{default:l(()=>[o(u(_.value.description),1)]),_:1}),a(E,{label:"系统角色"},{default:l(()=>[a(c,{type:_.value.is_system_role?"danger":"success"},{default:l(()=>[o(u(_.value.is_system_role?"是":"否"),1)]),_:1},8,["type"])]),_:1}),a(E,{label:"权限层级"},{default:l(()=>[_.value.level_scope===0?(i(),r(c,{key:0,type:"warning"},{default:l(()=>e[22]||(e[22]=[o("无限制",-1)])),_:1,__:[22]})):(i(),r(c,{key:1,type:"info"},{default:l(()=>[o(u(_.value.level_scope)+"级",1)]),_:1}))]),_:1}),a(E,{label:"创建时间"},{default:l(()=>[o(u(L(_.value.created_at)),1)]),_:1}),a(E,{label:"更新时间"},{default:l(()=>[o(u(L(_.value.updated_at)),1)]),_:1})]),_:1}),e[23]||(e[23]=d("h4",{style:{"margin-top":"20px"}},"权限列表",-1)),d("div",Ge,[(i(!0),C(j,null,A(_.value.permissions,s=>(i(),r(c,{key:s,style:{margin:"2px"},size:"small"},{default:l(()=>[o(u(M(s)),1)]),_:2},1024))),128))])])):y("",!0)]),_:1},8,["modelValue"])])}}},il=oe(Qe,[["__scopeId","data-v-8743c0d1"]]);export{il as default};
