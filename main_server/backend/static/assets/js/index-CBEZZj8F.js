import{_ as O}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                  *//* empty css                     *//* empty css                 */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css                         *//* empty css                  */import{u as $,r as k,a as j,c as W,e,w as s,J as H,K as J,x as K,b as Q,h as X,i as d,d as n,E as Y,p as a,L as E,M as Z,N as G,n as f,t as V,O as ee,P as se,y as i,z as _,Q as oe,R as ae,S as le,T as te,U as ne,V as re,W as de,X as ue,Y as ie,Z as _e,_ as h,$ as me,a0 as pe,a1 as fe,f as ce,j as we,k as ge,m as ve,a2 as Pe,s as ye,a3 as ke}from"./index-BMqDoMFQ.js";const xe={class:"layout-container"},be={class:"header-content"},Ee={class:"header-left"},Ve={class:"logo"},he={class:"header-right"},Ce={class:"user-info"},Ne={class:"user-name"},Ue={__name:"index",setup(Be){const x=Q(),l=$(),c=k(!1),w=k(!1),g=k(),r=j({oldPassword:"",newPassword:"",confirmPassword:""}),C={oldPassword:[{required:!0,message:"请输入原密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:(u,o,t)=>{o!==r.newPassword?t(new Error("两次输入的密码不一致")):t()},trigger:"blur"}]},N=u=>{switch(u){case"profile":x.push("/profile");break;case"changePassword":c.value=!0;break;case"logout":B();break}},U=async()=>{try{await g.value.validate(),w.value=!0,await Pe({old_password:r.oldPassword,new_password:r.newPassword}),ye.success("密码修改成功"),c.value=!1,g.value.resetFields(),Object.assign(r,{oldPassword:"",newPassword:"",confirmPassword:""})}catch(u){console.error("修改密码失败:",u)}finally{w.value=!1}},B=async()=>{try{await ke.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await l.userLogout(),x.push("/login")}catch(u){u!=="cancel"&&console.error("登出失败:",u)}};return(u,o)=>{const t=Y,M=G,v=oe,A=se,F=Z,I=H,p=de,R=re,S=ne,D=X("router-view"),L=pe,q=J,P=ge,y=we,T=ce,b=ve,z=K;return d(),W("div",xe,[e(I,{class:"layout-header",height:"60px"},{default:s(()=>[n("div",be,[n("div",Ee,[n("div",Ve,[e(t,{class:"logo-icon"},{default:s(()=>[e(a(E))]),_:1}),o[5]||(o[5]=n("span",{class:"logo-text"},"OmniLink 全联通系统",-1))])]),n("div",he,[e(F,{onCommand:N},{dropdown:s(()=>[e(A,null,{default:s(()=>[e(v,{command:"profile"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(ae))]),_:1}),o[6]||(o[6]=f(" 个人资料 ",-1))]),_:1,__:[6]}),a(l).isNewUser?_("",!0):(d(),i(v,{key:0,command:"changePassword"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(le))]),_:1}),o[7]||(o[7]=f(" 修改密码 ",-1))]),_:1,__:[7]})),e(v,{divided:"",command:"logout"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(te))]),_:1}),o[8]||(o[8]=f(" 退出登录 ",-1))]),_:1,__:[8]})]),_:1})]),default:s(()=>[n("div",Ce,[e(M,{size:32,class:"user-avatar"},{default:s(()=>[f(V(a(l).userName.charAt(0)),1)]),_:1}),n("span",Ne,V(a(l).userName),1),e(t,{class:"dropdown-icon"},{default:s(()=>[e(a(ee))]),_:1})])]),_:1})])])]),_:1}),e(q,{class:"layout-main"},{default:s(()=>[a(l).isNewUser?_("",!0):(d(),i(S,{key:0,class:"layout-sidebar",width:"200px"},{default:s(()=>[e(R,{"default-active":u.$route.path,class:"sidebar-menu",router:"","unique-opened":""},{default:s(()=>[a(l).canAccessWorkspace?(d(),i(p,{key:0,index:"/dashboard"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(ue))]),_:1}),o[9]||(o[9]=n("span",null,"工作台",-1))]),_:1,__:[9]})):_("",!0),a(l).hasPermission("application.view")?(d(),i(p,{key:1,index:"/applications"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(ie))]),_:1}),o[10]||(o[10]=n("span",null,"处理事项",-1))]),_:1,__:[10]})):_("",!0),a(l).hasPermission("user.view")||a(l).hasPermission("org.view")?(d(),i(p,{key:2,index:"/org-users"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(_e))]),_:1}),o[11]||(o[11]=n("span",null,"组织与用户管理",-1))]),_:1,__:[11]})):_("",!0),a(l).hasPermission("user.approve")?(d(),i(p,{key:3,index:"/user-registration"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(h))]),_:1}),o[12]||(o[12]=n("span",null,"新用户审核",-1))]),_:1,__:[12]})):_("",!0),a(l).hasPermission("device.view")||a(l).hasPermission("slave.view")||a(l).hasPermission("device.group")?(d(),i(p,{key:4,index:"/device-center"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(E))]),_:1}),o[13]||(o[13]=n("span",null,"设备管理中心",-1))]),_:1,__:[13]})):_("",!0),a(l).canAccessRoleManagement?(d(),i(p,{key:5,index:"/role-management"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(h))]),_:1}),o[14]||(o[14]=n("span",null,"角色管理",-1))]),_:1,__:[14]})):_("",!0),a(l).canAccessSystemSettings?(d(),i(p,{key:6,index:"/system-settings"},{default:s(()=>[e(t,null,{default:s(()=>[e(a(me))]),_:1}),o[15]||(o[15]=n("span",null,"系统设置",-1))]),_:1,__:[15]})):_("",!0)]),_:1},8,["default-active"])]),_:1})),e(L,{class:fe(["layout-content",{"new-user-content":a(l).isNewUser}])},{default:s(()=>[e(D)]),_:1},8,["class"])]),_:1}),e(z,{modelValue:c.value,"onUpdate:modelValue":o[4]||(o[4]=m=>c.value=m),title:"修改密码",width:"400px","close-on-click-modal":!1},{footer:s(()=>[e(b,{onClick:o[3]||(o[3]=m=>c.value=!1)},{default:s(()=>o[16]||(o[16]=[f("取消",-1)])),_:1,__:[16]}),e(b,{type:"primary",onClick:U,loading:w.value},{default:s(()=>o[17]||(o[17]=[f(" 确定 ",-1)])),_:1,__:[17]},8,["loading"])]),default:s(()=>[e(T,{ref_key:"passwordFormRef",ref:g,model:r,rules:C,"label-width":"80px"},{default:s(()=>[e(y,{label:"原密码",prop:"oldPassword"},{default:s(()=>[e(P,{modelValue:r.oldPassword,"onUpdate:modelValue":o[0]||(o[0]=m=>r.oldPassword=m),type:"password",placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])]),_:1}),e(y,{label:"新密码",prop:"newPassword"},{default:s(()=>[e(P,{modelValue:r.newPassword,"onUpdate:modelValue":o[1]||(o[1]=m=>r.newPassword=m),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),e(y,{label:"确认密码",prop:"confirmPassword"},{default:s(()=>[e(P,{modelValue:r.confirmPassword,"onUpdate:modelValue":o[2]||(o[2]=m=>r.confirmPassword=m),type:"password",placeholder:"请确认新密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},ze=O(Ue,[["__scopeId","data-v-35b9ad20"]]);export{ze as default};
