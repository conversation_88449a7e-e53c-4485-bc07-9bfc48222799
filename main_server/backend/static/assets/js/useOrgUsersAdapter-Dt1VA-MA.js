import{b as j}from"./users-DQFKP8B4.js";import{u as q}from"./useOrganizationTree-DZ1vCZ3y.js";import{r as n,aM as g,o as B,s as x,by as d}from"./index-z41QasVh.js";function J(L={}){const{autoLoad:T=!0,enableCache:_=!0}=L,{loading:D,searchText:i,expandedKeys:o,defaultExpandedKeys:N,canManageOrganization:b,getLevelName:O,collectExpandKeys:v,filterNodes:m,findNode:C,getNodePath:E}=q({autoLoad:!1,enableCache:_,maxExpandLevel:3}),f=n(!1),s=n([]),h=n([]),y=n([]),M=n([]),u=n(null),U=g(()=>s.value),K=g(()=>i.value?m(s.value,e=>{var r;return e.name.toLowerCase().includes(i.value.toLowerCase())||e.type==="user"&&((r=e.role_name)==null?void 0:r.toLowerCase().includes(i.value.toLowerCase()))}):s.value),A=e=>{const r=[],a=(c,R=null)=>{Array.isArray(c)&&c.forEach(l=>{r.push({...l,parentId:R}),l.children&&l.children.length>0&&a(l.children,l.id)})};let t=e;return e&&e.success&&e.data&&(t=e.data),a(Array.isArray(t)?t:[t]),r},z=e=>{if(!e||!Array.isArray(e))return[];const r=a=>{const t={id:a.id,name:a.name,type:a.type||"organization",level:a.level||0,children:[]};return a.type==="user"&&(t.role_name=a.role_name,t.username=a.username,t.email=a.email,t.permission_level=a.permission_level),a.type==="organization"&&(t.description=a.description,t.user_count=a.user_count||0,t.child_count=a.child_count||0,t.users=a.users||[]),a.children&&Array.isArray(a.children)&&(t.children=a.children.map(c=>r(c))),t};return e.map(a=>r(a))},p=async()=>{f.value=!0;try{const e=await j();console.log("🔍 原始API响应类型:",typeof e,"是否数组:",Array.isArray(e));let r=e;e&&e.success&&e.data&&(console.log("🔍 检测到API中间件包装格式，提取data字段:",e.data),r=e.data),y.value=r,h.value=A(r),w(),x.success("组织架构数据加载成功")}catch(e){console.error("加载组织架构失败:",e),x.error("加载组织架构失败"),s.value=[]}finally{f.value=!1}},w=()=>{try{const e=y.value;if(!e){s.value=[];return}let r=Array.isArray(e)?e:[e];if(r.length===0){s.value=[];return}const a=z(r);d(()=>{s.value=a,s.value.length>0&&!u.value&&(u.value=s.value[0]),o.value=v(s.value,3)})}catch(e){console.error("构建树结构失败:",e),s.value=[]}},P=async()=>{await p()},I=e=>{u.value=e},W=async()=>{await d(),o.value=v(s.value)},k=async()=>{await d(),o.value=[]};return B(()=>{T&&p()}),{loading:g(()=>f.value||D.value),organizationTreeData:s,flatOrganizations:h,organizationsResponse:y,allUsers:M,selectedNode:u,searchText:i,expandedKeys:o,defaultExpandedKeys:N,computedTreeData:U,filteredTreeData:K,canManageOrganization:b,loadOrganizationsWithUsers:p,buildTreeData:w,refreshData:P,selectNode:I,expandAll:W,collapseAll:k,getLevelName:O,collectExpandKeys:v,filterNodes:m,findNode:C,getNodePath:E,flattenOrganizations:A,buildTree:z}}export{J as u};
