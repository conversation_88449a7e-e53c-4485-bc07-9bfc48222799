import{a as k}from"./permission-assignment-DxvPLwl_.js";import{u as R,r as c,aM as d,o as V,bL as j,s as q,by as G}from"./index-CPRp_bj6.js";const C="organization_tree_cache",T="organization_tree_timestamp",Q=5*60*1e3;let h=c([]),z=c(!1),S=c(null),N=c(null),E=new Set;class D{static setCache(t){try{localStorage.setItem(C,JSON.stringify(t)),localStorage.setItem(T,Date.now().toString())}catch(e){console.warn("缓存存储失败:",e)}}static getCache(){try{const t=localStorage.getItem(T);if(!t||Date.now()-parseInt(t)>Q)return null;const e=localStorage.getItem(C);return e?JSON.parse(e):null}catch(t){return console.warn("缓存读取失败:",t),null}}static clearCache(){try{localStorage.removeItem(C),localStorage.removeItem(T)}catch(t){console.warn("缓存清除失败:",t)}}}class I{static async fetchData(t=!1){if(z.value)return h.value;if(!t){const e=D.getCache();if(e)return h.value=e,N.value=new Date,this.notifySubscribers(),e}try{z.value=!0,S.value=null,console.log("🔍 useOrganizationTree - 开始获取组织架构数据");const e=await k();console.log("🔍 useOrganizationTree - API响应:",e);let a=e;e&&e.success&&e.data?(console.log("🔍 useOrganizationTree - 检测到API中间件包装格式，提取data字段"),a=e.data):e&&e.data&&(console.log("🔍 useOrganizationTree - 使用response.data字段"),a=e.data);const r=Array.isArray(a)?a:[];return console.log("🔍 useOrganizationTree - 处理后的数据:",r),console.log("🔍 useOrganizationTree - 数据长度:",r.length),h.value=r,N.value=new Date,D.setCache(r),this.notifySubscribers(),r}catch(e){throw S.value=e,console.error("获取组织架构失败:",e),q.error("获取组织架构失败"),e}finally{z.value=!1}}static notifySubscribers(){E.forEach(t=>{try{t(h.value)}catch(e){console.error("通知订阅者失败:",e)}})}static subscribe(t){return E.add(t),()=>E.delete(t)}}class u{static getLevelName(t){return["集团总部","大区","分公司","部门","小组"][t]||"未知"}static filterNewUserOrganizations(t,e=!0){return!e||!t?t:t.filter(a=>a.name==="新注册用户"?(console.log('🔧 OrganizationUtils - 过滤掉"新注册用户"组织:',a.name),!1):!0).map(a=>{const r={...a};return a.children&&a.children.length>0&&(r.children=this.filterNewUserOrganizations(a.children,e)),r})}static collectExpandKeys(t,e=3){const a=[],r=(o,l=0)=>{l>=e||o.forEach(s=>{s.children&&s.children.length>0&&(a.push(s.id),r(s.children,l+1))})};return r(t),a}static findNodeById(t,e){for(const a of t){if(a.id===e)return a;if(a.children){const r=this.findNodeById(a.children,e);if(r)return r}}return null}static findNodePath(t,e){const a=[],r=(o,l,s=[])=>{for(const i of o){const g=[...s,i];if(i.id===l)return a.push(...g),!0;if(i.children&&r(i.children,l,g))return!0}return!1};return r(t,e),a}static filterNodes(t,e){const a=[];for(const r of t)if(e(r)){const o={...r};r.children&&(o.children=this.filterNodes(r.children,e)),a.push(o)}else if(r.children){const o=this.filterNodes(r.children,e);o.length>0&&a.push({...r,children:o})}return a}}function Z(p={}){const{autoLoad:t=!0,enableCache:e=!0,maxExpandLevel:a=3,enableVirtualScroll:r=!1}=p,o=R(),l=c(!1),s=c(null),i=c([]),g=c(""),m=c(!1),f=d(()=>{let n=h.value;return n=u.filterNewUserOrganizations(n,!0),n=A(n),console.log("🔍 useOrganizationTree - organizationTree computed:",n),console.log("🔍 useOrganizationTree - 数据长度:",n.length),n}),P=d(()=>z.value||l.value),L=d(()=>S.value),x=d(()=>N.value),A=n=>{if(!n||n.length===0)return console.log("🔍 applyPermissionFilter - 数据为空，返回空数组"),[];console.log("🔍 applyPermissionFilter - 输入数据:",n),console.log("🔍 applyPermissionFilter - 用户权限等级:",o.getPermissionLevel),console.log("🔍 applyPermissionFilter - 用户组织ID:",o.organizationId);const v=n.map(y=>({...y,type:y.type||"organization",children:y.children?y.children.map(O=>({...O,type:O.type||(O.users?"organization":"user")})):[]}));return console.log("🔍 applyPermissionFilter - 输出数据:",v),v},M=d(()=>g.value?u.filterNodes(f.value,n=>{var v;return n.name.toLowerCase().includes(g.value.toLowerCase())||n.type==="user"&&((v=n.role_name)==null?void 0:v.toLowerCase().includes(g.value.toLowerCase()))}):f.value),b=d(()=>u.collectExpandKeys(f.value,a)),U=d(()=>o.hasPermission("organization.manage")||o.canAccessDeviceManagement),w=async(n=!1)=>{try{l.value=!0,await I.fetchData(n)}finally{l.value=!1}},_=()=>w(!0),K=()=>{D.clearCache(),h.value=[],N.value=null},F=n=>{s.value=n},B=async()=>{m.value=!m.value,await G(),i.value=m.value?b.value:[]},H=n=>u.findNodeById(f.value,n),J=n=>u.findNodePath(f.value,n),Y=I.subscribe(n=>{i.value.length===0&&(i.value=b.value)});return V(()=>{t&&w()}),j(()=>{Y()}),{organizationTree:f,filteredTree:M,loading:P,error:L,lastUpdate:x,selectedNode:s,expandedKeys:i,defaultExpandedKeys:b,searchText:g,allExpanded:m,canManageOrganization:U,loadData:w,refreshData:_,clearCache:K,selectNode:F,expandAll:B,findNode:H,getNodePath:J,getLevelName:u.getLevelName,collectExpandKeys:u.collectExpandKeys,filterNodes:u.filterNodes}}export{Z as u};
