import{_ as ee}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                   *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css                *//* empty css                             *//* empty css               *//* empty css                  */import{u as te,r as c,a as oe,o as se,c as b,d as i,a8 as ae,e as s,w as t,m as le,a9 as ne,y as x,z as m,x as re,g as ie,s as g,b as ue,ah as pe,h as de,i as d,n as l,E as ce,ak as _e,al as me,t as n,a6 as fe,p as V,f as ve,j as ge,af as ye,ag as be,k as xe,a7 as ke,a3 as we}from"./index-BMqDoMFQ.js";import{a as D}from"./applicationRequests-uG3lWFI6.js";const Te={class:"application-detail-container"},Ee={class:"page-header"},Ve={class:"detail-content"},De={class:"info-section"},Re={class:"info-section"},qe={class:"content-box"},Ce={key:0,class:"info-section"},Ae={key:0,class:"response-section"},Be={class:"content-box"},Ie={class:"action-section"},je={__name:"Detail",setup(Se){const L=ie(),M=ue(),k=te(),w=c(!1),f=c(!1),T=c(!1),a=c(null),v=c(!1),R=c(""),q=c(),p=oe({status:"",response_content:""}),P={status:[{required:!0,message:"请选择处理结果",trigger:"change"}],response_content:[{required:!0,message:"请输入处理意见",trigger:"blur"}]},E=async()=>{try{w.value=!0;const o=await D.getApplicationRequest(L.params.id);a.value=o}catch(o){console.error("加载申请详情失败:",o),g.error("加载申请详情失败"),M.back()}finally{w.value=!1}},C=o=>{p.status=o,p.response_content="",R.value=o==="approved"?"批准申请":"拒绝申请",v.value=!0},$=async()=>{try{await q.value.validate(),f.value=!0,await D.processApplicationRequest(a.value.id,{status:p.status,process_comment:p.response_content}),g.success("处理成功"),v.value=!1,E()}catch(o){console.error("处理申请失败:",o),g.error("处理申请失败")}finally{f.value=!1}},N=async()=>{try{await we.confirm("确定要取消这个申请吗？","确认取消",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),T.value=!0,await D.processApplicationRequest(a.value.id,{status:"cancelled",process_comment:"申请人主动取消"}),g.success("申请已取消"),E()}catch(o){o!=="cancel"&&(console.error("取消申请失败:",o),g.error("取消申请失败"))}finally{T.value=!1}},A=o=>ke(o).format("YYYY-MM-DD HH:mm:ss"),B=o=>({pending:"warning",approved:"success",rejected:"danger",cancelled:"info"})[o]||"info",I=o=>({pending:"待处理",approved:"已批准",rejected:"已拒绝",cancelled:"已取消"})[o]||o,U=o=>({permission_request:"primary",role_change:"success",device_request:"warning",other:"info"})[o]||"info",Y=o=>({permission_request:"权限申请",role_change:"角色变更",device_request:"设备申请",other:"其他"})[o]||o,z=o=>({low:"info",normal:"primary",high:"warning",urgent:"danger"})[o]||"primary",H=o=>({low:"低",normal:"普通",high:"高",urgent:"紧急"})[o]||o;return se(()=>{E()}),(o,e)=>{const G=de("ArrowLeft"),J=ce,_=le,r=me,y=fe,j=_e,K=pe,S=be,O=ye,F=ge,Q=xe,W=ve,X=re,Z=ne;return d(),b("div",Te,[i("div",Ee,[e[8]||(e[8]=i("h2",{class:"page-title"},"申请详情",-1)),s(_,{onClick:e[0]||(e[0]=u=>o.$router.back())},{default:t(()=>[s(J,null,{default:t(()=>[s(G)]),_:1}),e[7]||(e[7]=l(" 返回 ",-1))]),_:1,__:[7]})]),ae((d(),b("div",Ve,[a.value?(d(),x(K,{key:0,class:"detail-card"},{default:t(()=>{var u;return[i("div",De,[e[9]||(e[9]=i("h3",null,"基本信息",-1)),s(j,{column:2,border:""},{default:t(()=>[s(r,{label:"申请ID"},{default:t(()=>[l(n(a.value.id),1)]),_:1}),s(r,{label:"申请标题"},{default:t(()=>[l(n(a.value.title),1)]),_:1}),s(r,{label:"申请类型"},{default:t(()=>[s(y,{type:U(a.value.application_type)},{default:t(()=>[l(n(Y(a.value.application_type)),1)]),_:1},8,["type"])]),_:1}),s(r,{label:"状态"},{default:t(()=>[s(y,{type:B(a.value.status)},{default:t(()=>[l(n(I(a.value.status)),1)]),_:1},8,["type"])]),_:1}),s(r,{label:"优先级"},{default:t(()=>[s(y,{type:z(a.value.priority)},{default:t(()=>[l(n(H(a.value.priority)),1)]),_:1},8,["type"])]),_:1}),s(r,{label:"申请人"},{default:t(()=>[l(n(a.value.applicant_name),1)]),_:1}),s(r,{label:"所属组织"},{default:t(()=>[l(n(a.value.organization_name||"未指定"),1)]),_:1}),s(r,{label:"创建时间"},{default:t(()=>[l(n(A(a.value.created_at)),1)]),_:1})]),_:1})]),i("div",Re,[e[10]||(e[10]=i("h3",null,"申请内容",-1)),i("div",qe,n(a.value.content),1)]),a.value.status!=="pending"?(d(),b("div",Ce,[e[12]||(e[12]=i("h3",null,"处理信息",-1)),s(j,{column:2,border:""},{default:t(()=>[s(r,{label:"处理人"},{default:t(()=>[l(n(a.value.processor_name||"未知"),1)]),_:1}),s(r,{label:"处理时间"},{default:t(()=>[l(n(A(a.value.processed_at)),1)]),_:1}),s(r,{label:"处理结果",span:"2"},{default:t(()=>[s(y,{type:B(a.value.status)},{default:t(()=>[l(n(I(a.value.status)),1)]),_:1},8,["type"])]),_:1})]),_:1}),a.value.response_content?(d(),b("div",Ae,[e[11]||(e[11]=i("h4",null,"处理意见",-1)),i("div",Be,n(a.value.response_content),1)])):m("",!0)])):m("",!0),i("div",Ie,[a.value.status==="pending"&&V(k).hasPermission("application.process")?(d(),x(_,{key:0,type:"success",onClick:e[1]||(e[1]=h=>C("approved")),loading:f.value},{default:t(()=>e[13]||(e[13]=[l(" 批准申请 ",-1)])),_:1,__:[13]},8,["loading"])):m("",!0),a.value.status==="pending"&&V(k).hasPermission("application.process")?(d(),x(_,{key:1,type:"danger",onClick:e[2]||(e[2]=h=>C("rejected")),loading:f.value},{default:t(()=>e[14]||(e[14]=[l(" 拒绝申请 ",-1)])),_:1,__:[14]},8,["loading"])):m("",!0),a.value.applicant_id===((u=V(k).userInfo)==null?void 0:u.id)&&a.value.status==="pending"?(d(),x(_,{key:2,type:"warning",onClick:N,loading:T.value},{default:t(()=>e[15]||(e[15]=[l(" 取消申请 ",-1)])),_:1,__:[15]},8,["loading"])):m("",!0)])]}),_:1})):m("",!0)])),[[Z,w.value]]),s(X,{modelValue:v.value,"onUpdate:modelValue":e[6]||(e[6]=u=>v.value=u),title:R.value,width:"500px","close-on-click-modal":!1},{footer:t(()=>[s(_,{onClick:e[5]||(e[5]=u=>v.value=!1)},{default:t(()=>e[18]||(e[18]=[l("取消",-1)])),_:1,__:[18]}),s(_,{type:"primary",onClick:$,loading:f.value},{default:t(()=>e[19]||(e[19]=[l(" 确定 ",-1)])),_:1,__:[19]},8,["loading"])]),default:t(()=>[s(W,{ref_key:"processFormRef",ref:q,model:p,rules:P,"label-width":"80px"},{default:t(()=>[s(F,{label:"处理结果",prop:"status"},{default:t(()=>[s(O,{modelValue:p.status,"onUpdate:modelValue":e[3]||(e[3]=u=>p.status=u)},{default:t(()=>[s(S,{label:"approved"},{default:t(()=>e[16]||(e[16]=[l("批准",-1)])),_:1,__:[16]}),s(S,{label:"rejected"},{default:t(()=>e[17]||(e[17]=[l("拒绝",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),s(F,{label:"处理意见",prop:"response_content"},{default:t(()=>[s(Q,{modelValue:p.response_content,"onUpdate:modelValue":e[4]||(e[4]=u=>p.response_content=u),type:"textarea",rows:4,placeholder:"请输入处理意见"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Oe=ee(je,[["__scopeId","data-v-cdfe1149"]]);export{Oe as default};
