import{_ as N}from"./_plugin-vue_export-helper-CX8STRoL.js";/* empty css                *//* empty css                     *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                       *//* empty css                 *//* empty css               *//* empty css                  */import{u as O,r as f,a as T,o as G,c,d as u,e as t,w as l,m as P,ah as H,s as m,b as J,h as K,i as n,n as d,E as Q,f as W,j as X,k as Z,ac as ee,ad as te,z as E,af as oe,ag as ae,ai as le,y as h,a4 as re,a5 as se,t as C,aj as ne}from"./index-CZgshpeE.js";import{a as z}from"./applicationRequests-9zdArEAq.js";const ie={class:"create-application-container"},pe={class:"page-header"},de={key:0,class:"receiver-info"},_e={key:1,class:"receiver-info"},ce={key:2,class:"receiver-info"},ue={style:{display:"flex","justify-content":"space-between"}},me={style:{color:"#8492a6","font-size":"13px"}},ge={__name:"Create",setup(fe){const q=J(),D=O(),y=f(),v=f(!1),b=f([]),g=f([]),o=T({title:"",application_type:"",target_processor_type:"auto",target_processor_id:null,content:"",expected_completion_date:"",additional_notes:""}),R={title:[{required:!0,message:"请输入申请标题",trigger:"blur"},{min:5,max:100,message:"标题长度应在5-100个字符之间",trigger:"blur"}],application_type:[{required:!0,message:"请选择申请类型",trigger:"change"}],target_processor_type:[{required:!0,message:"请选择申请接收人类型",trigger:"change"}],target_processor_id:[{validator:(r,e,s)=>{o.target_processor_type==="specific"&&!e?s(new Error("请选择指定的管理员")):s()},trigger:"change"}],content:[{required:!0,message:"请输入申请内容",trigger:"blur"},{min:20,max:2e3,message:"申请内容应在20-2000个字符之间",trigger:"blur"}],expected_completion_date:[{required:!0,message:"请选择期望完成时间",trigger:"change"}]},U=r=>r.getTime()<Date.now()-864e5,Y=r=>{o.target_processor_id=null,r==="specific"?$():r==="organization"&&M()},$=async()=>{try{const r=await z.getAvailableManagers();b.value=Array.isArray(r)?r:r.managers||[]}catch(r){console.error("加载可用管理员失败:",r),m.error("加载可用管理员失败")}},M=async()=>{var r;try{const e=await z.getAvailableManagers(),s=Array.isArray(e)?e:e.managers||[],w=(r=D.user)==null?void 0:r.organization_id;g.value=s.filter(i=>i.organization_id===w&&i.permission_level<=2)}catch(e){console.error("加载组织管理员失败:",e),m.error("加载组织管理员失败")}},B=async()=>{var r,e;try{await y.value.validate(),v.value=!0;const s={title:o.title,type:o.application_type,priority:"normal",content:o.content,expected_completion_date:o.expected_completion_date,additional_notes:o.additional_notes||null,target_processor_id:o.target_processor_type==="specific"?o.target_processor_id:null,receiver_type:o.target_processor_type};await z.createApplicationRequest(s),m.success("申请提交成功，请等待管理员审核"),q.push("/applications")}catch(s){console.error("提交申请失败:",s),(e=(r=s.response)==null?void 0:r.data)!=null&&e.detail?m.error(s.response.data.detail):m.error("提交申请失败，请重试")}finally{v.value=!1}},I=()=>{y.value.resetFields(),Object.assign(o,{title:"",application_type:"",target_processor_type:"auto",target_processor_id:null,content:"",expected_completion_date:"",additional_notes:""}),b.value=[],g.value=[]};return G(()=>{M()}),(r,e)=>{const s=K("ArrowLeft"),w=Q,i=P,V=Z,p=X,_=te,A=ee,x=ae,S=oe,k=le,j=ne,F=W,L=H;return n(),c("div",ie,[u("div",pe,[e[10]||(e[10]=u("h2",{class:"page-title"},"创建申请",-1)),t(i,{onClick:e[0]||(e[0]=a=>r.$router.back())},{default:l(()=>[t(w,null,{default:l(()=>[t(s)]),_:1}),e[9]||(e[9]=d(" 返回 ",-1))]),_:1,__:[9]})]),t(L,{class:"form-card"},{default:l(()=>[t(F,{ref_key:"formRef",ref:y,model:o,rules:R,"label-width":"100px",size:"large"},{default:l(()=>[t(p,{label:"申请标题",prop:"title"},{default:l(()=>[t(V,{modelValue:o.title,"onUpdate:modelValue":e[1]||(e[1]=a=>o.title=a),placeholder:"请输入申请标题",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(p,{label:"申请类型",prop:"application_type"},{default:l(()=>[t(A,{modelValue:o.application_type,"onUpdate:modelValue":e[2]||(e[2]=a=>o.application_type=a),placeholder:"请选择申请类型"},{default:l(()=>[t(_,{label:"权限申请",value:"permission_request"}),t(_,{label:"角色变更",value:"role_change"}),t(_,{label:"设备申请",value:"device_request"}),t(_,{label:"其他申请",value:"other"})]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"申请接收人",prop:"target_processor_type"},{default:l(()=>[t(S,{modelValue:o.target_processor_type,"onUpdate:modelValue":e[3]||(e[3]=a=>o.target_processor_type=a),onChange:Y},{default:l(()=>[t(x,{label:"auto"},{default:l(()=>e[11]||(e[11]=[d("智能分配（推荐）",-1)])),_:1,__:[11]}),t(x,{label:"organization"},{default:l(()=>e[12]||(e[12]=[d("向所在组织管理员申请",-1)])),_:1,__:[12]}),t(x,{label:"specific"},{default:l(()=>e[13]||(e[13]=[d("指定特定管理员",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"]),o.target_processor_type==="auto"?(n(),c("div",de,[t(k,{title:"系统将自动为您查找最合适的管理员处理此申请",type:"info",closable:!1,"show-icon":""})])):E("",!0),o.target_processor_type==="organization"?(n(),c("div",_e,[g.value.length>0?(n(),h(k,{key:0,title:`将发送给您所在组织的 ${g.value.length} 位管理员`,type:"success",closable:!1,"show-icon":""},null,8,["title"])):(n(),h(k,{key:1,title:"您所在组织暂无管理员，系统将自动向上级组织查找",type:"warning",closable:!1,"show-icon":""}))])):E("",!0),o.target_processor_type==="specific"?(n(),c("div",ce,[t(A,{modelValue:o.target_processor_id,"onUpdate:modelValue":e[4]||(e[4]=a=>o.target_processor_id=a),placeholder:"请选择指定的管理员",filterable:"",style:{width:"100%","margin-top":"8px"}},{default:l(()=>[(n(!0),c(re,null,se(b.value,a=>(n(),h(_,{key:a.id,label:`${a.full_name} (${a.role_name})`,value:a.id},{default:l(()=>[u("div",ue,[u("span",null,C(a.full_name),1),u("span",me,C(a.role_name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])])):E("",!0)]),_:1}),t(p,{label:"申请内容",prop:"content"},{default:l(()=>[t(V,{modelValue:o.content,"onUpdate:modelValue":e[5]||(e[5]=a=>o.content=a),type:"textarea",rows:6,placeholder:"请详细描述您的申请内容、理由和期望结果...",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(p,{label:"期望完成时间",prop:"expected_completion_date"},{default:l(()=>[t(j,{modelValue:o.expected_completion_date,"onUpdate:modelValue":e[6]||(e[6]=a=>o.expected_completion_date=a),type:"date",placeholder:"请选择期望完成时间","disabled-date":U,format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),t(p,{label:"附加说明"},{default:l(()=>[t(V,{modelValue:o.additional_notes,"onUpdate:modelValue":e[7]||(e[7]=a=>o.additional_notes=a),type:"textarea",rows:3,placeholder:"其他需要说明的信息（可选）",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(p,null,{default:l(()=>[t(i,{type:"primary",onClick:B,loading:v.value,size:"large"},{default:l(()=>e[14]||(e[14]=[d(" 提交申请 ",-1)])),_:1,__:[14]},8,["loading"]),t(i,{onClick:I,size:"large"},{default:l(()=>e[15]||(e[15]=[d(" 重置 ",-1)])),_:1,__:[15]}),t(i,{onClick:e[8]||(e[8]=a=>r.$router.back()),size:"large"},{default:l(()=>e[16]||(e[16]=[d(" 取消 ",-1)])),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),_:1})])}}},De=N(ge,[["__scopeId","data-v-55d023c1"]]);export{De as default};
