#!/usr/bin/env python3
"""
压缩性能报告生成器
版本: 1.0
创建日期: 2025-01-15
描述: 基于测试结果生成详细的性能对比报告
"""

import json
import time
from pathlib import Path
from typing import Dict, Any, List
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

class CompressionReportGenerator:
    """压缩性能报告生成器"""
    
    def __init__(self, test_results_file: str):
        """
        初始化报告生成器
        
        Args:
            test_results_file: 测试结果JSON文件路径
        """
        self.test_results_file = test_results_file
        self.results = self._load_results()
        
    def _load_results(self) -> Dict[str, Any]:
        """加载测试结果"""
        with open(self.test_results_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def generate_markdown_report(self) -> str:
        """生成Markdown格式的报告"""
        report = []
        
        # 报告标题
        report.append("# OmniLink 数据压缩算法性能测试报告")
        report.append("")
        report.append(f"**测试时间**: {self.results['test_timestamp']}")
        report.append("")
        
        # 系统信息
        system_info = self.results['system_info']
        report.append("## 测试环境")
        report.append("")
        report.append(f"- **CPU核心数**: {system_info['cpu_count']}")
        report.append(f"- **内存总量**: {system_info['memory_total'] / 1024**3:.1f} GB")
        report.append(f"- **Python版本**: {system_info['python_version']}")
        report.append("")
        
        # 测试总结
        summary = self.results['summary']
        report.append("## 测试总结")
        report.append("")
        report.append("### 🏆 最佳性能指标")
        report.append("")
        report.append(f"- **最佳压缩率**: {summary['best_compression_rate']['algorithm']} ({summary['best_compression_rate']['rate']:.1f}%)")
        report.append(f"- **最快压缩**: {summary['fastest_compression']['algorithm']} ({summary['fastest_compression']['speed']:.1f} MB/s)")
        report.append(f"- **最快解压**: {summary['fastest_decompression']['algorithm']} ({summary['fastest_decompression']['speed']:.1f} MB/s)")
        report.append(f"- **最低内存**: {summary['lowest_memory_usage']['algorithm']} ({summary['lowest_memory_usage']['usage']/1024:.1f} KB)")
        report.append("")
        
        # 使用建议
        recommendations = summary['recommendations']
        report.append("### 🎯 使用建议")
        report.append("")
        report.append(f"- **实时流传输**: {recommendations['real_time_streaming']}")
        report.append(f"- **高压缩率场景**: {recommendations['high_compression']}")
        report.append(f"- **平衡性能**: {recommendations['balanced_performance']}")
        report.append(f"- **低内存使用**: {recommendations['low_memory_usage']}")
        report.append("")
        
        # 详细测试结果
        report.append("## 详细测试结果")
        report.append("")
        
        algorithms = self.results['algorithms']
        data_types = ['text', 'json', 'binary', 'mixed']
        
        for data_type in data_types:
            report.append(f"### {data_type.upper()} 数据类型")
            report.append("")
            
            # 创建表格
            report.append("| 算法 | 数据大小 | 压缩率(%) | 压缩速度(MB/s) | 解压速度(MB/s) | 内存使用(KB) |")
            report.append("|------|----------|-----------|----------------|----------------|--------------|")
            
            for algo_name, algo_data in algorithms.items():
                if data_type in algo_data:
                    for size_key, size_results in algo_data[data_type].items():
                        size = size_key.replace('_bytes', '')
                        for result in size_results:
                            level_str = f" (L{result['level']})" if result['level'] is not None else ""
                            report.append(
                                f"| {algo_name.upper()}{level_str} | {size} | "
                                f"{result['compression_rate']:.1f} | "
                                f"{result['compress_speed']:.1f} | "
                                f"{result['decompress_speed']:.1f} | "
                                f"{result['memory_usage']/1024:.1f} |"
                            )
            
            report.append("")
        
        # 性能分析
        report.append("## 性能分析")
        report.append("")
        
        # Snappy分析
        report.append("### Snappy")
        report.append("- **优势**: 压缩和解压速度极快，CPU占用低")
        report.append("- **劣势**: 压缩率相对较低")
        report.append("- **适用场景**: 实时数据传输，对延迟要求极高的场景")
        report.append("")
        
        # LZ4分析
        report.append("### LZ4")
        report.append("- **优势**: 解压速度最快，内存占用低")
        report.append("- **劣势**: 压缩率不如Zstandard")
        report.append("- **适用场景**: 需要快速解压的场景，CPU资源受限的环境")
        report.append("")
        
        # Zstandard分析
        report.append("### Zstandard")
        report.append("- **优势**: 压缩率最高，可调节压缩级别，平衡性能最佳")
        report.append("- **劣势**: 压缩速度相对较慢（高压缩级别时）")
        report.append("- **适用场景**: 需要高压缩率的场景，长期存储，网络带宽受限")
        report.append("")
        
        # 结论
        report.append("## 结论")
        report.append("")
        report.append("基于测试结果，我们建议：")
        report.append("")
        report.append("1. **默认选择**: Snappy - 在速度和压缩率之间提供良好平衡")
        report.append("2. **高性能场景**: LZ4 - 当解压速度是关键因素时")
        report.append("3. **高压缩率场景**: Zstandard (级别3-6) - 当网络带宽或存储空间受限时")
        report.append("4. **自适应策略**: 根据数据类型和网络条件动态选择算法")
        report.append("")
        
        return "\n".join(report)
    
    def save_report(self, output_dir: str = "test_results"):
        """保存报告到文件"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 生成Markdown报告
        markdown_report = self.generate_markdown_report()
        
        # 保存Markdown文件
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        markdown_file = output_path / f"compression_performance_report_{timestamp}.md"
        
        with open(markdown_file, 'w', encoding='utf-8') as f:
            f.write(markdown_report)
        
        print(f"📄 性能报告已保存到: {markdown_file}")
        return markdown_file

def main():
    """主函数"""
    # 查找最新的测试结果文件
    results_dir = Path("test_results")
    if not results_dir.exists():
        print("❌ 未找到测试结果目录，请先运行压缩测试")
        return
    
    # 查找最新的JSON文件
    json_files = list(results_dir.glob("compression_test_results_*.json"))
    if not json_files:
        print("❌ 未找到测试结果文件，请先运行压缩测试")
        return
    
    latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
    print(f"📊 使用测试结果文件: {latest_file}")
    
    # 生成报告
    generator = CompressionReportGenerator(str(latest_file))
    report_file = generator.save_report()
    
    print("✅ 性能报告生成完成")

if __name__ == "__main__":
    main()
