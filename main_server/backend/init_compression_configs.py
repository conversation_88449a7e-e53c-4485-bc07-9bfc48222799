#!/usr/bin/env python3
"""
初始化数据压缩配置
版本: 1.0
创建日期: 2025-01-15
描述: 在系统启动时创建默认的数据压缩配置
"""

import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from models import CompressionConfig

logger = logging.getLogger(__name__)

async def init_compression_configs(db: AsyncSession):
    """初始化数据压缩配置"""
    try:
        # 检查是否已存在配置
        result = await db.execute(select(func.count()).select_from(CompressionConfig))
        count = result.scalar()
        
        if count > 0:
            logger.info("数据压缩配置已存在，跳过初始化")
            return
        
        logger.info("开始初始化数据压缩配置...")
        
        # 默认配置
        default_configs = [
            {
                "name": "Snappy 通用配置",
                "algorithm": "snappy",
                "level": "medium",
                "stream_type": None,
                "is_active": True,
                "is_default": True,
                "max_concurrent_users": 500,
                "bandwidth_limit_mbps": 3.0,
                "auto_fallback": True,
                "config_data": {
                    "description": "Google开发的快速压缩算法，适合实时数据传输",
                    "priority": 1
                }
            },
            {
                "name": "LZ4 高速配置",
                "algorithm": "lz4",
                "level": "low",
                "stream_type": "vh_usb",
                "is_active": False,
                "is_default": False,
                "max_concurrent_users": 500,
                "bandwidth_limit_mbps": 3.0,
                "auto_fallback": True,
                "config_data": {
                    "description": "超高速压缩算法，适合VirtualHere USB数据流",
                    "priority": 2
                }
            },
            {
                "name": "Zstandard 高压缩配置",
                "algorithm": "zstd",
                "level": "high",
                "stream_type": "web_https",
                "is_active": False,
                "is_default": False,
                "max_concurrent_users": 500,
                "bandwidth_limit_mbps": 3.0,
                "auto_fallback": True,
                "config_data": {
                    "description": "Facebook开发的高效压缩算法，适合Web数据",
                    "priority": 3
                }
            }
        ]
        
        # 添加配置
        for config_data in default_configs:
            config = CompressionConfig(**config_data)
            db.add(config)
        
        await db.commit()
        logger.info("数据压缩配置初始化完成")
        
    except Exception as e:
        logger.error(f"初始化数据压缩配置失败: {e}")
        await db.rollback()
        raise
