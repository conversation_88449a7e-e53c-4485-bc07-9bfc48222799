#!/usr/bin/env python3
"""
组织架构变更处理器
版本: 1.0
创建日期: 2025-01-08
描述: 处理组织架构变更对权限系统的影响，确保数据一致性和权限安全
"""

import asyncio
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_
from database import AsyncSessionLocal
from models import User, Organization
import logging

logger = logging.getLogger(__name__)

class OrganizationChangeHandler:
    """组织架构变更处理器"""
    
    def __init__(self):
        self.change_log = []
    
    async def handle_organization_name_change(
        self, 
        db: AsyncSession, 
        org_id: int, 
        old_name: str, 
        new_name: str,
        operator_user_id: int
    ) -> Dict:
        """
        处理组织名称变更
        
        Args:
            db: 数据库会话
            org_id: 组织ID
            old_name: 原名称
            new_name: 新名称
            operator_user_id: 操作者用户ID
            
        Returns:
            变更结果报告
        """
        logger.info(f"🔄 开始处理组织名称变更: {old_name} -> {new_name}")
        
        try:
            # 1. 验证组织存在性
            org = await db.get(Organization, org_id)
            if not org:
                raise ValueError(f"组织ID {org_id} 不存在")
            
            if org.name != old_name:
                raise ValueError(f"组织当前名称 '{org.name}' 与预期的原名称 '{old_name}' 不匹配")
            
            # 2. 检查新名称是否已存在
            existing_org = await db.execute(
                select(Organization).where(
                    and_(Organization.name == new_name, Organization.id != org_id)
                )
            )
            if existing_org.scalar_one_or_none():
                raise ValueError(f"组织名称 '{new_name}' 已存在")
            
            # 3. 更新组织名称
            org.name = new_name
            org.updated_at = datetime.utcnow()
            
            # 4. 更新组织路径（path字段）
            await self._update_organization_paths(db, org)
            
            # 5. 记录变更日志
            change_record = {
                "change_type": "organization_name_change",
                "organization_id": org_id,
                "old_name": old_name,
                "new_name": new_name,
                "operator_user_id": operator_user_id,
                "timestamp": datetime.utcnow(),
                "affected_users": await self._get_affected_users(db, org_id)
            }
            self.change_log.append(change_record)
            
            # 6. 提交更改
            await db.commit()
            
            logger.info(f"✅ 组织名称变更完成: {old_name} -> {new_name}")
            
            return {
                "success": True,
                "message": f"组织名称已成功从 '{old_name}' 更改为 '{new_name}'",
                "affected_users_count": len(change_record["affected_users"]),
                "change_id": len(self.change_log) - 1
            }
            
        except Exception as e:
            await db.rollback()
            logger.error(f"❌ 组织名称变更失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "rollback_completed": True
            }
    
    async def _update_organization_paths(self, db: AsyncSession, org: Organization):
        """更新组织路径和所有子组织路径"""
        # 计算新的路径
        if org.parent_id:
            parent_org = await db.get(Organization, org.parent_id)
            new_path = f"{parent_org.path}/{org.name}"
        else:
            new_path = f"/{org.name}"
        
        old_path = org.path
        org.path = new_path
        
        # 递归更新所有子组织的路径
        await self._update_child_paths(db, org.id, old_path, new_path)
    
    async def _update_child_paths(self, db: AsyncSession, parent_id: int, old_parent_path: str, new_parent_path: str):
        """递归更新子组织路径"""
        child_orgs = await db.execute(
            select(Organization).where(Organization.parent_id == parent_id)
        )
        
        for child_org in child_orgs.scalars().all():
            # 更新子组织路径
            old_child_path = child_org.path
            new_child_path = old_child_path.replace(old_parent_path, new_parent_path, 1)
            child_org.path = new_child_path
            child_org.updated_at = datetime.utcnow()
            
            # 递归更新孙组织
            await self._update_child_paths(db, child_org.id, old_child_path, new_child_path)
    
    async def _get_affected_users(self, db: AsyncSession, org_id: int) -> List[Dict]:
        """获取受影响的用户列表"""
        # 获取直接属于该组织的用户
        direct_users = await db.execute(
            select(User).where(User.organization_id == org_id)
        )
        
        # 获取管理该组织的用户（上级管理员）
        from permissions_utils import get_child_organizations
        
        # 查找所有可能管理该组织的上级组织
        parent_org_ids = []
        current_org = await db.get(Organization, org_id)
        
        while current_org and current_org.parent_id:
            parent_org_ids.append(current_org.parent_id)
            current_org = await db.get(Organization, current_org.parent_id)
        
        # 获取上级组织的管理员
        manager_users = []
        if parent_org_ids:
            manager_result = await db.execute(
                select(User).where(
                    and_(
                        User.organization_id.in_(parent_org_ids),
                        User.role_name == "管理员"
                    )
                )
            )
            manager_users = manager_result.scalars().all()
        
        # 合并所有受影响用户
        affected_users = []
        for user in list(direct_users.scalars().all()) + manager_users:
            affected_users.append({
                "user_id": user.id,
                "username": user.username,
                "role_name": user.role_name,
                "organization_id": user.organization_id,
                "impact_type": "direct" if user.organization_id == org_id else "management_scope"
            })
        
        return affected_users

class OrganizationStructureManager:
    """组织结构管理器"""
    
    async def add_intermediate_level(
        self,
        db: AsyncSession,
        parent_org_id: int,
        new_level_name: str,
        affected_child_org_ids: List[int],
        operator_user_id: int
    ) -> Dict:
        """
        在现有层级间插入新的组织层级
        
        Args:
            db: 数据库会话
            parent_org_id: 父组织ID
            new_level_name: 新层级名称
            affected_child_org_ids: 需要调整到新层级下的子组织ID列表
            operator_user_id: 操作者用户ID
            
        Returns:
            调整结果报告
        """
        logger.info(f"🏗️ 开始插入新组织层级: {new_level_name}")
        
        try:
            # 1. 验证父组织
            parent_org = await db.get(Organization, parent_org_id)
            if not parent_org:
                raise ValueError(f"父组织ID {parent_org_id} 不存在")
            
            # 2. 创建新的中间层级组织
            new_org = Organization(
                name=new_level_name,
                parent_id=parent_org_id,
                level=parent_org.level + 1,
                path=f"{parent_org.path}/{new_level_name}",
                description=f"新增的{new_level_name}层级",
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.add(new_org)
            await db.flush()  # 获取新组织的ID
            
            # 3. 调整受影响的子组织
            affected_users = []
            for child_org_id in affected_child_org_ids:
                child_org = await db.get(Organization, child_org_id)
                if child_org:
                    # 更新子组织的父级关系
                    old_parent_id = child_org.parent_id
                    child_org.parent_id = new_org.id
                    child_org.level = new_org.level + 1
                    child_org.path = f"{new_org.path}/{child_org.name}"
                    child_org.updated_at = datetime.utcnow()
                    
                    # 获取受影响的用户
                    child_users = await db.execute(
                        select(User).where(User.organization_id == child_org_id)
                    )
                    for user in child_users.scalars().all():
                        affected_users.append({
                            "user_id": user.id,
                            "username": user.username,
                            "old_management_scope": await self._calculate_old_scope(db, user, old_parent_id),
                            "new_management_scope": await self._calculate_new_scope(db, user, new_org.id)
                        })
            
            # 4. 重新计算所有受影响用户的权限范围
            await self._recalculate_user_permissions(db, affected_users)
            
            # 5. 提交更改
            await db.commit()
            
            logger.info(f"✅ 新组织层级插入完成: {new_level_name}")
            
            return {
                "success": True,
                "new_organization_id": new_org.id,
                "affected_organizations": len(affected_child_org_ids),
                "affected_users": len(affected_users),
                "message": f"成功插入新层级 '{new_level_name}'"
            }
            
        except Exception as e:
            await db.rollback()
            logger.error(f"❌ 插入新组织层级失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "rollback_completed": True
            }
    
    async def _calculate_old_scope(self, db: AsyncSession, user: User, old_parent_id: int) -> List[int]:
        """计算用户在旧结构下的管理范围"""
        # 这里实现旧权限范围的计算逻辑
        # 简化实现，实际应该调用权限计算函数
        return []
    
    async def _calculate_new_scope(self, db: AsyncSession, user: User, new_parent_id: int) -> List[int]:
        """计算用户在新结构下的管理范围"""
        # 这里实现新权限范围的计算逻辑
        return []
    
    async def _recalculate_user_permissions(self, db: AsyncSession, affected_users: List[Dict]):
        """重新计算受影响用户的权限"""
        # 这里可以实现权限缓存的更新逻辑
        # 或者触发权限重新计算的事件
        logger.info(f"🔄 重新计算 {len(affected_users)} 个用户的权限范围")

# 使用示例
async def example_usage():
    """使用示例"""
    async with AsyncSessionLocal() as db:
        handler = OrganizationChangeHandler()
        
        # 示例1: 组织名称变更
        result = await handler.handle_organization_name_change(
            db=db,
            org_id=3,
            old_name="华北大区",
            new_name="北方大区",
            operator_user_id=1
        )
        print(f"名称变更结果: {result}")
        
        # 示例2: 插入新组织层级
        structure_manager = OrganizationStructureManager()
        result = await structure_manager.add_intermediate_level(
            db=db,
            parent_org_id=3,
            new_level_name="华北省级办事处",
            affected_child_org_ids=[9, 10],  # 北京分公司、天津分公司
            operator_user_id=1
        )
        print(f"层级调整结果: {result}")

if __name__ == "__main__":
    asyncio.run(example_usage())
