{"name": "omnilink-frontend", "version": "1.0.0", "description": "OmniLink 全联通系统前端应用", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "binary-extensions": "^3.1.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "element-plus": "^2.4.4", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@stagewise/toolbar": "^0.5.0", "@types/js-cookie": "^3.0.6", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^4.5.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.0"}, "engines": {"node": ">=16.0.0"}}