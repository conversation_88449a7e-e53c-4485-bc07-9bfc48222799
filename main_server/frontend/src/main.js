/**
 * OmniLink 前端应用入口
 * 版本: 1.0
 * 创建日期: 2024-12-19
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import './styles/index.css'

// 导入权限指令
import permissionDirectives from './directives/permission'

// 安全防护系统
import securityGuard from '@/utils/security-guard.js'
import secureTokenManager from '@/utils/secure-token.js'

// 启动安全防护（仅在生产环境）
if (import.meta.env.PROD) {
  console.log('启动安全防护系统...')
  // securityGuard 会自动初始化
}

// 全局安全配置
window.__SECURITY_CONFIG__ = {
  enableAntiDebug: import.meta.env.PROD,
  enableIntegrityCheck: true,
  enableNetworkMonitoring: true,
  buildTimestamp: import.meta.env.VITE_BUILD_TIMESTAMP || Date.now(),
  securityLevel: import.meta.env.VITE_SECURITY_MODE || 'normal'
}

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default'
})

// 注册权限指令
Object.keys(permissionDirectives).forEach(key => {
  app.directive(key, permissionDirectives[key])
})

// 挂载应用
app.mount('#app')



// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
}
