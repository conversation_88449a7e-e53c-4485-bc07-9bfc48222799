<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <el-header class="layout-header" height="60px">
      <div class="header-content">
        <div class="header-left">
          <div class="logo">
            <el-icon class="logo-icon"><Monitor /></el-icon>
            <span class="logo-text">OmniLink 全联通系统</span>
          </div>
        </div>
        
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <div class="user-info">
              <el-avatar :size="32" class="user-avatar">
                {{ userStore.userName.charAt(0) }}
              </el-avatar>
              <span class="user-name">{{ userStore.userName }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item
                  command="changePassword"
                  v-if="!userStore.isNewUser"
                >
                  <el-icon><Lock /></el-icon>
                  修改密码
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>
    
    <!-- 主体内容 -->
    <el-container class="layout-main">
      <!-- 侧边栏 - 新用户不显示侧边栏 -->
      <el-aside
        class="layout-sidebar"
        width="200px"
        v-if="!userStore.isNewUser"
      >
        <el-menu
          :default-active="$route.path"
          class="sidebar-menu"
          router
          unique-opened
        >
          <el-menu-item
            index="/dashboard"
            v-if="userStore.canAccessWorkspace"
          >
            <el-icon><House /></el-icon>
            <span>工作台</span>
          </el-menu-item>

          <el-menu-item
            index="/applications"
            v-if="userStore.hasPermission('application.view')"
          >
            <el-icon><Document /></el-icon>
            <span>处理事项</span>
          </el-menu-item>

          <el-menu-item
            index="/org-users"
            v-if="userStore.hasPermission('user.view') || userStore.hasPermission('org.view')"
          >
            <el-icon><OfficeBuilding /></el-icon>
            <span>组织与用户管理</span>
          </el-menu-item>

          <el-menu-item
            index="/user-registration"
            v-if="userStore.hasPermission('user.approve')"
          >
            <el-icon><UserFilled /></el-icon>
            <span>新用户审核</span>
          </el-menu-item>

          <el-menu-item
            index="/device-center"
            v-if="userStore.hasPermission('device.view') || userStore.hasPermission('slave.view') || userStore.hasPermission('device.group')"
          >
            <el-icon><Monitor /></el-icon>
            <span>设备管理中心</span>
          </el-menu-item>

          <el-menu-item
            index="/role-management"
            v-if="userStore.canAccessRoleManagement"
          >
            <el-icon><UserFilled /></el-icon>
            <span>角色管理</span>
          </el-menu-item>

          <el-menu-item
            index="/system-settings"
            v-if="userStore.canAccessSystemSettings"
          >
            <el-icon><Setting /></el-icon>
            <span>系统设置</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 内容区域 -->
      <el-main
        class="layout-content"
        :class="{ 'new-user-content': userStore.isNewUser }"
      >
        <router-view />
      </el-main>
    </el-container>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="changePasswordVisible"
      title="修改密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入原密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="changePasswordVisible = false">取消</el-button>
        <el-button type="primary" @click="handleChangePassword" :loading="passwordLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { changePassword } from '@/api/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  Connection,
  Collection,
  Key,
  Monitor,
  House,
  Document,
  OfficeBuilding,
  UserFilled,
  User,
  Lock,
  SwitchButton,
  ArrowDown
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 修改密码相关
const changePasswordVisible = ref(false)
const passwordLoading = ref(false)
const passwordFormRef = ref()

const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'changePassword':
      changePasswordVisible.value = true
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理修改密码
const handleChangePassword = async () => {
  try {
    await passwordFormRef.value.validate()
    
    passwordLoading.value = true
    
    await changePassword({
      old_password: passwordForm.oldPassword,
      new_password: passwordForm.newPassword
    })
    
    ElMessage.success('密码修改成功')
    changePasswordVisible.value = false
    
    // 重置表单
    passwordFormRef.value.resetFields()
    Object.assign(passwordForm, {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    
  } catch (error) {
    console.error('修改密码失败:', error)
  } finally {
    passwordLoading.value = false
  }
}

// 处理登出
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await userStore.userLogout()
    router.push('/login')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('登出失败:', error)
    }
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-content {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.logo-icon {
  font-size: 24px;
  color: #409eff;
  margin-right: 8px;
}

.logo-text {
  margin-left: 8px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.user-avatar {
  margin-right: 8px;
}

.user-name {
  margin-right: 4px;
  font-size: 14px;
  color: #303133;
}

.dropdown-icon {
  font-size: 12px;
  color: #909399;
}

.layout-main {
  flex: 1;
  overflow: hidden;
}

.layout-sidebar {
  background: #fff;
  border-right: 1px solid #e6e6e6;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.layout-content {
  background: #f5f5f5;
  overflow-y: auto;
}

/* 新用户内容区域样式 */
.new-user-content {
  margin-left: 0 !important;
  padding: 20px;
}
</style>
