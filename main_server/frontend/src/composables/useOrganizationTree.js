/**
 * 统一组织架构管理Composable
 * 提供统一的数据管理、缓存机制、性能优化和状态管理
 * 版本: 1.0
 * 创建日期: 2025-07-26
 */

import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { getOrganizationTree } from '@/api/permission-assignment'
import { useUserStore } from '@/stores/user.js'

// 全局缓存管理
const CACHE_KEY = 'organization_tree_cache'
const CACHE_TIMESTAMP_KEY = 'organization_tree_timestamp'
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

// 全局状态管理
let globalOrganizationData = ref([])
let globalLoading = ref(false)
let globalError = ref(null)
let globalLastUpdate = ref(null)
let subscribers = new Set()

// 缓存管理器
class CacheManager {
  static setCache(data) {
    try {
      localStorage.setItem(CACHE_KEY, JSON.stringify(data))
      localStorage.setItem(CACHE_TIMESTAMP_KEY, Date.now().toString())
    } catch (error) {
      console.warn('缓存存储失败:', error)
    }
  }

  static getCache() {
    try {
      const timestamp = localStorage.getItem(CACHE_TIMESTAMP_KEY)
      if (!timestamp || Date.now() - parseInt(timestamp) > CACHE_DURATION) {
        return null
      }
      const data = localStorage.getItem(CACHE_KEY)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.warn('缓存读取失败:', error)
      return null
    }
  }

  static clearCache() {
    try {
      localStorage.removeItem(CACHE_KEY)
      localStorage.removeItem(CACHE_TIMESTAMP_KEY)
    } catch (error) {
      console.warn('缓存清除失败:', error)
    }
  }
}

// 数据管理器
class OrganizationDataManager {
  static async fetchData(forceRefresh = false) {
    if (globalLoading.value) {
      return globalOrganizationData.value
    }

    // 检查缓存
    if (!forceRefresh) {
      const cachedData = CacheManager.getCache()
      if (cachedData) {
        globalOrganizationData.value = cachedData
        globalLastUpdate.value = new Date()
        this.notifySubscribers()
        return cachedData
      }
    }

    try {
      globalLoading.value = true
      globalError.value = null

      console.log('🔍 useOrganizationTree - 开始获取组织架构数据')
      const response = await getOrganizationTree()
      console.log('🔍 useOrganizationTree - API响应:', response)

      // 🔧 修复：统一数据处理逻辑，与useOrgUsersAdapter保持一致
      let organizationData = response
      if (response && response.success && response.data) {
        console.log('🔍 useOrganizationTree - 检测到API中间件包装格式，提取data字段')
        organizationData = response.data
      } else if (response && response.data) {
        console.log('🔍 useOrganizationTree - 使用response.data字段')
        organizationData = response.data
      }

      // 🔧 修复：确保数据是数组格式
      const data = Array.isArray(organizationData) ? organizationData : []
      console.log('🔍 useOrganizationTree - 处理后的数据:', data)
      console.log('🔍 useOrganizationTree - 数据长度:', data.length)

      globalOrganizationData.value = data
      globalLastUpdate.value = new Date()

      // 更新缓存
      CacheManager.setCache(data)

      // 通知所有订阅者
      this.notifySubscribers()

      return data
    } catch (error) {
      globalError.value = error
      console.error('获取组织架构失败:', error)
      ElMessage.error('获取组织架构失败')
      throw error
    } finally {
      globalLoading.value = false
    }
  }

  static notifySubscribers() {
    subscribers.forEach(callback => {
      try {
        callback(globalOrganizationData.value)
      } catch (error) {
        console.error('通知订阅者失败:', error)
      }
    })
  }

  static subscribe(callback) {
    subscribers.add(callback)
    return () => subscribers.delete(callback)
  }
}

// 工具函数
class OrganizationUtils {
  static getLevelName(level) {
    const levelNames = ['集团总部', '大区', '分公司', '部门', '小组']
    return levelNames[level] || '未知'
  }

  // 🔧 新增：过滤"新注册用户"层级的功能（不修改原始数据）
  static filterNewUserOrganizations(nodes, excludeNewUsers = true) {
    if (!excludeNewUsers || !nodes) return nodes

    return nodes.filter(node => {
      // 过滤掉"新注册用户"组织
      if (node.name === '新注册用户') {
        console.log('🔧 OrganizationUtils - 过滤掉"新注册用户"组织:', node.name)
        return false
      }

      return true
    }).map(node => {
      // 🔧 修复：创建新对象，避免修改原始数据
      const newNode = { ...node }

      // 递归过滤子节点
      if (node.children && node.children.length > 0) {
        newNode.children = this.filterNewUserOrganizations(node.children, excludeNewUsers)
      }

      return newNode
    })
  }

  static collectExpandKeys(nodes, maxLevel = 3) {
    const keys = []
    const collect = (nodeList, currentLevel = 0) => {
      if (currentLevel >= maxLevel) return
      nodeList.forEach(node => {
        if (node.children && node.children.length > 0) {
          keys.push(node.id)
          collect(node.children, currentLevel + 1)
        }
      })
    }
    collect(nodes)
    return keys
  }

  static findNodeById(nodes, id) {
    for (const node of nodes) {
      if (node.id === id) return node
      if (node.children) {
        const found = this.findNodeById(node.children, id)
        if (found) return found
      }
    }
    return null
  }

  static findNodePath(nodes, id) {
    const path = []
    const find = (nodeList, targetId, currentPath = []) => {
      for (const node of nodeList) {
        const newPath = [...currentPath, node]
        if (node.id === targetId) {
          path.push(...newPath)
          return true
        }
        if (node.children && find(node.children, targetId, newPath)) {
          return true
        }
      }
      return false
    }
    find(nodes, id)
    return path
  }

  static filterNodes(nodes, predicate) {
    const filtered = []
    for (const node of nodes) {
      if (predicate(node)) {
        const newNode = { ...node }
        if (node.children) {
          newNode.children = this.filterNodes(node.children, predicate)
        }
        filtered.push(newNode)
      } else if (node.children) {
        const childFiltered = this.filterNodes(node.children, predicate)
        if (childFiltered.length > 0) {
          filtered.push({ ...node, children: childFiltered })
        }
      }
    }
    return filtered
  }
}

export function useOrganizationTree(options = {}) {
  const {
    autoLoad = true,
    enableCache = true,
    maxExpandLevel = 3,
    enableVirtualScroll = false
  } = options

  const userStore = useUserStore()
  
  // 本地状态
  const localLoading = ref(false)
  const selectedNode = ref(null)
  const expandedKeys = ref([])
  const searchText = ref('')
  const allExpanded = ref(false)

  // 计算属性
  const organizationTree = computed(() => {
    // 🔧 修复：统一数据处理逻辑
    let data = globalOrganizationData.value

    // 1. 首先过滤"新注册用户"层级（设备管理中心专用）
    data = OrganizationUtils.filterNewUserOrganizations(data, true)

    // 2. 应用权限过滤
    data = applyPermissionFilter(data)

    console.log('🔍 useOrganizationTree - organizationTree computed:', data)
    console.log('🔍 useOrganizationTree - 数据长度:', data.length)

    return data
  })
  const loading = computed(() => globalLoading.value || localLoading.value)
  const error = computed(() => globalError.value)
  const lastUpdate = computed(() => globalLastUpdate.value)

  // 🔧 修复：简化权限过滤函数，与后端API保持一致
  const applyPermissionFilter = (data) => {
    if (!data || data.length === 0) {
      console.log('🔍 applyPermissionFilter - 数据为空，返回空数组')
      return []
    }

    console.log('🔍 applyPermissionFilter - 输入数据:', data)
    console.log('🔍 applyPermissionFilter - 用户权限等级:', userStore.getPermissionLevel)
    console.log('🔍 applyPermissionFilter - 用户组织ID:', userStore.organizationId)

    // 🔧 修复：直接返回后端过滤后的数据，不再进行前端二次过滤
    // 后端API已经根据用户权限进行了正确的过滤，前端只需要应用UI层面的过滤
    const result = data.map(node => ({
      ...node,
      type: node.type || 'organization', // 确保有type字段
      children: node.children ? node.children.map(child => ({
        ...child,
        type: child.type || (child.users ? 'organization' : 'user')
      })) : []
    }))

    console.log('🔍 applyPermissionFilter - 输出数据:', result)
    return result
  }

  // 过滤后的组织架构（搜索过滤）
  const filteredTree = computed(() => {
    if (!searchText.value) return organizationTree.value

    return OrganizationUtils.filterNodes(organizationTree.value, node => {
      return node.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
             (node.type === 'user' && node.role_name?.toLowerCase().includes(searchText.value.toLowerCase()))
    })
  })

  // 默认展开的节点
  const defaultExpandedKeys = computed(() => {
    return OrganizationUtils.collectExpandKeys(organizationTree.value, maxExpandLevel)
  })

  // 权限检查
  const canManageOrganization = computed(() => {
    return userStore.hasPermission('organization.manage') || 
           userStore.canAccessDeviceManagement
  })

  // 方法
  const loadData = async (forceRefresh = false) => {
    try {
      localLoading.value = true
      await OrganizationDataManager.fetchData(forceRefresh)
    } finally {
      localLoading.value = false
    }
  }

  const refreshData = () => loadData(true)

  const clearCache = () => {
    CacheManager.clearCache()
    globalOrganizationData.value = []
    globalLastUpdate.value = null
  }

  const selectNode = (node) => {
    selectedNode.value = node
  }

  const expandAll = async () => {
    allExpanded.value = !allExpanded.value
    await nextTick()
    expandedKeys.value = allExpanded.value ? defaultExpandedKeys.value : []
  }

  const findNode = (id) => {
    return OrganizationUtils.findNodeById(organizationTree.value, id)
  }

  const getNodePath = (id) => {
    return OrganizationUtils.findNodePath(organizationTree.value, id)
  }

  // 订阅数据更新
  const unsubscribe = OrganizationDataManager.subscribe((data) => {
    // 数据更新时的回调处理
    if (expandedKeys.value.length === 0) {
      expandedKeys.value = defaultExpandedKeys.value
    }
  })

  // 生命周期
  onMounted(() => {
    if (autoLoad) {
      loadData()
    }
  })

  onUnmounted(() => {
    unsubscribe()
  })

  return {
    // 数据
    organizationTree,
    filteredTree,
    loading,
    error,
    lastUpdate,
    selectedNode,
    expandedKeys,
    defaultExpandedKeys,
    searchText,
    allExpanded,

    // 计算属性
    canManageOrganization,

    // 方法
    loadData,
    refreshData,
    clearCache,
    selectNode,
    expandAll,
    findNode,
    getNodePath,

    // 工具方法
    getLevelName: OrganizationUtils.getLevelName,
    collectExpandKeys: OrganizationUtils.collectExpandKeys,
    filterNodes: OrganizationUtils.filterNodes
  }
}
