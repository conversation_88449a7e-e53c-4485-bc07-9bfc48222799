/**
 * 设备管理中心统一数据协调层
 * 提供设备、从服务器、分组、权限数据的统一管理
 * 版本: 1.0
 * 创建日期: 2025-07-26
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getSlaveServerList, getSlaveServerStats } from '@/api/slaveServers'
import { getDeviceGroups } from '@/api/permission-assignment'
import { useUserStore } from '@/stores/user.js'

export function useDeviceCenter() {
  // 响应式数据
  const loading = ref(false)
  const slaveServers = ref([])
  const deviceGroups = ref([])
  const devices = ref([])
  const statistics = ref({
    slaveServers: {
      total: 0,
      online: 0,
      offline: 0
    },
    devices: {
      total: 0,
      available: 0,
      connected: 0,
      virtual: 0
    },
    groups: {
      total: 0,
      withDevices: 0
    },
    permissions: {
      totalAssignments: 0,
      activeUsers: 0
    }
  })

  // 定时器
  let refreshTimer = null
  const userStore = useUserStore()

  // 计算属性 - 基于权限等级的细粒度控制
  const hasDeviceManagePermission = computed(() => {
    return userStore.canAccessDeviceManagementTab
  })

  const hasSlaveServerPermission = computed(() => {
    return userStore.canAccessSlaveServerTab
  })

  const hasDeviceGroupPermission = computed(() => {
    return userStore.canAccessDeviceGroupTab
  })

  const hasPermissionAssignPermission = computed(() => {
    return userStore.canAccessPermissionAssignmentTab
  })

  // 可见的标签页
  const visibleTabs = computed(() => {
    const tabs = []
    
    if (hasDeviceManagePermission.value) {
      tabs.push({
        name: 'devices',
        label: 'USB设备管理',
        icon: 'Monitor'
      })
    }

    if (hasSlaveServerPermission.value) {
      tabs.push({
        name: 'slaves',
        label: '分布式节点管理',
        icon: 'Monitor'
      })
    }
    
    if (hasDeviceGroupPermission.value) {
      tabs.push({
        name: 'groups',
        label: '资源调度分组',
        icon: 'Collection'
      })
    }

    if (hasPermissionAssignPermission.value) {
      tabs.push({
        name: 'permissions',
        label: '授权范围管理',
        icon: 'Key'
      })
    }
    
    return tabs
  })

  // 获取从服务器列表
  const fetchSlaveServers = async () => {
    try {
      const response = await getSlaveServerList()
      if (response.success) {
        slaveServers.value = response.data || []
        updateSlaveServerStats()
      }
    } catch (error) {
      console.error('获取从服务器列表失败:', error)
    }
  }

  // 获取设备分组列表
  const fetchDeviceGroups = async () => {
    try {
      const response = await getDeviceGroups()
      if (response.success) {
        deviceGroups.value = response.data || []
        updateGroupStats()
      }
    } catch (error) {
      console.error('获取设备分组列表失败:', error)
    }
  }

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      const response = await getSlaveServerStats()
      if (response.success) {
        const stats = response.data

        // 更新从服务器统计（使用后端准确数据）
        statistics.value.slaveServers = {
          total: stats.total_servers || 0,
          online: stats.online_servers || 0,
          offline: stats.offline_servers || 0
        }

        // 更新设备统计
        statistics.value.devices = {
          total: stats.total_devices || 0,
          available: stats.available_devices || 0,
          connected: stats.connected_devices || 0,
          virtual: stats.virtual_devices || 0
        }

        console.log('统计数据已更新:', {
          servers: statistics.value.slaveServers,
          devices: statistics.value.devices
        })
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
      // 如果API失败，回退到本地计算
      updateSlaveServerStats()
    }
  }

  // 更新从服务器统计
  const updateSlaveServerStats = () => {
    const total = slaveServers.value.length
    const online = slaveServers.value.filter(server => 
      server.status === 'online' || server.is_online
    ).length
    
    statistics.value.slaveServers = {
      total,
      online,
      offline: total - online
    }
  }

  // 更新分组统计
  const updateGroupStats = () => {
    const total = deviceGroups.value.length
    const withDevices = deviceGroups.value.filter(group => 
      group.device_count > 0
    ).length
    
    statistics.value.groups = {
      total,
      withDevices
    }
  }

  // 刷新所有数据
  const refreshAllData = async () => {
    loading.value = true
    try {
      await Promise.all([
        fetchSlaveServers(),
        fetchDeviceGroups(), 
        fetchStatistics()
      ])
      ElMessage.success('数据刷新成功')
    } catch (error) {
      console.error('刷新数据失败:', error)
      ElMessage.error('数据刷新失败')
    } finally {
      loading.value = false
    }
  }

  // 启动定时刷新
  const startAutoRefresh = () => {
    // 根据服务器数量动态调整刷新频率
    const getRefreshInterval = () => {
      const serverCount = statistics.value.slaveServers.total
      if (serverCount > 1000) {
        return 10000  // 大规模时每10秒刷新
      } else if (serverCount > 100) {
        return 15000  // 中等规模时每15秒刷新
      } else {
        return 30000  // 小规模时每30秒刷新
      }
    }

    // 立即刷新一次
    fetchStatistics()

    // 设置定时刷新
    refreshTimer = setInterval(() => {
      fetchStatistics()
    }, getRefreshInterval())
  }

  // 停止定时刷新
  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  // 生命周期
  onMounted(() => {
    refreshAllData()
    startAutoRefresh()
  })

  onUnmounted(() => {
    stopAutoRefresh()
  })

  return {
    // 数据
    loading,
    slaveServers,
    deviceGroups,
    devices,
    statistics,
    
    // 计算属性
    hasDeviceManagePermission,
    hasSlaveServerPermission,
    hasDeviceGroupPermission,
    hasPermissionAssignPermission,
    visibleTabs,
    
    // 方法
    refreshAllData,
    fetchSlaveServers,
    fetchDeviceGroups,
    fetchStatistics,
    startAutoRefresh,
    stopAutoRefresh
  }
}
