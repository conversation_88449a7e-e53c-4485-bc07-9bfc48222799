/**
 * 权限操作审计系统Composable
 * 提供详细的权限变更追踪、操作日志和审计功能
 * 版本: 1.0
 * 创建日期: 2025-07-26
 */

import { ref, computed, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user.js'

// 审计日志存储键
const AUDIT_LOG_KEY = 'permission_audit_logs'
const MAX_LOG_ENTRIES = 1000

// 操作类型枚举
const OPERATION_TYPES = {
  ASSIGN: 'assign',           // 分配权限
  REVOKE: 'revoke',          // 撤销权限
  MODIFY: 'modify',          // 修改权限
  BATCH_ASSIGN: 'batch_assign', // 批量分配
  BATCH_REVOKE: 'batch_revoke', // 批量撤销
  COPY: 'copy',              // 复制权限
  TEMPLATE_APPLY: 'template_apply' // 应用模板
}

// 权限类型枚举
const PERMISSION_TYPES = {
  DEVICE_GROUP: 'device_group',  // 设备分组权限
  DEVICE: 'device',              // 单个设备权限
  ORGANIZATION: 'organization',   // 组织权限
  USER: 'user'                   // 用户权限
}

// 审计日志管理器
class AuditLogManager {
  static getLogs() {
    try {
      const logs = localStorage.getItem(AUDIT_LOG_KEY)
      return logs ? JSON.parse(logs) : []
    } catch (error) {
      console.error('读取审计日志失败:', error)
      return []
    }
  }

  static saveLogs(logs) {
    try {
      // 限制日志条数，保留最新的记录
      const limitedLogs = logs.slice(-MAX_LOG_ENTRIES)
      localStorage.setItem(AUDIT_LOG_KEY, JSON.stringify(limitedLogs))
    } catch (error) {
      console.error('保存审计日志失败:', error)
    }
  }

  static addLog(logEntry) {
    const logs = this.getLogs()
    logs.push({
      ...logEntry,
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      sessionId: this.getSessionId()
    })
    this.saveLogs(logs)
  }

  static getSessionId() {
    let sessionId = sessionStorage.getItem('audit_session_id')
    if (!sessionId) {
      sessionId = Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      sessionStorage.setItem('audit_session_id', sessionId)
    }
    return sessionId
  }

  static clearLogs() {
    localStorage.removeItem(AUDIT_LOG_KEY)
  }
}

export function usePermissionAudit() {
  const userStore = useUserStore()
  
  // 响应式状态
  const auditLogs = ref([])
  const loading = ref(false)
  const pendingOperations = ref([])
  const operationHistory = ref([])

  // 统计信息
  const statistics = reactive({
    totalOperations: 0,
    todayOperations: 0,
    successRate: 0,
    mostActiveUser: null,
    mostCommonOperation: null
  })

  // 计算属性
  const recentLogs = computed(() => {
    return auditLogs.value.slice(-50).reverse()
  })

  const todayLogs = computed(() => {
    const today = new Date().toDateString()
    return auditLogs.value.filter(log => 
      new Date(log.timestamp).toDateString() === today
    )
  })

  const canViewAuditLogs = computed(() => {
    return userStore.hasPermission('audit.view') || 
           userStore.canAccessDeviceManagement
  })

  const canManagePermissions = computed(() => {
    return userStore.hasPermission('permission.manage') || 
           userStore.canAccessDeviceManagement
  })

  // 核心方法
  const recordOperation = async (operation) => {
    try {
      // 记录审计日志
      const logEntry = {
        operationType: operation.type,
        operator: {
          id: userStore.userInfo.id,
          username: userStore.userInfo.username,
          role: userStore.userInfo.role_name,
          permissionLevel: userStore.userInfo.permission_level
        },
        targets: operation.targets,
        permissions: operation.permissions,
        details: operation.details || {},
        result: 'pending'
      }

      AuditLogManager.addLog(logEntry)
      pendingOperations.value.push(logEntry)

      return logEntry
    } catch (error) {
      console.error('记录操作失败:', error)
      ElMessage.error(`操作记录失败: ${error.message}`)
      throw error
    }
  }

  const completeOperation = (operationId, result, details = {}) => {
    const operation = pendingOperations.value.find(op => op.id === operationId)
    if (operation) {
      operation.result = result
      operation.completedAt = new Date().toISOString()
      operation.completionDetails = details

      // 更新审计日志
      const logs = AuditLogManager.getLogs()
      const logIndex = logs.findIndex(log => log.id === operationId)
      if (logIndex !== -1) {
        logs[logIndex] = operation
        AuditLogManager.saveLogs(logs)
      }

      // 移除待处理操作
      const pendingIndex = pendingOperations.value.findIndex(op => op.id === operationId)
      if (pendingIndex !== -1) {
        pendingOperations.value.splice(pendingIndex, 1)
      }

      // 添加到历史记录
      operationHistory.value.unshift(operation)

      // 更新统计信息
      updateStatistics()
    }
  }

  const loadAuditLogs = () => {
    try {
      loading.value = true
      auditLogs.value = AuditLogManager.getLogs()
      updateStatistics()
    } catch (error) {
      console.error('加载审计日志失败:', error)
      ElMessage.error('加载审计日志失败')
    } finally {
      loading.value = false
    }
  }

  const clearAuditLogs = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要清除所有审计日志吗？此操作不可恢复。',
        '确认清除',
        {
          confirmButtonText: '确定清除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      AuditLogManager.clearLogs()
      auditLogs.value = []
      operationHistory.value = []
      pendingOperations.value = []
      updateStatistics()

      ElMessage.success('审计日志已清除')
    } catch (error) {
      if (error !== 'cancel') {
        console.error('清除审计日志失败:', error)
        ElMessage.error('清除审计日志失败')
      }
    }
  }

  // 辅助方法
  const updateStatistics = () => {
    const logs = auditLogs.value
    statistics.totalOperations = logs.length
    statistics.todayOperations = todayLogs.value.length
    
    const successCount = logs.filter(log => log.result === 'success').length
    statistics.successRate = logs.length > 0 ? (successCount / logs.length * 100).toFixed(2) : 0

    // 计算最活跃用户
    const userCounts = {}
    logs.forEach(log => {
      const username = log.operator?.username
      if (username) {
        userCounts[username] = (userCounts[username] || 0) + 1
      }
    })
    statistics.mostActiveUser = Object.keys(userCounts).reduce((a, b) => 
      userCounts[a] > userCounts[b] ? a : b, null
    )

    // 计算最常见操作
    const operationCounts = {}
    logs.forEach(log => {
      const type = log.operationType
      if (type) {
        operationCounts[type] = (operationCounts[type] || 0) + 1
      }
    })
    statistics.mostCommonOperation = Object.keys(operationCounts).reduce((a, b) => 
      operationCounts[a] > operationCounts[b] ? a : b, null
    )
  }

  return {
    // 数据
    auditLogs,
    loading,
    pendingOperations,
    operationHistory,
    statistics,

    // 计算属性
    recentLogs,
    todayLogs,
    canViewAuditLogs,
    canManagePermissions,

    // 方法
    recordOperation,
    completeOperation,
    loadAuditLogs,
    clearAuditLogs,

    // 常量
    OPERATION_TYPES,
    PERMISSION_TYPES
  }
}
