/**
 * OrgUsers组件数据适配层
 * 复用useOrganizationTree功能，适配getOrganizationsWithUsers API
 * 版本: 1.0
 * 创建日期: 2025-07-26
 */

import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { getOrganizationsWithUsers } from '@/api/users'
import { useOrganizationTree } from '@/composables/useOrganizationTree.js'

export function useOrgUsersAdapter(options = {}) {
  const {
    autoLoad = true,
    enableCache = true
  } = options

  // 使用基础的组织架构管理功能
  const {
    loading: baseLoading,
    searchText,
    expandedKeys,
    defaultExpandedKeys,
    canManageOrganization,
    getLevelName,
    collectExpandKeys,
    filterNodes,
    findNode,
    getNodePath
  } = useOrganizationTree({
    autoLoad: false, // 禁用自动加载，使用自定义API
    enableCache,
    maxExpandLevel: 3
  })

  // OrgUsers特有的响应式数据
  const loading = ref(false)
  const organizationTreeData = ref([])
  const flatOrganizations = ref([])
  const organizationsResponse = ref([])
  const allUsers = ref([])
  const selectedNode = ref(null)

  // 计算属性
  const computedTreeData = computed(() => {
    return organizationTreeData.value
  })

  const filteredTreeData = computed(() => {
    if (!searchText.value) return organizationTreeData.value
    
    return filterNodes(organizationTreeData.value, node => {
      return node.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
             (node.type === 'user' && node.role_name?.toLowerCase().includes(searchText.value.toLowerCase()))
    })
  })

  // 数据转换工具
  const flattenOrganizations = (data) => {
    const flattened = []
    
    const flatten = (nodes, parentId = null) => {
      if (!Array.isArray(nodes)) return
      
      nodes.forEach(node => {
        flattened.push({
          ...node,
          parentId
        })
        
        if (node.children && node.children.length > 0) {
          flatten(node.children, node.id)
        }
      })
    }
    
    // 处理API中间件包装格式
    let organizationData = data
    if (data && data.success && data.data) {
      organizationData = data.data
    }
    
    flatten(Array.isArray(organizationData) ? organizationData : [organizationData])
    return flattened
  }

  const buildTree = (data) => {
    if (!data || !Array.isArray(data)) return []
    
    const buildNode = (node) => {
      const newNode = {
        id: node.id,
        name: node.name,
        type: node.type || 'organization',
        level: node.level || 0,
        children: []
      }
      
      // 添加用户特有属性
      if (node.type === 'user') {
        newNode.role_name = node.role_name
        newNode.username = node.username
        newNode.email = node.email
        newNode.permission_level = node.permission_level
      }
      
      // 添加组织特有属性
      if (node.type === 'organization') {
        newNode.description = node.description
        newNode.user_count = node.user_count || 0
        newNode.child_count = node.child_count || 0
        // 🔧 修复：保留users字段
        newNode.users = node.users || []
      }
      
      // 递归构建子节点
      if (node.children && Array.isArray(node.children)) {
        newNode.children = node.children.map(child => buildNode(child))
      }
      
      return newNode
    }
    
    return data.map(node => buildNode(node))
  }

  // 核心方法
  const loadOrganizationsWithUsers = async () => {
    loading.value = true
    try {
      const response = await getOrganizationsWithUsers()

      // 简化调试信息
      console.log('🔍 原始API响应类型:', typeof response, '是否数组:', Array.isArray(response))

      // 处理API中间件包装格式
      let organizationData = response
      if (response && response.success && response.data) {
        console.log('🔍 检测到API中间件包装格式，提取data字段:', response.data)
        organizationData = response.data
      }

      // 保存原始响应
      organizationsResponse.value = organizationData

      // 将嵌套的组织结构扁平化
      flatOrganizations.value = flattenOrganizations(organizationData)

      // 构建树形数据
      buildTreeData()
      
      ElMessage.success('组织架构数据加载成功')
    } catch (error) {
      console.error('加载组织架构失败:', error)
      ElMessage.error('加载组织架构失败')
      organizationTreeData.value = []
    } finally {
      loading.value = false
    }
  }

  const buildTreeData = () => {
    try {
      const response = organizationsResponse.value

      if (!response) {
        organizationTreeData.value = []
        return
      }

      // 确保response是数组格式
      let organizationArray = Array.isArray(response) ? response : [response]
      if (organizationArray.length === 0) {
        organizationTreeData.value = []
        return
      }

      // 构建新的树数据
      const newTreeData = buildTree(organizationArray)

      // 使用nextTick确保响应式更新
      nextTick(() => {
        organizationTreeData.value = newTreeData

        // 默认选中根节点
        if (organizationTreeData.value.length > 0 && !selectedNode.value) {
          selectedNode.value = organizationTreeData.value[0]
        }

        // 设置默认展开的节点
        expandedKeys.value = collectExpandKeys(organizationTreeData.value, 3)
      })

    } catch (error) {
      console.error('构建树结构失败:', error)
      organizationTreeData.value = []
    }
  }

  const refreshData = async () => {
    await loadOrganizationsWithUsers()
  }

  const selectNode = (node) => {
    selectedNode.value = node
  }

  const expandAll = async () => {
    await nextTick()
    expandedKeys.value = collectExpandKeys(organizationTreeData.value)
  }

  const collapseAll = async () => {
    await nextTick()
    expandedKeys.value = []
  }

  // 生命周期
  onMounted(() => {
    if (autoLoad) {
      loadOrganizationsWithUsers()
    }
  })

  return {
    // 数据
    loading: computed(() => loading.value || baseLoading.value),
    organizationTreeData,
    flatOrganizations,
    organizationsResponse,
    allUsers,
    selectedNode,
    searchText,
    expandedKeys,
    defaultExpandedKeys,

    // 计算属性
    computedTreeData,
    filteredTreeData,
    canManageOrganization,

    // 方法
    loadOrganizationsWithUsers,
    buildTreeData,
    refreshData,
    selectNode,
    expandAll,
    collapseAll,

    // 工具方法
    getLevelName,
    collectExpandKeys,
    filterNodes,
    findNode,
    getNodePath,
    flattenOrganizations,
    buildTree
  }
}
