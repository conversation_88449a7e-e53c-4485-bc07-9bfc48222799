<template>
  <div class="device-status-monitor">
    <!-- 状态概览卡片 -->
    <el-row :gutter="20" class="status-overview">
      <el-col :span="6">
        <el-card class="status-card online">
          <div class="status-content">
            <div class="status-number">{{ statusStats.online }}</div>
            <div class="status-label">在线设备</div>
          </div>
          <el-icon class="status-icon"><Connection /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card in-use">
          <div class="status-content">
            <div class="status-number">{{ statusStats.inUse }}</div>
            <div class="status-label">使用中</div>
          </div>
          <el-icon class="status-icon"><Monitor /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card available">
          <div class="status-content">
            <div class="status-number">{{ statusStats.available }}</div>
            <div class="status-label">可用设备</div>
          </div>
          <el-icon class="status-icon"><Check /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card offline">
          <div class="status-content">
            <div class="status-number">{{ statusStats.offline }}</div>
            <div class="status-label">离线设备</div>
          </div>
          <el-icon class="status-icon"><Close /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时状态列表 -->
    <el-card class="status-list-card">
      <template #header>
        <div class="card-header">
          <span>设备实时状态</span>
          <div class="header-actions">
            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              inactive-text="手动刷新"
              @change="toggleAutoRefresh"
            />
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshStatus"
              :loading="loading"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="deviceStatuses" 
        v-loading="loading"
        stripe
        style="width: 100%"
        :default-sort="{ prop: 'last_updated', order: 'descending' }"
      >
        <el-table-column prop="device_name" label="设备名称" width="200" />
        <el-table-column prop="device_type" label="设备类型" width="120" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.status)"
              size="small"
              :class="{ 'status-blinking': row.status === 'connecting' }"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="从服务器" width="150">
          <template #default="{ row }">
            <span>{{ row.slave_server_name || `服务器-${row.slave_server_id}` }}</span>
          </template>
        </el-table-column>
        <el-table-column label="当前用户" width="120">
          <template #default="{ row }">
            <span v-if="row.current_user">{{ row.current_user }}</span>
            <span v-else class="text-muted">无</span>
          </template>
        </el-table-column>
        <el-table-column label="连接时长" width="120">
          <template #default="{ row }">
            <span v-if="row.connected_at">{{ formatDuration(row.connected_at) }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column label="最后更新" width="160">
          <template #default="{ row }">
            <span>{{ formatTime(row.last_updated) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="网络延迟" width="100">
          <template #default="{ row }">
            <span v-if="row.ping_latency !== null">
              <el-tag 
                :type="getLatencyType(row.ping_latency)"
                size="small"
              >
                {{ row.ping_latency }}ms
              </el-tag>
            </span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 状态变化日志 -->
    <el-card class="status-log-card">
      <template #header>
        <div class="card-header">
          <span>状态变化日志</span>
          <el-button 
            type="text" 
            size="small" 
            @click="clearLogs"
          >
            清空日志
          </el-button>
        </div>
      </template>

      <div class="status-logs">
        <div 
          v-for="log in statusLogs" 
          :key="log.id"
          class="log-entry"
          :class="getLogClass(log.type)"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-device">{{ log.device_name }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="statusLogs.length === 0" class="empty-logs">
          暂无状态变化记录
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Connection, 
  Monitor, 
  Check, 
  Close, 
  Refresh 
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(true)
const deviceStatuses = ref([])
const statusLogs = ref([])
const refreshTimer = ref(null)

// 状态统计
const statusStats = computed(() => {
  const stats = {
    online: 0,
    inUse: 0,
    available: 0,
    offline: 0
  }
  
  deviceStatuses.value.forEach(device => {
    switch (device.status) {
      case 'online':
        stats.online++
        break
      case 'in_use':
        stats.inUse++
        break
      case 'available':
        stats.available++
        break
      case 'offline':
        stats.offline++
        break
    }
  })
  
  return stats
})

// 方法
const refreshStatus = async () => {
  loading.value = true
  try {
    // 获取设备状态
    const response = await fetch('/api/v1/devices/status')
    if (response.ok) {
      const newStatuses = await response.json()
      
      // 检查状态变化
      checkStatusChanges(newStatuses)
      
      // 更新设备状态
      deviceStatuses.value = newStatuses.map(device => ({
        ...device,
        last_updated: new Date().toISOString()
      }))
    } else {
      throw new Error(`HTTP ${response.status}`)
    }
  } catch (error) {
    console.error('获取设备状态失败:', error)
    ElMessage.error('获取设备状态失败')
  } finally {
    loading.value = false
  }
}

const checkStatusChanges = (newStatuses) => {
  const oldStatusMap = new Map()
  deviceStatuses.value.forEach(device => {
    oldStatusMap.set(device.id, device.status)
  })
  
  newStatuses.forEach(device => {
    const oldStatus = oldStatusMap.get(device.id)
    if (oldStatus && oldStatus !== device.status) {
      addStatusLog({
        device_name: device.device_name,
        message: `状态从 ${getStatusText(oldStatus)} 变更为 ${getStatusText(device.status)}`,
        type: getLogType(oldStatus, device.status)
      })
    }
  })
}

const addStatusLog = (logData) => {
  const log = {
    id: Date.now() + Math.random(),
    timestamp: new Date().toISOString(),
    ...logData
  }
  
  statusLogs.value.unshift(log)
  
  // 限制日志数量
  if (statusLogs.value.length > 100) {
    statusLogs.value = statusLogs.value.slice(0, 100)
  }
}

const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
  
  refreshTimer.value = setInterval(() => {
    refreshStatus()
  }, 5000) // 每5秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

const clearLogs = () => {
  statusLogs.value = []
  ElMessage.success('日志已清空')
}

const getStatusType = (status) => {
  switch (status) {
    case 'online':
    case 'available': return 'success'
    case 'in_use': return 'warning'
    case 'connecting': return 'primary'
    case 'offline': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'online': return '在线'
    case 'available': return '可用'
    case 'in_use': return '使用中'
    case 'connecting': return '连接中'
    case 'offline': return '离线'
    default: return '未知'
  }
}

const getLatencyType = (latency) => {
  if (latency < 50) return 'success'
  if (latency < 100) return 'warning'
  return 'danger'
}

const getLogType = (oldStatus, newStatus) => {
  if (newStatus === 'online' || newStatus === 'available') return 'success'
  if (newStatus === 'offline') return 'error'
  return 'info'
}

const getLogClass = (type) => {
  return `log-${type}`
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const formatDuration = (startTime) => {
  const start = new Date(startTime)
  const now = new Date()
  const diff = Math.floor((now - start) / 1000)
  
  if (diff < 60) return `${diff}秒`
  if (diff < 3600) return `${Math.floor(diff / 60)}分钟`
  return `${Math.floor(diff / 3600)}小时${Math.floor((diff % 3600) / 60)}分钟`
}

// 生命周期
onMounted(() => {
  refreshStatus()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.device-status-monitor {
  padding: 20px;
}

.status-overview {
  margin-bottom: 20px;
}

.status-card {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-card.online {
  border-left: 4px solid #67c23a;
}

.status-card.in-use {
  border-left: 4px solid #e6a23c;
}

.status-card.available {
  border-left: 4px solid #409eff;
}

.status-card.offline {
  border-left: 4px solid #f56c6c;
}

.status-content {
  position: relative;
  z-index: 2;
}

.status-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.status-label {
  font-size: 14px;
  color: #909399;
}

.status-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 36px;
  color: #e4e7ed;
  z-index: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-blinking {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

.status-logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-entry {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  width: 140px;
  color: #909399;
  font-size: 12px;
}

.log-device {
  width: 150px;
  font-weight: 500;
  color: #303133;
}

.log-message {
  flex: 1;
  color: #606266;
}

.log-success {
  background-color: #f0f9ff;
}

.log-error {
  background-color: #fef0f0;
}

.log-info {
  background-color: #f9f9f9;
}

.empty-logs {
  text-align: center;
  color: #909399;
  padding: 40px;
}

.text-muted {
  color: #909399;
}
</style>
