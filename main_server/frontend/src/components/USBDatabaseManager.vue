<template>
  <div class="usb-database-manager">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>USB数据库管理</span>
          <el-button 
            type="primary" 
            :loading="updating" 
            @click="updateDatabase"
            :disabled="!canUpdate"
          >
            <el-icon><Refresh /></el-icon>
            更新USB.IDS数据库
          </el-button>
        </div>
      </template>
      
      <div class="database-info">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-statistic title="厂商数量" :value="stats.vendor_count || 0" />
          </el-col>
          <el-col :span="12">
            <el-statistic title="设备数量" :value="stats.device_count || 0" />
          </el-col>
        </el-row>
        
        <el-divider />
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="数据库版本">
            {{ stats.version || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后更新">
            {{ formatDate(stats.last_updated) }}
          </el-descriptions-item>
          <el-descriptions-item label="数据来源">
            <el-link :href="stats.source_url" target="_blank" type="primary">
              {{ stats.source_url }}
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item label="缓存状态">
            <el-tag :type="stats.cache_loaded ? 'success' : 'warning'">
              {{ stats.cache_loaded ? '已加载' : '未加载' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="文件大小" v-if="stats.file_info?.file_exists">
            {{ formatFileSize(stats.file_info.file_size) }}
          </el-descriptions-item>
          <el-descriptions-item label="文件修改时间" v-if="stats.file_info?.file_exists">
            {{ formatDate(stats.file_info.file_modified) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <el-divider />
      
      <div class="device-lookup">
        <h4>设备信息查询</h4>
        <el-form :model="lookupForm" inline>
          <el-form-item label="厂商ID (VID)">
            <el-input 
              v-model="lookupForm.vendor_id" 
              placeholder="例如: 1BC0 或 0x1BC0"
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="产品ID (PID)">
            <el-input 
              v-model="lookupForm.product_id" 
              placeholder="例如: 0055 或 0x0055"
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item>
            <el-button 
              type="primary" 
              @click="lookupDevice"
              :loading="lookingUp"
              :disabled="!lookupForm.vendor_id || !lookupForm.product_id"
            >
              查询
            </el-button>
          </el-form-item>
        </el-form>
        
        <div v-if="deviceInfo" class="device-info">
          <el-alert 
            :title="`设备信息: ${deviceInfo.full_name || '未知设备'}`"
            type="info"
            :closable="false"
          >
            <template #default>
              <p><strong>厂商:</strong> {{ deviceInfo.vendor_name || '未知' }}</p>
              <p><strong>设备:</strong> {{ deviceInfo.device_name || '未知' }}</p>
              <p><strong>VID:</strong> {{ deviceInfo.vendor_id }}</p>
              <p><strong>PID:</strong> {{ deviceInfo.product_id }}</p>
            </template>
          </el-alert>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import request from '@/utils/request'

// 响应式数据
const stats = ref({})
const updating = ref(false)
const lookingUp = ref(false)
const deviceInfo = ref(null)

const lookupForm = ref({
  vendor_id: '',
  product_id: ''
})

// 计算属性
const canUpdate = computed(() => {
  // 这里可以添加权限检查逻辑
  return true
})

// 方法
const loadStats = async () => {
  try {
    const response = await request.get('/api/v1/usb-database/stats')
    stats.value = response.data
  } catch (error) {
    console.error('加载USB数据库统计信息失败:', error)
    ElMessage.error('加载数据库信息失败')
  }
}

const updateDatabase = async () => {
  try {
    await ElMessageBox.confirm(
      '更新USB数据库将从网络下载最新的usb.ids文件，这可能需要几分钟时间。是否继续？',
      '确认更新',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    updating.value = true
    
    const response = await request.post('/api/v1/usb-database/update')
    
    if (response.data.success) {
      ElMessage.success('USB数据库更新任务已启动，请稍后刷新查看结果')
      
      // 延迟刷新统计信息
      setTimeout(() => {
        loadStats()
      }, 5000)
    } else {
      ElMessage.error(response.data.message || '更新失败')
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新USB数据库失败:', error)
      ElMessage.error('更新数据库失败')
    }
  } finally {
    updating.value = false
  }
}

const lookupDevice = async () => {
  try {
    lookingUp.value = true
    deviceInfo.value = null
    
    const response = await request.get(
      `/api/v1/usb-database/device-info/${lookupForm.value.vendor_id}/${lookupForm.value.product_id}`
    )
    
    deviceInfo.value = response.data
    
  } catch (error) {
    console.error('查询设备信息失败:', error)
    ElMessage.error('查询设备信息失败')
  } finally {
    lookingUp.value = false
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  try {
    return new Date(dateString).toLocaleString('zh-CN')
  } catch {
    return dateString
  }
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.usb-database-manager {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.database-info {
  margin-bottom: 20px;
}

.device-lookup {
  margin-top: 20px;
}

.device-info {
  margin-top: 15px;
}

.device-info p {
  margin: 5px 0;
}
</style>
