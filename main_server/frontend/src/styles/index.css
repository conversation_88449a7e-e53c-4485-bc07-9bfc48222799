/**
 * OmniLink 全局样式
 * 版本: 1.0
 * 创建日期: 2024-12-19
 */

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  font-size: 14px;
  color: #303133;
  background-color: #f5f5f5;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 布局样式 */
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 1000;
}

.layout-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  width: 200px;
  background: #fff;
  border-right: 1px solid #e6e6e6;
  overflow-y: auto;
}

.layout-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f5f5f5;
}

/* 卡片样式 */
.content-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

/* 表单样式 */
.form-container {
  max-width: 600px;
  margin: 0 auto;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

/* 表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 状态标签 */
.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background: #f0f9ff;
  color: #1890ff;
  border: 1px solid #d1ecf1;
}

.status-inactive {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-pending {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

/* 工具提示 */
.tooltip-content {
  max-width: 300px;
  word-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sidebar {
    width: 180px;
  }
  
  .layout-content {
    padding: 15px;
  }
  
  .content-card {
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .layout-main {
    flex-direction: column;
  }
  
  .layout-sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e6e6e6;
  }
  
  .layout-content {
    padding: 10px;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  line-height: 1.5;
}

/* Stagewise 工具栏样式 */
.stagewise-toolbar {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  width: 60px !important;
  height: 60px !important;
  background: #3498db !important;
  border-radius: 50% !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
  cursor: pointer !important;
  z-index: 10000 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: white !important;
  font-size: 24px !important;
  transition: all 0.3s ease !important;
  user-select: none !important;
}

.stagewise-toolbar:hover {
  transform: scale(1.1) !important;
}

.stagewise-toolbar.active {
  background: #27ae60 !important;
}

/* Stagewise 选中元素样式 */
.stagewise-selected {
  outline: 3px solid #3498db !important;
  outline-offset: 2px !important;
  position: relative !important;
}

.stagewise-selected::after {
  content: '🎯 已选中';
  position: absolute;
  top: -30px;
  left: 0;
  background: #3498db;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 9999;
  pointer-events: none;
}
