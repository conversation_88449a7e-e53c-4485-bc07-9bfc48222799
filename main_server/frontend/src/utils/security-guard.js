/**
 * 前端安全防护系统
 * 版本: 1.0
 * 创建日期: 2025-07-31
 * 
 * 功能：
 * 1. 防止开发者工具调试
 * 2. 防止代码篡改
 * 3. 防止网络抓包分析
 * 4. 增强Token安全性
 */

class SecurityGuard {
  constructor() {
    this.isDebugMode = false
    this.securityChecks = []
    this.encryptionKey = this.generateEncryptionKey()
    this.init()
  }

  /**
   * 初始化安全防护
   */
  init() {
    this.detectDebugger()
    this.preventRightClick()
    this.preventKeyboardShortcuts()
    this.obfuscateConsole()
    this.setupNetworkMonitoring()
    this.startSecurityChecks()
  }

  /**
   * 生成加密密钥
   */
  generateEncryptionKey() {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2)
    return btoa(`${timestamp}-${random}`).substring(0, 32)
  }

  /**
   * 检测调试器
   */
  detectDebugger() {
    const detectDebug = () => {
      const start = performance.now()
      debugger
      const end = performance.now()
      
      if (end - start > 100) {
        this.handleSecurityViolation('debugger_detected')
      }
    }

    // 定期检测
    setInterval(detectDebug, 1000)

    // 检测开发者工具
    const detectDevTools = () => {
      const threshold = 160
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        this.handleSecurityViolation('devtools_detected')
      }
    }

    setInterval(detectDevTools, 500)
  }

  /**
   * 防止右键菜单
   */
  preventRightClick() {
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault()
      this.handleSecurityViolation('right_click_attempt')
    })
  }

  /**
   * 防止键盘快捷键
   */
  preventKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // 防止F12, Ctrl+Shift+I, Ctrl+U等
      if (e.key === 'F12' || 
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.shiftKey && e.key === 'C') ||
          (e.ctrlKey && e.key === 'u')) {
        e.preventDefault()
        this.handleSecurityViolation('keyboard_shortcut_attempt')
      }
    })
  }

  /**
   * 混淆控制台
   */
  obfuscateConsole() {
    const originalConsole = window.console
    window.console = {
      log: () => {},
      warn: () => {},
      error: () => {},
      info: () => {},
      debug: () => {},
      clear: () => {},
      dir: () => {},
      dirxml: () => {},
      table: () => {},
      trace: () => {},
      group: () => {},
      groupCollapsed: () => {},
      groupEnd: () => {},
      time: () => {},
      timeEnd: () => {},
      timeLog: () => {},
      assert: () => {},
      count: () => {},
      countReset: () => {}
    }

    // 在生产环境中完全禁用console
    if (process.env.NODE_ENV === 'production') {
      Object.freeze(window.console)
    }
  }

  /**
   * 网络监控
   */
  setupNetworkMonitoring() {
    // 定义允许的URL模式白名单
    const allowedPatterns = [
      '/api/',           // API请求
      '/static/',        // 静态资源
      '/assets/',        // 资源文件
      'data:',           // Data URLs
      'blob:',           // Blob URLs
      'chrome-extension:', // 浏览器插件
      'moz-extension:',  // Firefox插件
      '/favicon.ico',    // 网站图标
      '/manifest.json',  // PWA清单
      'localhost',       // 本地开发
      '127.0.0.1',       // 本地IP
      window.location.origin // 同源请求
    ]

    // 检查URL是否在白名单中
    const isAllowedUrl = (url) => {
      if (!url) return true
      const urlStr = url.toString()
      return allowedPatterns.some(pattern => urlStr.includes(pattern))
    }

    // 监控XMLHttpRequest
    const originalXHR = window.XMLHttpRequest
    window.XMLHttpRequest = function() {
      const xhr = new originalXHR()
      const originalOpen = xhr.open
      const originalSend = xhr.send

      xhr.open = function(method, url, ...args) {
        this._method = method
        this._url = url
        return originalOpen.apply(this, [method, url, ...args])
      }

      xhr.send = function(data) {
        // 检测可疑的网络请求 - 使用白名单机制
        if (this._url && !isAllowedUrl(this._url)) {
          console.warn(`Potentially suspicious XHR request: ${this._url}`)
          // 改为警告模式，不阻止请求
        }
        return originalSend.apply(this, [data])
      }

      return xhr
    }

    // 监控fetch
    const originalFetch = window.fetch
    window.fetch = function(url, options) {
      if (typeof url === 'string' && !isAllowedUrl(url)) {
        console.warn(`Potentially suspicious fetch request: ${url}`)
        // 改为警告模式，不阻止请求
      }
      return originalFetch.apply(this, [url, options])
    }
  }

  /**
   * 启动安全检查
   */
  startSecurityChecks() {
    // 检查页面完整性
    setInterval(() => {
      this.checkPageIntegrity()
    }, 5000)

    // 检查内存使用
    setInterval(() => {
      this.checkMemoryUsage()
    }, 10000)
  }

  /**
   * 检查页面完整性
   */
  checkPageIntegrity() {
    // 检查关键DOM元素是否被篡改
    const criticalElements = document.querySelectorAll('[data-security-critical]')
    criticalElements.forEach(element => {
      if (element.hasAttribute('data-tampered')) {
        this.handleSecurityViolation('dom_tampering')
      }
    })
  }

  /**
   * 检查内存使用
   */
  checkMemoryUsage() {
    if (performance.memory) {
      const { usedJSHeapSize, totalJSHeapSize } = performance.memory
      const usage = usedJSHeapSize / totalJSHeapSize
      
      if (usage > 0.9) {
        this.handleSecurityViolation('memory_exhaustion')
      }
    }
  }

  /**
   * 处理安全违规
   */
  handleSecurityViolation(type) {
    console.warn(`Security violation detected: ${type}`)
    
    // 记录安全事件
    this.logSecurityEvent(type)
    
    // 根据违规类型采取不同措施
    switch (type) {
      case 'debugger_detected':
      case 'devtools_detected':
        this.handleDebuggerViolation()
        break
      case 'dom_tampering':
        this.handleTamperingViolation()
        break
      case 'suspicious_network_request':
        this.handleNetworkViolation()
        break
      default:
        this.handleGenericViolation()
    }
  }

  /**
   * 处理调试器违规
   */
  handleDebuggerViolation() {
    // 清除敏感数据
    this.clearSensitiveData()
    
    // 重定向到安全页面
    if (Math.random() > 0.7) {
      window.location.href = '/security-warning'
    }
  }

  /**
   * 处理篡改违规
   */
  handleTamperingViolation() {
    // 刷新页面
    window.location.reload()
  }

  /**
   * 处理网络违规
   */
  handleNetworkViolation() {
    // 改为警告模式，不阻止网络请求
    console.warn('Network security warning: Suspicious request detected but not blocked')
    // 记录事件但不采取阻止措施
    this.logSecurityEvent('network_warning')
  }

  /**
   * 处理通用违规
   */
  handleGenericViolation() {
    // 增加安全检查频率
    this.increaseSecurityChecks()
  }

  /**
   * 清除敏感数据
   */
  clearSensitiveData() {
    // 清除localStorage
    localStorage.clear()
    
    // 清除sessionStorage
    sessionStorage.clear()
    
    // 清除cookies
    document.cookie.split(";").forEach(cookie => {
      const eqPos = cookie.indexOf("=")
      const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
    })
  }

  /**
   * 阻止网络请求 - 已禁用，改为警告模式
   */
  blockNetworkRequests() {
    // 功能已禁用，避免误杀正常请求
    console.warn('Network blocking function called but disabled for stability')

    // 原有的阻止逻辑已注释
    // window.XMLHttpRequest = function() {
    //   throw new Error('Network requests blocked due to security violation')
    // }
    //
    // window.fetch = function() {
    //   throw new Error('Fetch requests blocked due to security violation')
    // }
  }

  /**
   * 增加安全检查频率
   */
  increaseSecurityChecks() {
    this.securityChecks.forEach(check => {
      if (check.interval) {
        clearInterval(check.interval)
        check.interval = setInterval(check.fn, check.frequency / 2)
      }
    })
  }

  /**
   * 记录安全事件
   */
  logSecurityEvent(type) {
    const event = {
      type,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      referrer: document.referrer
    }

    // 发送到后端安全日志 - 修复认证问题
    try {
      // 获取认证token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token')

      // 如果没有token，只记录到控制台，不发送到服务器
      if (!token) {
        console.warn(`Security event logged locally: ${type}`)
        return
      }

      fetch('/api/v1/security/log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(event)
      }).catch(() => {
        // 静默处理错误，避免暴露安全机制
        console.warn(`Failed to log security event: ${type}`)
      })
    } catch (e) {
      // 静默处理错误
      console.warn(`Security logging error: ${e.message}`)
    }
  }

  /**
   * 加密敏感数据
   */
  encryptData(data) {
    try {
      const encrypted = btoa(JSON.stringify(data) + this.encryptionKey)
      return encrypted
    } catch (e) {
      return data
    }
  }

  /**
   * 解密敏感数据
   */
  decryptData(encryptedData) {
    try {
      const decrypted = atob(encryptedData)
      const data = decrypted.replace(this.encryptionKey, '')
      return JSON.parse(data)
    } catch (e) {
      return encryptedData
    }
  }
}

// 创建全局安全守卫实例
const securityGuard = new SecurityGuard()

export default securityGuard
