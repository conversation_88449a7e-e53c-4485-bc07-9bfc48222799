/**
 * WebSocket客户端管理器
 * 支持自动重连、心跳检测、事件订阅等功能
 */

import { ElMessage } from 'element-plus'

class WebSocketManager {
  constructor() {
    this.ws = null
    this.url = ''
    this.token = ''
    this.isConnected = false
    this.isConnecting = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
    this.heartbeatInterval = 30000
    this.heartbeatTimer = null
    this.reconnectTimer = null
    
    // 事件监听器
    this.eventListeners = new Map()
    
    // 订阅列表
    this.subscriptions = new Set()
    
    // 消息队列（连接断开时缓存消息）
    this.messageQueue = []
    
    // 连接状态回调
    this.onConnectionChange = null
  }
  
  /**
   * 连接WebSocket
   * @param {string} token - 用户认证token
   */
  connect(token) {
    if (this.isConnecting || this.isConnected) {
      return
    }
    
    this.token = token
    this.url = this.getWebSocketUrl()
    this.isConnecting = true
    
    try {
      this.ws = new WebSocket(this.url)
      this.setupEventHandlers()
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.handleConnectionError()
    }
  }
  
  /**
   * 断开WebSocket连接
   */
  disconnect() {
    this.isConnecting = false
    this.clearTimers()
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    this.isConnected = false
    this.reconnectAttempts = 0
    this.notifyConnectionChange(false)
  }
  
  /**
   * 发送消息
   * @param {Object} message - 要发送的消息
   */
  send(message) {
    if (this.isConnected && this.ws) {
      try {
        this.ws.send(JSON.stringify(message))
        return true
      } catch (error) {
        console.error('发送消息失败:', error)
        return false
      }
    } else {
      // 连接断开时将消息加入队列
      this.messageQueue.push(message)
      return false
    }
  }
  
  /**
   * 订阅事件类型
   * @param {string} subscriptionType - 订阅类型
   */
  subscribe(subscriptionType) {
    this.subscriptions.add(subscriptionType)
    
    if (this.isConnected) {
      this.send({
        type: 'subscribe',
        subscription_type: subscriptionType
      })
    }
  }
  
  /**
   * 取消订阅
   * @param {string} subscriptionType - 订阅类型
   */
  unsubscribe(subscriptionType) {
    this.subscriptions.delete(subscriptionType)
    
    if (this.isConnected) {
      this.send({
        type: 'unsubscribe',
        subscription_type: subscriptionType
      })
    }
  }
  
  /**
   * 添加事件监听器
   * @param {string} eventType - 事件类型
   * @param {Function} callback - 回调函数
   */
  addEventListener(eventType, callback) {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set())
    }
    this.eventListeners.get(eventType).add(callback)
  }
  
  /**
   * 移除事件监听器
   * @param {string} eventType - 事件类型
   * @param {Function} callback - 回调函数
   */
  removeEventListener(eventType, callback) {
    if (this.eventListeners.has(eventType)) {
      this.eventListeners.get(eventType).delete(callback)
    }
  }
  
  /**
   * 设置连接状态变化回调
   * @param {Function} callback - 回调函数
   */
  setConnectionChangeCallback(callback) {
    this.onConnectionChange = callback
  }
  
  /**
   * 获取WebSocket URL
   */
  getWebSocketUrl() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    return `${protocol}//${host}/ws/${this.token}`
  }
  
  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    this.ws.onopen = () => {
      console.log('WebSocket连接已建立')
      this.isConnected = true
      this.isConnecting = false
      this.reconnectAttempts = 0
      
      // 重新订阅所有事件
      this.subscriptions.forEach(subscriptionType => {
        this.send({
          type: 'subscribe',
          subscription_type: subscriptionType
        })
      })
      
      // 发送队列中的消息
      this.flushMessageQueue()
      
      // 启动心跳
      this.startHeartbeat()
      
      // 通知连接状态变化
      this.notifyConnectionChange(true)
      
      ElMessage.success('实时连接已建立')
    }
    
    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        this.handleMessage(message)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }
    
    this.ws.onclose = (event) => {
      console.log('WebSocket连接已关闭:', event.code, event.reason)
      this.isConnected = false
      this.isConnecting = false
      this.clearTimers()
      
      // 通知连接状态变化
      this.notifyConnectionChange(false)
      
      // 尝试重连
      if (event.code !== 1000) { // 非正常关闭
        this.attemptReconnect()
      }
    }
    
    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
      this.handleConnectionError()
    }
  }
  
  /**
   * 处理接收到的消息
   * @param {Object} message - 消息对象
   */
  handleMessage(message) {
    const { type } = message
    
    // 触发对应的事件监听器
    if (this.eventListeners.has(type)) {
      this.eventListeners.get(type).forEach(callback => {
        try {
          callback(message)
        } catch (error) {
          console.error('事件监听器执行失败:', error)
        }
      })
    }
    
    // 处理特殊消息类型
    switch (type) {
      case 'pong':
        // 心跳响应
        break
      case 'connection_established':
        console.log('WebSocket连接确认:', message.connection_id)
        break
      case 'subscription_confirmed':
        console.log('订阅确认:', message.subscription_type)
        break
      case 'error':
        console.error('服务器错误:', message.message)
        ElMessage.error(`服务器错误: ${message.message}`)
        break
      default:
        // 其他消息类型由事件监听器处理
        break
    }
  }
  
  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    this.clearHeartbeat()
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping' })
      }
    }, this.heartbeatInterval)
  }
  
  /**
   * 清除心跳定时器
   */
  clearHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }
  
  /**
   * 尝试重连
   */
  attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限')
      ElMessage.error('连接失败，请刷新页面重试')
      return
    }
    
    this.reconnectAttempts++
    console.log(`WebSocket重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`)
    
    this.reconnectTimer = setTimeout(() => {
      if (!this.isConnected && !this.isConnecting) {
        this.connect(this.token)
      }
    }, this.reconnectInterval * this.reconnectAttempts)
  }
  
  /**
   * 处理连接错误
   */
  handleConnectionError() {
    this.isConnected = false
    this.isConnecting = false
    this.clearTimers()
    this.notifyConnectionChange(false)
  }
  
  /**
   * 清除所有定时器
   */
  clearTimers() {
    this.clearHeartbeat()
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }
  
  /**
   * 发送队列中的消息
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.send(message)
    }
  }
  
  /**
   * 通知连接状态变化
   * @param {boolean} connected - 是否已连接
   */
  notifyConnectionChange(connected) {
    if (this.onConnectionChange) {
      this.onConnectionChange(connected)
    }
  }
  
  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      subscriptions: Array.from(this.subscriptions)
    }
  }
}

// 创建全局实例
const websocketManager = new WebSocketManager()

export default websocketManager
