/**
 * API响应格式处理工具
 * 版本: 1.0
 * 创建日期: 2025-07-17
 * 描述: 提供统一的API响应格式处理函数，解决不同格式响应的兼容问题
 */

/**
 * 处理API响应，确保返回统一格式
 * @param {Object} response API响应对象
 * @param {Object} options 选项
 * @param {string} options.dataField 数据字段名称
 * @param {*} options.defaultValue 默认值
 * @returns {Object} 标准化的响应对象 {success, data, message, error_code}
 */
export function handleApiResponse(response, options = {}) {
  const { dataField, defaultValue = null } = options;
  
  // 如果响应为空
  if (!response) {
    return {
      success: false,
      data: defaultValue,
      message: '响应数据为空',
      error_code: 'EMPTY_RESPONSE'
    };
  }
  
  // 标准格式：直接包含success字段
  if (response.success !== undefined) {
    // 如果指定了数据字段，从response.data中提取
    if (dataField && response.data && response.data[dataField] !== undefined) {
      return {
        success: response.success,
        data: response.data[dataField],
        message: response.message || '',
        error_code: response.error_code
      };
    }
    return response;
  }
  
  // 嵌套格式：success在response.data中
  if (response.data && response.data.success !== undefined) {
    // 如果指定了数据字段，从response.data.data中提取
    if (dataField && response.data.data && response.data.data[dataField] !== undefined) {
      return {
        success: response.data.success,
        data: response.data.data[dataField],
        message: response.data.message || '',
        error_code: response.data.error_code
      };
    }
    return {
      success: response.data.success,
      data: response.data.data,
      message: response.data.message || '',
      error_code: response.data.error_code
    };
  }
  
  // 直接数据格式：没有success字段，但有指定的数据字段
  if (dataField && response[dataField] !== undefined) {
    return {
      success: true,
      data: response[dataField],
      message: '',
      error_code: null
    };
  }
  
  // 直接数据格式：没有success字段，但有data字段
  if (response.data !== undefined) {
    // 如果指定了数据字段，从response.data中提取
    if (dataField && response.data[dataField] !== undefined) {
      return {
        success: true,
        data: response.data[dataField],
        message: '',
        error_code: null
      };
    }
    return {
      success: true,
      data: response.data,
      message: '',
      error_code: null
    };
  }
  
  // 其他情况：将整个响应作为数据返回
  return {
    success: true,
    data: response,
    message: '',
    error_code: null
  };
}

/**
 * 从API响应中提取数据
 * @param {Object} response API响应对象
 * @param {string} dataField 数据字段名称
 * @param {*} defaultValue 默认值
 * @returns {*} 提取的数据或默认值
 */
export function extractDataFromResponse(response, dataField, defaultValue = null) {
  const standardResponse = handleApiResponse(response, { dataField });
  return standardResponse.success ? standardResponse.data : defaultValue;
}

/**
 * 检查API响应是否成功
 * @param {Object} response API响应对象
 * @returns {boolean} 是否成功
 */
export function isResponseSuccessful(response) {
  const standardResponse = handleApiResponse(response);
  return standardResponse.success;
}

/**
 * 从API响应中提取错误消息
 * @param {Object} response API响应对象
 * @param {string} defaultMessage 默认错误消息
 * @returns {string} 错误消息
 */
export function extractErrorMessage(response, defaultMessage = '操作失败') {
  const standardResponse = handleApiResponse(response);
  return standardResponse.message || defaultMessage;
}

/**
 * 处理API错误
 * @param {Error} error 错误对象
 * @param {string} defaultMessage 默认错误消息
 * @returns {Object} 标准化的错误响应对象
 */
export function handleApiError(error, defaultMessage = '操作失败') {
  console.error('API错误:', error);
  
  // 如果错误对象包含响应
  if (error.response) {
    const { status, data } = error.response;
    
    // 尝试从响应中提取错误消息
    const message = data?.message || data?.detail || `${defaultMessage} (${status})`;
    
    return {
      success: false,
      data: null,
      message,
      error_code: data?.error_code || `HTTP_${status}`
    };
  }
  
  // 网络错误
  if (error.message === 'Network Error') {
    return {
      success: false,
      data: null,
      message: '网络连接失败，请检查网络',
      error_code: 'NETWORK_ERROR'
    };
  }
  
  // 超时错误
  if (error.code === 'ECONNABORTED') {
    return {
      success: false,
      data: null,
      message: '请求超时，请稍后重试',
      error_code: 'TIMEOUT_ERROR'
    };
  }
  
  // 其他错误
  return {
    success: false,
    data: null,
    message: error.message || defaultMessage,
    error_code: 'UNKNOWN_ERROR'
  };
}