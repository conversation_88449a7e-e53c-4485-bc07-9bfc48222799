/**
 * 简化Token管理系统
 * 版本: 2.0
 * 创建日期: 2025-08-01
 * 
 * 功能：
 * 1. 简单Token存储
 * 2. 基础自动刷新机制
 * 3. 统一错误处理
 */

class SimpleTokenManager {
  constructor() {
    this.tokenKey = 'omnilink_token'
    this.refreshKey = 'omnilink_refresh_token'
    this.lastActivityKey = 'omnilink_last_activity'
    this.refreshTimer = null
    
    this.setupAutoRefresh()
  }

  /**
   * 更新最后活动时间
   */
  updateLastActivity() {
    localStorage.setItem(this.lastActivityKey, Date.now().toString())
  }

  /**
   * 检查token是否过期
   */
  isTokenExpired(tokenData) {
    if (!tokenData || !tokenData.timestamp) {
      return true
    }
    
    // Token有效期24小时
    const tokenAge = Date.now() - tokenData.timestamp
    const maxAge = 24 * 60 * 60 * 1000 // 24小时
    
    return tokenAge > maxAge
  }

  /**
   * 存储Token
   */
  setToken(token, refreshToken = null) {
    try {
      const tokenData = {
        token,
        timestamp: Date.now()
      }

      localStorage.setItem(this.tokenKey, JSON.stringify(tokenData))
      
      if (refreshToken) {
        const refreshData = {
          refreshToken,
          timestamp: Date.now()
        }
        localStorage.setItem(this.refreshKey, JSON.stringify(refreshData))
      }

      this.updateLastActivity()
      return true
    } catch (error) {
      console.error('Token storage failed:', error)
      return false
    }
  }

  /**
   * 获取Token
   */
  getToken() {
    try {
      const tokenDataStr = localStorage.getItem(this.tokenKey)
      if (!tokenDataStr) {
        return null
      }

      const tokenData = JSON.parse(tokenDataStr)
      if (!tokenData || !tokenData.token) {
        this.clearToken()
        return null
      }

      // 检查Token是否过期
      if (this.isTokenExpired(tokenData)) {
        console.warn('Token expired')
        this.clearToken()
        return null
      }

      this.updateLastActivity()
      return tokenData.token
    } catch (error) {
      console.error('Token retrieval failed:', error)
      this.clearToken()
      return null
    }
  }

  /**
   * 获取刷新Token
   */
  getRefreshToken() {
    try {
      const refreshDataStr = localStorage.getItem(this.refreshKey)
      if (!refreshDataStr) {
        return null
      }

      const refreshData = JSON.parse(refreshDataStr)
      if (!refreshData || !refreshData.refreshToken) {
        return null
      }

      return refreshData.refreshToken
    } catch (error) {
      console.error('Refresh token retrieval failed:', error)
      return null
    }
  }

  /**
   * 清除Token
   */
  clearToken() {
    try {
      localStorage.removeItem(this.tokenKey)
      localStorage.removeItem(this.refreshKey)
      localStorage.removeItem(this.lastActivityKey)
      
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    } catch (error) {
      console.error('Token clearing failed:', error)
    }
  }

  /**
   * 检查Token是否存在
   */
  hasToken() {
    return !!this.getToken()
  }

  /**
   * 设置自动刷新
   */
  setupAutoRefresh() {
    // 每30分钟检查一次token状态
    this.refreshTimer = setInterval(() => {
      this.checkAndRefreshToken()
    }, 30 * 60 * 1000)
  }

  /**
   * 检查并刷新Token
   */
  async checkAndRefreshToken() {
    try {
      const token = this.getToken()
      if (!token) {
        return false
      }

      const tokenDataStr = localStorage.getItem(this.tokenKey)
      if (!tokenDataStr) {
        return false
      }

      const tokenData = JSON.parse(tokenDataStr)
      const tokenAge = Date.now() - tokenData.timestamp
      
      // 如果token还有2小时就过期，尝试刷新
      if (tokenAge > 22 * 60 * 60 * 1000) {
        return await this.refreshToken()
      }

      return true
    } catch (error) {
      console.error('Token check failed:', error)
      return false
    }
  }

  /**
   * 刷新Token
   */
  async refreshToken() {
    try {
      const refreshToken = this.getRefreshToken()
      if (!refreshToken) {
        console.warn('No refresh token available')
        return false
      }

      const response = await fetch('/api/v1/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          refresh_token: refreshToken
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.access_token) {
          this.setToken(data.access_token, data.refresh_token)
          console.log('Token refreshed successfully')
          return true
        }
      }

      console.warn('Token refresh failed')
      this.clearToken()
      return false
    } catch (error) {
      console.error('Token refresh error:', error)
      this.clearToken()
      return false
    }
  }

  /**
   * 生成CSRF Token（简化版）
   */
  generateCSRFToken() {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15)
  }

  /**
   * 获取最后活动时间
   */
  getLastActivity() {
    try {
      const lastActivity = localStorage.getItem(this.lastActivityKey)
      return lastActivity ? parseInt(lastActivity) : null
    } catch (error) {
      return null
    }
  }

  /**
   * 检查用户是否活跃
   */
  isUserActive() {
    const lastActivity = this.getLastActivity()
    if (!lastActivity) {
      return false
    }

    // 如果超过1小时没有活动，认为用户不活跃
    const inactiveTime = Date.now() - lastActivity
    return inactiveTime < 60 * 60 * 1000
  }
}

// 创建全局实例
const simpleTokenManager = new SimpleTokenManager()

export default simpleTokenManager
