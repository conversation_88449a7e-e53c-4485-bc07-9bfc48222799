/**
 * 前端双维度权限控制工具 - 新版本
 * 实现基于数字等级+角色验证的权限控制
 * 版本: 2.0 - 移除硬编码依赖，支持双维度权限验证
 */

// 权限级别定义 - 基于数字等级
export const PermissionLevel = {
  GLOBAL_ADMIN: 0,      // 全域管理员
  SUPER_ADMIN: 1,       // 超级管理员
  MANAGER: 2,           // 管理员
  USER: 3,              // 普通用户
  NEW_USER: 4           // 新用户
}

// 权限等级映射表
export const PERMISSION_LEVEL_MAP = {
  "全域管理员": PermissionLevel.GLOBAL_ADMIN,
  "超级管理员": PermissionLevel.SUPER_ADMIN,
  "管理员": PermissionLevel.MANAGER,
  "普通用户": PermissionLevel.USER,
  "新用户": PermissionLevel.NEW_USER
}

/**
 * 双维度权限检查器类 - 基于数字等级+角色验证
 */
export class DualPermissionChecker {
  /**
   * 获取用户权限等级 - 核心方法，避免循环依赖
   */
  static getPermissionLevel(user) {
    if (!user) return PermissionLevel.NEW_USER
    
    // 优先使用数字权限等级
    if (user.permission_level !== undefined && user.permission_level !== null) {
      return user.permission_level
    }
    
    // 兼容旧版本：基于角色名称映射
    return PERMISSION_LEVEL_MAP[user.role_name] || PermissionLevel.NEW_USER
  }

  /**
   * 检查是否为特殊用户
   */
  static isSpecialUser(user) {
    return user && (user.is_special_user === true || user.username === 'firefly')
  }

  /**
   * 检查权限等级是否满足要求
   */
  static checkPermissionLevel(user, requiredLevel) {
    if (!user) return false
    
    // 特殊用户拥有所有权限
    if (this.isSpecialUser(user)) return true
    
    const userLevel = this.getPermissionLevel(user)
    // 权限等级数值越小，权限越高
    return userLevel <= requiredLevel
  }

  /**
   * 检查是否为全域管理员
   */
  static isGlobalAdmin(user) {
    return user && (this.getPermissionLevel(user) === PermissionLevel.GLOBAL_ADMIN || this.isSpecialUser(user))
  }

  /**
   * 检查是否为超级管理员
   */
  static isSuperAdmin(user) {
    return user && this.getPermissionLevel(user) === PermissionLevel.SUPER_ADMIN
  }

  /**
   * 检查是否为管理员
   */
  static isManager(user) {
    return user && this.getPermissionLevel(user) === PermissionLevel.MANAGER
  }

  /**
   * 检查是否为普通用户
   */
  static isUser(user) {
    return user && this.getPermissionLevel(user) === PermissionLevel.USER
  }

  /**
   * 检查是否为新用户
   */
  static isNewUser(user) {
    return user && this.getPermissionLevel(user) === PermissionLevel.NEW_USER
  }

  /**
   * 检查是否可以管理目标用户
   */
  static canManageUser(manager, target) {
    if (!manager || !target) return false
    
    // 特殊用户可以管理所有用户
    if (this.isSpecialUser(manager)) return true
    
    // 不能管理特殊用户（除非自己也是特殊用户）
    if (this.isSpecialUser(target)) return false
    
    const managerLevel = this.getPermissionLevel(manager)
    const targetLevel = this.getPermissionLevel(target)
    
    // 管理者权限等级必须高于目标用户
    return managerLevel < targetLevel
  }

  /**
   * 检查是否可以访问用户管理
   */
  static canAccessUserManagement(user) {
    return this.checkPermissionLevel(user, PermissionLevel.MANAGER)
  }

  /**
   * 检查是否可以访问角色管理
   */
  static canAccessRoleManagement(user) {
    return this.checkPermissionLevel(user, PermissionLevel.SUPER_ADMIN)
  }

  /**
   * 检查是否可以访问设备管理
   */
  static canAccessDeviceManagement(user) {
    // 🔧 修复：允许普通用户访问设备管理中心（但功能受限）
    return this.checkPermissionLevel(user, PermissionLevel.USER)
  }

  /**
   * 检查是否可以访问系统配置
   */
  static canAccessSystemConfig(user) {
    return this.checkPermissionLevel(user, PermissionLevel.SUPER_ADMIN)
  }

  /**
   * 检查是否可以访问审计日志
   */
  static canAccessAuditLogs(user) {
    return this.checkPermissionLevel(user, PermissionLevel.SUPER_ADMIN)
  }

  /**
   * 检查是否可以访问个人中心
   */
  static canAccessPersonalCenter(user) {
    return this.checkPermissionLevel(user, PermissionLevel.NEW_USER)
  }

  /**
   * 过滤菜单项目
   */
  static filterMenuItems(menuItems, user) {
    if (!menuItems || !user) return []
    
    return menuItems.filter(item => {
      // 根据菜单项的权限要求进行过滤
      switch (item.permission) {
        case 'user-management':
          return this.canAccessUserManagement(user)
        case 'role-management':
          return this.canAccessRoleManagement(user)
        case 'device-management':
          return this.canAccessDeviceManagement(user)
        case 'system-config':
          return this.canAccessSystemConfig(user)
        case 'audit-logs':
          return this.canAccessAuditLogs(user)
        case 'personal-center':
          return this.canAccessPersonalCenter(user)
        default:
          return true  // 默认允许访问
      }
    }).map(item => {
      // 递归过滤子菜单
      if (item.children && item.children.length > 0) {
        return {
          ...item,
          children: this.filterMenuItems(item.children, user)
        }
      }
      return item
    })
  }
}

/**
 * 路由权限守卫
 */
export class DualRouteGuard {
  /**
   * 检查路由访问权限
   */
  static checkRoutePermission(routePath, user) {
    if (!user) return false

    // 新用户只能访问个人中心
    if (DualPermissionChecker.isNewUser(user)) {
      return routePath === '/personal-center' || routePath === '/login' || routePath === '/logout'
    }

    // 其他用户根据权限等级检查
    const userLevel = DualPermissionChecker.getPermissionLevel(user)
    
    // 根据路由路径判断所需权限等级
    const routePermissions = {
      // 基础页面 - 所有用户都可以访问
      '/': PermissionLevel.NEW_USER,
      '/dashboard': PermissionLevel.NEW_USER,
      '/profile': PermissionLevel.NEW_USER,
      '/personal-center': PermissionLevel.NEW_USER,

      // 用户和组织管理
      '/org-users': PermissionLevel.MANAGER,
      '/user-management': PermissionLevel.MANAGER,
      '/users': PermissionLevel.MANAGER,
      '/organizations': PermissionLevel.MANAGER,

      // 高级管理功能
      '/role-management': PermissionLevel.SUPER_ADMIN,
      '/devices': PermissionLevel.SUPER_ADMIN,
      '/device-management': PermissionLevel.SUPER_ADMIN,
      '/system-config': PermissionLevel.SUPER_ADMIN,
      '/audit-logs': PermissionLevel.SUPER_ADMIN,

      // 申请处理
      '/applications': PermissionLevel.REGULAR_USER,
      '/applications/create': PermissionLevel.REGULAR_USER,
      '/user-registration': PermissionLevel.MANAGER
    }

    const requiredLevel = routePermissions[routePath]
    if (requiredLevel !== undefined) {
      return DualPermissionChecker.checkPermissionLevel(user, requiredLevel)
    }

    return true  // 默认允许访问
  }

  /**
   * 获取默认重定向路径
   */
  static getDefaultRedirectPath(user) {
    if (!user) return '/login'

    // 新用户只能访问个人资料
    if (DualPermissionChecker.isNewUser(user)) {
      return '/profile'
    }

    // 普通用户访问工作台
    if (DualPermissionChecker.isUser(user)) {
      return '/dashboard'
    }

    // 管理员访问组织用户管理
    if (DualPermissionChecker.isManager(user)) {
      return '/org-users'
    }

    // 超级管理员和全域管理员访问工作台
    return '/dashboard'
  }
}

/**
 * 数据权限过滤器
 */
export class DualDataFilter {
  /**
   * 过滤用户列表数据
   */
  static filterUserList(users, currentUser) {
    if (!users || !currentUser) return []

    // 特殊用户可以看到所有用户
    if (DualPermissionChecker.isSpecialUser(currentUser)) {
      return users
    }

    // 全域管理员可以看到所有用户
    if (DualPermissionChecker.isGlobalAdmin(currentUser)) {
      return users
    }

    // 超级管理员不能看到特殊用户
    if (DualPermissionChecker.isSuperAdmin(currentUser)) {
      return users.filter(user => !DualPermissionChecker.isSpecialUser(user))
    }

    // 管理员只能看到同级组织的用户
    if (DualPermissionChecker.isManager(currentUser)) {
      return users.filter(user => user.organization_id === currentUser.organization_id)
    }

    // 普通用户只能看到同级组织的用户
    if (DualPermissionChecker.isUser(currentUser)) {
      return users.filter(user => user.organization_id === currentUser.organization_id)
    }

    // 新用户只能看到自己
    return users.filter(user => user.id === currentUser.id)
  }

  /**
   * 过滤组织数据
   */
  static filterOrganizationList(organizations, currentUser) {
    if (!organizations || !currentUser) return []

    // 特殊用户和全域管理员可以看到所有组织
    if (DualPermissionChecker.isSpecialUser(currentUser) || DualPermissionChecker.isGlobalAdmin(currentUser)) {
      return organizations
    }

    // 超级管理员可以看到所有组织
    if (DualPermissionChecker.isSuperAdmin(currentUser)) {
      return organizations
    }

    // 管理员只能看到自己所属的组织及其子组织
    if (DualPermissionChecker.isManager(currentUser)) {
      const userOrgId = currentUser.organization_id
      return organizations.filter(org => 
        org.id === userOrgId || org.parent_id === userOrgId
      )
    }

    // 普通用户和新用户只能看到自己所属的组织
    return organizations.filter(org => org.id === currentUser.organization_id)
  }
}

// 导出兼容性别名
export const PermissionChecker = DualPermissionChecker
export const RouteGuard = DualRouteGuard
export const DataFilter = DualDataFilter
