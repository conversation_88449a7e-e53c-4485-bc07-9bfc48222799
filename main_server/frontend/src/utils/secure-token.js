/**
 * 安全Token管理系统
 * 版本: 1.1 (修复版)
 * 创建日期: 2025-08-01
 *
 * 功能：
 * 1. Token加密存储
 * 2. 自动刷新机制
 * 3. 防篡改验证
 * 4. 安全传输
 */

import CryptoJS from 'crypto-js'

class SecureTokenManager {
  constructor() {
    this.secretKey = this.generateSecretKey()
    this.tokenKey = 'omnilink_secure_token'
    this.refreshKey = 'omnilink_refresh_token'
    this.checksumKey = 'omnilink_token_checksum'
    this.lastActivityKey = 'omnilink_last_activity'

    this.setupAutoRefresh()
    this.setupActivityTracking()
  }

  /**
   * 获取浏览器指纹（安全验证必需）
   */
  getBrowserFingerprint() {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx.textBaseline = 'top'
    ctx.font = '14px Arial'
    ctx.fillText('Browser fingerprint', 2, 2)

    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|')

    return CryptoJS.SHA256(fingerprint).toString()
  }

  /**
   * 加密数据（安全存储必需）
   */
  encrypt(data) {
    try {
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), this.secretKey).toString()
      return encrypted
    } catch (error) {
      console.error('Encryption failed:', error)
      return null
    }
  }

  /**
   * 解密数据（安全验证必需）
   */
  decrypt(encryptedData) {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, this.secretKey)
      const decrypted = bytes.toString(CryptoJS.enc.Utf8)
      return JSON.parse(decrypted)
    } catch (error) {
      console.error('Decryption failed:', error)
      return null
    }
  }

  /**
   * 生成校验和（防篡改必需）
   */
  generateChecksum(data) {
    return CryptoJS.SHA256(JSON.stringify(data) + this.secretKey).toString()
  }

  /**
   * 验证校验和（防篡改必需）
   */
  verifyChecksum(data, checksum) {
    const expectedChecksum = this.generateChecksum(data)
    return expectedChecksum === checksum
  }

  /**
   * 生成密钥（安全基础）
   */
  generateSecretKey() {
    // 使用固定的浏览器指纹作为密钥基础，避免每次生成不同密钥
    const browserFingerprint = this.getBrowserFingerprint()

    // 检查是否已有存储的密钥
    const storedKey = localStorage.getItem('omnilink_secret_key')
    if (storedKey) {
      return storedKey
    }

    // 生成新密钥并存储
    const newKey = CryptoJS.SHA256(browserFingerprint + 'omnilink_static_salt').toString()
    localStorage.setItem('omnilink_secret_key', newKey)
    return newKey
  }

  /**
   * 设置活动跟踪
   */
  setupActivityTracking() {
    // 监听用户活动
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']

    const updateActivity = () => {
      this.updateLastActivity()
    }

    events.forEach(event => {
      document.addEventListener(event, updateActivity, true)
    })
  }

  /**
   * 更新最后活动时间
   */
  updateLastActivity() {
    localStorage.setItem(this.lastActivityKey, Date.now().toString())
  }

  /**
   * 安全存储Token
   */
  setToken(token, refreshToken = null) {
    try {
      const tokenData = {
        token,
        timestamp: Date.now(),
        fingerprint: this.getBrowserFingerprint()
      }

      const encryptedToken = this.encrypt(tokenData)
      const checksum = this.generateChecksum(tokenData)

      if (encryptedToken) {
        localStorage.setItem(this.tokenKey, encryptedToken)
        localStorage.setItem(this.checksumKey, checksum)

        if (refreshToken) {
          const refreshData = {
            refreshToken,
            timestamp: Date.now()
          }
          const encryptedRefresh = this.encrypt(refreshData)
          localStorage.setItem(this.refreshKey, encryptedRefresh)
        }

        this.updateLastActivity()
        return true
      }
    } catch (error) {
      console.error('Token storage failed:', error)
    }
    return false
  }

  /**
   * 安全获取Token（最大容错机制）
   */
  getToken() {
    try {
      const encryptedToken = localStorage.getItem(this.tokenKey)
      const checksum = localStorage.getItem(this.checksumKey)

      if (!encryptedToken || !checksum) {
        return null
      }

      let tokenData = null

      // 第一次尝试解密
      try {
        tokenData = this.decrypt(encryptedToken)
      } catch (decryptError) {
        console.warn('First decryption attempt failed:', decryptError)

        // 尝试重新生成密钥
        localStorage.removeItem('omnilink_secret_key')
        this.secretKey = this.generateSecretKey()

        try {
          tokenData = this.decrypt(encryptedToken)
        } catch (retryError) {
          console.warn('Retry decryption failed:', retryError)
          return null // 不清除token，保留数据
        }
      }

      if (!tokenData) {
        console.warn('Token data is null after decryption')
        return null // 不清除token，保留数据
      }

      // 校验和验证（容错处理）
      try {
        if (!this.verifyChecksum(tokenData, checksum)) {
          console.warn('Token checksum verification failed, but continuing')
          // 不清除token，继续使用
        }
      } catch (checksumError) {
        console.warn('Checksum verification error:', checksumError)
        // 继续使用token
      }

      // 浏览器指纹验证（完全容错）
      try {
        const currentFingerprint = this.getBrowserFingerprint()
        if (tokenData.fingerprint && tokenData.fingerprint !== currentFingerprint) {
          console.warn('Browser fingerprint mismatch, but continuing')
          // 继续使用token，不清除
        }
      } catch (fingerprintError) {
        console.warn('Fingerprint verification error:', fingerprintError)
        // 继续使用token
      }

      // 过期检查（宽松处理）
      try {
        const tokenAge = Date.now() - tokenData.timestamp
        if (tokenAge > 48 * 60 * 60 * 1000) { // 延长到48小时
          console.warn('Token expired after 48 hours')
          this.clearToken()
          return null
        }
      } catch (timeError) {
        console.warn('Time verification error:', timeError)
        // 继续使用token
      }

      this.updateLastActivity()
      return tokenData.token
    } catch (error) {
      console.error('Token retrieval failed:', error)
      // 绝不清除token，保留所有数据
      return null
    }
  }

  /**
   * 获取刷新Token
   */
  getRefreshToken() {
    try {
      const encryptedRefresh = localStorage.getItem(this.refreshKey)
      if (!encryptedRefresh) {
        return null
      }

      const refreshData = this.decrypt(encryptedRefresh)
      if (!refreshData) {
        return null
      }

      // 检查刷新Token是否过期（7天）
      const refreshAge = Date.now() - refreshData.timestamp
      if (refreshAge > 7 * 24 * 60 * 60 * 1000) {
        this.clearToken()
        return null
      }

      return refreshData.refreshToken
    } catch (error) {
      console.error('Refresh token retrieval failed:', error)
      return null
    }
  }

  /**
   * 清除Token
   */
  clearToken() {
    localStorage.removeItem(this.tokenKey)
    localStorage.removeItem(this.refreshKey)
    localStorage.removeItem(this.checksumKey)
    localStorage.removeItem(this.lastActivityKey)
  }

  /**
   * 更新最后活动时间
   */
  updateLastActivity() {
    localStorage.setItem(this.lastActivityKey, Date.now().toString())
  }

  /**
   * 获取最后活动时间
   */
  getLastActivity() {
    const lastActivity = localStorage.getItem(this.lastActivityKey)
    return lastActivity ? parseInt(lastActivity) : 0
  }

  /**
   * 检查是否需要刷新Token
   */
  shouldRefreshToken() {
    const lastActivity = this.getLastActivity()
    const inactiveTime = Date.now() - lastActivity
    
    // 如果超过30分钟无活动，需要刷新Token
    return inactiveTime > 30 * 60 * 1000
  }

  /**
   * 刷新Token
   */
  async refreshToken() {
    try {
      const refreshToken = this.getRefreshToken()
      if (!refreshToken) {
        throw new Error('No refresh token available')
      }

      const response = await fetch('/api/v1/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ refresh_token: refreshToken })
      })

      if (!response.ok) {
        throw new Error('Token refresh failed')
      }

      const data = await response.json()
      if (data.success && data.data.access_token) {
        this.setToken(data.data.access_token, data.data.refresh_token)
        return data.data.access_token
      } else {
        throw new Error('Invalid refresh response')
      }
    } catch (error) {
      console.error('Token refresh failed:', error)
      // 绝不清除token或重定向，保持用户会话
      return null
    }
  }

  /**
   * 设置自动刷新（已重新启用）
   */
  setupAutoRefresh() {
    // 重新启用自动刷新，修复token管理
    console.log('Auto refresh enabled with improved token management')

    // 每30分钟检查一次token状态
    setInterval(async () => {
      try {
        const token = this.getToken()
        if (token && this.shouldRefreshToken()) {
          const refreshed = await this.refreshToken()
          if (refreshed) {
            console.log('Token auto-refreshed successfully')
          }
        }
      } catch (error) {
        console.warn('Auto refresh check failed:', error)
      }
    }, 30 * 60 * 1000) // 每30分钟检查一次
  }

  /**
   * 设置活动跟踪
   */
  setupActivityTracking() {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
    
    const updateActivity = () => {
      this.updateLastActivity()
    }

    events.forEach(event => {
      document.addEventListener(event, updateActivity, true)
    })
  }

  /**
   * 验证Token有效性
   */
  async validateToken() {
    const token = this.getToken()
    if (!token) {
      return false
    }

    try {
      const response = await fetch('/api/v1/auth/validate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        return data.success === true
      } else if (response.status === 401) {
        // Token无效，尝试刷新
        return await this.refreshToken() !== null
      }
    } catch (error) {
      console.error('Token validation failed:', error)
    }

    return false
  }

  /**
   * 安全的API请求包装
   */
  async secureRequest(url, options = {}) {
    let token = this.getToken()
    
    if (!token) {
      throw new Error('No valid token available')
    }

    // 如果需要刷新Token
    if (this.shouldRefreshToken()) {
      token = await this.refreshToken()
      if (!token) {
        throw new Error('Token refresh failed')
      }
    }

    const secureOptions = {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': this.generateCSRFToken()
      }
    }

    const response = await fetch(url, secureOptions)
    
    if (response.status === 401) {
      // Token可能已过期，尝试刷新
      token = await this.refreshToken()
      if (token) {
        secureOptions.headers['Authorization'] = `Bearer ${token}`
        return fetch(url, secureOptions)
      } else {
        throw new Error('Authentication failed')
      }
    }

    return response
  }

  /**
   * 生成CSRF Token
   */
  generateCSRFToken() {
    const timestamp = Date.now().toString()
    const random = Math.random().toString(36).substring(2)
    return CryptoJS.SHA256(timestamp + random + this.secretKey).toString().substring(0, 32)
  }
}

// 创建全局安全Token管理器实例
const secureTokenManager = new SecureTokenManager()

export default secureTokenManager
