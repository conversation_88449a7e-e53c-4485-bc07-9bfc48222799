/**
 * 前端权限控制工具 - 双维度权限控制系统
 * 实现基于数字等级+角色验证的权限控制
 * 版本: 2.0 - 移除硬编码依赖，支持双维度权限验证
 */

// 权限级别定义 - 基于数字等级
export const PermissionLevel = {
  GLOBAL_ADMIN: 0,      // 全域管理员
  SUPER_ADMIN: 1,       // 超级管理员
  MANAGER: 2,           // 管理员
  USER: 3,              // 普通用户
  NEW_USER: 4           // 新用户
}

// 权限等级映射表
export const PERMISSION_LEVEL_MAP = {
  "全域管理员": PermissionLevel.GLOBAL_ADMIN,
  "超级管理员": PermissionLevel.SUPER_ADMIN,
  "管理员": PermissionLevel.MANAGER,
  "普通用户": PermissionLevel.USER,
  "新用户": PermissionLevel.NEW_USER
}

/**
 * 双维度权限检查器类 - 基于数字等级+角色验证
 */
export class PermissionChecker {
  /**
   * 获取用户权限等级
   */
  static getPermissionLevel(user) {
    if (!user) return PermissionLevel.NEW_USER

    // 优先使用数字权限等级
    if (user.permission_level !== undefined && user.permission_level !== null) {
      return user.permission_level
    }

    // 兼容旧版本：基于角色名称映射
    return PERMISSION_LEVEL_MAP[user.role_name] || PermissionLevel.NEW_USER
  }

  /**
   * 检查是否为特殊用户
   */
  static isSpecialUser(user) {
    return user && (user.is_special_user === true || user.username === 'firefly')
  }

  /**
   * 检查是否为全域管理员
   */
  static isGlobalAdmin(user) {
    return user && (this.getPermissionLevel(user) === PermissionLevel.GLOBAL_ADMIN || this.isSpecialUser(user))
  }

  /**
   * 检查是否为超级管理员
   */
  static isSuperAdmin(user) {
    return user && this.getPermissionLevel(user) === PermissionLevel.SUPER_ADMIN
  }

  /**
   * 检查是否为管理员
   */
  static isAdmin(user) {
    return user && this.getPermissionLevel(user) === PermissionLevel.MANAGER
  }

  /**
   * 检查是否为普通用户
   */
  static isRegularUser(user) {
    return user && this.getPermissionLevel(user) === PermissionLevel.USER
  }

  /**
   * 检查是否为新用户
   */
  static isNewUser(user) {
    return user && this.getPermissionLevel(user) === PermissionLevel.NEW_USER
  }



  /**
   * 检查是否可以访问用户管理
   */
  static canAccessUserManagement(user) {
    return this.isGlobalAdmin(user) || this.isSuperAdmin(user) || this.isAdmin(user)
  }

  /**
   * 检查是否可以访问角色管理
   */
  static canAccessRoleManagement(user) {
    return this.isGlobalAdmin(user) || this.isSuperAdmin(user)
  }

  /**
   * 检查是否可以访问管理员管理
   */
  static canAccessAdminManagement(user) {
    return this.isGlobalAdmin(user)
  }

  /**
   * 检查是否可以访问系统配置
   */
  static canAccessSystemConfig(user) {
    return this.isGlobalAdmin(user) || this.isSuperAdmin(user)
  }

  /**
   * 检查是否可以访问审计日志
   */
  static canAccessAuditLogs(user) {
    return this.isGlobalAdmin(user) || this.isSuperAdmin(user)
  }

  /**
   * 检查是否可以访问设备管理
   */
  static canAccessDeviceManagement(user) {
    return this.isGlobalAdmin(user) || this.isSuperAdmin(user) || this.isAdmin(user)
  }

  /**
   * 检查是否可以访问申请管理
   */
  static canAccessApplicationManagement(user) {
    return this.isGlobalAdmin(user) || this.isSuperAdmin(user) || this.isAdmin(user) || this.isRegularUser(user)
  }

  /**
   * 检查是否可以访问个人中心
   */
  static canAccessPersonalCenter(user) {
    return true  // 所有用户都可以访问个人中心
  }

  /**
   * 检查是否可以访问工作台
   */
  static canAccessWorkspace(user) {
    return !this.isNewUser(user)  // 新用户不能访问工作台
  }

  /**
   * 检查是否可以访问工作台（Dashboard）
   */
  static canAccessDashboard(user) {
    // 🔧 修复：管理员及以上可访问工作台，普通用户和新用户不能访问
    return this.isGlobalAdmin(user) || this.isSuperAdmin(user) || this.isAdmin(user)
  }
}

/**
 * 菜单项权限过滤器
 */
export class MenuFilter {
  /**
   * 根据用户权限过滤菜单项
   */
  static filterMenuItems(menuItems, user) {
    if (!user) return []

    return menuItems.filter(item => {
      // 检查菜单项权限
      switch (item.key) {
        case 'workspace':
          return PermissionChecker.canAccessWorkspace(user)
        case 'user-management':
          return PermissionChecker.canAccessUserManagement(user)
        case 'role-management':
          return PermissionChecker.canAccessRoleManagement(user)
        case 'admin-management':
          return PermissionChecker.canAccessAdminManagement(user)
        case 'device-management':
          return PermissionChecker.canAccessDeviceManagement(user)
        case 'application-management':
          return PermissionChecker.canAccessApplicationManagement(user)
        case 'system-config':
          return PermissionChecker.canAccessSystemConfig(user)
        case 'audit-logs':
          return PermissionChecker.canAccessAuditLogs(user)
        case 'personal-center':
          return PermissionChecker.canAccessPersonalCenter(user)
        default:
          return true  // 默认允许访问
      }
    }).map(item => {
      // 递归过滤子菜单
      if (item.children && item.children.length > 0) {
        return {
          ...item,
          children: this.filterMenuItems(item.children, user)
        }
      }
      return item
    })
  }
}

/**
 * 路由权限守卫
 */
export class RouteGuard {
  /**
   * 检查路由访问权限
   */
  static checkRoutePermission(routePath, user) {
    if (!user) return false

    // 新用户只能访问个人中心
    if (PermissionChecker.isNewUser(user)) {
      return routePath === '/personal-center' || routePath === '/login' || routePath === '/logout'
    }

    // 根据路由路径检查权限
    switch (true) {
      case routePath.startsWith('/dashboard'):
        // 🔧 修复：工作台权限检查 - 管理员及以上可访问
        return PermissionChecker.canAccessDashboard(user)
      case routePath.startsWith('/org-users'):
        // 🔧 新增：组织用户管理权限检查 - 管理员及以上可访问
        return PermissionChecker.canAccessUserManagement(user)
      case routePath.startsWith('/user-management'):
        return PermissionChecker.canAccessUserManagement(user)
      case routePath.startsWith('/role-management'):
        return PermissionChecker.canAccessRoleManagement(user)
      case routePath.startsWith('/admin-management'):
        return PermissionChecker.canAccessAdminManagement(user)
      case routePath.startsWith('/device-management'):
        return PermissionChecker.canAccessDeviceManagement(user)
      case routePath.startsWith('/application-management'):
        return PermissionChecker.canAccessApplicationManagement(user)
      case routePath.startsWith('/system-config'):
        return PermissionChecker.canAccessSystemConfig(user)
      case routePath.startsWith('/audit-logs'):
        return PermissionChecker.canAccessAuditLogs(user)
      case routePath.startsWith('/workspace'):
        return PermissionChecker.canAccessWorkspace(user)
      case routePath.startsWith('/personal-center'):
        return PermissionChecker.canAccessPersonalCenter(user)
      default:
        return true  // 默认允许访问
    }
  }

  /**
   * 获取用户默认重定向路径
   */
  static getDefaultRedirectPath(user) {
    if (!user) return '/login'

    // 新用户只能访问个人中心
    if (PermissionChecker.isNewUser(user)) {
      return '/profile'
    }

    // 🔧 修复：管理员及以上跳转到工作台，普通用户跳转到个人资料
    if (PermissionChecker.canAccessDashboard(user)) {
      return '/dashboard'
    } else {
      return '/profile'
    }
  }
}

/**
 * 数据权限过滤器
 */
export class DataFilter {
  /**
   * 过滤用户列表数据
   */
  static filterUserList(users, currentUser) {
    if (!users || !currentUser) return []

    // 全域管理员可以看到所有用户
    if (PermissionChecker.isGlobalAdmin(currentUser)) {
      return users
    }

    // 超级管理员不能看到firefly账户
    if (PermissionChecker.isSuperAdmin(currentUser)) {
      return users.filter(user => user.username !== GLOBAL_ADMIN_USERNAME)
    }

    // 管理员只能看到同级组织的用户
    if (PermissionChecker.isAdmin(currentUser)) {
      return users.filter(user => user.organization_id === currentUser.organization_id)
    }

    // 普通用户只能看到同级组织的用户
    if (PermissionChecker.isRegularUser(currentUser)) {
      return users.filter(user => user.organization_id === currentUser.organization_id)
    }

    // 新用户只能看到自己
    if (PermissionChecker.isNewUser(currentUser)) {
      return users.filter(user => user.id === currentUser.id)
    }

    return []
  }

  /**
   * 过滤角色列表数据
   */
  static filterRoleList(roles, currentUser) {
    if (!roles || !currentUser) return []

    // 只有全域管理员可以看到"全域管理员"角色
    if (!PermissionChecker.isGlobalAdmin(currentUser)) {
      return roles.filter(role => role.name !== "全域管理员")
    }

    return roles
  }
}
