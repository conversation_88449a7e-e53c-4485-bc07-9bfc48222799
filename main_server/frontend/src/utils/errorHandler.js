/**
 * 全局错误处理工具
 * 版本: 1.0
 * 创建日期: 2025-07-17
 * 描述: 提供统一的错误处理函数，用于处理API错误和前端异常
 */

import { ElMessage, ElNotification } from 'element-plus'
import { extractErrorMessage } from './apiResponseHandler'

/**
 * 处理API错误
 * @param {Error|Object} error 错误对象或API响应
 * @param {Object} options 选项
 * @param {string} options.defaultMessage 默认错误消息
 * @param {boolean} options.showNotification 是否显示通知
 * @param {boolean} options.logToConsole 是否记录到控制台
 */
export function handleError(error, options = {}) {
  const {
    defaultMessage = '操作失败',
    showNotification = false,
    logToConsole = true
  } = options;
  
  // 提取错误消息
  const errorMessage = extractErrorMessage(error, defaultMessage);
  
  // 记录到控制台
  if (logToConsole) {
    console.error('错误:', error);
  }
  
  // 显示错误消息
  if (showNotification) {
    ElNotification({
      title: '错误',
      message: errorMessage,
      type: 'error',
      duration: 5000
    });
  } else {
    ElMessage.error(errorMessage);
  }
  
  return errorMessage;
}

/**
 * 处理异步操作
 * @param {Function} asyncFn 异步函数
 * @param {Object} options 选项
 * @param {string} options.successMessage 成功消息
 * @param {string} options.errorMessage 错误消息
 * @param {boolean} options.showSuccessMessage 是否显示成功消息
 * @param {boolean} options.showErrorMessage 是否显示错误消息
 * @returns {Promise} 处理结果
 */
export async function handleAsync(asyncFn, options = {}) {
  const {
    successMessage = '操作成功',
    errorMessage = '操作失败',
    showSuccessMessage = true,
    showErrorMessage = true
  } = options;
  
  try {
    const result = await asyncFn();
    
    if (showSuccessMessage) {
      ElMessage.success(successMessage);
    }
    
    return result;
  } catch (error) {
    if (showErrorMessage) {
      handleError(error, { defaultMessage: errorMessage });
    }
    
    throw error;
  }
}

/**
 * 创建API错误处理函数
 * @param {string} operationName 操作名称
 * @returns {Function} 错误处理函数
 */
export function createErrorHandler(operationName) {
  return (error) => {
    return handleError(error, {
      defaultMessage: `${operationName}失败`,
      showNotification: true
    });
  };
}

/**
 * 全局未捕获异常处理
 */
export function setupGlobalErrorHandlers() {
  // 处理未捕获的Promise异常
  window.addEventListener('unhandledrejection', (event) => {
    console.error('未捕获的Promise异常:', event.reason);
    
    // 避免显示过多错误消息
    if (!event.reason._handled) {
      event.reason._handled = true;
      
      ElNotification({
        title: '系统错误',
        message: '发生未处理的异常，请刷新页面或联系管理员',
        type: 'error',
        duration: 0
      });
    }
    
    // 阻止默认处理
    event.preventDefault();
  });
  
  // 处理未捕获的JS异常
  window.onerror = (message, source, lineno, colno, error) => {
    console.error('未捕获的JS异常:', { message, source, lineno, colno, error });
    
    ElNotification({
      title: '系统错误',
      message: '发生未处理的异常，请刷新页面或联系管理员',
      type: 'error',
      duration: 0
    });
    
    // 返回true表示已处理
    return true;
  };
}