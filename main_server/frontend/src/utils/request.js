/**
 * HTTP请求工具
 * 版本: 1.1
 * 创建日期: 2024-12-19
 * 更新日期: 2025-07-17
 * 更新内容: 增强API响应处理能力，确保所有响应都有统一的格式
 */

import axios from 'axios'
import { handleApiResponse, handleApiError } from './apiResponseHandler'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken, removeToken } from './auth'
import secureTokenManager from './secure-token'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: '', // 移除baseURL，让各个API模块自己指定完整路径
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 优先使用安全Token管理器
    let token = secureTokenManager.getToken()

    // 回退到传统Token获取方式
    if (!token) {
      token = getToken()
    }

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加安全头
    config.headers['X-Requested-With'] = 'XMLHttpRequest'
    config.headers['X-CSRF-Token'] = secureTokenManager.generateCSRFToken()

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response
    }
    
    // 使用API响应处理工具处理响应
    return handleApiResponse(response.data)
  },
  error => {
    console.error('响应拦截器错误:', error)

    // 特殊处理skfirefly.cn相关的网络错误
    if (error.message && error.message.includes('skfirefly.cn')) {
      console.warn('检测到skfirefly.cn相关网络错误，跳过错误提示')
      return Promise.reject({
        success: false,
        data: null,
        message: '外部服务暂时不可用',
        error_code: 'EXTERNAL_SERVICE_UNAVAILABLE'
      })
    }

    // 使用API错误处理工具处理错误
    const errorResponse = handleApiError(error)

    // 特殊状态码处理
    if (error.response) {
      const { status } = error.response

      if (status === 401) {
        // 未授权，清除所有token并跳转到登录页
        removeToken()
        secureTokenManager.clearToken()
        router.push('/login')
        return Promise.reject(errorResponse)
      } else if (status === 403) {
        // 权限不足，但不跳转页面，只显示错误消息
        ElMessage.warning('权限不足，请联系管理员')
        return Promise.reject(errorResponse)
      } else if (status === 502) {
        // 网关错误，可能是外部服务问题
        ElMessage.warning('外部服务暂时不可用')
        return Promise.reject(errorResponse)
      }
    }

    // 显示错误消息（排除已特殊处理的情况）
    if (error.response?.status !== 403 && error.response?.status !== 502) {
      ElMessage.error(errorResponse.message)
    }

    // 返回标准化的错误响应
    return Promise.reject(errorResponse)
  }
)

// 请求方法封装
export const request = {
  get(url, params = {}, config = {}) {
    return service.get(url, { params, ...config })
  },
  
  post(url, data = {}, config = {}) {
    return service.post(url, data, config)
  },
  
  put(url, data = {}, config = {}) {
    return service.put(url, data, config)
  },
  
  patch(url, data = {}, config = {}) {
    return service.patch(url, data, config)
  },
  
  delete(url, config = {}) {
    return service.delete(url, config)
  },
  
  upload(url, formData, config = {}) {
    return service.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  },
  
  download(url, params = {}, filename = '') {
    return service.get(url, {
      params,
      responseType: 'blob'
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  }
}

export default service
