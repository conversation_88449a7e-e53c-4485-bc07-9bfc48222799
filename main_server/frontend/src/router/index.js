/**
 * OmniLink 路由配置
 * 版本: 1.0
 * 创建日期: 2024-12-19
 */

import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { DualRouteGuard, DualPermissionChecker } from '@/utils/dual_permission'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Register.vue'),
    meta: {
      title: '用户注册',
      requiresAuth: false
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',  // 🔧 修复：简化重定向逻辑
    meta: {
      requiresAuth: true  // 🔧 修复：恢复认证要求
    },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '工作台',
          icon: 'House',
          requiresAuth: true
        }
      },
      {
        path: '/applications',
        name: 'Applications',
        component: () => import('@/views/Applications/index.vue'),
        meta: {
          title: '处理事项',
          icon: 'Document',
          requiresAuth: true,
          permissions: ['application.view']
        }
      },
      {
        path: '/applications/create',
        name: 'ApplicationCreate',
        component: () => import('@/views/Applications/Create.vue'),
        meta: {
          title: '创建申请',
          requiresAuth: true,
          permissions: ['application.submit']
        }
      },
      {
        path: '/applications/:id',
        name: 'ApplicationDetail',
        component: () => import('@/views/Applications/Detail.vue'),
        meta: {
          title: '申请详情',
          requiresAuth: true,
          permissions: ['application.view']
        }
      },
      {
        path: '/org-users',
        name: 'OrgUsers',
        component: () => import('@/views/OrgUsers/index.vue'),
        meta: {
          title: '组织与用户管理',
          icon: 'OfficeBuilding',
          requiresAuth: true,
          permissions: ['user.view', 'org.view']
        }
      },
      {
        path: '/user-registration',
        name: 'UserRegistration',
        component: () => import('@/views/UserRegistration/index.vue'),
        meta: {
          title: '新用户审核',
          icon: 'UserFilled',
          requiresAuth: true,
          permissions: ['user.approve']
        }
      },
      {
        path: '/users',
        name: 'Users',
        component: () => import('@/views/Users/<USER>'),
        meta: {
          title: '用户管理（旧版）',
          icon: 'User',
          requiresAuth: true,
          permissions: ['user.view'],
          hidden: true // 隐藏在导航中，但保留路由
        }
      },
      {
        path: '/organizations',
        name: 'Organizations',
        component: () => import('@/views/Organizations/index.vue'),
        meta: {
          title: '组织管理（旧版）',
          icon: 'OfficeBuilding',
          requiresAuth: true,
          permissions: ['org.view'],
          hidden: true // 隐藏在导航中，但保留路由
        }
      },
      {
        path: '/devices',
        name: 'Devices',
        component: () => import('@/views/Devices/index.vue'),
        meta: {
          title: '从服务器管理中心',
          icon: 'Monitor',
          requiresAuth: true,
          permissions: ['device.view']
        }
      },
      {
        path: '/slave-servers',
        name: 'SlaveServers',
        component: () => import('@/views/SlaveServers/index.vue'),
        meta: {
          title: '从服务器管理',
          icon: 'Connection',
          requiresAuth: true,
          permissions: ['slave.view']
        }
      },
      {
        path: '/slave-servers/:id',
        name: 'SlaveServerDetail',
        component: () => import('@/views/SlaveServers/Detail.vue'),
        meta: {
          title: '从服务器详情',
          requiresAuth: true,
          permissions: ['slave.view']
        }
      },
      {
        path: '/device-groups',
        name: 'DeviceGroups',
        component: () => import('@/views/DeviceGroups/index.vue'),
        meta: {
          title: '设备分组管理',
          icon: 'Collection',
          requiresAuth: true,
          permissions: ['device.group']
        }
      },
      {
        path: '/device-groups/:id',
        name: 'DeviceGroupDetail',
        component: () => import('@/views/DeviceGroups/detail.vue'),
        meta: {
          title: '设备分组详情',
          requiresAuth: true,
          permissions: ['device.group']
        }
      },
      {
        path: '/device-center',
        name: 'DeviceCenter',
        component: () => import('@/views/DeviceCenter/index.vue'),
        meta: {
          title: '设备管理中心',
          icon: 'Monitor',
          requiresAuth: true
        }
      },
      {
        path: '/device-center/slave-server/:id',
        name: 'SlaveServerDetail',
        component: () => import('@/views/DeviceCenter/components/SlaveServerDetail.vue'),
        meta: {
          title: '从服务器详情',
          requiresAuth: true,
          hideInMenu: true  // 不在菜单中显示
        }
      },
      {
        path: '/device-center/slave-server/:id/virtualhere',
        name: 'VirtualHereManager',
        component: () => import('@/views/DeviceCenter/components/VirtualHereManager.vue'),
        meta: {
          title: 'VirtualHere程序管理',
          requiresAuth: true,
          hideInMenu: true  // 不在菜单中显示
        }
      },
      {
        path: '/device-center/device-group/:groupId',
        name: 'DeviceCenterGroupDetail',
        component: () => import('@/views/DeviceCenter/components/DeviceGroupDetail.vue'),
        meta: {
          title: '设备分组详情',
          requiresAuth: true,
          hideInMenu: true  // 不在菜单中显示
        }
      },
      {
        path: '/device-center/device/:id',
        name: 'DeviceDetail',
        component: () => import('@/views/DeviceCenter/components/DeviceDetail.vue'),
        meta: {
          title: '设备详情',
          requiresAuth: true,
          hideInMenu: true  // 不在菜单中显示
        }
      },
      // 保留原有路由作为重定向（暂时注释掉）
      // {
      //   path: '/devices',
      //   redirect: '/device-center'
      // },
      // {
      //   path: '/slave-servers',
      //   redirect: '/device-center'
      // },
      // {
      //   path: '/device-groups',
      //   redirect: '/device-center'
      // },
      // {
      //   path: '/permission-assignment',
      //   redirect: '/device-center'
      // },
      {
        path: '/role-management',
        name: 'RoleManagement',
        component: () => import('@/views/RoleManagement/index.vue'),
        meta: {
          title: '角色管理',
          icon: 'UserFilled',
          requiresAuth: true,
          requiresSuperuser: true
        }
      },
      {
        path: '/system-settings',
        name: 'SystemSettings',
        component: () => import('@/views/SystemSettings.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting',
          requiresAuth: true,
          requiresSuperuser: true,
          permissions: ['system.manage']
        }
      },
      {
        path: '/profile',
        name: 'Profile',
        component: () => import('@/views/Profile.vue'),
        meta: {
          title: '个人资料',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/Error/403.vue'),
    meta: {
      title: '权限不足'
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/Error/404.vue'),
    meta: {
      title: '页面不存在'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()

  const userStore = useUserStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - OmniLink 全联通系统`
  }

  // 🔧 修复：登录页面和注册页面直接允许访问
  if (to.path === '/login' || to.path === '/register' || to.path === '/403' || to.path === '/404') {
    next()
    return
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 尝试从本地存储恢复登录状态
      await userStore.checkLoginStatus()

      if (!userStore.isLoggedIn) {
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }

    // 确保用户信息完整后再进行权限检查
    const user = userStore.userInfo
    if (!user || !user.username || user.username === undefined) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 全域管理员和特殊用户跳过路由权限检查
    if (DualPermissionChecker.isGlobalAdmin(user) || user.is_special_user || user.username === 'firefly') {
      // 特殊用户直接允许访问
    } else {
      // 检查路由访问权限
      const hasRoutePermission = DualRouteGuard.checkRoutePermission(to.path, user)

      if (!hasRoutePermission) {
        // 根据用户权限级别重定向到合适的页面
        const defaultPath = DualRouteGuard.getDefaultRedirectPath(user)

        if (to.path !== defaultPath) {
          next(defaultPath)
          return
        } else {
          // 如果默认页面也无权限访问，跳转到403
          next('/403')
          return
        }
      }

      // 检查特定权限要求
      if (to.meta.permissions && to.meta.permissions.length > 0) {
        // 全域管理员、超级管理员和特殊用户跳过权限检查
        if (DualPermissionChecker.isGlobalAdmin(user) || DualPermissionChecker.isSuperAdmin(user) || user.is_special_user || user.username === 'firefly') {
          // 管理员用户直接允许访问
        } else {
          // 🔧 修复：使用every()确保拥有所有必需权限，而不是some()
          const hasPermission = to.meta.permissions.every(permission => {
            switch (permission) {
              case 'user-management':
                return DualPermissionChecker.canAccessUserManagement(user)
              case 'role-management':
                return DualPermissionChecker.canAccessRoleManagement(user)
              case 'admin-management':
                return DualPermissionChecker.canAccessRoleManagement(user)
              case 'device-management':
                return DualPermissionChecker.canAccessDeviceManagement(user)
              case 'system-config':
                return DualPermissionChecker.canAccessSystemConfig(user)
              case 'audit-logs':
                return DualPermissionChecker.canAccessAuditLogs(user)
              default:
                return userStore.hasPermission && userStore.hasPermission(permission)
            }
          })

          if (!hasPermission) {
            next('/403')
            return
          }
        }
      }
    }
  }
  
  // 已登录用户访问登录页，重定向到默认页面
  if (to.path === '/login' && userStore.isLoggedIn) {
    const user = userStore.userInfo
    const defaultPath = DualRouteGuard.getDefaultRedirectPath(user)
    next(defaultPath)
    return
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
