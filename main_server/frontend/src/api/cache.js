/**
 * 缓存管理API
 */
import request from '@/utils/request'

/**
 * 获取缓存信息
 */
export function getCacheInfo() {
  return request({
    url: '/api/v1/system/cache-info',
    method: 'get'
  })
}

/**
 * 清除指定类型缓存
 * @param {string} cacheType - 缓存类型
 */
export function clearCache(cacheType) {
  return request({
    url: '/api/v1/system/clear-cache',
    method: 'post',
    params: {
      cache_type: cacheType
    }
  })
}

/**
 * 清除所有缓存
 */
export function clearAllCache() {
  return clearCache('all')
}

/**
 * 清除Web页面缓存
 */
export function clearWebCache() {
  try {
    // 清除localStorage
    localStorage.clear()
    
    // 清除sessionStorage
    sessionStorage.clear()
    
    // 清除浏览器缓存（通过重新加载）
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          caches.delete(name)
        })
      })
    }
    
    return Promise.resolve({
      success: true,
      message: 'Web页面缓存清除成功'
    })
  } catch (error) {
    return Promise.reject({
      success: false,
      message: `Web页面缓存清除失败: ${error.message}`
    })
  }
}

/**
 * 强制刷新页面（清除页面缓存）
 */
export function forceRefreshPage() {
  // 添加时间戳参数强制刷新
  const url = new URL(window.location)
  url.searchParams.set('_refresh', Date.now())
  window.location.href = url.toString()
}
