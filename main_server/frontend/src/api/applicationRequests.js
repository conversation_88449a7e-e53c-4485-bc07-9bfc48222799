/**
 * 申请处理系统 API
 * 版本: 1.0
 * 创建日期: 2024-12-19
 */

import request from '@/utils/request'

export const applicationRequestApi = {
  // 获取申请类型列表
  getApplicationTypes() {
    return request({
      url: '/api/v1/application-requests/types',
      method: 'get'
    })
  },

  // 获取申请列表
  getApplicationRequests(params = {}) {
    return request({
      url: '/api/v1/application-requests/',
      method: 'get',
      params
    })
  },

  // 获取申请详情
  getApplicationRequest(id) {
    return request({
      url: `/api/v1/application-requests/${id}`,
      method: 'get'
    })
  },

  // 创建申请
  createApplicationRequest(data) {
    return request({
      url: '/api/v1/application-requests/',
      method: 'post',
      data
    })
  },

  // 处理申请
  processApplicationRequest(id, data) {
    return request({
      url: `/api/v1/application-requests/${id}/process`,
      method: 'post',
      data
    })
  },

  // 获取申请统计
  getApplicationStats() {
    return request({
      url: '/api/v1/application-requests/stats/summary',
      method: 'get'
    })
  },

  // 转交申请
  forwardApplicationRequest(id, data) {
    return request({
      url: `/api/v1/application-requests/${id}/forward`,
      method: 'post',
      data
    })
  },

  // 获取可用管理员列表
  getAvailableManagers(params = {}) {
    return request({
      url: '/api/v1/application-requests/available-managers',
      method: 'get',
      params
    })
  }
}

// 为了兼容新的组件，添加别名
export const applicationRequestsAPI = {
  getApplications: applicationRequestApi.getApplicationRequests,
  getApplication: applicationRequestApi.getApplicationRequest,
  createApplication: applicationRequestApi.createApplicationRequest,
  processApplication: applicationRequestApi.processApplicationRequest,
  forwardApplication: applicationRequestApi.forwardApplicationRequest,
  getAvailableManagers: applicationRequestApi.getAvailableManagers,
  getTypes: applicationRequestApi.getApplicationTypes,
  getStats: applicationRequestApi.getApplicationStats
}

export default applicationRequestApi
