/**
 * 数据压缩管理API
 * 版本: 1.0
 * 创建日期: 2025-01-15
 * 描述: 提供数据压缩配置管理、状态监控和统计分析的API接口
 */

import request from '@/utils/request'

// ==================== 压缩配置管理 ====================

/**
 * 获取压缩配置列表
 */
export function getCompressionConfigs() {
  return request({
    url: '/api/v1/compression/configs',
    method: 'get'
  })
}

/**
 * 创建压缩配置
 * @param {Object} data 配置数据
 */
export function createCompressionConfig(data) {
  return request({
    url: '/api/v1/compression/configs',
    method: 'post',
    data
  })
}

/**
 * 更新压缩配置
 * @param {number} id 配置ID
 * @param {Object} data 配置数据
 */
export function updateCompressionConfig(id, data) {
  return request({
    url: `/api/v1/compression/configs/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除压缩配置
 * @param {number} id 配置ID
 */
export function deleteCompressionConfig(id) {
  return request({
    url: `/api/v1/compression/configs/${id}`,
    method: 'delete'
  })
}

/**
 * 激活压缩配置
 * @param {number} id 配置ID
 */
export function activateCompressionConfig(id) {
  return request({
    url: `/api/v1/compression/configs/${id}/activate`,
    method: 'post'
  })
}

/**
 * 停用压缩配置
 * @param {number} id 配置ID
 */
export function deactivateCompressionConfig(id) {
  return request({
    url: `/api/v1/compression/configs/${id}/deactivate`,
    method: 'post'
  })
}

// ==================== 实时监控 ====================

/**
 * 获取压缩系统状态
 */
export function getCompressionStatus() {
  return request({
    url: '/api/v1/compression/status',
    method: 'get'
  })
}

/**
 * 获取压缩统计数据
 * @param {number} hours 时间范围（小时）
 */
export function getCompressionStats(hours = 24) {
  return request({
    url: '/api/v1/compression/stats',
    method: 'get',
    params: { hours }
  })
}

// ==================== 系统管理 ====================

/**
 * 获取可用压缩算法
 */
export function getAvailableAlgorithms() {
  return request({
    url: '/api/v1/compression/algorithms',
    method: 'get'
  })
}

// ==================== 高级功能 ====================

/**
 * 获取智能推荐配置
 * @param {Object} params 推荐参数
 */
export function getSmartRecommendations(params = {}) {
  return request({
    url: '/api/v1/compression/recommendations',
    method: 'get',
    params
  })
}

/**
 * 获取压缩效果预览
 * @param {Object} config 配置参数
 */
export function getCompressionPreview(config) {
  return request({
    url: '/api/v1/compression/preview',
    method: 'post',
    data: config
  })
}

/**
 * 获取网络影响分析
 * @param {Object} config 配置参数
 */
export function getNetworkImpactAnalysis(config) {
  return request({
    url: '/api/v1/compression/network-impact',
    method: 'post',
    data: config
  })
}

/**
 * 批量导入压缩配置
 * @param {Array} configs 配置列表
 */
export function batchImportCompressionConfigs(configs) {
  return request({
    url: '/api/v1/compression/configs/batch-import',
    method: 'post',
    data: { configs }
  })
}

/**
 * 获取配置版本历史
 * @param {number} configId 配置ID
 */
export function getConfigVersionHistory(configId) {
  return request({
    url: `/api/v1/compression/configs/${configId}/versions`,
    method: 'get'
  })
}

/**
 * 回滚配置到指定版本
 * @param {number} configId 配置ID
 * @param {number} versionId 版本ID
 */
export function rollbackConfigVersion(configId, versionId) {
  return request({
    url: `/api/v1/compression/configs/${configId}/rollback/${versionId}`,
    method: 'post'
  })
}

/**
 * 获取压缩性能基准测试结果
 */
export function getPerformanceBenchmark() {
  return request({
    url: '/api/v1/compression/benchmark',
    method: 'get'
  })
}



// ==================== 网络穿透工具集成 ====================

/**
 * 获取网络穿透工具压缩状态
 * @param {string} tunnelType 穿透工具类型
 */
export function getTunnelCompressionStatus(tunnelType) {
  return request({
    url: `/api/v1/compression/tunnel/${tunnelType}/status`,
    method: 'get'
  })
}

/**
 * 更新网络穿透工具压缩配置
 * @param {string} tunnelType 穿透工具类型
 * @param {Object} config 压缩配置
 */
export function updateTunnelCompressionConfig(tunnelType, config) {
  return request({
    url: `/api/v1/compression/tunnel/${tunnelType}/config`,
    method: 'put',
    data: config
  })
}

/**
 * 获取网络穿透工具压缩统计
 * @param {string} tunnelType 穿透工具类型
 * @param {number} hours 时间范围（小时）
 */
export function getTunnelCompressionStats(tunnelType, hours = 24) {
  return request({
    url: `/api/v1/compression/tunnel/${tunnelType}/stats`,
    method: 'get',
    params: { hours }
  })
}

// ==================== 客户端同步 ====================

/**
 * 获取客户端同步配置
 */
export function getClientSyncConfig() {
  return request({
    url: '/api/v1/compression/client-sync/config',
    method: 'get'
  })
}

/**
 * 更新客户端同步配置
 * @param {Object} config 同步配置
 */
export function updateClientSyncConfig(config) {
  return request({
    url: '/api/v1/compression/client-sync/config',
    method: 'put',
    data: config
  })
}

/**
 * 获取客户端连接状态
 */
export function getClientConnectionStatus() {
  return request({
    url: '/api/v1/compression/client-sync/status',
    method: 'get'
  })
}

/**
 * 强制同步配置到所有客户端
 */
export function forceSyncToAllClients() {
  return request({
    url: '/api/v1/compression/client-sync/force-sync',
    method: 'post'
  })
}

// ==================== 监控和告警 ====================

/**
 * 获取压缩系统健康状态
 */
export function getSystemHealth() {
  return request({
    url: '/api/v1/compression/health',
    method: 'get'
  })
}

/**
 * 获取告警规则列表
 */
export function getAlertRules() {
  return request({
    url: '/api/v1/compression/alerts/rules',
    method: 'get'
  })
}

/**
 * 创建告警规则
 * @param {Object} rule 告警规则
 */
export function createAlertRule(rule) {
  return request({
    url: '/api/v1/compression/alerts/rules',
    method: 'post',
    data: rule
  })
}

/**
 * 更新告警规则
 * @param {number} ruleId 规则ID
 * @param {Object} rule 告警规则
 */
export function updateAlertRule(ruleId, rule) {
  return request({
    url: `/api/v1/compression/alerts/rules/${ruleId}`,
    method: 'put',
    data: rule
  })
}

/**
 * 删除告警规则
 * @param {number} ruleId 规则ID
 */
export function deleteAlertRule(ruleId) {
  return request({
    url: `/api/v1/compression/alerts/rules/${ruleId}`,
    method: 'delete'
  })
}

/**
 * 获取告警历史
 * @param {Object} params 查询参数
 */
export function getAlertHistory(params = {}) {
  return request({
    url: '/api/v1/compression/alerts/history',
    method: 'get',
    params
  })
}
