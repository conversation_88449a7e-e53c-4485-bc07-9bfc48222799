/**
 * 用户管理API
 * 版本: 1.0
 * 创建日期: 2025-07-07
 */

import { request } from '@/utils/request'

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.page_size - 每页数量
 * @param {string} params.search - 搜索关键词
 * @param {string} params.role_filter - 角色筛选
 * @param {string} params.status_filter - 状态筛选
 * @param {number} params.organization_id - 组织筛选
 * @param {string} params.filter_mode - 筛选模式
 */
export function getUsers(params = {}) {
  return request.get('/api/v2/users/', { params })
}

/**
 * 获取用户列表（V1版本，兼容性保留）
 */
export function getUsersV1(params = {}) {
  return request.get('/api/v1/users/', { params })
}

/**
 * 获取用户统计信息
 */
export function getUserCount() {
  return request.get('/api/v1/users/count')
}

/**
 * 获取组织架构和用户信息
 */
export function getOrganizationsWithUsers() {
  return request.get('/api/v1/users/organizations')
}

/**
 * 获取用户详情
 * @param {number} userId - 用户ID
 */
export function getUserById(userId) {
  return request.get(`/api/v2/users/${userId}`)
}

/**
 * 创建用户
 * @param {Object} userData - 用户数据
 */
export function createUser(userData) {
  return request.post('/api/v2/users/', userData)
}

/**
 * 更新用户信息
 * @param {number} userId - 用户ID
 * @param {Object} userData - 用户数据
 */
export function updateUser(userId, userData) {
  return request.put(`/api/v2/users/${userId}`, userData)
}

/**
 * 删除用户 (V1版本，已弃用)
 * @param {number} userId - 用户ID
 */
export function deleteUserV1(userId) {
  return request.delete(`/users/${userId}`)
}

/**
 * 批量删除用户
 * @param {Array} userIds - 用户ID数组
 */
export function batchDeleteUsers(userIds) {
  return request.post('/users/batch-delete', { user_ids: userIds })
}

/**
 * 重置用户密码
 * @param {number} userId - 用户ID
 * @param {string} newPassword - 新密码
 */
export function resetUserPassword(userId, newPassword) {
  return request.post(`/users/${userId}/reset-password`, { new_password: newPassword })
}

/**
 * 启用/禁用用户
 * @param {number} userId - 用户ID
 * @param {boolean} isActive - 是否启用
 */
export function toggleUserStatus(userId, isActive) {
  return request.patch(`/users/${userId}/status`, { is_active: isActive })
}

/**
 * 分配用户角色
 * @param {number} userId - 用户ID
 * @param {string} roleName - 角色名称
 */
export function assignUserRole(userId, roleName) {
  return request.post(`/users/${userId}/assign-role`, { role_name: roleName })
}

/**
 * 获取用户的权限列表
 * @param {number} userId - 用户ID
 */
export function getUserPermissions(userId) {
  return request.get(`/users/${userId}/permissions`)
}

/**
 * 导出用户数据
 * @param {Object} params - 导出参数
 */
export function exportUsers(params = {}) {
  return request.get('/users/export', params, {
    responseType: 'blob'
  })
}

/**
 * 导入用户数据
 * @param {FormData} formData - 包含文件的表单数据
 */
export function importUsers(formData) {
  return request.upload('/users/import', formData)
}

/**
 * 删除用户
 * @param {number} userId - 用户ID
 */
export function deleteUser(userId) {
  return request.delete(`/api/v2/users/${userId}`)
}
