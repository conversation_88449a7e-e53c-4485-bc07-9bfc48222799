/**
 * 设备管理相关API
 * 版本: 1.0
 * 创建日期: 2025-07-26
 */

import { request } from '@/utils/request'

/**
 * 获取设备列表
 */
export function getDeviceList(params = {}) {
  return request.get('/api/v1/devices/', { params })
}

/**
 * 获取设备详情
 */
export function getDeviceDetail(deviceId) {
  return request.get(`/api/v1/devices/${deviceId}`)
}

/**
 * 创建设备
 */
export function createDevice(data) {
  return request.post('/api/v1/devices/', data)
}

/**
 * 更新设备信息
 */
export function updateDevice(deviceId, data) {
  return request.put(`/api/v1/devices/${deviceId}`, data)
}

/**
 * 删除设备
 */
export function deleteDevice(deviceId) {
  return request.delete(`/api/v1/devices/${deviceId}`)
}

/**
 * 获取设备统计信息
 */
export function getDeviceStats() {
  return request.get('/api/v1/devices/stats')
}

/**
 * 连接设备
 */
export function connectDevice(deviceId) {
  return request.post(`/api/v1/devices/${deviceId}/connect`)
}

/**
 * 断开设备连接
 */
export function disconnectDevice(deviceId) {
  return request.post(`/api/v1/devices/${deviceId}/disconnect`)
}

/**
 * 获取设备连接状态
 */
export function getDeviceStatus(deviceId) {
  return request.get(`/api/v1/devices/${deviceId}/status`)
}

/**
 * 批量操作设备
 */
export function batchOperateDevices(action, deviceIds) {
  return request.post('/api/v1/devices/batch', {
    action,
    device_ids: deviceIds
  })
}

/**
 * 搜索设备
 */
export function searchDevices(query, filters = {}) {
  return request.get('/api/v1/devices/search', {
    params: {
      q: query,
      ...filters
    }
  })
}

/**
 * 获取设备类型列表
 */
export function getDeviceTypes() {
  return request.get('/api/v1/devices/types')
}

/**
 * 获取设备厂商列表
 */
export function getDeviceVendors() {
  return request.get('/api/v1/devices/vendors')
}

/**
 * 获取用户可访问的设备列表
 */
export function getUserDevices() {
  return request.get('/api/v1/user/devices')
}

/**
 * 获取设备使用历史
 */
export function getDeviceHistory(deviceId, params = {}) {
  return request.get(`/api/v1/devices/${deviceId}/history`, { params })
}

/**
 * 获取设备分组中的设备
 */
export function getGroupDevices(groupId) {
  return request.get(`/api/v1/device-groups/${groupId}/devices`)
}

/**
 * 添加设备到分组
 */
export function addDevicesToGroup(groupId, deviceIds) {
  return request.post(`/api/v1/device-groups/${groupId}/devices`, {
    device_ids: deviceIds
  })
}

/**
 * 从分组中移除设备
 */
export function removeDevicesFromGroup(groupId, deviceIds) {
  return request.delete(`/api/v1/device-groups/${groupId}/devices`, {
    data: { device_ids: deviceIds }
  })
}
