/**
 * 系统设置API
 * 版本: 1.0
 * 创建日期: 2025-01-13
 * 描述: 系统设置和内网穿透管理API接口
 */

import request from '@/utils/request'

// ==================== 系统设置管理 ====================

/**
 * 获取系统设置列表
 * @param {string} category - 设置分类
 * @returns {Promise}
 */
export function getSystemSettings(category = null) {
  const params = category ? { category } : {}
  return request({
    url: '/api/v1/system/settings',
    method: 'get',
    params
  })
}

/**
 * 获取单个系统设置
 * @param {string} key - 设置键
 * @returns {Promise}
 */
export function getSystemSetting(key) {
  return request({
    url: `/api/v1/system/settings/${key}`,
    method: 'get'
  })
}

/**
 * 创建系统设置
 * @param {Object} data - 设置数据
 * @returns {Promise}
 */
export function createSystemSetting(data) {
  return request({
    url: '/api/v1/system/settings',
    method: 'post',
    data
  })
}

/**
 * 更新系统设置
 * @param {string} key - 设置键
 * @param {Object} data - 设置数据
 * @returns {Promise}
 */
export function updateSystemSetting(key, data) {
  return request({
    url: `/api/v1/system/settings/${key}`,
    method: 'put',
    data
  })
}

/**
 * 删除系统设置
 * @param {string} key - 设置键
 * @returns {Promise}
 */
export function deleteSystemSetting(key) {
  return request({
    url: `/api/v1/system/settings/${key}`,
    method: 'delete'
  })
}

// ==================== 内网穿透管理 ====================

/**
 * 获取内网穿透配置列表
 * @returns {Promise}
 */
export function getTunnelConfigs() {
  return request({
    url: '/api/v1/system/tunnels',
    method: 'get'
  })
}

/**
 * 创建内网穿透配置
 * @param {Object} data - 配置数据
 * @returns {Promise}
 */
export function createTunnelConfig(data) {
  return request({
    url: '/api/v1/system/tunnels',
    method: 'post',
    data
  })
}

/**
 * 更新内网穿透配置
 * @param {number} configId - 配置ID
 * @param {Object} data - 配置数据
 * @returns {Promise}
 */
export function updateTunnelConfig(configId, data) {
  return request({
    url: `/api/v1/system/tunnels/${configId}`,
    method: 'put',
    data
  })
}

/**
 * 删除内网穿透配置
 * @param {number} configId - 配置ID
 * @returns {Promise}
 */
export function deleteTunnelConfig(configId) {
  return request({
    url: `/api/v1/system/tunnels/${configId}`,
    method: 'delete'
  })
}

/**
 * 启动内网穿透
 * @param {number} configId - 配置ID
 * @returns {Promise}
 */
export function startTunnel(configId) {
  return request({
    url: `/api/v1/system/tunnels/${configId}/start`,
    method: 'post'
  })
}

/**
 * 停止内网穿透
 * @param {number} configId - 配置ID
 * @returns {Promise}
 */
export function stopTunnel(configId) {
  return request({
    url: `/api/v1/system/tunnels/${configId}/stop`,
    method: 'post'
  })
}

/**
 * 获取所有内网穿透状态
 * @returns {Promise}
 */
export function getTunnelsStatus() {
  return request({
    url: '/api/v1/system/tunnels/status',
    method: 'get'
  })
}

/**
 * 升级内网穿透工具
 * @param {number} configId - 配置ID
 * @returns {Promise}
 */
export function upgradeTunnel(configId) {
  return request({
    url: `/api/v1/system/tunnels/${configId}/upgrade`,
    method: 'post'
  })
}

/**
 * 强制禁用/启用内网穿透配置
 * @param {number} configId - 配置ID
 * @param {boolean} forceDisabled - 是否强制禁用
 * @returns {Promise}
 */
export function toggleForceDisabled(configId, forceDisabled) {
  return request({
    url: `/api/v1/system/tunnels/${configId}/force-disable`,
    method: 'post',
    data: { force_disabled: forceDisabled }
  })
}

// ==================== 快捷设置方法 ====================

/**
 * 更新客户端下载链接
 * @param {string} url - 下载链接
 * @returns {Promise}
 */
export function updateClientDownloadUrl(url) {
  return updateSystemSetting('client_download_url', { value: url })
}

/**
 * 获取客户端下载链接（公开接口）
 * @returns {Promise}
 */
export function getClientDownloadUrl() {
  return request({
    url: '/api/v1/system/client-download-url',
    method: 'get'
  })
}

/**
 * 获取客户端下载链接（管理员接口）
 * @returns {Promise}
 */
export function getClientDownloadUrlAdmin() {
  return getSystemSetting('client_download_url')
}

/**
 * 切换内网穿透功能
 * @param {boolean} enabled - 是否启用
 * @returns {Promise}
 */
export function toggleTunnelFeature(enabled) {
  return updateSystemSetting('tunnel_enabled', { value: enabled.toString() })
}

/**
 * 获取系统基本信息
 * @returns {Promise}
 */
export function getSystemInfo() {
  return Promise.all([
    getSystemSetting('system_name'),
    getSystemSetting('system_version'),
    getSystemSetting('tunnel_enabled')
  ]).then(([name, version, tunnelEnabled]) => {
    return {
      name: name.data?.value || 'OmniLink 全联通系统',
      version: version.data?.value || '2.0.0',
      tunnelEnabled: tunnelEnabled.data?.value === 'true'
    }
  })
}
