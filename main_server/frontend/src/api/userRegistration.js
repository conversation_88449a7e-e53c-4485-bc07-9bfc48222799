/**
 * 新用户注册审核API接口
 * 实施严格的实时数据获取规范，禁用缓存
 */
import request from '@/utils/request'

/**
 * 获取待审核用户列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.page_size - 每页数量
 * @param {string} params.search - 搜索关键词
 * @param {string} params.status_filter - 状态筛选
 */
export function getPendingUsers(params = {}) {
  return request({
    url: '/api/v1/user-registration/pending-users',
    method: 'get',
    params,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
}

/**
 * 获取用户注册统计信息
 */
export function getRegistrationStats() {
  return request({
    url: '/api/v1/user-registration/stats',
    method: 'get',
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
}

/**
 * 审核通过用户
 * @param {number} userId - 用户ID
 * @param {Object} data - 审核数据
 * @param {string} data.reason - 审核理由
 * @param {number} data.target_organization_id - 目标组织ID
 * @param {string} data.target_role_name - 目标角色名称
 */
export function approveUser(userId, data = {}) {
  return request({
    url: `/api/v1/user-registration/approve/${userId}`,
    method: 'post',
    data,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
}

/**
 * 拒绝用户申请
 * @param {number} userId - 用户ID
 * @param {Object} data - 拒绝数据
 * @param {string} data.reason - 拒绝理由
 */
export function rejectUser(userId, data) {
  return request({
    url: `/api/v1/user-registration/reject/${userId}`,
    method: 'post',
    data,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
}

/**
 * 批量审核通过用户
 * @param {Object} data - 批量审核数据
 * @param {Array} data.user_ids - 用户ID列表
 * @param {string} data.reason - 审核理由
 */
export function batchApproveUsers(data) {
  return request({
    url: '/api/v1/user-registration/batch-approve',
    method: 'post',
    data,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
}

/**
 * 批量拒绝用户申请
 * @param {Object} data - 批量拒绝数据
 * @param {Array} data.user_ids - 用户ID列表
 * @param {string} data.reason - 拒绝理由
 */
export function batchRejectUsers(data) {
  return request({
    url: '/api/v1/user-registration/batch-reject',
    method: 'post',
    data,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
}

/**
 * 获取用户详细信息
 * @param {number} userId - 用户ID
 */
export function getUserDetail(userId) {
  return request({
    url: `/api/v1/user-registration/user/${userId}`,
    method: 'get',
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
}

// 为了兼容现有组件，添加别名
export const userRegistrationAPI = {
  getPendingUsers,
  getRegistrationStats,
  approveUser,
  rejectUser,
  batchApproveUsers,
  batchRejectUsers,
  getUserDetail
}

export default {
  getPendingUsers,
  getRegistrationStats,
  approveUser,
  rejectUser,
  batchApproveUsers,
  batchRejectUsers,
  getUserDetail
}
