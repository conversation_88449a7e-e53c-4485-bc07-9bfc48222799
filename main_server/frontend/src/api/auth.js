/**
 * 认证相关API
 * 版本: 1.0
 * 创建日期: 2024-12-19
 */

import { request } from '@/utils/request'

/**
 * 用户登录
 */
export function login(data) {
  return request.post('/api/v1/auth/login', data)
}

/**
 * 用户登出
 */
export function logout() {
  return request.post('/api/v1/auth/logout')
}

/**
 * 获取当前用户信息
 */
export function getCurrentUser() {
  return request.get('/api/v1/auth/me')
}

/**
 * 修改密码
 */
export function changePassword(data) {
  return request.post('/auth/change-password', data)
}

/**
 * 刷新token
 */
export function refreshToken() {
  return request.post('/auth/refresh')
}

/**
 * 用户注册
 */
export function registerUser(data) {
  return request.post('/api/v1/auth/register', data)
}

/**
 * 检查用户名可用性
 */
export function checkUsernameAvailability(username) {
  return request.post('/api/v1/auth/check-username', { username })
}

/**
 * 检查手机号可用性
 */
export function checkPhoneAvailability(phone) {
  return request.post('/api/v1/auth/check-phone', { phone })
}

/**
 * 检查邮箱可用性
 */
export function checkEmailAvailability(email) {
  return request.post('/api/v1/auth/check-email', { email })
}
