/**
 * 从服务器管理相关API
 * 版本: 1.0
 * 创建日期: 2025-07-26
 */

import { request } from '@/utils/request'

/**
 * 获取从服务器列表
 */
export function getSlaveServerList() {
  return request.get('/api/v1/slave/list')
}

/**
 * 注册从服务器
 */
export function registerSlaveServer(data) {
  return request.post('/api/v1/slave/register', data)
}

/**
 * 创建从服务器（registerSlaveServer的别名）
 */
export function createSlaveServer(data) {
  return registerSlaveServer(data)
}

/**
 * 发送心跳到从服务器
 */
export function sendHeartbeat(data) {
  return request.post('/api/v1/slave/heartbeat', data)
}

/**
 * 获取指定从服务器的设备列表
 */
export function getSlaveServerDevices(serverId) {
  return request.get(`/api/v1/slave/${serverId}/devices`)
}

/**
 * 控制从服务器
 */
export function controlSlaveServer(serverId, action, parameters = {}) {
  return request.post(`/api/v1/slave/${serverId}/control`, {
    action,
    parameters
  })
}

/**
 * 获取从服务器详细信息
 */
export function getSlaveServerDetail(serverId) {
  return request.get(`/api/v1/slave/${serverId}`)
}

/**
 * 获取从服务器详细系统信息（通过主服务器代理）
 */
export function getSlaveServerSystemDetail(serverId) {
  return request.get(`/api/v1/slave/${serverId}/system-detail`)
}

/**
 * 更新从服务器信息
 */
export function updateSlaveServer(serverId, data) {
  return request.put(`/api/v1/slave/${serverId}`, data)
}

/**
 * 更新从服务器名称
 */
export function updateSlaveServerName(serverId, data) {
  return request.put(`/api/v1/slave/${serverId}/name`, data)
}

/**
 * 删除从服务器
 */
export function deleteSlaveServer(serverId) {
  return request.delete(`/api/v1/slave/${serverId}`)
}

/**
 * 获取从服务器状态统计
 */
export function getSlaveServerStats() {
  return request.get('/api/v1/slave/servers/stats')
}

/**
 * 重启从服务器VirtualHere服务
 */
export function restartVirtualHere(serverId) {
  return controlSlaveServer(serverId, 'restart_vh')
}

/**
 * 停止从服务器VirtualHere服务
 */
export function stopVirtualHere(serverId) {
  return controlSlaveServer(serverId, 'stop_vh')
}

/**
 * 启动从服务器VirtualHere服务
 */
export function startVirtualHere(serverId) {
  return controlSlaveServer(serverId, 'start_vh')
}

/**
 * 获取从服务器日志
 */
export function getSlaveServerLogs(serverId, params = {}) {
  return request.get(`/api/v1/slave/${serverId}/logs`, { params })
}

/**
 * 测试从服务器连接
 */
export function testSlaveServerConnection(serverId) {
  return request.post(`/api/v1/slave/${serverId}/test`)
}
