/**
 * 权限分配相关API
 */
import api from './index'

/**
 * 获取组织架构树（权限分配专用，使用V1稳定接口）
 */
export function getOrganizationTree() {
  return api.get('/api/v1/users/organizations', {
    params: {
      include_users: true
    }
  })
}

/**
 * 获取组织下的用户列表
 * @param {number} organizationId - 组织ID
 * @param {boolean} includeChildren - 是否包含子组织用户
 */
export function getOrganizationUsers(organizationId, includeChildren = false) {
  return api.get('/api/v2/users/', {
    params: {
      organization_id: organizationId,
      include_children: includeChildren,
      page_size: 1000
    }
  })
}

/**
 * 获取设备分组列表
 */
export function getDeviceGroups() {
  return api.get('/api/v1/device-groups/')
}

/**
 * 创建设备分组
 */
export function createDeviceGroup(data) {
  return api.post('/api/v1/device-groups/', data)
}

/**
 * 更新设备分组
 */
export function updateDeviceGroup(id, data) {
  return api.put(`/api/v1/device-groups/${id}`, data)
}

/**
 * 删除设备分组
 */
export function deleteDeviceGroup(id) {
  return api.delete(`/api/v1/device-groups/${id}`)
}

/**
 * 获取设备列表
 */
export function getDevices() {
  return api.get('/api/v1/devices/')
}

/**
 * 获取用户的设备权限
 * @param {number} userId - 用户ID
 */
export function getUserDevicePermissions(userId) {
  return api.get(`/api/v1/users/${userId}/device-permissions`)
}

/**
 * 获取组织的设备权限
 * @param {number} organizationId - 组织ID
 */
export function getOrganizationDevicePermissions(organizationId) {
  return api.get(`/api/v1/organizations/${organizationId}/device-permissions`)
}

/**
 * 分配设备权限
 * @param {Object} assignmentData - 权限分配数据
 * @param {string} assignmentData.target_type - 目标类型：'user' 或 'organization'
 * @param {number} assignmentData.target_id - 目标ID
 * @param {string} assignmentData.device_type - 设备类型：'groups' 或 'devices'
 * @param {Array} assignmentData.device_ids - 设备或分组ID列表
 * @param {string} assignmentData.permission_type - 权限类型：'view', 'use', 'manage'
 * @param {string} assignmentData.expires_at - 过期时间（可选）
 */
export function assignDevicePermissions(assignmentData) {
  const { target_type, target_id, device_type, device_ids, permission_type, expires_at } = assignmentData
  
  if (target_type === 'user') {
    // 为用户分配权限
    if (device_type === 'groups') {
      // 分配设备分组权限
      return Promise.all(
        device_ids.map(groupId => 
          api.post(`/api/v1/device-groups/${groupId}/permissions`, {
            user_ids: [target_id],
            permission_type,
            expires_at
          })
        )
      )
    } else {
      // 分配独立设备权限
      return api.post('/api/v1/device-permissions/assign', {
        user_id: target_id,
        device_ids,
        permission_type,
        expires_at
      })
    }
  } else {
    // 为组织分配权限
    if (device_type === 'groups') {
      // 分配设备分组权限给组织
      return Promise.all(
        device_ids.map(groupId => 
          api.post(`/api/v1/device-groups/${groupId}/organization-permissions`, {
            organization_id: target_id,
            permission_type,
            expires_at
          })
        )
      )
    } else {
      // 分配独立设备权限给组织
      return api.post('/api/v1/device-permissions/assign-organization', {
        organization_id: target_id,
        device_ids,
        permission_type,
        expires_at
      })
    }
  }
}

/**
 * 撤销设备权限
 * @param {Object} revokeData - 撤销权限数据
 */
export function revokeDevicePermissions(revokeData) {
  const { target_type, target_id, device_type, device_ids } = revokeData
  
  if (target_type === 'user') {
    if (device_type === 'groups') {
      // 撤销用户的设备分组权限
      return Promise.all(
        device_ids.map(groupId => 
          api.delete(`/api/v1/device-groups/${groupId}/permissions/${target_id}`)
        )
      )
    } else {
      // 撤销用户的独立设备权限
      return api.delete('/api/v1/device-permissions/revoke', {
        data: {
          user_id: target_id,
          device_ids
        }
      })
    }
  } else {
    if (device_type === 'groups') {
      // 撤销组织的设备分组权限
      return Promise.all(
        device_ids.map(groupId => 
          api.delete(`/api/v1/device-groups/${groupId}/organization-permissions/${target_id}`)
        )
      )
    } else {
      // 撤销组织的独立设备权限
      return api.delete('/api/v1/device-permissions/revoke-organization', {
        data: {
          organization_id: target_id,
          device_ids
        }
      })
    }
  }
}

/**
 * 批量分配权限
 * @param {Array} assignments - 权限分配列表
 */
export function batchAssignPermissions(assignments) {
  return api.post('/api/v1/device-permissions/batch-assign', {
    assignments
  })
}

/**
 * 获取权限分配历史
 * @param {Object} params - 查询参数
 */
export function getPermissionHistory(params = {}) {
  return api.get('/api/v1/device-permissions/history', { params })
}

/**
 * 检查权限冲突
 * @param {Object} assignmentData - 权限分配数据
 */
export function checkPermissionConflicts(assignmentData) {
  return api.post('/api/v1/device-permissions/check-conflicts', assignmentData)
}

/**
 * 获取权限继承关系
 * @param {number} userId - 用户ID
 */
export function getPermissionInheritance(userId) {
  return api.get(`/api/v1/users/${userId}/permission-inheritance`)
}

/**
 * 预览权限分配结果
 * @param {Object} assignmentData - 权限分配数据
 */
export function previewPermissionAssignment(assignmentData) {
  return api.post('/api/v1/device-permissions/preview', assignmentData)
}

/**
 * 获取设备使用统计
 * @param {Object} params - 查询参数
 */
export function getDeviceUsageStats(params = {}) {
  return api.get('/api/v1/device-permissions/usage-stats', { params })
}

/**
 * 导出权限分配报告
 * @param {Object} params - 导出参数
 */
export function exportPermissionReport(params = {}) {
  return api.get('/api/v1/device-permissions/export', { 
    params,
    responseType: 'blob'
  })
}

/**
 * 获取权限模板
 */
export function getPermissionTemplates() {
  return api.get('/api/v1/device-permissions/templates')
}

/**
 * 应用权限模板
 * @param {Object} templateData - 模板数据
 */
export function applyPermissionTemplate(templateData) {
  return api.post('/api/v1/device-permissions/apply-template', templateData)
}

/**
 * 获取权限审批流程
 * @param {number} requestId - 申请ID
 */
export function getPermissionApprovalFlow(requestId) {
  return api.get(`/api/v1/device-permissions/approval-flow/${requestId}`)
}

/**
 * 提交权限申请
 * @param {Object} requestData - 申请数据
 */
export function submitPermissionRequest(requestData) {
  return api.post('/api/v1/device-permissions/request', requestData)
}

/**
 * 审批权限申请
 * @param {number} requestId - 申请ID
 * @param {Object} approvalData - 审批数据
 */
export function approvePermissionRequest(requestId, approvalData) {
  return api.post(`/api/v1/device-permissions/approve/${requestId}`, approvalData)
}

/**
 * 获取权限分配建议
 * @param {Object} params - 查询参数
 */
export function getPermissionSuggestions(params) {
  return api.get('/api/v1/device-permissions/suggestions', { params })
}

/**
 * 验证权限分配规则
 * @param {Object} ruleData - 规则数据
 */
export function validatePermissionRules(ruleData) {
  return api.post('/api/v1/device-permissions/validate-rules', ruleData)
}
