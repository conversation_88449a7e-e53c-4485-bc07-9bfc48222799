/**
 * 角色管理API
 * 版本: 1.0
 * 创建日期: 2024-12-19
 */

import { request } from '@/utils/request'

/**
 * 获取所有角色列表 - 使用权限控制API
 */
export function getRoles() {
  return request.get('/api/v2/roles/')
}

/**
 * 获取角色详情 - 使用权限控制API
 * @param {number} roleId - 角色ID
 */
export function getRoleById(roleId) {
  return request.get(`/api/v2/roles/${roleId}`)
}

/**
 * 创建新角色
 * @param {Object} roleData - 角色数据
 * @param {string} roleData.name - 角色名称
 * @param {string} roleData.description - 角色描述
 * @param {Array} roleData.permissions - 权限列表
 * @param {number} roleData.level_scope - 权限层级范围
 * @param {boolean} roleData.can_manage_users - 是否可以管理用户
 * @param {boolean} roleData.can_manage_devices - 是否可以管理设备
 * @param {boolean} roleData.can_view_reports - 是否可以查看报告
 */
export function createRole(roleData) {
  return request.post('/api/v2/roles/', roleData)
}

/**
 * 更新角色信息
 * @param {number} roleId - 角色ID
 * @param {Object} roleData - 角色数据
 */
export function updateRole(roleId, roleData) {
  return request.put(`/api/v2/roles/${roleId}`, roleData)
}

/**
 * 删除角色
 * @param {number} roleId - 角色ID
 */
export function deleteRole(roleId) {
  return request.delete(`/api/v2/roles/${roleId}`)
}

/**
 * 获取所有可用权限列表
 */
export function getAvailablePermissions() {
  return request.get('/api/v2/roles/permissions')
}

/**
 * 获取角色的用户列表
 * @param {number} roleId - 角色ID
 */
export function getRoleUsers(roleId) {
  return request.get(`/api/v2/roles/${roleId}/users`)
}

/**
 * 为用户分配角色
 * @param {number} userId - 用户ID
 * @param {string} roleName - 角色名称
 */
export function assignUserRole(userId, roleName) {
  return request.post(`/api/v2/roles/assign`, {
    user_id: userId,
    role_name: roleName
  })
}

/**
 * 移除用户角色
 * @param {number} userId - 用户ID
 * @param {string} roleName - 角色名称
 */
export function removeUserRole(userId, roleName) {
  return request.post(`/api/v2/roles/remove`, {
    user_id: userId,
    role_name: roleName
  })
}

/**
 * 批量分配角色
 * @param {Array} assignments - 分配列表
 * @param {number} assignments[].user_id - 用户ID
 * @param {string} assignments[].role_name - 角色名称
 */
export function batchAssignRoles(assignments) {
  return request.post('/api/v2/roles/batch-assign', {
    assignments
  })
}

/**
 * 获取权限描述信息
 */
export function getPermissionDescriptions() {
  return {
    'user.view': '查看用户',
    'user.create': '创建用户',
    'user.edit': '编辑用户',
    'user.delete': '删除用户',
    'user.manage_role': '管理用户角色',
    'org.view': '查看组织',
    'org.create': '创建组织',
    'org.edit': '编辑组织',
    'org.delete': '删除组织',
    'device.view': '查看设备',
    'device.manage': '管理设备',
    'device.connect': '连接设备',
    'application.view': '查看申请',
    'application.submit': '提交申请',
    'application.process': '处理申请',
    'profile.view': '查看个人资料',
    'profile.edit': '编辑个人资料',
    'system.config': '系统配置',
    'system.monitor': '系统监控',
    'system.audit': '审计日志'
  }
}

/**
 * 获取所有可用权限及其分组
 */
export function getPermissionGroups() {
  return {
    '用户管理': [
      'user.view',
      'user.create', 
      'user.edit',
      'user.delete',
      'user.manage_role'
    ],
    '组织管理': [
      'org.view',
      'org.create',
      'org.edit', 
      'org.delete'
    ],
    '设备管理': [
      'device.view',
      'device.manage',
      'device.connect'
    ],
    '申请处理': [
      'application.view',
      'application.submit',
      'application.process'
    ],
    '个人资料': [
      'profile.view',
      'profile.edit'
    ],
    '系统管理': [
      'system.config',
      'system.monitor',
      'system.audit'
    ]
  }
}

/**
 * 验证角色权限配置
 * @param {Array} permissions - 权限列表
 */
export function validateRolePermissions(permissions) {
  const validPermissions = Object.keys(getPermissionDescriptions())
  const invalidPermissions = permissions.filter(perm => !validPermissions.includes(perm))
  
  return {
    isValid: invalidPermissions.length === 0,
    invalidPermissions
  }
}

/**
 * 获取角色权限建议
 * @param {string} roleName - 角色名称
 */
export function getRolePermissionSuggestions(roleName) {
  const suggestions = {
    '管理员': [
      'user.view', 'user.create', 'user.edit', 'user.manage_role',
      'org.view', 'device.view', 'device.manage',
      'application.view', 'application.process'
    ],
    '普通用户': [
      'profile.view', 'profile.edit',
      'device.view', 'application.submit'
    ],
    '设备管理员': [
      'profile.view', 'profile.edit',
      'device.view', 'device.manage', 'device.connect'
    ],
    '审核员': [
      'profile.view', 'profile.edit',
      'application.view', 'application.process'
    ]
  }
  
  return suggestions[roleName] || []
}
