/**
 * Vue权限指令
 * 用于在模板中进行细粒度的权限控制
 */

import { DualPermissionChecker } from '@/utils/dual_permission'
import { useUserStore } from '@/stores/user'

/**
 * v-permission 指令
 * 用法：
 * v-permission="'user-management'"  // 检查是否可以访问用户管理
 * v-permission="['admin', 'super-admin']"  // 检查是否为管理员或超级管理员
 * v-permission="{role: 'admin', level: 2}"  // 复杂权限检查
 */
export const permission = {
  mounted(el, binding) {
    checkPermission(el, binding)
  },
  updated(el, binding) {
    checkPermission(el, binding)
  }
}

/**
 * v-permission-role 指令
 * 用法：
 * v-permission-role="'超级管理员'"  // 检查是否为超级管理员
 * v-permission-role="['超级管理员', '全域管理员']"  // 检查是否为指定角色之一
 */
export const permissionRole = {
  mounted(el, binding) {
    checkRolePermission(el, binding)
  },
  updated(el, binding) {
    checkRolePermission(el, binding)
  }
}

/**
 * v-permission-level 指令
 * 用法：
 * v-permission-level="0"  // 检查权限级别是否 <= 0
 * v-permission-level="{max: 2, min: 0}"  // 检查权限级别是否在范围内
 */
export const permissionLevel = {
  mounted(el, binding) {
    checkLevelPermission(el, binding)
  },
  updated(el, binding) {
    checkLevelPermission(el, binding)
  }
}

/**
 * v-permission-feature 指令
 * 用法：
 * v-permission-feature="'can-manage-users'"  // 检查是否有特定功能权限
 */
export const permissionFeature = {
  mounted(el, binding) {
    checkFeaturePermission(el, binding)
  },
  updated(el, binding) {
    checkFeaturePermission(el, binding)
  }
}

/**
 * 检查基本权限
 */
function checkPermission(el, binding) {
  const userStore = useUserStore()
  const user = userStore.userInfo
  
  if (!user) {
    hideElement(el)
    return
  }

  const permission = binding.value
  let hasPermission = false

  if (typeof permission === 'string') {
    // 字符串权限检查
    hasPermission = checkStringPermission(permission, user)
  } else if (Array.isArray(permission)) {
    // 数组权限检查（满足其中一个即可）
    hasPermission = permission.some(p => checkStringPermission(p, user))
  } else if (typeof permission === 'object') {
    // 对象权限检查
    hasPermission = checkObjectPermission(permission, user)
  }

  if (!hasPermission) {
    hideElement(el)
  } else {
    showElement(el)
  }
}

/**
 * 检查角色权限
 */
function checkRolePermission(el, binding) {
  const userStore = useUserStore()
  const user = userStore.userInfo
  
  if (!user) {
    hideElement(el)
    return
  }

  const requiredRoles = Array.isArray(binding.value) ? binding.value : [binding.value]
  const hasPermission = requiredRoles.includes(user.role_name)

  if (!hasPermission) {
    hideElement(el)
  } else {
    showElement(el)
  }
}

/**
 * 检查权限级别
 */
function checkLevelPermission(el, binding) {
  const userStore = useUserStore()
  const user = userStore.userInfo
  
  if (!user) {
    hideElement(el)
    return
  }

  const userLevel = DualPermissionChecker.getPermissionLevel(user)
  const requirement = binding.value
  let hasPermission = false

  if (typeof requirement === 'number') {
    // 简单级别检查：用户级别 <= 要求级别
    hasPermission = userLevel <= requirement
  } else if (typeof requirement === 'object') {
    // 复杂级别检查
    const { max, min } = requirement
    if (max !== undefined && min !== undefined) {
      hasPermission = userLevel >= min && userLevel <= max
    } else if (max !== undefined) {
      hasPermission = userLevel <= max
    } else if (min !== undefined) {
      hasPermission = userLevel >= min
    }
  }

  if (!hasPermission) {
    hideElement(el)
  } else {
    showElement(el)
  }
}

/**
 * 检查功能权限
 */
function checkFeaturePermission(el, binding) {
  const userStore = useUserStore()
  const user = userStore.userInfo
  
  if (!user) {
    hideElement(el)
    return
  }

  const feature = binding.value
  let hasPermission = false

  switch (feature) {
    case 'can-manage-users':
      hasPermission = PermissionChecker.canAccessUserManagement(user)
      break
    case 'can-manage-roles':
      hasPermission = PermissionChecker.canAccessRoleManagement(user)
      break
    case 'can-manage-admins':
      hasPermission = PermissionChecker.canAccessAdminManagement(user)
      break
    case 'can-manage-devices':
      hasPermission = PermissionChecker.canAccessDeviceManagement(user)
      break
    case 'can-manage-applications':
      hasPermission = PermissionChecker.canAccessApplicationManagement(user)
      break
    case 'can-access-system-config':
      hasPermission = PermissionChecker.canAccessSystemConfig(user)
      break
    case 'can-access-audit-logs':
      hasPermission = PermissionChecker.canAccessAuditLogs(user)
      break
    case 'can-access-workspace':
      hasPermission = PermissionChecker.canAccessWorkspace(user)
      break
    case 'is-global-admin':
      hasPermission = PermissionChecker.isGlobalAdmin(user)
      break
    case 'is-super-admin':
      hasPermission = PermissionChecker.isSuperAdmin(user)
      break
    case 'is-admin':
      hasPermission = PermissionChecker.isAdmin(user)
      break
    case 'is-regular-user':
      hasPermission = PermissionChecker.isRegularUser(user)
      break
    case 'is-new-user':
      hasPermission = PermissionChecker.isNewUser(user)
      break
    default:
      hasPermission = false
  }

  if (!hasPermission) {
    hideElement(el)
  } else {
    showElement(el)
  }
}

/**
 * 检查字符串权限
 */
function checkStringPermission(permission, user) {
  switch (permission) {
    case 'user-management':
      return PermissionChecker.canAccessUserManagement(user)
    case 'role-management':
      return PermissionChecker.canAccessRoleManagement(user)
    case 'admin-management':
      return PermissionChecker.canAccessAdminManagement(user)
    case 'device-management':
      return PermissionChecker.canAccessDeviceManagement(user)
    case 'application-management':
      return PermissionChecker.canAccessApplicationManagement(user)
    case 'system-config':
      return PermissionChecker.canAccessSystemConfig(user)
    case 'audit-logs':
      return PermissionChecker.canAccessAuditLogs(user)
    case 'workspace':
      return PermissionChecker.canAccessWorkspace(user)
    case 'global-admin':
      return PermissionChecker.isGlobalAdmin(user)
    case 'super-admin':
      return PermissionChecker.isSuperAdmin(user)
    case 'admin':
      return PermissionChecker.isAdmin(user)
    case 'regular-user':
      return PermissionChecker.isRegularUser(user)
    case 'new-user':
      return PermissionChecker.isNewUser(user)
    default:
      return false
  }
}

/**
 * 检查对象权限
 */
function checkObjectPermission(permission, user) {
  const { role, level, feature, any, all } = permission

  // 检查角色
  if (role) {
    const roles = Array.isArray(role) ? role : [role]
    if (!roles.includes(user.role_name)) {
      return false
    }
  }

  // 检查权限级别
  if (level !== undefined) {
    const userLevel = DualPermissionChecker.getPermissionLevel(user)
    if (userLevel > level) {
      return false
    }
  }

  // 检查功能权限
  if (feature) {
    const features = Array.isArray(feature) ? feature : [feature]
    const hasFeature = features.some(f => checkStringPermission(f, user))
    if (!hasFeature) {
      return false
    }
  }

  // 检查any条件（满足其中一个即可）
  if (any && Array.isArray(any)) {
    const hasAny = any.some(condition => checkObjectPermission(condition, user))
    if (!hasAny) {
      return false
    }
  }

  // 检查all条件（必须全部满足）
  if (all && Array.isArray(all)) {
    const hasAll = all.every(condition => checkObjectPermission(condition, user))
    if (!hasAll) {
      return false
    }
  }

  return true
}

/**
 * 隐藏元素
 */
function hideElement(el) {
  el.style.display = 'none'
  el.setAttribute('data-permission-hidden', 'true')
}

/**
 * 显示元素
 */
function showElement(el) {
  if (el.getAttribute('data-permission-hidden') === 'true') {
    el.style.display = ''
    el.removeAttribute('data-permission-hidden')
  }
}

// 导出所有指令
export default {
  permission,
  permissionRole,
  permissionLevel,
  permissionFeature
}
