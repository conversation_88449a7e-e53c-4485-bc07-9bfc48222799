/**
 * 用户状态管理
 * 版本: 1.0
 * 创建日期: 2024-12-19
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, logout, getCurrentUser } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(getToken())
  const userInfo = ref(null)
  const permissions = ref([])
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const isAdmin = computed(() => {
    if (!userInfo.value) return false
    // 🔧 修复：包含超级管理员和管理员角色
    return userInfo.value.is_superuser ||
           userInfo.value.role_name === '管理员' ||
           userInfo.value.role_name === '超级管理员' ||
           userInfo.value.role_name === '全域管理员'
  })
  const userName = computed(() => userInfo.value?.full_name || userInfo.value?.username || '')
  const userEmail = computed(() => userInfo.value?.email || '')
  const organizationId = computed(() => userInfo.value?.organization_id)
  const user = computed(() => userInfo.value)

  // 新增：角色相关计算属性
  const userRoles = computed(() => userInfo.value?.roles || [])
  const isNewUser = computed(() => {
    return userRoles.value.some(role => role.name === '新用户')
  })
  const isSuperAdmin = computed(() => {
    return userInfo.value?.is_superuser || userRoles.value.some(role => role.name === '超级管理员')
  })
  const isSuperuser = computed(() => {
    return userInfo.value?.is_superuser || false
  })
  const isManager = computed(() => {
    return userRoles.value.some(role => role.name === '管理员')
  })
  const isRegularUser = computed(() => {
    return userRoles.value.some(role => role.name === '普通用户')
  })

  // 特殊功能权限检查
  const canAccessRoleManagement = computed(() => {
    if (!userInfo.value) return false
    // 只有全域管理员(权限级别0)和超级管理员(权限级别1)可以访问角色管理
    return userInfo.value.is_special_user ||
           userInfo.value.role_name === '全域管理员' ||
           userInfo.value.role_name === '超级管理员' ||
           (userInfo.value.permission_level !== undefined && userInfo.value.permission_level <= 1)
  })

  const canAccessDeviceManagement = computed(() => {
    if (!userInfo.value) return false
    // 🔧 修复：允许普通用户访问设备管理中心（但功能受限）
    return getPermissionLevel.value <= 3 // 普通用户及以上都可以访问设备管理中心
  })

  const canAccessWorkspace = computed(() => {
    if (!userInfo.value) return false
    // 除了新用户(权限级别4)外，其他所有权限均可查看工作台
    return userInfo.value.permission_level !== 4
  })

  const canAccessSystemSettings = computed(() => {
    if (!userInfo.value) return false
    // 只有全域管理员(权限级别0)和超级管理员(权限级别1)可以访问系统设置
    return userInfo.value.is_special_user ||
           userInfo.value.role_name === '全域管理员' ||
           userInfo.value.role_name === '超级管理员' ||
           (userInfo.value.permission_level !== undefined && userInfo.value.permission_level <= 1)
  })

  // 新增：设备管理中心权限等级检查方法
  const getPermissionLevel = computed(() => {
    if (!userInfo.value) return 4 // 默认为新用户级别
    if (userInfo.value.is_special_user) return 0 // 特殊用户为全域管理员
    return userInfo.value.permission_level !== undefined ? userInfo.value.permission_level : 4
  })

  const isGlobalAdmin = computed(() => getPermissionLevel.value === 0)
  const isSuperAdminLevel = computed(() => getPermissionLevel.value === 1)
  const isRegularAdminLevel = computed(() => getPermissionLevel.value === 2)
  const isRegularUserLevel = computed(() => getPermissionLevel.value === 3)
  const isNewUserLevel = computed(() => getPermissionLevel.value === 4)

  // 设备管理中心标签页权限
  const canAccessDeviceManagementTab = computed(() => getPermissionLevel.value <= 1) // 全域管理员和超级管理员
  const canAccessSlaveServerTab = computed(() => getPermissionLevel.value <= 1) // 全域管理员和超级管理员
  const canAccessDeviceGroupTab = computed(() => getPermissionLevel.value <= 1) // 全域管理员和超级管理员
  const canAccessPermissionAssignmentTab = computed(() => getPermissionLevel.value <= 3) // 普通用户及以上都可以访问权限分配

  // 统计卡片显示权限
  const canViewSlaveServerStats = computed(() => getPermissionLevel.value <= 1) // 全域管理员和超级管理员
  const canViewUSBDeviceStats = computed(() => getPermissionLevel.value <= 1) // 全域管理员和超级管理员
  const canViewDeviceGroupStats = computed(() => true) // 所有用户可见，但数据范围受权限限制
  const canViewPermissionAssignmentStats = computed(() => getPermissionLevel.value <= 2) // 普通用户不可见

  // 方法
  const setUserInfo = (info) => {
    userInfo.value = info
  }
  
  const setPermissions = (perms) => {
    permissions.value = perms || []
  }
  
  const hasPermission = (permission) => {
    // 新用户没有任何权限，除了查看个人资料
    if (isNewUser.value) {
      return false
    }

    // 🔧 修复：设备管理权限特殊处理 - 允许普通用户访问设备管理中心
    if (permission === 'device.view' || permission === 'device.manage' || permission === 'device.group') {
      if (!userInfo.value) return false
      // 普通用户及以上都可以访问设备管理中心（但功能受限）
      return getPermissionLevel.value <= 3
    }

    // 🔧 新增：从服务器管理权限特殊处理
    if (permission.startsWith('slave.')) {
      if (!userInfo.value) return false

      const permissionType = permission.split('.')[1]

      switch (permissionType) {
        case 'view':
          // 普通用户及以上都可以查看从服务器
          return getPermissionLevel.value <= 3
        case 'create':
        case 'manage':
        case 'control':
          // 管理员及以上可以创建、管理、控制从服务器
          return getPermissionLevel.value <= 2
        case 'delete':
          // 只有超级管理员及以上可以删除从服务器
          return getPermissionLevel.value <= 1
        default:
          return false
      }
    }

    if (isAdmin.value) return true
    return permissions.value.includes(permission)
  }
  
  const hasAnyPermission = (permissionList) => {
    // 新用户没有任何权限
    if (isNewUser.value) return false
    if (isAdmin.value) return true
    return permissionList.some(permission => permissions.value.includes(permission))
  }

  const hasAllPermissions = (permissionList) => {
    // 新用户没有任何权限
    if (isNewUser.value) return false
    if (isAdmin.value) return true
    return permissionList.every(permission => permissions.value.includes(permission))
  }
  
  // 登录
  const userLogin = async (loginData) => {
    try {
      const response = await login(loginData)

      // 处理不同的响应格式
      let loginResponse = response

      // 如果响应被包装在data字段中，提取实际数据
      if (response.success && response.data) {
        loginResponse = response.data
      }

      if (loginResponse.access_token) {
        token.value = loginResponse.access_token
        setToken(loginResponse.access_token)
        setUserInfo(loginResponse.user_info)
        setPermissions(loginResponse.permissions)

        ElMessage.success('登录成功')
        return true
      } else {
        throw new Error('登录响应格式错误')
      }
    } catch (error) {
      console.error('登录失败:', error)
      ElMessage.error(error.message || '登录失败')
      return false
    }
  }
  
  // 登出
  const userLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      token.value = null
      userInfo.value = null
      permissions.value = []
      removeToken()
      
      ElMessage.success('已退出登录')
    }
  }
  
  // 检查登录状态
  const checkLoginStatus = async () => {
    const savedToken = getToken()
    if (!savedToken) {
      return false
    }
    
    try {
      const response = await getCurrentUser()
      if (response) {
        token.value = savedToken
        setUserInfo(response)
        setPermissions(response.permissions)
        return true
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)
      // 清除无效的token
      removeToken()
      token.value = null
      userInfo.value = null
      permissions.value = []
    }
    
    return false
  }
  
  // 更新用户信息
  const updateUserInfo = async () => {
    try {
      const response = await getCurrentUser()
      if (response) {
        setUserInfo(response)
        setPermissions(response.permissions)
        return true
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return false
    }
  }
  
  // 重置状态
  const resetState = () => {
    token.value = null
    userInfo.value = null
    permissions.value = []
    removeToken()
  }

  return {
    // 状态
    token,
    userInfo,
    permissions,

    // 计算属性
    isLoggedIn,
    isAdmin,
    userName,
    userEmail,
    organizationId,
    userRoles,
    isNewUser,
    isSuperAdmin,
    isSuperuser,
    isManager,
    isRegularUser,
    canAccessRoleManagement,
    canAccessDeviceManagement,
    canAccessWorkspace,
    canAccessSystemSettings,

    // 新增：权限等级检查
    getPermissionLevel,
    isGlobalAdmin,
    isSuperAdminLevel,
    isRegularAdminLevel,
    isRegularUserLevel,
    isNewUserLevel,

    // 新增：设备管理中心权限
    canAccessDeviceManagementTab,
    canAccessSlaveServerTab,
    canAccessDeviceGroupTab,
    canAccessPermissionAssignmentTab,
    canViewSlaveServerStats,
    canViewUSBDeviceStats,
    canViewDeviceGroupStats,
    canViewPermissionAssignmentStats,

    // 方法
    setUserInfo,
    setPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    userLogin,
    userLogout,
    checkLoginStatus,
    updateUserInfo,
    resetState
  }
})
