<template>
  <div class="user-registration-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>新用户注册审核</h2>
        <p class="header-desc">审核和管理新用户注册申请，支持批量操作</p>
      </div>
      <div class="header-right">
        <el-button 
          type="success" 
          :disabled="!hasSelectedItems"
          @click="handleBatchApprove"
        >
          <el-icon><Check /></el-icon>
          批量通过
        </el-button>
        <el-button 
          type="danger" 
          :disabled="!hasSelectedItems"
          @click="handleBatchReject"
        >
          <el-icon><Close /></el-icon>
          批量拒绝
        </el-button>
      </div>
    </div>

    <!-- 筛选和搜索区域 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-bar">
        <div class="filter-left">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索用户姓名、用户名或邮箱..."
            style="width: 300px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select v-model="filterStatus" placeholder="筛选状态" clearable style="width: 150px">
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
          
          <el-date-picker
            v-model="filterDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </div>
        
        <div class="filter-right">
          <el-button @click="resetFilters">重置筛选</el-button>
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 统计信息 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon pending">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.pending }}</div>
            <div class="stat-label">待审核</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon approved">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.approved }}</div>
            <div class="stat-label">已通过</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon rejected">
            <el-icon><Close /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.rejected }}</div>
            <div class="stat-label">已拒绝</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon total">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stats.total }}</div>
            <div class="stat-label">总申请</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 申请列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>注册申请列表</span>
          <div class="header-actions">
            <el-checkbox 
              v-model="selectAll" 
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            >
              全选
            </el-checkbox>
          </div>
        </div>
      </template>

      <div class="table-wrapper">
        <el-table
          :data="filteredRegistrations"
          style="width: 100%;"
          @selection-change="handleSelectionChange"
          table-layout="auto"
        >
        <el-table-column type="selection" width="50" />

        <el-table-column prop="full_name" label="用户姓名" width="120">
          <template #default="scope">
            <div class="user-info">
              <el-avatar :size="28" :src="scope.row.avatar">
                {{ scope.row.full_name.charAt(0) }}
              </el-avatar>
              <span class="user-name">{{ scope.row.full_name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="username" label="用户名" width="140" show-overflow-tooltip />
        <el-table-column prop="email" label="邮箱" width="180" show-overflow-tooltip />
        <el-table-column prop="phone" label="手机号" width="120" />

        <el-table-column prop="requested_organization" label="申请组织" width="100">
          <template #default="scope">
            <el-tag type="info" size="small">
              {{ scope.row.requested_organization }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="requested_roles" label="申请角色" width="90">
          <template #default="scope">
            <el-tag
              v-for="role in scope.row.requested_roles"
              :key="role"
              size="small"
              class="role-tag"
            >
              {{ role }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="申请时间" width="140">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column prop="remark" label="审核备注" min-width="300" show-overflow-tooltip>
          <template #default="scope">
            <div class="remark-content">
              <span v-if="scope.row.remark" class="remark-text">{{ scope.row.remark }}</span>
              <span v-else class="no-remark">暂无备注</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons-container">
              <el-button
                size="small"
                @click="handleViewDetail(scope.row)"
                class="action-btn view-btn"
              >
                查看
              </el-button>

              <template v-if="scope.row.status === 'pending'">
                <el-button
                  size="small"
                  type="success"
                  @click="handleApproveDialog(scope.row)"
                  class="action-btn approve-btn"
                >
                  审核通过
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="handleReject(scope.row)"
                  class="action-btn reject-btn"
                >
                  拒绝
                </el-button>
              </template>

              <template v-else>
                <el-button
                  size="small"
                  type="info"
                  @click="handleViewResult(scope.row)"
                  class="action-btn result-btn"
                >
                  查看结果
                </el-button>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <RegistrationDetailDialog
      v-model="showDetailDialog"
      :registration="selectedRegistration"
      @approve="handleApproveFromDetail"
      @reject="handleRejectFromDetail"
    />

    <!-- 审核对话框 -->
    <ReviewDialog
      v-model="showReviewDialog"
      :registration="reviewingRegistration"
      :action="reviewAction"
      @confirm="handleReviewConfirm"
    />

    <!-- 审核通过对话框 -->
    <el-dialog
      v-model="approveDialogVisible"
      title="用户审核 - 分配角色和组织"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="currentUser" class="approve-form">
        <div class="user-info">
          <h4>用户信息</h4>
          <p><strong>姓名：</strong>{{ currentUser.full_name }}</p>
          <p><strong>用户名：</strong>{{ currentUser.username }}</p>
          <p><strong>邮箱：</strong>{{ currentUser.email }}</p>
          <p><strong>手机号：</strong>{{ currentUser.phone }}</p>
        </div>

        <el-form :model="approveForm" label-width="120px" class="approve-form-content">
          <el-form-item label="目标组织" required>
            <div class="organization-selector">
              <div class="selected-organization" v-if="selectedOrganization">
                <el-tag type="success" size="large">
                  <el-icon><Check /></el-icon>
                  {{ selectedOrganization.name }}
                </el-tag>
                <el-button
                  type="text"
                  size="small"
                  @click="clearSelection"
                  class="clear-btn"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>

              <div class="tree-container">
                <el-tree
                  ref="organizationTreeRef"
                  :data="organizationTree"
                  :props="treeProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="false"
                  :highlight-current="false"
                  class="organization-tree"
                >
                  <template #default="{ node, data }">
                    <div class="tree-node-content">
                      <div
                        class="node-label"
                        @click="toggleExpand(node)"
                      >
                        <el-icon v-if="data.children && data.children.length > 0" class="expand-icon">
                          <ArrowRight v-if="!node.expanded" />
                          <ArrowDown v-else />
                        </el-icon>
                        <span class="node-text">{{ data.name }}</span>
                      </div>

                      <div class="node-selector" @click="selectOrganization(data)">
                        <el-radio
                          :model-value="approveForm.target_organization_id"
                          :label="data.id"
                          @change="selectOrganization(data)"
                          class="org-radio"
                        >
                          <span></span>
                        </el-radio>
                      </div>
                    </div>
                  </template>
                </el-tree>
              </div>
            </div>
            <div class="form-help">必须为用户分配具体的组织层级</div>
          </el-form-item>

          <el-form-item label="权限级别" required>
            <el-select v-model="approveForm.permission_level" placeholder="请选择权限级别" style="width: 100%">
              <el-option label="管理员 (级别2)" :value="2" />
              <el-option label="普通用户 (级别3)" :value="3" />
            </el-select>
            <div class="form-help">权限级别决定用户在系统中的操作范围</div>
          </el-form-item>

          <el-form-item label="角色名称" required>
            <el-input
              v-model="approveForm.target_role_name"
              placeholder="请输入角色名称"
            />
            <div class="form-help">角色名称应与权限级别匹配</div>
          </el-form-item>

          <el-form-item label="审核备注">
            <el-input
              v-model="approveForm.remark"
              type="textarea"
              :rows="3"
              placeholder="可选：审核备注信息"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="approveDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleApproveConfirm"
            :disabled="!isApproveFormValid"
          >
            确认通过
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check,
  Close,
  Search,
  Refresh,
  Clock,
  User,
  ArrowRight,
  ArrowDown
} from '@element-plus/icons-vue'
import RegistrationDetailDialog from './components/RegistrationDetailDialog.vue'
import ReviewDialog from './components/ReviewDialog.vue'
import { getPendingUsers, getRegistrationStats, approveUser, rejectUser } from '@/api/userRegistration'

// 响应式数据
const searchKeyword = ref('')
const filterStatus = ref('')
const filterDateRange = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)
const selectAll = ref(false)
const selectedItems = ref([])
const showDetailDialog = ref(false)
const showReviewDialog = ref(false)
const selectedRegistration = ref(null)
const reviewingRegistration = ref(null)
const reviewAction = ref('')
const loading = ref(false)
const statsLoading = ref(false)

// 统计数据
const stats = reactive({
  pending: 0,
  approved: 0,
  rejected: 0,
  total: 0
})

// 真实数据 - 从API获取
const registrations = ref([])

// 审核对话框相关
const approveDialogVisible = ref(false)
const currentUser = ref(null)
const organizationTree = ref([])
const organizationTreeRef = ref(null)
const selectedOrganization = ref(null)
const approveForm = reactive({
  target_organization_id: null,
  permission_level: 3, // 默认普通用户
  target_role_name: '普通用户',
  remark: ''
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 计算属性
const filteredRegistrations = computed(() => {
  let filtered = registrations.value
  
  // 状态筛选
  if (filterStatus.value) {
    filtered = filtered.filter(item => item.status === filterStatus.value)
  }
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(item =>
      item.full_name.toLowerCase().includes(keyword) ||
      item.username.toLowerCase().includes(keyword) ||
      item.email.toLowerCase().includes(keyword)
    )
  }
  
  // 日期范围筛选
  if (filterDateRange.value && filterDateRange.value.length === 2) {
    const [startDate, endDate] = filterDateRange.value
    filtered = filtered.filter(item => {
      const itemDate = new Date(item.created_at)
      return itemDate >= startDate && itemDate <= endDate
    })
  }
  
  return filtered
})

const hasSelectedItems = computed(() => {
  return selectedItems.value.length > 0
})

// 审核表单验证
const isApproveFormValid = computed(() => {
  return approveForm.target_organization_id &&
         approveForm.permission_level &&
         approveForm.target_role_name.trim()
})

const isIndeterminate = computed(() => {
  const selectedCount = selectedItems.value.length
  const totalCount = filteredRegistrations.value.length
  return selectedCount > 0 && selectedCount < totalCount
})

// 方法
const loadRegistrations = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchKeyword.value || undefined,
      status_filter: filterStatus.value || undefined
    }

    const response = await getPendingUsers(params)

    // 🔧 修复：处理API中间件包装格式
    let userData = response
    if (response && response.success && response.data) {
      console.log('🔧 loadRegistrations - 检测到API中间件包装格式，提取data字段')
      userData = response.data
    }
    console.log('loadRegistrations - 处理后的用户数据:', userData)

    // 确保userData是数组格式
    const userArray = Array.isArray(userData) ? userData : (userData?.users || [])

    // 映射API数据到前端格式
    registrations.value = userArray.map(user => ({
      ...user,
      status: user.is_active ? 'approved' : 'pending',
      requested_organization: user.organization_name,
      requested_roles: [user.role_name],
      remark: user.approval_remark || null  // 映射审核备注
    }))

    // 更新总数（这里简化处理，实际应该从API返回）
    totalCount.value = registrations.value.length

  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  statsLoading.value = true
  try {
    const response = await getRegistrationStats()

    // 处理API中间件包装格式
    let statsData = response
    if (response && response.success && response.data) {
      console.log('🔧 loadStats - 检测到API中间件包装格式，提取data字段:', response.data)
      statsData = response.data
    }

    console.log('🔧 loadStats - 最终统计数据:', statsData)
    Object.assign(stats, statsData)
  } catch (error) {
    console.error('加载统计信息失败:', error)
    ElMessage.error('加载统计信息失败')
  } finally {
    statsLoading.value = false
  }
}

const updateStats = () => {
  loadStats()
}

// 加载组织树
const loadOrganizationTree = async () => {
  try {
    // 这里需要调用组织API获取组织树
    // 暂时使用模拟数据
    organizationTree.value = [
      {
        id: 1,
        name: '总部',
        children: [
          {
            id: 2,
            name: '技术部',
            children: [
              { id: 3, name: '前端组' },
              { id: 4, name: '后端组' }
            ]
          },
          {
            id: 5,
            name: '销售部',
            children: [
              { id: 6, name: '华北区' },
              { id: 7, name: '华南区' }
            ]
          }
        ]
      }
    ]
  } catch (error) {
    console.error('加载组织树失败:', error)
    ElMessage.error('加载组织树失败')
  }
}

const getStatusType = (status) => {
  const typeMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return textMap[status] || '未知'
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

const handleSearch = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  searchKeyword.value = ''
  filterStatus.value = ''
  filterDateRange.value = []
  currentPage.value = 1
}

const refreshData = async () => {
  await Promise.all([loadRegistrations(), loadStats()])
  ElMessage.success('数据已刷新')
}

const handleSelectAll = (checked) => {
  if (checked) {
    selectedItems.value = filteredRegistrations.value.slice()
  } else {
    selectedItems.value = []
  }
}

const handleSelectionChange = (selection) => {
  selectedItems.value = selection
  selectAll.value = selection.length === filteredRegistrations.value.length
}

const handleViewDetail = (registration) => {
  selectedRegistration.value = registration
  showDetailDialog.value = true
}

// 打开审核对话框
const handleApproveDialog = async (registration) => {
  currentUser.value = registration

  // 重置表单和选择状态
  approveForm.target_organization_id = null
  approveForm.permission_level = 3
  approveForm.target_role_name = '普通用户'
  approveForm.remark = ''
  selectedOrganization.value = null

  // 加载组织树
  await loadOrganizationTree()

  approveDialogVisible.value = true
}

// 组织选择相关方法
const selectOrganization = (organization) => {
  approveForm.target_organization_id = organization.id
  selectedOrganization.value = organization
}

const clearSelection = () => {
  approveForm.target_organization_id = null
  selectedOrganization.value = null
}

const toggleExpand = (node) => {
  if (node.childNodes && node.childNodes.length > 0) {
    node.expanded = !node.expanded
  }
}

// 确认审核通过
const handleApproveConfirm = async () => {
  if (!isApproveFormValid.value) {
    ElMessage.warning('请完整填写必填项')
    return
  }

  try {
    await approveUser(currentUser.value.id, {
      target_organization_id: approveForm.target_organization_id,
      permission_level: approveForm.permission_level,
      target_role_name: approveForm.target_role_name,
      remark: approveForm.remark
    })

    ElMessage.success('用户审核通过')
    approveDialogVisible.value = false
    await loadRegistrations()
    await loadStats()
  } catch (error) {
    ElMessage.error('审核失败')
  }
}

const handleReject = async (registration) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入拒绝理由',
      '拒绝申请',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入拒绝理由'
      }
    )

    await rejectUser(registration.id, { reason })
    ElMessage.success('已拒绝用户申请')
    await refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('拒绝失败:', error)
      ElMessage.error('拒绝失败')
    }
  }
}

const handleBatchApprove = () => {
  const pendingItems = selectedItems.value.filter(item => item.status === 'pending')
  if (pendingItems.length === 0) {
    ElMessage.warning('请选择待审核的申请')
    return
  }
  
  ElMessageBox.confirm(
    `确定要批量通过 ${pendingItems.length} 个申请吗？`,
    '批量审核确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    // 这里调用批量审核API
    pendingItems.forEach(item => {
      item.status = 'approved'
      item.approved_at = new Date().toLocaleString('zh-CN')
      item.approved_by = '系统管理员'
    })
    
    ElMessage.success(`已批量通过 ${pendingItems.length} 个申请`)
    selectedItems.value = []
    selectAll.value = false
    updateStats()
  }).catch(() => {
    // 用户取消
  })
}

const handleBatchReject = () => {
  const pendingItems = selectedItems.value.filter(item => item.status === 'pending')
  if (pendingItems.length === 0) {
    ElMessage.warning('请选择待审核的申请')
    return
  }
  
  ElMessageBox.confirm(
    `确定要批量拒绝 ${pendingItems.length} 个申请吗？`,
    '批量审核确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 这里调用批量审核API
    pendingItems.forEach(item => {
      item.status = 'rejected'
      item.rejected_at = new Date().toLocaleString('zh-CN')
      item.rejected_by = '系统管理员'
    })
    
    ElMessage.success(`已批量拒绝 ${pendingItems.length} 个申请`)
    selectedItems.value = []
    selectAll.value = false
    updateStats()
  }).catch(() => {
    // 用户取消
  })
}

const handleApproveFromDetail = (registration) => {
  handleApprove(registration)
  showDetailDialog.value = false
}

const handleRejectFromDetail = (registration) => {
  handleReject(registration)
  showDetailDialog.value = false
}

const handleReviewConfirm = (reviewData) => {
  const { registration, action, reason } = reviewData
  
  if (action === 'approve') {
    registration.status = 'approved'
    registration.approved_at = new Date().toLocaleString('zh-CN')
    registration.approved_by = '系统管理员'
    registration.approve_reason = reason
    ElMessage.success('申请已通过')
  } else {
    registration.status = 'rejected'
    registration.rejected_at = new Date().toLocaleString('zh-CN')
    registration.rejected_by = '系统管理员'
    registration.reject_reason = reason
    ElMessage.success('申请已拒绝')
  }
  
  updateStats()
}

const handleViewResult = (registration) => {
  selectedRegistration.value = registration
  showDetailDialog.value = true
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 生命周期
onMounted(async () => {
  await Promise.all([loadRegistrations(), loadStats()])
})
</script>

<style scoped>
.user-registration-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.pending {
  background: #e6a23c;
}

.stat-icon.approved {
  background: #67c23a;
}

.stat-icon.rejected {
  background: #f56c6c;
}

.stat-icon.total {
  background: #409eff;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-weight: 500;
}

.role-tag {
  margin-right: 4px;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.approve-form {
  .user-info {
    background: #f5f7fa;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 16px;
    }

    p {
      margin: 8px 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .approve-form-content {
    .form-help {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }
}

/* 备注列样式 - 充分利用空间 */
.remark-content {
  width: 100%;
  min-height: 20px;
  display: flex;
  align-items: center;
}

.remark-text {
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
  word-break: break-word;
  word-wrap: break-word;
  white-space: normal;
  max-width: 100%;
  display: block;
  padding: 4px 0;
}

.no-remark {
  color: #c0c4cc;
  font-style: italic;
  font-size: 13px;
  opacity: 0.7;
}

/* 优化用户信息显示 */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-info .user-name {
  font-size: 13px;
  color: #303133;
}

/* 优化角色标签 */
.role-tag {
  margin-right: 4px;
}

/* 操作按钮容器 - 防止按钮被挤压 */
.action-buttons-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  justify-content: flex-start;
  min-width: 200px;
  padding: 4px 0;
}

/* 操作按钮样式 - 确保按钮不被压缩 */
.action-btn {
  flex-shrink: 0 !important;
  min-width: auto !important;
  white-space: nowrap !important;
  padding: 4px 12px !important;
  font-size: 12px !important;
  height: 28px !important;
  line-height: 1.2 !important;
}

/* 具体按钮样式 */
.view-btn {
  min-width: 48px !important;
}

.approve-btn {
  min-width: 72px !important;
}

.reject-btn {
  min-width: 48px !important;
}

.result-btn {
  min-width: 72px !important;
}

/* 表格容器样式 - 充分利用空间 */
.el-table {
  width: 100% !important;
  table-layout: auto !important;
}

.el-table__body-wrapper {
  overflow-x: visible !important;
}

/* 表格单元格样式 - 优化空间利用 */
.el-table .cell {
  padding: 8px 12px !important;
}

/* 备注列允许换行显示更多内容 */
.el-table td:nth-child(10) .cell {
  white-space: normal !important;
  word-wrap: break-word !important;
  line-height: 1.5 !important;
  max-height: none !important;
}

/* 其他列保持单行显示 */
.el-table td:not(:nth-child(10)) .cell {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 固定列样式优化 */
.el-table__fixed-right {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1) !important;
}

.el-table__fixed-left {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1) !important;
}

/* 表格包装器样式 - 充分利用可用空间 */
.table-wrapper {
  width: 100%;
  overflow: visible;
  min-height: 400px;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  background: #fff;
}

/* 表格容器优化 */
.table-container {
  width: 100%;
  padding: 0;
  margin: 0;
}

/* 组织选择器样式 */
.organization-selector {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: #fff;
  min-height: 200px;
  max-height: 400px;
  overflow: hidden;
}

.selected-organization {
  padding: 12px;
  background: #f0f9ff;
  border-bottom: 1px solid #e1f5fe;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.clear-btn {
  margin-left: 8px;
  color: #909399;
}

.clear-btn:hover {
  color: #f56c6c;
}

.tree-container {
  padding: 8px;
  max-height: 320px;
  overflow-y: auto;
}

.organization-tree {
  background: transparent;
}

.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 4px 0;
  min-height: 32px;
}

.node-label {
  display: flex;
  align-items: center;
  flex: 1;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.node-label:hover {
  background-color: #f5f7fa;
}

.expand-icon {
  margin-right: 6px;
  font-size: 14px;
  color: #909399;
  transition: transform 0.2s;
}

.node-text {
  font-size: 14px;
  color: #303133;
}

.node-selector {
  padding: 4px 8px;
  cursor: pointer;
}

.org-radio {
  margin: 0;
}

.org-radio .el-radio__input {
  margin: 0;
}

.org-radio .el-radio__label {
  display: none;
}

/* 选中状态样式 */
.el-tree-node.is-current > .el-tree-node__content .tree-node-content {
  background-color: #e6f7ff;
  border-radius: 4px;
}

.el-tree-node.is-current > .el-tree-node__content .node-text {
  color: #1890ff;
  font-weight: 500;
}

/* 响应式处理 */
@media (max-width: 1600px) {
  .table-wrapper {
    overflow-x: scroll;
  }

  .action-buttons-container {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }

  .action-btn {
    width: 100% !important;
    margin: 0 !important;
  }
}
</style>
