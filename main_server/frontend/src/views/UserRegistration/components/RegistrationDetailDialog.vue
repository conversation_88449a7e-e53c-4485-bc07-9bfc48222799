<template>
  <el-dialog
    v-model="dialogVisible"
    title="注册申请详情"
    width="700px"
    :close-on-click-modal="false"
  >
    <div v-if="registration" class="registration-detail">
      <!-- 用户基本信息 -->
      <div class="detail-section">
        <h4>申请人信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户姓名">
            <div class="user-info">
              <el-avatar :size="32" :src="registration.avatar">
                {{ registration.full_name.charAt(0) }}
              </el-avatar>
              <span class="user-name">{{ registration.full_name }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="用户名">{{ registration.username }}</el-descriptions-item>
          <el-descriptions-item label="邮箱地址">{{ registration.email }}</el-descriptions-item>
          <el-descriptions-item label="手机号码">{{ registration.phone }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 申请信息 -->
      <div class="detail-section">
        <h4>申请信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请组织">
            <el-tag type="info">{{ registration.requested_organization }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请角色">
            <div class="role-tags">
              <el-tag 
                v-for="role in registration.requested_roles" 
                :key="role" 
                size="small"
                class="role-tag"
              >
                {{ role }}
              </el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间" :span="2">
            {{ formatDate(registration.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="申请理由" :span="2">
            <div class="reason-text">
              {{ registration.reason || '无' }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 审核状态 -->
      <div class="detail-section">
        <h4>审核状态</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusType(registration.status)">
              {{ getStatusText(registration.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="审核时间" v-if="registration.approved_at || registration.rejected_at">
            {{ formatDate(registration.approved_at || registration.rejected_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="审核人员" v-if="registration.approved_by || registration.rejected_by">
            {{ registration.approved_by || registration.rejected_by }}
          </el-descriptions-item>
          <el-descriptions-item 
            label="审核意见" 
            :span="2"
            v-if="registration.approve_reason || registration.reject_reason"
          >
            <div class="review-reason">
              {{ registration.approve_reason || registration.reject_reason }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 附加信息 -->
      <div class="detail-section" v-if="registration.additional_info">
        <h4>附加信息</h4>
        <el-descriptions border>
          <el-descriptions-item label="IP地址" v-if="registration.ip_address">
            {{ registration.ip_address }}
          </el-descriptions-item>
          <el-descriptions-item label="用户代理" v-if="registration.user_agent">
            <div class="user-agent">{{ registration.user_agent }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="推荐人" v-if="registration.referrer">
            {{ registration.referrer }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 操作历史 -->
      <div class="detail-section" v-if="registration.history && registration.history.length > 0">
        <h4>操作历史</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in registration.history"
            :key="index"
            :timestamp="formatDate(item.timestamp)"
            :type="getHistoryType(item.action)"
          >
            <div class="history-item">
              <div class="history-action">{{ item.action }}</div>
              <div class="history-operator">操作人：{{ item.operator }}</div>
              <div class="history-note" v-if="item.note">备注：{{ item.note }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        
        <template v-if="registration && registration.status === 'pending'">
          <el-button type="success" @click="handleApprove">
            <el-icon><Check /></el-icon>
            通过申请
          </el-button>
          <el-button type="danger" @click="handleReject">
            <el-icon><Close /></el-icon>
            拒绝申请
          </el-button>
        </template>
        
        <template v-else-if="registration && registration.status === 'approved'">
          <el-button type="info" @click="handleViewUser">
            <el-icon><User /></el-icon>
            查看用户
          </el-button>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close, User } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  registration: {
    type: Object,
    default: () => null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'approve', 'reject'])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const getStatusType = (status) => {
  const typeMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return textMap[status] || '未知'
}

const getHistoryType = (action) => {
  const typeMap = {
    '提交申请': 'primary',
    '审核通过': 'success',
    '审核拒绝': 'danger',
    '修改申请': 'warning'
  }
  return typeMap[action] || 'info'
}

const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleString('zh-CN')
}

const handleApprove = () => {
  emit('approve', props.registration)
}

const handleReject = () => {
  emit('reject', props.registration)
}

const handleViewUser = () => {
  // 跳转到用户详情页面
  ElMessage.info('跳转到用户详情页面')
}
</script>

<style scoped>
.registration-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-weight: 500;
}

.role-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.role-tag {
  margin-right: 0;
}

.reason-text {
  line-height: 1.6;
  color: #606266;
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.review-reason {
  line-height: 1.6;
  color: #606266;
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #67c23a;
}

.user-agent {
  font-size: 12px;
  color: #909399;
  word-break: break-all;
}

.history-item {
  padding: 8px 0;
}

.history-action {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.history-operator {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.history-note {
  font-size: 12px;
  color: #606266;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
