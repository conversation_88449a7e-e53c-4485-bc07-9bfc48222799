<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="registration" class="review-content">
      <!-- 申请人信息摘要 -->
      <div class="applicant-summary">
        <div class="summary-header">
          <el-avatar :size="40" :src="registration.avatar">
            {{ registration.full_name.charAt(0) }}
          </el-avatar>
          <div class="summary-info">
            <div class="applicant-name">{{ registration.full_name }}</div>
            <div class="applicant-detail">
              {{ registration.username }} | {{ registration.email }}
            </div>
          </div>
        </div>
        
        <div class="summary-request">
          <div class="request-item">
            <span class="request-label">申请组织：</span>
            <el-tag type="info" size="small">{{ registration.requested_organization }}</el-tag>
          </div>
          <div class="request-item">
            <span class="request-label">申请角色：</span>
            <el-tag 
              v-for="role in registration.requested_roles" 
              :key="role" 
              size="small"
              class="role-tag"
            >
              {{ role }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 审核表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="审核结果" prop="result">
          <el-radio-group v-model="formData.result" :disabled="true">
            <el-radio :label="action === 'approve' ? 'approved' : 'rejected'">
              {{ action === 'approve' ? '通过申请' : '拒绝申请' }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item 
          :label="action === 'approve' ? '通过理由' : '拒绝理由'" 
          prop="reason"
        >
          <el-input
            v-model="formData.reason"
            type="textarea"
            :rows="4"
            :placeholder="action === 'approve' ? '请输入通过理由（可选）' : '请输入拒绝理由'"
          />
          <div class="form-hint">
            <el-text size="small" type="info">
              {{ action === 'approve' ? '通过理由将发送给申请人' : '拒绝理由将发送给申请人，请详细说明原因' }}
            </el-text>
          </div>
        </el-form-item>

        <el-form-item 
          label="分配组织" 
          prop="assigned_organization"
          v-if="action === 'approve'"
        >
          <el-select
            v-model="formData.assigned_organization"
            placeholder="选择实际分配的组织"
            style="width: 100%"
          >
            <el-option
              v-for="org in availableOrganizations"
              :key="org.id"
              :label="org.name"
              :value="org.id"
            />
          </el-select>
          <div class="form-hint">
            <el-text size="small" type="info">
              可以分配到与申请不同的组织
            </el-text>
          </div>
        </el-form-item>

        <el-form-item 
          label="分配角色" 
          prop="assigned_roles"
          v-if="action === 'approve'"
        >
          <el-checkbox-group v-model="formData.assigned_roles">
            <el-checkbox 
              v-for="role in availableRoles" 
              :key="role.value" 
              :label="role.value"
              :disabled="role.disabled"
            >
              {{ role.label }}
            </el-checkbox>
          </el-checkbox-group>
          <div class="form-hint">
            <el-text size="small" type="info">
              可以分配与申请不同的角色
            </el-text>
          </div>
        </el-form-item>

        <el-form-item label="发送通知" prop="send_notification">
          <el-switch
            v-model="formData.send_notification"
            active-text="发送邮件通知"
            inactive-text="不发送通知"
          />
          <div class="form-hint">
            <el-text size="small" type="info">
              审核结果将通过邮件通知申请人
            </el-text>
          </div>
        </el-form-item>

        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            placeholder="内部备注（申请人不可见）"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          :type="action === 'approve' ? 'success' : 'danger'" 
          @click="handleConfirm"
          :loading="submitting"
        >
          <el-icon>
            <Check v-if="action === 'approve'" />
            <Close v-else />
          </el-icon>
          {{ action === 'approve' ? '确认通过' : '确认拒绝' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  registration: {
    type: Object,
    default: () => null
  },
  action: {
    type: String,
    default: 'approve' // 'approve' | 'reject'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'confirm'])

// 响应式数据
const formRef = ref(null)
const submitting = ref(false)

const formData = reactive({
  result: '',
  reason: '',
  assigned_organization: null,
  assigned_roles: [],
  send_notification: true,
  notes: ''
})

// 可用组织列表（模拟数据）
const availableOrganizations = ref([
  { id: 1, name: '集团总部' },
  { id: 2, name: '华北大区' },
  { id: 3, name: '北京分公司' },
  { id: 4, name: '技术部' },
  { id: 5, name: '销售部' }
])

// 可用角色列表
const availableRoles = ref([
  { label: '普通用户', value: '普通用户', disabled: false },
  { label: '管理员', value: '管理员', disabled: false },
  { label: '超级管理员', value: '超级管理员', disabled: true } // 假设当前用户无权分配超级管理员
])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  return props.action === 'approve' ? '审核通过' : '审核拒绝'
})

// 表单验证规则
const formRules = reactive({
  reason: [
    {
      validator: (rule, value, callback) => {
        if (props.action === 'reject' && !value.trim()) {
          callback(new Error('拒绝申请时必须填写拒绝理由'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  assigned_organization: [
    {
      validator: (rule, value, callback) => {
        if (props.action === 'approve' && !value) {
          callback(new Error('请选择分配的组织'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  assigned_roles: [
    {
      validator: (rule, value, callback) => {
        if (props.action === 'approve' && (!value || value.length === 0)) {
          callback(new Error('请选择至少一个角色'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
})

// 方法
const resetForm = () => {
  Object.assign(formData, {
    result: props.action === 'approve' ? 'approved' : 'rejected',
    reason: '',
    assigned_organization: null,
    assigned_roles: [],
    send_notification: true,
    notes: ''
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const loadDefaultData = () => {
  if (props.registration && props.action === 'approve') {
    // 预填充申请的组织和角色
    const requestedOrg = availableOrganizations.value.find(
      org => org.name === props.registration.requested_organization
    )
    if (requestedOrg) {
      formData.assigned_organization = requestedOrg.id
    }
    
    formData.assigned_roles = [...props.registration.requested_roles]
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const submitData = {
      registration: props.registration,
      action: props.action,
      reason: formData.reason,
      send_notification: formData.send_notification,
      notes: formData.notes
    }
    
    if (props.action === 'approve') {
      submitData.assigned_organization = formData.assigned_organization
      submitData.assigned_roles = formData.assigned_roles
    }
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('confirm', submitData)
    
    dialogVisible.value = false
    resetForm()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    resetForm()
    loadDefaultData()
  }
})

watch(() => props.action, () => {
  if (props.modelValue) {
    resetForm()
    loadDefaultData()
  }
})
</script>

<style scoped>
.review-content {
  padding: 0;
}

.applicant-summary {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.summary-info {
  flex: 1;
}

.applicant-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.applicant-detail {
  font-size: 12px;
  color: #909399;
}

.summary-request {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.request-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.request-label {
  font-size: 12px;
  color: #606266;
  min-width: 60px;
}

.role-tag {
  margin-right: 4px;
}

.form-hint {
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-checkbox) {
  margin-right: 0;
}
</style>
