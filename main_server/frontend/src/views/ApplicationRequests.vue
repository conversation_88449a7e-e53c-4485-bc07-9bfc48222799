<template>
  <div class="application-requests-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h2>处理事项</h2>
        <p>申请处理与管理，支持智能分发和转交功能</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true" v-if="canSubmitApplication">
          <el-icon><Plus /></el-icon>
          提交申请
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-if="userPermissionLevel <= 2">
      <div class="stat-card">
        <div class="stat-icon pending">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.pending_applications || 0 }}</div>
          <div class="stat-label">待处理申请</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon approved">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.approved_applications || 0 }}</div>
          <div class="stat-label">已批准申请</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon total">
          <el-icon><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.total_applications || 0 }}</div>
          <div class="stat-label">申请总数</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-select v-model="filters.status" placeholder="状态筛选" clearable @change="loadApplications">
          <el-option label="待处理" value="pending" />
          <el-option label="已批准" value="approved" />
          <el-option label="已拒绝" value="rejected" />
          <el-option label="已转交" value="forwarded" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
        <el-select v-model="filters.type" placeholder="类型筛选" clearable @change="loadApplications">
          <el-option 
            v-for="type in applicationTypes" 
            :key="type.code" 
            :label="type.name" 
            :value="type.code" 
          />
        </el-select>
      </div>
      <div class="filter-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索申请标题或内容"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 申请列表 -->
    <div class="applications-table">
      <el-table 
        :data="applications" 
        v-loading="loading"
        @row-click="viewApplication"
        style="width: 100%"
      >
        <el-table-column prop="title" label="申请标题" min-width="200">
          <template #default="{ row }">
            <div class="application-title">
              <span>{{ row.title }}</span>
              <el-tag v-if="row.is_forwarded" type="info" size="small">已转交</el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="申请类型" width="120">
          <template #default="{ row }">
            <el-tag size="small">{{ getTypeName(row.type) }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="applicant_name" label="申请人" width="120" />
        
        <el-table-column prop="applicant_organization" label="所属组织" width="150" />
        
        <el-table-column label="处理信息" width="150">
          <template #default="{ row }">
            <div v-if="row.processor_name">
              <div class="processor-info">处理人: {{ row.processor_name }}</div>
            </div>
            <div v-else-if="row.target_processor_name">
              <div class="processor-info">指定: {{ row.target_processor_name }}</div>
            </div>
            <div v-else-if="row.forwarded_to_name">
              <div class="processor-info">转交至: {{ row.forwarded_to_name }}</div>
            </div>
            <div v-else class="processor-info">待分配</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click.stop="viewApplication(row)">查看</el-button>
            <el-button 
              v-if="canProcessApplication(row)" 
              size="small" 
              type="primary" 
              @click.stop="processApplication(row)"
            >
              处理
            </el-button>
            <el-button 
              v-if="canForwardApplication(row)" 
              size="small" 
              type="warning" 
              @click.stop="forwardApplication(row)"
            >
              转交
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadApplications"
          @current-change="loadApplications"
        />
      </div>
    </div>

    <!-- 申请详情对话框 -->
    <ApplicationDetailDialog
      v-model="showDetailDialog"
      :application="selectedApplication"
      @refresh="loadApplications"
    />

    <!-- 申请处理对话框 -->
    <ApplicationProcessDialog
      v-model="showProcessDialog"
      :application="selectedApplication"
      @refresh="loadApplications"
    />

    <!-- 申请转交对话框 -->
    <ApplicationForwardDialog
      v-model="showForwardDialog"
      :application="selectedApplication"
      @refresh="loadApplications"
    />

    <!-- 创建申请对话框 -->
    <ApplicationCreateDialog
      v-model="showCreateDialog"
      @refresh="loadApplications"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Refresh, Search, Clock, Check, Document } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { applicationRequestsAPI } from '@/api/applicationRequests'
import ApplicationDetailDialog from '@/components/ApplicationDetailDialog.vue'
import ApplicationProcessDialog from '@/components/ApplicationProcessDialog.vue'
import ApplicationForwardDialog from '@/components/ApplicationForwardDialog.vue'
import ApplicationCreateDialog from '@/components/ApplicationCreateDialog.vue'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const applications = ref([])
const applicationTypes = ref([])
const stats = ref({})
const searchKeyword = ref('')
const selectedApplication = ref(null)

// 对话框状态
const showDetailDialog = ref(false)
const showProcessDialog = ref(false)
const showForwardDialog = ref(false)
const showCreateDialog = ref(false)

// 筛选条件
const filters = reactive({
  status: '',
  type: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 计算属性
const userPermissionLevel = computed(() => userStore.user?.permission_level || 4)
const canSubmitApplication = computed(() => userPermissionLevel.value <= 3)

// 方法
const loadApplications = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...filters
    }
    
    const response = await applicationRequestsAPI.getApplications(params)
    applications.value = response.data || []
    
    // 这里需要从响应头或其他方式获取总数
    // pagination.total = response.total || 0
    
  } catch (error) {
    console.error('加载申请列表失败:', error)
    ElMessage.error('加载申请列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await applicationRequestsAPI.getStats()
    stats.value = response.data || {}
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadApplicationTypes = async () => {
  try {
    const response = await applicationRequestsAPI.getTypes()
    applicationTypes.value = response.data || []
  } catch (error) {
    console.error('加载申请类型失败:', error)
  }
}

const refreshData = () => {
  loadApplications()
  loadStats()
}

const handleSearch = () => {
  // 实现搜索逻辑
  loadApplications()
}

const viewApplication = (application) => {
  selectedApplication.value = application
  showDetailDialog.value = true
}

const processApplication = (application) => {
  selectedApplication.value = application
  showProcessDialog.value = true
}

const forwardApplication = (application) => {
  selectedApplication.value = application
  showForwardDialog.value = true
}

const canProcessApplication = (application) => {
  // 检查是否可以处理申请
  return userPermissionLevel.value <= 2 && 
         ['pending', 'forwarded'].includes(application.status)
}

const canForwardApplication = (application) => {
  // 检查是否可以转交申请
  return userPermissionLevel.value <= 2 && 
         ['pending'].includes(application.status)
}

// 辅助方法
const getTypeName = (typeCode) => {
  const type = applicationTypes.value.find(t => t.code === typeCode)
  return type?.name || typeCode
}

const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    forwarded: 'info',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    approved: '已批准',
    rejected: '已拒绝',
    forwarded: '已转交',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getPriorityType = (priority) => {
  const priorityMap = {
    low: 'info',
    normal: '',
    high: 'warning',
    urgent: 'danger'
  }
  return priorityMap[priority] || ''
}

const getPriorityText = (priority) => {
  const priorityMap = {
    low: '低',
    normal: '普通',
    high: '高',
    urgent: '紧急'
  }
  return priorityMap[priority] || priority
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadApplicationTypes()
  loadApplications()
  if (userPermissionLevel.value <= 2) {
    loadStats()
  }
})
</script>

<style scoped>
.application-requests-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-icon.approved {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.stat-icon.total {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-left {
  display: flex;
  gap: 12px;
}

.filter-right {
  width: 300px;
}

.applications-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.application-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.processor-info {
  font-size: 12px;
  color: #606266;
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
