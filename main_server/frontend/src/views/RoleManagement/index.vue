<template>
  <div class="role-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>角色管理</h2>
        <p>管理系统角色和权限分配，仅超级管理员可访问</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog" :icon="Plus">
          创建角色
        </el-button>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="role-list">
      <el-table :data="roles" style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="角色名称" width="150">
          <template #default="{ row }">
            <div class="role-name">
              <el-tag v-if="row.is_system_role" type="danger" size="small">系统</el-tag>
              {{ row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="角色描述" min-width="200" />
        <el-table-column label="权限范围" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.level_scope === 0" type="warning">无限制</el-tag>
            <el-tag v-else type="info">{{ row.level_scope }}级</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="管理权限" width="200">
          <template #default="{ row }">
            <div class="permission-tags">
              <el-tag v-if="row.can_manage_users" size="small" type="success">用户管理</el-tag>
              <el-tag v-if="row.can_manage_devices" size="small" type="primary">设备管理</el-tag>
              <el-tag v-if="row.can_view_reports" size="small" type="info">报告查看</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewRole(row)" :icon="View">查看</el-button>
            <el-button 
              v-if="!row.is_system_role" 
              size="small" 
              type="primary" 
              @click="editRole(row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button
              v-if="!row.is_system_role"
              size="small"
              type="danger"
              @click="handleDeleteRole(row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建/编辑角色对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑角色' : '创建角色'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="roleForm" :rules="rules" ref="roleFormRef" label-width="120px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input 
            v-model="roleForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
        <el-form-item label="权限层级">
          <el-select v-model="roleForm.level_scope" placeholder="选择权限层级范围">
            <el-option label="无限制" :value="0" />
            <el-option label="1级权限" :value="1" />
            <el-option label="2级权限" :value="2" />
            <el-option label="3级权限" :value="3" />
            <el-option label="4级权限" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="管理权限">
          <el-checkbox-group v-model="managementPermissions">
            <el-checkbox label="can_manage_users">用户管理</el-checkbox>
            <el-checkbox label="can_manage_devices">设备管理</el-checkbox>
            <el-checkbox label="can_view_reports">报告查看</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="功能权限">
          <el-checkbox-group v-model="roleForm.permissions">
            <div
              v-for="(permissions, groupName) in permissionGroups"
              :key="groupName"
              class="permission-group"
            >
              <h4>{{ groupName }}</h4>
              <el-checkbox
                v-for="permission in permissions"
                :key="permission"
                :label="permission"
              >
                {{ getPermissionName(permission) }}
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRole" :loading="saving">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 角色详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="角色详情" width="500px">
      <div v-if="selectedRole" class="role-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="角色名称">{{ selectedRole.name }}</el-descriptions-item>
          <el-descriptions-item label="角色描述">{{ selectedRole.description }}</el-descriptions-item>
          <el-descriptions-item label="系统角色">
            <el-tag :type="selectedRole.is_system_role ? 'danger' : 'success'">
              {{ selectedRole.is_system_role ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="权限层级">
            <el-tag v-if="selectedRole.level_scope === 0" type="warning">无限制</el-tag>
            <el-tag v-else type="info">{{ selectedRole.level_scope }}级</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedRole.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(selectedRole.updated_at) }}</el-descriptions-item>
        </el-descriptions>
        
        <h4 style="margin-top: 20px;">权限列表</h4>
        <div class="permission-list">
          <el-tag 
            v-for="permission in selectedRole.permissions" 
            :key="permission" 
            style="margin: 2px;"
            size="small"
          >
            {{ getPermissionName(permission) }}
          </el-tag>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, View } from '@element-plus/icons-vue'
import {
  getRoles,
  createRole,
  updateRole,
  deleteRole,
  getPermissionGroups,
  getPermissionDescriptions,
  validateRolePermissions
} from '@/api/roles'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const roles = ref([])
const selectedRole = ref(null)
const roleFormRef = ref()

// 表单数据
const roleForm = reactive({
  name: '',
  description: '',
  level_scope: 0,
  permissions: [],
  can_manage_users: false,
  can_manage_devices: false,
  can_view_reports: false
})

// 管理权限的计算属性
const managementPermissions = computed({
  get() {
    const perms = []
    if (roleForm.can_manage_users) perms.push('can_manage_users')
    if (roleForm.can_manage_devices) perms.push('can_manage_devices')
    if (roleForm.can_view_reports) perms.push('can_view_reports')
    return perms
  },
  set(value) {
    roleForm.can_manage_users = value.includes('can_manage_users')
    roleForm.can_manage_devices = value.includes('can_manage_devices')
    roleForm.can_view_reports = value.includes('can_view_reports')
  }
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入角色描述', trigger: 'blur' }
  ]
}



// 获取权限分组和描述
const permissionGroups = getPermissionGroups()
const permissionDescriptions = getPermissionDescriptions()

// 方法
const loadRoles = async () => {
  loading.value = true
  try {
    const response = await getRoles()

    // 🔧 修复：处理API中间件包装格式
    let roleData = response
    if (response && response.success && response.data) {
      console.log('🔧 loadRoles - 检测到API中间件包装格式，提取data字段')
      roleData = response.data
    }
    console.log('loadRoles - 处理后的角色数据:', roleData)

    // 处理角色数据格式
    if (roleData && roleData.roles) {
      roles.value = Array.isArray(roleData.roles) ? roleData.roles : []
      console.log('加载角色列表成功:', roles.value.length, '个角色')
      if (roleData.filtered_by_permission) {
        console.log('权限过滤已生效')
      }
    } else if (Array.isArray(roleData)) {
      // 兼容直接返回数组格式
      roles.value = roleData
      console.log('加载角色列表成功（数组格式）:', roles.value.length, '个角色')
    } else {
      // 兜底处理
      roles.value = []
      console.log('角色数据格式异常，设置为空数组')
    }
  } catch (error) {
    ElMessage.error('加载角色列表失败')
    console.error('Load roles error:', error)
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const editRole = (role) => {
  isEdit.value = true
  Object.assign(roleForm, role)
  dialogVisible.value = true
}

const viewRole = (role) => {
  selectedRole.value = role
  detailDialogVisible.value = true
}

const resetForm = () => {
  Object.assign(roleForm, {
    name: '',
    description: '',
    level_scope: 0,
    permissions: [],
    can_manage_users: false,
    can_manage_devices: false,
    can_view_reports: false
  })
  roleFormRef.value?.resetFields()
}

const saveRole = async () => {
  if (!roleFormRef.value) return

  try {
    await roleFormRef.value.validate()
    saving.value = true

    const data = { ...roleForm }

    // 验证权限配置
    const validation = validateRolePermissions(data.permissions)
    if (!validation.isValid) {
      ElMessage.error(`无效的权限配置: ${validation.invalidPermissions.join(', ')}`)
      return
    }

    if (isEdit.value) {
      await updateRole(roleForm.id, data)
      ElMessage.success('角色更新成功')
    } else {
      await createRole(data)
      ElMessage.success('角色创建成功')
    }

    dialogVisible.value = false
    await loadRoles()
  } catch (error) {
    ElMessage.error(isEdit.value ? '角色更新失败' : '角色创建失败')
    console.error('Save role error:', error)
  } finally {
    saving.value = false
  }
}

const handleDeleteRole = async (role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${role.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteRole(role.id)
    ElMessage.success('角色删除成功')
    await loadRoles()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('角色删除失败')
      console.error('Delete role error:', error)
    }
  }
}

const getPermissionName = (permission) => {
  return permissionDescriptions[permission] || permission
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.role-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.role-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.permission-group {
  margin-bottom: 16px;
}

.permission-group h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.permission-group .el-checkbox {
  margin-right: 16px;
  margin-bottom: 8px;
}

.role-detail .permission-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}
</style>
