<template>
  <div class="permission-assignment-component">

    <!-- 当前选中状态显示 -->
    <div class="current-selection" v-if="selectedTarget">
      <div class="selection-info">
        <el-icon class="selection-icon"><User /></el-icon>
        <span class="selection-text">
          当前选中：
          <strong>{{ selectedTarget.name }}</strong>
          <el-tag v-if="selectedTarget.type === 'organization'" type="info" size="small">
            {{ getLevelName(selectedTarget.level) }}
          </el-tag>
          <el-tag v-else type="success" size="small">用户</el-tag>
        </span>
      </div>
      <div class="selection-actions">
        <el-button size="small" @click="clearSelection">清除选择</el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：组织架构面板 -->
      <div class="org-panel">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>组织架构</span>
              <el-button size="small" @click="expandAll">
                {{ allExpanded ? '收起全部' : '展开全部' }}
              </el-button>
            </div>
          </template>
          
          <el-tree
            ref="orgTreeRef"
            :data="organizationTree"
            :props="treeProps"
            node-key="id"
            :default-expanded-keys="defaultExpandedKeys"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            :highlight-current="true"
            class="org-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node" :class="{ 'is-selected': isNodeSelected(data) }">
                <div class="node-info">
                  <el-icon class="node-icon">
                    <OfficeBuilding v-if="data.type === 'organization'" />
                    <User v-else />
                  </el-icon>
                  <span class="node-label">
                    {{ data.name }}
                    <span v-if="data.type === 'organization'" class="org-type">
                      ({{ getLevelName(data.level) }})
                    </span>
                    <span v-if="data.type === 'organization'" class="user-count">
                      - {{ data.userCount || 0 }}人
                    </span>
                    <span v-else class="user-role">
                      - {{ data.role_name }}
                    </span>
                  </span>
                </div>
              </div>
            </template>
          </el-tree>
        </el-card>
      </div>

      <!-- 右侧：设备分配面板 -->
      <div class="device-panel">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>设备权限分配</span>
              <div class="header-actions">
                <el-button 
                  type="success" 
                  size="small" 
                  @click="assignPermissions"
                  :disabled="!selectedTarget || selectedDevices.length === 0"
                  :loading="assigning"
                >
                  <el-icon><Check /></el-icon>
                  分配权限
                </el-button>
              </div>
            </div>
          </template>

          <div class="device-tabs">
            <el-tabs v-model="activeTab" @tab-change="handleTabChange">
              <!-- 设备分组标签页 -->
              <el-tab-pane label="分组" name="groups">
                <div class="tab-content">
                  <div class="search-bar">
                    <el-input
                      v-model="groupSearchText"
                      placeholder="搜索设备分组..."
                      :prefix-icon="Search"
                      clearable
                      @input="filterGroups"
                    />
                  </div>
                  
                  <div class="device-list" v-loading="groupsLoading">
                    <div 
                      v-for="group in filteredGroups" 
                      :key="group.id"
                      class="device-item"
                      :class="{ 'is-selected': selectedDevices.includes(group.id) }"
                      @click="toggleDeviceSelection(group.id)"
                    >
                      <el-checkbox 
                        :model-value="selectedDevices.includes(group.id)"
                        @change="toggleDeviceSelection(group.id)"
                      />
                      <div class="device-info">
                        <div class="device-name">{{ group.name }}</div>
                        <div class="device-desc">{{ group.description || '无描述' }}</div>
                        <div class="device-meta">
                          <el-tag :type="getGroupTypeColor(group.group_type)" size="small">
                            {{ getGroupTypeName(group.group_type) }}
                          </el-tag>
                          <span class="device-count">{{ group.device_count || 0 }}个设备</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 独立设备标签页 -->
              <el-tab-pane label="设备" name="devices">
                <div class="tab-content">
                  <div class="search-bar">
                    <el-input
                      v-model="deviceSearchText"
                      placeholder="搜索USB设备..."
                      :prefix-icon="Search"
                      clearable
                      @input="filterDevices"
                    />
                  </div>
                  
                  <div class="device-list" v-loading="devicesLoading">
                    <div 
                      v-for="device in filteredDevices" 
                      :key="device.id"
                      class="device-item"
                      :class="{ 'is-selected': selectedDevices.includes(device.id) }"
                      @click="toggleDeviceSelection(device.id)"
                    >
                      <el-checkbox 
                        :model-value="selectedDevices.includes(device.id)"
                        @change="toggleDeviceSelection(device.id)"
                      />
                      <div class="device-info">
                        <div class="device-name">{{ device.name || device.device_name }}</div>
                        <div class="device-desc">{{ device.description || '无描述' }}</div>
                        <div class="device-meta">
                          <el-tag :type="getDeviceStatusColor(device.status)" size="small">
                            {{ device.status }}
                          </el-tag>
                          <span class="device-id">VID:{{ device.vid }} PID:{{ device.pid }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 权限分配确认对话框 -->
    <el-dialog
      v-model="assignDialogVisible"
      title="确认权限分配"
      width="500px"
      @close="resetAssignDialog"
    >
      <div class="assign-confirm">
        <p><strong>分配对象：</strong>{{ selectedTarget?.name }}</p>
        <p><strong>分配类型：</strong>{{ activeTab === 'groups' ? '设备分组' : '独立设备' }}</p>
        <p><strong>选中数量：</strong>{{ selectedDevices.length }}个</p>
        <p><strong>权限类型：</strong>使用权限</p>
      </div>
      
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAssignPermissions" :loading="assigning">
          确认分配
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  User,
  OfficeBuilding,
  Check,
  Search
} from '@element-plus/icons-vue'
import {
  getDeviceGroups,
  getDevices,
  assignDevicePermissions
} from '@/api/permission-assignment'
import { useOrganizationTree } from '@/composables/useOrganizationTree.js'
import { usePermissionAudit } from '@/composables/usePermissionAudit.js'

// 使用统一的组织架构管理
const {
  organizationTree,
  loading: orgLoading,
  selectedNode: selectedTarget,
  expandedKeys: defaultExpandedKeys,
  searchText: orgSearchText,
  loadData: loadOrganizationData,
  refreshData: refreshOrganizationData,
  selectNode
} = useOrganizationTree({
  autoLoad: true,
  enableCache: true,
  maxExpandLevel: 3
})

// 使用权限审计系统
const {
  recordOperation,
  completeOperation,
  canManagePermissions,
  OPERATION_TYPES,
  PERMISSION_TYPES
} = usePermissionAudit()

// 其他响应式数据
const loading = ref(false)
const assigning = ref(false)
const groupsLoading = ref(false)
const devicesLoading = ref(false)
const allExpanded = ref(false)
const assignDialogVisible = ref(false)

// 组织架构相关
const orgTreeRef = ref()

// 设备相关
const activeTab = ref('groups')
const deviceGroups = ref([])
const devices = ref([])
const selectedDevices = ref([])

// 搜索相关
const groupSearchText = ref('')
const deviceSearchText = ref('')

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 计算属性
const filteredGroups = computed(() => {
  if (!groupSearchText.value) return deviceGroups.value
  return deviceGroups.value.filter(group => 
    group.name.toLowerCase().includes(groupSearchText.value.toLowerCase()) ||
    (group.description && group.description.toLowerCase().includes(groupSearchText.value.toLowerCase()))
  )
})

const filteredDevices = computed(() => {
  if (!deviceSearchText.value) return devices.value
  return devices.value.filter(device => 
    (device.name || device.device_name || '').toLowerCase().includes(deviceSearchText.value.toLowerCase())
  )
})

// 方法
const getLevelName = (level) => {
  const levelNames = ['集团总部', '大区', '分公司', '部门', '小组']
  return levelNames[level] || '未知'
}

const getGroupTypeColor = (type) => {
  switch (type) {
    case 'server': return 'success'
    case 'mixed': return 'warning'
    case 'single': return 'info'
    default: return ''
  }
}

const getGroupTypeName = (type) => {
  switch (type) {
    case 'server': return '服务器分组'
    case 'mixed': return '混合分组'
    case 'single': return '单设备分组'
    default: return '未知类型'
  }
}

const getDeviceStatusColor = (status) => {
  switch (status) {
    case '在线': return 'success'
    case '离线': return 'danger'
    case '占用': return 'warning'
    default: return 'info'
  }
}

const isNodeSelected = (data) => {
  return selectedTarget.value && selectedTarget.value.id === data.id && selectedTarget.value.type === data.type
}

const handleNodeClick = (data) => {
  selectedTarget.value = data
  selectedDevices.value = []
  ElMessage.info(`已选择：${data.name}`)
}

const clearSelection = () => {
  selectedTarget.value = null
  selectedDevices.value = []
}

const expandAll = () => {
  allExpanded.value = !allExpanded.value
  nextTick(() => {
    if (allExpanded.value) {
      orgTreeRef.value?.expandAll()
    } else {
      orgTreeRef.value?.collapseAll()
    }
  })
}

const handleTabChange = (tabName) => {
  selectedDevices.value = []
}

const toggleDeviceSelection = (deviceId) => {
  const index = selectedDevices.value.indexOf(deviceId)
  if (index > -1) {
    selectedDevices.value.splice(index, 1)
  } else {
    selectedDevices.value.push(deviceId)
  }
}

const filterGroups = () => {
  // 搜索过滤逻辑已在计算属性中实现
}

const filterDevices = () => {
  // 搜索过滤逻辑已在计算属性中实现
}

const assignPermissions = () => {
  if (!selectedTarget.value) {
    ElMessage.warning('请先选择组织或用户')
    return
  }
  if (selectedDevices.value.length === 0) {
    ElMessage.warning('请选择要分配的设备或分组')
    return
  }
  assignDialogVisible.value = true
}

const confirmAssignPermissions = async () => {
  try {
    assigning.value = true
    
    const assignmentData = {
      target_type: selectedTarget.value.type,
      target_id: selectedTarget.value.id,
      device_type: activeTab.value,
      device_ids: selectedDevices.value,
      permission_type: 'use'
    }

    await assignDevicePermissions(assignmentData)
    
    ElMessage.success('权限分配成功')
    assignDialogVisible.value = false
    selectedDevices.value = []
    
  } catch (error) {
    console.error('权限分配失败:', error)
    ElMessage.error('权限分配失败')
  } finally {
    assigning.value = false
  }
}

const resetAssignDialog = () => {
  assignDialogVisible.value = false
}

// loadOrganizationTree 函数已由 useOrganizationTree Composable 替代

const loadDeviceGroups = async () => {
  try {
    groupsLoading.value = true
    const response = await getDeviceGroups()
    deviceGroups.value = response.data || []
  } catch (error) {
    console.error('加载设备分组失败:', error)
    ElMessage.error('加载设备分组失败')
  } finally {
    groupsLoading.value = false
  }
}

const loadDevices = async () => {
  try {
    devicesLoading.value = true
    const response = await getDevices()
    devices.value = response.data || []
  } catch (error) {
    console.error('加载设备列表失败:', error)
    ElMessage.error('加载设备列表失败')
  } finally {
    devicesLoading.value = false
  }
}

const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadOrganizationTree(),
      loadDeviceGroups(),
      loadDevices()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.permission-assignment-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.current-selection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selection-icon {
  font-size: 20px;
}

.selection-text {
  font-size: 16px;
}

.main-content {
  display: flex;
  gap: 20px;
  height: calc(100vh - 200px);
}

.org-panel {
  width: 400px;
  flex-shrink: 0;
}

.device-panel {
  flex: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.org-tree {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.tree-node:hover {
  background-color: #f5f7fa;
}

.tree-node.is-selected {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.node-icon {
  color: #409eff;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.org-type, .user-count, .user-role {
  color: #909399;
  font-size: 12px;
}

.device-tabs {
  height: calc(100vh - 300px);
}

.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-bar {
  margin-bottom: 16px;
}

.device-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.device-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.device-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.device-item.is-selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.device-info {
  flex: 1;
}

.device-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.device-desc {
  color: #606266;
  font-size: 12px;
  margin-bottom: 8px;
}

.device-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.device-count, .device-id {
  color: #909399;
  font-size: 12px;
}

.assign-confirm p {
  margin: 8px 0;
  color: #606266;
}

.header-actions {
  display: flex;
  gap: 8px;
}
</style>
