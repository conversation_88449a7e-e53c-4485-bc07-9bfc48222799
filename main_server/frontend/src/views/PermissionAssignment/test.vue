<template>
  <div style="padding: 20px;">
    <h1>🎉 权限分配功能测试页面</h1>
    <p>如果您能看到这个页面，说明路由配置正确！</p>
    <p>当前用户：{{ userInfo?.full_name || '未知' }}</p>
    <p>用户权限：{{ permissions.join(', ') }}</p>
    <p>是否有device.group权限：{{ hasDeviceGroup ? '是' : '否' }}</p>
    <el-button type="primary" @click="testAPI">测试API调用</el-button>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const userInfo = ref(userStore.userInfo)
const permissions = ref(userStore.permissions)
const hasDeviceGroup = ref(userStore.hasPermission('device.group'))

const testAPI = () => {
  // 测试API调用功能
}

onMounted(() => {
  // 权限分配测试页面初始化
})
</script>
