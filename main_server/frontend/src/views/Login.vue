<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <div class="logo">
          <el-icon class="logo-icon"><Monitor /></el-icon>
          <h1 class="logo-text">OmniLink 全联通系统</h1>
        </div>
        <p class="subtitle">USB设备远程共享与管理平台</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>

        <!-- 专属客户端下载按钮 -->
        <el-form-item>
          <el-button
            type="success"
            class="download-button"
            @click="downloadClient"
          >
            <el-icon><Download /></el-icon>
            下载专属客户端
          </el-button>
        </el-form-item>

        <!-- 注册按钮 -->
        <el-form-item>
          <div class="register-section">
            <span>还没有账户？</span>
            <el-button type="text" @click="goToRegister">注册新用户</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { getClientDownloadUrl } from '@/api/systemSettings'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loading = ref(false)
const loginFormRef = ref()

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}



// 处理登录
const handleLogin = async () => {
  try {
    await loginFormRef.value.validate()
    
    loading.value = true
    
    const success = await userStore.userLogin({
      username: loginForm.username,
      password: loginForm.password
    })
    
    if (success) {
      // 登录成功，跳转到目标页面或首页
      const redirect = route.query.redirect || '/'
      router.push(redirect)
    }
    
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

// 跳转到注册页面
const goToRegister = () => {
  router.push('/register')
}

// 下载专属客户端
const downloadClient = async () => {
  try {
    const response = await getClientDownloadUrl()
    if (response.success && response.data?.url) {
      window.open(response.data.url, '_blank')
    } else {
      ElMessage.warning(response.message || '客户端下载链接未配置')
    }
  } catch (error) {
    console.error('获取下载链接失败:', error)
    ElMessage.error('获取下载链接失败，请稍后重试')
  }
}

onMounted(() => {
  // 如果已经登录，直接跳转
  if (userStore.isLoggedIn) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 400px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.logo-icon {
  font-size: 32px;
  color: #409eff;
  margin-right: 12px;
}

.logo-text {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.subtitle {
  color: #909399;
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-bottom: 30px;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.download-button {
  width: 100%;
  height: 44px;
  font-size: 14px;
  margin-bottom: 10px;
}

.register-section {
  text-align: center;
  color: #909399;
  font-size: 14px;
  margin-top: 16px;
}

.register-section .el-button--text {
  color: #409eff;
  font-weight: 500;
  margin-left: 8px;
}



/* 平板设备 (768px - 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
  .login-container {
    padding: 30px;
  }

  .login-box {
    max-width: 450px;
    padding: 45px;
  }

  .logo-text {
    font-size: 26px;
  }

  .login-button {
    height: 50px;
    font-size: 17px;
  }
}

/* 小平板和大手机 (481px - 768px) */
@media (max-width: 768px) and (min-width: 481px) {
  .login-container {
    padding: 25px;
  }

  .login-box {
    max-width: 420px;
    padding: 35px 30px;
  }

  .logo-text {
    font-size: 22px;
  }

  .logo-icon {
    font-size: 28px;
  }

  .login-button {
    height: 46px;
    font-size: 15px;
  }

  .download-button {
    height: 42px;
    font-size: 13px;
  }
}

/* 手机设备 (最大480px) */
@media (max-width: 480px) {
  .login-container {
    padding: 15px;
    min-height: 100vh;
    align-items: flex-start;
    padding-top: 10vh;
  }

  .login-box {
    max-width: 100%;
    padding: 25px 20px;
    margin: 0;
  }

  .login-header {
    margin-bottom: 30px;
  }

  .logo {
    flex-direction: column;
    margin-bottom: 12px;
  }

  .logo-icon {
    font-size: 24px;
    margin-right: 0;
    margin-bottom: 8px;
  }

  .logo-text {
    font-size: 18px;
    text-align: center;
  }

  .subtitle {
    font-size: 13px;
  }

  .login-button {
    height: 44px;
    font-size: 14px;
  }

  .download-button {
    height: 40px;
    font-size: 12px;
  }

  .register-section {
    font-size: 13px;
    margin-top: 12px;
  }
}

/* 超小屏幕 (最大360px) */
@media (max-width: 360px) {
  .login-container {
    padding: 10px;
    padding-top: 8vh;
  }

  .login-box {
    padding: 20px 15px;
  }

  .logo-text {
    font-size: 16px;
  }

  .subtitle {
    font-size: 12px;
  }

  .login-button,
  .download-button {
    height: 42px;
    font-size: 13px;
  }
}
</style>
