<template>
  <div class="dashboard-container">
    <div class="page-header">
      <h2 class="page-title">工作台</h2>
      <p class="welcome-text">欢迎使用 OmniLink 全联通系统，{{ userStore.userName }}！</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon pending">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.pending_applications }}</div>
          <div class="stat-label">待处理申请</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon approved">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.approved_applications }}</div>
          <div class="stat-label">已批准申请</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon users">
          <el-icon><User /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.total_users }}</div>
          <div class="stat-label">系统用户</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon devices">
          <el-icon><Monitor /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.online_devices }}</div>
          <div class="stat-label">在线设备</div>
        </div>
      </div>
    </div>
    
    <!-- 快速操作 -->
    <div class="quick-actions">
      <h3 class="section-title">快速操作</h3>
      <div class="action-grid">
        <div
          class="action-card"
          @click="$router.push('/applications/create')"
          v-if="userStore.hasPermission('application.submit')"
        >
          <el-icon class="action-icon"><Plus /></el-icon>
          <span class="action-text">创建申请</span>
        </div>
        
        <div 
          class="action-card" 
          @click="$router.push('/applications')"
          v-if="userStore.hasPermission('application.view')"
        >
          <el-icon class="action-icon"><Document /></el-icon>
          <span class="action-text">处理事项</span>
        </div>
        
        <div 
          class="action-card" 
          @click="$router.push('/users')"
          v-if="userStore.hasPermission('user.view')"
        >
          <el-icon class="action-icon"><User /></el-icon>
          <span class="action-text">用户管理</span>
        </div>
        
        <div 
          class="action-card" 
          @click="$router.push('/devices')"
          v-if="userStore.hasPermission('device.view')"
        >
          <el-icon class="action-icon"><Monitor /></el-icon>
          <span class="action-text">设备管理</span>
        </div>
      </div>
    </div>
    
    <!-- 最近申请 -->
    <div class="recent-applications" v-if="userStore.hasPermission('application.view')">
      <h3 class="section-title">最近申请</h3>
      <div class="application-list">
        <div 
          v-for="app in recentApplications" 
          :key="app.id"
          class="application-item"
          @click="$router.push(`/applications/${app.id}`)"
        >
          <div class="app-info">
            <div class="app-title">{{ app.title }}</div>
            <div class="app-meta">
              <span class="app-applicant">{{ app.applicant_name }}</span>
              <span class="app-time">{{ formatDateTime(app.created_at) }}</span>
            </div>
          </div>
          <div class="app-status">
            <el-tag :type="getStatusTagType(app.status)">
              {{ getStatusText(app.status) }}
            </el-tag>
          </div>
        </div>
        
        <div v-if="recentApplications.length === 0" class="empty-state">
          <el-icon class="empty-icon"><Document /></el-icon>
          <p class="empty-text">暂无申请记录</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { applicationRequestApi } from '@/api/applicationRequests'
import dayjs from 'dayjs'

const userStore = useUserStore()

const stats = reactive({
  pending_applications: 0,
  approved_applications: 0,
  total_users: 0,
  online_devices: 0
})

const recentApplications = ref([])

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await applicationRequestApi.getApplicationStats()
    // 修复：正确处理API响应格式
    const statsData = response.data || response
    Object.assign(stats, statsData)
    console.log('统计数据加载成功:', statsData)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载最近申请
const loadRecentApplications = async () => {
  try {
    const response = await applicationRequestApi.getApplicationRequests({ page: 1, size: 5 })
    recentApplications.value = response.data || []
  } catch (error) {
    console.error('加载最近申请失败:', error)
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('MM-DD HH:mm')
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    approved: '已批准',
    rejected: '已拒绝',
    cancelled: '已取消'
  }
  return texts[status] || status
}

onMounted(() => {
  loadStats()
  if (userStore.hasPermission('application.view')) {
    loadRecentApplications()
  }
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.welcome-text {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: #fff;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.stat-icon.approved {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.stat-icon.users {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.stat-icon.devices {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.quick-actions {
  margin-bottom: 40px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 16px;
}

.action-card {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.action-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 12px;
}

.action-text {
  display: block;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.recent-applications {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.application-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.application-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.application-item:hover {
  background: #e6f7ff;
}

.app-info {
  flex: 1;
}

.app-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.app-meta {
  font-size: 12px;
  color: #909399;
}

.app-applicant {
  margin-right: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  margin: 0;
  font-size: 14px;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
