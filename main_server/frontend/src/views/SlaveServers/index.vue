<template>
  <div class="slave-servers-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>从服务器管理</h2>
      <p>管理和监控所有从服务器的状态和设备</p>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button 
        type="primary" 
        @click="refreshList"
        :loading="loading"
      >
        <el-icon><Refresh /></el-icon>
        刷新列表
      </el-button>
      <el-button 
        type="success" 
        @click="showAddDialog = true"
        v-if="userStore.hasPermission('slave.create')"
      >
        <el-icon><Plus /></el-icon>
        添加从服务器
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.total }}</div>
              <div class="stat-label">总数</div>
            </div>
            <el-icon class="stat-icon"><Monitor /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card online">
            <div class="stat-content">
              <div class="stat-number">{{ stats.online }}</div>
              <div class="stat-label">在线</div>
            </div>
            <el-icon class="stat-icon"><Connection /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card offline">
            <div class="stat-content">
              <div class="stat-number">{{ stats.offline }}</div>
              <div class="stat-label">离线</div>
            </div>
            <el-icon class="stat-icon"><Close /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card devices">
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalDevices }}</div>
              <div class="stat-label">设备总数</div>
            </div>
            <el-icon class="stat-icon"><Monitor /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 从服务器列表 -->
    <el-card class="list-card">
      <el-table 
        :data="slaveServers" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="server_id" label="服务器ID" width="150" />
        <el-table-column label="名称" width="200">
          <template #default="{ row }">
            <span>{{ row.name || '未设置名称' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="ip_address" label="IP地址" width="120" />
        <el-table-column prop="port" label="API端口" width="80" />
        <el-table-column label="VH端口" width="80">
          <template #default="{ row }">
            <span>{{ row.vh_port || '7575' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="最后心跳" width="160">
          <template #default="{ row }">
            <span v-if="row.last_seen">
              {{ formatTime(row.last_seen) }}
            </span>
            <span v-else class="text-muted">从未连接</span>
          </template>
        </el-table-column>
        <el-table-column label="设备数量" width="100">
          <template #default="{ row }">
            <el-badge :value="row.device_count || 0" :max="99">
              <el-icon><Monitor /></el-icon>
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column label="位置" width="120">
          <template #default="{ row }">
            <span>{{ row.location || '未设置' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="150">
          <template #default="{ row }">
            <span>{{ row.description || '无描述' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewDetail(row)"
            >
              详情
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              @click="controlServer(row, 'restart')"
              v-if="userStore.hasPermission('slave.control')"
            >
              重启
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteServer(row)"
              v-if="userStore.hasPermission('slave.delete')"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加从服务器对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加从服务器"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addRules"
        label-width="100px"
      >
        <el-form-item label="服务器名称" prop="server_name">
          <el-input v-model="addForm.server_name" placeholder="请输入服务器名称" />
        </el-form-item>
        <el-form-item label="IP地址" prop="server_ip">
          <el-input v-model="addForm.server_ip" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="API端口" prop="server_port">
          <el-input-number v-model="addForm.server_port" :min="1" :max="65535" />
        </el-form-item>
        <el-form-item label="VH端口" prop="vh_port">
          <el-input-number v-model="addForm.vh_port" :min="1" :max="65535" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="addForm.description" 
            type="textarea" 
            placeholder="请输入描述信息"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="位置" prop="location">
          <el-input v-model="addForm.location" placeholder="请输入服务器位置" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="handleAdd" :loading="addLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getSlaveServerList, 
  registerSlaveServer, 
  deleteSlaveServer,
  controlSlaveServer 
} from '@/api/slaveServers'
import { 
  Refresh, 
  Plus, 
  Monitor, 
  Connection, 
  Close 
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const addLoading = ref(false)
const showAddDialog = ref(false)
const slaveServers = ref([])

// 统计数据
const stats = computed(() => {
  const total = slaveServers.value.length
  const online = slaveServers.value.filter(s => s.status === 'online').length
  const offline = total - online
  const totalDevices = slaveServers.value.reduce((sum, s) => sum + (s.device_count || 0), 0)
  
  return { total, online, offline, totalDevices }
})

// 添加表单
const addFormRef = ref()
const addForm = reactive({
  server_name: '',
  server_ip: '',
  server_port: 8889,
  vh_port: 7575,
  description: '',
  location: ''
})

const addRules = {
  server_name: [
    { required: true, message: '请输入服务器名称', trigger: 'blur' }
  ],
  server_ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: 'IP地址格式不正确', trigger: 'blur' }
  ],
  server_port: [
    { required: true, message: '请输入API端口', trigger: 'blur' }
  ],
  vh_port: [
    { required: true, message: '请输入VH端口', trigger: 'blur' }
  ]
}

// 方法
const refreshList = async () => {
  loading.value = true
  try {
    const response = await getSlaveServerList()
    slaveServers.value = response.data || []

    // 启动实时状态监控
    setupRealtimeMonitoring()
  } catch (error) {
    ElMessage.error('获取从服务器列表失败')
    console.error('获取从服务器列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 实时监控设置
const setupRealtimeMonitoring = () => {
  // 使用WebSocket监听从服务器状态变化
  try {
    import('@/utils/websocket').then(({ WebSocketManager }) => {
      const wsManager = new WebSocketManager()

      // 订阅从服务器状态变化
      wsManager.subscribe('slave_status')

      // 监听状态变化事件
      wsManager.on('slave_status_change', (data) => {
        console.log('从服务器状态变化:', data)

        // 更新本地状态
        const serverIndex = slaveServers.value.findIndex(s => s.id === data.server_id)
        if (serverIndex !== -1) {
          slaveServers.value[serverIndex].status = data.new_status

          // 显示状态变化通知
          const statusText = data.new_status === 'online' ? '上线' : '离线'
          ElMessage({
            message: `从服务器 ${data.server_name} 已${statusText}`,
            type: data.new_status === 'online' ? 'success' : 'warning',
            duration: 3000
          })
        }
      })

      // 连接WebSocket
      wsManager.connect()
    }).catch(error => {
      console.warn('WebSocket模块加载失败，使用轮询模式:', error)
      setupPollingMonitoring()
    })
  } catch (error) {
    console.warn('WebSocket初始化失败，使用轮询模式:', error)
    setupPollingMonitoring()
  }
}

// 轮询监控（WebSocket不可用时的备选方案）
const setupPollingMonitoring = () => {
  // 每30秒轮询一次状态
  setInterval(async () => {
    try {
      const response = await getSlaveServerList()
      const newServers = response.data || []

      // 检查状态变化
      newServers.forEach(newServer => {
        const oldServer = slaveServers.value.find(s => s.id === newServer.id)
        if (oldServer && oldServer.status !== newServer.status) {
          const statusText = newServer.status === 'online' ? '上线' : '离线'
          ElMessage({
            message: `从服务器 ${newServer.name} 已${statusText}`,
            type: newServer.status === 'online' ? 'success' : 'warning',
            duration: 3000
          })
        }
      })

      // 更新列表
      slaveServers.value = newServers
    } catch (error) {
      console.error('轮询更新失败:', error)
    }
  }, 30000)
}

const getStatusType = (status) => {
  switch (status) {
    case 'online': return 'success'
    case 'offline': return 'danger'
    default: return 'warning'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'online': return '在线'
    case 'offline': return '离线'
    default: return '未知'
  }
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const viewDetail = (row) => {
  router.push(`/slave-servers/${row.id}`)
}

const controlServer = async (row, action) => {
  try {
    await controlSlaveServer(row.id, action)
    ElMessage.success(`${action === 'restart' ? '重启' : '操作'}命令已发送`)
    refreshList()
  } catch (error) {
    ElMessage.error('操作失败')
    console.error('控制从服务器失败:', error)
  }
}

const deleteServer = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除从服务器 "${row.name}" 吗？此操作将同时删除该服务器上的所有设备记录。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    await deleteSlaveServer(row.id)
    ElMessage.success('从服务器删除成功')
    refreshList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除从服务器失败:', error)
    }
  }
}

const handleAdd = async () => {
  if (!addFormRef.value) return
  
  const valid = await addFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  addLoading.value = true
  try {
    await registerSlaveServer(addForm)
    ElMessage.success('添加成功')
    showAddDialog.value = false
    refreshList()
    
    // 重置表单
    Object.assign(addForm, {
      server_name: '',
      server_ip: '',
      server_port: 8889,
      vh_port: 7575,
      description: '',
      location: ''
    })
  } catch (error) {
    ElMessage.error('添加失败')
    console.error('添加从服务器失败:', error)
  } finally {
    addLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  refreshList()
})
</script>

<style scoped>
.slave-servers-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card.online {
  border-left: 4px solid #67c23a;
}

.stat-card.offline {
  border-left: 4px solid #f56c6c;
}

.stat-card.devices {
  border-left: 4px solid #409eff;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  color: #e4e7ed;
  z-index: 1;
}

.list-card {
  margin-top: 20px;
}

.text-muted {
  color: #909399;
}
</style>
