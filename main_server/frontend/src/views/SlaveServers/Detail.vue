<template>
  <div class="slave-server-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button @click="$router.back()" type="text">
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>从服务器详情</h2>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else-if="serverInfo" class="detail-content">
      <!-- 基本信息卡片 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag 
              :type="getStatusType(serverInfo.status)"
              size="large"
            >
              {{ getStatusText(serverInfo.status) }}
            </el-tag>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>服务器ID:</label>
              <span>{{ serverInfo.server_id }}</span>
            </div>
            <div class="info-item">
              <label>名称:</label>
              <span>{{ serverInfo.name || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label>IP地址:</label>
              <span>{{ serverInfo.ip_address }}</span>
            </div>
            <div class="info-item">
              <label>端口:</label>
              <span>{{ serverInfo.port }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>状态:</label>
              <span>{{ getStatusText(serverInfo.status) }}</span>
            </div>
            <div class="info-item">
              <label>最后心跳:</label>
              <span>{{ formatTime(serverInfo.last_seen) }}</span>
            </div>
            <div class="info-item">
              <label>创建时间:</label>
              <span>{{ formatTime(serverInfo.created_at) }}</span>
            </div>
            <div class="info-item">
              <label>更新时间:</label>
              <span>{{ formatTime(serverInfo.updated_at) }}</span>
            </div>
          </el-col>
        </el-row>
        
        <div class="info-item full-width">
          <label>描述:</label>
          <span>{{ serverInfo.description || '无描述' }}</span>
        </div>
      </el-card>

      <!-- 控制面板 -->
      <el-card class="control-card">
        <template #header>
          <span>控制面板</span>
        </template>
        
        <div class="control-buttons">
          <el-button 
            type="success" 
            @click="controlServer('start_vh')"
            :loading="controlLoading"
            v-if="userStore.hasPermission('slave.control')"
          >
            <el-icon><VideoPlay /></el-icon>
            启动VirtualHere
          </el-button>
          <el-button 
            type="warning" 
            @click="controlServer('restart_vh')"
            :loading="controlLoading"
            v-if="userStore.hasPermission('slave.control')"
          >
            <el-icon><Refresh /></el-icon>
            重启VirtualHere
          </el-button>
          <el-button 
            type="danger" 
            @click="controlServer('stop_vh')"
            :loading="controlLoading"
            v-if="userStore.hasPermission('slave.control')"
          >
            <el-icon><VideoPause /></el-icon>
            停止VirtualHere
          </el-button>
          <el-button
            type="primary"
            @click="testConnection"
            :loading="testLoading"
          >
            <el-icon><Connection /></el-icon>
            测试连接
          </el-button>
          <el-button
            type="info"
            @click="controlServer('refresh')"
            :loading="controlLoading"
          >
            <el-icon><Refresh /></el-icon>
            强制刷新
          </el-button>
        </div>
      </el-card>

      <!-- 设备列表 -->
      <el-card class="devices-card">
        <template #header>
          <div class="card-header">
            <span>设备列表</span>
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshDevices"
              :loading="devicesLoading"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        
        <el-table 
          :data="devices" 
          v-loading="devicesLoading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="device_name" label="设备名称" width="150" />
          <el-table-column prop="device_type" label="设备类型" width="120" />
          <el-table-column label="厂商ID" width="100">
            <template #default="{ row }">
              <code>{{ row.vendor_id }}</code>
            </template>
          </el-table-column>
          <el-table-column label="产品ID" width="100">
            <template #default="{ row }">
              <code>{{ row.product_id }}</code>
            </template>
          </el-table-column>
          <el-table-column prop="serial_number" label="序列号" width="150" />
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag 
                :type="getDeviceStatusType(row.status)"
                size="small"
              >
                {{ getDeviceStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.created_at) }}
            </template>
          </el-table-column>
        </el-table>
        
        <div v-if="devices.length === 0 && !devicesLoading" class="empty-devices">
          <el-empty description="暂无设备" />
        </div>
      </el-card>
    </div>

    <div v-else class="error-container">
      <el-result
        icon="error"
        title="加载失败"
        sub-title="无法获取从服务器信息"
      >
        <template #extra>
          <el-button type="primary" @click="loadServerInfo">重试</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { 
  getSlaveServerDetail,
  getSlaveServerDevices,
  controlSlaveServer,
  testSlaveServerConnection
} from '@/api/slaveServers'
import { 
  ArrowLeft,
  VideoPlay,
  VideoPause,
  Refresh,
  Connection
} from '@element-plus/icons-vue'

const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const loading = ref(true)
const controlLoading = ref(false)
const testLoading = ref(false)
const devicesLoading = ref(false)
const serverInfo = ref(null)
const devices = ref([])

// 计算属性
const isServerOnline = computed(() => {
  if (!serverInfo.value) return false
  return serverInfo.value.status === 'online'
})

// 方法
const loadServerInfo = async () => {
  loading.value = true
  try {
    const serverId = route.params.id
    const response = await getSlaveServerDetail(serverId)
    serverInfo.value = response.data
  } catch (error) {
    ElMessage.error('获取从服务器信息失败')
    console.error('获取从服务器信息失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshDevices = async () => {
  devicesLoading.value = true
  try {
    const serverId = route.params.id
    const response = await getSlaveServerDevices(serverId)

    // 处理API响应格式
    if (response.success && response.data) {
      devices.value = response.data
    } else if (response.data) {
      // 兼容旧格式
      devices.value = response.data
    } else {
      devices.value = []
    }

    console.log('设备列表获取成功:', devices.value.length, '个设备')
  } catch (error) {
    ElMessage.error('获取设备列表失败')
    console.error('获取设备列表失败:', error)
    devices.value = []
  } finally {
    devicesLoading.value = false
  }
}

const controlServer = async (action) => {
  controlLoading.value = true
  try {
    const serverId = route.params.id
    await controlSlaveServer(serverId, action)
    ElMessage.success('控制命令已发送')
    
    // 延迟刷新服务器信息
    setTimeout(() => {
      loadServerInfo()
    }, 2000)
  } catch (error) {
    ElMessage.error('控制命令发送失败')
    console.error('控制从服务器失败:', error)
  } finally {
    controlLoading.value = false
  }
}

const testConnection = async () => {
  testLoading.value = true
  try {
    const serverId = route.params.id
    await testSlaveServerConnection(serverId)
    ElMessage.success('连接测试成功')
  } catch (error) {
    ElMessage.error('连接测试失败')
    console.error('测试连接失败:', error)
  } finally {
    testLoading.value = false
  }
}

const getStatusType = (status) => {
  switch (status) {
    case 'online': return 'success'
    case 'offline': return 'danger'
    default: return 'warning'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'online': return '在线'
    case 'offline': return '离线'
    default: return '未知'
  }
}

const getDeviceStatusType = (status) => {
  switch (status) {
    case 'available': return 'success'
    case 'in_use': return 'warning'
    case 'error': return 'danger'
    default: return 'info'
  }
}

const getDeviceStatusText = (status) => {
  switch (status) {
    case 'available': return '可用'
    case 'in_use': return '使用中'
    case 'error': return '错误'
    default: return '未知'
  }
}

const formatTime = (time) => {
  if (!time) return '无'
  return new Date(time).toLocaleString()
}

// 生命周期
onMounted(() => {
  loadServerInfo()
  refreshDevices()
})
</script>

<style scoped>
.slave-server-detail {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 10px 0 0 0;
  color: #303133;
}

.loading-container,
.error-container {
  padding: 40px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item.full-width {
  width: 100%;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  width: 100px;
  flex-shrink: 0;
}

.info-item span {
  color: #303133;
}

.control-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.empty-devices {
  padding: 40px;
}

code {
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}
</style>
