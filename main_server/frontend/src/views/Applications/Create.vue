<template>
  <div class="create-application-container">
    <div class="page-header">
      <h2 class="page-title">创建申请</h2>
      <el-button @click="$router.back()">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
    </div>
    
    <el-card class="form-card">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        size="large"
      >
        <el-form-item label="申请标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入申请标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="申请类型" prop="application_type">
          <el-select v-model="form.application_type" placeholder="请选择申请类型">
            <el-option label="权限申请" value="permission_request" />
            <el-option label="角色变更" value="role_change" />
            <el-option label="设备申请" value="device_request" />
            <el-option label="其他申请" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="申请接收人" prop="target_processor_type">
          <el-radio-group v-model="form.target_processor_type" @change="handleReceiverTypeChange">
            <el-radio label="auto">智能分配（推荐）</el-radio>
            <el-radio label="organization">向所在组织管理员申请</el-radio>
            <el-radio label="specific">指定特定管理员</el-radio>
          </el-radio-group>

          <!-- 智能分配说明 -->
          <div v-if="form.target_processor_type === 'auto'" class="receiver-info">
            <el-alert
              title="系统将自动为您查找最合适的管理员处理此申请"
              type="info"
              :closable="false"
              show-icon
            />
          </div>

          <!-- 组织管理员说明 -->
          <div v-if="form.target_processor_type === 'organization'" class="receiver-info">
            <el-alert
              v-if="organizationManagers.length > 0"
              :title="`将发送给您所在组织的 ${organizationManagers.length} 位管理员`"
              type="success"
              :closable="false"
              show-icon
            />
            <el-alert
              v-else
              title="您所在组织暂无管理员，系统将自动向上级组织查找"
              type="warning"
              :closable="false"
              show-icon
            />
          </div>

          <!-- 指定管理员选择 -->
          <div v-if="form.target_processor_type === 'specific'" class="receiver-info">
            <el-select
              v-model="form.target_processor_id"
              placeholder="请选择指定的管理员"
              filterable
              style="width: 100%; margin-top: 8px;"
            >
              <el-option
                v-for="manager in availableManagers"
                :key="manager.id"
                :label="`${manager.full_name} (${manager.role_name})`"
                :value="manager.id"
              >
                <div style="display: flex; justify-content: space-between;">
                  <span>{{ manager.full_name }}</span>
                  <span style="color: #8492a6; font-size: 13px;">{{ manager.role_name }}</span>
                </div>
              </el-option>
            </el-select>
          </div>
        </el-form-item>
        
        <el-form-item label="申请内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="6"
            placeholder="请详细描述您的申请内容、理由和期望结果..."
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="期望完成时间" prop="expected_completion_date">
          <el-date-picker
            v-model="form.expected_completion_date"
            type="date"
            placeholder="请选择期望完成时间"
            :disabled-date="disabledDate"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item label="附加说明">
          <el-input
            v-model="form.additional_notes"
            type="textarea"
            :rows="3"
            placeholder="其他需要说明的信息（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :loading="submitLoading"
            size="large"
          >
            提交申请
          </el-button>
          <el-button @click="handleReset" size="large">
            重置
          </el-button>
          <el-button @click="$router.back()" size="large">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { applicationRequestApi } from '@/api/applicationRequests'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const formRef = ref()
const submitLoading = ref(false)
const availableManagers = ref([])
const organizationManagers = ref([])

const form = reactive({
  title: '',
  application_type: '',
  target_processor_type: 'auto', // auto, organization, specific
  target_processor_id: null,
  content: '',
  expected_completion_date: '',
  additional_notes: ''
})

const rules = {
  title: [
    { required: true, message: '请输入申请标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度应在5-100个字符之间', trigger: 'blur' }
  ],
  application_type: [
    { required: true, message: '请选择申请类型', trigger: 'change' }
  ],
  target_processor_type: [
    { required: true, message: '请选择申请接收人类型', trigger: 'change' }
  ],
  target_processor_id: [
    {
      validator: (rule, value, callback) => {
        if (form.target_processor_type === 'specific' && !value) {
          callback(new Error('请选择指定的管理员'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  content: [
    { required: true, message: '请输入申请内容', trigger: 'blur' },
    { min: 20, max: 2000, message: '申请内容应在20-2000个字符之间', trigger: 'blur' }
  ],
  expected_completion_date: [
    { required: true, message: '请选择期望完成时间', trigger: 'change' }
  ]
}

// 禁用过去的日期
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7 // 禁用昨天及之前的日期
}

// 处理接收人类型变化
const handleReceiverTypeChange = (value) => {
  form.target_processor_id = null

  if (value === 'specific') {
    loadAvailableManagers()
  } else if (value === 'organization') {
    loadOrganizationManagers()
  }
}

// 加载可用管理员
const loadAvailableManagers = async () => {
  try {
    const response = await applicationRequestApi.getAvailableManagers()
    availableManagers.value = Array.isArray(response) ? response : (response.managers || [])
  } catch (error) {
    console.error('加载可用管理员失败:', error)
    ElMessage.error('加载可用管理员失败')
  }
}

// 加载组织管理员
const loadOrganizationManagers = async () => {
  try {
    // 这里可以调用专门的组织管理员API，暂时使用可用管理员API
    const response = await applicationRequestApi.getAvailableManagers()
    const allManagers = Array.isArray(response) ? response : (response.managers || [])

    // 过滤出当前用户组织的管理员
    const currentUserOrgId = userStore.user?.organization_id
    organizationManagers.value = allManagers.filter(manager =>
      manager.organization_id === currentUserOrgId && manager.permission_level <= 2
    )
  } catch (error) {
    console.error('加载组织管理员失败:', error)
    ElMessage.error('加载组织管理员失败')
  }
}

// 提交申请
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    const applicationData = {
      title: form.title,
      type: form.application_type, // 后端使用type字段
      priority: 'normal', // 固定为普通优先级
      content: form.content,
      expected_completion_date: form.expected_completion_date,
      additional_notes: form.additional_notes || null,
      // 根据接收人类型设置目标处理人
      target_processor_id: form.target_processor_type === 'specific' ? form.target_processor_id : null,
      receiver_type: form.target_processor_type // 传递接收人类型给后端
    }
    
    await applicationRequestApi.createApplicationRequest(applicationData)
    
    ElMessage.success('申请提交成功，请等待管理员审核')
    router.push('/applications')
    
  } catch (error) {
    console.error('提交申请失败:', error)
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else {
      ElMessage.error('提交申请失败，请重试')
    }
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const handleReset = () => {
  formRef.value.resetFields()
  Object.assign(form, {
    title: '',
    application_type: '',
    target_processor_type: 'auto',
    target_processor_id: null,
    content: '',
    expected_completion_date: '',
    additional_notes: ''
  })
  availableManagers.value = []
  organizationManagers.value = []
}

// 组件挂载时初始化
onMounted(() => {
  // 默认加载组织管理员信息用于显示
  loadOrganizationManagers()
})
</script>

<style scoped>
.create-application-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.form-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
  margin-top: 32px;
  text-align: center;
}

:deep(.el-button) {
  margin: 0 8px;
}

.receiver-info {
  margin-top: 12px;
}

.receiver-info .el-alert {
  margin-bottom: 8px;
}
</style>
