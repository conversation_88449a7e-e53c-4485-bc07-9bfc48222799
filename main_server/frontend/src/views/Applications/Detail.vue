<template>
  <div class="application-detail-container">
    <div class="page-header">
      <h2 class="page-title">申请详情</h2>
      <el-button @click="$router.back()">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
    </div>
    
    <div v-loading="loading" class="detail-content">
      <el-card v-if="application" class="detail-card">
        <!-- 申请基本信息 -->
        <div class="info-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="申请ID">
              {{ application.id }}
            </el-descriptions-item>
            <el-descriptions-item label="申请标题">
              {{ application.title }}
            </el-descriptions-item>
            <el-descriptions-item label="申请类型">
              <el-tag :type="getTypeTagType(application.application_type)">
                {{ getTypeText(application.application_type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTagType(application.status)">
                {{ getStatusText(application.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityTagType(application.priority)">
                {{ getPriorityText(application.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="申请人">
              {{ application.applicant_name }}
            </el-descriptions-item>
            <el-descriptions-item label="所属组织">
              {{ application.organization_name || '未指定' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(application.created_at) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <!-- 申请内容 -->
        <div class="info-section">
          <h3>申请内容</h3>
          <div class="content-box">
            {{ application.content }}
          </div>
        </div>
        
        <!-- 处理信息 -->
        <div v-if="application.status !== 'pending'" class="info-section">
          <h3>处理信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="处理人">
              {{ application.processor_name || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="处理时间">
              {{ formatDateTime(application.processed_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="处理结果" span="2">
              <el-tag :type="getStatusTagType(application.status)">
                {{ getStatusText(application.status) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
          
          <div v-if="application.response_content" class="response-section">
            <h4>处理意见</h4>
            <div class="content-box">
              {{ application.response_content }}
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-section">
          <el-button 
            v-if="application.status === 'pending' && userStore.hasPermission('application.process')"
            type="success" 
            @click="processApplication('approved')"
            :loading="processLoading"
          >
            批准申请
          </el-button>
          
          <el-button 
            v-if="application.status === 'pending' && userStore.hasPermission('application.process')"
            type="danger" 
            @click="processApplication('rejected')"
            :loading="processLoading"
          >
            拒绝申请
          </el-button>
          
          <el-button 
            v-if="application.applicant_id === userStore.userInfo?.id && application.status === 'pending'"
            type="warning" 
            @click="cancelApplication"
            :loading="cancelLoading"
          >
            取消申请
          </el-button>
        </div>
      </el-card>
    </div>
    
    <!-- 处理申请对话框 -->
    <el-dialog
      v-model="processDialogVisible"
      :title="processDialogTitle"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="processFormRef"
        :model="processForm"
        :rules="processRules"
        label-width="80px"
      >
        <el-form-item label="处理结果" prop="status">
          <el-radio-group v-model="processForm.status">
            <el-radio label="approved">批准</el-radio>
            <el-radio label="rejected">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="处理意见" prop="response_content">
          <el-input
            v-model="processForm.response_content"
            type="textarea"
            :rows="4"
            placeholder="请输入处理意见"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="processDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleProcessSubmit" :loading="processLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { applicationRequestApi } from '@/api/applicationRequests'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const processLoading = ref(false)
const cancelLoading = ref(false)
const application = ref(null)

// 处理申请对话框
const processDialogVisible = ref(false)
const processDialogTitle = ref('')
const processFormRef = ref()

const processForm = reactive({
  status: '',
  response_content: ''
})

const processRules = {
  status: [
    { required: true, message: '请选择处理结果', trigger: 'change' }
  ],
  response_content: [
    { required: true, message: '请输入处理意见', trigger: 'blur' }
  ]
}

// 加载申请详情
const loadApplication = async () => {
  try {
    loading.value = true
    const response = await applicationRequestApi.getApplicationRequest(route.params.id)
    application.value = response
  } catch (error) {
    console.error('加载申请详情失败:', error)
    ElMessage.error('加载申请详情失败')
    router.back()
  } finally {
    loading.value = false
  }
}

// 处理申请
const processApplication = (status) => {
  processForm.status = status
  processForm.response_content = ''
  processDialogTitle.value = status === 'approved' ? '批准申请' : '拒绝申请'
  processDialogVisible.value = true
}

// 提交处理结果
const handleProcessSubmit = async () => {
  try {
    await processFormRef.value.validate()
    
    processLoading.value = true
    
    await applicationRequestApi.processApplicationRequest(application.value.id, {
      status: processForm.status,
      process_comment: processForm.response_content
    })
    
    ElMessage.success('处理成功')
    processDialogVisible.value = false
    loadApplication() // 重新加载数据
    
  } catch (error) {
    console.error('处理申请失败:', error)
    ElMessage.error('处理申请失败')
  } finally {
    processLoading.value = false
  }
}

// 取消申请
const cancelApplication = async () => {
  try {
    await ElMessageBox.confirm('确定要取消这个申请吗？', '确认取消', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    cancelLoading.value = true
    
    await applicationRequestApi.processApplicationRequest(application.value.id, {
      status: 'cancelled',
      process_comment: '申请人主动取消'
    })
    
    ElMessage.success('申请已取消')
    loadApplication() // 重新加载数据
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消申请失败:', error)
      ElMessage.error('取消申请失败')
    }
  } finally {
    cancelLoading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    approved: '已批准',
    rejected: '已拒绝',
    cancelled: '已取消'
  }
  return texts[status] || status
}

// 获取类型标签类型
const getTypeTagType = (type) => {
  const types = {
    permission_request: 'primary',
    role_change: 'success',
    device_request: 'warning',
    other: 'info'
  }
  return types[type] || 'info'
}

// 获取类型文本
const getTypeText = (type) => {
  const texts = {
    permission_request: '权限申请',
    role_change: '角色变更',
    device_request: '设备申请',
    other: '其他'
  }
  return texts[type] || type
}

// 获取优先级标签类型
const getPriorityTagType = (priority) => {
  const types = {
    low: 'info',
    normal: 'primary',
    high: 'warning',
    urgent: 'danger'
  }
  return types[priority] || 'primary'
}

// 获取优先级文本
const getPriorityText = (priority) => {
  const texts = {
    low: '低',
    normal: '普通',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

onMounted(() => {
  loadApplication()
})
</script>

<style scoped>
.application-detail-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.detail-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.info-section {
  margin-bottom: 30px;
}

.info-section h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.info-section h4 {
  margin: 15px 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #606266;
}

.content-box {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.response-section {
  margin-top: 15px;
}

.action-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

.action-section .el-button {
  margin: 0 8px;
}
</style>
