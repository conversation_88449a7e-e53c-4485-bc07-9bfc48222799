<template>
  <div class="error-container">
    <div class="error-content">
      <div class="error-code">403</div>
      <div class="error-title">访问被拒绝</div>
      <div class="error-description">
        抱歉，您没有权限访问此页面。请联系管理员获取相应权限。
      </div>
      <div class="error-actions">
        <el-button type="primary" @click="goBack">返回上一页</el-button>
        <el-button @click="goHome">回到首页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

const goBack = () => {
  // 🔧 修复：智能返回逻辑
  if (window.history.length > 1) {
    // 检查上一页是否是403页面，避免无限循环
    const referrer = document.referrer
    if (referrer && !referrer.includes('/403')) {
      router.go(-1)
      return
    }
  }

  // 如果无法安全返回，跳转到登录页面
  router.push('/login')
}

const goHome = () => {
  // 🔧 修复：根据登录状态决定跳转目标
  if (userStore.isLoggedIn && userStore.userInfo) {
    // 已登录用户跳转到工作台
    router.push('/dashboard')
  } else {
    // 未登录用户跳转到登录页面
    router.push('/login')
  }
}
</script>

<style scoped>
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.error-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.error-code {
  font-size: 120px;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 20px;
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.error-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}
</style>
