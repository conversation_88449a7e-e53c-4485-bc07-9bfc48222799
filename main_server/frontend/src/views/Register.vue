<template>
  <div class="register-container">
    <div class="register-box">
      <!-- 系统标题 -->
      <div class="logo-section">
        <h1 class="logo-text">OmniLink 全联通系统</h1>
        <p class="logo-subtitle">用户注册</p>
      </div>

      <!-- 注册表单 -->
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="rules"
        label-width="80px"
        size="large"
        @submit.prevent="handleRegister"
      >
        <!-- 用户名 -->
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名（3-50位字母、数字、下划线）"
            @blur="checkUsername"
          >
            <template #suffix>
              <el-icon v-if="usernameChecking" class="is-loading">
                <Loading />
              </el-icon>
              <el-icon v-else-if="usernameStatus === 'success'" style="color: #67c23a">
                <Check />
              </el-icon>
              <el-icon v-else-if="usernameStatus === 'error'" style="color: #f56c6c">
                <Close />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 密码 -->
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码（至少6位，包含字母和数字）"
            show-password
          />
        </el-form-item>

        <!-- 确认密码 -->
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            show-password
          />
        </el-form-item>

        <!-- 姓名 -->
        <el-form-item label="姓名" prop="fullName">
          <el-input
            v-model="registerForm.fullName"
            placeholder="请输入真实姓名"
          />
        </el-form-item>

        <!-- 手机号 -->
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="registerForm.phone"
            placeholder="请输入手机号"
            @blur="checkPhone"
          >
            <template #suffix>
              <el-icon v-if="phoneChecking" class="is-loading">
                <Loading />
              </el-icon>
              <el-icon v-else-if="phoneStatus === 'success'" style="color: #67c23a">
                <Check />
              </el-icon>
              <el-icon v-else-if="phoneStatus === 'error'" style="color: #f56c6c">
                <Close />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 邮箱（可选） -->
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="registerForm.email"
            placeholder="请输入邮箱（可选）"
            @blur="checkEmail"
          >
            <template #suffix>
              <el-icon v-if="emailChecking" class="is-loading">
                <Loading />
              </el-icon>
              <el-icon v-else-if="emailStatus === 'success'" style="color: #67c23a">
                <Check />
              </el-icon>
              <el-icon v-else-if="emailStatus === 'error'" style="color: #f56c6c">
                <Close />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 备注（可选） -->
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="registerForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <!-- 注册按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="registerLoading"
            @click="handleRegister"
          >
            {{ registerLoading ? '注册中...' : '注册' }}
          </el-button>
        </el-form-item>

        <!-- 返回登录 -->
        <el-form-item>
          <div class="back-to-login">
            <span>已有账户？</span>
            <el-button type="text" @click="goToLogin">返回登录</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 注册成功对话框 -->
    <el-dialog
      v-model="showSuccessDialog"
      title="注册成功"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="success-content">
        <el-icon size="60" style="color: #67c23a; margin-bottom: 16px">
          <CircleCheck />
        </el-icon>
        <p>{{ successMessage }}</p>
        <p style="color: #909399; font-size: 14px; margin-top: 16px">
          您的账户已提交审核，请等待管理员激活后即可正常使用系统。
        </p>
      </div>
      <template #footer>
        <el-button type="primary" @click="goToLogin">返回登录</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Loading, Check, Close, CircleCheck 
} from '@element-plus/icons-vue'
import { registerUser, checkUsernameAvailability, checkPhoneAvailability, checkEmailAvailability } from '@/api/auth'

const router = useRouter()

// 表单引用
const registerFormRef = ref()

// 加载状态
const registerLoading = ref(false)
const usernameChecking = ref(false)
const phoneChecking = ref(false)
const emailChecking = ref(false)

// 验证状态
const usernameStatus = ref('')
const phoneStatus = ref('')
const emailStatus = ref('')

// 成功对话框
const showSuccessDialog = ref(false)
const successMessage = ref('')

// 注册表单数据
const registerForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  fullName: '',
  phone: '',
  email: '',
  notes: ''
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度应在3-50个字符之间', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '用户名只能包含英文字母、数字、下划线(_)和连字符(-)', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!/^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+$/.test(value)) {
          callback(new Error('密码包含不允许的字符'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  fullName: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度应在2-50个字符之间', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  email: [
    { 
      validator: (rule, value, callback) => {
        if (value && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
          callback(new Error('请输入正确的邮箱格式'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  notes: [
    { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
  ]
}

// 检查用户名可用性
const checkUsername = async () => {
  if (!registerForm.username || registerForm.username.length < 3) {
    usernameStatus.value = ''
    return
  }

  usernameChecking.value = true
  usernameStatus.value = ''

  try {
    const response = await checkUsernameAvailability(registerForm.username)
    if (response.available) {
      usernameStatus.value = 'success'
    } else {
      usernameStatus.value = 'error'
      ElMessage.warning(response.message)
    }
  } catch (error) {
    usernameStatus.value = 'error'
    console.error('检查用户名失败:', error)
  } finally {
    usernameChecking.value = false
  }
}

// 检查手机号可用性
const checkPhone = async () => {
  if (!registerForm.phone || !/^1[3-9]\d{9}$/.test(registerForm.phone)) {
    phoneStatus.value = ''
    return
  }

  phoneChecking.value = true
  phoneStatus.value = ''

  try {
    const response = await checkPhoneAvailability(registerForm.phone)
    if (response.available) {
      phoneStatus.value = 'success'
    } else {
      phoneStatus.value = 'error'
      ElMessage.warning(response.message)
    }
  } catch (error) {
    phoneStatus.value = 'error'
    console.error('检查手机号失败:', error)
  } finally {
    phoneChecking.value = false
  }
}

// 检查邮箱可用性
const checkEmail = async () => {
  if (!registerForm.email) {
    emailStatus.value = 'success'
    return
  }

  if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(registerForm.email)) {
    emailStatus.value = ''
    return
  }

  emailChecking.value = true
  emailStatus.value = ''

  try {
    const response = await checkEmailAvailability(registerForm.email)
    if (response.available) {
      emailStatus.value = 'success'
    } else {
      emailStatus.value = 'error'
      ElMessage.warning(response.message)
    }
  } catch (error) {
    emailStatus.value = 'error'
    console.error('检查邮箱失败:', error)
  } finally {
    emailChecking.value = false
  }
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    // 表单验证
    await registerFormRef.value.validate()

    // 检查唯一性验证状态
    if (usernameStatus.value === 'error') {
      ElMessage.error('用户名已存在，请更换')
      return
    }
    if (phoneStatus.value === 'error') {
      ElMessage.error('手机号已被注册，请更换')
      return
    }
    if (emailStatus.value === 'error') {
      ElMessage.error('邮箱已被注册，请更换')
      return
    }

    registerLoading.value = true

    // 提交注册请求
    const response = await registerUser({
      username: registerForm.username,
      password: registerForm.password,
      confirm_password: registerForm.confirmPassword,
      full_name: registerForm.fullName,
      phone: registerForm.phone,
      email: registerForm.email || null,
      notes: registerForm.notes || null
    })

    if (response.success) {
      successMessage.value = response.message
      showSuccessDialog.value = true
    } else {
      ElMessage.error(response.message || '注册失败')
    }

  } catch (error) {
    console.error('注册失败:', error)
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else {
      ElMessage.error('注册失败，请稍后重试')
    }
  } finally {
    registerLoading.value = false
  }
}

// 返回登录页面
const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-box {
  background: white;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.logo-section {
  text-align: center;
  margin-bottom: 40px;
}

.logo-text {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-subtitle {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
  font-weight: 500;
}

.back-to-login {
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.success-content {
  text-align: center;
  padding: 20px 0;
}

.success-content p {
  margin: 8px 0;
  font-size: 16px;
  color: #606266;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__inner) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}

/* 加载动画 */
.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .register-box {
    padding: 30px 20px;
    margin: 10px;
  }

  .logo-text {
    font-size: 24px;
  }

  .logo-subtitle {
    font-size: 14px;
  }
}
</style>
