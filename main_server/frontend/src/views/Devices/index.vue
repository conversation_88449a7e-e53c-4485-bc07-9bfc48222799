<template>
  <div class="slave-server-management">
    <!-- 页面标题和工具栏 -->
    <div class="page-header">
      <div class="header-content">
        <h2>从服务器管理中心</h2>
        <p>统一管理所有从服务器和USB设备</p>
      </div>
      <div class="header-actions">
        <!-- 显示模式切换 -->
        <el-radio-group v-model="displayMode" class="mode-switch" style="margin-right: 12px;">
          <el-radio-button label="group">按分组模式</el-radio-button>
          <el-radio-button label="server">按从服务器模式</el-radio-button>
        </el-radio-group>

        <el-input
          v-model="searchKeyword"
          placeholder="搜索设备或从服务器..."
          style="width: 300px; margin-right: 12px;"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button
          type="primary"
          @click="refreshData"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button
          type="success"
          @click="showCreateGroupDialog = true"
          v-if="userStore.hasPermission('device.group')"
        >
          <el-icon><Plus /></el-icon>
          创建分组
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon online">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalServers }}</div>
                <div class="stat-label">从服务器总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon success">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.onlineServers }}</div>
                <div class="stat-label">在线服务器</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon primary">
                <el-icon><Cpu /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalDevices }}</div>
                <div class="stat-label">USB设备总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon warning">
                <el-icon><Collection /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.deviceGroups }}</div>
                <div class="stat-label">设备分组</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="16">
        <!-- 左侧：从服务器列表 -->
        <el-col :span="6">
          <el-card class="server-list-card">
            <template #header>
              <div class="card-header">
                <span>从服务器列表</span>
                <el-badge :value="stats.onlineServers" :max="99" class="badge">
                  <el-icon><Monitor /></el-icon>
                </el-badge>
              </div>
            </template>
            <div class="server-list">
              <div
                v-for="server in filteredServers"
                :key="server.id"
                class="server-item"
                :class="{ 'active': selectedServerId === server.id, 'offline': server.status !== 'online' }"
                @click="selectServer(server)"
              >
                <div class="server-info">
                  <div class="server-name">{{ server.name }}</div>
                  <div class="server-address">{{ server.ip_address }}:{{ server.port }}</div>
                </div>
                <div class="server-status">
                  <el-tag
                    :type="server.status === 'online' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ server.status === 'online' ? '在线' : '离线' }}
                  </el-tag>
                </div>
              </div>
              <div v-if="filteredServers.length === 0" class="empty-state">
                <el-empty description="暂无从服务器" />
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 中间：设备列表 -->
        <el-col :span="12">
          <el-card class="device-list-card">
            <template #header>
              <div class="card-header">
                <span>
                  {{ selectedServer ? `${selectedServer.name} - 设备列表` : '所有设备' }}
                </span>
                <div class="header-actions">
                  <el-select
                    v-model="deviceFilter"
                    placeholder="筛选设备类型"
                    style="width: 150px; margin-right: 8px;"
                    clearable
                  >
                    <el-option label="全部" value="" />
                    <el-option label="加密锁" value="dongle" />
                    <el-option label="打印机" value="printer" />
                    <el-option label="摄像头" value="camera" />
                    <el-option label="其他" value="other" />
                  </el-select>
                </div>
              </div>
            </template>
            <div class="device-list">
              <el-table
                :data="filteredDevices"
                style="width: 100%"
                :loading="deviceLoading"
                @selection-change="handleDeviceSelection"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="device_name" label="设备名称" min-width="120">
                  <template #default="scope">
                    <div class="device-name">
                      <el-icon class="device-icon"><Cpu /></el-icon>
                      {{ scope.row.device_name || '未知设备' }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="device_type" label="类型" width="100">
                  <template #default="scope">
                    <el-tag size="small">{{ getDeviceTypeLabel(scope.row.device_type) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="80">
                  <template #default="scope">
                    <el-tag
                      :type="scope.row.status === 'available' ? 'success' : 'warning'"
                      size="small"
                    >
                      {{ scope.row.status === 'available' ? '可用' : '占用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="vendor_id" label="VID" width="80" />
                <el-table-column prop="product_id" label="PID" width="80" />
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-button
                      size="small"
                      @click="viewDeviceDetail(scope.row)"
                    >
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：详情和操作面板 -->
        <el-col :span="6">
          <el-card class="detail-panel">
            <template #header>
              <div class="card-header">
                <span>详情面板</span>
              </div>
            </template>

            <!-- 从服务器详情 -->
            <div v-if="selectedServer" class="server-detail">
              <h4>从服务器信息</h4>
              <div class="detail-item">
                <label>名称：</label>
                <span>{{ selectedServer.name }}</span>
              </div>
              <div class="detail-item">
                <label>地址：</label>
                <span>{{ selectedServer.ip_address }}:{{ selectedServer.port }}</span>
              </div>
              <div class="detail-item">
                <label>状态：</label>
                <el-tag :type="selectedServer.status === 'online' ? 'success' : 'danger'">
                  {{ selectedServer.status === 'online' ? '在线' : '离线' }}
                </el-tag>
              </div>
              <div class="detail-item">
                <label>最后心跳：</label>
                <span>{{ formatTime(selectedServer.last_seen) }}</span>
              </div>
              <div class="detail-item">
                <label>设备数量：</label>
                <span>{{ getServerDeviceCount(selectedServer.id) }}</span>
              </div>

              <div class="server-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click="viewServerDetail(selectedServer)"
                >
                  查看详情
                </el-button>
                <el-button
                  type="warning"
                  size="small"
                  @click="restartServer(selectedServer)"
                  v-if="userStore.hasPermission('slave.manage')"
                >
                  重启服务
                </el-button>
              </div>
            </div>

            <!-- 设备详情 -->
            <div v-if="selectedDevice" class="device-detail">
              <h4>设备信息</h4>
              <div class="detail-item">
                <label>设备名称：</label>
                <span>{{ selectedDevice.device_name || '未知设备' }}</span>
              </div>
              <div class="detail-item">
                <label>设备类型：</label>
                <span>{{ getDeviceTypeLabel(selectedDevice.device_type) }}</span>
              </div>
              <div class="detail-item">
                <label>厂商ID：</label>
                <span>{{ selectedDevice.vendor_id || 'N/A' }}</span>
              </div>
              <div class="detail-item">
                <label>产品ID：</label>
                <span>{{ selectedDevice.product_id || 'N/A' }}</span>
              </div>
              <div class="detail-item">
                <label>状态：</label>
                <el-tag :type="selectedDevice.status === 'available' ? 'success' : 'warning'">
                  {{ selectedDevice.status === 'available' ? '可用' : '占用' }}
                </el-tag>
              </div>
              <div class="detail-item">
                <label>物理位置：</label>
                <span>{{ selectedDevice.physical_address || 'N/A' }}</span>
              </div>
            </div>

            <!-- 批量操作 -->
            <div v-if="selectedDevices.length > 0" class="batch-operations">
              <h4>批量操作</h4>
              <div class="batch-info">
                已选择 {{ selectedDevices.length }} 个设备
              </div>
              <div class="batch-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click="showGroupDialog = true"
                >
                  添加到分组
                </el-button>
                <el-button
                  type="warning"
                  size="small"
                  @click="batchUpdateStatus"
                >
                  批量更新状态
                </el-button>
              </div>
            </div>

            <!-- 设备分组管理 -->
            <div class="group-management">
              <h4>设备分组</h4>
              <div class="group-list">
                <div
                  v-for="group in deviceGroups"
                  :key="group.id"
                  class="group-item"
                  @click="selectGroup(group)"
                >
                  <div class="group-info">
                    <div class="group-name">{{ group.name }}</div>
                    <div class="group-count">{{ group.device_count || 0 }} 个设备</div>
                  </div>
                  <el-button
                    size="small"
                    type="text"
                    @click.stop="editGroup(group)"
                  >
                    编辑
                  </el-button>
                </div>
              </div>
              <el-button
                type="primary"
                size="small"
                style="width: 100%; margin-top: 12px;"
                @click="showCreateGroupDialog = true"
                v-if="userStore.hasPermission('device.group')"
              >
                创建新分组
              </el-button>
            </div>

            <!-- 空状态 -->
            <div v-if="!selectedServer && !selectedDevice && selectedDevices.length === 0" class="empty-detail">
              <el-empty description="请选择从服务器或设备查看详情" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 创建设备分组对话框 -->
    <el-dialog
      v-model="showCreateGroupDialog"
      title="创建设备分组"
      width="600px"
    >
      <el-form :model="groupForm" :rules="groupRules" ref="groupFormRef" label-width="100px">
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="groupForm.name" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="分组描述" prop="description">
          <el-input
            v-model="groupForm.description"
            type="textarea"
            placeholder="请输入分组描述"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="分组类型" prop="group_type">
          <el-select v-model="groupForm.group_type" placeholder="请选择分组类型">
            <el-option label="按服务器分组" value="server" />
            <el-option label="按设备类型分组" value="type" />
            <el-option label="自定义分组" value="custom" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateGroupDialog = false">取消</el-button>
        <el-button type="primary" @click="createGroup" :loading="groupLoading">创建</el-button>
      </template>
    </el-dialog>

    <!-- 设备分组对话框 -->
    <el-dialog
      v-model="showGroupDialog"
      title="添加设备到分组"
      width="500px"
    >
      <el-form label-width="100px">
        <el-form-item label="选择分组">
          <el-select v-model="selectedGroupId" placeholder="请选择设备分组">
            <el-option
              v-for="group in deviceGroups"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选中设备">
          <div class="selected-devices">
            <el-tag
              v-for="device in selectedDevices"
              :key="device.id"
              style="margin: 2px;"
            >
              {{ device.device_name || '未知设备' }}
            </el-tag>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showGroupDialog = false">取消</el-button>
        <el-button type="primary" @click="addDevicesToGroup" :loading="groupLoading">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import api from '@/api'

// 引入图标
import {
  Search, Refresh, Plus, Monitor, Connection, Cpu, Collection
} from '@element-plus/icons-vue'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const deviceLoading = ref(false)
const groupLoading = ref(false)
const searchKeyword = ref('')
const deviceFilter = ref('')
const selectedServerId = ref(null)
const selectedDevice = ref(null)
const selectedDevices = ref([])
const selectedGroupId = ref(null)
const displayMode = ref('group') // 显示模式：group(按分组) 或 server(按从服务器)

// 对话框控制
const showCreateGroupDialog = ref(false)
const showGroupDialog = ref(false)

// 数据
const slaveServers = ref([])
const allDevices = ref([])
const deviceGroups = ref([])

// 表单数据
const groupForm = ref({
  name: '',
  description: '',
  group_type: 'custom'
})

const groupRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' }
  ],
  group_type: [
    { required: true, message: '请选择分组类型', trigger: 'change' }
  ]
}

// 计算属性
const selectedServer = computed(() => {
  return slaveServers.value.find(server => server.id === selectedServerId.value)
})

const filteredServers = computed(() => {
  if (!searchKeyword.value) return slaveServers.value
  return slaveServers.value.filter(server =>
    server.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    server.ip_address.includes(searchKeyword.value)
  )
})

const filteredDevices = computed(() => {
  let devices = allDevices.value

  // 按从服务器筛选
  if (selectedServerId.value) {
    devices = devices.filter(device => device.slave_server_id === selectedServerId.value)
  }

  // 按设备类型筛选
  if (deviceFilter.value) {
    devices = devices.filter(device => device.device_type === deviceFilter.value)
  }

  // 按搜索关键词筛选
  if (searchKeyword.value) {
    devices = devices.filter(device =>
      (device.device_name || '').toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      (device.vendor_id || '').includes(searchKeyword.value) ||
      (device.product_id || '').includes(searchKeyword.value)
    )
  }

  return devices
})

const stats = computed(() => {
  return {
    totalServers: slaveServers.value.length,
    onlineServers: slaveServers.value.filter(s => s.status === 'online').length,
    totalDevices: allDevices.value.length,
    deviceGroups: deviceGroups.value.length
  }
})

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadSlaveServers(),
      loadAllDevices(),
      loadDeviceGroups()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    loading.value = false
  }
}

const loadSlaveServers = async () => {
  try {
    const response = await api.get('/api/v1/slave/list')
    if (response.data.success) {
      slaveServers.value = response.data.data || []
    }
  } catch (error) {
    console.error('加载从服务器列表失败:', error)
    ElMessage.error('加载从服务器列表失败')
  }
}

const loadAllDevices = async () => {
  try {
    const devices = []
    for (const server of slaveServers.value) {
      try {
        const response = await api.get(`/api/v1/slave/${server.id}/devices`)
        if (response.data.success && response.data.data) {
          const serverDevices = response.data.data.map(device => ({
            ...device,
            slave_server_id: server.id,
            slave_server_name: server.name
          }))
          devices.push(...serverDevices)
        }
      } catch (error) {
        console.error(`加载服务器 ${server.name} 的设备失败:`, error)
      }
    }
    allDevices.value = devices
  } catch (error) {
    console.error('加载设备列表失败:', error)
  }
}

const loadDeviceGroups = async () => {
  try {
    const response = await api.get('/api/v1/device-groups/')
    if (response.data) {
      deviceGroups.value = response.data || []
    }
  } catch (error) {
    console.error('加载设备分组失败:', error)
  }
}

const selectServer = (server) => {
  selectedServerId.value = server.id
  selectedDevice.value = null
  selectedDevices.value = []
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleDeviceSelection = (selection) => {
  selectedDevices.value = selection
  if (selection.length === 1) {
    selectedDevice.value = selection[0]
  } else {
    selectedDevice.value = null
  }
}

const viewDeviceDetail = (device) => {
  selectedDevice.value = device
  selectedDevices.value = [device]
}

const viewServerDetail = (server) => {
  router.push(`/slave-servers/${server.id}`)
}

const restartServer = async (server) => {
  try {
    await ElMessageBox.confirm(
      `确定要重启从服务器 "${server.name}" 吗？`,
      '确认重启',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用重启API
    const response = await api.post(`/api/v1/slave/${server.id}/control`, {
      action: 'restart',
      parameters: {}
    })

    if (response.data.status === 'success') {
      ElMessage.success(`从服务器 "${server.name}" 重启指令已发送`)
      // 刷新从服务器状态
      await loadSlaveServers()
    } else {
      ElMessage.error(response.data.message || '重启失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重启从服务器失败:', error)
      ElMessage.error('重启失败')
    }
  }
}

const getDeviceTypeLabel = (type) => {
  const typeMap = {
    'dongle': '加密锁',
    'printer': '打印机',
    'camera': '摄像头',
    'scanner': '扫描仪',
    'other': '其他'
  }
  return typeMap[type] || type || '未知'
}

const getServerDeviceCount = (serverId) => {
  return allDevices.value.filter(device => device.slave_server_id === serverId).length
}

const formatTime = (timeString) => {
  if (!timeString) return 'N/A'
  return new Date(timeString).toLocaleString('zh-CN')
}

const createGroup = async () => {
  try {
    groupLoading.value = true
    const response = await api.post('/api/v1/device-groups/', groupForm.value)
    if (response.data.status === 'success') {
      ElMessage.success('设备分组创建成功')
      showCreateGroupDialog.value = false
      groupForm.value = { name: '', description: '', group_type: 'custom' }
      await loadDeviceGroups()
    }
  } catch (error) {
    console.error('创建设备分组失败:', error)
    ElMessage.error('创建设备分组失败')
  } finally {
    groupLoading.value = false
  }
}

const addDevicesToGroup = async () => {
  if (!selectedGroupId.value || selectedDevices.value.length === 0) {
    ElMessage.warning('请选择分组和设备')
    return
  }

  try {
    groupLoading.value = true
    const deviceIds = selectedDevices.value.map(device => device.id)
    const response = await api.post(`/api/v1/device-groups/${selectedGroupId.value}/devices/`, {
      device_ids: deviceIds
    })

    if (response.data.status === 'success') {
      ElMessage.success('设备已添加到分组')
      showGroupDialog.value = false
      selectedGroupId.value = null
      selectedDevices.value = []
      await loadDeviceGroups()
    }
  } catch (error) {
    console.error('添加设备到分组失败:', error)
    ElMessage.error('添加设备到分组失败')
  } finally {
    groupLoading.value = false
  }
}

const selectGroup = (group) => {
  // 可以实现分组选择逻辑
  ElMessage.info(`选择了分组: ${group.name}`)
}

const editGroup = (group) => {
  // 可以实现分组编辑逻辑
  ElMessage.info(`编辑分组: ${group.name}`)
}

const batchUpdateStatus = () => {
  ElMessage.info('批量更新状态功能开发中...')
}

// 监听从服务器变化，自动加载设备
watch(slaveServers, async (newServers) => {
  if (newServers.length > 0) {
    await loadAllDevices()
  }
}, { deep: true })

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.slave-server-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  overflow-y: auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  align-items: center;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  font-size: 32px;
  opacity: 0.8;
}

.stat-icon.online {
  color: #67c23a;
}

.stat-icon.success {
  color: #67c23a;
}

.stat-icon.primary {
  color: #409eff;
}

.stat-icon.warning {
  color: #e6a23c;
}

.main-content {
  margin-top: 20px;
}

.server-list-card,
.device-list-card,
.detail-panel {
  height: 600px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.badge {
  margin-left: 8px;
}

.server-list {
  height: calc(100vh - 300px);
  min-height: 400px;
  overflow-y: auto;
}

.server-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.server-item:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
}

.server-item.active {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.server-item.offline {
  opacity: 0.6;
}

.server-info {
  flex: 1;
}

.server-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.server-address {
  font-size: 12px;
  color: #909399;
}

.device-list {
  height: calc(100vh - 300px);
  min-height: 400px;
  overflow-y: auto;
}

.device-name {
  display: flex;
  align-items: center;
}

.device-icon {
  margin-right: 8px;
  color: #409eff;
}

.detail-panel {
  padding: 0;
}

.detail-panel .el-card__body {
  padding: 20px;
  height: calc(100vh - 300px);
  min-height: 400px;
  overflow-y: auto;
}

.server-detail,
.device-detail,
.batch-operations,
.group-management {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.server-detail:last-child,
.device-detail:last-child,
.batch-operations:last-child,
.group-management:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.server-detail h4,
.device-detail h4,
.batch-operations h4,
.group-management h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.detail-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.detail-item span {
  color: #303133;
  text-align: right;
  word-break: break-all;
}

.server-actions,
.batch-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.batch-info {
  background-color: #f0f9ff;
  color: #1d4ed8;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 12px;
}

.group-list {
  max-height: 200px;
  overflow-y: auto;
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.group-item:hover {
  background-color: #e9ecef;
}

.group-info {
  flex: 1;
}

.group-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.group-count {
  font-size: 12px;
  color: #909399;
}

.empty-state,
.empty-detail {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.selected-devices {
  max-height: 120px;
  overflow-y: auto;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content .el-col {
    margin-bottom: 16px;
  }

  .server-list-card,
  .device-list-card,
  .detail-panel {
    height: auto;
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .stats-section .el-col {
    margin-bottom: 12px;
  }
}
</style>
