<template>
  <div class="device-groups-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>设备分组管理</h2>
      <p>管理设备分组和用户权限分配</p>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button 
        type="primary" 
        @click="refreshList"
        :loading="loading"
      >
        <el-icon><Refresh /></el-icon>
        刷新列表
      </el-button>
      <el-button 
        type="success" 
        @click="showCreateDialog = true"
        v-if="userStore.hasPermission('device.group')"
      >
        <el-icon><Plus /></el-icon>
        创建分组
      </el-button>
    </div>

    <!-- 分组类型统计 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.total }}</div>
              <div class="stat-label">总分组数</div>
            </div>
            <el-icon class="stat-icon"><Collection /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card server">
            <div class="stat-content">
              <div class="stat-number">{{ stats.server }}</div>
              <div class="stat-label">服务器分组</div>
            </div>
            <el-icon class="stat-icon"><Monitor /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card mixed">
            <div class="stat-content">
              <div class="stat-number">{{ stats.mixed }}</div>
              <div class="stat-label">混合分组</div>
            </div>
            <el-icon class="stat-icon"><Connection /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card single">
            <div class="stat-content">
              <div class="stat-number">{{ stats.single }}</div>
              <div class="stat-label">单设备分组</div>
            </div>
            <el-icon class="stat-icon"><Monitor /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 设备分组列表 -->
    <el-card class="list-card">
      <el-table 
        :data="deviceGroups" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="分组名称" width="150" />
        <el-table-column label="分组类型" width="120">
          <template #default="{ row }">
            <el-tag 
              :type="getGroupTypeColor(row.group_type)"
              size="small"
            >
              {{ getGroupTypeText(row.group_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="设备数量" width="100">
          <template #default="{ row }">
            <span>{{ row.device_count || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="虚拟设备" width="100">
          <template #default="{ row }">
            <el-tag 
              v-if="row.has_virtual_devices"
              type="warning"
              size="small"
            >
              有占位
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="150" />
        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewGroup(row)"
            >
              详情
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              @click="editGroup(row)"
              v-if="userStore.hasPermission('device.group')"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteGroup(row)"
              v-if="userStore.hasPermission('device.group')"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建分组对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建设备分组"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
      >
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="分组类型" prop="group_type">
          <el-select v-model="createForm.group_type" placeholder="请选择分组类型">
            <el-option label="按服务器分组" value="server" />
            <el-option label="跨服务器混合分组" value="mixed" />
            <el-option label="单设备分组" value="single" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="createForm.description" 
            type="textarea" 
            placeholder="请输入分组描述"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="自动添加虚拟设备" prop="auto_virtual">
          <el-switch 
            v-model="createForm.auto_virtual"
            active-text="是"
            inactive-text="否"
          />
          <div class="form-tip">
            开启后，创建分组时会自动添加虚拟设备占位，添加真实设备后自动删除
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreate" :loading="createLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑分组对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑设备分组"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="createRules"
        label-width="120px"
      >
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="分组类型" prop="group_type">
          <el-select v-model="editForm.group_type" placeholder="请选择分组类型" disabled>
            <el-option label="按服务器分组" value="server" />
            <el-option label="跨服务器混合分组" value="mixed" />
            <el-option label="单设备分组" value="single" />
          </el-select>
          <div class="form-tip">分组类型创建后不可修改</div>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="editForm.description" 
            type="textarea" 
            placeholder="请输入分组描述"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleEdit" :loading="editLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, 
  Plus, 
  Collection,
  Monitor,
  Connection
} from '@element-plus/icons-vue'
import { getDeviceGroups, createDeviceGroup, updateDeviceGroup, deleteDeviceGroup } from '@/api/permission-assignment.js'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const editLoading = ref(false)
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const deviceGroups = ref([])

// 统计数据
const stats = computed(() => {
  const total = deviceGroups.value.length
  const server = deviceGroups.value.filter(g => g.group_type === 'server').length
  const mixed = deviceGroups.value.filter(g => g.group_type === 'mixed').length
  const single = deviceGroups.value.filter(g => g.group_type === 'single').length
  
  return { total, server, mixed, single }
})

// 创建表单
const createFormRef = ref()
const createForm = reactive({
  name: '',
  group_type: '',
  description: '',
  auto_virtual: true
})

// 编辑表单
const editFormRef = ref()
const editForm = reactive({
  id: null,
  name: '',
  group_type: '',
  description: ''
})

const createRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' }
  ],
  group_type: [
    { required: true, message: '请选择分组类型', trigger: 'change' }
  ]
}

// 方法
const refreshList = async () => {
  loading.value = true
  try {
    // 调用获取设备分组列表API
    const response = await fetch('/api/v1/device-groups/')
    if (response.ok) {
      const data = await response.json()
      deviceGroups.value = data || []
      ElMessage.success(`成功加载 ${data.length} 个设备分组`)
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
  } catch (error) {
    ElMessage.error('获取设备分组列表失败')
    console.error('获取设备分组列表失败:', error)
    deviceGroups.value = []
  } finally {
    loading.value = false
  }
}

const getGroupTypeColor = (type) => {
  switch (type) {
    case 'server': return 'success'
    case 'mixed': return 'warning'
    case 'single': return 'info'
    default: return ''
  }
}

const getGroupTypeText = (type) => {
  switch (type) {
    case 'server': return '服务器分组'
    case 'mixed': return '混合分组'
    case 'single': return '单设备分组'
    default: return '未知'
  }
}

const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

const viewGroup = (row) => {
  router.push(`/device-groups/${row.id}`)
}

const editGroup = (row) => {
  editForm.id = row.id
  editForm.name = row.name
  editForm.group_type = row.group_type
  editForm.description = row.description
  showEditDialog.value = true
}

const deleteGroup = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备分组 "${row.name}" 吗？此操作将同时删除分组中的所有虚拟设备占位符。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 调用删除设备分组API
    const response = await fetch(`/api/v1/device-groups/${row.id}`, {
      method: 'DELETE'
    })

    if (response.ok) {
      ElMessage.success(`设备分组 "${row.name}" 删除成功`)
      refreshList()
    } else {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`删除失败: ${error.message}`)
      console.error('删除设备分组失败:', error)
    }
  }
}

const handleCreate = async () => {
  if (!createFormRef.value) return

  const valid = await createFormRef.value.validate().catch(() => false)
  if (!valid) return

  createLoading.value = true
  try {
    // 调用创建设备分组API
    const response = await fetch('/api/v1/device-groups/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: createForm.name,
        group_type: createForm.group_type,
        description: createForm.description,
        auto_virtual: createForm.auto_virtual
      })
    })

    if (response.ok) {
      const result = await response.json()
      ElMessage.success(`设备分组 "${createForm.name}" 创建成功`)
      showCreateDialog.value = false
      refreshList()

      // 重置表单
      Object.assign(createForm, {
        name: '',
        group_type: '',
        description: '',
        auto_virtual: true
      })
    } else {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }
  } catch (error) {
    ElMessage.error(`创建失败: ${error.message}`)
    console.error('创建设备分组失败:', error)
  } finally {
    createLoading.value = false
  }
}

const handleEdit = async () => {
  if (!editFormRef.value) return
  
  const valid = await editFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  editLoading.value = true
  try {
    // 调用更新设备分组API
    await updateDeviceGroup(editForm.id, editForm)
    ElMessage.success('更新成功')
    showEditDialog.value = false
    refreshList()
  } catch (error) {
    ElMessage.error('更新失败')
    console.error('更新设备分组失败:', error)
  } finally {
    editLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  refreshList()
})
</script>

<style scoped>
.device-groups-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card.server {
  border-left: 4px solid #67c23a;
}

.stat-card.mixed {
  border-left: 4px solid #e6a23c;
}

.stat-card.single {
  border-left: 4px solid #409eff;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  color: #e4e7ed;
  z-index: 1;
}

.list-card {
  margin-top: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
