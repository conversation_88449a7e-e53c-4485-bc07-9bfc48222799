<template>
  <div class="device-group-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button
          type="text"
          @click="goBack"
          class="back-button"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="header-info">
          <h2>{{ groupInfo.name || '设备分组详情' }}</h2>
          <p>{{ groupInfo.description || '暂无描述' }}</p>
        </div>
      </div>
      <div class="header-actions">
        <el-button
          type="primary"
          @click="showEditDialog = true"
        >
          <el-icon><Edit /></el-icon>
          编辑分组
        </el-button>
        <el-button
          type="success"
          @click="showAddDeviceDialog = true"
        >
          <el-icon><Plus /></el-icon>
          添加设备
        </el-button>
        <el-button
          type="warning"
          @click="showPermissionDialog = true"
        >
          <el-icon><Key /></el-icon>
          分配权限
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <el-icon class="stat-icon"><Monitor /></el-icon>
          <div class="stat-info">
            <div class="stat-value">{{ groupDevices.length }}</div>
            <div class="stat-label">设备总数</div>
          </div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <el-icon class="stat-icon"><User /></el-icon>
          <div class="stat-info">
            <div class="stat-value">{{ groupPermissions.length }}</div>
            <div class="stat-label">授权用户</div>
          </div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <el-icon class="stat-icon"><Connection /></el-icon>
          <div class="stat-info">
            <div class="stat-value">{{ onlineDevices }}</div>
            <div class="stat-label">在线设备</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-card class="content-card">
        <el-tabs type="border-card">
          <!-- 设备列表标签页 -->
          <el-tab-pane label="分组设备">
            <template #label>
              <span><el-icon><Cpu /></el-icon> 分组设备</span>
            </template>

            <div class="tab-header">
              <el-button
                type="primary"
                size="small"
                @click="showAddDeviceDialog = true"
              >
                <el-icon><Plus /></el-icon>
                添加设备
              </el-button>
            </div>
        
        <el-table
          :data="groupDevices"
          style="width: 100%"
          :loading="deviceLoading"
        >
          <el-table-column prop="device_name" label="设备名称" min-width="150">
            <template #default="scope">
              <div class="device-name">
                <el-icon class="device-icon"><Cpu /></el-icon>
                {{ scope.row.device_name || '未知设备' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="device_type" label="设备类型" width="120">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.device_type || '未知' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag
                :type="scope.row.status === 'available' ? 'success' : 'warning'"
                size="small"
              >
                {{ scope.row.status === 'available' ? '可用' : '占用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="slave_server_name" label="所属服务器" width="150" />
          <el-table-column prop="added_at" label="添加时间" width="180">
            <template #default="scope">
              {{ formatTime(scope.row.added_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button
                size="small"
                type="danger"
                @click="removeDevice(scope.row)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
          </el-tab-pane>

          <!-- 权限管理标签页 -->
          <el-tab-pane label="用户权限">
            <template #label>
              <span><el-icon><Key /></el-icon> 用户权限</span>
            </template>

            <div class="tab-header">
              <el-button
                type="primary"
                size="small"
                @click="showPermissionDialog = true"
              >
                <el-icon><Key /></el-icon>
                分配权限
              </el-button>
            </div>
        
        <el-table
          :data="groupPermissions"
          style="width: 100%"
          :loading="permissionLoading"
        >
          <el-table-column prop="username" label="用户名" width="150" />
          <el-table-column prop="role_name" label="角色" width="120">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.role_name }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="organization_name" label="组织" width="150" />
          <el-table-column prop="granted_at" label="授权时间" width="180">
            <template #default="scope">
              {{ formatTime(scope.row.granted_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button
                size="small"
                type="danger"
                @click="revokePermission(scope.row)"
              >
                撤销
              </el-button>
            </template>
          </el-table-column>
        </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 编辑分组对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑设备分组"
      width="500px"
    >
      <el-form
        :model="editForm"
        label-width="100px"
      >
        <el-form-item label="分组名称" required>
          <el-input v-model="editForm.name" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="分组描述">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分组描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="updateGroup">确定</el-button>
      </template>
    </el-dialog>

    <!-- 添加设备对话框 -->
    <el-dialog
      v-model="showAddDeviceDialog"
      title="添加设备到分组"
      width="600px"
    >
      <el-table
        :data="availableDevices"
        style="width: 100%"
        @selection-change="handleDeviceSelection"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="device_name" label="设备名称" min-width="150" />
        <el-table-column prop="device_type" label="设备类型" width="120" />
        <el-table-column prop="slave_server_name" label="所属服务器" width="150" />
      </el-table>
      <template #footer>
        <el-button @click="showAddDeviceDialog = false">取消</el-button>
        <el-button type="primary" @click="addDevicesToGroup">确定</el-button>
      </template>
    </el-dialog>

    <!-- 分配权限对话框 -->
    <el-dialog
      v-model="showPermissionDialog"
      title="分配用户权限"
      width="600px"
    >
      <el-table
        :data="availableUsers"
        style="width: 100%"
        @selection-change="handleUserSelection"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="role_name" label="角色" width="120" />
        <el-table-column prop="organization_name" label="组织" width="150" />
      </el-table>
      <template #footer>
        <el-button @click="showPermissionDialog = false">取消</el-button>
        <el-button type="primary" @click="grantPermissions">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Edit,
  Plus,
  Monitor,
  User,
  Connection,
  Cpu,
  Key
} from '@element-plus/icons-vue'
import api from '@/api'

const route = useRoute()
const router = useRouter()

// 响应式数据
const groupInfo = ref({})
const groupDevices = ref([])
const groupPermissions = ref([])
const availableDevices = ref([])
const availableUsers = ref([])
const selectedDevices = ref([])
const selectedUsers = ref([])

// 加载状态
const deviceLoading = ref(false)
const permissionLoading = ref(false)

// 对话框状态
const showEditDialog = ref(false)
const showAddDeviceDialog = ref(false)
const showPermissionDialog = ref(false)

// 表单数据
const editForm = reactive({
  name: '',
  description: ''
})

// 计算属性
const onlineDevices = computed(() => {
  return groupDevices.value.filter(device => device.status === 'available').length
})

// 方法
const goBack = () => {
  router.go(-1)
}

const formatTime = (timeString) => {
  if (!timeString) return 'N/A'
  return new Date(timeString).toLocaleString('zh-CN')
}

// 加载分组详情
const loadGroupDetail = async () => {
  try {
    const groupId = route.params.id
    const response = await api.get(`/api/v1/device-groups/${groupId}`)
    if (response.data.success) {
      groupInfo.value = response.data.data
      editForm.name = groupInfo.value.name
      editForm.description = groupInfo.value.description
    }
  } catch (error) {
    console.error('加载分组详情失败:', error)
    ElMessage.error('加载分组详情失败')
  }
}

// 加载分组设备
const loadGroupDevices = async () => {
  try {
    deviceLoading.value = true
    const groupId = route.params.id
    const response = await api.get(`/api/v1/device-groups/${groupId}/devices`)
    if (response.data.success) {
      groupDevices.value = response.data.data
    }
  } catch (error) {
    console.error('加载分组设备失败:', error)
    ElMessage.error('加载分组设备失败')
  } finally {
    deviceLoading.value = false
  }
}

// 加载分组权限
const loadGroupPermissions = async () => {
  try {
    permissionLoading.value = true
    const groupId = route.params.id
    const response = await api.get(`/api/v1/device-groups/${groupId}/permissions`)
    groupPermissions.value = response.data || []
  } catch (error) {
    console.error('加载分组权限失败:', error)
    ElMessage.error('加载分组权限失败')
  } finally {
    permissionLoading.value = false
  }
}

// 加载可分配用户
const loadAvailableUsers = async () => {
  try {
    const groupId = route.params.id
    const response = await api.get(`/api/v1/device-groups/${groupId}/available-users`)
    availableUsers.value = response.data || []
  } catch (error) {
    console.error('加载可分配用户失败:', error)
    ElMessage.error('加载可分配用户失败')
  }
}

// 处理用户选择
const handleUserSelection = (selection) => {
  selectedUsers.value = selection
}

// 分配权限
const grantPermissions = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要分配权限的用户')
    return
  }

  try {
    const groupId = route.params.id
    const userIds = selectedUsers.value.map(user => user.id)

    await api.post(`/api/v1/device-groups/${groupId}/permissions`, {
      user_ids: userIds,
      permission_type: 'use', // 默认分配使用权限
      expires_at: null
    })

    ElMessage.success('权限分配成功')
    showPermissionDialog.value = false
    selectedUsers.value = []
    await loadGroupPermissions()
  } catch (error) {
    console.error('分配权限失败:', error)
    ElMessage.error('分配权限失败')
  }
}

// 撤销权限
const revokePermission = async (permission) => {
  try {
    await ElMessageBox.confirm(
      `确定要撤销用户"${permission.username}"的权限吗？`,
      '撤销确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const groupId = route.params.id
    await api.delete(`/api/v1/device-groups/${groupId}/permissions/${permission.id}`)

    ElMessage.success('权限撤销成功')
    await loadGroupPermissions()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销权限失败:', error)
      ElMessage.error('撤销权限失败')
    }
  }
}

// 页面初始化
onMounted(() => {
  loadGroupDetail()
  loadGroupDevices()
  loadGroupPermissions()
  loadAvailableUsers()
})
</script>

<style scoped>
.device-group-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  margin-right: 16px;
  font-size: 16px;
}

.header-info h2 {
  margin: 0 0 4px 0;
  color: #303133;
}

.header-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stat-icon {
  font-size: 32px;
  color: #409eff;
  margin-right: 16px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.main-content {
  display: grid;
  gap: 20px;
}

.content-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-name {
  display: flex;
  align-items: center;
}

.device-icon {
  margin-right: 8px;
  color: #409eff;
}

.tab-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.el-tabs__content {
  padding: 20px;
}
</style>
