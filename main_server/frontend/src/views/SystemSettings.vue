<template>
  <div class="system-settings-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">系统设置</span>
          <span class="header-subtitle">管理系统配置、内网穿透和数据压缩功能</span>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础设置 -->
        <el-tab-pane label="基础设置" name="general">
          <div class="settings-section">
            <h3>系统信息</h3>
            <el-form :model="generalSettings" label-width="150px">
              <el-form-item label="系统名称">
                <el-input 
                  v-model="generalSettings.system_name" 
                  @blur="updateSetting('system_name', generalSettings.system_name)"
                />
              </el-form-item>
              <el-form-item label="系统版本">
                <el-input 
                  v-model="generalSettings.system_version" 
                  @blur="updateSetting('system_version', generalSettings.system_version)"
                />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- USB数据库管理 -->
        <el-tab-pane label="USB数据库" name="usb-database">
          <USBDatabaseManager />
        </el-tab-pane>

        <!-- 客户端设置 -->
        <el-tab-pane label="客户端设置" name="client">
          <div class="settings-section">
            <h3>专属客户端</h3>
            <el-form :model="clientSettings" label-width="150px">
              <el-form-item label="下载链接">
                <el-input
                  v-model="clientSettings.client_download_url"
                  placeholder="请输入客户端下载链接"
                  @blur="updateSetting('client_download_url', clientSettings.client_download_url)"
                />
                <div class="setting-help">
                  此链接将在登录页面显示，用户可点击下载专属客户端
                </div>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 公网链接设置 -->
        <el-tab-pane label="公网链接" name="tunnel">
          <div class="standard-container">
            <el-card class="standard-card">
              <template #header>
                <div class="card-header">
                  <span>公网链接管理</span>
                  <el-button type="primary" @click="showCreateTunnelDialog = true">
                    <el-icon><Plus /></el-icon>
                    新增配置
                  </el-button>
                </div>
              </template>

              <el-table :data="tunnelConfigs" style="width: 100%" size="small">
                <el-table-column prop="name" label="配置名称" width="120" show-overflow-tooltip />
                <el-table-column prop="tunnel_type" label="类型" width="240" align="center">
                  <template #default="scope">
                    <el-tag :type="getTunnelTypeColor(scope.row.tunnel_type)" size="small">
                      {{ scope.row.tunnel_type.toUpperCase() }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="is_active" label="启用" width="80" align="center">
                  <template #default="scope">
                    <el-switch
                      v-model="scope.row.is_active"
                      @change="toggleTunnelEnabled(scope.row)"
                      :disabled="scope.row.force_disabled"
                      size="small"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100" align="center">
                  <template #default="scope">
                    <el-tag :type="getStatusColor(scope.row.status)" size="small">
                      {{ getStatusText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="400" align="left">
                  <template #default="scope">
                    <div class="tunnel-action-buttons">
                      <!-- 启动/停止按钮 -->
                      <el-button
                        v-if="!scope.row.is_running"
                        type="success"
                        size="small"
                        @click="startTunnel(scope.row)"
                        :disabled="!scope.row.is_active || scope.row.force_disabled"
                      >
                        启动
                      </el-button>
                      <el-button
                        v-else
                        type="warning"
                        size="small"
                        @click="stopTunnel(scope.row)"
                      >
                        停止
                      </el-button>

                      <!-- 编辑按钮 -->
                      <el-button
                        type="primary"
                        size="small"
                        @click="editTunnelConfig(scope.row)"
                      >
                        编辑
                      </el-button>

                      <!-- 升级按钮 -->
                      <el-button
                        type="info"
                        size="small"
                        @click="upgradeTunnel(scope.row)"
                      >
                        升级
                      </el-button>

                      <!-- 强制禁用按钮 -->
                      <el-button
                        :type="scope.row.force_disabled ? 'success' : 'danger'"
                        size="small"
                        @click="toggleForceDisabled(scope.row)"
                      >
                        {{ scope.row.force_disabled ? '解锁' : '锁定' }}
                      </el-button>

                      <!-- 测试连接按钮 -->
                      <el-button
                        type="success"
                        size="small"
                        @click="testTunnelConnection(scope.row)"
                        :loading="testingConnections[scope.row.id]"
                        icon="Connection"
                      >
                        {{ testingConnections[scope.row.id] ? '测试中' : '测试连接' }}
                      </el-button>

                      <!-- 管理界面按钮 (仅对有Web UI的工具显示) -->
                      <el-button
                        v-if="hasWebInterface(scope.row.tunnel_type) && isServerConfig(scope.row)"
                        type="info"
                        size="small"
                        @click="openManagementInterface(scope.row)"
                      >
                        管理
                      </el-button>
                    </div>

                      <!-- 删除配置按钮 -->
                      <el-button
                        type="danger"
                        size="small"
                        @click="deleteTunnelConfig(scope.row)"
                        :icon="Delete"
                        :disabled="scope.row.is_running"
                      >
                        删除
                      </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 数据链路验证 -->
        <el-tab-pane label="数据链路验证" name="datalink">
          <div class="standard-container">
            <!-- 连接状态检查 -->
            <el-card class="standard-card">
              <template #header>
                <div class="card-header">
                  <span>连接状态检查</span>
                  <el-button type="primary" @click="refreshConnectionStatus" :loading="refreshingStatus">
                    <el-icon><Refresh /></el-icon>
                    刷新状态
                  </el-button>
                </div>
              </template>

              <div class="connection-status-grid">
                <div v-for="config in activeTunnelConfigs" :key="config.id" class="connection-card">
                  <div class="connection-header">
                    <h4>{{ config.name }} ({{ config.tunnel_type.toUpperCase() }})</h4>
                    <el-tag :type="getConnectionStatusType(config.status)" size="large">
                      {{ getConnectionStatusText(config.status) }}
                    </el-tag>
                  </div>

                  <div class="connection-details">
                    <div class="detail-item">
                      <span class="label">服务状态:</span>
                      <span class="value">{{ config.service_status || '未知' }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">最后检查:</span>
                      <span class="value">{{ formatTime(config.last_check) }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">运行时长:</span>
                      <span class="value">{{ config.uptime || '0分钟' }}</span>
                    </div>
                  </div>

                  <div class="connection-actions">
                    <el-button
                      type="success"
                      size="small"
                      @click="startPerformanceTest(config)"
                      :disabled="!isConnectionReady(config)"
                    >
                      开始性能测试
                    </el-button>
                  </div>
                </div>
              </div>

              <el-empty v-if="activeTunnelConfigs.length === 0" description="没有活跃的内网穿透连接">
                <el-button type="primary" @click="$emit('switch-tab', 'tunnel')">
                  前往配置管理
                </el-button>
              </el-empty>
            </el-card>

            <!-- 性能测试套件 -->
            <el-card class="standard-card" v-if="selectedConfig">
              <template #header>
                <div class="card-header">
                  <span>{{ selectedConfig.name }} - 数据性能测试</span>
                  <div class="test-controls">
                    <el-button
                      type="primary"
                      @click="startAllTests"
                      :loading="isTestingAll"
                      :disabled="!isConnectionReady(selectedConfig)"
                    >
                      开始全面测试
                    </el-button>
                    <el-button
                      type="danger"
                      @click="stopAllTests"
                      :disabled="!isTestingAny"
                    >
                      停止测试
                    </el-button>
                  </div>
                </div>
              </template>

              <div class="test-suite">
                <!-- 延迟测试 -->
                <div class="test-section">
                  <div class="test-header">
                    <h4>连接延迟测试</h4>
                    <el-button
                      size="small"
                      @click="startLatencyTest"
                      :loading="testStatus.latency.running"
                    >
                      {{ testStatus.latency.running ? '测试中...' : '开始测试' }}
                    </el-button>
                  </div>

                  <div class="test-results">
                    <div class="metric-grid">
                      <div class="metric-item">
                        <span class="metric-label">平均延迟</span>
                        <span class="metric-value">{{ testResults.latency.average || '--' }}ms</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">最小延迟</span>
                        <span class="metric-value">{{ testResults.latency.min || '--' }}ms</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">最大延迟</span>
                        <span class="metric-value">{{ testResults.latency.max || '--' }}ms</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">丢包率</span>
                        <span class="metric-value">{{ testResults.latency.packetLoss || '--' }}%</span>
                      </div>
                    </div>

                    <div class="test-progress" v-if="testStatus.latency.running">
                      <el-progress
                        :percentage="testStatus.latency.progress"
                        :status="testStatus.latency.progress === 100 ? 'success' : ''"
                      />
                      <span class="progress-text">{{ testStatus.latency.currentStep }}</span>
                    </div>
                  </div>
                </div>

                <!-- 带宽测试 -->
                <div class="test-section">
                  <div class="test-header">
                    <h4>带宽速率测试</h4>
                    <el-button
                      size="small"
                      @click="startBandwidthTest"
                      :loading="testStatus.bandwidth.running"
                    >
                      {{ testStatus.bandwidth.running ? '测试中...' : '开始测试' }}
                    </el-button>
                  </div>

                  <div class="test-results">
                    <div class="metric-grid">
                      <div class="metric-item">
                        <span class="metric-label">下载速度</span>
                        <span class="metric-value">{{ testResults.bandwidth.download || '--' }} Mbps</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">上传速度</span>
                        <span class="metric-value">{{ testResults.bandwidth.upload || '--' }} Mbps</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">数据传输量</span>
                        <span class="metric-value">{{ testResults.bandwidth.totalData || '--' }} MB</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">测试时长</span>
                        <span class="metric-value">{{ testResults.bandwidth.duration || '--' }}s</span>
                      </div>
                    </div>

                    <div class="test-progress" v-if="testStatus.bandwidth.running">
                      <el-progress
                        :percentage="testStatus.bandwidth.progress"
                        :status="testStatus.bandwidth.progress === 100 ? 'success' : ''"
                      />
                      <span class="progress-text">{{ testStatus.bandwidth.currentStep }}</span>
                    </div>
                  </div>
                </div>

                <!-- 并发测试 -->
                <div class="test-section">
                  <div class="test-header">
                    <h4>多并发连接测试</h4>
                    <div class="concurrent-controls">
                      <el-input-number
                        v-model="concurrentTestConfig.connections"
                        :min="1"
                        :max="100"
                        size="small"
                        style="width: 120px; margin-right: 10px;"
                      />
                      <span style="margin-right: 10px;">个并发连接</span>
                      <el-button
                        size="small"
                        @click="startConcurrentTest"
                        :loading="testStatus.concurrent.running"
                      >
                        {{ testStatus.concurrent.running ? '测试中...' : '开始测试' }}
                      </el-button>
                    </div>
                  </div>

                  <div class="test-results">
                    <div class="metric-grid">
                      <div class="metric-item">
                        <span class="metric-label">成功连接</span>
                        <span class="metric-value">{{ testResults.concurrent.successful || '--' }}</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">失败连接</span>
                        <span class="metric-value">{{ testResults.concurrent.failed || '--' }}</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">平均响应时间</span>
                        <span class="metric-value">{{ testResults.concurrent.avgResponseTime || '--' }}ms</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">连接成功率</span>
                        <span class="metric-value">{{ testResults.concurrent.successRate || '--' }}%</span>
                      </div>
                    </div>

                    <div class="test-progress" v-if="testStatus.concurrent.running">
                      <el-progress
                        :percentage="testStatus.concurrent.progress"
                        :status="testStatus.concurrent.progress === 100 ? 'success' : ''"
                      />
                      <span class="progress-text">{{ testStatus.concurrent.currentStep }}</span>
                    </div>
                  </div>
                </div>

                <!-- 稳定性测试 -->
                <div class="test-section">
                  <div class="test-header">
                    <h4>长期稳定性测试</h4>
                    <div class="stability-controls">
                      <el-select v-model="stabilityTestConfig.duration" size="small" style="width: 120px; margin-right: 10px;">
                        <el-option label="5分钟" value="5" />
                        <el-option label="15分钟" value="15" />
                        <el-option label="30分钟" value="30" />
                        <el-option label="1小时" value="60" />
                      </el-select>
                      <el-button
                        size="small"
                        @click="startStabilityTest"
                        :loading="testStatus.stability.running"
                      >
                        {{ testStatus.stability.running ? '测试中...' : '开始测试' }}
                      </el-button>
                    </div>
                  </div>

                  <div class="test-results">
                    <div class="metric-grid">
                      <div class="metric-item">
                        <span class="metric-label">运行时长</span>
                        <span class="metric-value">{{ testResults.stability.elapsed || '--' }}</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">连接中断次数</span>
                        <span class="metric-value">{{ testResults.stability.disconnections || '--' }}</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">平均延迟</span>
                        <span class="metric-value">{{ testResults.stability.avgLatency || '--' }}ms</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">稳定性评分</span>
                        <span class="metric-value">{{ testResults.stability.score || '--' }}/100</span>
                      </div>
                    </div>

                    <div class="test-progress" v-if="testStatus.stability.running">
                      <el-progress
                        :percentage="testStatus.stability.progress"
                        :status="testStatus.stability.progress === 100 ? 'success' : ''"
                      />
                      <span class="progress-text">{{ testStatus.stability.currentStep }}</span>
                    </div>
                  </div>
                </div>

                <!-- 数据压缩性能测试 -->
                <div class="test-section">
                  <div class="test-header">
                    <h4>数据压缩性能测试</h4>
                    <div class="compression-controls">
                      <el-button
                        size="small"
                        @click="startCompressionTest"
                        :loading="testStatus.compression.running"
                        type="primary"
                      >
                        {{ testStatus.compression.running ? '测试中...' : '开始压缩测试' }}
                      </el-button>
                      <el-button
                        size="small"
                        @click="stopCompressionTest"
                        :disabled="!testStatus.compression.running"
                        type="danger"
                      >
                        停止测试
                      </el-button>
                    </div>
                  </div>

                  <!-- 测试参数配置 -->
                  <div class="compression-config">
                    <div class="config-row">
                      <div class="config-item">
                        <span class="config-label">数据大小:</span>
                        <el-select v-model="compressionTestConfig.dataSize" size="small" style="width: 100px;">
                          <el-option label="1MB" value="1" />
                          <el-option label="10MB" value="10" />
                          <el-option label="50MB" value="50" />
                          <el-option label="100MB" value="100" />
                        </el-select>
                      </div>

                      <div class="config-item">
                        <span class="config-label">并发数:</span>
                        <el-select v-model="compressionTestConfig.concurrency" size="small" style="width: 100px;">
                          <el-option label="1" value="1" />
                          <el-option label="5" value="5" />
                          <el-option label="10" value="10" />
                          <el-option label="20" value="20" />
                          <el-option label="50" value="50" />
                          <el-option label="100" value="100" />
                          <el-option label="400" value="400" />
                        </el-select>
                      </div>

                      <div class="config-item">
                        <span class="config-label">持续时间:</span>
                        <el-select v-model="compressionTestConfig.duration" size="small" style="width: 120px;">
                          <el-option label="1秒" value="1" />
                          <el-option label="5秒" value="5" />
                          <el-option label="10秒" value="10" />
                          <el-option label="30秒" value="30" />
                          <el-option label="60秒" value="60" />
                          <el-option label="5分钟" value="300" />
                          <el-option label="10分钟" value="600" />
                          <el-option label="长期持续" value="continuous" />
                        </el-select>
                      </div>

                      <div class="config-item">
                        <span class="config-label">数据类型:</span>
                        <el-select v-model="compressionTestConfig.dataType" size="small" style="width: 140px;">
                          <el-option label="JSON配置数据" value="json" />
                          <el-option label="二进制设备数据" value="binary" />
                          <el-option label="文本日志数据" value="text" />
                          <el-option label="混合数据包" value="mixed" />
                        </el-select>
                      </div>
                    </div>

                    <div class="config-row">
                      <div class="config-item">
                        <span class="config-label">启用压缩:</span>
                        <el-switch v-model="compressionTestConfig.enableCompression" />
                      </div>

                      <div class="config-item" v-if="compressionTestConfig.enableCompression">
                        <span class="config-label">压缩算法:</span>
                        <el-select v-model="compressionTestConfig.algorithm" size="small" style="width: 120px;">
                          <el-option label="Snappy" value="snappy" />
                          <el-option label="LZ4" value="lz4" />
                          <el-option label="Zstandard" value="zstd" />
                        </el-select>
                      </div>

                      <div class="config-item" v-if="compressionTestConfig.enableCompression">
                        <span class="config-label">压缩级别:</span>
                        <el-select v-model="compressionTestConfig.compressionLevel" size="small" style="width: 80px;">
                          <el-option label="低" value="low" />
                          <el-option label="中" value="medium" />
                          <el-option label="高" value="high" />
                        </el-select>
                      </div>
                    </div>
                  </div>

                  <div class="test-results">
                    <div class="metric-grid">
                      <div class="metric-item">
                        <span class="metric-label">原始数据大小</span>
                        <span class="metric-value">{{ testResults.compression.originalSize || '--' }}</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">压缩后大小</span>
                        <span class="metric-value">{{ testResults.compression.compressedSize || '--' }}</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">压缩率</span>
                        <span class="metric-value">{{ testResults.compression.compressionRatio || '--' }}%</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">传输速度</span>
                        <span class="metric-value">{{ testResults.compression.transferSpeed || '--' }} MB/s</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">压缩时间</span>
                        <span class="metric-value">{{ testResults.compression.compressionTime || '--' }}ms</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">解压时间</span>
                        <span class="metric-value">{{ testResults.compression.decompressionTime || '--' }}ms</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">总传输量</span>
                        <span class="metric-value">{{ testResults.compression.totalTransferred || '--' }}</span>
                      </div>
                      <div class="metric-item">
                        <span class="metric-label">节省带宽</span>
                        <span class="metric-value">{{ testResults.compression.bandwidthSaved || '--' }}</span>
                      </div>
                    </div>

                    <div class="test-progress" v-if="testStatus.compression.running">
                      <el-progress
                        :percentage="testStatus.compression.progress"
                        :status="testStatus.compression.progress === 100 ? 'success' : ''"
                      />
                      <span class="progress-text">{{ testStatus.compression.currentStep }}</span>
                    </div>

                    <!-- 实时压缩性能图表 -->
                    <div class="compression-charts" v-if="testResults.compression.chartData">
                      <div class="chart-container">
                        <h5>压缩率对比</h5>
                        <div class="chart-placeholder">
                          <div class="chart-bar"
                               v-for="(data, index) in testResults.compression.chartData"
                               :key="index"
                               :style="{ height: data.ratio + '%' }">
                            <span class="chart-label">{{ data.algorithm }}</span>
                            <span class="chart-value">{{ data.ratio }}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 测试报告 -->
            <el-card class="standard-card" v-if="hasTestResults">
              <template #header>
                <div class="card-header">
                  <span>测试报告</span>
                  <div class="report-actions">
                    <el-button size="small" @click="exportReport">
                      <el-icon><Download /></el-icon>
                      导出报告
                    </el-button>
                    <el-button size="small" @click="clearResults">
                      <el-icon><Delete /></el-icon>
                      清除结果
                    </el-button>
                  </div>
                </div>
              </template>

              <div class="test-report">
                <div class="report-summary">
                  <h4>测试摘要</h4>
                  <div class="summary-grid">
                    <div class="summary-item">
                      <span class="label">测试配置:</span>
                      <span class="value">{{ selectedConfig?.name }} ({{ selectedConfig?.tunnel_type.toUpperCase() }})</span>
                    </div>
                    <div class="summary-item">
                      <span class="label">测试时间:</span>
                      <span class="value">{{ formatTime(testReport.startTime) }} - {{ formatTime(testReport.endTime) }}</span>
                    </div>
                    <div class="summary-item">
                      <span class="label">总体评分:</span>
                      <span class="value">{{ testReport.overallScore }}/100</span>
                    </div>
                  </div>
                </div>

                <div class="report-details">
                  <h4>详细结果</h4>
                  <el-table :data="testReport.details" style="width: 100%">
                    <el-table-column prop="testType" label="测试类型" width="150" />
                    <el-table-column prop="result" label="测试结果" width="200" />
                    <el-table-column prop="score" label="评分" width="100" />
                    <el-table-column prop="status" label="状态" width="100">
                      <template #default="scope">
                        <el-tag :type="scope.row.status === 'passed' ? 'success' : 'danger'">
                          {{ scope.row.status === 'passed' ? '通过' : '失败' }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="notes" label="备注" />
                  </el-table>
                </div>
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 从设备管理 -->
        <el-tab-pane label="从设备管理" name="slave">
          <div class="settings-section">
            <el-empty description="从设备管理功能开发中，敬请期待" />
          </div>
        </el-tab-pane>

        <!-- 数据压缩设置 -->
        <el-tab-pane label="数据压缩" name="compression">
          <div class="standard-container">
            <!-- 压缩算法管理 -->
            <el-card class="standard-card">
              <template #header>
                <div class="card-header">
                  <span>压缩算法管理</span>
                </div>
              </template>

              <el-table :data="availableAlgorithms" style="width: 100%">
                <el-table-column prop="display_name" label="算法名称" width="150" />
                <el-table-column prop="description" label="描述" min-width="200" />
                <el-table-column prop="available" label="可用状态" width="100" align="center">
                  <template #default="scope">
                    <el-tag :type="scope.row.available ? 'success' : 'danger'">
                      {{ scope.row.available ? '可用' : '不可用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="enabled" label="启用状态" width="100" align="center">
                  <template #default="scope">
                    <el-switch
                      v-model="scope.row.enabled"
                      @change="toggleAlgorithm(scope.row)"
                      :disabled="!scope.row.available"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="scope">
                    <el-button
                      size="small"
                      @click="configureAlgorithm(scope.row)"
                      :disabled="!scope.row.available"
                    >
                      配置
                    </el-button>
                    <el-button
                      size="small"
                      type="info"
                      @click="testAlgorithm(scope.row)"
                      :disabled="!scope.row.available || !scope.row.enabled"
                    >
                      测试
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>

            <!-- 压缩配置管理 -->
            <el-card class="standard-card" style="margin-top: 20px;">
              <template #header>
                <div class="card-header">
                  <span>压缩配置管理</span>
                  <el-button type="primary" @click="showCreateCompressionDialog = true">
                    <el-icon><Plus /></el-icon>
                    新增配置
                  </el-button>
                </div>
              </template>

              <el-table :data="compressionConfigs" style="width: 100%" v-loading="compressionLoading">
                <el-table-column prop="name" label="配置名称" width="150" />
                <el-table-column prop="algorithm" label="压缩算法" width="120">
                  <template #default="scope">
                    <el-tag :type="getAlgorithmTagType(scope.row.algorithm)">
                      {{ getAlgorithmDisplayName(scope.row.algorithm) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="level" label="压缩级别" width="120">
                  <template #default="scope">
                    <el-tag :type="getLevelTagType(scope.row.level)">
                      {{ getLevelDisplayName(scope.row.level) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="stream_type" label="数据流类型" width="150">
                  <template #default="scope">
                    {{ getStreamTypeDisplayName(scope.row.stream_type) }}
                  </template>
                </el-table-column>
                <el-table-column prop="is_active" label="状态" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.is_active ? 'success' : 'info'">
                      {{ scope.row.is_active ? '已启用' : '未启用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="is_default" label="默认" width="80" align="center">
                  <template #default="scope">
                    <el-icon v-if="scope.row.is_default" color="#67C23A"><Check /></el-icon>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="280">
                  <template #default="scope">
                    <el-button
                      v-if="!scope.row.is_active"
                      type="success"
                      size="small"
                      @click="activateCompressionConfig(scope.row.id)"
                      :loading="switchingConfig === scope.row.id"
                    >
                      {{ switchingConfig === scope.row.id ? '切换中' : '启用' }}
                    </el-button>
                    <el-button
                      v-else
                      type="warning"
                      size="small"
                      @click="deactivateCompressionConfig(scope.row.id)"
                      :loading="switchingConfig === scope.row.id"
                    >
                      {{ switchingConfig === scope.row.id ? '停用中' : '停用' }}
                    </el-button>
                    <el-button type="primary" size="small" @click="editCompressionConfig(scope.row)">
                      编辑
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteCompressionConfig(scope.row.id)"
                      :disabled="scope.row.is_default || scope.row.is_active"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 缓存清除设置 -->
        <el-tab-pane label="缓存清除" name="cache">
          <div class="standard-container">
            <!-- 缓存信息 -->
            <el-card class="standard-card">
              <template #header>
                <div class="card-header">
                  <span class="header-title">系统缓存管理</span>
                  <span class="header-subtitle">查看和清除各类系统缓存，解决页面显示异常问题</span>
                </div>
              </template>

              <div class="cache-info-section">
                <el-alert
                  title="缓存清除说明"
                  type="info"
                  :closable="false"
                  show-icon
                  style="margin-bottom: 20px;"
                >
                  <template #default>
                    <p>如果遇到页面显示异常、数据不更新等问题，可以尝试清除相应的缓存。</p>
                    <p><strong>注意：</strong>清除缓存可能会导致短暂的性能下降，建议在系统空闲时操作。</p>
                  </template>
                </el-alert>

                <el-table
                  :data="cacheInfo"
                  v-loading="cacheInfoLoading"
                  style="width: 100%"
                  class="standard-table"
                >
                  <el-table-column prop="name" label="缓存类型" width="200">
                    <template #default="{ row }">
                      <div class="cache-name">
                        <el-icon class="cache-icon">
                          <component :is="getCacheIcon(row.type)" />
                        </el-icon>
                        <span>{{ row.name }}</span>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column prop="description" label="描述" min-width="300">
                    <template #default="{ row }">
                      <span class="cache-description">{{ row.description }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag
                        :type="row.status === 'active' ? 'success' : row.status === 'error' ? 'danger' : 'info'"
                        size="small"
                      >
                        {{ getCacheStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>

                  <el-table-column prop="size" label="大小" width="100">
                    <template #default="{ row }">
                      <span class="cache-size">{{ row.size }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" width="150">
                    <template #default="{ row }">
                      <el-button
                        type="primary"
                        size="small"
                        :loading="clearingCache[row.type]"
                        @click="clearSingleCache(row.type)"
                        :disabled="row.status === 'inactive'"
                      >
                        清除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 批量操作 -->
                <div class="cache-actions" style="margin-top: 20px;">
                  <el-space>
                    <el-button
                      type="warning"
                      :loading="clearingCache.all"
                      @click="clearAllCaches"
                      icon="Delete"
                    >
                      清除所有服务器缓存
                    </el-button>

                    <el-button
                      type="info"
                      :loading="clearingCache.web"
                      @click="clearWebCache"
                      icon="Refresh"
                    >
                      清除Web页面缓存
                    </el-button>

                    <el-button
                      type="success"
                      @click="refreshCacheInfo"
                      icon="Refresh"
                    >
                      刷新缓存信息
                    </el-button>
                  </el-space>
                </div>
              </div>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 创建内网穿透配置对话框 -->
    <el-dialog 
      v-model="showCreateTunnelDialog" 
      title="创建内网穿透配置" 
      width="600px"
    >
      <el-form :model="newTunnelConfig" label-width="120px">
        <el-form-item label="配置名称" required>
          <el-input v-model="newTunnelConfig.name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="穿透类型" required>
          <el-select v-model="newTunnelConfig.tunnel_type" placeholder="请选择穿透类型">
            <el-option label="FRP" value="frp" />
            <el-option label="Linker" value="linker" />
          </el-select>
        </el-form-item>
        <el-form-item label="配置数据">
          <el-input 
            v-model="newTunnelConfigJson" 
            type="textarea" 
            :rows="8" 
            placeholder="请输入JSON格式的配置数据"
          />
        </el-form-item>
        <el-form-item label="启用配置">
          <el-switch v-model="newTunnelConfig.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateTunnelDialog = false">取消</el-button>
        <el-button type="primary" @click="createTunnelConfig">确定</el-button>
      </template>
    </el-dialog>

    <!-- 编辑内网穿透配置对话框 -->
    <el-dialog
      v-model="showEditTunnelDialog"
      title="编辑内网穿透配置"
      width="600px"
    >
      <el-form :model="editTunnelForm" label-width="120px">
        <el-form-item label="配置名称" required>
          <el-input v-model="editTunnelForm.name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="穿透类型" required>
          <el-select v-model="editTunnelForm.tunnel_type" placeholder="请选择穿透类型" disabled>
            <el-option label="FRP" value="frp" />
            <el-option label="Linker" value="linker" />
          </el-select>
        </el-form-item>
        <el-form-item label="配置数据">
          <el-input
            v-model="editTunnelConfigJson"
            type="textarea"
            :rows="8"
            placeholder="请输入JSON格式的配置数据"
          />
        </el-form-item>
        <el-form-item label="启用配置">
          <el-switch v-model="editTunnelForm.is_active" :disabled="editTunnelForm.force_disabled" />
        </el-form-item>
        <el-form-item label="强制禁用">
          <el-switch v-model="editTunnelForm.force_disabled" />
          <div class="form-tip">启用后将完全阻止此配置被启动，用于安全防护</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditTunnelDialog = false">取消</el-button>
        <el-button type="primary" @click="saveEditTunnelConfig">保存</el-button>
      </template>
    </el-dialog>

    <!-- 创建数据压缩配置对话框 -->
    <el-dialog
      v-model="showCreateCompressionDialog"
      title="创建数据压缩配置"
      width="700px"
    >
      <el-form :model="newCompressionConfig" :rules="compressionRules" ref="compressionFormRef" label-width="140px">
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="newCompressionConfig.name" placeholder="请输入配置名称" />
        </el-form-item>

        <el-form-item label="压缩算法" prop="algorithm">
          <el-select v-model="newCompressionConfig.algorithm" placeholder="请选择压缩算法" @change="onAlgorithmChange">
            <el-option
              v-for="algo in availableAlgorithms.filter(a => a.available)"
              :key="algo.name"
              :label="algo.display_name"
              :value="algo.name"
            >
              <div class="algorithm-option">
                <span>{{ algo.display_name }}</span>
                <span class="algorithm-desc">{{ algo.description }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="form-help" v-if="selectedAlgorithmInfo">
            <el-tag size="small" type="info">{{ selectedAlgorithmInfo.description }}</el-tag>
          </div>
        </el-form-item>

        <el-form-item label="压缩级别" prop="level">
          <el-radio-group v-model="newCompressionConfig.level">
            <el-radio label="low">
              <span class="level-option">
                <strong>低压缩率</strong> - 高速度，适合实时传输
              </span>
            </el-radio>
            <el-radio label="medium">
              <span class="level-option">
                <strong>中等压缩率</strong> - 平衡速度与压缩效果
              </span>
            </el-radio>
            <el-radio label="high">
              <span class="level-option">
                <strong>高压缩率</strong> - 低速度，适合大文件传输
              </span>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="数据流类型" prop="stream_type">
          <el-select v-model="newCompressionConfig.stream_type" placeholder="请选择数据流类型">
            <el-option label="Web数据 (包含HTTP和HTTPS流量)" value="web_data" />
            <el-option label="专属客户端数据 (VirtualHere USB和客户端通信)" value="client_data" />
            <el-option label="通用数据 (所有其他类型数据流)" value="general_data" />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最大并发用户">
              <el-input-number
                v-model="newCompressionConfig.max_concurrent_users"
                :min="1"
                :max="1000"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="带宽限制(Mbps)">
              <el-input-number
                v-model="newCompressionConfig.bandwidth_limit_mbps"
                :min="0.1"
                :max="100"
                :step="0.1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="高级选项">
          <el-checkbox v-model="newCompressionConfig.auto_fallback">自动降级</el-checkbox>
          <el-checkbox v-model="newCompressionConfig.is_default">设为默认配置</el-checkbox>
        </el-form-item>

        <!-- 实时预览 -->
        <el-form-item label="压缩效果预览" v-if="compressionPreview">
          <el-card class="preview-card">
            <div class="preview-content">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="preview-item">
                    <span class="preview-label">预估压缩率:</span>
                    <span class="preview-value">{{ compressionPreview.estimated_rate }}%</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="preview-item">
                    <span class="preview-label">预估速度:</span>
                    <span class="preview-value">{{ compressionPreview.estimated_speed }} MB/s</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="preview-item">
                    <span class="preview-label">内存使用:</span>
                    <span class="preview-value">{{ compressionPreview.estimated_memory }} KB</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateCompressionDialog = false">取消</el-button>
          <el-button type="primary" @click="createCompressionConfig" :loading="creatingConfig">
            {{ creatingConfig ? '创建中...' : '创建配置' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑数据压缩配置对话框 -->
    <el-dialog
      v-model="showEditCompressionDialog"
      title="编辑数据压缩配置"
      width="700px"
    >
      <el-form :model="editCompressionForm" :rules="compressionRules" ref="editCompressionFormRef" label-width="140px">
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="editCompressionForm.name" placeholder="请输入配置名称" />
        </el-form-item>

        <el-form-item label="压缩算法" prop="algorithm">
          <el-select v-model="editCompressionForm.algorithm" placeholder="请选择压缩算法">
            <el-option
              v-for="algo in availableAlgorithms.filter(a => a.available)"
              :key="algo.name"
              :label="algo.display_name"
              :value="algo.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="压缩级别" prop="level">
          <el-radio-group v-model="editCompressionForm.level">
            <el-radio label="low">低压缩率 (高速度)</el-radio>
            <el-radio label="medium">中等压缩率 (平衡)</el-radio>
            <el-radio label="high">高压缩率 (低速度)</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="数据流类型" prop="stream_type">
          <el-select v-model="editCompressionForm.stream_type" placeholder="请选择数据流类型">
            <el-option label="Web数据 (包含HTTP和HTTPS流量)" value="web_data" />
            <el-option label="专属客户端数据 (VirtualHere USB和客户端通信)" value="client_data" />
            <el-option label="通用数据 (所有其他类型数据流)" value="general_data" />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最大并发用户">
              <el-input-number
                v-model="editCompressionForm.max_concurrent_users"
                :min="1"
                :max="1000"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="带宽限制(Mbps)">
              <el-input-number
                v-model="editCompressionForm.bandwidth_limit_mbps"
                :min="0.1"
                :max="100"
                :step="0.1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="高级选项">
          <el-checkbox v-model="editCompressionForm.auto_fallback">自动降级</el-checkbox>
          <el-checkbox v-model="editCompressionForm.is_default">设为默认配置</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditCompressionDialog = false">取消</el-button>
          <el-button type="primary" @click="updateCompressionConfig" :loading="updatingConfig">
            {{ updatingConfig ? '更新中...' : '更新配置' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 压缩效果预览对话框 -->
    <el-dialog
      v-model="showCompressionPreview"
      title="压缩效果预览"
      width="900px"
    >
      <div class="preview-section">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>算法性能对比</span>
              </template>
              <el-table :data="algorithmComparison" style="width: 100%">
                <el-table-column prop="algorithm" label="算法" width="100" />
                <el-table-column prop="compression_rate" label="压缩率" width="80">
                  <template #default="scope">
                    {{ scope.row.compression_rate }}%
                  </template>
                </el-table-column>
                <el-table-column prop="speed" label="速度" width="100">
                  <template #default="scope">
                    {{ scope.row.speed }} MB/s
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>网络影响分析</span>
              </template>
              <div class="network-impact">
                <div class="impact-item">
                  <span class="impact-label">带宽节省:</span>
                  <span class="impact-value">{{ networkImpact.bandwidth_saved }}%</span>
                </div>
                <div class="impact-item">
                  <span class="impact-label">延迟增加:</span>
                  <span class="impact-value">{{ networkImpact.latency_increase }}ms</span>
                </div>
                <div class="impact-item">
                  <span class="impact-label">CPU使用:</span>
                  <span class="impact-value">{{ networkImpact.cpu_usage }}%</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 配置导入对话框 -->
    <el-dialog
      v-model="showImportDialog"
      title="导入压缩配置"
      width="600px"
    >
      <el-upload
        class="upload-demo"
        drag
        :auto-upload="false"
        :on-change="handleConfigFileChange"
        accept=".json"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将配置文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 JSON 格式的配置文件
          </div>
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showImportDialog = false">取消</el-button>
          <el-button type="primary" @click="importCompressionConfig" :loading="importingConfig">
            {{ importingConfig ? '导入中...' : '导入配置' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Refresh, Lock, Unlock, VideoPlay, VideoPause, Monitor, View, Check, Connection, DataAnalysis, TrendCharts, Lightbulb, UploadFilled, FolderOpened, Document, Setting, Download, Delete } from '@element-plus/icons-vue'
import USBDatabaseManager from '@/components/USBDatabaseManager.vue'
import {
  getSystemSettings,
  updateSystemSetting,
  getTunnelConfigs,
  createTunnelConfig as apiCreateTunnelConfig,
  updateTunnelConfig,
  deleteTunnelConfig as apiDeleteTunnelConfig,
  startTunnel as apiStartTunnel,
  stopTunnel as apiStopTunnel
} from '@/api/systemSettings'

// 压缩相关API - 简化版本（直接调用后端API）
import request from '@/utils/request'

const getCompressionConfigs = () => request.get('/api/v1/compression/configs')
const apiCreateCompressionConfig = (data) => request.post('/api/v1/compression/configs', data)
const apiUpdateCompressionConfig = (id, data) => request.put(`/api/v1/compression/configs/${id}`, data)
const apiDeleteCompressionConfig = (id) => request.delete(`/api/v1/compression/configs/${id}`)
const apiActivateCompressionConfig = (id) => request.post(`/api/v1/compression/configs/${id}/activate`)
const apiDeactivateCompressionConfig = (id) => request.post(`/api/v1/compression/configs/${id}/deactivate`)
const getCompressionStatus = () => request.get('/compression/status')
const getAvailableAlgorithms = () => request.get('/compression/algorithms')
const getCompressionStats = () => request.get('/compression/stats')
const getSmartRecommendations = () => request.get('/compression/recommendations')
const getCompressionPreview = (data) => request.post('/api/v1/compression/preview', data)

// 导入缓存相关API
import {
  getCacheInfo,
  clearCache,
  clearAllCache,
  clearWebCache as apiClearWebCache,
  forceRefreshPage
} from '@/api/cache'

// 响应式数据
const activeTab = ref('general')
const showCreateTunnelDialog = ref(false)
const showEditTunnelDialog = ref(false)

// 设置数据
const generalSettings = reactive({
  system_name: '',
  system_version: ''
})

const clientSettings = reactive({
  client_download_url: ''
})

const tunnelConfigs = ref([])

// 连接测试相关
const testingConnections = ref({})
const connectionTestResults = ref({})

// 数据链路验证相关
const activeTunnelConfigs = ref([])
const selectedConfig = ref(null)
const refreshingStatus = ref(false)
const isTestingAll = ref(false)
const isTestingAny = ref(false)

// 测试状态
const testStatus = reactive({
  latency: {
    running: false,
    progress: 0,
    currentStep: ''
  },
  bandwidth: {
    running: false,
    progress: 0,
    currentStep: ''
  },
  concurrent: {
    running: false,
    progress: 0,
    currentStep: ''
  },
  stability: {
    running: false,
    progress: 0,
    currentStep: ''
  },
  compression: {
    running: false,
    progress: 0,
    currentStep: ''
  }
})

// 测试结果
const testResults = reactive({
  latency: {
    average: null,
    min: null,
    max: null,
    packetLoss: null
  },
  bandwidth: {
    download: null,
    upload: null,
    totalData: null,
    duration: null
  },
  concurrent: {
    successful: null,
    failed: null,
    avgResponseTime: null,
    successRate: null
  },
  stability: {
    elapsed: null,
    disconnections: null,
    avgLatency: null,
    score: null
  },
  compression: {
    originalSize: null,
    compressedSize: null,
    compressionRatio: null,
    transferSpeed: null,
    compressionTime: null,
    decompressionTime: null,
    totalTransferred: null,
    bandwidthSaved: null,
    chartData: null
  }
})

// 测试配置
const concurrentTestConfig = reactive({
  connections: 10
})

const stabilityTestConfig = reactive({
  duration: '15'
})

// 数据压缩测试配置
const compressionTestConfig = reactive({
  dataSize: '10',           // MB
  concurrency: '10',        // 并发数
  duration: '30',           // 秒
  dataType: 'json',         // 数据类型
  enableCompression: true,   // 启用压缩
  algorithm: 'snappy',      // 压缩算法
  compressionLevel: 'medium' // 压缩级别
})

// 测试报告
const testReport = reactive({
  startTime: null,
  endTime: null,
  overallScore: 0,
  details: []
})

const hasTestResults = computed(() => {
  return testResults.latency.average !== null ||
         testResults.bandwidth.download !== null ||
         testResults.concurrent.successful !== null ||
         testResults.stability.score !== null
})

// 新建内网穿透配置
const newTunnelConfig = reactive({
  name: '',
  tunnel_type: '',
  is_active: false
})
const newTunnelConfigJson = ref('')

// 编辑内网穿透配置
const editTunnelForm = reactive({
  id: null,
  name: '',
  tunnel_type: '',
  is_active: false,
  force_disabled: false
})
const editTunnelConfigJson = ref('')

// 数据压缩相关响应式数据
const showCreateCompressionDialog = ref(false)
const showEditCompressionDialog = ref(false)
const showCompressionPreview = ref(false)
const showImportDialog = ref(false)
const compressionLoading = ref(false)
const creatingConfig = ref(false)
const updatingConfig = ref(false)
const importingConfig = ref(false)
const switchingConfig = ref(null)

// 压缩配置数据
const compressionConfigs = ref([])
const activeCompressionConfig = ref(null)
const availableAlgorithms = ref([])
const compressionStats = ref(null)
const smartRecommendations = ref([])

// 新建压缩配置
const newCompressionConfig = reactive({
  name: '',
  algorithm: '',
  level: 'medium',
  stream_type: '',
  max_concurrent_users: 500,
  bandwidth_limit_mbps: 3.0,
  auto_fallback: true,
  is_default: false
})

// 编辑压缩配置
const editCompressionForm = reactive({
  id: null,
  name: '',
  algorithm: '',
  level: 'medium',
  stream_type: '',
  max_concurrent_users: 500,
  bandwidth_limit_mbps: 3.0,
  auto_fallback: true,
  is_default: false
})

// 压缩预览数据
const compressionPreview = ref(null)
const selectedAlgorithmInfo = ref(null)
const algorithmComparison = ref([])
const networkImpact = ref({
  bandwidth_saved: 0,
  latency_increase: 0,
  cpu_usage: 0
})

// 表单验证规则
const compressionRules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' }
  ],
  algorithm: [
    { required: true, message: '请选择压缩算法', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择压缩级别', trigger: 'change' }
  ]
}

const compressionFormRef = ref()
const editCompressionFormRef = ref()

// 缓存管理相关数据
const cacheInfo = ref([])
const cacheInfoLoading = ref(false)
const clearingCache = reactive({
  all: false,
  web: false,
  server_cache: false,
  redis_cache: false,
  query_cache: false,
  compression_cache: false,
  tunnel_cache: false,
  api_cache: false
})

// 方法
const loadSettings = async () => {
  try {
    const response = await getSystemSettings()
    if (response.success) {
      response.data.forEach(setting => {
        if (setting.key === 'system_name') {
          generalSettings.system_name = setting.value
        } else if (setting.key === 'system_version') {
          generalSettings.system_version = setting.value
        } else if (setting.key === 'client_download_url') {
          clientSettings.client_download_url = setting.value
        }
      })
    }
  } catch (error) {
    ElMessage.error('加载设置失败')
  }
}

const updateSetting = async (key, value) => {
  try {
    await updateSystemSetting(key, { value })
    ElMessage.success('设置更新成功')
  } catch (error) {
    ElMessage.error('设置更新失败')
  }
}



const loadTunnelConfigs = async () => {
  try {
    const response = await getTunnelConfigs()
    if (response.success) {
      tunnelConfigs.value = response.data
    }
  } catch (error) {
    ElMessage.error('加载内网穿透配置失败')
  }
}

const createTunnelConfig = async () => {
  try {
    let configData = {}
    if (newTunnelConfigJson.value) {
      configData = JSON.parse(newTunnelConfigJson.value)
    }
    
    await apiCreateTunnelConfig({
      ...newTunnelConfig,
      config_data: configData
    })
    
    ElMessage.success('内网穿透配置创建成功')
    showCreateTunnelDialog.value = false
    
    // 重置表单
    Object.assign(newTunnelConfig, {
      name: '',
      tunnel_type: '',
      is_active: false
    })
    newTunnelConfigJson.value = ''
    
    // 重新加载配置
    await loadTunnelConfigs()
  } catch (error) {
    ElMessage.error('创建内网穿透配置失败')
  }
}

const toggleTunnelEnabled = async (config) => {
  try {
    await updateTunnelConfig(config.id, { is_active: config.is_active })
    ElMessage.success('配置状态更新成功')
  } catch (error) {
    ElMessage.error('配置状态更新失败')
    config.is_active = !config.is_active // 回滚状态
  }
}

const startTunnel = async (config) => {
  try {
    await apiStartTunnel(config.id)
    ElMessage.success(`${config.tunnel_type} 启动成功`)
    config.is_running = true
    config.status = 'running'
  } catch (error) {
    ElMessage.error('启动内网穿透失败')
  }
}

const stopTunnel = async (config) => {
  try {
    await apiStopTunnel(config.id)
    ElMessage.success(`${config.tunnel_type} 停止成功`)
    config.is_running = false
    config.status = 'stopped'
  } catch (error) {
    ElMessage.error('停止内网穿透失败')
  }
}

const editTunnelConfig = (config) => {
  // 填充编辑表单
  Object.assign(editTunnelForm, {
    id: config.id,
    name: config.name,
    tunnel_type: config.tunnel_type,
    is_active: config.is_active,
    force_disabled: config.force_disabled || false
  })
  editTunnelConfigJson.value = JSON.stringify(config.config_data, null, 2)
  showEditTunnelDialog.value = true
}

const saveEditTunnelConfig = async () => {
  try {
    let configData = {}
    if (editTunnelConfigJson.value) {
      configData = JSON.parse(editTunnelConfigJson.value)
    }

    await updateTunnelConfig(editTunnelForm.id, {
      name: editTunnelForm.name,
      config_data: configData,
      is_active: editTunnelForm.is_active,
      force_disabled: editTunnelForm.force_disabled
    })

    ElMessage.success('内网穿透配置更新成功')
    showEditTunnelDialog.value = false

    // 重新加载配置
    await loadTunnelConfigs()
  } catch (error) {
    ElMessage.error('更新内网穿透配置失败')
  }
}

// 测试内网穿透连接
const testTunnelConnection = async (config) => {
  try {
    testingConnections.value[config.id] = true

    // 模拟连接测试 - 这里应该调用实际的API
    const testResult = await performConnectionTest(config)

    connectionTestResults.value[config.id] = testResult

    if (testResult.success) {
      ElMessage.success(`${config.name} 连接测试成功！延迟: ${testResult.latency}ms`)
    } else {
      ElMessage.error(`${config.name} 连接测试失败: ${testResult.error}`)
    }
  } catch (error) {
    ElMessage.error(`连接测试失败: ${error.message}`)
  } finally {
    testingConnections.value[config.id] = false
  }
}

// 执行实际的连接测试
const performConnectionTest = async (config) => {
  // 根据配置类型执行不同的测试
  const testConfig = getTestConfigForTunnel(config)

  try {
    // 测试端口连通性
    const portTest = await testPortConnectivity(testConfig.host, testConfig.port)

    // 测试HTTP响应（如果有Web界面）
    let httpTest = null
    if (hasWebInterface(config.tunnel_type)) {
      httpTest = await testHttpResponse(testConfig.webUrl)
    }

    // 测试数据传输
    const dataTest = await testDataTransfer(testConfig)

    return {
      success: portTest.success && dataTest.success,
      latency: portTest.latency,
      portConnectivity: portTest,
      httpResponse: httpTest,
      dataTransfer: dataTest,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }
  }
}

// 获取测试配置
const getTestConfigForTunnel = (config) => {
  const baseConfig = {
    host: 'skfirefly.cn',
    name: config.name,
    type: config.tunnel_type
  }

  // 根据工具类型设置端口和URL
  switch (config.tunnel_type.toLowerCase()) {
    case 'frp':
      return {
        ...baseConfig,
        port: 28000,
        webUrl: 'http://skfirefly.cn:28000'
      }
    case 'linker':
      return {
        ...baseConfig,
        port: 28005,
        webUrl: null // Linker没有Web界面
      }
    default:
      return {
        ...baseConfig,
        port: 8080,
        webUrl: null
      }
  }
}

// 测试端口连通性
const testPortConnectivity = async (host, port) => {
  const startTime = Date.now()

  try {
    // 使用fetch测试连通性（实际应该调用后端API）
    const response = await fetch(`/api/v1/system/test-connection`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ host, port }),
      timeout: 5000
    })

    const latency = Date.now() - startTime

    if (response.ok) {
      return { success: true, latency, status: 'connected' }
    } else {
      return { success: false, latency, status: 'failed', error: 'Connection refused' }
    }
  } catch (error) {
    const latency = Date.now() - startTime
    return { success: false, latency, status: 'timeout', error: error.message }
  }
}

// 测试HTTP响应
const testHttpResponse = async (url) => {
  if (!url) return null

  try {
    const response = await fetch(url, {
      method: 'HEAD',
      timeout: 3000,
      mode: 'no-cors' // 避免CORS问题
    })

    return {
      success: true,
      status: response.status,
      statusText: response.statusText
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

// 测试数据传输
const testDataTransfer = async (testConfig) => {
  try {
    // 模拟数据传输测试
    const testData = 'test-data-' + Date.now()
    const startTime = Date.now()

    // 这里应该调用实际的数据传输测试API
    await new Promise(resolve => setTimeout(resolve, 100)) // 模拟延迟

    const transferTime = Date.now() - startTime

    return {
      success: true,
      transferTime,
      dataSize: testData.length,
      throughput: (testData.length / transferTime * 1000).toFixed(2) + ' bytes/s'
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

// 数据链路验证相关方法
const refreshConnectionStatus = async () => {
  refreshingStatus.value = true
  try {
    // 获取活跃的隧道配置
    await loadTunnelConfigs()
    activeTunnelConfigs.value = tunnelConfigs.value.filter(config => config.is_active)

    // 检查每个配置的实际连接状态
    for (const config of activeTunnelConfigs.value) {
      await checkConfigStatus(config)
    }

    ElMessage.success('连接状态刷新完成')
  } catch (error) {
    ElMessage.error('刷新连接状态失败')
  } finally {
    refreshingStatus.value = false
  }
}

const checkConfigStatus = async (config) => {
  try {
    const testResult = await performConnectionTest(config)
    config.service_status = testResult.success ? '运行中' : '连接失败'
    config.last_check = new Date().toISOString()
    config.uptime = testResult.success ? '正常运行' : '0分钟'
  } catch (error) {
    config.service_status = '检查失败'
    config.last_check = new Date().toISOString()
    config.uptime = '0分钟'
  }
}

const getConnectionStatusType = (status) => {
  switch (status) {
    case '运行中': return 'success'
    case '连接失败': return 'danger'
    case '检查失败': return 'warning'
    default: return 'info'
  }
}

const getConnectionStatusText = (status) => {
  return status || '未知'
}

const isConnectionReady = (config) => {
  return config.service_status === '运行中'
}

const startPerformanceTest = (config) => {
  selectedConfig.value = config
  ElMessage.info(`已选择 ${config.name} 进行性能测试`)
}

const startAllTests = async () => {
  if (!selectedConfig.value) {
    ElMessage.warning('请先选择要测试的配置')
    return
  }

  isTestingAll.value = true
  testReport.startTime = new Date().toISOString()

  try {
    // 依次执行所有测试
    await startLatencyTest()
    await startBandwidthTest()
    await startConcurrentTest()

    // 生成测试报告
    generateTestReport()
    ElMessage.success('全面测试完成')
  } catch (error) {
    ElMessage.error('测试过程中出现错误')
  } finally {
    isTestingAll.value = false
    testReport.endTime = new Date().toISOString()
  }
}

const stopAllTests = () => {
  // 停止所有正在进行的测试
  testStatus.latency.running = false
  testStatus.bandwidth.running = false
  testStatus.concurrent.running = false
  testStatus.stability.running = false

  isTestingAll.value = false
  isTestingAny.value = false

  ElMessage.info('已停止所有测试')
}

const startLatencyTest = async () => {
  if (!selectedConfig.value) return

  testStatus.latency.running = true
  testStatus.latency.progress = 0
  testStatus.latency.currentStep = '开始延迟测试...'

  try {
    const results = []
    const testCount = 10

    for (let i = 0; i < testCount; i++) {
      testStatus.latency.currentStep = `第 ${i + 1}/${testCount} 次 ping 测试`
      testStatus.latency.progress = Math.round((i / testCount) * 100)

      const result = await performSingleLatencyTest(selectedConfig.value)
      if (result.success) {
        results.push(result.latency)
      }

      // 延迟一下避免过于频繁
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    if (results.length > 0) {
      testResults.latency.average = Math.round(results.reduce((a, b) => a + b, 0) / results.length)
      testResults.latency.min = Math.min(...results)
      testResults.latency.max = Math.max(...results)
      testResults.latency.packetLoss = Math.round(((testCount - results.length) / testCount) * 100)
    }

    testStatus.latency.progress = 100
    testStatus.latency.currentStep = '延迟测试完成'
  } catch (error) {
    ElMessage.error('延迟测试失败')
  } finally {
    testStatus.latency.running = false
  }
}

const performSingleLatencyTest = async (config) => {
  const startTime = Date.now()
  try {
    const testConfig = getTestConfigForTunnel(config)
    const response = await fetch('/api/v1/system/test-connection', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ host: testConfig.host, port: testConfig.port }),
      timeout: 3000
    })

    const latency = Date.now() - startTime
    return { success: response.ok, latency }
  } catch (error) {
    return { success: false, latency: -1 }
  }
}

const startBandwidthTest = async () => {
  if (!selectedConfig.value) return

  testStatus.bandwidth.running = true
  testStatus.bandwidth.progress = 0
  testStatus.bandwidth.currentStep = '开始带宽测试...'

  try {
    const startTime = Date.now()

    // 模拟下载测试
    testStatus.bandwidth.currentStep = '测试下载速度...'
    testStatus.bandwidth.progress = 25
    const downloadSpeed = await simulateSpeedTest('download')

    // 模拟上传测试
    testStatus.bandwidth.currentStep = '测试上传速度...'
    testStatus.bandwidth.progress = 75
    const uploadSpeed = await simulateSpeedTest('upload')

    const duration = Math.round((Date.now() - startTime) / 1000)

    testResults.bandwidth.download = downloadSpeed.toFixed(2)
    testResults.bandwidth.upload = uploadSpeed.toFixed(2)
    testResults.bandwidth.totalData = ((downloadSpeed + uploadSpeed) * duration / 8).toFixed(2)
    testResults.bandwidth.duration = duration

    testStatus.bandwidth.progress = 100
    testStatus.bandwidth.currentStep = '带宽测试完成'
  } catch (error) {
    ElMessage.error('带宽测试失败')
  } finally {
    testStatus.bandwidth.running = false
  }
}

const simulateSpeedTest = async (type) => {
  // 模拟速度测试，实际应该传输真实数据
  await new Promise(resolve => setTimeout(resolve, 2000))
  return Math.random() * 50 + 10 // 10-60 Mbps
}

const startConcurrentTest = async () => {
  if (!selectedConfig.value) return

  testStatus.concurrent.running = true
  testStatus.concurrent.progress = 0
  testStatus.concurrent.currentStep = '开始并发连接测试...'

  try {
    const connectionCount = concurrentTestConfig.connections
    const promises = []
    const results = []

    testStatus.concurrent.currentStep = `创建 ${connectionCount} 个并发连接...`

    for (let i = 0; i < connectionCount; i++) {
      promises.push(performConcurrentConnection(selectedConfig.value))
      testStatus.concurrent.progress = Math.round((i / connectionCount) * 50)
      await new Promise(resolve => setTimeout(resolve, 50)) // 避免过快创建连接
    }

    testStatus.concurrent.currentStep = '等待连接结果...'
    const connectionResults = await Promise.allSettled(promises)

    let successful = 0
    let failed = 0
    let totalResponseTime = 0

    connectionResults.forEach(result => {
      if (result.status === 'fulfilled' && result.value.success) {
        successful++
        totalResponseTime += result.value.responseTime
      } else {
        failed++
      }
    })

    testResults.concurrent.successful = successful
    testResults.concurrent.failed = failed
    testResults.concurrent.avgResponseTime = successful > 0 ? Math.round(totalResponseTime / successful) : 0
    testResults.concurrent.successRate = Math.round((successful / connectionCount) * 100)

    testStatus.concurrent.progress = 100
    testStatus.concurrent.currentStep = '并发测试完成'
  } catch (error) {
    ElMessage.error('并发测试失败')
  } finally {
    testStatus.concurrent.running = false
  }
}

const performConcurrentConnection = async (config) => {
  const startTime = Date.now()
  try {
    const testConfig = getTestConfigForTunnel(config)
    const response = await fetch('/api/v1/system/test-connection', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ host: testConfig.host, port: testConfig.port }),
      timeout: 5000
    })

    const responseTime = Date.now() - startTime
    return { success: response.ok, responseTime }
  } catch (error) {
    return { success: false, responseTime: Date.now() - startTime }
  }
}

const startStabilityTest = async () => {
  if (!selectedConfig.value) return

  testStatus.stability.running = true
  testStatus.stability.progress = 0
  testStatus.stability.currentStep = '开始稳定性测试...'

  const duration = parseInt(stabilityTestConfig.duration) * 60 * 1000 // 转换为毫秒
  const startTime = Date.now()
  let disconnections = 0
  const latencies = []

  try {
    const interval = setInterval(async () => {
      if (!testStatus.stability.running) {
        clearInterval(interval)
        return
      }

      const elapsed = Date.now() - startTime
      const progress = Math.min(Math.round((elapsed / duration) * 100), 100)

      testStatus.stability.progress = progress
      testStatus.stability.currentStep = `稳定性测试进行中... ${Math.round(elapsed / 1000)}s / ${Math.round(duration / 1000)}s`

      // 执行连接测试
      const result = await performSingleLatencyTest(selectedConfig.value)
      if (result.success) {
        latencies.push(result.latency)
      } else {
        disconnections++
      }

      // 更新实时结果
      testResults.stability.elapsed = formatDuration(elapsed)
      testResults.stability.disconnections = disconnections
      testResults.stability.avgLatency = latencies.length > 0 ? Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length) : 0

      if (elapsed >= duration) {
        clearInterval(interval)

        // 计算稳定性评分
        const successRate = latencies.length / (latencies.length + disconnections)
        const avgLatency = testResults.stability.avgLatency
        let score = Math.round(successRate * 70) // 连接成功率占70%
        if (avgLatency < 50) score += 30 // 延迟小于50ms加30分
        else if (avgLatency < 100) score += 20 // 延迟小于100ms加20分
        else if (avgLatency < 200) score += 10 // 延迟小于200ms加10分

        testResults.stability.score = Math.min(score, 100)
        testStatus.stability.progress = 100
        testStatus.stability.currentStep = '稳定性测试完成'
        testStatus.stability.running = false
      }
    }, 2000) // 每2秒测试一次

  } catch (error) {
    ElMessage.error('稳定性测试失败')
    testStatus.stability.running = false
  }
}

const formatDuration = (ms) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

const generateTestReport = () => {
  testReport.details = []
  let totalScore = 0
  let testCount = 0

  // 延迟测试结果
  if (testResults.latency.average !== null) {
    const latencyScore = testResults.latency.average < 50 ? 90 :
                        testResults.latency.average < 100 ? 75 :
                        testResults.latency.average < 200 ? 60 : 40
    testReport.details.push({
      testType: '延迟测试',
      result: `平均 ${testResults.latency.average}ms，丢包率 ${testResults.latency.packetLoss}%`,
      score: latencyScore,
      status: latencyScore >= 70 ? 'passed' : 'failed',
      notes: testResults.latency.average < 100 ? '延迟表现良好' : '延迟较高，可能影响实时性'
    })
    totalScore += latencyScore
    testCount++
  }

  // 带宽测试结果
  if (testResults.bandwidth.download !== null) {
    const bandwidthScore = parseFloat(testResults.bandwidth.download) > 20 ? 90 :
                          parseFloat(testResults.bandwidth.download) > 10 ? 75 :
                          parseFloat(testResults.bandwidth.download) > 5 ? 60 : 40
    testReport.details.push({
      testType: '带宽测试',
      result: `下载 ${testResults.bandwidth.download}Mbps，上传 ${testResults.bandwidth.upload}Mbps`,
      score: bandwidthScore,
      status: bandwidthScore >= 70 ? 'passed' : 'failed',
      notes: parseFloat(testResults.bandwidth.download) > 10 ? '带宽充足' : '带宽较低'
    })
    totalScore += bandwidthScore
    testCount++
  }

  // 并发测试结果
  if (testResults.concurrent.successful !== null) {
    const concurrentScore = testResults.concurrent.successRate > 90 ? 90 :
                           testResults.concurrent.successRate > 80 ? 75 :
                           testResults.concurrent.successRate > 70 ? 60 : 40
    testReport.details.push({
      testType: '并发测试',
      result: `成功率 ${testResults.concurrent.successRate}%，平均响应 ${testResults.concurrent.avgResponseTime}ms`,
      score: concurrentScore,
      status: concurrentScore >= 70 ? 'passed' : 'failed',
      notes: testResults.concurrent.successRate > 85 ? '并发性能优秀' : '并发性能需要优化'
    })
    totalScore += concurrentScore
    testCount++
  }

  // 稳定性测试结果
  if (testResults.stability.score !== null) {
    testReport.details.push({
      testType: '稳定性测试',
      result: `评分 ${testResults.stability.score}/100，中断 ${testResults.stability.disconnections} 次`,
      score: testResults.stability.score,
      status: testResults.stability.score >= 70 ? 'passed' : 'failed',
      notes: testResults.stability.score > 85 ? '连接非常稳定' : '连接稳定性一般'
    })
    totalScore += testResults.stability.score
    testCount++
  }

  testReport.overallScore = testCount > 0 ? Math.round(totalScore / testCount) : 0
}

const formatTime = (timeString) => {
  if (!timeString) return '--'
  return new Date(timeString).toLocaleString()
}

const exportReport = () => {
  // 导出测试报告为JSON或PDF
  const reportData = {
    config: selectedConfig.value,
    testResults: testResults,
    report: testReport,
    timestamp: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `datalink-test-report-${selectedConfig.value?.name}-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('测试报告已导出')
}

const clearResults = () => {
  // 清除所有测试结果
  Object.keys(testResults.latency).forEach(key => testResults.latency[key] = null)
  Object.keys(testResults.bandwidth).forEach(key => testResults.bandwidth[key] = null)
  Object.keys(testResults.concurrent).forEach(key => testResults.concurrent[key] = null)
  Object.keys(testResults.stability).forEach(key => testResults.stability[key] = null)
  Object.keys(testResults.compression).forEach(key => testResults.compression[key] = null)

  testReport.details = []
  testReport.overallScore = 0
  testReport.startTime = null
  testReport.endTime = null

  ElMessage.success('测试结果已清除')
}

// 数据压缩测试相关方法
const startCompressionTest = async () => {
  if (!selectedConfig.value) {
    ElMessage.warning('请先选择要测试的配置')
    return
  }

  testStatus.compression.running = true
  testStatus.compression.progress = 0
  testStatus.compression.currentStep = '初始化数据压缩测试...'

  try {
    // 生成测试数据
    testStatus.compression.currentStep = '生成测试数据...'
    testStatus.compression.progress = 10
    const testData = generateTestData(compressionTestConfig.dataType, parseInt(compressionTestConfig.dataSize))

    // 执行压缩测试
    testStatus.compression.currentStep = '执行压缩性能测试...'
    testStatus.compression.progress = 30

    const compressionResults = await performCompressionTest(testData)

    // 执行传输测试
    testStatus.compression.currentStep = '执行数据传输测试...'
    testStatus.compression.progress = 60

    const transferResults = await performTransferTest(compressionResults.compressedData)

    // 计算最终结果
    testStatus.compression.currentStep = '计算测试结果...'
    testStatus.compression.progress = 90

    updateCompressionResults(testData, compressionResults, transferResults)

    testStatus.compression.progress = 100
    testStatus.compression.currentStep = '数据压缩测试完成'

    ElMessage.success('数据压缩测试完成')
  } catch (error) {
    ElMessage.error(`数据压缩测试失败: ${error.message}`)
  } finally {
    testStatus.compression.running = false
  }
}

const stopCompressionTest = () => {
  testStatus.compression.running = false
  ElMessage.info('数据压缩测试已停止')
}

const generateTestData = (dataType, sizeMB) => {
  const sizeBytes = sizeMB * 1024 * 1024
  let data = ''

  switch (dataType) {
    case 'json':
      // 生成JSON配置数据
      data = generateJSONData(sizeBytes)
      break
    case 'binary':
      // 生成二进制设备数据
      data = generateBinaryData(sizeBytes)
      break
    case 'text':
      // 生成文本日志数据
      data = generateTextData(sizeBytes)
      break
    case 'mixed':
      // 生成混合数据包
      data = generateMixedData(sizeBytes)
      break
    default:
      data = generateJSONData(sizeBytes)
  }

  return {
    type: dataType,
    size: sizeBytes,
    data: data,
    timestamp: new Date().toISOString()
  }
}

const generateJSONData = (targetSize) => {
  const baseConfig = {
    system: {
      version: '1.0.0',
      environment: 'production',
      features: ['compression', 'encryption', 'monitoring'],
      settings: {
        maxConnections: 1000,
        timeout: 30000,
        retryAttempts: 3,
        enableLogging: true
      }
    },
    users: [],
    devices: [],
    tunnels: [],
    logs: []
  }

  // 填充数据直到达到目标大小
  let currentData = JSON.stringify(baseConfig)
  while (currentData.length < targetSize) {
    baseConfig.users.push({
      id: Math.random().toString(36).substr(2, 9),
      name: `User_${Math.random().toString(36).substr(2, 8)}`,
      email: `user${Math.random().toString(36).substr(2, 5)}@example.com`,
      role: ['admin', 'user', 'guest'][Math.floor(Math.random() * 3)],
      permissions: ['read', 'write', 'execute'],
      lastLogin: new Date().toISOString(),
      settings: {
        theme: 'dark',
        language: 'zh-CN',
        notifications: true
      }
    })

    baseConfig.devices.push({
      id: Math.random().toString(36).substr(2, 9),
      name: `Device_${Math.random().toString(36).substr(2, 8)}`,
      type: ['usb', 'network', 'serial'][Math.floor(Math.random() * 3)],
      status: ['online', 'offline', 'error'][Math.floor(Math.random() * 3)],
      lastSeen: new Date().toISOString(),
      properties: {
        vendor: 'Generic',
        model: 'Model_' + Math.random().toString(36).substr(2, 6),
        version: '1.0.0'
      }
    })

    currentData = JSON.stringify(baseConfig)
  }

  return currentData.substring(0, targetSize)
}

const generateBinaryData = (targetSize) => {
  // 模拟USB设备数据传输
  const buffer = new ArrayBuffer(targetSize)
  const view = new Uint8Array(buffer)

  // 填充模拟的设备数据
  for (let i = 0; i < targetSize; i++) {
    view[i] = Math.floor(Math.random() * 256)
  }

  // 转换为Base64字符串以便传输
  return btoa(String.fromCharCode.apply(null, view))
}

const generateTextData = (targetSize) => {
  const logTemplates = [
    '[INFO] System startup completed successfully',
    '[WARN] High memory usage detected: 85%',
    '[ERROR] Connection timeout to remote server',
    '[DEBUG] Processing user request: GET /api/v1/users',
    '[INFO] Database connection established',
    '[WARN] SSL certificate expires in 30 days',
    '[ERROR] Failed to authenticate user session',
    '[INFO] Backup process completed successfully',
    '[DEBUG] Cache hit ratio: 92%',
    '[WARN] Disk space usage: 78%'
  ]

  let logData = ''
  while (logData.length < targetSize) {
    const timestamp = new Date().toISOString()
    const template = logTemplates[Math.floor(Math.random() * logTemplates.length)]
    const sessionId = Math.random().toString(36).substr(2, 16)
    const logEntry = `${timestamp} [${sessionId}] ${template}\n`
    logData += logEntry
  }

  return logData.substring(0, targetSize)
}

const generateMixedData = (targetSize) => {
  const jsonPart = generateJSONData(Math.floor(targetSize * 0.4))
  const binaryPart = generateBinaryData(Math.floor(targetSize * 0.3))
  const textPart = generateTextData(Math.floor(targetSize * 0.3))

  return JSON.stringify({
    json: jsonPart,
    binary: binaryPart,
    text: textPart,
    metadata: {
      type: 'mixed',
      timestamp: new Date().toISOString(),
      components: ['json', 'binary', 'text']
    }
  })
}

const performCompressionTest = async (testData) => {
  const startTime = Date.now()

  try {
    // 调用后端API进行压缩测试
    const response = await fetch('/api/v1/system/test-compression', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        data: testData.data,
        algorithm: compressionTestConfig.algorithm,
        level: compressionTestConfig.compressionLevel,
        enableCompression: compressionTestConfig.enableCompression
      })
    })

    if (!response.ok) {
      throw new Error('压缩测试API调用失败')
    }

    const result = await response.json()
    const compressionTime = Date.now() - startTime

    return {
      originalSize: testData.size,
      compressedSize: result.compressedSize,
      compressedData: result.compressedData,
      compressionTime: compressionTime,
      algorithm: compressionTestConfig.algorithm,
      compressionRatio: ((testData.size - result.compressedSize) / testData.size * 100).toFixed(2)
    }
  } catch (error) {
    // 如果后端API不可用，使用前端模拟
    return simulateCompressionTest(testData)
  }
}

const simulateCompressionTest = (testData) => {
  // 模拟不同算法的压缩效果
  const compressionRatios = {
    snappy: { json: 0.65, binary: 0.85, text: 0.45, mixed: 0.60 },
    lz4: { json: 0.60, binary: 0.80, text: 0.40, mixed: 0.55 },
    zstd: { json: 0.50, binary: 0.70, text: 0.30, mixed: 0.45 }
  }

  const algorithm = compressionTestConfig.algorithm
  const dataType = compressionTestConfig.dataType
  const ratio = compressionRatios[algorithm][dataType] || 0.70

  const compressedSize = Math.floor(testData.size * ratio)
  const compressionTime = Math.floor(testData.size / 1024 / 1024 * 50) // 模拟压缩时间

  return {
    originalSize: testData.size,
    compressedSize: compressedSize,
    compressedData: testData.data.substring(0, compressedSize),
    compressionTime: compressionTime,
    algorithm: algorithm,
    compressionRatio: ((testData.size - compressedSize) / testData.size * 100).toFixed(2)
  }
}

const performTransferTest = async (compressedData) => {
  const startTime = Date.now()
  const concurrency = parseInt(compressionTestConfig.concurrency)
  const duration = compressionTestConfig.duration === 'continuous' ?
                   60 : parseInt(compressionTestConfig.duration)

  let totalTransferred = 0
  let transferCount = 0
  const transferPromises = []

  // 创建并发传输任务
  for (let i = 0; i < concurrency; i++) {
    transferPromises.push(performSingleTransfer(compressedData, duration))
  }

  try {
    const results = await Promise.allSettled(transferPromises)

    results.forEach(result => {
      if (result.status === 'fulfilled') {
        totalTransferred += result.value.bytesTransferred
        transferCount += result.value.transferCount
      }
    })

    const totalTime = Date.now() - startTime
    const transferSpeed = (totalTransferred / 1024 / 1024) / (totalTime / 1000) // MB/s

    return {
      totalTransferred: totalTransferred,
      transferCount: transferCount,
      transferSpeed: transferSpeed.toFixed(2),
      totalTime: totalTime,
      concurrency: concurrency
    }
  } catch (error) {
    throw new Error(`传输测试失败: ${error.message}`)
  }
}

const performSingleTransfer = async (data, durationSeconds) => {
  const startTime = Date.now()
  const endTime = startTime + (durationSeconds * 1000)
  let bytesTransferred = 0
  let transferCount = 0

  while (Date.now() < endTime && testStatus.compression.running) {
    try {
      // 模拟数据传输
      const transferResult = await simulateDataTransfer(data)
      bytesTransferred += transferResult.bytes
      transferCount++

      // 更新进度
      const elapsed = Date.now() - startTime
      const progress = Math.min((elapsed / (durationSeconds * 1000)) * 100, 100)
      testStatus.compression.progress = Math.max(testStatus.compression.progress, 60 + progress * 0.3)

      // 短暂延迟避免过度占用资源
      await new Promise(resolve => setTimeout(resolve, 10))
    } catch (error) {
      console.error('单次传输失败:', error)
      break
    }
  }

  return { bytesTransferred, transferCount }
}

const simulateDataTransfer = async (data) => {
  // 模拟网络传输延迟
  const latency = Math.random() * 50 + 10 // 10-60ms
  await new Promise(resolve => setTimeout(resolve, latency))

  return {
    bytes: data.length,
    latency: latency,
    success: true
  }
}

const updateCompressionResults = (originalData, compressionResults, transferResults) => {
  testResults.compression.originalSize = formatBytes(originalData.size)
  testResults.compression.compressedSize = formatBytes(compressionResults.compressedSize)
  testResults.compression.compressionRatio = compressionResults.compressionRatio
  testResults.compression.transferSpeed = transferResults.transferSpeed
  testResults.compression.compressionTime = compressionResults.compressionTime
  testResults.compression.decompressionTime = Math.floor(compressionResults.compressionTime * 0.3) // 解压通常更快
  testResults.compression.totalTransferred = formatBytes(transferResults.totalTransferred)

  // 计算节省的带宽
  const originalTransferSize = originalData.size * transferResults.transferCount
  const actualTransferSize = transferResults.totalTransferred
  const bandwidthSaved = formatBytes(originalTransferSize - actualTransferSize)
  testResults.compression.bandwidthSaved = bandwidthSaved

  // 生成图表数据
  testResults.compression.chartData = [
    {
      algorithm: compressionResults.algorithm.toUpperCase(),
      ratio: parseFloat(compressionResults.compressionRatio),
      size: compressionResults.compressedSize
    }
  ]
}

const formatBytes = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const upgradeTunnel = async (config) => {
  try {
    await ElMessageBox.confirm(
      `确定要升级 ${config.name} 的内网穿透工具吗？这将下载并安装最新版本。`,
      '升级确认',
      {
        confirmButtonText: '确定升级',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.info('升级功能正在开发中，敬请期待')
    // 升级逻辑将在后续版本中实现
  } catch {
    // 用户取消
  }
}

const toggleForceDisabled = async (config) => {
  try {
    const action = config.force_disabled ? '解锁' : '锁定'
    await ElMessageBox.confirm(
      `确定要${action} ${config.name} 吗？${config.force_disabled ? '解锁后可以正常启用配置。' : '锁定后将完全禁止启动，用于安全防护。'}`,
      `${action}确认`,
      {
        confirmButtonText: `确定${action}`,
        cancelButtonText: '取消',
        type: config.force_disabled ? 'success' : 'warning'
      }
    )

    config.force_disabled = !config.force_disabled
    await updateTunnelConfig(config.id, { force_disabled: config.force_disabled })

    ElMessage.success(`${config.name} 已${action}`)

    // 如果锁定了配置，同时禁用它
    if (config.force_disabled && config.is_active) {
      config.is_active = false
      await updateTunnelConfig(config.id, { is_active: false })
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
      config.force_disabled = !config.force_disabled // 回滚状态
    }
  }
}

const deleteTunnelConfig = async (config) => {
  try {
    await ElMessageBox.confirm('确定要删除此配置吗？', '确认删除', {
      type: 'warning'
    })
    
    await apiDeleteTunnelConfig(config.id)
    ElMessage.success('配置删除成功')
    await loadTunnelConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除配置失败')
    }
  }
}

// 辅助方法
const getTunnelTypeColor = (type) => {
  const colors = {
    headscale: 'primary',
    netbird: 'success',
    zerotier: 'warning',
    nebula: 'info',
    frp: 'primary',
    nps: 'success',
    rathole: 'info',
    linker: 'danger'
  }
  return colors[type] || 'default'
}

const getStatusColor = (status) => {
  const colors = {
    running: 'success',
    stopped: 'info',
    error: 'danger'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    running: '运行中',
    stopped: '已停止',
    error: '错误'
  }
  return texts[status] || '未知'
}

// 检查工具是否有Web管理界面
const hasWebInterface = (tunnelType) => {
  const webInterfaceTools = ['frp', 'nps', 'headscale', 'netbird', 'zerotier']
  return webInterfaceTools.includes(tunnelType.toLowerCase())
}

// 检查是否为服务端配置（有管理界面）
const isServerConfig = (config) => {
  if (config.tunnel_type.toLowerCase() === 'frp') {
    const configData = config.config_data || {}
    // FRP服务端配置会有dashboard_port或web_port
    return configData.dashboard_port || configData.web_port || configData.bind_port
  }
  return true // 其他类型默认认为有管理界面
}

// 打开管理界面
const openManagementInterface = async (config) => {
  try {
    const response = await fetch(`/api/v1/system/tunnels/${config.id}/management-url`)
    const data = await response.json()

    if (data.success && data.url) {
      // 在新窗口中打开管理界面
      window.open(data.url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes')
    } else {
      ElMessage.warning(data.message || '无法获取管理界面地址')
    }
  } catch (error) {
    console.error('获取管理界面地址失败:', error)
    ElMessage.error('获取管理界面地址失败')
  }
}

// 数据压缩相关方法
const loadCompressionConfigs = async () => {
  try {
    compressionLoading.value = true
    const response = await getCompressionConfigs()
    
    // 安全的响应格式处理
    if (response && typeof response === 'object') {
      if (response.success === true) {
        // 标准成功响应格式
        compressionConfigs.value = Array.isArray(response.data) ? response.data : []
        activeCompressionConfig.value = compressionConfigs.value.find(config => config?.is_active) || null
      } else if (response.data && response.data.success === true) {
        // 嵌套成功响应格式
        compressionConfigs.value = Array.isArray(response.data.data) ? response.data.data : []
        activeCompressionConfig.value = compressionConfigs.value.find(config => config?.is_active) || null
      } else if (Array.isArray(response)) {
        // 直接数组响应
        compressionConfigs.value = response
        activeCompressionConfig.value = compressionConfigs.value.find(config => config?.is_active) || null
      } else if (Array.isArray(response.data)) {
        // 数据在 data 字段中的数组
        compressionConfigs.value = response.data
        activeCompressionConfig.value = compressionConfigs.value.find(config => config?.is_active) || null
      } else {
        // 未知格式，设置为空数组
        console.warn('未知的响应格式:', response)
        compressionConfigs.value = []
        activeCompressionConfig.value = null
      }
    } else {
      // 响应为空或非对象
      console.warn('无效的响应:', response)
      compressionConfigs.value = []
      activeCompressionConfig.value = null
    }
  } catch (error) {
    console.error('加载压缩配置失败:', error)
    ElMessage.error(`加载压缩配置失败: ${error.message || '未知错误'}`)
    compressionConfigs.value = []
    activeCompressionConfig.value = null
  } finally {
    compressionLoading.value = false
  }
}

const loadAvailableAlgorithms = async () => {
  try {
    const response = await getAvailableAlgorithms()

    // 🔧 修复：处理API中间件包装格式
    let algorithmData = response
    if (response && response.success && response.data) {
      console.log('🔧 loadAvailableAlgorithms - 检测到API中间件包装格式，提取data字段')
      algorithmData = response.data
    }
    console.log('loadAvailableAlgorithms - 处理后的算法数据:', algorithmData)

    // 兼容不同的响应格式
    let algorithmsData = null;

    if (algorithmData && algorithmData.algorithms) {
      // 标准格式：{algorithms: [...]}
      algorithmsData = algorithmData.algorithms;
    } else if (Array.isArray(algorithmData)) {
      // 直接数组格式
      algorithmsData = algorithmData;
    } else if (algorithmData && algorithmData.data && algorithmData.data.algorithms) {
      // 嵌套格式：{data: {algorithms: [...]}}
      algorithmsData = algorithmData.data.algorithms;
    }
    
    if (algorithmsData) {
      // 为每个算法添加enabled属性
      availableAlgorithms.value = algorithmsData.map(algo => ({
        ...algo,
        enabled: algo.available // 默认可用的算法为启用状态
      }));
    } else {
      throw new Error('无法解析算法数据');
    }
  } catch (error) {
    console.error('加载可用算法失败:', error)
    // 提供默认数据
    availableAlgorithms.value = [
      {
        name: 'snappy',
        display_name: 'Snappy',
        description: 'Google开发的快速压缩算法，速度优先',
        available: true,
        enabled: true
      },
      {
        name: 'lz4',
        display_name: 'LZ4',
        description: '超高速压缩算法，极低延迟',
        available: true,
        enabled: true
      },
      {
        name: 'zstd',
        display_name: 'Zstandard',
        description: 'Facebook开发的高效压缩算法，压缩率优先',
        available: true,
        enabled: false
      }
    ]
  }
}

const loadCompressionStats = async () => {
  try {
    const response = await getCompressionStatus()
    
    // 兼容不同的响应格式
    if (response.success && response.data && response.data.global_stats) {
      // 标准格式
      compressionStats.value = response.data.global_stats
    } else if (response.data && response.data.success && response.data.data && response.data.data.global_stats) {
      // 嵌套格式
      compressionStats.value = response.data.data.global_stats
    } else if (response.global_stats) {
      // 直接包含统计数据的格式
      compressionStats.value = response.global_stats
    } else if (response.data && response.data.global_stats) {
      // 数据在data字段
      compressionStats.value = response.data.global_stats
    } else {
      console.warn('无法解析压缩统计数据格式:', response)
    }
  } catch (error) {
    console.error('加载压缩统计失败:', error)
  }
}

const loadSmartRecommendations = async () => {
  try {
    const response = await getSmartRecommendations()
    
    // 兼容不同的响应格式
    if (response.success && response.data) {
      // 标准格式
      smartRecommendations.value = response.data
    } else if (response.data && response.data.success && response.data.data) {
      // 嵌套格式
      smartRecommendations.value = response.data.data
    } else if (response.recommendations) {
      // 直接包含推荐数据的格式
      smartRecommendations.value = response.recommendations
    } else if (response.data && response.data.recommendations) {
      // 数据在data.recommendations字段
      smartRecommendations.value = response.data.recommendations
    } else {
      console.warn('无法解析智能推荐数据格式:', response)
    }
  } catch (error) {
    console.error('加载智能推荐失败:', error)
  }
}

const onAlgorithmChange = () => {
  const algorithm = availableAlgorithms.value.find(a => a.name === newCompressionConfig.algorithm)
  selectedAlgorithmInfo.value = algorithm

  // 生成压缩预览
  if (algorithm) {
    generateCompressionPreview()
  }
}

const generateCompressionPreview = () => {
  const algorithm = selectedAlgorithmInfo.value
  if (!algorithm) return

  // 模拟预览数据
  const previewData = {
    snappy: { rate: 85, speed: 520, memory: 440 },
    lz4: { rate: 78, speed: 610, memory: 410 },
    zstd: { rate: 92, speed: 380, memory: 520 }
  }

  const data = previewData[algorithm.name] || { rate: 80, speed: 400, memory: 500 }

  compressionPreview.value = {
    estimated_rate: data.rate,
    estimated_speed: data.speed,
    estimated_memory: data.memory
  }
}

const createCompressionConfig = async () => {
  try {
    await compressionFormRef.value.validate()
    creatingConfig.value = true

    const response = await apiCreateCompressionConfig(newCompressionConfig)
    
    // 安全的响应处理
    if (response && typeof response === 'object') {
      if (response.success === true || (response.data && response.data.success === true)) {
        ElMessage.success('压缩配置创建成功')
        showCreateCompressionDialog.value = false
        resetCompressionForm()
        await loadCompressionConfigs()
      } else {
        const errorMsg = response.message || response.data?.message || '创建压缩配置失败'
        ElMessage.error(errorMsg)
      }
    } else {
      ElMessage.error('服务器响应格式错误')
    }
  } catch (error) {
    console.error('创建压缩配置失败:', error)
    ElMessage.error('创建压缩配置失败')
  } finally {
    creatingConfig.value = false
  }
}

const editCompressionConfig = (config) => {
  Object.assign(editCompressionForm, config)
  showEditCompressionDialog.value = true
}

const updateCompressionConfig = async () => {
  try {
    await editCompressionFormRef.value.validate()
    updatingConfig.value = true

    const response = await apiUpdateCompressionConfig(editCompressionForm.id, editCompressionForm)
    // 兼容不同的响应格式
    if (response.success) {
      // 直接返回标准格式
      ElMessage.success('压缩配置更新成功')
      showEditCompressionDialog.value = false
      await loadCompressionConfigs()
    } else if (response.data && response.data.success) {
      // 嵌套格式
      ElMessage.success('压缩配置更新成功')
      showEditCompressionDialog.value = false
      await loadCompressionConfigs()
    } else {
      ElMessage.error('更新压缩配置失败')
    }
  } catch (error) {
    console.error('更新压缩配置失败:', error)
    ElMessage.error('更新压缩配置失败')
  } finally {
    updatingConfig.value = false
  }
}

const activateCompressionConfig = async (configId) => {
  try {
    switchingConfig.value = configId

    // 显示切换进度
    const loadingMessage = ElMessage({
      message: '正在切换压缩配置，请稍候...',
      type: 'info',
      duration: 0
    })

    const response = await apiActivateCompressionConfig(configId)
    loadingMessage.close()

    // 兼容不同的响应格式
    if (response.success) {
      // 直接返回标准格式
      ElMessage.success('压缩配置已激活，新连接将使用新配置')
      await loadCompressionConfigs()
      await loadCompressionStats()
    } else if (response.data && response.data.success) {
      // 嵌套格式
      ElMessage.success('压缩配置已激活，新连接将使用新配置')
      await loadCompressionConfigs()
      await loadCompressionStats()
    } else {
      ElMessage.error('激活压缩配置失败')
    }
  } catch (error) {
    console.error('激活压缩配置失败:', error)
    ElMessage.error('激活压缩配置失败')
  } finally {
    switchingConfig.value = null
  }
}

const deactivateCompressionConfig = async (configId) => {
  try {
    switchingConfig.value = configId

    const response = await apiDeactivateCompressionConfig(configId)
    // 兼容不同的响应格式
    if (response.success) {
      // 直接返回标准格式
      ElMessage.success('压缩配置已停用')
      await loadCompressionConfigs()
      await loadCompressionStats()
    } else if (response.data && response.data.success) {
      // 嵌套格式
      ElMessage.success('压缩配置已停用')
      await loadCompressionConfigs()
      await loadCompressionStats()
    } else {
      ElMessage.error('停用压缩配置失败')
    }
  } catch (error) {
    console.error('停用压缩配置失败:', error)
    ElMessage.error('停用压缩配置失败')
  } finally {
    switchingConfig.value = null
  }
}

const deleteCompressionConfig = async (configId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个压缩配置吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await apiDeleteCompressionConfig(configId)
    // 兼容不同的响应格式
    if (response.success) {
      // 直接返回标准格式
      ElMessage.success('压缩配置已删除')
      await loadCompressionConfigs()
    } else if (response.data && response.data.success) {
      // 嵌套格式
      ElMessage.success('压缩配置已删除')
      await loadCompressionConfigs()
    } else {
      ElMessage.error('删除压缩配置失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除压缩配置失败:', error)
      ElMessage.error('删除压缩配置失败')
    }
  }
}

const exportCompressionConfig = (config) => {
  const exportData = {
    name: config.name,
    algorithm: config.algorithm,
    level: config.level,
    stream_type: config.stream_type,
    max_concurrent_users: config.max_concurrent_users,
    bandwidth_limit_mbps: config.bandwidth_limit_mbps,
    auto_fallback: config.auto_fallback,
    config_data: config.config_data
  }

  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `compression_config_${config.name}.json`
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('配置已导出')
}

const handleConfigFileChange = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target.result)
      Object.assign(newCompressionConfig, config)
      ElMessage.success('配置文件读取成功')
    } catch (error) {
      ElMessage.error('配置文件格式错误')
    }
  }
  reader.readAsText(file.raw)
}

const importCompressionConfig = async () => {
  try {
    importingConfig.value = true

    const response = await apiCreateCompressionConfig(newCompressionConfig)
    // 兼容不同的响应格式
    if (response.success) {
      // 直接返回标准格式
      ElMessage.success('配置导入成功')
      showImportDialog.value = false
      resetCompressionForm()
      await loadCompressionConfigs()
    } else if (response.data && response.data.success) {
      // 嵌套格式
      ElMessage.success('配置导入成功')
      showImportDialog.value = false
      resetCompressionForm()
      await loadCompressionConfigs()
    }
  } catch (error) {
    console.error('导入配置失败:', error)
    ElMessage.error('导入配置失败')
  } finally {
    importingConfig.value = false
  }
}

const applyRecommendation = async (recommendation) => {
  try {
    Object.assign(newCompressionConfig, recommendation.config)
    showCreateCompressionDialog.value = true
    ElMessage.success('已应用推荐配置')
  } catch (error) {
    ElMessage.error('应用推荐失败')
  }
}

const resetCompressionForm = () => {
  Object.assign(newCompressionConfig, {
    name: '',
    algorithm: '',
    level: 'medium',
    stream_type: '',
    max_concurrent_users: 500,
    bandwidth_limit_mbps: 3.0,
    auto_fallback: true,
    is_default: false
  })
  compressionPreview.value = null
  selectedAlgorithmInfo.value = null
  compressionFormRef.value?.resetFields()
}

// 工具函数已在上方定义

const getAlgorithmTagType = (algorithm) => {
  const types = {
    'snappy': 'success',
    'lz4': 'warning',
    'zstd': 'primary'
  }
  return types[algorithm] || 'info'
}

const getAlgorithmDisplayName = (algorithm) => {
  const names = {
    'snappy': 'Snappy',
    'lz4': 'LZ4',
    'zstd': 'Zstandard'
  }
  return names[algorithm] || algorithm.toUpperCase()
}

const getLevelTagType = (level) => {
  const types = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  }
  return types[level] || 'info'
}

const getLevelDisplayName = (level) => {
  const names = {
    'low': '低',
    'medium': '中',
    'high': '高'
  }
  return names[level] || level
}

const getStreamTypeDisplayName = (streamType) => {
  const names = {
    'web_data': 'Web数据',
    'client_data': '专属客户端数据',
    'general_data': '通用数据',
    // 兼容旧的数据流类型
    'web_http': 'Web HTTP (旧)',
    'web_https': 'Web HTTPS (旧)',
    'vh_usb': 'VirtualHere USB (旧)',
    'vh_ctrl': 'VirtualHere 控制 (旧)'
  }
  return names[streamType] || '通用数据'
}

// 数据压缩算法管理方法
const toggleAlgorithm = async (algorithm) => {
  try {
    // 这里应该调用API来启用/禁用算法
    ElMessage.success(`${algorithm.display_name} ${algorithm.enabled ? '已启用' : '已禁用'}`)
  } catch (error) {
    ElMessage.error('算法状态切换失败')
    algorithm.enabled = !algorithm.enabled // 回滚状态
  }
}

const configureAlgorithm = (algorithm) => {
  ElMessage.info(`配置 ${algorithm.display_name} 算法`)
  // 这里可以打开配置对话框
}

const testAlgorithm = async (algorithm) => {
  try {
    ElMessage.info(`正在测试 ${algorithm.display_name} 算法...`)
    // 这里应该调用API来测试算法
    setTimeout(() => {
      ElMessage.success(`${algorithm.display_name} 算法测试通过`)
    }, 2000)
  } catch (error) {
    ElMessage.error('算法测试失败')
  }
}

// 生命周期
onMounted(() => {
  loadSettings()
  loadTunnelConfigs()
  loadCompressionConfigs()
  loadAvailableAlgorithms()
  loadCompressionStats()
  loadSmartRecommendations()

  // 初始化数据链路验证
  refreshConnectionStatus()

  // 定时刷新压缩统计
  setInterval(loadCompressionStats, 5000)
  // 定时刷新智能推荐
  setInterval(loadSmartRecommendations, 30000)

  // 加载缓存信息
  loadCacheInfo()
})

// 缓存管理方法
const loadCacheInfo = async () => {
  cacheInfoLoading.value = true
  try {
    const response = await getCacheInfo()
    if (response.success) {
      cacheInfo.value = response.data
    } else {
      ElMessage.error(response.message || '获取缓存信息失败')
    }
  } catch (error) {
    console.error('获取缓存信息失败:', error)
    ElMessage.error('获取缓存信息失败')
  } finally {
    cacheInfoLoading.value = false
  }
}

const clearSingleCache = async (cacheType) => {
  try {
    await ElMessageBox.confirm(
      `确定要清除 ${getCacheTypeName(cacheType)} 吗？`,
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    clearingCache[cacheType] = true

    const response = await clearCache(cacheType)
    if (response.success) {
      ElMessage.success(response.message || '缓存清除成功')
      await loadCacheInfo() // 刷新缓存信息
    } else {
      ElMessage.error(response.message || '缓存清除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清除缓存失败:', error)
      ElMessage.error('清除缓存失败')
    }
  } finally {
    clearingCache[cacheType] = false
  }
}

const clearAllCaches = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除所有服务器缓存吗？这可能会导致短暂的性能下降。',
      '确认清除所有缓存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    clearingCache.all = true

    const response = await clearAllCache()
    if (response.success) {
      ElMessage.success(response.message || '所有缓存清除成功')
      await loadCacheInfo() // 刷新缓存信息
    } else {
      ElMessage.error(response.message || '清除缓存失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清除所有缓存失败:', error)
      ElMessage.error('清除所有缓存失败')
    }
  } finally {
    clearingCache.all = false
  }
}

const clearWebCache = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除Web页面缓存吗？页面将会自动刷新。',
      '确认清除Web缓存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    clearingCache.web = true

    try {
      await apiClearWebCache()
      ElMessage.success('Web页面缓存清除成功，页面即将刷新')

      // 延迟刷新页面
      setTimeout(() => {
        forceRefreshPage()
      }, 1000)
    } catch (error) {
      ElMessage.error('Web页面缓存清除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清除Web缓存失败:', error)
      ElMessage.error('清除Web缓存失败')
    }
  } finally {
    clearingCache.web = false
  }
}

const refreshCacheInfo = () => {
  loadCacheInfo()
}

const getCacheTypeName = (type) => {
  const names = {
    server_cache: '服务器程序缓存',
    redis_cache: 'Redis数据缓存',
    query_cache: '数据库查询缓存',
    compression_cache: '压缩统计缓存',
    tunnel_cache: '隧道状态缓存',
    api_cache: 'API响应缓存'
  }
  return names[type] || type
}

const getCacheIcon = (type) => {
  const icons = {
    server_cache: Setting,
    redis_cache: Monitor,
    query_cache: DataAnalysis,
    compression_cache: TrendCharts,
    tunnel_cache: Connection,
    api_cache: Document
  }
  return icons[type] || FolderOpened
}

const getCacheStatusText = (status) => {
  const texts = {
    active: '活跃',
    inactive: '未激活',
    error: '错误'
  }
  return texts[status] || status
}
</script>

<style scoped>
/* 主容器样式 - 优化空间利用率 */
.system-settings-container {
  padding: 16px;
}

.main-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

/* 卡片头部样式 - 统一风格 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-subtitle {
  color: #909399;
  font-size: 14px;
}

/* 标准容器和卡片样式 - 优化间距 */
.standard-container {
  padding: 16px;
}

.standard-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid #ebeef5;
  margin-bottom: 16px;
}

.standard-card:last-child {
  margin-bottom: 0;
}

.standard-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 操作按钮样式 - 左对齐，紧凑布局 */
.action-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
}

/* 公网链接操作按钮样式 - 左对齐紧凑布局 */
.tunnel-action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  padding-left: 0;
}

.tunnel-action-buttons .el-button.is-circle {
  width: 28px;
  height: 28px;
  padding: 0;
}

/* 表格样式统一 - 优化行高和间距 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ebeef5;
}

:deep(.el-table__header-wrapper) {
  background-color: #fafafa;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
  height: 44px;
  padding: 8px 12px;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f5f7fa;
  height: 44px;
  padding: 8px 12px;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 按钮样式统一 - 优化间距 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  margin-right: 6px;
  padding: 8px 16px;
  font-size: 14px;
}

:deep(.el-button--small) {
  padding: 6px 12px;
  font-size: 12px;
}

:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-button--success) {
  background-color: #67c23a;
  border-color: #67c23a;
}

:deep(.el-button--warning) {
  background-color: #e6a23c;
  border-color: #e6a23c;
}

:deep(.el-button--danger) {
  background-color: #f56c6c;
  border-color: #f56c6c;
}

:deep(.el-button--info) {
  background-color: #909399;
  border-color: #909399;
}

/* 表单样式统一 */
:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
}

:deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

/* 标签样式统一 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
  border: none;
}

:deep(.el-tag--success) {
  background-color: #f0f9ff;
  color: #67c23a;
}

:deep(.el-tag--danger) {
  background-color: #fef0f0;
  color: #f56c6c;
}

:deep(.el-tag--warning) {
  background-color: #fdf6ec;
  color: #e6a23c;
}

:deep(.el-tag--info) {
  background-color: #f4f4f5;
  color: #909399;
}

/* 开关样式 */
:deep(.el-switch) {
  --el-switch-on-color: #67c23a;
  --el-switch-off-color: #dcdfe6;
}

/* 卡片样式统一 - 优化内边距 */
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeef5;
}

:deep(.el-card__header) {
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
  padding: 16px 18px;
}

:deep(.el-card__body) {
  padding: 16px 18px;
}

/* 对话框样式 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #ebeef5;
}

/* 缓存管理样式 */
.cache-info-section {
  padding: 0;
}

.cache-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cache-icon {
  color: #409eff;
  font-size: 16px;
}

.cache-description {
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
}

.cache-size {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #909399;
}

.cache-actions {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
  background-color: #fafafa;
  margin: 0 -18px -16px -18px;
}

/* 响应式设计 - 优化移动端布局 */
@media (max-width: 768px) {
  .system-settings-container {
    padding: 12px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }

  .standard-container {
    padding: 12px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  :deep(.el-card__header) {
    padding: 12px 14px;
  }

  .cache-actions {
    flex-direction: column;
    gap: 8px;
  }

  .cache-actions .el-space {
    width: 100%;
  }

  .cache-actions .el-button {
    width: 100%;
  }

  :deep(.el-card__body) {
    padding: 12px 14px;
  }

  :deep(.el-table th),
  :deep(.el-table td) {
    padding: 6px 8px;
    height: 40px;
  }
}

/* 数据链路验证样式 */
.connection-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.connection-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.connection-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.connection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.connection-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.connection-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item .label {
  color: #909399;
  font-weight: 500;
}

.detail-item .value {
  color: #303133;
  font-weight: 600;
}

.connection-actions {
  display: flex;
  justify-content: center;
}

.test-suite {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.test-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.test-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.test-controls {
  display: flex;
  gap: 8px;
}

.concurrent-controls,
.stability-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.test-results {
  background-color: #fff;
  border-radius: 6px;
  padding: 16px;
}

.metric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.metric-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  text-align: center;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  text-align: center;
}

.test-progress {
  margin-top: 12px;
}

.progress-text {
  display: block;
  text-align: center;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.test-report {
  background-color: #fff;
}

.report-summary {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.report-summary h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e4e7ed;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item .label {
  color: #909399;
  font-weight: 500;
}

.summary-item .value {
  color: #303133;
  font-weight: 600;
}

.report-details h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.report-actions {
  display: flex;
  gap: 8px;
}

/* 数据压缩测试样式 */
.compression-config {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.config-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
}

.config-row:last-child {
  margin-bottom: 0;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

.compression-controls {
  display: flex;
  gap: 8px;
}

.compression-charts {
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.chart-container h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.chart-placeholder {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  height: 120px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  min-width: 60px;
  background: linear-gradient(to top, #409eff, #79bbff);
  border-radius: 4px 4px 0 0;
  position: relative;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  background: linear-gradient(to top, #337ecc, #66b1ff);
}

.chart-label {
  position: absolute;
  bottom: -20px;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.chart-value {
  position: absolute;
  top: -20px;
  font-size: 12px;
  color: #303133;
  font-weight: 600;
}

/* 响应式设计 - 数据链路验证 */
@media (max-width: 768px) {
  .connection-status-grid {
    grid-template-columns: 1fr;
  }

  .metric-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .test-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .test-controls,
  .concurrent-controls,
  .stability-controls,
  .compression-controls,
  .report-actions {
    flex-direction: column;
    width: 100%;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .config-row {
    flex-direction: column;
    gap: 8px;
  }

  .config-item {
    justify-content: space-between;
    width: 100%;
  }

  .chart-placeholder {
    height: 80px;
  }

  .chart-bar {
    min-width: 40px;
  }
}


</style>
