<template>
  <div class="device-center">
    <!-- 统计概览面板 -->
    <div class="statistics-panel">
      <el-row :gutter="20">
        <!-- 分布式节点统计 - 仅全域管理员和超级管理员可见 -->
        <el-col :span="6" v-if="userStore.canViewSlaveServerStats">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon server-icon">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">分布式节点</div>
                <div class="stat-value">{{ statistics.slaveServers.total }}</div>
                <div class="stat-detail">
                  在线: {{ statistics.slaveServers.online }} |
                  离线: {{ statistics.slaveServers.offline }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- USB设备统计 - 仅全域管理员和超级管理员可见 -->
        <el-col :span="6" v-if="userStore.canViewUSBDeviceStats">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon device-icon">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">USB设备管理中心</div>
                <div class="stat-value">{{ statistics.devices.total }}</div>
                <div class="stat-detail">
                  可用: {{ statistics.devices.available }} |
                  已连接: {{ statistics.devices.connected }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 资源调度分组统计 - 所有权限等级用户可见（但数据范围受权限限制） -->
        <el-col :span="6" v-if="userStore.canViewDeviceGroupStats">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon group-icon">
                <el-icon><Collection /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">资源调度分组</div>
                <div class="stat-value">{{ statistics.groups.total }}</div>
                <div class="stat-detail">
                  有设备: {{ statistics.groups.withDevices }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 授权范围管理统计 - 普通用户不可见，其他权限等级可见 -->
        <el-col :span="6" v-if="userStore.canViewPermissionAssignmentStats">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon permission-icon">
                <el-icon><Key /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">授权范围管理</div>
                <div class="stat-value">{{ statistics.permissions.totalAssignments }}</div>
                <div class="stat-detail">
                  活跃用户: {{ statistics.permissions.activeUsers }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h2>USB设备管理中心</h2>
      </div>
      <div class="toolbar-right">
        <el-button
          type="primary"
          @click="refreshAllData"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 功能标签页 -->
    <el-card class="main-content">
      <el-tabs
        v-model="activeTab"
        type="border-card"
        v-if="visibleTabs.length > 0"
      >
        <el-tab-pane
          v-for="tab in visibleTabs"
          :key="tab.name"
          :name="tab.name"
          :label="tab.label"
        >
          <template #label>
            <span class="tab-label">
              <el-icon><component :is="tab.icon" /></el-icon>
              {{ tab.label }}
            </span>
          </template>

          <!-- 设备管理 -->
          <DeviceManagement v-if="tab.name === 'devices'" />

          <!-- 从服务器管理 -->
          <SlaveServerManagement v-if="tab.name === 'slaves'" />

          <!-- 设备分组管理 -->
          <DeviceGroupManagement v-if="tab.name === 'groups'" />

          <!-- 权限范围 -->
          <PermissionAssignment v-if="tab.name === 'permissions'" />
        </el-tab-pane>
      </el-tabs>

      <!-- 无权限提示 -->
      <div v-else class="no-permission">
        <el-empty description="您暂无权限访问设备管理功能">
          <el-button type="primary" @click="$router.push('/dashboard')">
            返回工作台
          </el-button>
        </el-empty>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
  Monitor,
  Collection,
  Key,
  Refresh
} from '@element-plus/icons-vue'
import { useDeviceCenter } from '@/composables/useDeviceCenter'
import { useUserStore } from '@/stores/user'

// 导入子组件
import DeviceManagement from './components/DeviceManagement.vue'
import SlaveServerManagement from './components/SlaveServerManagement.vue'
import DeviceGroupManagement from './components/DeviceGroupManagement.vue'
import PermissionAssignment from './components/PermissionAssignment.vue'

// 使用设备中心数据协调层
const {
  loading,
  statistics,
  visibleTabs,
  refreshAllData
} = useDeviceCenter()

// 使用用户存储
const userStore = useUserStore()

// 当前激活的标签页
const activeTab = ref('')

// 生命周期
onMounted(() => {
  console.log('DeviceCenter 组件已挂载')
  // 设置默认激活的标签页
  if (visibleTabs.value.length > 0) {
    activeTab.value = visibleTabs.value[0].name
  }
})
</script>

<style scoped>
.device-center {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

/* 统计概览面板 */
.statistics-panel {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.server-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.device-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.group-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.permission-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-detail {
  font-size: 12px;
  color: #909399;
}

/* 操作栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 10px;
}

.toolbar-left h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}

/* 主内容区域 */
.main-content {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 标签页样式 */
.tab-label {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 无权限提示 */
.no-permission {
  text-align: center;
  padding: 60px 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stat-value {
    font-size: 24px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .device-center {
    padding: 10px;
  }

  .toolbar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .toolbar-left h2 {
    text-align: center;
    font-size: 20px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>
