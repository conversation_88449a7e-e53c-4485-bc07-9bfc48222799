<template>
  <div class="device-detail">
    <!-- 页面头部 -->
    <div class="detail-header">
      <div class="header-left">
        <el-button @click="goBack" type="text" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回设备列表
        </el-button>
        <div class="device-title">
          <h2>{{ deviceDetail.custom_name || deviceDetail.device_name || '设备详情' }}</h2>
          <el-tag :type="getStatusColor(deviceDetail.status)" size="large">
            <el-icon class="status-icon">
              <Monitor v-if="deviceDetail.status === 'idle'" />
              <Lock v-else-if="deviceDetail.status === 'occupied'" />
              <Warning v-else-if="deviceDetail.status === 'damaged'" />
              <OfflineOutlined v-else />
            </el-icon>
            {{ getStatusText(deviceDetail.status) }}
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="refreshData" :loading="loading" type="primary">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="editMode = !editMode" :type="editMode ? 'success' : 'warning'">
          <el-icon><Edit /></el-icon>
          {{ editMode ? '保存修改' : '修改信息' }}
        </el-button>
        <el-dropdown @command="handleCommand">
          <el-button type="danger">
            设备操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="unbind">解除绑定</el-dropdown-item>
              <el-dropdown-item command="reset">重置设备</el-dropdown-item>
              <el-dropdown-item divided command="delete">删除设备</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="detail-content" v-loading="loading">
      <!-- 基础信息 -->
      <el-row :gutter="20" class="content-sections">
        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><InfoFilled /></el-icon>
                <span>基础信息</span>
              </div>
            </template>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">设备ID：</span>
                <span class="info-value">{{ deviceDetail.device_id }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">设备名称：</span>
                <el-input
                  v-if="editMode"
                  v-model="editForm.custom_name"
                  placeholder="自定义设备名称"
                  class="edit-input"
                />
                <span v-else class="info-value">{{ deviceDetail.custom_name || deviceDetail.device_name }}</span>
              </div>
              <div class="info-item" v-if="deviceDetail.custom_name">
                <span class="info-label">自动生成名称：</span>
                <span class="info-value secondary">{{ deviceDetail.device_name }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">设备类型：</span>
                <el-select
                  v-if="editMode"
                  v-model="editForm.device_type"
                  placeholder="选择设备类型"
                  class="edit-input"
                >
                  <el-option label="加密锁" value="encryption_key" />
                  <el-option label="存储设备" value="storage" />
                  <el-option label="输入设备" value="input" />
                  <el-option label="通信设备" value="communication" />
                  <el-option label="硬件设备" value="hardware" />
                  <el-option label="未知设备" value="unknown" />
                </el-select>
                <el-tag v-else :type="getTypeColor(deviceDetail.device_type)" size="small">
                  {{ getTypeText(deviceDetail.device_type) }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="info-label">设备状态：</span>
                <el-tag :type="getStatusColor(deviceDetail.status)" size="small">
                  {{ getStatusText(deviceDetail.status) }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="info-label">备注信息：</span>
                <el-input
                  v-if="editMode"
                  v-model="editForm.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="设备备注信息（最长100字符）"
                  maxlength="100"
                  show-word-limit
                  class="edit-input"
                />
                <span v-else class="info-value">{{ deviceDetail.remark || '无备注' }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><Connection /></el-icon>
                <span>硬件信息</span>
              </div>
            </template>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">厂商ID：</span>
                <span class="info-value code">{{ deviceDetail.vendor_id || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">产品ID：</span>
                <span class="info-value code">{{ deviceDetail.product_id || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">硬件签名：</span>
                <span class="info-value code">{{ deviceDetail.hardware_signature || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">设备描述：</span>
                <span class="info-value">{{ deviceDetail.description || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">物理端口：</span>
                <span class="info-value code">{{ deviceDetail.physical_port || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">端口位置码：</span>
                <span class="info-value code">{{ deviceDetail.port_location_code || 'N/A' }}</span>
              </div>

              <!-- USB.IDS增强信息 -->
              <div v-if="deviceDetail.usb_ids_vendor_name || deviceDetail.usb_ids_device_name" class="usb-ids-section">
                <div class="info-divider">
                  <span class="divider-text">USB.IDS 识别信息</span>
                </div>
                <div class="info-item" v-if="deviceDetail.usb_ids_vendor_name">
                  <span class="info-label">厂商名称：</span>
                  <span class="info-value highlight">{{ deviceDetail.usb_ids_vendor_name }}</span>
                </div>
                <div class="info-item" v-if="deviceDetail.usb_ids_device_name">
                  <span class="info-label">产品名称：</span>
                  <span class="info-value highlight">{{ deviceDetail.usb_ids_device_name }}</span>
                </div>
                <div class="info-item" v-if="deviceDetail.usb_ids_full_name">
                  <span class="info-label">完整名称：</span>
                  <span class="info-value">{{ deviceDetail.usb_ids_full_name }}</span>
                </div>
                <div class="info-item" v-if="deviceDetail.identification_source">
                  <span class="info-label">识别来源：</span>
                  <el-tag
                    :type="getIdentificationSourceColor(deviceDetail.identification_source)"
                    size="small"
                  >
                    {{ getIdentificationSourceText(deviceDetail.identification_source) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 服务器信息 -->
      <el-row :gutter="20" class="content-sections">
        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><Monitor /></el-icon>
                <span>所属服务器</span>
              </div>
            </template>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">服务器名称：</span>
                <span class="info-value">{{ deviceDetail.server_name || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">服务器IP：</span>
                <span class="info-value code">{{ deviceDetail.server_ip || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">服务器端口：</span>
                <span class="info-value code">{{ deviceDetail.server_port || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">服务器状态：</span>
                <el-tag :type="deviceDetail.server_status === 'online' ? 'success' : 'danger'" size="small">
                  {{ deviceDetail.server_status === 'online' ? '在线' : '离线' }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><Clock /></el-icon>
                <span>使用记录</span>
              </div>
            </template>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">创建时间：</span>
                <span class="info-value">{{ formatTime(deviceDetail.created_at) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">更新时间：</span>
                <span class="info-value">{{ formatTime(deviceDetail.updated_at) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">最后连接：</span>
                <span class="info-value">{{ formatTime(deviceDetail.last_connected) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">连接用户：</span>
                <span class="info-value">{{ deviceDetail.last_connected_user || 'N/A' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">连接次数：</span>
                <span class="info-value">{{ deviceDetail.connection_count || 0 }} 次</span>
              </div>
              <div class="info-item">
                <span class="info-label">总使用时长：</span>
                <span class="info-value">{{ formatDuration(deviceDetail.total_usage_time) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 占用信息（如果设备被占用） -->
      <el-row :gutter="20" class="content-sections" v-if="deviceDetail.status === 'occupied'">
        <el-col :span="24">
          <el-card class="info-card occupied-info">
            <template #header>
              <div class="card-header">
                <el-icon><Lock /></el-icon>
                <span>当前占用信息</span>
              </div>
            </template>
            <div class="occupied-content">
              <div class="occupied-user">
                <div class="user-avatar">
                  <el-icon><User /></el-icon>
                </div>
                <div class="user-info">
                  <div class="user-name">{{ deviceDetail.current_user_name || 'N/A' }}</div>
                  <div class="user-contact">{{ deviceDetail.current_user_contact || 'N/A' }}</div>
                </div>
              </div>
              <div class="occupied-details">
                <div class="detail-item">
                  <span class="detail-label">占用开始：</span>
                  <span class="detail-value">{{ formatTime(deviceDetail.occupied_start_time) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">持续时间：</span>
                  <span class="detail-value">{{ formatDuration(deviceDetail.occupied_duration) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">预计结束：</span>
                  <span class="detail-value">{{ formatTime(deviceDetail.estimated_end_time) || '未设置' }}</span>
                </div>
              </div>
              <div class="occupied-actions">
                <el-button type="primary" @click="contactUser">
                  <el-icon><Message /></el-icon>
                  联系用户
                </el-button>
                <el-button type="warning" @click="requestRelease">
                  <el-icon><Bell /></el-icon>
                  请求释放
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 分组信息 -->
      <el-row :gutter="20" class="content-sections">
        <el-col :span="24">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><Collection /></el-icon>
                <span>所属分组</span>
                <div class="header-actions">
                  <el-button size="small" @click="manageGroups">
                    <el-icon><Setting /></el-icon>
                    管理分组
                  </el-button>
                </div>
              </div>
            </template>
            <div class="groups-content">
              <div v-if="deviceGroups.length === 0" class="empty-groups">
                <el-empty description="该设备未加入任何分组" />
              </div>
              <div v-else class="groups-list">
                <el-tag
                  v-for="group in deviceGroups"
                  :key="group.id"
                  size="large"
                  class="group-tag"
                  @click="viewGroupDetail(group)"
                >
                  <el-icon><Collection /></el-icon>
                  {{ group.name }}
                  <span class="group-device-count">({{ group.device_count }}个设备)</span>
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import {
  ArrowLeft,
  Refresh,
  Edit,
  ArrowDown,
  Monitor,
  Lock,
  Warning,
  InfoFilled,
  Connection,
  Clock,
  User,
  Message,
  Bell,
  Collection,
  Setting
} from '@element-plus/icons-vue'

// 路由和状态
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const editMode = ref(false)
const deviceId = route.params.id

// 设备详情数据
const deviceDetail = reactive({
  id: null,
  device_id: '',
  device_name: '',
  custom_name: '',
  device_type: 'unknown',
  status: 'idle',
  vendor_id: '',
  product_id: '',
  hardware_signature: '',
  description: '',
  physical_port: '',
  port_location_code: '',
  server_name: '',
  server_ip: '',
  server_port: '',
  server_status: 'online',
  remark: '',
  created_at: null,
  updated_at: null,
  last_connected: null,
  last_connected_user: '',
  connection_count: 0,
  total_usage_time: 0,
  current_user_name: '',
  current_user_contact: '',
  occupied_start_time: null,
  occupied_duration: 0,
  estimated_end_time: null
})

// 编辑表单
const editForm = reactive({
  custom_name: '',
  device_type: 'unknown',
  remark: ''
})

// 设备分组
const deviceGroups = ref([])

// 自动刷新定时器
let refreshTimer = null

// 方法
const goBack = () => {
  // 智能返回：基于来源页面决定返回路径

  // 方法1: 检查路由参数中是否有来源信息
  const fromGroup = route.query.from_group
  if (fromGroup) {
    // 从分组详情页面进入，返回到分组详情页面
    router.push(`/device-center/group/${fromGroup}`)
    return
  }

  // 方法2: 检查referrer URL
  const referrer = document.referrer
  if (referrer && referrer.includes('/device-center/group/')) {
    // 从分组详情页面进入，返回到分组详情页面
    const groupId = extractGroupIdFromReferrer(referrer)
    if (groupId) {
      router.push(`/device-center/group/${groupId}`)
      return
    }
  }

  // 方法3: 使用浏览器历史记录返回
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    // 默认返回到设备中心首页
    router.push('/device-center')
  }
}

// 从referrer URL中提取分组ID
const extractGroupIdFromReferrer = (referrer) => {
  try {
    const url = new URL(referrer)
    const pathMatch = url.pathname.match(/\/device-center\/group\/(\d+)/)
    return pathMatch ? pathMatch[1] : null
  } catch (error) {
    console.error('解析referrer URL失败:', error)
    return null
  }
}

const getStatusColor = (status) => {
  const colorMap = {
    'idle': 'success',
    'occupied': 'warning',
    'damaged': 'danger',
    'offline': 'info'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'idle': '空闲',
    'occupied': '被占用',
    'damaged': '硬件损坏',
    'offline': '离线'
  }
  return textMap[status] || '未知'
}

const getTypeColor = (type) => {
  const colorMap = {
    'encryption_key': 'danger',
    'storage': 'warning',
    'input': 'info',
    'communication': 'success',
    'hardware': 'primary',
    'unknown': ''
  }
  return colorMap[type] || ''
}

const getTypeText = (type) => {
  const textMap = {
    'encryption_key': '加密锁',
    'storage': '存储设备',
    'input': '输入设备',
    'communication': '通信设备',
    'hardware': '硬件设备',
    'unknown': '未知设备'
  }
  return textMap[type] || '待补充'
}

// USB.IDS识别来源处理函数
const getIdentificationSourceColor = (source) => {
  const colorMap = {
    'usb_ids_enhanced': 'success',
    'usb_ids': 'primary',
    'local_rules': 'warning'
  }
  return colorMap[source] || 'info'
}

const getIdentificationSourceText = (source) => {
  const textMap = {
    'usb_ids_enhanced': 'USB.IDS增强识别',
    'usb_ids': 'USB.IDS标准识别',
    'local_rules': '本地规则识别'
  }
  return textMap[source] || '未知来源'
}

const formatTime = (timeStr) => {
  if (!timeStr) return 'N/A'
  return new Date(timeStr).toLocaleString()
}

const formatDuration = (seconds) => {
  if (!seconds) return '0分钟'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

// 数据获取
const refreshData = async () => {
  loading.value = true
  try {
    // 调用真实API获取设备详情
    const response = await fetch(`/api/v1/devices/${deviceId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    // 更新设备详情
    Object.assign(deviceDetail, data)

    // 更新编辑表单
    editForm.custom_name = deviceDetail.custom_name || ''
    editForm.device_type = deviceDetail.device_type || 'unknown'
    editForm.remark = deviceDetail.remark || ''

    // 获取设备分组信息（如果有相关API）
    // TODO: 实现设备分组查询API
    deviceGroups.value = []

  } catch (error) {
    console.error('获取设备详情失败:', error)
    ElMessage.error(`获取设备详情失败: ${error.message}`)

    // 如果设备不存在，返回设备列表
    if (error.message.includes('404')) {
      router.push('/device-center')
    }
  } finally {
    loading.value = false
  }
}

const handleCommand = async (command) => {
  try {
    await ElMessageBox.confirm(
      `确定要执行 "${getCommandText(command)}" 操作吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success(`${getCommandText(command)} 操作已执行`)
    
    if (command === 'delete') {
      router.push('/device-center')
    } else {
      refreshData()
    }
  } catch {
    // 用户取消操作
  }
}

const getCommandText = (command) => {
  const commandMap = {
    'unbind': '解除绑定',
    'reset': '重置设备',
    'delete': '删除设备'
  }
  return commandMap[command] || command
}

const contactUser = () => {
  ElMessage.info(`联系用户: ${deviceDetail.current_user_name} (${deviceDetail.current_user_contact})`)
}

const requestRelease = () => {
  ElMessage.info('已发送设备释放请求')
}

const manageGroups = () => {
  ElMessage.info('管理设备分组')
}

const viewGroupDetail = (group) => {
  router.push(`/device-center/device-group/${group.id}`)
}

// 生命周期
onMounted(() => {
  refreshData()
  // 设置自动刷新（每30秒）
  refreshTimer = setInterval(refreshData, 30000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.device-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  font-size: 14px;
  color: #606266;
}

.device-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.device-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 10px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-sections {
  margin-bottom: 20px;
}

.info-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  margin-left: auto;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
}

.info-value {
  color: #303133;
  flex: 1;
}

.info-value.code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.info-value.secondary {
  color: #909399;
  font-size: 14px;
}

.edit-input {
  flex: 1;
}

.occupied-info {
  border-left: 4px solid #e6a23c;
}

.occupied-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 10px 0;
}

.occupied-user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #e6a23c;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.user-contact {
  color: #606266;
  font-size: 14px;
}

.occupied-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.detail-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.detail-value {
  color: #303133;
}

.occupied-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.groups-content {
  padding: 10px 0;
}

.empty-groups {
  text-align: center;
  padding: 20px 0;
}

.groups-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.group-tag {
  cursor: pointer;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s;
}

.group-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.group-device-count {
  color: #909399;
  font-size: 12px;
}

.status-icon {
  margin-right: 5px;
}

/* USB.IDS信息样式 */
.usb-ids-section {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 2px solid #e4e7ed;
}

.info-divider {
  margin-bottom: 15px;
  text-align: center;
  position: relative;
}

.divider-text {
  background: white;
  padding: 0 15px;
  color: #409eff;
  font-weight: 600;
  font-size: 14px;
}

.info-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #409eff;
  z-index: 1;
}

.info-value.highlight {
  color: #409eff;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .header-left {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .device-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .occupied-content {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .occupied-actions {
    flex-direction: row;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .info-label {
    min-width: auto;
  }
}
</style>
