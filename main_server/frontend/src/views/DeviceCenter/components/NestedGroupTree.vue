<template>
  <div class="nested-group-tree">
    <div class="tree-header">
      <h3>嵌套分组管理</h3>
      <div class="header-actions">
        <el-button type="primary" @click="refreshTree">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="success" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建分组
        </el-button>
      </div>
    </div>

    <div class="tree-content">
      <el-tree
        ref="treeRef"
        :data="treeData"
        :props="treeProps"
        :expand-on-click-node="false"
        :default-expand-all="false"
        node-key="id"
        class="group-tree"
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <div class="node-content">
              <div class="node-info">
                <span class="node-name">{{ data.name }}</span>
                <div class="node-tags">
                  <el-tag
                    :type="getGroupTypeColor(data.group_type)"
                    size="small"
                  >
                    {{ getGroupTypeText(data.group_type) }}
                  </el-tag>
                  <el-tag
                    type="info"
                    size="small"
                  >
                    L{{ data.nesting_level }}
                  </el-tag>
                </div>
              </div>

              <div class="node-stats">
                <span class="stat-item">
                  <el-icon><Monitor /></el-icon>
                  {{ data.device_count }}设备
                </span>
                <span v-if="data.child_count > 0" class="stat-item">
                  <el-icon><FolderOpened /></el-icon>
                  {{ data.child_count }}子组
                </span>
              </div>
            </div>

            <div class="node-actions">
              <!-- 权限标识 -->
              <div class="permission-indicators">
                <el-tag
                  v-if="data.is_readonly"
                  type="info"
                  size="small"
                >
                  只读
                </el-tag>

                <el-tag
                  v-if="!data.can_view_devices"
                  type="warning"
                  size="small"
                >
                  无设备权限
                </el-tag>
              </div>

              <!-- 操作按钮组 -->
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="viewGroupDetail(data)"
                >
                  详情
                </el-button>

                <el-button
                  v-if="data.can_view_devices"
                  type="info"
                  size="small"
                  @click="viewDeviceDetails(data)"
                >
                  查看设备
                </el-button>

                <template v-if="data.can_manage">
                  <el-button
                    type="success"
                    size="small"
                    @click="createChildGroup(data)"
                    :disabled="data.nesting_level >= 3"
                  >
                    <el-tooltip
                      v-if="data.nesting_level >= 3"
                      content="已达到最大嵌套深度（4层）"
                      placement="top"
                    >
                      <span>添加子组</span>
                    </el-tooltip>
                    <span v-else>添加子组</span>
                  </el-button>

                  <el-button
                    type="warning"
                    size="small"
                    @click="editGroup(data)"
                  >
                    编辑
                  </el-button>

                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteGroup(data)"
                  >
                    删除
                  </el-button>
                </template>
              </div>
            </div>
          </div>
        </template>
      </el-tree>
    </div>

    <!-- 创建分组对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="createForm.parent_group_id ? '创建子分组' : '创建顶级分组'"
      width="500px"
      @close="resetCreateForm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
      >
        <el-form-item label="父分组" v-if="createForm.parent_group_id">
          <el-input 
            :value="parentGroupName" 
            disabled 
            placeholder="顶级分组"
          />
        </el-form-item>
        
        <el-form-item label="分组名称" prop="name">
          <el-input 
            v-model="createForm.name" 
            placeholder="请输入分组名称"
          />
        </el-form-item>
        
        <el-form-item label="分组类型" prop="group_type">
          <el-select 
            v-model="createForm.group_type" 
            placeholder="请选择分组类型"
            style="width: 100%;"
          >
            <el-option label="服务器分组" value="server" />
            <el-option label="混合分组" value="mixed" />
            <el-option label="单设备分组" value="single" />
            <el-option label="嵌套分组" value="nested" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分组描述" prop="description">
          <el-input 
            v-model="createForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入分组描述"
          />
        </el-form-item>
        
        <el-form-item label="嵌套层级" v-if="createForm.parent_group_id">
          <el-input 
            :value="`第 ${nestingLevel} 层`" 
            disabled 
          />
          <div class="level-warning" v-if="nestingLevel >= 3">
            <el-alert
              title="已达到最大嵌套深度（4层）"
              type="warning"
              :closable="false"
              show-icon
            />
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="createGroup"
            :loading="createLoading"
            :disabled="nestingLevel >= 4"
          >
            创建
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设备详情对话框 -->
    <el-dialog
      v-model="showDeviceDialog"
      title="分组设备详情"
      width="80%"
      @close="resetDeviceDialog"
    >
      <div class="device-details">
        <div class="device-header">
          <h4>{{ selectedGroup?.name }} - 设备列表</h4>
          <div class="device-actions">
            <el-button
              type="success"
              @click="showReorganizeDialog = true"
              v-if="selectedGroup?.can_manage"
            >
              <el-icon><Operation /></el-icon>
              重新分组
            </el-button>
          </div>
        </div>

        <el-table
          :data="groupDevices"
          v-loading="deviceLoading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="name" label="设备名称" min-width="150">
            <template #default="{ row }">
              <div class="device-name">
                <span class="primary-name">{{ row.custom_name || row.name }}</span>
                <span v-if="row.custom_name && row.name !== row.custom_name" class="secondary-name">
                  ({{ row.name }})
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="device_type" label="设备类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getDeviceTypeColor(row.device_type)" size="small">
                {{ getDeviceTypeText(row.device_type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="slave_server_name" label="所属服务器" width="150" />

          <el-table-column prop="vid" label="VID" width="80" />
          <el-table-column prop="pid" label="PID" width="80" />

          <el-table-column prop="description" label="描述" min-width="200" />
        </el-table>
      </div>
    </el-dialog>

    <!-- 设备重新分组对话框 -->
    <el-dialog
      v-model="showReorganizeDialog"
      title="设备重新分组"
      width="60%"
      @close="resetReorganizeDialog"
    >
      <div class="reorganize-content">
        <el-form
          ref="reorganizeFormRef"
          :model="reorganizeForm"
          :rules="reorganizeRules"
          label-width="120px"
        >
          <el-form-item label="新分组名称" prop="group_name">
            <el-input
              v-model="reorganizeForm.group_name"
              placeholder="请输入新分组名称"
            />
          </el-form-item>

          <el-form-item label="分组描述" prop="group_description">
            <el-input
              v-model="reorganizeForm.group_description"
              type="textarea"
              :rows="3"
              placeholder="请输入分组描述"
            />
          </el-form-item>

          <el-form-item label="选择设备">
            <el-table
              ref="deviceSelectionTable"
              :data="groupDevices"
              @selection-change="handleDeviceSelection"
              max-height="300"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="name" label="设备名称" min-width="150" />
              <el-table-column prop="device_type" label="类型" width="100" />
              <el-table-column prop="status" label="状态" width="80" />
            </el-table>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showReorganizeDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="executeReorganize"
            :loading="reorganizeLoading"
            :disabled="selectedDevices.length === 0"
          >
            创建新分组
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑分组对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑分组"
      width="500px"
      @close="resetEditForm"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        label-width="100px"
      >
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="分组类型" prop="group_type">
          <el-select v-model="editForm.group_type" placeholder="请选择分组类型" style="width: 100%">
            <el-option label="标准分组" value="standard" />
            <el-option label="临时分组" value="temporary" />
            <el-option label="专用分组" value="dedicated" />
            <el-option label="重组分组" value="reorganized" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述信息" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分组描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitEditForm"
            :loading="editLoading"
          >
            确认修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Plus, Monitor, FolderOpened } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

// Props
const props = defineProps({
  refreshTrigger: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['group-created', 'group-updated', 'group-deleted'])

// 路由
const router = useRouter()

// 响应式数据
const treeRef = ref()
const createFormRef = ref()
const reorganizeFormRef = ref()
const deviceSelectionTable = ref()
const treeData = ref([])
const showCreateDialog = ref(false)
const showDeviceDialog = ref(false)
const showReorganizeDialog = ref(false)
const showEditDialog = ref(false)
const createLoading = ref(false)
const deviceLoading = ref(false)
const reorganizeLoading = ref(false)
const editLoading = ref(false)
const selectedGroup = ref(null)
const groupDevices = ref([])
const selectedDevices = ref([])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 创建表单
const createForm = reactive({
  name: '',
  group_type: '',
  description: '',
  parent_group_id: null
})

// 重新分组表单
const reorganizeForm = reactive({
  group_name: '',
  group_description: '',
  device_ids: [],
  source_group_id: null
})

// 表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 1, max: 200, message: '分组名称长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  group_type: [
    { required: true, message: '请选择分组类型', trigger: 'change' }
  ]
}

const reorganizeRules = {
  group_name: [
    { required: true, message: '请输入新分组名称', trigger: 'blur' },
    { min: 1, max: 200, message: '分组名称长度在 1 到 200 个字符', trigger: 'blur' }
  ]
}

// 编辑表单
const editFormRef = ref()
const editForm = reactive({
  id: null,
  name: '',
  group_type: '',
  description: ''
})

const editFormRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' },
    { min: 1, max: 200, message: '分组名称长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  group_type: [
    { required: true, message: '请选择分组类型', trigger: 'change' }
  ]
}

// 计算属性
const parentGroupName = computed(() => {
  if (!createForm.parent_group_id) return ''
  
  const findGroupName = (groups, id) => {
    for (const group of groups) {
      if (group.id === id) return group.name
      if (group.children) {
        const found = findGroupName(group.children, id)
        if (found) return found
      }
    }
    return ''
  }
  
  return findGroupName(treeData.value, createForm.parent_group_id)
})

const nestingLevel = computed(() => {
  if (!createForm.parent_group_id) return 0
  
  const findGroupLevel = (groups, id) => {
    for (const group of groups) {
      if (group.id === id) return group.nesting_level + 1
      if (group.children) {
        const found = findGroupLevel(group.children, id)
        if (found !== null) return found
      }
    }
    return null
  }
  
  return findGroupLevel(treeData.value, createForm.parent_group_id) || 0
})

// 方法
const refreshTree = async () => {
  try {
    const response = await fetch('/api/v1/device-groups/tree', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      treeData.value = await response.json()
      console.log('嵌套分组树加载成功:', treeData.value)
    } else {
      throw new Error(`HTTP ${response.status}`)
    }
  } catch (error) {
    console.error('加载嵌套分组树失败:', error)
    ElMessage.error(`加载嵌套分组树失败: ${error.message}`)
  }
}

const createChildGroup = (parentGroup) => {
  if (parentGroup.nesting_level >= 3) {
    ElMessage.warning('已达到最大嵌套深度（4层），无法创建子分组')
    return
  }
  
  createForm.parent_group_id = parentGroup.id
  createForm.name = ''
  createForm.group_type = 'nested'
  createForm.description = ''
  showCreateDialog.value = true
}

const createGroup = async () => {
  try {
    await createFormRef.value.validate()
    createLoading.value = true

    const response = await fetch('/api/v1/device-groups/', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(createForm)
    })

    if (response.ok) {
      const result = await response.json()
      ElMessage.success('分组创建成功')
      showCreateDialog.value = false
      resetCreateForm()
      refreshTree()
      emit('group-created', result)
    } else {
      const errorData = await response.json()
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }
  } catch (error) {
    console.error('创建分组失败:', error)
    ElMessage.error(`创建分组失败: ${error.message}`)
  } finally {
    createLoading.value = false
  }
}

const resetCreateForm = () => {
  createForm.name = ''
  createForm.group_type = ''
  createForm.description = ''
  createForm.parent_group_id = null
  
  nextTick(() => {
    createFormRef.value?.clearValidate()
  })
}

const viewGroupDetail = (group) => {
  router.push({
    name: 'DeviceCenterGroupDetail',
    params: { id: group.id }
  })
}

const editGroup = (group) => {
  // 填充编辑表单
  editForm.id = group.id
  editForm.name = group.name
  editForm.group_type = group.group_type
  editForm.description = group.description || ''

  showEditDialog.value = true
}

const deleteGroup = async (group) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分组 "${group.name}" 吗？此操作将同时删除所有子分组。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 执行删除操作
    ElMessage.info('删除功能开发中...')
  } catch {
    // 用户取消删除
  }
}

// 查看设备详情
const viewDeviceDetails = async (group) => {
  try {
    selectedGroup.value = group
    deviceLoading.value = true
    showDeviceDialog.value = true

    const response = await fetch(`/api/v1/device-groups/${group.id}/devices`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      groupDevices.value = await response.json()
    } else {
      throw new Error(`HTTP ${response.status}`)
    }
  } catch (error) {
    console.error('加载分组设备失败:', error)
    ElMessage.error(`加载分组设备失败: ${error.message}`)
  } finally {
    deviceLoading.value = false
  }
}

// 重置设备对话框
const resetDeviceDialog = () => {
  selectedGroup.value = null
  groupDevices.value = []
  showDeviceDialog.value = false
}

// 设备选择处理
const handleDeviceSelection = (selection) => {
  selectedDevices.value = selection
  reorganizeForm.device_ids = selection.map(device => device.id)
}

// 重置重新分组对话框
const resetReorganizeDialog = () => {
  reorganizeForm.group_name = ''
  reorganizeForm.group_description = ''
  reorganizeForm.device_ids = []
  reorganizeForm.source_group_id = null
  selectedDevices.value = []
  showReorganizeDialog.value = false
}

// 执行重新分组
const executeReorganize = async () => {
  try {
    await reorganizeFormRef.value.validate()
    reorganizeLoading.value = true

    const requestData = {
      group_name: reorganizeForm.group_name,
      group_description: reorganizeForm.group_description,
      device_ids: reorganizeForm.device_ids,
      source_group_id: selectedGroup.value.id
    }

    const response = await fetch('/api/v1/device-groups/reorganize', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })

    if (response.ok) {
      const result = await response.json()
      ElMessage.success(`成功创建新分组: ${result.new_group_name}`)
      showReorganizeDialog.value = false
      resetReorganizeDialog()
      refreshTree()
      emit('group-created', result)
    } else {
      const errorData = await response.json()
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }
  } catch (error) {
    console.error('设备重新分组失败:', error)
    ElMessage.error(`设备重新分组失败: ${error.message}`)
  } finally {
    reorganizeLoading.value = false
  }
}

// 编辑分组相关方法
const submitEditForm = async () => {
  try {
    await editFormRef.value.validate()
    editLoading.value = true

    const response = await fetch(`/api/v1/device-groups/${editForm.id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: editForm.name,
        group_type: editForm.group_type,
        description: editForm.description
      })
    })

    if (response.ok) {
      ElMessage.success('分组信息更新成功')
      showEditDialog.value = false
      resetEditForm()
      refreshTree()
    } else {
      const errorData = await response.json()
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }
  } catch (error) {
    console.error('更新分组失败:', error)
    ElMessage.error(`更新分组失败: ${error.message}`)
  } finally {
    editLoading.value = false
  }
}

const resetEditForm = () => {
  editForm.id = null
  editForm.name = ''
  editForm.group_type = ''
  editForm.description = ''

  nextTick(() => {
    editFormRef.value?.clearValidate()
  })
}

// 辅助方法
const getGroupTypeText = (type) => {
  const typeMap = {
    'server': '服务器',
    'mixed': '混合',
    'single': '单设备',
    'nested': '嵌套'
  }
  return typeMap[type] || type
}

const getGroupTypeColor = (type) => {
  const colorMap = {
    'server': 'success',
    'mixed': 'primary',
    'single': 'warning',
    'nested': 'info',
    'reorganized': 'danger'
  }
  return colorMap[type] || 'info'
}

// 设备状态和类型显示
const getStatusText = (status) => {
  const statusMap = {
    'online': '在线',
    'offline': '离线',
    'busy': '占用',
    'available': '可用',
    'error': '错误'
  }
  return statusMap[status] || status
}

const getStatusColor = (status) => {
  const colorMap = {
    'online': 'success',
    'offline': 'info',
    'busy': 'warning',
    'available': 'success',
    'error': 'danger'
  }
  return colorMap[status] || 'info'
}

const getDeviceTypeText = (type) => {
  const typeMap = {
    'ca_lock': 'CA锁',
    'encryption_key': '加密锁',
    'bank_ukey': '银行U盾'
  }
  return typeMap[type] || type
}

const getDeviceTypeColor = (type) => {
  const colorMap = {
    'ca_lock': 'danger',
    'encryption_key': 'warning',
    'bank_ukey': 'primary'
  }
  return colorMap[type] || 'info'
}

// 监听刷新触发器
watch(() => props.refreshTrigger, () => {
  refreshTree()
})

// 生命周期
onMounted(() => {
  refreshTree()
})
</script>

<style scoped>
.nested-group-tree {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.tree-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.tree-content {
  flex: 1;
  overflow: auto;
}

.group-tree {
  background: #fff;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  padding: 12px 0;
  min-height: 60px;
}

.node-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 8px;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.node-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.node-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.node-stats {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #909399;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.node-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 20px;
  min-width: 200px;
}

.permission-indicators {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.action-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.level-warning {
  margin-top: 10px;
}

.dialog-footer {
  text-align: right;
}

.device-details {
  padding: 10px 0;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.device-header h4 {
  margin: 0;
  color: #303133;
}

.device-actions {
  display: flex;
  gap: 10px;
}

.device-name .secondary-name {
  color: #909399;
  font-size: 12px;
}

.reorganize-content {
  padding: 10px 0;
}
</style>
