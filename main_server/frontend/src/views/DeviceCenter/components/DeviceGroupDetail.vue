<template>
  <div class="device-group-detail">
    <!-- 页面头部 -->
    <div class="detail-header">
      <div class="header-left">
        <el-button @click="goBack" type="text" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回分组列表
        </el-button>
        <div class="group-title">
          <h2>{{ groupDetail.name || '设备分组详情' }}</h2>
          <el-tag :type="getStatusColor(groupDetail.status)" size="large">
            <el-icon class="status-icon">
              <Collection v-if="groupDetail.device_count > 0" />
              <FolderOpened v-else />
            </el-icon>
            {{ groupDetail.device_count || 0 }} 个设备
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="refreshData" :loading="loading" type="primary">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-dropdown @command="handleCommand">
          <el-button type="success">
            管理操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="edit">编辑分组</el-dropdown-item>
              <el-dropdown-item command="add-device">添加设备</el-dropdown-item>
              <el-dropdown-item command="remove-device">移除设备</el-dropdown-item>
              <el-dropdown-item divided command="delete">删除分组</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="detail-content" v-loading="loading">
      <!-- 分组信息概览 -->
      <div class="overview-cards">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon devices">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ groupDetail.device_count || 0 }}</div>
              <div class="card-label">设备总数</div>
            </div>
          </div>
        </el-card>
        
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon online">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ onlineDeviceCount }}</div>
              <div class="card-label">在线设备</div>
            </div>
          </div>
        </el-card>
        
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon servers">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ serverCount }}</div>
              <div class="card-label">涉及服务器</div>
            </div>
          </div>
        </el-card>
        
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon users">
              <el-icon><User /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ groupDetail.user_count || 0 }}</div>
              <div class="card-label">授权用户</div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 详细信息区域 -->
      <el-row :gutter="20" class="detail-sections">
        <!-- 基础信息 -->
        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><InfoFilled /></el-icon>
                <span>基础信息</span>
              </div>
            </template>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">分组名称：</span>
                <span class="info-value">{{ groupDetail.name }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">分组描述：</span>
                <span class="info-value">{{ groupDetail.description || '暂无描述' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">创建时间：</span>
                <span class="info-value">{{ formatTime(groupDetail.created_at) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">更新时间：</span>
                <span class="info-value">{{ formatTime(groupDetail.updated_at) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">分组状态：</span>
                <el-tag :type="getStatusColor(groupDetail.status)">
                  {{ getStatusText(groupDetail.status) }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 统计信息 -->
        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><DataAnalysis /></el-icon>
                <span>统计信息</span>
              </div>
            </template>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">设备总数：</span>
                <span class="info-value">{{ groupDetail.device_count || 0 }} 个</span>
              </div>
              <div class="info-item">
                <span class="info-label">在线设备：</span>
                <span class="info-value">{{ onlineDeviceCount }} 个</span>
              </div>
              <div class="info-item">
                <span class="info-label">离线设备：</span>
                <span class="info-value">{{ offlineDeviceCount }} 个</span>
              </div>
              <div class="info-item">
                <span class="info-label">涉及服务器：</span>
                <span class="info-value">{{ serverCount }} 台</span>
              </div>
              <div class="info-item">
                <span class="info-label">授权用户：</span>
                <span class="info-value">{{ groupDetail.user_count || 0 }} 人</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 设备列表 -->
      <el-row :gutter="20" class="detail-sections">
        <el-col :span="24">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><Monitor /></el-icon>
                <span>设备列表</span>
                <div class="header-actions">
                  <el-input
                    v-model="searchText"
                    placeholder="搜索设备名称..."
                    style="width: 200px; margin-right: 10px;"
                    clearable
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>
                  <el-select
                    v-model="statusFilter"
                    placeholder="筛选状态"
                    style="width: 120px;"
                    clearable
                  >
                    <el-option label="全部" value="" />
                    <el-option label="在线" value="online" />
                    <el-option label="离线" value="offline" />
                    <el-option label="占用中" value="busy" />
                  </el-select>
                </div>
              </div>
            </template>
            <div class="device-list">
              <el-table :data="filteredDevices" stripe>
                <el-table-column prop="device_id" label="设备ID" width="120" />
                <el-table-column label="设备名称" width="280" show-overflow-tooltip>
                  <template #default="{ row }">
                    <div class="device-name">
                      <span class="primary-name">{{ row.custom_name || row.device_name }}</span>
                      <span v-if="row.custom_name && row.device_name !== row.custom_name" class="secondary-name">
                        ({{ row.device_name }})
                      </span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="设备类型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small" type="info">
                      {{ getDeviceTypeText(row.device_type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="设备来源" width="240" show-overflow-tooltip>
                  <template #default="{ row }">
                    <div class="device-source-single">
                      {{ row.server_name || '未知服务器' }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getDeviceStatusColor(row.status)" size="small">
                      {{ getDeviceStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="最后连接" width="180" show-overflow-tooltip>
                  <template #default="{ row }">
                    {{ formatTime(row.last_connected) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default="{ row }">
                    <div class="action-buttons">
                      <el-button size="small" @click="viewDeviceDetail(row)">
                        详情
                      </el-button>
                      <el-button size="small" @click="viewServerDetail(row)">
                        服务器
                      </el-button>
                      <el-button size="small" type="danger" @click="removeDevice(row)">
                        移除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import websocketManager from '@/utils/websocket'
import {
  ArrowLeft,
  Refresh,
  ArrowDown,
  Collection,
  FolderOpened,
  Monitor,
  CircleCheck,
  Connection,
  User,
  InfoFilled,
  DataAnalysis,
  Search
} from '@element-plus/icons-vue'

// 路由和状态
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const groupId = route.params.groupId

// 搜索和筛选
const searchText = ref('')
const statusFilter = ref('')

// 分组详情数据
const groupDetail = reactive({
  id: null,
  name: '',
  description: '',
  device_count: 0,
  user_count: 0,
  status: 'active',
  created_at: null,
  updated_at: null
})

// 设备列表
const devices = ref([])

// 自动刷新定时器
let refreshTimer = null

// 计算属性
const onlineDeviceCount = computed(() => {
  return devices.value.filter(device => device.status === 'online').length
})

const offlineDeviceCount = computed(() => {
  return devices.value.filter(device => device.status === 'offline').length
})

const serverCount = computed(() => {
  const servers = new Set(devices.value.map(device => device.server_id))
  return servers.size
})

const filteredDevices = computed(() => {
  let result = devices.value

  // 按名称搜索
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    result = result.filter(device => 
      device.device_name && device.device_name.toLowerCase().includes(search)
    )
  }

  // 按状态筛选
  if (statusFilter.value) {
    result = result.filter(device => device.status === statusFilter.value)
  }

  return result
})

// 方法
const goBack = () => {
  // 返回到资源调度分组列表视图页面
  router.push('/device-center?tab=groups')
}

const getStatusColor = (status) => {
  const colorMap = {
    'active': 'success',
    'inactive': 'info',
    'disabled': 'danger'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'active': '活跃',
    'inactive': '非活跃',
    'disabled': '已禁用'
  }
  return textMap[status] || '未知'
}

const getDeviceStatusColor = (status) => {
  const colorMap = {
    'online': 'success',
    'offline': 'info',
    'busy': 'warning',
    'error': 'danger'
  }
  return colorMap[status] || 'info'
}

const getDeviceStatusText = (status) => {
  const textMap = {
    'online': '在线',
    'offline': '离线',
    'busy': '占用中',
    'error': '错误'
  }
  return textMap[status] || '未知'
}

const formatTime = (timeStr) => {
  if (!timeStr) return 'N/A'
  return new Date(timeStr).toLocaleString()
}

const getDeviceTypeText = (type) => {
  const typeMap = {
    '🔐 CA锁': 'CA锁',
    '加密锁': '加密锁',
    '📡 通信设备': '通信设备',
    '📶 蓝牙设备': '蓝牙设备',
    'ca_lock': 'CA锁',
    'encryption_key': '加密锁',
    'bank_ukey': '银行U盾',
    'communication': '通信设备',
    'bluetooth': '蓝牙设备',
    'other': '其他设备'
  }
  return typeMap[type] || type || '未知类型'
}

const formatHex = (value) => {
  return value ? `0x${value.toString(16).toUpperCase().padStart(4, '0')}` : 'N/A'
}

// 数据获取
const refreshData = async () => {
  loading.value = true
  try {
    // 调用真实API获取设备分组详情
    const response = await fetch(`/api/v1/device-groups/${groupId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    // 更新分组详情
    Object.assign(groupDetail, {
      id: data.id || groupId,
      name: data.name || '未命名分组',
      description: data.description || '',
      device_count: data.device_count || 0,
      user_count: data.user_count || 0,
      status: data.status || 'active',
      created_at: data.created_at,
      updated_at: data.updated_at
    })

    // 获取分组中的设备列表
    const devicesResponse = await fetch(`/api/v1/device-groups/${groupId}/devices`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      }
    })

    if (devicesResponse.ok) {
      const devicesData = await devicesResponse.json()
      console.log('分组设备API响应:', devicesData)

      // 处理API返回的标准格式 {success, data, message}
      let deviceList = []
      if (devicesData.success && devicesData.data) {
        // data字段直接是设备数组
        deviceList = Array.isArray(devicesData.data) ? devicesData.data : []
      } else {
        // 兼容旧格式
        deviceList = devicesData.devices || devicesData || []
      }
      devices.value = deviceList.map(device => ({
        id: device.id,
        device_id: device.device_id || device.id,
        device_name: device.device_name,
        device_type: device.device_type,
        custom_name: device.custom_name,
        server_id: device.slave_server_id,
        server_name: `服务器-${device.slave_server_id || '未知'}`,
        physical_port: device.physical_port,
        vendor_id: device.vendor_id,
        product_id: device.product_id,
        status: device.status,
        last_connected: device.added_at || device.last_used_at || device.connected_at
      }))
    } else {
      devices.value = []
    }

    loading.value = false
  } catch (error) {
    console.error('获取分组详情失败:', error)
    ElMessage.error('获取分组详情失败')
    loading.value = false
  }
}

const handleCommand = async (command) => {
  switch (command) {
    case 'edit':
      ElMessage.info('编辑分组功能开发中...')
      break
    case 'add-device':
      await showAddDeviceDialog()
      break
    case 'remove-device':
      ElMessage.info('请在设备列表中选择要移除的设备')
      break
    case 'delete':
      await deleteGroup()
      break
  }
}

// 显示添加设备对话框
const showAddDeviceDialog = async () => {
  try {
    // 获取可添加的设备列表（排除当前分组中的设备）
    const response = await fetch(`/api/v1/devices?exclude_group=${groupId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error('获取设备列表失败')
    }

    const availableDevicesData = await response.json()
    console.log('可添加设备API响应:', availableDevicesData)

    // 处理API返回的标准格式 {success, data, message}
    let availableDevices = []
    if (availableDevicesData.success && availableDevicesData.data) {
      availableDevices = availableDevicesData.data.devices || availableDevicesData.data || []
    } else {
      // 兼容旧格式
      availableDevices = availableDevicesData.devices || availableDevicesData || []
    }

    if (availableDevices.length === 0) {
      ElMessage.warning('没有可添加的设备')
      return
    }

    // 这里应该显示设备选择对话框
    // 暂时使用简单的确认对话框
    await ElMessageBox.confirm(
      `找到 ${availableDevices.length} 个可添加的设备，是否继续？`,
      '添加设备',
      {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    ElMessage.info('设备选择对话框功能开发中...')
  } catch (error) {
    if (error.message && error.message !== 'cancel') {
      ElMessage.error(error.message)
    }
  }
}

// 删除分组
const deleteGroup = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分组 "${groupDetail.name}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    const response = await fetch(`/api/v1/device-groups/${groupId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error('删除分组失败')
    }

    ElMessage.success('分组删除成功')
    router.push('/device-center/device-groups')
  } catch (error) {
    if (error.message && error.message !== 'cancel') {
      ElMessage.error(error.message)
    }
  }
}

const getCommandText = (command) => {
  const commandMap = {
    'edit': '编辑分组',
    'add-device': '添加设备',
    'remove-device': '移除设备',
    'delete': '删除分组'
  }
  return commandMap[command] || command
}

const viewDeviceDetail = (device) => {
  // 跳转到设备详情页面，携带来源分组信息以便正确返回
  const groupId = route.params.id
  router.push({
    path: `/device-center/device/${device.id}`,
    query: { from_group: groupId }
  })
}

const viewServerDetail = (device) => {
  router.push(`/device-center/slave-server/${device.server_id}`)
}

const removeDevice = async (device) => {
  try {
    await ElMessageBox.confirm(
      `确定要从分组中移除设备 "${device.custom_name || device.device_name}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用API移除设备
    const response = await fetch(`/api/v1/device-groups/${groupId}/devices`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        device_ids: [device.id]
      })
    })

    if (!response.ok) {
      throw new Error(`移除失败: ${response.statusText}`)
    }

    ElMessage.success(`设备 "${device.custom_name || device.device_name}" 已从分组中移除`)
    refreshData()
  } catch (error) {
    if (error.message && error.message !== 'cancel') {
      ElMessage.error(error.message)
    }
    // 用户取消操作或其他错误
  }
}

// WebSocket事件处理
const handleDeviceAssignmentUpdate = (data) => {
  console.log('收到设备分配更新事件:', data)
  // 检查是否涉及当前分组
  if (data.assignments) {
    const currentGroupAssignment = data.assignments.find(assignment =>
      assignment.group_id === parseInt(route.params.id)
    )
    if (currentGroupAssignment) {
      console.log('当前分组有设备分配更新，刷新数据')
      refreshData()
    }
  }
}

const handleDeviceUpdate = (data) => {
  console.log('收到设备更新事件:', data)
  // 刷新设备数据以确保最新状态
  refreshData()
}

// 生命周期
onMounted(() => {
  refreshData()
  // 设置自动刷新（每30秒）
  refreshTimer = setInterval(refreshData, 30000)

  // 监听WebSocket事件
  websocketManager.on('devices_batch_assigned', handleDeviceAssignmentUpdate)
  websocketManager.on('device_updates', handleDeviceUpdate)
  websocketManager.on('device_added', handleDeviceUpdate)
  websocketManager.on('device_removed', handleDeviceUpdate)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }

  // 移除WebSocket监听
  websocketManager.off('devices_batch_assigned', handleDeviceAssignmentUpdate)
  websocketManager.off('device_updates', handleDeviceUpdate)
  websocketManager.off('device_added', handleDeviceUpdate)
  websocketManager.off('device_removed', handleDeviceUpdate)
})
</script>

<style scoped>
.device-group-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  font-size: 14px;
  color: #606266;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.group-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 10px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.overview-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 0;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.card-icon.devices {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.online {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-icon.servers {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.users {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.card-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.detail-sections {
  margin-bottom: 20px;
}

.info-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
}

.info-value {
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.device-list {
  margin-top: 10px;
}

.server-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.server-name {
  font-weight: 500;
  color: #303133;
}

.server-ip {
  font-size: 12px;
  color: #909399;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.vid-pid {
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.status-icon {
  margin-right: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .header-left {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .group-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
}

/* 设备列表样式优化 */
.device-name {
  display: flex;
  flex-direction: column;
  gap: 2px;
  max-width: 100%;
}

.device-name .primary-name {
  font-weight: 500;
  color: #303133;
  word-break: break-word;
  line-height: 1.4;
}

.device-name .secondary-name {
  font-size: 12px;
  color: #909399;
  word-break: break-word;
  line-height: 1.3;
}

.device-source-single {
  color: #606266;
  word-break: break-word;
  line-height: 1.4;
  padding: 2px 0;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: nowrap;
  justify-content: flex-start;
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
  min-width: auto;
}

/* 表格整体优化 */
.device-list .el-table {
  width: 100%;
}

.device-list .el-table th {
  background-color: #fafafa;
  font-weight: 600;
}

.device-list .el-table td {
  padding: 8px 0;
}

.device-list .el-table .cell {
  padding: 0 8px;
  word-break: break-word;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .device-name {
    font-size: 13px;
  }

  .device-name .secondary-name {
    font-size: 11px;
  }

  .device-source-single {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .action-buttons .el-button {
    font-size: 11px;
    padding: 2px 6px;
  }
}
</style>

<style scoped>
.device-group-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  font-size: 14px;
  color: #606266;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.group-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 10px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.overview-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 0;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.card-icon.devices {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.online {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-icon.servers {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.users {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.card-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.detail-sections {
  margin-bottom: 20px;
}

.info-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
}

.info-value {
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.device-list {
  margin-top: 10px;
}

.server-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.server-name {
  font-weight: 500;
  color: #303133;
}

.server-ip {
  font-size: 12px;
  color: #909399;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.vid-pid {
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.status-icon {
  margin-right: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .header-left {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .group-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
}
</style>
