<template>
  <div class="slave-server-management">
    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button 
          type="primary" 
          @click="refreshData"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          刷新列表
        </el-button>
        <el-button
          type="success"
          @click="showAddDialog = true"
          v-if="canCreateSlaves"
        >
          <el-icon><Plus /></el-icon>
          添加分布式节点
        </el-button>
        <el-button
          type="warning"
          @click="forceSyncData"
          :loading="syncLoading"
          :disabled="selectedServers.length === 0"
          v-if="canManageSlaves"
        >
          <el-icon><Refresh /></el-icon>
          强制同步数据 ({{ selectedServers.length }})
        </el-button>
        <el-button
          type="danger"
          @click="bulkDeleteServers"
          :loading="deleteLoading"
          :disabled="selectedServers.length === 0"
          v-if="canDeleteSlaves"
        >
          <el-icon><Delete /></el-icon>
          批量删除节点 ({{ selectedServers.length }})
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-select
          v-model="statusFilter"
          placeholder="筛选状态"
          style="width: 120px; margin-right: 8px;"
          clearable
          @change="filterServers"
        >
          <el-option label="全部" value="" />
          <el-option label="在线" value="online" />
          <el-option label="离线" value="offline" />
        </el-select>
        <el-input
          v-model="searchText"
          placeholder="搜索服务器名称..."
          style="width: 200px;"
          clearable
          @input="filterServers"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 从服务器列表 -->
    <el-table
      :data="paginatedServers"
      v-loading="loading"
      stripe
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" />
      <el-table-column prop="id" label="ID" width="50" />
      <el-table-column label="分布式节点名称" min-width="220" sortable>
        <template #default="{ row }">
          <div class="server-name">
            <el-icon class="server-icon">
              <Monitor />
            </el-icon>
            <div class="server-name-text">
              <el-tooltip :content="row.name" placement="top" :disabled="row.name.length <= 25">
                <span class="server-name-display">{{ row.name }}</span>
              </el-tooltip>
              <div class="status-tags">
                <el-tag v-if="row.is_online" type="success" size="small">在线</el-tag>
                <el-tag v-else type="danger" size="small">离线</el-tag>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="IP地址" width="120">
        <template #default="{ row }">
          <span class="ip-address">{{ row.ip_address }}:{{ row.port }}</span>
        </template>
      </el-table-column>
      <el-table-column label="VH端口" width="65">
        <template #default="{ row }">
          <span>{{ row.vh_port }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="85">
        <template #default="{ row }">
          <el-tag :type="getStatusColor(row.status)" size="small">
            <el-icon class="status-icon">
              <CircleCheck v-if="row.status === 'online'" />
              <CircleClose v-else />
            </el-icon>
            {{ row.status === 'online' ? '在线' : '离线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="设备数量" width="75">
        <template #default="{ row }">
          <span class="device-count">{{ row.device_count || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后心跳验证" width="160" class-name="no-wrap">
        <template #default="{ row }">
          <span v-if="row.last_seen && row.last_seen !== 'null' && row.last_seen !== ''" class="heartbeat-time">
            {{ row.last_seen }}
          </span>
          <span v-else class="text-muted">
            无心跳记录
          </span>
        </template>
      </el-table-column>
      <el-table-column label="最后配置核对" width="160" class-name="no-wrap">
        <template #default="{ row }">
          <span v-if="row.last_config_check" class="config-check-time">
            {{ formatTime(row.last_config_check) }}
          </span>
          <span v-else class="text-muted">无核对记录</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160" fixed="right" class-name="no-wrap">
        <template #default="{ row }">
          <el-button 
            type="primary" 
            size="small" 
            @click="viewDetail(row)"
          >
            详情
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="controlServer(row, 'restart')"
            v-if="canControlSlaves"
          >
            重启
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDeleteServer(row)"
            v-if="canDeleteSlaves"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalServers"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加从服务器对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加从服务器"
      width="500px"
      @close="resetAddForm"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addRules"
        label-width="100px"
      >
        <el-form-item label="服务器名称" prop="server_name">
          <el-input v-model="addForm.server_name" placeholder="请输入服务器名称" />
        </el-form-item>
        <el-form-item label="IP地址" prop="server_ip">
          <el-input v-model="addForm.server_ip" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="服务端口" prop="server_port">
          <el-input-number 
            v-model="addForm.server_port" 
            :min="1" 
            :max="65535"
            placeholder="8889"
          />
        </el-form-item>
        <el-form-item label="VH端口" prop="vh_port">
          <el-input-number 
            v-model="addForm.vh_port" 
            :min="1" 
            :max="65535"
            placeholder="7575"
          />
        </el-form-item>
        <el-form-item label="位置">
          <el-input v-model="addForm.location" placeholder="请输入服务器位置" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="addForm.description" 
            type="textarea" 
            placeholder="请输入服务器描述"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="addServer" :loading="addLoading">
          确认添加
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Plus,
  Search,
  Monitor,
  CircleCheck,
  CircleClose,
  Delete
} from '@element-plus/icons-vue'
import { getSlaveServerList, createSlaveServer } from '@/api/slaveServers.js'

const emit = defineEmits(['stats-update'])
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const addLoading = ref(false)
const syncLoading = ref(false)
const deleteLoading = ref(false)
const showAddDialog = ref(false)
const slaveServers = ref([])
const selectedServers = ref([])
const statusFilter = ref('')
const searchText = ref('')

// 分页数据
const currentPage = ref(1)
const pageSize = ref(20)
const totalServers = ref(0)

// 添加表单
const addFormRef = ref()
const addForm = reactive({
  server_name: '',
  server_ip: '',
  server_port: 8889,
  vh_port: 7575,
  location: '',
  description: ''
})

const addRules = {
  server_name: [
    { required: true, message: '请输入服务器名称', trigger: 'blur' }
  ],
  server_ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入正确的IP地址格式', trigger: 'blur' }
  ],
  server_port: [
    { required: true, message: '请输入服务端口', trigger: 'blur' }
  ],
  vh_port: [
    { required: true, message: '请输入VH端口', trigger: 'blur' }
  ]
}

// 权限检查计算属性
const canManageSlaves = computed(() => {
  // 临时开放管理权限用于测试
  return true
})

const canDeleteSlaves = computed(() => {
  // 临时开放删除权限用于测试
  return true

  // 正式权限检查逻辑（暂时注释）
  // return userStore.user && (
  //   userStore.user.is_superuser ||
  //   userStore.user.role_name === '全域管理员' ||
  //   userStore.user.role_name === '超级管理员'
  // )
})

const canCreateSlaves = computed(() => {
  // 临时开放创建权限用于测试
  return true
})

const canControlSlaves = computed(() => {
  // 临时开放控制权限用于测试
  return true
})

// 计算属性
const filteredServers = computed(() => {
  let result = slaveServers.value

  // 按状态筛选
  if (statusFilter.value) {
    result = result.filter(server => server.status === statusFilter.value)
  }

  // 按名称搜索
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    result = result.filter(server =>
      (server.name && server.name.toLowerCase().includes(search)) ||
      (server.ip_address && server.ip_address.toLowerCase().includes(search))
    )
  }

  // 智能排序：在线服务器优先，然后按ID升序排列
  const sorted = result.sort((a, b) => {
    // 首先按状态排序：在线优先
    if (a.status === 'online' && b.status !== 'online') {
      return -1
    }
    if (a.status !== 'online' && b.status === 'online') {
      return 1
    }

    // 状态相同时，按ID升序排列
    return a.id - b.id
  })

  // 更新总数
  totalServers.value = sorted.length

  return sorted
})

// 分页后的服务器列表
const paginatedServers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredServers.value.slice(start, end)
})

// 方法
const getStatusColor = (status) => {
  return status === 'online' ? 'success' : 'danger'
}

const formatTime = (time) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

const refreshData = async () => {
  loading.value = true
  try {
    // 调用真实API获取从服务器列表
    const response = await getSlaveServerList()
    slaveServers.value = response.data || []

    totalServers.value = slaveServers.value.length

    // 调试输出：检查数据结构
    console.log('从服务器列表数据:', response.data)
    if (response.data && response.data.length > 0) {
      console.log('第一个服务器的数据:', response.data[0])
      console.log('第一个服务器的last_seen字段:', response.data[0].last_seen)
    }

    // 更新统计数据
    updateStats()

  } catch (error) {
    console.error('加载从服务器数据失败:', error)
    ElMessage.error('加载从服务器数据失败')
  } finally {
    loading.value = false
  }
}

const updateStats = () => {
  const stats = {
    total: slaveServers.value.length,
    online: slaveServers.value.filter(s => s.status === 'online').length,
    offline: slaveServers.value.filter(s => s.status === 'offline').length
  }
  emit('stats-update', stats)
}

const filterServers = () => {
  // 筛选逻辑已在计算属性中实现
}

const handleSizeChange = (size) => {
  pageSize.value = size
  refreshData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  refreshData()
}

const viewDetail = (server) => {
  // 跳转到详情页面
  router.push(`/device-center/slave-server/${server.id}`)
}

const controlServer = async (server, action) => {
  try {
    await ElMessageBox.confirm(
      `确定要${action === 'restart' ? '重启' : '控制'}服务器 "${server.name}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success(`服务器 "${server.name}" ${action === 'restart' ? '重启' : '控制'}成功`)
  } catch {
    // 用户取消操作
  }
}

const deleteServer = async (server) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除服务器 "${server.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success(`服务器 "${server.name}" 删除成功`)
    refreshData()
  } catch {
    // 用户取消操作
  }
}

const addServer = async () => {
  try {
    await addFormRef.value.validate()
    
    addLoading.value = true

    // 调用真实API添加从服务器
    await createSlaveServer(addForm)

    ElMessage.success('从服务器添加成功')
    showAddDialog.value = false
    resetAddForm()
    refreshData()
    refreshData()
    
  } catch (error) {
    console.error('添加从服务器失败:', error)
    ElMessage.error('添加从服务器失败')
  } finally {
    addLoading.value = false
  }
}

const resetAddForm = () => {
  Object.assign(addForm, {
    server_name: '',
    server_ip: '',
    server_port: 8889,
    vh_port: 7575,
    location: '',
    description: ''
  })
  addFormRef.value?.clearValidate()
}

// 强制同步相关方法
const handleSelectionChange = (selection) => {
  selectedServers.value = selection
}

const forceSyncData = async () => {
  if (selectedServers.value.length === 0) {
    ElMessage.warning('请先选择要同步的从服务器')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要强制同步选中的 ${selectedServers.value.length} 个从服务器的设备数据吗？\n\n此操作将：\n1. 清空选中从服务器的现有设备数据\n2. 重新从从服务器获取最新设备信息\n3. 应用USB.IDS增强识别`,
      '确认强制同步',
      {
        confirmButtonText: '确定同步',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    syncLoading.value = true

    // 提取选中服务器的ID
    const slaveIds = selectedServers.value.map(server => server.id)

    // 调用强制同步API
    const response = await fetch('/api/v1/slave/force-sync', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ slave_ids: slaveIds })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()

    if (result.success) {
      ElMessage.success(`强制同步完成！${result.message}`)

      // 显示详细结果
      const successCount = result.results.filter(r => r.success).length
      const failCount = result.results.filter(r => !r.success).length

      if (failCount > 0) {
        const failedServers = result.results
          .filter(r => !r.success)
          .map(r => `服务器${r.slave_id}: ${r.message}`)
          .join('\n')

        ElMessage.warning(`同步完成，但有 ${failCount} 个服务器同步失败：\n${failedServers}`)
      }

      // 刷新数据
      await refreshData()

      // 清空选择
      selectedServers.value = []

    } else {
      throw new Error(result.message || '强制同步失败')
    }

  } catch (error) {
    console.error('强制同步失败:', error)
    ElMessage.error(`强制同步失败: ${error.message}`)
  } finally {
    syncLoading.value = false
  }
}

// 批量删除从服务器
const bulkDeleteServers = async () => {
  if (selectedServers.value.length === 0) {
    ElMessage.warning('请先选择要删除的从服务器')
    return
  }

  try {
    // 详细的确认对话框
    const serverNames = selectedServers.value.map(s => s.name).join('、')
    const confirmMessage = `
      <div style="text-align: left;">
        <p><strong>确定要删除以下 ${selectedServers.value.length} 个从服务器吗？</strong></p>
        <p style="color: #666; font-size: 14px; margin: 10px 0;">${serverNames}</p>

        <p><strong>此操作将执行深度清理：</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px; color: #666;">
          <li>🗑️ 删除从服务器记录</li>
          <li>🔌 删除所有关联的USB设备记录</li>
          <li>👥 从设备分组中移除这些设备</li>
          <li>👤 清除设备的直接用户分配关系</li>
          <li>🔄 更新设备占用记录状态</li>
          <li>🧹 清理空的设备分组</li>
          <li>📋 保留审计日志（合规要求）</li>
        </ul>

        <p style="color: #e74c3c; font-weight: bold;">⚠️ 此操作不可撤销！</p>
      </div>
    `

    await ElMessageBox.confirm(confirmMessage, '确认深度删除', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error',
      dangerouslyUseHTMLString: true,
      customClass: 'bulk-delete-confirm'
    })

    deleteLoading.value = true

    // 使用新的批量删除API
    const serverIds = selectedServers.value.map(server => server.id)
    const result = await batchDeleteSlaveServers(serverIds)

    // 显示详细结果
    if (result.batch_results.successful_deletions > 0) {
      const successMsg = `✅ 成功删除 ${result.batch_results.successful_deletions} 个从服务器`
      const statsMsg = `📊 清理统计：设备 ${result.batch_results.overall_stats.total_devices_deleted} 个，分组关联 ${result.batch_results.overall_stats.total_group_assignments_removed} 个，空分组清理 ${result.batch_results.overall_stats.total_empty_groups_cleaned} 个`
      ElMessage.success(`${successMsg}\n${statsMsg}`)
    }

    if (result.batch_results.failed_deletions > 0) {
      const failedDetails = result.batch_results.deletion_details
        .filter(detail => detail.status === 'failed')
        .map(detail => `服务器 ${detail.server_id}: ${detail.error}`)
        .join('\n')

      ElMessage.error(`❌ ${result.batch_results.failed_deletions} 个服务器删除失败：\n${failedDetails}`)
    }

    // 刷新数据
    await refreshData()

    // 清空选择
    selectedServers.value = []

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error(`批量删除失败: ${error.message}`)
    }
  } finally {
    deleteLoading.value = false
  }
}

// 单个删除从服务器
const deleteSlaveServer = async (serverId) => {
  try {
    const response = await fetch(`/api/v1/slave/${serverId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    return result

  } catch (error) {
    console.error(`删除从服务器 ${serverId} 失败:`, error)
    throw error
  }
}

// 批量删除从服务器
const batchDeleteSlaveServers = async (serverIds) => {
  try {
    const response = await fetch('/api/v1/slave/batch-delete', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(serverIds)
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    return result

  } catch (error) {
    console.error('批量删除从服务器失败:', error)
    throw error
  }
}

// 单个删除操作（从表格操作列调用）
const handleDeleteServer = async (server) => {
  try {
    const confirmMessage = `
      <div style="text-align: left;">
        <p><strong>确定要删除从服务器 "${server.name}" 吗？</strong></p>

        <p><strong>此操作将执行深度清理：</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px; color: #666;">
          <li>🗑️ 删除从服务器记录</li>
          <li>🔌 删除所有关联的USB设备记录</li>
          <li>👥 从设备分组中移除这些设备</li>
          <li>👤 清除设备的直接用户分配关系</li>
          <li>🔄 更新设备占用记录状态</li>
          <li>🧹 清理空的设备分组</li>
          <li>📋 保留审计日志（合规要求）</li>
        </ul>

        <p style="color: #e74c3c; font-weight: bold;">⚠️ 此操作不可撤销！</p>
      </div>
    `

    await ElMessageBox.confirm(confirmMessage, '确认深度删除', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error',
      dangerouslyUseHTMLString: true
    })

    const result = await deleteSlaveServer(server.id)

    // 显示详细删除结果
    if (result.deletion_stats) {
      const stats = result.deletion_stats
      const successMsg = `✅ 从服务器 "${server.name}" 删除成功`

      let detailMsg = `📊 清理统计：设备 ${stats.devices_deleted} 个，分组关联 ${stats.device_group_assignments_removed} 个`

      if (stats.empty_groups_cleaned > 0) {
        detailMsg += `，清理空分组 ${stats.empty_groups_cleaned} 个`
      }

      if (stats.affected_groups && stats.affected_groups.length > 0) {
        const groupNames = stats.affected_groups.map(g => g.name).join('、')
        detailMsg += `\n🔗 受影响的设备分组：${groupNames}`
      }

      if (stats.affected_users && stats.affected_users.length > 0) {
        const userNames = stats.affected_users.map(u => u.username).join('、')
        detailMsg += `\n👤 受影响的用户：${userNames}`
      }

      ElMessage.success(`${successMsg}\n${detailMsg}`)
    } else {
      ElMessage.success(`从服务器 "${server.name}" 删除成功`)
    }

    // 刷新数据
    await refreshData()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除从服务器失败:', error)
      ElMessage.error(`删除失败: ${error.message}`)
    }
  }
}

// 暴露方法给父组件
defineExpose({
  refreshData
})

// 生命周期
onMounted(() => {
  refreshData()

  // 调试权限信息
  console.log('用户权限调试信息:', {
    user: userStore.user,
    canManageSlaves: canManageSlaves.value,
    canDeleteSlaves: canDeleteSlaves.value,
    canCreateSlaves: canCreateSlaves.value,
    canControlSlaves: canControlSlaves.value
  })
})
</script>

<style scoped>
/* 批量删除确认对话框样式 */
:deep(.bulk-delete-confirm) {
  .el-message-box__content {
    max-height: 400px;
    overflow-y: auto;
  }

  .el-message-box__message {
    line-height: 1.6;
  }

  ul {
    list-style-type: none;
    padding-left: 0;
  }

  li {
    padding: 4px 0;
    border-left: 3px solid #f39c12;
    padding-left: 10px;
    margin: 2px 0;
  }
}

/* 表格选择样式优化 */
.el-table {
  .el-table__row.selected {
    background-color: #f0f9ff;
  }
}

/* 批量操作按钮样式 */
.el-button.is-disabled {
  opacity: 0.5;
}

/* 删除统计信息样式 */
.deletion-stats {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  font-size: 12px;
  color: #6c757d;
}

.slave-server-management {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.server-name {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.server-icon {
  color: #409eff;
  font-size: 16px;
  flex-shrink: 0;
}

.server-name-text {
  flex: 1;
  min-width: 0;
}

.server-name-display {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  line-height: 1.4;
}

/* 确保关键列不换行 */
:deep(.no-wrap .cell) {
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
}

.heartbeat-time,
.config-check-time {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  white-space: nowrap;
}

/* 表格整体优化 */
:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table .cell) {
  padding: 0 8px;
  line-height: 1.4;
}

.ip-address {
  font-family: monospace;
  font-size: 12px;
  color: #606266;
}

.status-icon {
  margin-right: 4px;
}

.device-count {
  font-weight: 600;
  color: #409eff;
}

.text-muted {
  color: #909399;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
