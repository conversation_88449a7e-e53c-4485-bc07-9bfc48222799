<template>
  <div class="device-group-management">
    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button 
          type="primary" 
          @click="refreshData"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          刷新列表
        </el-button>
        <el-button
          type="success"
          @click="showCreateDialog = true"
          v-if="userStore.hasPermission('device.group')"
        >
          <el-icon><Plus /></el-icon>
          创建分组
        </el-button>
        <el-button
          type="primary"
          @click="openAssignmentDialog"
          v-if="userStore.hasPermission('device.assign')"
        >
          <el-icon><Collection /></el-icon>
          设备分配
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-select
          v-model="typeFilter"
          placeholder="筛选分组类型"
          style="width: 180px; margin-right: 8px;"
          clearable
          @change="filterGroups"
        >
          <el-option label="全部分组" value="" />
          <el-option label="单设备分组" value="single_device" />
          <el-option label="多设备分组" value="multi_device" />
          <el-option label="服务器分组" value="server_group" />
          <el-option label="交叉分组" value="cross_group" />
          <el-option label="批量分组" value="batch_group" />
        </el-select>
        <el-input
          v-model="searchText"
          placeholder="搜索分组名称..."
          style="width: 200px;"
          clearable
          @input="filterGroups"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 视图切换标签 -->
    <div class="view-tabs">
      <el-tabs v-model="activeView" @tab-change="handleViewChange">
        <el-tab-pane label="列表视图" name="list">
          <template #label>
            <span>
              <el-icon><List /></el-icon>
              列表视图
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="嵌套树视图" name="tree">
          <template #label>
            <span>
              <el-icon><Operation /></el-icon>
              嵌套树视图
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 列表视图 -->
    <div v-show="activeView === 'list'" class="list-view">
      <!-- 设备分组列表 -->
      <el-table
        :data="filteredGroups"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="分组名称" min-width="150">
        <template #default="{ row }">
          <div class="group-name">
            <el-icon class="group-icon">
              <Collection />
            </el-icon>
            <span>{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分组类型" width="120">
        <template #default="{ row }">
          <el-tag 
            :type="getGroupTypeColor(row.group_type)"
            size="small"
          >
            {{ getGroupTypeText(row.group_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="设备数量" width="100">
        <template #default="{ row }">
          <span class="device-count">{{ row.device_count || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="虚拟设备" width="100">
        <template #default="{ row }">
          <el-tag 
            v-if="row.has_virtual_devices"
            type="warning"
            size="small"
          >
            有占位
          </el-tag>
          <span v-else class="text-muted">无</span>
        </template>
      </el-table-column>
      <el-table-column label="权限用户" width="100">
        <template #default="{ row }">
          <span class="user-count">{{ row.user_count || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="150">
        <template #default="{ row }">
          <span>{{ formatTime(row.created_at) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="150" />
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="viewGroupDetail(row)"
          >
            详情
          </el-button>

          <!-- 权限标识 -->
          <el-tag
            v-if="row.is_readonly"
            type="info"
            size="small"
            style="margin: 0 5px;"
          >
            只读
          </el-tag>

          <!-- 管理操作按钮 -->
          <template v-if="row.can_manage">
            <el-button
              type="warning"
              size="small"
              @click="editGroup(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteGroup(row)"
            >
              删除
            </el-button>
          </template>

          <!-- 无权限提示 -->
          <el-tooltip
            v-else-if="row.is_readonly"
            content="此分组由上级管理员创建，您只有查看权限"
            placement="top"
          >
            <el-button
              type="info"
              size="small"
              disabled
            >
              无权限
            </el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    </div>

    <!-- 嵌套树视图 -->
    <div v-show="activeView === 'tree'" class="tree-view">
      <NestedGroupTree
        :refresh-trigger="refreshTrigger"
        @group-created="handleNestedGroupCreated"
        @group-updated="handleNestedGroupUpdated"
        @group-deleted="handleNestedGroupDeleted"
      />
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalGroups"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建分组对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建设备分组"
      width="500px"
      @close="resetCreateForm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="分组类型" prop="group_type">
          <el-select v-model="createForm.group_type" placeholder="请选择分组类型" @change="onGroupTypeChange">
            <el-option label="单设备分组" value="single_device">
              <div class="option-content">
                <div class="option-title">单设备分组</div>
                <div class="option-desc">一个设备独立成组</div>
              </div>
            </el-option>
            <el-option label="多设备分组" value="multi_device">
              <div class="option-content">
                <div class="option-title">多设备分组</div>
                <div class="option-desc">多个设备组成一个分组</div>
              </div>
            </el-option>
            <el-option label="服务器分组" value="server_group">
              <div class="option-content">
                <div class="option-title">服务器分组</div>
                <div class="option-desc">以从服务器为单位进行分组</div>
              </div>
            </el-option>
            <el-option label="交叉分组" value="cross_group">
              <div class="option-content">
                <div class="option-title">交叉分组</div>
                <div class="option-desc">一个设备可属于多个分组</div>
              </div>
            </el-option>
            <el-option label="批量分组" value="batch_group">
              <div class="option-content">
                <div class="option-title">批量分组</div>
                <div class="option-desc">支持批量设备的分组操作</div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="createForm.description" 
            type="textarea" 
            placeholder="请输入分组描述"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createGroup" :loading="createLoading">
          确认创建
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑分组对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑设备分组"
      width="500px"
      @close="resetEditForm"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="分组类型" prop="group_type">
          <el-select v-model="editForm.group_type" placeholder="请选择分组类型">
            <el-option label="单设备分组" value="single_device" />
            <el-option label="多设备分组" value="multi_device" />
            <el-option label="服务器分组" value="server_group" />
            <el-option label="交叉分组" value="cross_group" />
            <el-option label="批量分组" value="batch_group" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="editForm.description" 
            type="textarea" 
            placeholder="请输入分组描述"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="updateGroup" :loading="editLoading">
          确认修改
        </el-button>
      </template>
    </el-dialog>

    <!-- 设备分配对话框 -->
    <el-dialog
      v-model="showAssignmentDialog"
      title="设备分配"
      width="80%"
      @close="resetAssignmentDialog"
    >
      <div class="assignment-content">
        <el-alert
          title="批量分配提示"
          description="选择目标分组和设备后点击【分配设备】，分配成功后可继续选择其他设备进行下一轮分配，完成所有操作后点击【完成分配】关闭对话框。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>选择设备分组</h4>
            <el-select
              v-model="selectedGroupForAssignment"
              placeholder="请选择目标分组"
              style="width: 100%"
              @change="handleGroupSelection"
            >
              <el-option
                v-for="group in deviceGroups"
                :key="group.id"
                :label="group.name"
                :value="group.id"
              />
            </el-select>
          </el-col>
          <el-col :span="12">
            <h4>设备分配状态</h4>
            <div class="assignment-stats">
              <el-tag type="info">待分配: {{ availableDevices.length }}</el-tag>
              <el-tag type="success" style="margin-left: 10px">已选择: {{ selectedDevicesForAssignment.length }}</el-tag>
            </div>
          </el-col>
        </el-row>

        <div class="device-selection" style="margin-top: 20px;">
          <h4>选择设备</h4>
          <el-table
            ref="deviceSelectionTable"
            :data="availableDevices"
            @selection-change="handleDeviceSelectionChange"
            max-height="400"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="设备名称" min-width="150">
              <template #default="{ row }">
                {{ row.custom_name || row.device_name || row.name || '未知设备' }}
              </template>
            </el-table-column>
            <el-table-column prop="device_type" label="设备类型" width="120">
              <template #default="{ row }">
                <el-tag size="small">{{ getDeviceTypeText(row.device_type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusColor(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="所属服务器" width="150">
              <template #default="{ row }">
                {{ row.server_name || row.slave_server?.name || row.slave_server_name || '未知服务器' }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAssignmentDialog = false">取消</el-button>
          <el-button
            type="success"
            @click="finishAssignment"
          >
            完成分配
          </el-button>
          <el-button
            type="primary"
            @click="executeDeviceAssignment"
            :loading="assignmentLoading"
            :disabled="!selectedGroupForAssignment || selectedDevicesForAssignment.length === 0"
          >
            分配设备
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import websocketManager from '@/utils/websocket'
import NestedGroupTree from './NestedGroupTree.vue'
import {
  Refresh,
  Plus,
  Search,
  Collection
} from '@element-plus/icons-vue'

const emit = defineEmits(['stats-update'])
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const editLoading = ref(false)
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showAssignmentDialog = ref(false)
const deviceGroups = ref([])
const typeFilter = ref('')
const searchText = ref('')
const activeView = ref('list')
const refreshTrigger = ref(0)

// 设备分配相关
const assignmentLoading = ref(false)
const selectedGroupForAssignment = ref(null)
const selectedDevicesForAssignment = ref([])
const availableDevices = ref([])
const deviceSelectionTable = ref()

// 分页数据
const currentPage = ref(1)
const pageSize = ref(20)
const totalGroups = ref(0)

// 创建表单
const createFormRef = ref()
const createForm = reactive({
  name: '',
  group_type: '',
  description: ''
})

const createRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' }
  ],
  group_type: [
    { required: true, message: '请选择分组类型', trigger: 'change' }
  ]
}

// 编辑表单
const editFormRef = ref()
const editForm = reactive({
  id: null,
  name: '',
  group_type: '',
  description: ''
})

const editRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' }
  ],
  group_type: [
    { required: true, message: '请选择分组类型', trigger: 'change' }
  ]
}

// 计算属性
const filteredGroups = computed(() => {
  let result = deviceGroups.value

  // 按类型筛选
  if (typeFilter.value) {
    result = result.filter(group => group.group_type === typeFilter.value)
  }

  // 按名称搜索
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    result = result.filter(group => 
      group.name.toLowerCase().includes(search)
    )
  }

  return result
})

// 方法
const getGroupTypeColor = (type) => {
  const colorMap = {
    'single_device': 'info',
    'multi_device': 'primary',
    'server_group': 'success',
    'cross_group': 'warning',
    'batch_group': 'danger'
  }
  return colorMap[type] || ''
}

const getGroupTypeText = (type) => {
  const textMap = {
    'single_device': '单设备分组',
    'multi_device': '多设备分组',
    'server_group': '服务器分组',
    'cross_group': '交叉分组',
    'batch_group': '批量分组'
  }
  return textMap[type] || '未知类型'
}

const onGroupTypeChange = (type) => {
  // 根据分组类型调整表单验证和提示
  switch (type) {
    case 'single_device':
      ElMessage.info('单设备分组：每个分组只包含一个设备')
      break
    case 'multi_device':
      ElMessage.info('多设备分组：可以包含多个不同的设备')
      break
    case 'server_group':
      ElMessage.info('服务器分组：按从服务器整体进行分组')
      break
    case 'cross_group':
      ElMessage.info('交叉分组：设备可以同时属于多个分组')
      break
    case 'batch_group':
      ElMessage.info('批量分组：支持批量设备的分组操作')
      break
  }
}

const formatTime = (time) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

const refreshData = async () => {
  loading.value = true
  try {
    // 调用真实API获取设备分组数据
    const response = await fetch('/api/v1/device-groups/', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('获取到的设备分组数据:', data)

    // 转换数据格式以匹配前端需求
    deviceGroups.value = data.map(group => ({
      id: group.id,
      name: group.name,
      group_type: group.group_type,
      device_count: group.device_count || 0,
      has_virtual_devices: group.has_virtual_devices || false,
      user_count: group.user_count || 0,
      description: group.description || '',
      created_at: group.created_at,
      updated_at: group.updated_at,
      is_active: group.is_active
    }))

    console.log('处理后的设备分组数据:', deviceGroups.value)

    totalGroups.value = deviceGroups.value.length

    // 更新统计数据
    updateStats()

  } catch (error) {
    console.error('加载设备分组数据失败:', error)
    ElMessage.error(`加载设备分组数据失败: ${error.message}`)
    deviceGroups.value = []
    totalGroups.value = 0
  } finally {
    loading.value = false
  }
}

const updateStats = () => {
  const stats = {
    total: deviceGroups.value.length,
    server: deviceGroups.value.filter(g => g.group_type === 'server').length,
    mixed: deviceGroups.value.filter(g => g.group_type === 'mixed').length
  }
  emit('stats-update', stats)
}

const filterGroups = () => {
  // 筛选逻辑已在计算属性中实现
}

const handleSizeChange = (size) => {
  pageSize.value = size
  refreshData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  refreshData()
}

const viewGroupDetail = (group) => {
  try {
    console.log('点击详情按钮，分组信息:', group)
    console.log('准备跳转到路径:', `/device-center/device-group/${group.id}`)

    // 跳转到分组详情页面
    router.push(`/device-center/device-group/${group.id}`)
      .then(() => {
        console.log('路由跳转成功')
      })
      .catch((error) => {
        console.error('路由跳转失败:', error)
        ElMessage.error(`跳转失败: ${error.message}`)
      })
  } catch (error) {
    console.error('viewGroupDetail函数执行失败:', error)
    ElMessage.error(`操作失败: ${error.message}`)
  }
}

const editGroup = (group) => {
  Object.assign(editForm, {
    id: group.id,
    name: group.name,
    group_type: group.group_type,
    description: group.description
  })
  showEditDialog.value = true
}

const deleteGroup = async (group) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分组 "${group.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success(`分组 "${group.name}" 删除成功`)
    refreshData()
  } catch {
    // 用户取消操作
  }
}

const createGroup = async () => {
  try {
    await createFormRef.value.validate()

    createLoading.value = true

    // 调用真实API创建设备分组
    const response = await fetch('/api/v1/device-groups/', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('omnilink_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: createForm.name,
        group_type: createForm.type,
        description: createForm.description,
        auto_virtual: false
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }

    const result = await response.json()
    console.log('分组创建成功:', result)

    ElMessage.success('设备分组创建成功')
    showCreateDialog.value = false
    resetCreateForm()
    refreshData()

  } catch (error) {
    console.error('创建设备分组失败:', error)
    ElMessage.error(`创建设备分组失败: ${error.message}`)
  } finally {
    createLoading.value = false
  }
}

const updateGroup = async () => {
  try {
    await editFormRef.value.validate()
    
    editLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('设备分组修改成功')
    showEditDialog.value = false
    resetEditForm()
    refreshData()
    
  } catch (error) {
    console.error('修改设备分组失败:', error)
    ElMessage.error('修改设备分组失败')
  } finally {
    editLoading.value = false
  }
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    group_type: '',
    description: ''
  })
  createFormRef.value?.clearValidate()
}

const resetEditForm = () => {
  Object.assign(editForm, {
    id: null,
    name: '',
    group_type: '',
    description: ''
  })
  editFormRef.value?.clearValidate()
}

// 暴露方法给父组件
defineExpose({
  refreshData
})

// WebSocket事件处理
const handleGroupCreated = (message) => {
  console.log('分组创建事件:', message)
  ElMessage.success(`分组 "${message.group_name}" 已创建`)
  refreshData()
}

const handleGroupsUpdated = (message) => {
  console.log('分组更新事件:', message)
  refreshData()
}

const handleDevicesAssigned = (message) => {
  console.log('设备分配事件:', message)
  ElMessage.info(`${message.added_count || message.total_assigned} 个设备已分配到分组`)
  refreshData()
}

const handleAssignmentCompleted = (result) => {
  console.log('设备分配完成:', result)
  ElMessage.success(`批量分配完成: ${result.total_assigned} 个设备已分配`)
  refreshData()
}

// 设备分配相关方法
const openAssignmentDialog = async () => {
  showAssignmentDialog.value = true
  // 确保分组数据和设备数据都是最新的
  await Promise.all([
    loadAvailableDevices(),
    refreshData() // 刷新分组数据
  ])
}

const loadAvailableDevices = async () => {
  try {
    const response = await fetch('/api/v1/devices', {
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      availableDevices.value = data.devices || []
    } else {
      throw new Error(`HTTP ${response.status}`)
    }
  } catch (error) {
    console.error('加载可用设备失败:', error)
    ElMessage.error(`加载可用设备失败: ${error.message}`)
  }
}

const handleGroupSelection = (groupId) => {
  selectedGroupForAssignment.value = groupId
  // 清空之前的设备选择
  selectedDevicesForAssignment.value = []
  if (deviceSelectionTable.value) {
    deviceSelectionTable.value.clearSelection()
  }
}

const handleDeviceSelectionChange = (selection) => {
  selectedDevicesForAssignment.value = selection
}

const executeDeviceAssignment = async () => {
  try {
    assignmentLoading.value = true

    const requestData = {
      assignments: [{
        group_id: selectedGroupForAssignment.value,
        device_ids: selectedDevicesForAssignment.value.map(device => device.id)
      }]
    }

    const response = await fetch('/api/v1/device-groups/batch-assign', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })

    if (response.ok) {
      const result = await response.json()
      const totalAssigned = result.assignments ? result.assignments.reduce((sum, assignment) => sum + assignment.added_devices.length, 0) : 0
      ElMessage.success(`成功分配 ${totalAssigned} 个设备，可继续分配其他设备`)

      // 保持对话框打开，但清空设备选择
      selectedDevicesForAssignment.value = []
      if (deviceSelectionTable.value) {
        deviceSelectionTable.value.clearSelection()
      }

      // 刷新可用设备列表，移除已分配的设备
      await loadAvailableDevices()
      refreshData()
    } else {
      const errorData = await response.json()
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }
  } catch (error) {
    console.error('设备分配失败:', error)
    ElMessage.error(`设备分配失败: ${error.message}`)
  } finally {
    assignmentLoading.value = false
  }
}

const finishAssignment = () => {
  showAssignmentDialog.value = false
  ElMessage.success('设备分配操作已完成')
}

const resetAssignmentDialog = () => {
  selectedGroupForAssignment.value = null
  selectedDevicesForAssignment.value = []
  availableDevices.value = []
  if (deviceSelectionTable.value) {
    deviceSelectionTable.value.clearSelection()
  }
}

// 设备状态和类型显示辅助方法
const getDeviceTypeText = (type) => {
  const typeMap = {
    'ca_lock': 'CA锁',
    'encryption_key': '加密锁',
    'bank_ukey': '银行U盾',
    'other': '其他设备'
  }
  return typeMap[type] || type
}

const getStatusText = (status) => {
  const statusMap = {
    'online': '在线',
    'offline': '离线',
    'busy': '占用',
    'available': '可用',
    'error': '错误'
  }
  return statusMap[status] || status
}

const getStatusColor = (status) => {
  const colorMap = {
    'online': 'success',
    'offline': 'info',
    'busy': 'warning',
    'available': 'success',
    'error': 'danger'
  }
  return colorMap[status] || 'info'
}

// 视图切换处理
const handleViewChange = (viewName) => {
  console.log('切换视图:', viewName)
  if (viewName === 'tree') {
    refreshTrigger.value++
  }
}

// 嵌套分组事件处理
const handleNestedGroupCreated = (group) => {
  console.log('嵌套分组创建:', group)
  refreshData()
  refreshTrigger.value++
}

const handleNestedGroupUpdated = (group) => {
  console.log('嵌套分组更新:', group)
  refreshData()
  refreshTrigger.value++
}

const handleNestedGroupDeleted = (group) => {
  console.log('嵌套分组删除:', group)
  refreshData()
  refreshTrigger.value++
}

// 设置WebSocket监听
const setupWebSocketListeners = () => {
  websocketManager.subscribe('device_updates')
  websocketManager.on('device_group_created', handleGroupCreated)
  websocketManager.on('device_groups_batch_created', handleGroupsUpdated)
  websocketManager.on('devices_assigned_to_group', handleDevicesAssigned)
  websocketManager.on('devices_batch_assigned', handleDevicesAssigned)
}

// 清理WebSocket监听
const cleanupWebSocketListeners = () => {
  websocketManager.off('device_group_created', handleGroupCreated)
  websocketManager.off('device_groups_batch_created', handleGroupsUpdated)
  websocketManager.off('devices_assigned_to_group', handleDevicesAssigned)
  websocketManager.off('devices_batch_assigned', handleDevicesAssigned)
  websocketManager.unsubscribe('device_updates')
}

// 生命周期
onMounted(() => {
  refreshData()
  setupWebSocketListeners()
})

onUnmounted(() => {
  cleanupWebSocketListeners()
})
</script>

<style scoped>
.device-group-management {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.group-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.group-icon {
  color: #409eff;
}

.device-count, .user-count {
  font-weight: 600;
  color: #409eff;
}

.text-muted {
  color: #909399;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.view-tabs {
  margin: 20px 0;
}

.view-tabs .el-tabs__header {
  margin-bottom: 0;
}

.list-view,
.tree-view {
  min-height: 400px;
}

.tree-view {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
}
</style>
