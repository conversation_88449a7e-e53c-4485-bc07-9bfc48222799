<template>
  <div class="slave-server-detail">
    <!-- 页面头部 -->
    <div class="detail-header">
      <div class="header-left">
        <el-button @click="goBack" type="text" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
        <div class="server-title">
          <h2>{{ serverDetail.server_info?.name || '从服务器详情' }}</h2>
          <el-tag :type="getStatusColor(serverDetail.server_info?.status)" size="large">
            <el-icon class="status-icon">
              <CircleCheck v-if="isServerOnline" />
              <CircleClose v-else />
            </el-icon>
            {{ getStatusText(serverDetail.server_info) }}
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="refreshData" :loading="loading" type="primary" :disabled="!isServerOnline">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-dropdown @command="handleCommand">
          <el-button type="success">
            管理操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <!-- 在线状态的操作 -->
              <template v-if="isServerOnline">
                <el-dropdown-item command="restart">重启服务器</el-dropdown-item>
                <el-dropdown-item command="restart-vh">重启VirtualHere</el-dropdown-item>
                <el-dropdown-item command="stop-vh">停止VirtualHere</el-dropdown-item>
                <el-dropdown-item command="start-vh">启动VirtualHere</el-dropdown-item>
                <el-dropdown-item divided command="update">更新VirtualHere</el-dropdown-item>
              </template>
              <!-- 离线状态的操作 -->
              <template v-else>
                <el-dropdown-item command="wake-up">唤醒服务器</el-dropdown-item>
                <el-dropdown-item command="force-restart">强制重启</el-dropdown-item>
                <el-dropdown-item divided command="check-connection">检查连接</el-dropdown-item>
                <el-dropdown-item command="remove-offline">移除离线记录</el-dropdown-item>
              </template>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="detail-content" v-loading="loading">
      <!-- 概览卡片 -->
      <div class="overview-cards">
        <el-card class="overview-card" :class="{ 'offline-card': !isServerOnline }">
          <div class="card-content">
            <div class="card-icon cpu">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">
                {{ isServerOnline ? formatPercent(serverDetail.system_info?.cpu?.percent) : '数据缺失' }}
              </div>
              <div class="card-label">CPU使用率</div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card" :class="{ 'offline-card': !isServerOnline }">
          <div class="card-content">
            <div class="card-icon memory">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">
                {{ isServerOnline ? formatPercent(serverDetail.system_info?.memory?.percent) : '数据缺失' }}
              </div>
              <div class="card-label">内存使用率</div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card" :class="{ 'offline-card': !isServerOnline }">
          <div class="card-content">
            <div class="card-icon disk">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">
                {{ isServerOnline ? formatPercent(serverDetail.system_info?.disk?.percent) : '数据缺失' }}
              </div>
              <div class="card-label">磁盘使用率</div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card" :class="{ 'offline-card': !isServerOnline }">
          <div class="card-content">
            <div class="card-icon devices">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">
                {{ isServerOnline ? (serverDetail.usb_info?.count || 0) : '离线状态' }}
              </div>
              <div class="card-label">USB设备</div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 详细信息区域 -->
      <el-row :gutter="20" class="detail-sections">
        <!-- 基础信息 -->
        <el-col :span="12">
          <el-card class="info-card" :class="{ 'offline-card': !isServerOnline }">
            <template #header>
              <div class="card-header">
                <el-icon><InfoFilled /></el-icon>
                <span>基础信息</span>
              </div>
            </template>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">服务器名称：</span>
                <span class="info-value">{{ serverDetail.server_info?.name }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">IP地址：</span>
                <span class="info-value">{{ serverDetail.server_info?.ip_address }}:{{ serverDetail.server_info?.port }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">VH端口：</span>
                <span class="info-value">{{ serverDetail.server_info?.vh_port }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">{{ isServerOnline ? '运行时间：' : '离线前运行时间：' }}</span>
                <span class="info-value">
                  {{ formatUptime(serverDetail.server_info?.uptime) || '未知' }}
                </span>
              </div>
              <div class="info-item">
                <span class="info-label">{{ isServerOnline ? '启动时间：' : '离线前启动时间：' }}</span>
                <span class="info-value">
                  {{ formatTime(serverDetail.server_info?.boot_time) || '未知' }}
                </span>
              </div>
              <div class="info-item">
                <span class="info-label">最后心跳：</span>
                <span class="info-value">{{ formatTime(serverDetail.server_info?.last_seen) }}</span>
              </div>
              <!-- 离线状态下显示离线持续时间 -->
              <div v-if="!isServerOnline" class="info-item">
                <span class="info-label">离线持续时间：</span>
                <span class="info-value">
                  {{ serverDetail.server_info?.offline_duration || '计算中...' }}
                </span>
              </div>
              <!-- 离线状态提示 -->
              <div v-if="!isServerOnline" class="offline-warning">
                <el-alert
                  title="服务器离线"
                  :description="`服务器当前处于离线状态${serverDetail.server_info?.offline_duration ? '，已离线 ' + serverDetail.server_info.offline_duration : ''}。部分功能可能无法使用。`"
                  type="warning"
                  :closable="false"
                  show-icon>
                </el-alert>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- VirtualHere状态 -->
        <el-col :span="12">
          <el-card class="info-card" :class="{ 'offline-card': !isServerOnline }">
            <template #header>
              <div class="card-header">
                <el-icon><Connection /></el-icon>
                <span>VirtualHere状态</span>
              </div>
            </template>
            <div class="info-content">
              <div class="info-item">
                <span class="info-label">运行状态：</span>
                <el-tag v-if="isServerOnline" :type="serverDetail.virtualhere_info?.status === 'running' ? 'success' : 'danger'">
                  {{ serverDetail.virtualhere_info?.status === 'running' ? '运行中' : '已停止' }}
                </el-tag>
                <el-tag v-else type="danger">
                  无法连接
                </el-tag>
              </div>
              <div class="info-item">
                <span class="info-label">监听端口：</span>
                <span class="info-value">
                  {{ isServerOnline ? serverDetail.virtualhere_info?.port : '数据缺失' }}
                </span>
              </div>
              <div class="info-item">
                <span class="info-label">进程ID：</span>
                <span class="info-value">
                  {{ isServerOnline ? (serverDetail.virtualhere_info?.process_id || 'N/A') : '数据缺失' }}
                </span>
              </div>
              <div class="info-item">
                <span class="info-label">软件版本：</span>
                <span class="info-value">
                  {{ isServerOnline ? serverDetail.virtualhere_info?.version : '数据缺失' }}
                </span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- USB设备信息 -->
      <el-row :gutter="20" class="detail-sections">
        <el-col :span="24">
          <el-card class="info-card" :class="{ 'offline-card': !isServerOnline }">
            <template #header>
              <div class="card-header">
                <el-icon><Connection /></el-icon>
                <span>USB设备信息</span>
                <div class="header-stats">
                  <el-tag v-if="isServerOnline" size="small">总计: {{ serverDetail.usb_info?.count || 0 }}</el-tag>
                  <el-tag v-if="isServerOnline" size="small" type="warning">HUB: {{ serverDetail.usb_info?.hub_count || 0 }}</el-tag>
                  <el-tag v-if="isServerOnline" size="small" type="success">设备: {{ serverDetail.usb_info?.device_count || 0 }}</el-tag>
                  <el-tag v-else size="small" type="info">历史记录</el-tag>
                </div>
              </div>
            </template>
            <div class="usb-devices">
              <!-- 离线状态提示 -->
              <div v-if="!isServerOnline" class="offline-device-warning">
                <el-alert
                  title="设备信息不可用"
                  description="从服务器离线，根据历史信息该服务器拥有以下设备。实时设备状态请等待服务器上线后查看。"
                  type="info"
                  :closable="false"
                  show-icon>
                </el-alert>
              </div>

              <el-table :data="serverDetail.usb_info?.devices || []" stripe :class="{ 'offline-table': !isServerOnline }">
                <el-table-column prop="bus" label="总线" width="80" />
                <el-table-column prop="address" label="地址" width="80" />
                <el-table-column prop="vendor_id" label="VID" width="100">
                  <template #default="{ row }">
                    <code>{{ formatHex(row.vendor_id) }}</code>
                  </template>
                </el-table-column>
                <el-table-column prop="product_id" label="PID" width="100">
                  <template #default="{ row }">
                    <code>{{ formatHex(row.product_id) }}</code>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="设备描述" min-width="200" />
                <el-table-column label="设备类型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small" :type="getDeviceTypeColor(row.description)">
                      {{ getDeviceType(row.description) }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getSlaveServerDetail, getSlaveServerSystemDetail } from '@/api/slaveServers.js'
import {
  ArrowLeft,
  Refresh,
  ArrowDown,
  CircleCheck,
  CircleClose,
  Cpu,
  Monitor,
  FolderOpened,
  Connection,
  InfoFilled
} from '@element-plus/icons-vue'

// 路由和状态
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const serverId = route.params.id

// 服务器详情数据
const serverDetail = reactive({
  server_info: {},
  system_info: {},
  virtualhere_info: {},
  usb_info: {},
  timestamp: null
})

// 自动刷新定时器
let refreshTimer = null

// 计算属性
const isServerOnline = computed(() => {
  return serverDetail.server_info?.status === 'online' && serverDetail.server_info?.is_online === true
})

// 方法
const goBack = () => {
  router.push('/device-center')
}

const getStatusColor = (status) => {
  return isServerOnline.value ? 'success' : 'danger'
}

const getStatusText = (serverInfo) => {
  if (!serverInfo) return '未知'

  if (isServerOnline.value) {
    return '在线'
  } else {
    // 离线状态，显示离线时长
    if (serverInfo.offline_duration) {
      return `离线 (${serverInfo.offline_duration})`
    } else {
      return '离线'
    }
  }
}

const formatPercent = (value) => {
  return value ? `${value.toFixed(1)}%` : '0%'
}

const formatUptime = (seconds) => {
  if (!seconds) return 'N/A'
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${days}天 ${hours}小时 ${minutes}分钟`
}

const formatTime = (timeStr) => {
  if (!timeStr) return 'N/A'
  return new Date(timeStr).toLocaleString()
}

const formatHex = (value) => {
  return value ? `0x${value.toString(16).toUpperCase().padStart(4, '0')}` : 'N/A'
}

const getDeviceType = (description) => {
  if (!description) return '未知'
  const desc = description.toLowerCase()
  if (desc.includes('hub')) return 'HUB'
  if (desc.includes('mouse')) return '鼠标'
  if (desc.includes('keyboard')) return '键盘'
  if (desc.includes('storage')) return '存储'
  return '设备'
}

const getDeviceTypeColor = (description) => {
  const type = getDeviceType(description)
  switch (type) {
    case 'HUB': return 'info'
    case '鼠标': return 'success'
    case '键盘': return 'warning'
    case '存储': return 'danger'
    default: return ''
  }
}

// 数据获取
const refreshData = async () => {
  loading.value = true
  try {
    // 获取从服务器基础信息
    const basicResponse = await getSlaveServerDetail(serverId)
    const basicInfo = basicResponse.data

    // 直接使用基础信息，不依赖可能失败的系统详情API
    Object.assign(serverDetail, {
      server_info: {
        id: basicInfo.id,
        server_id: basicInfo.server_id,
        name: basicInfo.name,
        ip_address: basicInfo.ip_address,
        port: basicInfo.port,
        vh_port: basicInfo.vh_port,
        status: basicInfo.status,
        is_online: basicInfo.is_online,
        offline_duration: basicInfo.offline_duration,
        last_seen: basicInfo.last_seen,
        description: basicInfo.description,
        created_at: basicInfo.created_at,
        device_count: basicInfo.device_count,
        hardware_uuid: basicInfo.hardware_uuid
      },
      system_info: {
        cpu_usage: 'N/A',
        memory_usage: 'N/A',
        disk_usage: 'N/A'
      },
      virtualhere_info: {
        status: basicInfo.is_online ? 'running' : 'stopped',
        port: basicInfo.vh_port,
        version: 'N/A'
      },
      usb_info: {
        device_count: basicInfo.device_count || 0,
        devices: []
      },
      timestamp: new Date().toISOString()
    })

    // 调试输出：检查数据结构
    console.log('获取到的基础信息:', basicInfo)
    console.log('合并后的服务器详情:', serverDetail)

    loading.value = false
  } catch (error) {
    console.error('获取服务器详情失败:', error)
    ElMessage.error('获取服务器详情失败')

    // 降级到模拟数据
    Object.assign(serverDetail, {
      server_info: {
        name: 'OmniLink-Slave-Server',
        ip_address: '**********',
        port: 8891,
        vh_port: 7575,
        status: 'online',
        uptime: 86400,
        boot_time: new Date(Date.now() - 86400000).toISOString()
      },
      system_info: {
        cpu: { percent: 15.2 },
        memory: { percent: 45.8 },
        disk: { percent: 23.1 }
      },
      virtualhere_info: {
        status: 'running',
        port: 7575,
        process_id: 1234,
        version: 'VirtualHere USB Server 4.3.3'
      },
      usb_info: {
        count: 2,
        hub_count: 2,
        device_count: 0,
        devices: [
          {
            bus: 1,
            address: 1,
            vendor_id: 7531,
            product_id: 2,
            description: 'USB Device 1d6b:0002'
          },
          {
            bus: 2,
            address: 1,
            vendor_id: 7531,
            product_id: 3,
            description: 'USB Device 1d6b:0003'
          }
        ]
      },
      timestamp: new Date().toISOString()
    })
    loading.value = false
  }
}

const handleCommand = async (command) => {
  try {
    // 如果是更新VirtualHere，直接跳转到管理页面
    if (command === 'update') {
      router.push(`/device-center/slave-server/${serverId}/virtualhere`)
      return
    }

    await ElMessageBox.confirm(
      `确定要执行 "${getCommandText(command)}" 操作吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里调用相应的API
    ElMessage.success(`${getCommandText(command)} 操作已执行`)

    // 刷新数据
    setTimeout(() => {
      refreshData()
    }, 1000)
  } catch {
    // 用户取消操作
  }
}

const getCommandText = (command) => {
  const commandMap = {
    // 在线状态操作
    'restart': '重启服务器',
    'restart-vh': '重启VirtualHere',
    'stop-vh': '停止VirtualHere',
    'start-vh': '启动VirtualHere',
    'update': '更新VirtualHere',
    // 离线状态操作
    'wake-up': '唤醒服务器',
    'force-restart': '强制重启',
    'check-connection': '检查连接',
    'remove-offline': '移除离线记录'
  }
  return commandMap[command] || command
}

// 生命周期
onMounted(() => {
  refreshData()
  // 设置自动刷新（每30秒）
  refreshTimer = setInterval(refreshData, 30000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.slave-server-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  font-size: 14px;
  color: #606266;
}

.server-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.server-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 10px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.overview-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 0;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.card-icon.cpu {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.memory {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.disk {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.devices {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.card-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.detail-sections {
  margin-bottom: 20px;
}

.info-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.header-stats {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
}

.info-value {
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.usb-devices {
  margin-top: 10px;
}

.status-icon {
  margin-right: 5px;
}

.offline-warning {
  margin-top: 15px;
}

.offline-warning .el-alert {
  border-radius: 6px;
}

/* 离线状态样式 */
.offline-card {
  opacity: 0.7;
  background-color: #f5f5f5;
}

.offline-card .card-value {
  color: #909399;
  font-style: italic;
}

.offline-card .info-value {
  color: #909399;
  font-style: italic;
}

.offline-device-warning {
  margin-bottom: 15px;
}

.offline-table {
  opacity: 0.8;
}

.offline-table .el-table__row {
  background-color: #fafafa !important;
}

.offline-table .el-table__row:hover {
  background-color: #f0f0f0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .header-left {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .server-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }
}
</style>
