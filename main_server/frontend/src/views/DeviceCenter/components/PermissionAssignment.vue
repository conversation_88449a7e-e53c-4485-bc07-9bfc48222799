<template>
  <div class="permission-assignment">
    <!-- 操作栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button 
          type="primary" 
          @click="refreshData"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-button
          type="success"
          @click="assignPermissions"
          :disabled="(selectionMode === 'single' ? !selectedTarget : selectedTargets.length === 0) || selectedDevices.length === 0"
          :loading="assigning"
        >
          <el-icon><Check /></el-icon>
          分配权限
        </el-button>
      </div>
    </div>

    <!-- 当前选中状态显示 -->
    <div class="current-selection" v-if="currentSelectedInfo">
      <div class="selection-info">
        <el-icon class="selection-icon"><User /></el-icon>
        <span class="selection-text">
          当前选中：
          <strong>{{ currentSelectedInfo.name }}</strong>
          <el-tag type="info" size="small">
            {{ currentSelectedInfo.type }}
          </el-tag>
          <span v-if="selectionMode === 'multiple' && selectedTargets.length > 0" class="batch-info">
            (共{{ selectedTargetsUserCount }}人)
          </span>
        </span>
      </div>
      <div class="selection-actions">
        <el-button size="small" @click="clearSelection">清除选择</el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：组织架构面板 -->
      <div class="org-panel">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>组织架构</span>
              <div class="header-controls">
                <!-- 🔧 新增：选择模式切换 -->
                <el-radio-group v-model="selectionMode" size="small" @change="handleSelectionModeChange">
                  <el-radio-button value="single">单选</el-radio-button>
                  <el-radio-button value="multiple">多选</el-radio-button>
                </el-radio-group>
                <el-button size="small" @click="expandAll">
                  {{ allExpanded ? '收起全部' : '展开全部' }}
                </el-button>
              </div>
            </div>
          </template>

          <!-- 🔧 新增：多选模式下的批量操作 -->
          <div v-if="selectionMode === 'multiple'" class="batch-controls">
            <el-button size="small" @click="selectAllNodes">全选</el-button>
            <el-button size="small" @click="clearAllSelection">全不选</el-button>
            <el-button size="small" @click="invertSelection">反选</el-button>
            <span class="batch-selection-info">
              已选择 {{ selectedTargets.length }} 个组织
              <span v-if="selectedTargetsUserCount > 0">，共 {{ selectedTargetsUserCount }} 人</span>
            </span>
          </div>
          
          <el-tree
            ref="orgTreeRef"
            :data="organizationTree"
            :props="treeProps"
            node-key="id"
            :default-expanded-keys="defaultExpandedKeys"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            :highlight-current="selectionMode === 'single'"
            :show-checkbox="selectionMode === 'multiple'"
            :check-strictly="true"
            @check="handleNodeCheck"
            :key="treeKey"
            class="org-tree"
            empty-text="暂无组织架构数据"
          >
            <template #default="{ node, data }">
              <div class="tree-node" :class="{
                'is-selected': isNodeSelected(data),
                'is-multi-selected': isNodeMultiSelected(data)
              }" @dblclick="handleNodeDoubleClick(node, data)">
                <div class="node-info">
                  <el-icon class="node-icon">
                    <OfficeBuilding v-if="data.type === 'organization'" />
                    <UserFilled v-else-if="data.type === 'admin_group'" />
                    <User v-else />
                  </el-icon>
                  <span class="node-label">
                    {{ data.name }}
                    <span v-if="data.type === 'organization'" class="user-count">
                      ({{ getTotalUserCount(data) }}人)
                    </span>
                    <span v-else-if="data.type === 'admin_group'" class="user-count">
                      ({{ data.userCount || 0 }}人)
                    </span>
                    <span v-else-if="data.type === 'normal_user_group'" class="user-count">
                      ({{ data.userCount || 0 }}人)
                    </span>
                    <span v-if="data.type === 'organization'" class="org-type">
                      ({{ getLevelName(data.level) }})
                    </span>
                    <span v-else-if="data.type === 'user'" class="user-role">
                      - {{ data.role_name }}
                    </span>
                  </span>
                </div>
              </div>
            </template>
          </el-tree>
        </el-card>
      </div>

      <!-- 右侧：设备分配面板 -->
      <div class="device-panel">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>设备权限分配</span>
            </div>
          </template>

          <div class="device-tabs">
            <el-tabs v-model="activeTab" @tab-change="handleTabChange">
              <!-- 设备分组标签页 -->
              <el-tab-pane label="分组" name="groups">
                <div class="tab-content">
                  <div class="search-bar">
                    <el-input
                      v-model="groupSearchText"
                      placeholder="搜索设备分组..."
                      :prefix-icon="Search"
                      clearable
                      @input="filterGroups"
                    />
                  </div>
                  
                  <div class="device-list" v-loading="groupsLoading">
                    <div 
                      v-for="group in filteredGroups" 
                      :key="group.id"
                      class="device-item"
                      :class="{ 'is-selected': selectedDevices.includes(group.id) }"
                      @click="toggleDeviceSelection(group.id)"
                    >
                      <el-checkbox 
                        :model-value="selectedDevices.includes(group.id)"
                        @change="toggleDeviceSelection(group.id)"
                      />
                      <div class="device-info">
                        <div class="device-name">{{ group.name }}</div>
                        <div class="device-desc">{{ group.description || '无描述' }}</div>
                        <div class="device-meta">
                          <el-tag :type="getGroupTypeColor(group.group_type)" size="small">
                            {{ getGroupTypeName(group.group_type) }}
                          </el-tag>
                          <span class="device-count">{{ group.device_count || 0 }}个设备</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 独立设备标签页 -->
              <el-tab-pane label="设备" name="devices">
                <div class="tab-content">
                  <!-- 设备筛选器 -->
                  <div class="device-filters">
                    <el-row :gutter="12">
                      <el-col :span="8">
                        <el-input
                          v-model="deviceFilters.name"
                          placeholder="设备名称搜索"
                          clearable
                          @input="handleDeviceFilter"
                        >
                          <template #prefix>
                            <el-icon><Search /></el-icon>
                          </template>
                        </el-input>
                      </el-col>
                      <el-col :span="5">
                        <el-select
                          v-model="deviceFilters.type"
                          placeholder="设备类型"
                          clearable
                          @change="handleDeviceFilter"
                        >
                          <el-option label="全部类型" value="" />
                          <el-option
                            v-for="(config, type) in deviceTypeConfig"
                            :key="type"
                            :label="config.label"
                            :value="type"
                          />
                        </el-select>
                      </el-col>
                      <el-col :span="6">
                        <el-select
                          v-model="deviceFilters.server"
                          placeholder="所属服务器"
                          clearable
                          @change="handleDeviceFilter"
                        >
                          <el-option label="全部服务器" value="" />
                          <el-option
                            v-for="server in serverList"
                            :key="server.id"
                            :label="server.name"
                            :value="server.id"
                          />
                        </el-select>
                      </el-col>
                      <el-col :span="5">
                        <el-button type="primary" @click="handleDeviceFilter">
                          <el-icon><Search /></el-icon>
                          搜索
                        </el-button>
                      </el-col>
                    </el-row>
                  </div>

                  <div class="search-bar">
                    <el-input
                      v-model="deviceSearchText"
                      placeholder="搜索USB设备..."
                      :prefix-icon="Search"
                      clearable
                      @input="filterDevices"
                    />
                  </div>
                  
                  <div class="device-list" v-loading="devicesLoading">
                    <div 
                      v-for="device in filteredDevices" 
                      :key="device.id"
                      class="device-item"
                      :class="{ 'is-selected': selectedDevices.includes(device.id) }"
                      @click="toggleDeviceSelection(device.id)"
                    >
                      <el-checkbox 
                        :model-value="selectedDevices.includes(device.id)"
                        @change="toggleDeviceSelection(device.id)"
                      />
                      <div class="device-info">
                        <div class="device-name">{{ device.name || device.device_name }}</div>
                        <div class="device-desc">{{ device.description || '无描述' }}</div>
                        <div class="device-meta">
                          <el-tag :type="getDeviceStatusColor(device.status)" size="small">
                            {{ device.status }}
                          </el-tag>
                          <span class="device-id">VID:{{ device.vid }} PID:{{ device.pid }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 权限分配确认对话框 -->
    <el-dialog
      v-model="assignDialogVisible"
      title="确认权限分配"
      width="500px"
      @close="resetAssignDialog"
    >
      <div class="assign-confirm">
        <p><strong>分配对象：</strong>{{ selectedTarget?.name }}</p>
        <p><strong>分配类型：</strong>{{ activeTab === 'groups' ? '设备分组' : '独立设备' }}</p>
        <p><strong>选中数量：</strong>{{ selectedDevices.length }}个</p>
        <p><strong>权限类型：</strong>使用权限</p>
      </div>
      
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAssignPermissions" :loading="assigning">
          确认分配
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, defineEmits, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  User,
  UserFilled,
  OfficeBuilding,
  Check,
  Search
} from '@element-plus/icons-vue'
import { getDeviceGroups } from '@/api/permission-assignment'
import { useOrgUsersAdapter } from '@/composables/useOrgUsersAdapter.js'
import { usePermissionAudit } from '@/composables/usePermissionAudit.js'
import websocketManager from '@/utils/websocket'
import { useUserStore } from '@/stores/user'

const emit = defineEmits(['stats-update'])
const userStore = useUserStore()

// WebSocket管理器（使用全局实例）
const wsManager = websocketManager

// 🔧 修复：使用与组织与用户管理页面相同的useOrgUsersAdapter
const {
  loading: orgLoading,
  organizationTreeData,
  flatOrganizations,
  organizationsResponse,
  selectedNode: selectedTarget,
  searchText: orgSearchText,
  loadOrganizationsWithUsers: loadOrganizationData,
  refreshData: refreshOrganizationData,
  getLevelName,
  canManageOrganization
} = useOrgUsersAdapter({
  autoLoad: true,
  enableCache: true
})

// 使用权限审计系统
const {
  recordOperation,
  completeOperation,
  canManagePermissions,
  OPERATION_TYPES,
  PERMISSION_TYPES
} = usePermissionAudit()

// 🔧 统一设备类型配置 - 与USB设备管理模块保持一致
const deviceTypeConfig = {
  'ca_lock': {
    label: 'CA锁',
    color: 'danger',
    icon: '🔐',
    description: 'CA证书锁、银行U盾、数字证书设备'
  },
  'encryption_key': {
    label: '加密锁',
    color: 'warning',
    icon: '🔒',
    description: '软件加密锁、硬件加密锁设备'
  },
  'bank_ukey': {
    label: '银行U盾',
    color: 'primary',
    icon: '🏦',
    description: '银行数字证书U盾设备'
  },
  'financial_lock': {
    label: '财务锁',
    color: 'warning',
    icon: '💰',
    description: '用友、金蝶等财务软件加密锁'
  },
  'cost_lock': {
    label: '造价加密锁',
    color: 'primary',
    icon: '🏗️',
    description: '广联达、新点等造价软件加密锁'
  },
  'other_lock': {
    label: '其他加密锁',
    color: 'danger',
    icon: '🔒',
    description: '其他类型的加密锁设备'
  },
  'video_device': {
    label: '视频设备',
    color: 'success',
    icon: '📹',
    description: '摄像头、视频采集设备'
  },
  'audio_device': {
    label: '音频设备',
    color: 'info',
    icon: '🎵',
    description: '麦克风、音频设备'
  },
  'storage': {
    label: '存储设备',
    color: 'warning',
    icon: '💾',
    description: 'U盘、移动硬盘等存储设备'
  },
  'input': {
    label: '输入设备',
    color: '',
    icon: '⌨️',
    description: '键盘、鼠标等输入设备'
  },
  'communication': {
    label: '通信设备',
    color: '',
    icon: '📡',
    description: '网络适配器等通信设备'
  },
  'hardware': {
    label: '硬件设备',
    color: '',
    icon: '🔧',
    description: '其他硬件设备'
  },
  'unknown': {
    label: '未知设备',
    color: '',
    icon: '❓',
    description: '未识别的设备类型'
  }
}

// 其他响应式数据
const loading = ref(false)
const assigning = ref(false)
const groupsLoading = ref(false)
const devicesLoading = ref(false)
const allExpanded = ref(false)
const assignDialogVisible = ref(false)

// 🔧 新增：选择模式管理
const selectionMode = ref('single') // 'single' | 'multiple'
const selectedTargets = ref([]) // 多选模式下的选中节点数组

// 组织架构相关
const orgTreeRef = ref()
const treeKey = ref(0) // 用于强制重新渲染树组件

// 设备相关
const activeTab = ref('groups')
const deviceGroups = ref([])
const devices = ref([])
const selectedDevices = ref([])

// 搜索相关
const groupSearchText = ref('')
const deviceSearchText = ref('')

// 设备筛选器
const deviceFilters = reactive({
  name: '',
  type: '',
  server: '',
  group: ''
})

const serverList = ref([])
const deviceGroupList = ref([])

// 🔧 新增：过滤"新注册用户"层级的功能（设备管理中心专用）
const filterNewUserOrganizations = (nodes) => {
  if (!nodes || !Array.isArray(nodes)) return []

  return nodes.filter(node => {
    // 过滤掉"新注册用户"组织
    if (node.name === '新注册用户') {
      console.log('🔧 PermissionAssignment - 过滤掉"新注册用户"组织:', node.name)
      return false
    }
    return true
  }).map(node => {
    // 创建新对象，避免修改原始数据
    const newNode = { ...node }

    // 递归过滤子节点
    if (node.children && node.children.length > 0) {
      newNode.children = filterNewUserOrganizations(node.children)
    }

    return newNode
  })
}

// 🔧 新增：为组织节点添加管理员和用户分类子节点
const addUserCategoryNodes = (nodes) => {
  if (!Array.isArray(nodes)) return []

  return nodes.map(node => {
    const newNode = { ...node }

    if (node.type === 'organization' && node.users && Array.isArray(node.users)) {
      // 分类用户
      const adminUsers = node.users.filter(user =>
        user.role_name && user.role_name.includes('管理员')
      )
      const normalUsers = node.users.filter(user =>
        !user.role_name || !user.role_name.includes('管理员')
      )

      // 创建分类子节点
      const categoryChildren = []

      // 添加管理员分类节点
      if (adminUsers.length > 0) {
        categoryChildren.push({
          id: `${node.id}_admin_group`,
          name: '管理员',
          type: 'admin_group',
          userCount: adminUsers.length,
          users: adminUsers,
          children: adminUsers.map(user => ({
            ...user,
            id: `${node.id}_admin_${user.id}`,
            type: 'user'
          }))
        })
      }

      // 添加用户分类节点
      if (normalUsers.length > 0) {
        categoryChildren.push({
          id: `${node.id}_user_group`,
          name: '用户',
          type: 'normal_user_group',
          userCount: normalUsers.length,
          users: normalUsers,
          children: normalUsers.map(user => ({
            ...user,
            id: `${node.id}_user_${user.id}`,
            type: 'user'
          }))
        })
      }

      // 合并原有子组织和新的分类节点
      const orgChildren = node.children ? addUserCategoryNodes(node.children) : []
      newNode.children = [...orgChildren, ...categoryChildren]
    } else if (node.children) {
      // 递归处理子节点
      newNode.children = addUserCategoryNodes(node.children)
    }

    return newNode
  })
}

// 🔧 修复：统一组织架构数据处理，与组织与用户管理页面保持一致
const organizationTree = computed(() => {
  console.log('🔍 PermissionAssignment - organizationTree computed')
  console.log('🔍 PermissionAssignment - organizationTreeData.value:', organizationTreeData.value)

  // 应用"新注册用户"过滤（设备管理中心专用）
  let filteredData = filterNewUserOrganizations(organizationTreeData.value)

  // 添加用户分类子节点
  filteredData = addUserCategoryNodes(filteredData)

  console.log('🔍 PermissionAssignment - 过滤后数据:', filteredData)
  console.log('🔍 PermissionAssignment - 过滤后数据长度:', filteredData.length)

  return filteredData
})

// 默认展开的节点
const defaultExpandedKeys = computed(() => {
  const keys = []
  const collectExpandedKeys = (nodes, currentLevel = 0) => {
    if (!Array.isArray(nodes)) return

    nodes.forEach(node => {
      // 展开前3级组织
      if (currentLevel <= 2 && node.type === 'organization') {
        keys.push(node.id)

        // 递归处理子节点
        if (node.children && node.children.length > 0) {
          collectExpandedKeys(node.children, currentLevel + 1)
        }
      }
    })
  }

  collectExpandedKeys(organizationTree.value)
  return keys
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 🔧 新增：多选相关计算属性
const selectedTargetsUserCount = computed(() => {
  return selectedTargets.value.reduce((total, target) => {
    return total + (target.userCount || 0)
  }, 0)
})

const currentSelectedInfo = computed(() => {
  if (selectionMode.value === 'single') {
    return selectedTarget.value ? {
      name: selectedTarget.value.name,
      type: selectedTarget.value.type === 'organization' ? getLevelName(selectedTarget.value.level) : '用户',
      count: 1
    } : null
  } else {
    return selectedTargets.value.length > 0 ? {
      name: `${selectedTargets.value.length} 个组织`,
      type: '批量选择',
      count: selectedTargets.value.length
    } : null
  }
})

// 计算属性
const filteredGroups = computed(() => {
  if (!groupSearchText.value) return deviceGroups.value
  return deviceGroups.value.filter(group =>
    group.name.toLowerCase().includes(groupSearchText.value.toLowerCase()) ||
    (group.description && group.description.toLowerCase().includes(groupSearchText.value.toLowerCase()))
  )
})

const filteredDevices = computed(() => {
  if (!deviceSearchText.value) return devices.value
  return devices.value.filter(device => 
    (device.name || device.device_name || '').toLowerCase().includes(deviceSearchText.value.toLowerCase())
  )
})

// 方法
const getGroupTypeColor = (type) => {
  switch (type) {
    case 'server': return 'success'
    case 'mixed': return 'warning'
    case 'single': return 'info'
    default: return ''
  }
}

const getGroupTypeName = (type) => {
  switch (type) {
    case 'server': return '服务器分组'
    case 'mixed': return '混合分组'
    case 'single': return '单设备分组'
    default: return '未知类型'
  }
}

const getDeviceStatusColor = (status) => {
  switch (status) {
    case '在线': return 'success'
    case '离线': return 'danger'
    case '占用': return 'warning'
    default: return 'info'
  }
}

const isNodeSelected = (data) => {
  return selectedTarget.value && selectedTarget.value.id === data.id && selectedTarget.value.type === data.type
}

// 🔧 新增：多选模式下的节点选中状态检查
const isNodeMultiSelected = (data) => {
  return selectedTargets.value.some(target => target.id === data.id && target.type === data.type)
}

const handleNodeClick = (data) => {
  if (selectionMode.value === 'single') {
    console.log('🔧 PermissionAssignment - 单选节点点击:', data)
    selectedTarget.value = data
    selectedDevices.value = []
    ElMessage.info(`已选择：${data.name}`)
  }
  // 多选模式下点击由复选框处理，不需要在这里处理
}

// 🔧 新增：多选模式下的复选框变化处理
const handleNodeCheck = (data, checked) => {
  console.log('🔧 PermissionAssignment - 节点复选框变化:', data, checked)

  if (checked.checkedNodes) {
    // Element Plus的check事件返回的是包含所有选中节点的对象
    selectedTargets.value = checked.checkedNodes.filter(node => node.type === 'organization')
    console.log('🔧 PermissionAssignment - 更新多选列表:', selectedTargets.value)

    if (selectedTargets.value.length > 0) {
      ElMessage.info(`已选择 ${selectedTargets.value.length} 个组织`)
    }
  }
}

const clearSelection = () => {
  // 🔧 修改：支持单选和多选模式的清除
  if (selectionMode.value === 'single') {
    selectedTarget.value = null
  } else {
    selectedTargets.value = []
    if (orgTreeRef.value) {
      orgTreeRef.value.setCheckedKeys([])
    }
  }
  selectedDevices.value = []
  ElMessage.info('已清除所有选择')
}

// 🔧 新增：选择模式切换处理
const handleSelectionModeChange = (mode) => {
  console.log('🔧 PermissionAssignment - 选择模式切换:', mode)

  // 清空所有选择
  selectedTarget.value = null
  selectedTargets.value = []
  selectedDevices.value = []

  // 强制重新渲染树组件
  treeKey.value++

  ElMessage.info(`已切换到${mode === 'single' ? '单选' : '多选'}模式`)
}

// 🔧 新增：批量操作方法
const selectAllNodes = () => {
  if (!orgTreeRef.value) return

  // 获取所有组织节点
  const allOrgNodes = []
  const collectOrgNodes = (nodes) => {
    nodes.forEach(node => {
      if (node.type === 'organization') {
        allOrgNodes.push(node)
      }
      if (node.children) {
        collectOrgNodes(node.children)
      }
    })
  }

  collectOrgNodes(organizationTree.value)

  // 设置所有节点为选中状态
  const nodeKeys = allOrgNodes.map(node => node.id)
  orgTreeRef.value.setCheckedKeys(nodeKeys)
  selectedTargets.value = [...allOrgNodes]

  ElMessage.success(`已选择所有 ${allOrgNodes.length} 个组织`)
}

const clearAllSelection = () => {
  if (!orgTreeRef.value) return

  orgTreeRef.value.setCheckedKeys([])
  selectedTargets.value = []

  ElMessage.info('已清空所有选择')
}

const invertSelection = () => {
  if (!orgTreeRef.value) return

  // 获取所有组织节点
  const allOrgNodes = []
  const collectOrgNodes = (nodes) => {
    nodes.forEach(node => {
      if (node.type === 'organization') {
        allOrgNodes.push(node)
      }
      if (node.children) {
        collectOrgNodes(node.children)
      }
    })
  }

  collectOrgNodes(organizationTree.value)

  // 获取当前选中的节点ID
  const currentSelectedIds = selectedTargets.value.map(target => target.id)

  // 反选：选中未选中的，取消选中已选中的
  const newSelectedNodes = allOrgNodes.filter(node => !currentSelectedIds.includes(node.id))
  const newSelectedKeys = newSelectedNodes.map(node => node.id)

  orgTreeRef.value.setCheckedKeys(newSelectedKeys)
  selectedTargets.value = [...newSelectedNodes]

  ElMessage.info(`反选完成，当前选择 ${newSelectedNodes.length} 个组织`)
}

// 🔧 新增：计算组织的总人员数量（包含子组织）
const getTotalUserCount = (orgData) => {
  if (!orgData) return 0

  let totalCount = 0

  // 计算当前组织的用户数量
  if (orgData.users && Array.isArray(orgData.users)) {
    totalCount += orgData.users.length
  }

  // 递归计算子组织的用户数量
  if (orgData.children && Array.isArray(orgData.children)) {
    orgData.children.forEach(child => {
      if (child.type === 'organization') {
        totalCount += getTotalUserCount(child)
      }
    })
  }

  return totalCount
}

// 🔧 新增：双击节点处理函数
let clickTimer = null
const handleNodeDoubleClick = (node, data) => {
  // 清除单击定时器，防止双击时触发单击事件
  if (clickTimer) {
    clearTimeout(clickTimer)
    clickTimer = null
  }

  // 只对可展开的节点类型处理双击事件
  const expandableTypes = ['organization', 'admin_group', 'normal_user_group']
  if (!expandableTypes.includes(data.type)) {
    return
  }

  console.log(`🖱️ 双击节点: ${data.name} (类型: ${data.type})`)

  // 切换节点展开状态
  if (orgTreeRef.value) {
    const nodeKey = data.id
    const isExpanded = orgTreeRef.value.store.nodesMap[nodeKey]?.expanded

    if (isExpanded) {
      orgTreeRef.value.store.nodesMap[nodeKey].collapse()
    } else {
      orgTreeRef.value.store.nodesMap[nodeKey].expand()
    }
  }
}

const expandAll = () => {
  allExpanded.value = !allExpanded.value
  console.log('🔧 PermissionAssignment - 切换展开状态:', allExpanded.value)

  nextTick(() => {
    if (orgTreeRef.value) {
      if (allExpanded.value) {
        console.log('🔧 PermissionAssignment - 展开所有节点')
        orgTreeRef.value.expandAll()
      } else {
        console.log('🔧 PermissionAssignment - 收起所有节点')
        orgTreeRef.value.collapseAll()
      }
    } else {
      console.warn('⚠️ PermissionAssignment - orgTreeRef 未找到')
    }
  })
}

const handleTabChange = (tabName) => {
  selectedDevices.value = []
}

const toggleDeviceSelection = (deviceId) => {
  const index = selectedDevices.value.indexOf(deviceId)
  if (index > -1) {
    selectedDevices.value.splice(index, 1)
  } else {
    selectedDevices.value.push(deviceId)
  }
}

const filterGroups = () => {
  // 搜索过滤逻辑已在计算属性中实现
}

const filterDevices = () => {
  // 搜索过滤逻辑已在计算属性中实现
}

// 🔧 优化：设备筛选处理，支持统一的设备类型配置
const handleDeviceFilter = () => {
  let filtered = devices.value

  // 按名称筛选
  if (deviceFilters.name) {
    filtered = filtered.filter(device =>
      (device.name || device.device_name || '').toLowerCase().includes(deviceFilters.name.toLowerCase())
    )
  }

  // 按类型筛选 - 使用统一的设备类型配置
  if (deviceFilters.type) {
    filtered = filtered.filter(device => {
      const deviceType = getFinalDeviceType(device)
      return deviceType === deviceFilters.type
    })
  }

  // 按服务器筛选
  if (deviceFilters.server) {
    filtered = filtered.filter(device => device.slave_server_id === deviceFilters.server)
  }

  filteredDevices.value = filtered
}

// 🔧 新增：获取设备的最终类型（与USB设备管理模块保持一致）
const getFinalDeviceType = (device) => {
  // 使用后端返回的最终设备类型
  if (device.final_device_type) {
    return mapDeviceType(device.final_device_type, device)
  }

  // 兜底：使用原始设备类型映射
  return mapDeviceType(device.device_type || 'unknown', device)
}

// 🔧 新增：设备类型映射函数
const mapDeviceType = (backendType, device = null) => {
  // 如果提供了设备对象，优先使用USB.IDS增强识别信息
  if (device && device.identification_source === 'usb_ids_enhanced') {
    return 'encryption_key'  // USB.IDS增强识别的设备显示为加密锁
  }

  // 使用精准分类映射
  const typeMapping = {
    'ca_lock': 'ca_lock',
    'encryption_key': 'encryption_key',
    'bank_ukey': 'bank_ukey',
    'financial_lock': 'financial_lock',
    'cost_lock': 'cost_lock',
    'other_lock': 'other_lock',
    'video_device': 'video_device',
    'audio_device': 'audio_device',
    'storage': 'storage',
    'input': 'input',
    'communication': 'communication',
    'hardware': 'hardware',
    'unknown': 'unknown'
  }

  return typeMapping[backendType] || 'unknown'
}

const assignPermissions = () => {
  // 🔧 修改：支持单选和多选模式
  const hasSelection = selectionMode.value === 'single'
    ? selectedTarget.value
    : selectedTargets.value.length > 0

  if (!hasSelection) {
    ElMessage.warning(`请先选择${selectionMode.value === 'single' ? '组织或用户' : '要分配权限的组织'}`)
    return
  }

  if (selectedDevices.value.length === 0) {
    ElMessage.warning('请选择要分配的设备或分组')
    return
  }

  assignDialogVisible.value = true
}

const confirmAssignPermissions = async () => {
  try {
    assigning.value = true

    // 🔧 修改：支持批量权限分配
    const targets = selectionMode.value === 'single'
      ? [selectedTarget.value]
      : selectedTargets.value

    console.log('🔧 PermissionAssignment - 开始权限分配:', {
      模式: selectionMode.value,
      目标数量: targets.length,
      设备数量: selectedDevices.value.length,
      目标列表: targets.map(t => t.name)
    })

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const message = selectionMode.value === 'single'
      ? `成功为 ${targets[0].name} 分配 ${selectedDevices.value.length} 个设备权限`
      : `成功为 ${targets.length} 个组织批量分配 ${selectedDevices.value.length} 个设备权限`

    ElMessage.success(message)
    assignDialogVisible.value = false
    selectedDevices.value = []

    // 更新统计数据
    updateStats()

  } catch (error) {
    console.error('权限分配失败:', error)
    ElMessage.error('权限分配失败')
  } finally {
    assigning.value = false
  }
}

const resetAssignDialog = () => {
  assignDialogVisible.value = false
}

const refreshData = async () => {
  loading.value = true
  try {
    console.log('🔧 PermissionAssignment - 开始刷新数据')

    await Promise.all([
      loadOrganizationData(), // 使用新的加载方法
      loadDeviceGroups(),
      loadDevices(),
      loadServerList(), // 🔧 新增：刷新服务器列表
      loadDeviceGroupList() // 🔧 新增：刷新设备分组列表
    ])

    // 强制重新渲染树组件
    treeKey.value++
    console.log('🔧 PermissionAssignment - 数据刷新完成，强制重新渲染，treeKey:', treeKey.value)

    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    loading.value = false
  }
}

// loadOrganizationTree 函数已由 useOrganizationTree Composable 替代

const loadDeviceGroups = async () => {
  try {
    groupsLoading.value = true
    const response = await getDeviceGroups()
    deviceGroups.value = response.data || []
  } catch (error) {
    console.error('加载设备分组失败:', error)
    ElMessage.error('加载设备分组失败')
  } finally {
    groupsLoading.value = false
  }
}

const loadDevices = async () => {
  try {
    devicesLoading.value = true

    // 调用真实API获取设备列表
    const response = await fetch('/api/v1/devices', {
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('获取到的设备数据:', data)

    // 正确解析API返回的数据格式
    const deviceList = data.devices || data || []

    // 转换数据格式以匹配前端需求
    devices.value = deviceList.map(device => ({
      id: device.id,
      name: device.custom_name || device.device_name,
      status: getDeviceStatusText(device.status),
      vid: device.vendor_id,
      pid: device.product_id,
      description: device.description || device.device_notes || '无描述',
      device_type: device.device_type,
      slave_server_name: device.slave_server?.name || 'Unknown Server',
      physical_port: device.physical_port,
      is_shared: device.is_shared,
      is_virtual: device.is_virtual
    }))

    console.log('处理后的设备数据:', devices.value)

  } catch (error) {
    console.error('加载设备列表失败:', error)
    ElMessage.error(`加载设备列表失败: ${error.message}`)
    devices.value = []
  } finally {
    devicesLoading.value = false
  }
}

// 加载服务器列表
const loadServerList = async () => {
  try {
    const response = await fetch('/api/v1/slave/list', {
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      serverList.value = data.servers || []
    }
  } catch (error) {
    console.error('加载服务器列表失败:', error)
  }
}

// 加载设备分组列表
const loadDeviceGroupList = async () => {
  try {
    const response = await fetch('/api/v1/device-groups/', {
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      deviceGroupList.value = data || []
    }
  } catch (error) {
    console.error('加载设备分组列表失败:', error)
  }
}

// 设备状态文本转换
const getDeviceStatusText = (status) => {
  const statusMap = {
    'online': '在线',
    'offline': '离线',
    'busy': '占用',
    'available': '可用',
    'error': '错误',
    'maintenance': '维护中'
  }
  return statusMap[status] || status || '未知'
}

const updateStats = () => {
  // 基于真实数据计算权限分配统计
  const stats = {
    total: devices.value.length,
    users: selectedTarget.value?.type === 'user' ? 1 : 0,
    orgs: selectedTarget.value?.type === 'organization' ? 1 : 0
  }
  emit('stats-update', stats)
}

// WebSocket设备状态更新处理
const handleDeviceStatusUpdate = (message) => {
  console.log('设备状态更新:', message)

  // 更新设备列表中对应设备的状态
  const deviceIndex = devices.value.findIndex(d => d.id === message.device_id)
  if (deviceIndex !== -1) {
    devices.value[deviceIndex].status = getDeviceStatusText(message.new_status)

    ElMessage.info(`设备状态更新: ${devices.value[deviceIndex].name} -> ${devices.value[deviceIndex].status}`)

    // 更新统计信息
    updateStats()
  }
}

// 设置WebSocket连接
const setupWebSocket = () => {
  // 订阅设备更新事件
  wsManager.subscribe('device_updates')

  // 监听设备状态变化
  wsManager.on('device_status_update', handleDeviceStatusUpdate)

  // 连接WebSocket
  const token = userStore.token
  if (token) {
    wsManager.connect(token)
  }
}

// 清理WebSocket连接
const cleanupWebSocket = () => {
  wsManager.off('device_status_update', handleDeviceStatusUpdate)
  wsManager.unsubscribe('device_updates')
}

// 生命周期管理
onMounted(() => {
  setupWebSocket()
})

onUnmounted(() => {
  cleanupWebSocket()
})

// 暴露方法给父组件
defineExpose({
  refreshData
})

// 数据监听器
watch(organizationTreeData, (newData, oldData) => {
  console.log('🔍 PermissionAssignment - organizationTreeData 数据变化:', {
    新数据: newData,
    新数据长度: newData?.length || 0,
    旧数据长度: oldData?.length || 0,
    数据类型: typeof newData,
    是否数组: Array.isArray(newData)
  })

  // 强制重新渲染树组件
  if (newData && newData.length > 0) {
    treeKey.value++
    console.log('🔧 PermissionAssignment - 强制重新渲染树组件, treeKey:', treeKey.value)

    // 确保DOM更新后再设置展开状态
    nextTick(() => {
      if (defaultExpandedKeys.value && defaultExpandedKeys.value.length > 0) {
        console.log('🔧 PermissionAssignment - 设置默认展开节点:', defaultExpandedKeys.value)
      }
    })
  }
}, { immediate: true, deep: true })

watch(organizationTree, (newData) => {
  console.log('🔍 PermissionAssignment - organizationTree computed 变化:', newData)
}, { immediate: true })

watch(defaultExpandedKeys, (newKeys) => {
  console.log('🔍 PermissionAssignment - defaultExpandedKeys 变化:', newKeys)
}, { immediate: true })

// 生命周期
onMounted(() => {
  console.log('🔧 PermissionAssignment - 组件挂载完成')
  console.log('🔍 PermissionAssignment - 初始 organizationTreeData:', organizationTreeData.value)
  console.log('🔍 PermissionAssignment - 初始 organizationTree:', organizationTree.value)
  console.log('🔍 PermissionAssignment - 初始 defaultExpandedKeys:', defaultExpandedKeys.value)

  // 组织架构数据由 useOrgUsersAdapter 自动加载
  loadDeviceGroups()
  loadDevices()
  loadServerList()
  loadDeviceGroupList()
})
</script>

<style scoped>
.permission-assignment {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* 🔧 修复：优化选中状态显示的颜色对比度和布局 */
.current-selection {
  display: flex;
  justify-content: flex-start; /* 改为左对齐 */
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #f8fafc; /* 使用浅色背景 */
  border: 1px solid #e2e8f0;
  border-left: 4px solid #3b82f6; /* 添加左侧蓝色边框作为视觉标识 */
  color: #1e293b; /* 使用深色文字确保对比度 */
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 60px; /* 确保足够的高度 */
}

/* 🔧 修复：优化选中信息的布局和文字样式 */
.selection-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1; /* 占据剩余空间 */
  min-width: 0; /* 允许内容收缩 */
}

.selection-icon {
  font-size: 20px;
  color: #3b82f6; /* 使用蓝色图标 */
  flex-shrink: 0; /* 图标不收缩 */
}

.selection-text {
  font-size: 16px;
  line-height: 1.5;
  color: #1e293b; /* 确保文字颜色对比度 */
  font-weight: 500;
  display: flex;
  align-items: center;
  flex-wrap: wrap; /* 允许换行 */
  gap: 8px;
}

/* 🔧 新增：选择操作按钮样式 */
.selection-actions {
  flex-shrink: 0; /* 按钮不收缩 */
  margin-left: 16px;
}

.main-content {
  display: flex;
  gap: 20px;
  height: 600px;
}

.org-panel {
  width: 400px;
  flex-shrink: 0;
}

.org-panel .el-card {
  height: 100%;
}

.device-panel {
  flex: 1;
}

.device-panel .el-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.org-tree {
  max-height: 500px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.tree-node:hover {
  background-color: #f5f7fa;
}

.tree-node.is-selected {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.node-icon {
  color: #409eff;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.org-type, .user-count, .user-role {
  color: #909399;
  font-size: 12px;
}

.device-tabs {
  height: 500px;
}

.device-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow: hidden;
}

.device-tabs :deep(.el-tab-pane) {
  height: 100%;
  overflow: hidden;
}

.tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-bar {
  margin-bottom: 16px;
}

.device-filters {
  margin-bottom: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.device-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: calc(100% - 120px);
  padding-right: 8px;
}

/* 🔧 新增：优化滚动条样式 */
.device-list::-webkit-scrollbar {
  width: 6px;
}

.device-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.device-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.device-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.device-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.device-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.device-item.is-selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.device-info {
  flex: 1;
}

.device-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.device-desc {
  color: #606266;
  font-size: 12px;
  margin-bottom: 8px;
}

.device-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.device-count, .device-id {
  color: #909399;
  font-size: 12px;
}

.assign-confirm p {
  margin: 8px 0;
  color: #606266;
}

/* 🔧 新增：多选模式相关样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.batch-controls {
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 6px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 🔧 修复：重命名并优化批量选择信息样式 */
.batch-selection-info {
  color: #0369a1;
  font-size: 14px;
  font-weight: 500;
  margin-left: auto;
  white-space: nowrap; /* 防止文字换行 */
}

/* 🔧 修复：优化批量信息的文字样式 */
.batch-info {
  color: #475569; /* 提高对比度 */
  font-size: 14px; /* 增大字体 */
  font-weight: 400;
  margin-left: 8px;
  white-space: nowrap; /* 防止文字换行 */
}

.tree-node.is-multi-selected {
  background-color: #e0f2fe;
  border-radius: 4px;
}

/* 🔧 新增：响应式设计 */
@media (max-width: 768px) {
  .current-selection {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 16px;
  }

  .selection-info {
    width: 100%;
  }

  .selection-text {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .selection-actions {
    margin-left: 0;
    align-self: flex-end;
  }

  .batch-controls {
    flex-wrap: wrap;
    gap: 8px;
  }

  .batch-selection-info {
    margin-left: 0;
    margin-top: 8px;
    width: 100%;
  }
}

/* 🔧 新增：确保文字不被截断 */
.selection-text strong {
  color: #1e293b;
  font-weight: 600;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.selection-text .el-tag {
  flex-shrink: 0;
}
</style>
