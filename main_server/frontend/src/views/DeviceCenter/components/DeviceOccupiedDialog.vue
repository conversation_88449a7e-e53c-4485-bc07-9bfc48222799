<template>
  <el-dialog
    v-model="visible"
    title="设备占用信息"
    width="500px"
    :before-close="handleClose"
  >
    <div class="occupied-dialog-content" v-loading="loading">
      <!-- 设备基础信息 -->
      <div class="device-info">
        <div class="device-header">
          <el-icon class="device-icon"><Monitor /></el-icon>
          <div class="device-details">
            <div class="device-name">{{ deviceInfo.custom_name || deviceInfo.device_name }}</div>
            <div class="device-id">设备ID: {{ deviceInfo.device_id }}</div>
          </div>
          <el-tag type="warning" size="large">
            <el-icon><Lock /></el-icon>
            被占用
          </el-tag>
        </div>
      </div>

      <!-- 占用用户信息 -->
      <div class="occupier-info">
        <div class="section-title">
          <el-icon><User /></el-icon>
          <span>占用用户信息</span>
        </div>
        <div class="user-card">
          <div class="user-avatar">
            <el-icon><User /></el-icon>
          </div>
          <div class="user-details">
            <div class="user-name">{{ occupiedInfo.user_name || 'N/A' }}</div>
            <div class="user-contact">
              <el-icon><Phone /></el-icon>
              <span>{{ occupiedInfo.user_contact || 'N/A' }}</span>
            </div>
            <div class="user-email" v-if="occupiedInfo.user_email">
              <el-icon><Message /></el-icon>
              <span>{{ occupiedInfo.user_email }}</span>
            </div>
          </div>
          <div class="contact-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click="contactUser('phone')"
              :disabled="!occupiedInfo.user_contact"
            >
              <el-icon><Phone /></el-icon>
              拨打电话
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click="contactUser('message')"
              :disabled="!occupiedInfo.user_contact"
            >
              <el-icon><ChatDotRound /></el-icon>
              发送消息
            </el-button>
          </div>
        </div>
      </div>

      <!-- 占用时间信息 -->
      <div class="time-info">
        <div class="section-title">
          <el-icon><Clock /></el-icon>
          <span>占用时间信息</span>
        </div>
        <div class="time-details">
          <div class="time-item">
            <span class="time-label">开始时间：</span>
            <span class="time-value">{{ formatTime(occupiedInfo.start_time) }}</span>
          </div>
          <div class="time-item">
            <span class="time-label">持续时间：</span>
            <span class="time-value">{{ formatDuration(occupiedInfo.duration) }}</span>
          </div>
          <div class="time-item">
            <span class="time-label">预计结束：</span>
            <span class="time-value">{{ formatTime(occupiedInfo.estimated_end_time) || '未设置' }}</span>
          </div>
        </div>
      </div>

      <!-- 占用说明 -->
      <div class="occupation-note" v-if="occupiedInfo.note">
        <div class="section-title">
          <el-icon><Document /></el-icon>
          <span>占用说明</span>
        </div>
        <div class="note-content">
          {{ occupiedInfo.note }}
        </div>
      </div>

      <!-- 操作建议 -->
      <div class="action-suggestions">
        <div class="section-title">
          <el-icon><Bell /></el-icon>
          <span>操作建议</span>
        </div>
        <div class="suggestions-list">
          <div class="suggestion-item">
            <el-icon class="suggestion-icon"><Phone /></el-icon>
            <span>联系占用用户协商使用时间</span>
          </div>
          <div class="suggestion-item">
            <el-icon class="suggestion-icon"><Clock /></el-icon>
            <span>等待设备自动释放（如有预计结束时间）</span>
          </div>
          <div class="suggestion-item">
            <el-icon class="suggestion-icon"><Bell /></el-icon>
            <span>发送释放请求通知</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          type="warning" 
          @click="requestRelease"
          :loading="requestLoading"
        >
          <el-icon><Bell /></el-icon>
          请求释放
        </el-button>
        <el-button 
          type="primary" 
          @click="contactUser('phone')"
          :disabled="!occupiedInfo.user_contact"
        >
          <el-icon><Phone /></el-icon>
          联系用户
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineProps, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import {
  Monitor,
  Lock,
  User,
  Phone,
  Message,
  ChatDotRound,
  Clock,
  Document,
  Bell
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  deviceId: {
    type: [String, Number],
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'release-requested'])

// 响应式数据
const userStore = useUserStore()
const loading = ref(false)
const requestLoading = ref(false)
const visible = ref(false)

// 设备信息
const deviceInfo = reactive({
  device_id: '',
  device_name: '',
  custom_name: '',
  device_type: 'unknown'
})

// 占用信息
const occupiedInfo = reactive({
  user_name: '',
  user_contact: '',
  user_email: '',
  start_time: null,
  duration: 0,
  estimated_end_time: null,
  note: ''
})

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.deviceId) {
    loadOccupiedInfo()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const loadOccupiedInfo = async () => {
  loading.value = true
  try {
    // 获取设备详情
    const deviceResponse = await fetch(`/api/v1/devices/${props.deviceId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!deviceResponse.ok) {
      throw new Error(`获取设备信息失败: HTTP ${deviceResponse.status}`)
    }

    const deviceData = await deviceResponse.json()

    // 更新设备信息
    Object.assign(deviceInfo, {
      device_id: deviceData.device_id,
      device_name: deviceData.device_name,
      custom_name: deviceData.custom_name,
      device_type: deviceData.device_type
    })

    // 如果设备被占用，获取占用信息
    if (deviceData.status === 'occupied') {
      const occupationResponse = await fetch(`/api/v1/devices/${props.deviceId}/occupation`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${userStore.token}`,
          'Content-Type': 'application/json'
        }
      })

      if (occupationResponse.ok) {
        const occupationData = await occupationResponse.json()

        Object.assign(occupiedInfo, {
          user_name: occupationData.user_name,
          user_contact: occupationData.user_contact,
          user_email: occupationData.user_email,
          start_time: occupationData.start_time,
          duration: occupationData.current_duration,
          estimated_end_time: occupationData.estimated_end_time,
          note: occupationData.note
        })
      } else {
        // 使用设备数据中的占用信息
        Object.assign(occupiedInfo, {
          user_name: deviceData.current_user_name,
          user_contact: deviceData.current_user_contact,
          user_email: '',
          start_time: deviceData.occupied_start_time,
          duration: deviceData.occupied_duration,
          estimated_end_time: deviceData.estimated_end_time,
          note: deviceData.occupation_note
        })
      }
    } else {
      throw new Error('设备未被占用')
    }

  } catch (error) {
    console.error('获取设备占用信息失败:', error)
    ElMessage.error(`获取设备占用信息失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

const formatTime = (timeStr) => {
  if (!timeStr) return 'N/A'
  return new Date(timeStr).toLocaleString()
}

const formatDuration = (seconds) => {
  if (!seconds) return '0分钟'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

const contactUser = async (type) => {
  try {
    if (type === 'phone') {
      await ElMessageBox.confirm(
        `确定要拨打电话 ${occupiedInfo.user_contact} 联系 ${occupiedInfo.user_name} 吗？`,
        '确认拨打电话',
        {
          confirmButtonText: '拨打',
          cancelButtonText: '取消',
          type: 'info'
        }
      )
      
      // 这里可以集成电话拨打功能
      ElMessage.success('正在为您拨打电话...')
      
    } else if (type === 'message') {
      await ElMessageBox.prompt(
        `请输入要发送给 ${occupiedInfo.user_name} 的消息：`,
        '发送消息',
        {
          confirmButtonText: '发送',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入消息内容...',
          inputType: 'textarea'
        }
      )
      
      ElMessage.success('消息已发送')
    }
  } catch {
    // 用户取消操作
  }
}

const requestRelease = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要向 ${occupiedInfo.user_name} 发送设备释放请求吗？`,
      '确认发送释放请求',
      {
        confirmButtonText: '发送',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    requestLoading.value = true

    // 调用API发送释放请求
    const response = await fetch(`/api/v1/devices/${props.deviceId}/release-request`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }

    const result = await response.json()
    ElMessage.success(result.message || '释放请求已发送')
    emit('release-requested', props.deviceId)

  } catch (error) {
    if (error.message !== 'cancel') {
      console.error('发送释放请求失败:', error)
      ElMessage.error(`发送释放请求失败: ${error.message}`)
    }
  } finally {
    requestLoading.value = false
  }
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.occupied-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.device-info {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #e6a23c;
}

.device-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.device-icon {
  font-size: 24px;
  color: #e6a23c;
}

.device-details {
  flex: 1;
}

.device-name {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.device-id {
  font-size: 14px;
  color: #606266;
  margin-top: 2px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.user-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  margin-bottom: 5px;
}

.user-contact,
.user-email {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #606266;
  font-size: 14px;
  margin-bottom: 3px;
}

.contact-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.time-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.time-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.time-value {
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.note-content {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  color: #606266;
  line-height: 1.6;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border-radius: 6px;
  color: #606266;
  font-size: 14px;
}

.suggestion-icon {
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
