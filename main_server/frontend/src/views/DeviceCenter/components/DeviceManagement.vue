<template>
  <div class="device-management">
    <!-- 页面头部 -->
    <div class="management-header">
      <div class="header-left">
        <div class="page-title">
          <h2>USB设备管理</h2>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="refreshData" :loading="loading" type="primary">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="handleBatchReclassify" type="warning" :loading="reclassifyLoading">
          <el-icon><Star /></el-icon>
          智能重新分类
        </el-button>
        <el-dropdown @command="handleBatchCommand">
          <el-button type="success">
            批量操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="batch-edit">批量编辑</el-dropdown-item>
              <el-dropdown-item command="batch-group">批量分组</el-dropdown-item>
              <el-dropdown-item divided command="batch-delete">批量删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>



    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-card>
        <div class="filter-row">
          <div class="filter-group">
            <el-input
              v-model="searchText"
              placeholder="搜索设备名称、ID、描述..."
              style="width: 300px;"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="filter-group">
            <el-select
              v-model="filterServer"
              placeholder="筛选服务器"
              style="width: 200px;"
              clearable
            >
              <el-option label="全部服务器" value="" />
              <el-option
                v-for="server in serverList"
                :key="server.id"
                :label="server.name"
                :value="server.id"
              />
            </el-select>
          </div>

          <div class="filter-group">
            <el-select
              v-model="filterType"
              placeholder="筛选类型"
              style="width: 150px;"
              clearable
            >
              <el-option label="全部类型" value="" />
              <el-option label="加密锁" value="encryption_key" />
              <el-option label="存储设备" value="storage" />
              <el-option label="输入设备" value="input" />
              <el-option label="通信设备" value="communication" />
              <el-option label="硬件设备" value="hardware" />
              <el-option label="未知设备" value="unknown" />
            </el-select>
          </div>

          <div class="filter-group">
            <el-select
              v-model="filterStatus"
              placeholder="筛选状态"
              style="width: 120px;"
              clearable
            >
              <el-option label="全部状态" value="" />
              <el-option label="空闲" value="idle" />
              <el-option label="被占用" value="occupied" />
              <el-option label="硬件损坏" value="damaged" />
              <el-option label="离线" value="offline" />
            </el-select>
          </div>

          <div class="filter-group">
            <el-select
              v-model="sortBy"
              placeholder="排序方式"
              style="width: 150px;"
            >
              <el-option label="注册时间" value="created_at" />
              <el-option label="设备名称" value="device_name" />
              <el-option label="服务器分组" value="server_group" />
              <el-option label="最后连接" value="last_connected" />
            </el-select>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 设备类型过滤控制面板 -->
    <div class="device-type-filter-panel">
      <el-card>
        <template #header>
          <div class="panel-header">
            <div class="panel-title">
              <el-icon><Filter /></el-icon>
              <span>设备类型过滤</span>
              <el-tag size="small" type="info">
                显示{{ Object.keys(selectedDeviceTypes).filter(k => selectedDeviceTypes[k]).length }}种类型，共{{ filteredDevicesByType.length }}个设备
              </el-tag>
            </div>
            <div class="panel-actions">
              <el-button size="small" @click="selectAllDeviceTypes">全选</el-button>
              <el-button size="small" @click="resetToDefaultTypes">重置为默认</el-button>
            </div>
          </div>
        </template>

        <div class="device-type-checkboxes">
          <el-checkbox-group v-model="selectedDeviceTypesArray" @change="handleDeviceTypeChange">
            <div class="checkbox-grid">
              <el-checkbox
                v-for="(config, type) in deviceTypeConfig"
                :key="type"
                :label="type"
                class="device-type-checkbox"
              >
                <div class="checkbox-content">
                  <span class="type-icon">{{ config.icon }}</span>
                  <span class="type-label">
                    {{ config.label.replace(/^[🔐💾⌨️📡🖨️🔌🔧❓]\s/, '') }}
                    <span class="type-count">（{{ getDeviceTypeCount(type) }}）</span>
                  </span>
                  <el-tooltip :content="config.description" placement="top">
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </el-card>
    </div>

    <!-- 设备列表 -->
    <div class="device-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>设备列表</span>
            <div class="header-actions">
              <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
              <span class="selected-count">已选择 {{ selectedDevices.length }} 个设备</span>
            </div>
          </div>
        </template>

        <el-table
          :data="filteredDevices"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column prop="device_id" label="设备ID" width="120" />

          <el-table-column label="设备名称" min-width="200">
            <template #default="{ row }">
              <div class="device-name">
                <div class="name-primary">{{ row.custom_name || row.device_name }}</div>
                <div class="name-secondary" v-if="row.custom_name">{{ row.device_name }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="设备类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getTypeColor(getFinalDeviceType(row))" size="small">
                {{ getTypeText(getFinalDeviceType(row)) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getStatusColor(row.status)"
                size="small"
                :class="{ 'clickable-status': row.status === 'occupied' }"
                @click="row.status === 'occupied' ? showOccupiedInfo(row) : null"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

      <el-table-column label="所属服务器" width="180">
        <template #default="{ row }">
          <div class="server-info">
            <div class="server-name">{{ row.server_name }}</div>
            <div class="server-ip">{{ row.server_ip }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="位置" width="120">
        <template #default="{ row }">
          <code>{{ row.physical_port || 'N/A' }}</code>
        </template>
      </el-table-column>

      <el-table-column label="最后连接" width="150">
        <template #default="{ row }">
          <div class="last-connected">
            <div v-if="row.last_connected_user">{{ row.last_connected_user }}</div>
            <div class="time">{{ formatTime(row.last_connected) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="备注" min-width="150">
        <template #default="{ row }">
          <div class="device-remark">
            {{ row.remark || '无备注' }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="viewDeviceDetail(row)">
            详情
          </el-button>
          <el-button size="small" @click="editDevice(row)">
            修改
          </el-button>
          <el-button size="small" type="danger" @click="deleteDevice(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 50, 100, 200]"
        :total="totalDevices"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</div>
  </div>

  <!-- 设备占用信息弹窗 -->
  <DeviceOccupiedDialog
    v-model="showOccupiedDialog"
    :device-id="selectedDeviceId"
    @release-requested="handleReleaseRequested"
  />
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import websocketManager from '@/utils/websocket'
import { useUserStore } from '@/stores/user'
import {
  Monitor,
  Refresh,
  ArrowDown,
  CircleCheck,
  Cpu,
  Lock,
  Search,
  Star,
  Filter,
  InfoFilled
} from '@element-plus/icons-vue'
import DeviceOccupiedDialog from './DeviceOccupiedDialog.vue'

// 路由和状态管理
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const reclassifyLoading = ref(false)
const searchText = ref('')
const filterServer = ref('')
const filterType = ref('')
const filterStatus = ref('')
const sortBy = ref('created_at')
const selectAll = ref(false)
const selectedDevices = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const showOccupiedDialog = ref(false)
const selectedDeviceId = ref(null)
const wsConnected = ref(false)
const lastUpdateTime = ref(null)

// 设备数据
const devices = ref([])
const serverList = ref([])
const deviceStats = reactive({
  total_devices: 0,
  online_devices: 0,
  hardware_devices: 0,
  occupied_devices: 0
})

// 实时数据管理
const realtimeStats = reactive({
  total_devices: 0,
  available_devices: 0,
  online_servers: 0,
  total_servers: 0,
  data_freshness: 'unknown',
  last_update: null
})

const dataMode = ref('hybrid') // realtime, database, hybrid
const showDataSource = ref(true)

// 设备类型过滤相关
const selectedDeviceTypes = ref({})
const selectedDeviceTypesArray = ref([])

// 自动刷新定时器
let refreshTimer = null

// 计算属性
const filteredDevicesByType = computed(() => {
  // 首先根据设备类型过滤
  const selectedTypes = Object.keys(selectedDeviceTypes.value).filter(type => selectedDeviceTypes.value[type])
  if (selectedTypes.length === 0) {
    return []
  }

  return devices.value.filter(device => {
    const finalType = getFinalDeviceType(device)
    return selectedTypes.includes(finalType)
  })
})

const filteredDevices = computed(() => {
  let result = filteredDevicesByType.value

  // 搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    result = result.filter(device =>
      (device.device_name && device.device_name.toLowerCase().includes(search)) ||
      (device.custom_name && device.custom_name.toLowerCase().includes(search)) ||
      (device.device_id && device.device_id.toLowerCase().includes(search)) ||
      (device.description && device.description.toLowerCase().includes(search))
    )
  }

  // 服务器过滤
  if (filterServer.value) {
    result = result.filter(device => device.server_id === filterServer.value)
  }

  // 类型过滤
  if (filterType.value) {
    result = result.filter(device => device.device_type === filterType.value)
  }

  // 状态过滤
  if (filterStatus.value) {
    result = result.filter(device => device.status === filterStatus.value)
  }

  // 排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case 'device_name':
        return (a.device_name || '').localeCompare(b.device_name || '')
      case 'server_group':
        return (a.server_name || '').localeCompare(b.server_name || '')
      case 'last_connected':
        return new Date(b.last_connected || 0) - new Date(a.last_connected || 0)
      case 'created_at':
      default:
        return new Date(b.created_at || 0) - new Date(a.created_at || 0)
    }
  })

  return result
})

const totalDevices = computed(() => filteredDevices.value.length)

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    // 并行获取设备数据和统计数据
    await Promise.all([
      fetchDeviceList(),
      fetchDeviceStats()
    ])
  } catch (error) {
    console.error('数据刷新失败:', error)
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const fetchDeviceList = async () => {
  try {
    // 调用真实API获取设备数据
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchText.value || undefined,
      server_id: filterServer.value || undefined,
      device_type: filterType.value || undefined,
      status: filterStatus.value || undefined,
      sort_by: sortBy.value,
      sort_order: 'desc'
    }

    // 移除undefined值
    Object.keys(params).forEach(key => {
      if (params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await fetch('/api/v1/devices?' + new URLSearchParams(params), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    // 更新设备列表
    devices.value = data.devices || []

    // 更新统计信息
    updateDeviceStats()

    lastUpdateTime.value = new Date().toLocaleString()

  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error(`获取设备列表失败: ${error.message}`)
  }
}

const fetchDeviceStats = async () => {
  try {
    // 根据数据模式获取统计信息
    const statsResponse = await fetch(`/api/v1/devices/stats/summary?mode=${dataMode.value}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!statsResponse.ok) {
      throw new Error(`HTTP ${statsResponse.status}: ${statsResponse.statusText}`)
    }

    const statsData = await statsResponse.json()

    // 更新统计数据
    Object.assign(deviceStats, {
      total_devices: statsData.total_devices || 0,
      online_devices: statsData.online_devices || 0,
      hardware_devices: statsData.hardware_devices || 0,
      occupied_devices: statsData.occupied_devices || 0
    })

    // 如果是混合模式或实时模式，更新实时统计
    if (dataMode.value !== 'database') {
      Object.assign(realtimeStats, {
        total_devices: statsData.total_devices || 0,
        available_devices: statsData.available_devices || 0,
        online_servers: statsData.online_servers || 0,
        total_servers: statsData.total_servers || 0,
        data_freshness: statsData.data_freshness || 'unknown',
        last_update: statsData.last_update
      })
    }

    lastUpdateTime.value = new Date().toLocaleString()

    console.log('统计数据获取成功:', statsData)
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 统计数据获取失败不影响主要功能，只记录错误
  }
}

const switchDataMode = async (mode) => {
  dataMode.value = mode
  await fetchDeviceStats()
  ElMessage.success(`已切换到${mode === 'realtime' ? '实时' : mode === 'database' ? '数据库' : '混合'}模式`)
}

const forceRefresh = async () => {
  ElMessage.info('正在强制刷新数据...')
  await refreshData()
  ElMessage.success('数据刷新完成')
}

// 设备类型配置 - 精准分类系统
const deviceTypeConfig = {
  'ca_lock': {
    label: 'CA锁',
    color: 'danger',
    icon: '🔐',
    description: 'CA证书锁、银行U盾、数字证书设备',
    defaultVisible: true
  },
  'encryption_key': {
    label: '加密锁',
    color: 'warning',
    icon: '🔒',
    description: '软件加密锁、硬件加密锁设备',
    defaultVisible: true
  },
  'bank_ukey': {
    label: '银行U盾',
    color: 'primary',
    icon: '🏦',
    description: '银行数字证书U盾设备',
    defaultVisible: true
  },
  'financial_lock': {
    label: '财务锁',
    color: 'warning',
    icon: '💰',
    description: '用友、金蝶等财务软件加密锁',
    defaultVisible: true
  },
  'cost_lock': {
    label: '造价加密锁',
    color: 'primary',
    icon: '🏗️',
    description: '广联达、新点等造价软件加密锁',
    defaultVisible: true
  },
  'other_lock': {
    label: '其他加密锁',
    color: 'danger',
    icon: '🔒',
    description: '其他类型的加密锁设备',
    defaultVisible: false
  },
  'storage': {
    label: '存储设备',
    color: 'info',
    icon: '💾',
    description: 'U盘、移动硬盘等存储设备',
    defaultVisible: false
  },
  'peripheral_device': {
    label: '外设',
    color: 'success',
    icon: '🖨️',
    description: '打印机、扫描仪等外围设备',
    defaultVisible: false
  },
  'video_device': {
    label: '视频设备',
    color: 'primary',
    icon: '📹',
    description: '摄像头、采集卡等视频设备',
    defaultVisible: false
  },
  'audio_device': {
    label: '音频设备',
    color: 'warning',
    icon: '🎵',
    description: '麦克风、耳机、声卡等音频设备',
    defaultVisible: false
  },
  'bluetooth_device': {
    label: '蓝牙设备',
    color: 'info',
    icon: '📶',
    description: '蓝牙适配器、蓝牙设备',
    defaultVisible: false
  },
  'hub': {
    label: 'Hub设备',
    color: '',
    icon: '🔌',
    description: 'USB Hub、集线器等',
    defaultVisible: false
  },
  'input': {
    label: '输入设备',
    color: '',
    icon: '⌨️',
    description: '键盘、鼠标等输入设备',
    defaultVisible: false
  },
  'communication': {
    label: '通信设备',
    color: '',
    icon: '📡',
    description: '网络适配器等通信设备',
    defaultVisible: false
  },
  'hardware': {
    label: '硬件设备',
    color: '',
    icon: '🔧',
    description: '其他硬件设备',
    defaultVisible: false
  },
  'unknown': {
    label: '未知设备',
    color: '',
    icon: '❓',
    description: '未识别的设备类型',
    defaultVisible: false
  }
}

const getTypeColor = (type) => {
  return deviceTypeConfig[type]?.color || ''
}

const getTypeText = (type) => {
  return deviceTypeConfig[type]?.label || '待补充'
}

const getTypeIcon = (type) => {
  return deviceTypeConfig[type]?.icon || '❓'
}

const getTypeDescription = (type) => {
  return deviceTypeConfig[type]?.description || '未知设备类型'
}

// 获取设备的最终类型（优先级：手动 > 主服务器判定 > 从服务器上报 > 原始类型）
const getFinalDeviceType = (device) => {
  // 使用后端返回的最终设备类型
  if (device.final_device_type) {
    return mapDeviceType(device.final_device_type, device)
  }

  // 兜底：使用原始设备类型映射
  return mapDeviceType(device.device_type || 'unknown', device)
}

// 设备类型映射函数 - 处理前后端类型差异，优先使用USB.IDS增强信息
const mapDeviceType = (backendType, device = null) => {
  // 如果提供了设备对象，优先使用USB.IDS增强识别信息
  if (device) {
    // 1. 优先检查USB.IDS增强识别结果
    if (device.identification_source === 'usb_ids_enhanced') {
      return 'encryption_key'  // USB.IDS增强识别的设备显示为加密锁
    }

    // 2. 检查是否有USB.IDS厂商信息
    if (device.usb_ids_vendor_name && device.usb_ids_vendor_name.trim()) {
      const vendorName = device.usb_ids_vendor_name.toLowerCase()
      // 基于厂商名称进行设备类型推断
      if (vendorName.includes('senseshield') ||
          vendorName.includes('rockey') ||
          vendorName.includes('hasp') ||
          vendorName.includes('sentinel') ||
          vendorName.includes('safenet') ||
          vendorName.includes('aladdin')) {
        return 'encryption_key'
      }
      // 如果有厂商信息但不是加密锁厂商，根据设备名称进一步判断
      if (device.usb_ids_device_name) {
        const deviceName = device.usb_ids_device_name.toLowerCase()
        if (deviceName.includes('dongle') ||
            deviceName.includes('key') ||
            deviceName.includes('lock') ||
            deviceName.includes('token')) {
          return 'encryption_key'
        }
      }
    }
  }

  // 3. 使用新的精准分类映射
  const typeMapping = {
    // 新的精准分类
    'ca_lock': 'ca_lock',
    'encryption_key': 'encryption_key',
    'bank_ukey': 'bank_ukey',
    'financial_lock': 'financial_lock',
    'cost_lock': 'cost_lock',
    'other_lock': 'other_lock',
    'video_device': 'video_device',
    'audio_device': 'audio_device',
    'bluetooth_device': 'bluetooth_device',
    'peripheral_device': 'peripheral_device',

    // 保持兼容的原有分类
    'storage': 'storage',
    'input': 'input',
    'communication': 'communication',
    'hub': 'hub',
    'hardware': 'hardware',
    'unknown': 'unknown',

    // 数据库中的设备类型映射
    'CA锁': 'ca_lock',
    '加密锁': 'encryption_key',
    '银行U盾': 'bank_ukey',

    // 兼容性映射
    'encryption_lock': 'encryption_key',
    'encryption': 'encryption_key',
    'printer_scanner': 'peripheral_device',
    'printer': 'peripheral_device',
    'unknown_error': 'unknown',
    'virtual': 'unknown',
    'system': 'unknown'
  }

  return typeMapping[backendType] || 'unknown'
}

// 设备类型过滤方法
const initializeDeviceTypeFilter = () => {
  // 从localStorage加载用户偏好，如果没有则使用默认设置
  const savedTypes = localStorage.getItem('omnilink-device-type-filter')
  if (savedTypes) {
    try {
      selectedDeviceTypes.value = JSON.parse(savedTypes)
    } catch (e) {
      console.warn('Failed to parse saved device type filter:', e)
      resetToDefaultTypes()
    }
  } else {
    resetToDefaultTypes()
  }

  // 同步数组格式
  selectedDeviceTypesArray.value = Object.keys(selectedDeviceTypes.value).filter(type => selectedDeviceTypes.value[type])
}

const saveDeviceTypeFilter = () => {
  localStorage.setItem('omnilink-device-type-filter', JSON.stringify(selectedDeviceTypes.value))
}

const handleDeviceTypeChange = (selectedTypes) => {
  // 更新对象格式
  selectedDeviceTypes.value = {}
  Object.keys(deviceTypeConfig).forEach(type => {
    selectedDeviceTypes.value[type] = selectedTypes.includes(type)
  })

  // 保存到localStorage
  saveDeviceTypeFilter()
}

const selectAllDeviceTypes = () => {
  Object.keys(deviceTypeConfig).forEach(type => {
    selectedDeviceTypes.value[type] = true
  })
  selectedDeviceTypesArray.value = Object.keys(deviceTypeConfig)
  saveDeviceTypeFilter()
}

const resetToDefaultTypes = () => {
  selectedDeviceTypes.value = {}
  Object.keys(deviceTypeConfig).forEach(type => {
    selectedDeviceTypes.value[type] = deviceTypeConfig[type].defaultVisible
  })
  selectedDeviceTypesArray.value = Object.keys(selectedDeviceTypes.value).filter(type => selectedDeviceTypes.value[type])
  saveDeviceTypeFilter()
}

// 获取设备类型计数
const getDeviceTypeCount = (type) => {
  if (!devices.value || devices.value.length === 0) {
    return 0
  }

  // 根据当前过滤条件计算设备数量
  let filteredDevices = devices.value

  // 应用搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    filteredDevices = filteredDevices.filter(device =>
      device.device_name?.toLowerCase().includes(search) ||
      device.custom_name?.toLowerCase().includes(search) ||
      device.device_id?.toLowerCase().includes(search) ||
      device.serial_number?.toLowerCase().includes(search)
    )
  }

  // 应用服务器过滤
  if (filterServer.value) {
    filteredDevices = filteredDevices.filter(device => device.slave_server_id === filterServer.value)
  }

  // 应用状态过滤
  if (filterStatus.value) {
    filteredDevices = filteredDevices.filter(device => device.status === filterStatus.value)
  }

  // 计算指定类型的设备数量
  return filteredDevices.filter(device => {
    const deviceType = getFinalDeviceType(device)
    return deviceType === type
  }).length
}

const getStatusColor = (status) => {
  const colorMap = {
    'idle': 'success',
    'occupied': 'warning',
    'damaged': 'danger',
    'offline': 'info'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'idle': '空闲',
    'occupied': '被占用',
    'damaged': '硬件损坏',
    'offline': '离线'
  }
  return textMap[status] || '未知'
}

const formatTime = (timeStr) => {
  if (!timeStr) return 'N/A'
  return new Date(timeStr).toLocaleString()
}

const handleSelectAll = (checked) => {
  if (checked) {
    selectedDevices.value = [...filteredDevices.value]
  } else {
    selectedDevices.value = []
  }
}

const handleSelectionChange = (selection) => {
  selectedDevices.value = selection
  selectAll.value = selection.length === filteredDevices.value.length
}

const handleBatchCommand = async (command) => {
  switch (command) {
    case 'batch-edit':
    case 'batch-group':
    case 'batch-delete':
      if (selectedDevices.value.length === 0) {
        ElMessage.warning('请先选择要操作的设备')
        return
      }
      ElMessage.info(`批量操作: ${command}, 选中 ${selectedDevices.value.length} 个设备`)
      break
    default:
      ElMessage.warning('未知的批量操作')
  }
}

// 批量重新分类设备类型
const handleBatchReclassify = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将使用智能分类器重新分析所有设备的类型，支持精准识别CA锁、财务锁、造价加密锁等。是否继续？',
      '智能重新分类设备类型',
      {
        confirmButtonText: '开始分类',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    reclassifyLoading.value = true

    try {
      const response = await fetch('/api/v1/devices/batch-reclassify-types', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userStore.token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      ElMessage.success(`智能分类完成！总计: ${result.statistics.total}, 成功: ${result.statistics.updated}`)

      // 刷新设备列表
      await refreshData()

    } finally {
      reclassifyLoading.value = false
    }

  } catch (error) {
    reclassifyLoading.value = false
    if (error !== 'cancel') {
      console.error('智能重新分类失败:', error)
      ElMessage.error('智能重新分类失败: ' + error.message)
    }
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const viewDeviceDetail = (device) => {
  // 跳转到设备详情页面
  router.push(`/device-center/device/${device.id}`)
}

const editDevice = (device) => {
  // 跳转到设备详情页面进行编辑
  router.push(`/device-center/device/${device.id}`)
}

const deleteDevice = async (device) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备 "${device.device_name}" 吗？此操作将解除USB设备自动绑定。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用删除API
    const response = await fetch(`/api/v1/devices/${device.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }

    ElMessage.success(`设备 "${device.device_name}" 已删除`)
    refreshData()

  } catch (error) {
    if (error.message !== 'cancel') {
      console.error('删除设备失败:', error)
      ElMessage.error(`删除设备失败: ${error.message}`)
    }
  }
}

const showOccupiedInfo = (device) => {
  if (device.status === 'occupied') {
    selectedDeviceId.value = device.id
    showOccupiedDialog.value = true
  }
}

const handleReleaseRequested = async (deviceId) => {
  try {
    const response = await fetch(`/api/v1/devices/${deviceId}/release-request`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || `HTTP ${response.status}`)
    }

    const result = await response.json()
    ElMessage.success(result.message || '设备释放请求已发送')

    // 刷新设备状态
    refreshData()

  } catch (error) {
    console.error('发送释放请求失败:', error)
    ElMessage.error(`发送释放请求失败: ${error.message}`)
  }
}

// WebSocket相关方法
const setupWebSocket = () => {
  // 设置连接状态变化回调
  websocketManager.setConnectionChangeCallback((connected) => {
    wsConnected.value = connected
    if (connected) {
      console.log('WebSocket连接已建立')
    } else {
      console.log('WebSocket连接已断开')
    }
  })

  // 订阅设备更新事件
  websocketManager.subscribe('device_updates')

  // 添加设备状态更新监听器
  websocketManager.addEventListener('device_status_update', handleDeviceStatusUpdate)
  websocketManager.addEventListener('device_connected', handleDeviceConnection)
  websocketManager.addEventListener('device_disconnected', handleDeviceDisconnection)
  websocketManager.addEventListener('device_occupied', handleDeviceOccupation)
  websocketManager.addEventListener('device_released', handleDeviceRelease)
  websocketManager.addEventListener('device_added', handleDeviceAdded)
  websocketManager.addEventListener('device_updated', handleDeviceUpdated)
  websocketManager.addEventListener('device_deleted', handleDeviceDeleted)
  websocketManager.addEventListener('device_release_requested', handleDeviceReleaseRequested)

  // 连接WebSocket
  const token = userStore.token
  if (token) {
    websocketManager.connect(token)
  }
}

const handleDeviceStatusUpdate = (message) => {
  console.log('设备状态更新:', message)

  // 更新设备列表中对应设备的状态
  const deviceIndex = devices.value.findIndex(d => d.id === message.device_id)
  if (deviceIndex !== -1) {
    devices.value[deviceIndex].status = message.new_status
    lastUpdateTime.value = new Date().toLocaleString()

    // 更新统计信息
    updateDeviceStats()

    ElMessage.info(`设备状态更新: ${devices.value[deviceIndex].device_name} -> ${getStatusText(message.new_status)}`)
  }
}

const handleDeviceConnection = (message) => {
  console.log('设备连接:', message)

  // 可能需要刷新设备列表或更新特定设备状态
  const deviceIndex = devices.value.findIndex(d => d.id === message.device_id)
  if (deviceIndex !== -1) {
    devices.value[deviceIndex].status = 'occupied'
    devices.value[deviceIndex].last_connected = message.timestamp
    devices.value[deviceIndex].last_connected_user = message.user_name || 'Unknown'

    updateDeviceStats()
    ElMessage.success(`设备已连接: ${devices.value[deviceIndex].device_name}`)
  }
}

const handleDeviceDisconnection = (message) => {
  console.log('设备断开:', message)

  const deviceIndex = devices.value.findIndex(d => d.id === message.device_id)
  if (deviceIndex !== -1) {
    devices.value[deviceIndex].status = 'idle'

    updateDeviceStats()
    ElMessage.info(`设备已断开: ${devices.value[deviceIndex].device_name}`)
  }
}

const handleDeviceOccupation = (message) => {
  console.log('设备被占用:', message)

  const deviceIndex = devices.value.findIndex(d => d.id === message.device_id)
  if (deviceIndex !== -1) {
    devices.value[deviceIndex].status = 'occupied'
    devices.value[deviceIndex].last_connected_user = message.user_name
    devices.value[deviceIndex].last_connected = message.timestamp

    updateDeviceStats()
    ElMessage.warning(`设备被占用: ${devices.value[deviceIndex].device_name} (${message.user_name})`)
  }
}

const handleDeviceRelease = (message) => {
  console.log('设备已释放:', message)

  const deviceIndex = devices.value.findIndex(d => d.id === message.device_id)
  if (deviceIndex !== -1) {
    devices.value[deviceIndex].status = 'idle'

    updateDeviceStats()
    ElMessage.success(`设备已释放: ${devices.value[deviceIndex].device_name}`)
  }
}

const handleDeviceAdded = (message) => {
  console.log('设备已添加:', message)

  // 刷新设备列表以获取新设备
  refreshData()
  ElMessage.success(`新设备已添加: ${message.device_name}`)
}

const handleDeviceUpdated = (message) => {
  console.log('设备已更新:', message)

  // 刷新设备列表以获取最新信息
  refreshData()
  ElMessage.info(`设备信息已更新`)
}

const handleDeviceDeleted = (message) => {
  console.log('设备已删除:', message)

  // 从列表中移除设备
  const deviceIndex = devices.value.findIndex(d => d.id === message.device_id)
  if (deviceIndex !== -1) {
    devices.value.splice(deviceIndex, 1)
    updateDeviceStats()
    ElMessage.info(`设备已删除: ${message.device_name}`)
  }
}

const handleDeviceReleaseRequested = (message) => {
  console.log('收到设备释放请求:', message)

  // 如果当前用户是占用者，显示释放请求通知
  if (message.requester_id !== userStore.user?.id) {
    ElMessage({
      type: 'warning',
      message: `${message.requester_name} 请求您释放设备: ${message.device_name}`,
      duration: 10000,
      showClose: true
    })
  }
}

const updateDeviceStats = () => {
  deviceStats.total_devices = devices.value.length
  deviceStats.online_devices = devices.value.filter(d => d.status !== 'offline').length
  deviceStats.hardware_devices = devices.value.filter(d => d.device_type !== 'unknown').length
  deviceStats.occupied_devices = devices.value.filter(d => d.status === 'occupied').length
}

const cleanupWebSocket = () => {
  // 移除事件监听器
  websocketManager.removeEventListener('device_status_update', handleDeviceStatusUpdate)
  websocketManager.removeEventListener('device_connected', handleDeviceConnection)
  websocketManager.removeEventListener('device_disconnected', handleDeviceDisconnection)
  websocketManager.removeEventListener('device_occupied', handleDeviceOccupation)
  websocketManager.removeEventListener('device_released', handleDeviceRelease)
  websocketManager.removeEventListener('device_added', handleDeviceAdded)
  websocketManager.removeEventListener('device_updated', handleDeviceUpdated)
  websocketManager.removeEventListener('device_deleted', handleDeviceDeleted)
  websocketManager.removeEventListener('device_release_requested', handleDeviceReleaseRequested)

  // 取消订阅
  websocketManager.unsubscribe('device_updates')
}

// 生命周期
onMounted(() => {
  // 初始化设备类型过滤
  initializeDeviceTypeFilter()

  refreshData()
  // 设置WebSocket连接
  setupWebSocket()
  // 设置自动刷新（每60秒，作为WebSocket的备用机制）
  refreshTimer = setInterval(refreshData, 60000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  // 清理WebSocket
  cleanupWebSocket()
})
</script>

<style scoped>
.device-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.page-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 10px;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stats-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 0;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.online {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.hardware {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.occupied {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.search-filters {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
}

.device-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.selected-count {
  color: #606266;
  font-size: 14px;
}

.device-name {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.name-primary {
  font-weight: 500;
  color: #303133;
}

.name-secondary {
  font-size: 12px;
  color: #909399;
}

.server-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.server-name {
  font-weight: 500;
  color: #303133;
}

.server-ip {
  font-size: 12px;
  color: #909399;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.last-connected {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time {
  font-size: 12px;
  color: #909399;
}

.device-remark {
  color: #606266;
  font-size: 14px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.status-icon {
  margin-right: 5px;
}

.clickable-status {
  cursor: pointer;
  transition: all 0.3s;
}

.clickable-status:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .management-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .header-left {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .page-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .stats-overview {
    grid-template-columns: 1fr;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .filter-group {
    width: 100%;
  }
}

/* 设备类型过滤面板样式 */
.device-type-filter-panel {
  margin-bottom: 16px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

.device-type-checkboxes {
  margin-top: 16px;
}

.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.device-type-checkbox {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.3s;
}

.device-type-checkbox:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.device-type-checkbox.is-checked {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.checkbox-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-icon {
  font-size: 18px;
}

.type-label {
  flex: 1;
  font-weight: 500;
}

.info-icon {
  color: #909399;
  cursor: help;
}

.info-icon:hover {
  color: #409eff;
}
</style>
