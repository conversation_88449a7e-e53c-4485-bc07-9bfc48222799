<template>
  <div class="virtualhere-manager">
    <!-- 页面头部 -->
    <div class="manager-header">
      <div class="header-left">
        <el-button @click="goBack" type="text" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回详情
        </el-button>
        <div class="manager-title">
          <h2>VirtualHere 程序管理</h2>
          <el-tag type="info" size="large">
            <el-icon class="status-icon">
              <Monitor />
            </el-icon>
            {{ serverInfo.name || '从服务器' }}
          </el-tag>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="refreshFileList" :loading="loading" type="primary">
          <el-icon><Refresh /></el-icon>
          刷新文件列表
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="manager-content" v-loading="loading">
      <!-- 当前文件状态 -->
      <el-row :gutter="20" class="content-sections">
        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><FolderOpened /></el-icon>
                <span>当前VirtualHere文件</span>
              </div>
            </template>
            <div class="file-list">
              <div v-if="currentFiles.length === 0" class="empty-state">
                <el-empty description="暂无文件信息" />
              </div>
              <div v-else>
                <div v-for="file in currentFiles" :key="file.name" class="file-item">
                  <div class="file-info">
                    <el-icon class="file-icon"><Document /></el-icon>
                    <div class="file-details">
                      <div class="file-name">{{ file.name }}</div>
                      <div class="file-meta">
                        <span class="file-size">{{ formatFileSize(file.size) }}</span>
                        <span class="file-time">{{ formatTime(file.modified) }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="file-actions">
                    <el-button size="small" @click="backupFile(file)">
                      <el-icon><Download /></el-icon>
                      备份
                    </el-button>
                    <el-button size="small" type="warning" @click="rollbackFile(file)">
                      <el-icon><RefreshLeft /></el-icon>
                      回滚
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <el-icon><Connection /></el-icon>
                <span>VirtualHere状态</span>
              </div>
            </template>
            <div class="status-info">
              <div class="status-item">
                <span class="status-label">运行状态：</span>
                <el-tag :type="serverInfo.vh_status === 'running' ? 'success' : 'danger'">
                  {{ serverInfo.vh_status === 'running' ? '运行中' : '已停止' }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">监听端口：</span>
                <span class="status-value">{{ serverInfo.vh_port || 7575 }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">当前版本：</span>
                <span class="status-value">{{ serverInfo.vh_version || 'VirtualHere USB Server 4.3.3' }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 文件上传区域 -->
      <el-row :gutter="20" class="content-sections">
        <el-col :span="24">
          <el-card class="upload-card">
            <template #header>
              <div class="card-header">
                <el-icon><Upload /></el-icon>
                <span>程序文件上传</span>
                <div class="header-actions">
                  <el-button size="small" @click="addUploadSlot">
                    <el-icon><Plus /></el-icon>
                    添加上传槽位
                  </el-button>
                </div>
              </div>
            </template>
            
            <div class="upload-slots">
              <div v-for="(slot, index) in uploadSlots" :key="slot.id" class="upload-slot">
                <div class="slot-header">
                  <div class="slot-title">
                    <el-icon><Document /></el-icon>
                    <span>上传槽位 {{ index + 1 }}</span>
                  </div>
                  <div class="slot-actions">
                    <el-button 
                      v-if="uploadSlots.length > 1" 
                      size="small" 
                      type="danger" 
                      @click="removeUploadSlot(index)"
                    >
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
                
                <div class="slot-content">
                  <div class="target-name">
                    <el-input
                      v-model="slot.targetName"
                      placeholder="目标文件名（如：vhusbdx86_64）"
                      class="target-input"
                    >
                      <template #prepend>目标名称</template>
                    </el-input>
                  </div>
                  
                  <div class="upload-area">
                    <el-upload
                      :ref="`upload-${slot.id}`"
                      class="upload-dragger"
                      drag
                      :auto-upload="false"
                      :limit="1"
                      :on-change="(file) => handleFileChange(file, slot)"
                      :on-remove="() => handleFileRemove(slot)"
                      :file-list="slot.fileList"
                    >
                      <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                      <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                      </div>
                      <template #tip>
                        <div class="el-upload__tip">
                          支持可执行文件、库文件等，单个文件大小不超过100MB
                        </div>
                      </template>
                    </el-upload>
                  </div>
                </div>
              </div>
            </div>

            <div class="upload-actions">
              <el-button 
                type="primary" 
                size="large"
                :loading="uploading"
                :disabled="!hasValidUploads"
                @click="startUpload"
              >
                <el-icon><Upload /></el-icon>
                开始上传并替换
              </el-button>
              <el-button size="large" @click="clearAllUploads">
                <el-icon><Delete /></el-icon>
                清空所有
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 操作历史 -->
      <el-row :gutter="20" class="content-sections">
        <el-col :span="24">
          <el-card class="history-card">
            <template #header>
              <div class="card-header">
                <el-icon><Clock /></el-icon>
                <span>操作历史</span>
              </div>
            </template>
            <div class="history-list">
              <div v-if="operationHistory.length === 0" class="empty-state">
                <el-empty description="暂无操作记录" />
              </div>
              <el-timeline v-else>
                <el-timeline-item
                  v-for="item in operationHistory"
                  :key="item.id"
                  :timestamp="formatTime(item.timestamp)"
                  :type="getHistoryType(item.type)"
                >
                  <div class="history-item">
                    <div class="history-title">{{ item.title }}</div>
                    <div class="history-description">{{ item.description }}</div>
                    <div v-if="item.files" class="history-files">
                      <el-tag v-for="file in item.files" :key="file" size="small">{{ file }}</el-tag>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  RefreshLeft,
  Monitor,
  FolderOpened,
  Document,
  Download,
  Connection,
  Upload,
  Plus,
  Delete,
  UploadFilled,
  Clock
} from '@element-plus/icons-vue'

// 路由和状态
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const uploading = ref(false)
const serverId = route.params.id

// 服务器信息
const serverInfo = reactive({
  name: '',
  vh_status: 'unknown',
  vh_port: 7575,
  vh_version: ''
})

// 当前文件列表
const currentFiles = ref([])

// 上传槽位
const uploadSlots = ref([
  {
    id: Date.now(),
    targetName: '',
    fileList: [],
    file: null
  }
])

// 操作历史
const operationHistory = ref([])

// 计算属性
const hasValidUploads = computed(() => {
  return uploadSlots.value.some(slot => 
    slot.file && slot.targetName.trim()
  )
})

// 方法
const goBack = () => {
  router.push(`/device-center/slave-server/${serverId}`)
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (timeStr) => {
  if (!timeStr) return 'N/A'
  return new Date(timeStr).toLocaleString()
}

const getHistoryType = (type) => {
  const typeMap = {
    'upload': 'success',
    'backup': 'info',
    'error': 'danger',
    'rollback': 'warning'
  }
  return typeMap[type] || 'info'
}

const addUploadSlot = () => {
  uploadSlots.value.push({
    id: Date.now(),
    targetName: '',
    fileList: [],
    file: null
  })
}

const removeUploadSlot = (index) => {
  uploadSlots.value.splice(index, 1)
}

const handleFileChange = (file, slot) => {
  slot.file = file.raw
  slot.fileList = [file]
}

const handleFileRemove = (slot) => {
  slot.file = null
  slot.fileList = []
}

const clearAllUploads = () => {
  uploadSlots.value.forEach(slot => {
    slot.file = null
    slot.fileList = []
    slot.targetName = ''
  })
}

const refreshFileList = async () => {
  loading.value = true
  try {
    // 调用从服务器API获取VirtualHere文件列表
    const response = await fetch(`http://localhost:8890/api/files/virtualhere`)
    const data = await response.json()

    if (data.status === 'success') {
      currentFiles.value = data.files || []
    } else {
      throw new Error(data.message || '获取文件列表失败')
    }

    loading.value = false
  } catch (error) {
    console.error('获取文件列表失败:', error)
    ElMessage.error('获取文件列表失败')

    // 降级到模拟数据
    currentFiles.value = [
      {
        name: 'vhusbdx86_64',
        size: 2048576,
        modified: new Date().toISOString()
      },
      {
        name: 'vhclientx86_64',
        size: 1024768,
        modified: new Date(Date.now() - 86400000).toISOString()
      }
    ]
    loading.value = false
  }
}

const backupFile = async (file) => {
  try {
    await ElMessageBox.confirm(
      `确定要备份文件 "${file.name}" 吗？`,
      '确认备份',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    // 调用从服务器备份API
    const response = await fetch(`http://localhost:8890/api/files/backup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        filename: file.name
      })
    })

    const data = await response.json()

    if (data.status === 'success') {
      ElMessage.success(`文件 "${file.name}" 备份成功`)

      // 添加到操作历史
      operationHistory.value.unshift({
        id: Date.now(),
        type: 'backup',
        title: '文件备份',
        description: `备份文件: ${file.name} → ${data.backup_file}`,
        timestamp: new Date().toISOString(),
        files: [file.name, data.backup_file]
      })

      // 刷新文件列表
      refreshFileList()
    } else {
      throw new Error(data.message || '备份失败')
    }
  } catch (error) {
    if (error.message !== 'cancel') {
      console.error('备份失败:', error)
      ElMessage.error(`备份失败: ${error.message}`)
    }
  }
}

const startUpload = async () => {
  try {
    const validSlots = uploadSlots.value.filter(slot =>
      slot.file && slot.targetName.trim()
    )

    if (validSlots.length === 0) {
      ElMessage.warning('请选择文件并设置目标名称')
      return
    }

    await ElMessageBox.confirm(
      `确定要上传并替换 ${validSlots.length} 个文件吗？此操作将备份原文件并替换为新文件。`,
      '确认上传替换',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    uploading.value = true
    const uploadResults = []

    try {
      // 实现安全的文件上传和替换流程
      for (let i = 0; i < validSlots.length; i++) {
        const slot = validSlots[i]

        // 步骤1: 上传文件到临时目录
        const formData = new FormData()
        formData.append('file', slot.file)
        formData.append('target_name', slot.targetName)

        const uploadResponse = await fetch(`http://localhost:8890/api/files/upload`, {
          method: 'POST',
          body: formData
        })

        const uploadData = await uploadResponse.json()

        if (uploadData.status !== 'success') {
          throw new Error(`上传文件 ${slot.file.name} 失败: ${uploadData.message}`)
        }

        // 步骤2: 安全替换文件（包含自动备份）
        const replaceResponse = await fetch(`http://localhost:8890/api/files/replace`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            temp_filename: slot.targetName,
            target_filename: slot.targetName
          })
        })

        const replaceData = await replaceResponse.json()

        if (replaceData.status !== 'success') {
          throw new Error(`替换文件 ${slot.targetName} 失败: ${replaceData.message}`)
        }

        uploadResults.push({
          originalName: slot.file.name,
          targetName: slot.targetName,
          size: uploadData.size
        })

        // 添加到操作历史
        operationHistory.value.unshift({
          id: Date.now() + i,
          type: 'upload',
          title: '文件上传替换',
          description: `成功替换: ${slot.file.name} → ${slot.targetName} (${formatFileSize(uploadData.size)})`,
          timestamp: new Date().toISOString(),
          files: [slot.targetName]
        })
      }

      uploading.value = false
      ElMessage.success(`成功上传并替换 ${uploadResults.length} 个文件`)

      // 清空上传槽位
      clearAllUploads()

      // 刷新文件列表
      refreshFileList()

    } catch (error) {
      uploading.value = false
      console.error('上传替换过程中出错:', error)
      ElMessage.error(`上传替换失败: ${error.message}`)

      // 添加错误记录到操作历史
      operationHistory.value.unshift({
        id: Date.now(),
        type: 'error',
        title: '文件上传替换失败',
        description: error.message,
        timestamp: new Date().toISOString(),
        files: validSlots.map(slot => slot.targetName)
      })
    }

  } catch (error) {
    uploading.value = false
    if (error !== 'cancel') {
      console.error('上传失败:', error)
      ElMessage.error('文件上传失败')
    }
  }
}

const rollbackFile = async (file) => {
  try {
    await ElMessageBox.confirm(
      `确定要回滚文件 "${file.name}" 到备份版本吗？这将覆盖当前文件。`,
      '确认回滚',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用从服务器回滚API
    const response = await fetch(`http://localhost:8890/api/files/rollback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        filename: file.name
      })
    })

    const data = await response.json()

    if (data.status === 'success') {
      ElMessage.success(`文件 "${file.name}" 回滚成功`)

      // 添加到操作历史
      operationHistory.value.unshift({
        id: Date.now(),
        type: 'rollback',
        title: '文件回滚',
        description: `回滚文件: ${file.name} 到备份版本`,
        timestamp: new Date().toISOString(),
        files: [file.name]
      })

      // 刷新文件列表
      refreshFileList()
    } else {
      throw new Error(data.message || '回滚失败')
    }
  } catch (error) {
    if (error.message !== 'cancel') {
      console.error('回滚失败:', error)
      ElMessage.error(`回滚失败: ${error.message}`)
    }
  }
}

// 生命周期
onMounted(() => {
  // 获取服务器信息
  serverInfo.name = 'OmniLink-Slave-Server'
  serverInfo.vh_status = 'running'
  serverInfo.vh_port = 7575
  serverInfo.vh_version = 'VirtualHere USB Server 4.3.3'

  // 获取文件列表
  refreshFileList()
})
</script>

<style scoped>
.virtualhere-manager {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  font-size: 14px;
  color: #606266;
}

.manager-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.manager-title h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 10px;
}

.manager-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-sections {
  margin-bottom: 20px;
}

.info-card, .upload-card, .history-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  margin-left: auto;
}

.file-list {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.file-icon {
  font-size: 20px;
  color: #409eff;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.file-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #909399;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
}

.status-value {
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.upload-slots {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.upload-slot {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.slot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.slot-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #303133;
}

.slot-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.target-name {
  width: 100%;
}

.target-input {
  width: 100%;
}

.upload-area {
  width: 100%;
}

.upload-dragger {
  width: 100%;
}

.upload-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.history-list {
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  padding: 8px 0;
}

.history-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.history-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.history-files {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.status-icon {
  margin-right: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .manager-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .header-left {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .manager-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .slot-content {
    flex-direction: column;
  }

  .upload-actions {
    flex-direction: column;
  }
}
</style>
