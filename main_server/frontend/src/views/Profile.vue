<template>
  <div class="profile-container">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <h2>个人资料</h2>
          <el-tag v-if="userStore.isNewUser" type="warning" size="large">
            新用户
          </el-tag>
          <el-tag v-else-if="userStore.isSuperAdmin" type="danger" size="large">
            超级管理员
          </el-tag>
          <el-tag v-else-if="userStore.isManager" type="success" size="large">
            管理员
          </el-tag>
          <el-tag v-else type="info" size="large">
            普通用户
          </el-tag>
        </div>
      </template>
      
      <div class="profile-content">
        <!-- 用户头像 -->
        <div class="avatar-section">
          <el-avatar :size="120" class="user-avatar">
            {{ userStore.userName.charAt(0) }}
          </el-avatar>
          <h3 class="user-name">{{ userStore.userName }}</h3>
          <p class="user-username">@{{ userStore.userInfo?.username }}</p>
        </div>
        
        <!-- 基本信息 -->
        <div class="info-section">
          <h4>基本信息</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="用户名">
              {{ userStore.userInfo?.username }}
            </el-descriptions-item>
            <el-descriptions-item label="姓名">
              {{ userStore.userInfo?.full_name }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ userStore.userInfo?.email }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
              {{ userStore.userInfo?.phone || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="账户状态">
              <el-tag :type="userStore.userInfo?.is_active ? 'success' : 'danger'">
                {{ userStore.userInfo?.is_active ? '正常' : '已禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatDate(userStore.userInfo?.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后登录">
              {{ formatDate(userStore.userInfo?.last_login) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <!-- 组织信息 -->
        <div class="info-section">
          <h4>组织信息</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="所属组织">
              {{ organizationInfo?.name || '未分配' }}
            </el-descriptions-item>
            <el-descriptions-item label="组织层级">
              {{ getOrganizationLevel(organizationInfo?.level) }}
            </el-descriptions-item>
            <el-descriptions-item label="组织路径">
              {{ organizationInfo?.path || '/' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <!-- 角色权限信息 -->
        <div class="info-section">
          <h4>角色权限</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="用户角色">
              <div class="roles-container">
                <el-tag 
                  v-for="role in userStore.userRoles" 
                  :key="role.id"
                  :type="getRoleTagType(role.name)"
                  class="role-tag"
                >
                  {{ role.name }}
                </el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="权限列表" v-if="!userStore.isNewUser">
              <div class="permissions-container">
                <el-tag 
                  v-for="permission in userStore.permissions" 
                  :key="permission"
                  size="small"
                  class="permission-tag"
                >
                  {{ permission }}
                </el-tag>
                <span v-if="userStore.permissions.length === 0" class="no-permissions">
                  暂无特殊权限
                </span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="权限说明" v-if="userStore.isNewUser">
              <el-alert
                title="新用户权限限制"
                type="warning"
                :closable="false"
                show-icon
              >
                <p>作为新用户，您当前只能访问个人资料页面。</p>
                <p>如需使用其他功能，请联系管理员进行权限申请。</p>
              </el-alert>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <!-- 新用户申请权限按钮 -->
        <div class="action-section" v-if="userStore.isNewUser">
          <el-button 
            type="primary" 
            size="large"
            @click="showApplicationDialog = true"
            :disabled="applicationLoading"
          >
            申请权限
          </el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 权限申请对话框 -->
    <el-dialog
      v-model="showApplicationDialog"
      title="权限申请"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="applicationFormRef"
        :model="applicationForm"
        :rules="applicationRules"
        label-width="80px"
      >
        <el-form-item label="申请类型" prop="type">
          <el-select v-model="applicationForm.type" placeholder="请选择申请类型">
            <el-option label="权限升级" value="permission_upgrade" />
            <el-option label="功能访问" value="feature_access" />
            <el-option label="其他申请" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请说明" prop="description">
          <el-input
            v-model="applicationForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细说明您的申请理由和需要的权限..."
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showApplicationDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="submitApplication"
          :loading="applicationLoading"
        >
          提交申请
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()

// 组织信息
const organizationInfo = ref(null)

// 权限申请相关
const showApplicationDialog = ref(false)
const applicationLoading = ref(false)
const applicationFormRef = ref()

const applicationForm = reactive({
  type: '',
  description: ''
})

const applicationRules = {
  type: [
    { required: true, message: '请选择申请类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请填写申请说明', trigger: 'blur' },
    { min: 10, message: '申请说明至少10个字符', trigger: 'blur' }
  ]
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取组织层级名称
const getOrganizationLevel = (level) => {
  const levelMap = {
    0: '集团总部',
    1: '大区',
    2: '分公司',
    3: '部门',
    4: '小组'
  }
  return levelMap[level] || '未知'
}

// 获取角色标签类型
const getRoleTagType = (roleName) => {
  const typeMap = {
    '超级管理员': 'danger',
    '管理员': 'success',
    '普通用户': 'info',
    '新用户': 'warning'
  }
  return typeMap[roleName] || 'info'
}

// 提交权限申请
const submitApplication = async () => {
  try {
    await applicationFormRef.value.validate()
    
    applicationLoading.value = true
    
    // 这里应该调用API提交申请
    // await submitPermissionApplication(applicationForm)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('权限申请已提交，请等待管理员审核')
    showApplicationDialog.value = false
    
    // 重置表单
    applicationFormRef.value.resetFields()
    Object.assign(applicationForm, {
      type: '',
      description: ''
    })
    
  } catch (error) {
    console.error('提交申请失败:', error)
    ElMessage.error('提交申请失败，请重试')
  } finally {
    applicationLoading.value = false
  }
}

// 获取组织信息
const fetchOrganizationInfo = async () => {
  try {
    if (!userStore.userInfo?.organization_id) {
      console.warn('用户未分配组织')
      return
    }

    // 调用API获取组织信息
    const response = await fetch(`/api/v1/organizations/${userStore.userInfo.organization_id}`, {
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const orgData = await response.json()
      organizationInfo.value = orgData
    } else {
      console.error('获取组织信息失败:', response.status)
      // 如果API失败，显示基本信息
      organizationInfo.value = {
        name: '未知组织',
        level: 0,
        path: '/'
      }
    }
  } catch (error) {
    console.error('获取组织信息失败:', error)
    // 错误时显示基本信息
    organizationInfo.value = {
      name: '获取失败',
      level: 0,
      path: '/'
    }
  }
}

onMounted(() => {
  fetchOrganizationInfo()
})
</script>

<style scoped>
.profile-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.profile-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.profile-content {
  padding: 20px 0;
}

.avatar-section {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.user-avatar {
  background: linear-gradient(45deg, #409eff, #67c23a);
  color: white;
  font-size: 48px;
  font-weight: bold;
}

.user-name {
  margin: 15px 0 5px 0;
  color: #303133;
  font-size: 24px;
}

.user-username {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.info-section {
  margin-bottom: 30px;
}

.info-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.role-tag {
  margin: 0;
}

.permissions-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.permission-tag {
  margin: 0;
}

.no-permissions {
  color: #909399;
  font-style: italic;
}

.action-section {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
