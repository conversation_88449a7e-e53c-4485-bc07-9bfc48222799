<template>
  <div class="organizations-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>组织架构管理</span>
          <el-button type="primary" @click="handleAdd">添加组织</el-button>
        </div>
      </template>
      
      <el-tree
        :data="organizationTree"
        :props="treeProps"
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
      >
        <template #default="{ node, data }">
          <span class="custom-tree-node">
            <span>{{ node.label }} ({{ getLevelName(data.level) }})</span>
            <span>
              <el-button size="mini" @click="handleEdit(data)">编辑</el-button>
              <el-button size="mini" type="danger" @click="handleDelete(data)">删除</el-button>
            </span>
          </span>
        </template>
      </el-tree>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useOrganizationTree } from '@/composables/useOrganizationTree.js'

const {
  organizationTree,
  loading,
  refreshData
} = useOrganizationTree({
  autoLoad: true,
  enableCache: true
})
const treeProps = {
  children: 'children',
  label: 'name'
}

const handleAdd = () => {
  ElMessage.info('添加组织功能开发中...')
}

const handleEdit = (data) => {
  ElMessage.info(`编辑组织: ${data.name}`)
}

const handleDelete = (data) => {
  ElMessage.info(`删除组织: ${data.name}`)
}

onMounted(() => {
  // 组织架构数据由useOrganizationTree自动加载
})
</script>

<style scoped>
.organizations-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
