<template>
  <div class="user-stats-enhanced">
    <!-- 总用户数卡片 -->
    <div 
      class="stat-card"
      :class="{ 'selected': selectedCard === 'total' }"
      @click="handleCardClick('total')"
      @dblclick="handleCardDoubleClick('total')"
    >
      <div class="card-icon total">
        <el-icon><User /></el-icon>
      </div>
      <div class="card-content">
        <div class="card-label">总用户数</div>
        <div class="card-value">{{ totalUsers }}</div>
        <div class="card-desc">当前层级及下属层级</div>
      </div>
      <div class="card-indicator" v-if="selectedCard === 'total'">
        <el-icon><Check /></el-icon>
      </div>
    </div>

    <!-- 管理员卡片 -->
    <div 
      class="stat-card"
      :class="{ 'selected': selectedCard === 'admin' }"
      @click="handleCardClick('admin')"
      @dblclick="handleCardDoubleClick('admin')"
    >
      <div class="card-icon admin">
        <el-icon><UserFilled /></el-icon>
      </div>
      <div class="card-content">
        <div class="card-label">管理员</div>
        <div class="card-value">{{ adminUsers }}</div>
        <div class="card-desc">具有管理权限</div>
      </div>
      <div class="card-indicator" v-if="selectedCard === 'admin'">
        <el-icon><Check /></el-icon>
      </div>
    </div>

    <!-- 普通用户卡片 -->
    <div 
      class="stat-card"
      :class="{ 'selected': selectedCard === 'normal' }"
      @click="handleCardClick('normal')"
      @dblclick="handleCardDoubleClick('normal')"
    >
      <div class="card-icon normal">
        <el-icon><Avatar /></el-icon>
      </div>
      <div class="card-content">
        <div class="card-label">普通用户</div>
        <div class="card-value">{{ normalUsers }}</div>
        <div class="card-desc">基础权限用户</div>
      </div>
      <div class="card-indicator" v-if="selectedCard === 'normal'">
        <el-icon><Check /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { User, UserFilled, Avatar, Check } from '@element-plus/icons-vue'

const props = defineProps({
  totalUsers: {
    type: Number,
    default: 0
  },
  adminUsers: {
    type: Number,
    default: 0
  },
  normalUsers: {
    type: Number,
    default: 0
  },
  selectedOrganization: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['card-selected', 'card-opened'])

const selectedCard = ref(null)

// 处理卡片单击选中
const handleCardClick = (cardType) => {
  selectedCard.value = selectedCard.value === cardType ? null : cardType
  emit('card-selected', {
    type: cardType,
    selected: selectedCard.value === cardType,
    organization: props.selectedOrganization
  })
}

// 处理卡片双击打开
const handleCardDoubleClick = (cardType) => {
  emit('card-opened', {
    type: cardType,
    organization: props.selectedOrganization,
    data: {
      total: props.totalUsers,
      admin: props.adminUsers,
      normal: props.normalUsers
    }
  })
}

// 重置选中状态
const resetSelection = () => {
  selectedCard.value = null
}

// 暴露方法给父组件
defineExpose({
  resetSelection
})
</script>

<style scoped>
.user-stats-enhanced {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  margin-top: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 16px;
  min-width: 200px;
  flex: 1;
  border: 2px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  user-select: none;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-card.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
}

.stat-card.selected::before {
  transform: scaleX(1);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.admin {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.normal {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-content {
  flex: 1;
}

.card-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-value {
  font-size: 32px;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 500;
}

.card-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  animation: checkmark 0.3s ease;
}

@keyframes checkmark {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-stats-enhanced {
    flex-direction: column;
  }
  
  .stat-card {
    min-width: auto;
  }
  
  .card-value {
    font-size: 28px;
  }
}
</style>
