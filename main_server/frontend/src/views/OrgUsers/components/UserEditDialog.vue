<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑用户' : '添加用户'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户姓名" prop="full_name">
            <el-input
              v-model="formData.full_name"
              placeholder="请输入用户姓名"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formData.username"
              placeholder="请输入用户名"
              clearable
              :disabled="isEdit"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="formData.email"
              placeholder="请输入邮箱地址"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input
              v-model="formData.phone"
              placeholder="请输入手机号"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属组织" prop="organization_id">
            <el-select
              v-model="formData.organization_id"
              placeholder="请选择所属组织"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="org in organizations"
                :key="org.id"
                :label="getOrgDisplayName(org)"
                :value="org.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户状态" prop="is_active">
            <el-switch
              v-model="formData.is_active"
              active-text="激活"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="角色权限" prop="roles">
        <el-checkbox-group v-model="formData.roles">
          <el-checkbox 
            v-for="role in availableRoles" 
            :key="role.value" 
            :label="role.value"
            :disabled="role.disabled"
          >
            {{ role.label }}
          </el-checkbox>
        </el-checkbox-group>
        <div class="role-hint">
          <el-text size="small" type="warning">
            注意：超级管理员是系统内置角色，不能随意分配给其他用户
          </el-text>
        </div>
      </el-form-item>

      <el-form-item label="密码" prop="password" v-if="!isEdit">
        <el-input
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          show-password
          clearable
        />
      </el-form-item>

      <el-form-item label="确认密码" prop="confirmPassword" v-if="!isEdit">
        <el-input
          v-model="formData.confirmPassword"
          type="password"
          placeholder="请再次输入密码"
          show-password
          clearable
        />
      </el-form-item>

      <el-form-item label="备注" prop="notes">
        <el-input
          v-model="formData.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '保存' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: () => ({})
  },
  organizations: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save'])

// 响应式数据
const formRef = ref(null)
const saving = ref(false)

const formData = reactive({
  id: null,
  username: '',
  full_name: '',
  email: '',
  phone: '',
  organization_id: null,
  roles: ['普通用户'],
  is_active: true,
  password: '',
  confirmPassword: '',
  notes: ''
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => {
  return props.user && props.user.id
})

const availableRoles = computed(() => {
  // 🔒 安全策略：超级管理员是系统内置角色，严禁分配给其他用户
  // 在前端UI层面完全移除超级管理员选项，确保无法通过界面操作分配此角色
  const baseRoles = [
    { label: '普通用户', value: '普通用户', disabled: false },
    { label: '管理员', value: '管理员', disabled: false }
  ]

  // 🚫 完全移除超级管理员选项，不在任何情况下显示
  // 这是第一层安全防护：前端UI限制
  console.log('🔒 角色选择器已过滤超级管理员选项，当前可选角色:', baseRoles.map(r => r.value))

  return baseRoles
})

// 表单验证规则
const formRules = reactive({
  full_name: [
    { required: true, message: '请输入用户姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  organization_id: [
    { required: true, message: '请选择所属组织', trigger: 'change' }
  ],
  roles: [
    { required: true, message: '请选择至少一个角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== formData.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

// 方法
const getOrgDisplayName = (org) => {
  const levelNames = ['集团总部', '大区', '分公司', '部门', '小组']
  const levelName = levelNames[org.level] || '未知'
  return `${org.name} (${levelName})`
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    username: '',
    full_name: '',
    email: '',
    phone: '',
    organization_id: null,
    roles: ['普通用户'],
    is_active: true,
    password: '',
    confirmPassword: '',
    notes: ''
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const loadUserData = () => {
  if (props.user && props.user.id) {
    // 🔒 安全策略：严格过滤超级管理员角色
    let userRoles = props.user.roles || ['普通用户']

    // 🚫 强制过滤超级管理员角色，无论何种情况都不允许在编辑界面显示
    const originalRolesCount = userRoles.length
    userRoles = userRoles.filter(role => role !== '超级管理员')

    // 记录安全日志
    if (originalRolesCount !== userRoles.length) {
      console.warn('🔒 安全过滤：已从用户角色中移除超级管理员权限', {
        userId: props.user.id,
        username: props.user.username,
        originalRoles: props.user.roles,
        filteredRoles: userRoles
      })
    }

    // 如果过滤后没有其他角色，默认给普通用户角色
    if (userRoles.length === 0) {
      userRoles = ['普通用户']
      console.log('🔒 安全默认：用户角色为空，已设置为普通用户')
    }

    Object.assign(formData, {
      id: props.user.id,
      username: props.user.username || '',
      full_name: props.user.full_name || '',
      email: props.user.email || '',
      phone: props.user.phone || '',
      organization_id: props.user.organization_id || null,
      roles: userRoles,
      is_active: props.user.is_active !== undefined ? props.user.is_active : true,
      notes: props.user.notes || ''
    })
  } else {
    resetForm()
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...formData }
    
    // 如果是编辑模式，移除密码字段
    if (isEdit.value) {
      delete saveData.password
      delete saveData.confirmPassword
    }
    
    // 🔒 第二层安全防护：保存前的严格验证
    if (saveData.roles.includes('超级管理员')) {
      // 记录安全事件
      console.error('🚨 安全警告：检测到尝试分配超级管理员权限', {
        userId: saveData.id,
        username: saveData.username,
        attemptedRoles: saveData.roles,
        timestamp: new Date().toISOString()
      })

      ElMessage.error({
        message: '🔒 安全限制：超级管理员是系统内置角色，严禁分配给其他用户',
        type: 'error',
        duration: 5000
      })
      saving.value = false
      return
    }

    // 🔒 额外安全检查：确保角色列表中没有任何非法角色
    const allowedRoles = ['普通用户', '管理员']
    const invalidRoles = saveData.roles.filter(role => !allowedRoles.includes(role))
    if (invalidRoles.length > 0) {
      console.error('🚨 安全警告：检测到非法角色', {
        userId: saveData.id,
        username: saveData.username,
        invalidRoles: invalidRoles,
        timestamp: new Date().toISOString()
      })

      ElMessage.error({
        message: `🔒 安全限制：检测到非法角色 [${invalidRoles.join(', ')}]`,
        type: 'error',
        duration: 5000
      })
      saving.value = false
      return
    }
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('save', saveData)
    ElMessage.success(isEdit.value ? '用户更新成功' : '用户创建成功')
    
    dialogVisible.value = false
    resetForm()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    saving.value = false
  }
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadUserData()
  }
})

watch(() => props.user, () => {
  if (props.modelValue) {
    loadUserData()
  }
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.role-hint {
  margin-top: 8px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-checkbox) {
  margin-right: 0;
}
</style>
