<template>
  <div class="user-details-tab">
    <!-- 标签页头部 -->
    <div class="tab-header">
      <div class="header-left">
        <el-button 
          type="primary" 
          :icon="ArrowLeft" 
          @click="handleBack"
          class="back-button"
        >
          返回
        </el-button>
        <div class="tab-title">
          <h3>{{ getTabTitle() }}</h3>
          <div class="tab-context">
            <el-tag type="info" size="small">
              {{ organization?.name || '未选择组织' }}
            </el-tag>
            <span class="context-text">{{ getContextText() }}</span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <el-button 
          type="success" 
          :icon="Plus" 
          @click="handleAddUser"
          v-if="canAddUser"
        >
          添加用户
        </el-button>
        <el-button 
          :icon="Refresh" 
          @click="handleRefresh"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="users-content">
      <el-table
        :data="filteredUsers"
        style="width: 100%"
        v-loading="loading"
        stripe
        border
        height="600"
        :default-sort="{ prop: 'full_name', order: 'ascending' }"
      >
        <el-table-column prop="full_name" label="姓名" min-width="150" show-overflow-tooltip />
        <el-table-column prop="phone" label="联系方式" min-width="140" show-overflow-tooltip />
        <el-table-column prop="email" label="邮箱" min-width="220" show-overflow-tooltip />
        <el-table-column prop="role_name" label="层级角色" min-width="130" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getRoleTagType(row.role_name)"
              size="small"
            >
              {{ row.role_name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="organization_name" label="所属组织" min-width="200">
          <template #default="{ row }">
            <div class="organization-cell">
              <span class="organization-name">{{ row.organization_name }}</span>
              <span v-if="row.organization_level" class="organization-level">
                ({{ getLevelName(row.organization_level) }})
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="90" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.is_active ? 'success' : 'danger'"
              size="small"
            >
              {{ row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="200" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <!-- 编辑按钮 -->
              <el-button
                size="small"
                type="primary"
                @click="handleEditUser(row)"
                v-if="canEditUser(row)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              
              <!-- 查看按钮 -->
              <el-button
                size="small"
                @click="handleViewUser(row)"
                v-if="!canEditUser(row)"
              >
                <el-icon><View /></el-icon>
                查看
              </el-button>

              <!-- 重置密码按钮 -->
              <el-button
                size="small"
                type="warning"
                @click="handleResetPassword(row)"
                v-if="canResetPassword(row)"
              >
                <el-icon><Key /></el-icon>
                重置密码
              </el-button>

              <!-- 删除按钮 -->
              <el-button
                size="small"
                type="danger"
                @click="handleDeleteUser(row)"
                v-if="canDeleteUser(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalUsers"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Refresh, Edit, View, Key, Delete } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const props = defineProps({
  tabType: {
    type: String,
    required: true,
    validator: (value) => ['total', 'admin', 'normal'].includes(value)
  },
  organization: {
    type: Object,
    default: null
  },
  users: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['back', 'refresh', 'add-user', 'edit-user', 'delete-user'])

const userStore = useUserStore()
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const filteredUsers = computed(() => {
  let users = props.users || []
  
  // 根据标签页类型过滤用户
  switch (props.tabType) {
    case 'admin':
      users = users.filter(user => 
        user.role_name === '管理员' || 
        user.role_name === '超级管理员' || 
        user.role_name === '全域管理员'
      )
      break
    case 'normal':
      users = users.filter(user => user.role_name === '普通用户')
      break
    case 'total':
    default:
      // 显示所有用户
      break
  }
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return users.slice(start, end)
})

const totalUsers = computed(() => {
  let users = props.users || []
  
  switch (props.tabType) {
    case 'admin':
      return users.filter(user => 
        user.role_name === '管理员' || 
        user.role_name === '超级管理员' || 
        user.role_name === '全域管理员'
      ).length
    case 'normal':
      return users.filter(user => user.role_name === '普通用户').length
    case 'total':
    default:
      return users.length
  }
})

const canAddUser = computed(() => {
  const currentUser = userStore.user
  return currentUser && (
    currentUser.role_name === '全域管理员' ||
    currentUser.role_name === '超级管理员' ||
    currentUser.role_name === '管理员'
  )
})

// 方法
const getTabTitle = () => {
  switch (props.tabType) {
    case 'total':
      return '全部用户详情'
    case 'admin':
      return '管理员详情'
    case 'normal':
      return '普通用户详情'
    default:
      return '用户详情'
  }
}

const getContextText = () => {
  const orgName = props.organization?.name || '未选择组织'
  return `当前查看：${orgName} 及其下属层级`
}

const getRoleTagType = (roleName) => {
  switch (roleName) {
    case '全域管理员':
      return 'danger'
    case '超级管理员':
      return 'warning'
    case '管理员':
      return 'primary'
    case '普通用户':
      return 'success'
    default:
      return 'info'
  }
}

const getLevelName = (level) => {
  const levelNames = {
    0: '集团总部',
    1: '大区',
    2: '分公司',
    3: '部门',
    4: '小组'
  }
  return levelNames[level] || `${level}级`
}

// 权限检查方法
const canEditUser = (user) => {
  const currentUser = userStore.user
  if (!currentUser) return false
  
  // 全域管理员可以编辑所有用户（除了自己的某些字段）
  if (currentUser.role_name === '全域管理员') {
    return true
  }
  
  // 超级管理员不能编辑全域管理员
  if (currentUser.role_name === '超级管理员') {
    return user.role_name !== '全域管理员'
  }
  
  // 管理员只能编辑同级或下级组织的用户
  if (currentUser.role_name === '管理员') {
    return user.organization_id === currentUser.organization_id ||
           isSubordinateOrganization(user.organization_id, currentUser.organization_id)
  }
  
  // 普通用户只能编辑自己
  return user.id === currentUser.id
}

const canResetPassword = (user) => {
  const currentUser = userStore.user
  if (!currentUser) return false
  
  // 只有管理员及以上可以重置他人密码
  return (currentUser.role_name === '全域管理员' ||
          currentUser.role_name === '超级管理员' ||
          currentUser.role_name === '管理员') && 
         canEditUser(user)
}

const canDeleteUser = (user) => {
  const currentUser = userStore.user
  if (!currentUser) return false
  
  // 不能删除自己
  if (user.id === currentUser.id) return false
  
  // 全域管理员不能被删除
  if (user.role_name === '全域管理员') return false
  
  return canEditUser(user)
}

const isSubordinateOrganization = (targetOrgId, currentOrgId) => {
  // 这里需要实现组织层级检查逻辑
  // 暂时返回false，实际应该检查组织树结构
  return false
}

// 事件处理方法
const handleBack = () => {
  emit('back')
}

const handleRefresh = () => {
  emit('refresh')
}

const handleAddUser = () => {
  emit('add-user')
}

const handleEditUser = (user) => {
  emit('edit-user', user)
}

const handleViewUser = (user) => {
  // 查看用户详情（只读模式）
  emit('edit-user', { ...user, readonly: true })
}

const handleResetPassword = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 "${user.full_name}" 的密码吗？`,
      '重置密码确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 这里调用重置密码API
    ElMessage.success('密码重置成功')
  } catch {
    // 用户取消操作
  }
}

const handleDeleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.full_name}" 吗？此操作不可恢复！`,
      '删除用户确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error',
      }
    )
    
    emit('delete-user', user)
  } catch {
    // 用户取消操作
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}
</script>

<style scoped>
.user-details-tab {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  margin-bottom: 20px;
  border: 1px solid #e2e8f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  border-radius: 8px;
}

.tab-title h3 {
  margin: 0 0 8px 0;
  color: #1e293b;
  font-size: 20px;
  font-weight: 600;
}

.tab-context {
  display: flex;
  align-items: center;
  gap: 12px;
}

.context-text {
  color: #64748b;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.users-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.organization-cell {
  display: flex;
  flex-direction: column;
}

.organization-name {
  font-weight: 500;
}

.organization-level {
  font-size: 12px;
  color: #64748b;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
</style>
