<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑组织' : '添加组织'"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="组织名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入组织名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="上级组织" prop="parent_id">
        <el-select
          v-model="formData.parent_id"
          placeholder="请选择上级组织（可选）"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="org in availableParentOrgs"
            :key="org.id"
            :label="getOrgDisplayName(org)"
            :value="org.id"
          />
        </el-select>
        <div class="form-hint">
          <el-text size="small" type="info">
            不选择上级组织将创建为顶级组织
          </el-text>
        </div>
      </el-form-item>

      <el-form-item label="组织层级" prop="level">
        <el-select
          v-model="formData.level"
          placeholder="请选择组织层级"
          style="width: 100%"
          :disabled="!!formData.parent_id"
        >
          <el-option
            v-for="(levelName, index) in levelNames"
            :key="index"
            :label="`${levelName} (第${index}级)`"
            :value="index"
            :disabled="!isLevelAvailable(index)"
          />
        </el-select>
        <div class="form-hint">
          <el-text size="small" type="info">
            选择上级组织后将自动设置层级
          </el-text>
        </div>
      </el-form-item>

      <el-form-item label="组织代码" prop="code">
        <el-input
          v-model="formData.code"
          placeholder="请输入组织代码（可选）"
          clearable
        />
        <div class="form-hint">
          <el-text size="small" type="info">
            组织代码用于系统内部标识，建议使用英文字母和数字
          </el-text>
        </div>
      </el-form-item>

      <el-form-item label="组织描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入组织描述（可选）"
        />
      </el-form-item>

      <el-form-item label="排序权重" prop="sort_order">
        <el-input-number
          v-model="formData.sort_order"
          :min="0"
          :max="999"
          placeholder="排序权重"
          style="width: 100%"
        />
        <div class="form-hint">
          <el-text size="small" type="info">
            数值越小排序越靠前，默认为0
          </el-text>
        </div>
      </el-form-item>

      <el-form-item label="组织状态" prop="is_active">
        <el-switch
          v-model="formData.is_active"
          active-text="启用"
          inactive-text="禁用"
        />
        <div class="form-hint">
          <el-text size="small" type="warning">
            禁用组织将影响该组织下所有用户的访问权限
          </el-text>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '保存' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  organization: {
    type: Object,
    default: () => ({})
  },
  parentOrganizations: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save'])

// 响应式数据
const formRef = ref(null)
const saving = ref(false)

const formData = reactive({
  id: null,
  name: '',
  parent_id: null,
  level: 0,
  code: '',
  description: '',
  sort_order: 0,
  is_active: true
})

// 组织层级名称
const levelNames = ['集团总部', '大区', '分公司', '部门', '小组']

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => {
  return props.organization && props.organization.id
})

const availableParentOrgs = computed(() => {
  // 过滤掉自己和自己的子组织，避免循环引用
  return props.parentOrganizations.filter(org => {
    if (isEdit.value && org.id === props.organization.id) {
      return false
    }
    // 这里可以添加更复杂的逻辑来过滤子组织
    return true
  })
})

// 表单验证规则
const formRules = reactive({
  name: [
    { required: true, message: '请输入组织名称', trigger: 'blur' },
    { min: 2, max: 50, message: '组织名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择组织层级', trigger: 'change' }
  ],
  code: [
    { pattern: /^[a-zA-Z0-9_-]*$/, message: '组织代码只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', message: '排序权重必须是数字', trigger: 'blur' }
  ]
})

// 方法
const getOrgDisplayName = (org) => {
  const levelName = levelNames[org.level] || '未知'
  return `${org.name} (${levelName})`
}

const isLevelAvailable = (level) => {
  // 根据业务规则判断层级是否可用
  // 这里可以添加更复杂的逻辑
  return level >= 0 && level < levelNames.length
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    parent_id: null,
    level: 0,
    code: '',
    description: '',
    sort_order: 0,
    is_active: true
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const loadOrgData = () => {
  if (props.organization && props.organization.id) {
    Object.assign(formData, {
      id: props.organization.id,
      name: props.organization.name || '',
      parent_id: props.organization.parent_id || null,
      level: props.organization.level || 0,
      code: props.organization.code || '',
      description: props.organization.description || '',
      sort_order: props.organization.sort_order || 0,
      is_active: props.organization.is_active !== undefined ? props.organization.is_active : true
    })
  } else {
    resetForm()
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...formData }
    
    // 如果选择了上级组织，自动设置层级
    if (saveData.parent_id) {
      const parentOrg = props.parentOrganizations.find(org => org.id === saveData.parent_id)
      if (parentOrg) {
        saveData.level = parentOrg.level + 1
      }
    }
    
    // 验证层级限制
    if (saveData.level >= levelNames.length) {
      ElMessage.error(`组织层级不能超过${levelNames.length - 1}级`)
      saving.value = false
      return
    }
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('save', saveData)
    ElMessage.success(isEdit.value ? '组织更新成功' : '组织创建成功')
    
    dialogVisible.value = false
    resetForm()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    saving.value = false
  }
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadOrgData()
  }
})

watch(() => props.organization, () => {
  if (props.modelValue) {
    loadOrgData()
  }
})

// 监听上级组织变化，自动设置层级
watch(() => formData.parent_id, (newParentId) => {
  if (newParentId) {
    const parentOrg = props.parentOrganizations.find(org => org.id === newParentId)
    if (parentOrg) {
      formData.level = parentOrg.level + 1
    }
  } else {
    formData.level = 0
  }
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-hint {
  margin-top: 4px;
}
</style>
