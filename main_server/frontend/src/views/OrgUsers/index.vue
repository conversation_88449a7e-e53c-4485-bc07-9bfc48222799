<template>
  <div class="org-users-container">


    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>组织与用户管理</h2>
        <p class="header-desc">统一管理组织架构和用户信息，支持拖拽调整和快速编辑</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleAddOrganization">
          <el-icon><Plus /></el-icon>
          添加组织
        </el-button>
        <el-button type="success" @click="handleAddUser">
          <el-icon><UserFilled /></el-icon>
          添加用户
        </el-button>
      </div>
    </div>



    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：组织架构树 -->
      <div class="tree-panel">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>组织架构</span>
              <el-button size="small" @click="expandAll">
                {{ allExpanded ? '收起全部' : '展开全部' }}
              </el-button>
            </div>
          </template>
          




          <el-tree
            ref="orgTreeRef"
            :data="computedTreeData"
            :props="treeProps"
            node-key="id"
            :default-expanded-keys="defaultExpandedKeys"
            :expand-on-click-node="false"
            :allow-drop="allowDrop"
            :allow-drag="allowDrag"
            draggable
            @node-drop="handleNodeDrop"
            @node-click="handleNodeClick"
            class="fast-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node" :data-type="data.type" @dblclick="handleNodeDoubleClick(node, data)">
                <div class="node-info">
                  <el-icon class="org-icon">
                    <OfficeBuilding />
                  </el-icon>
                  <span class="node-label">
                    {{ data.name }}
                    <span class="org-type">
                      ({{ getLevelName(data.level) }})
                    </span>
                    <span class="org-stats">
                      - 管理员: {{ data.adminCount || 0 }}人, 用户: {{ data.normalUserCount || 0 }}人
                    </span>
                  </span>
                </div>

                <div class="node-actions">
                  <el-button size="small" text @click.stop="handleEditOrg(data)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button size="small" text type="danger" @click.stop="handleDeleteOrg(data)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </el-card>
      </div>

      <!-- 右侧：详情面板 -->
      <div class="detail-panel">
        <!-- 详情卡片 -->
        <el-card class="detail-card">
          <template #header>
            <div class="card-header">
              <span>{{ selectedNode ? (selectedNode.type === 'organization' ? '组织详情' : '用户详情') : '选择组织或用户查看详情' }}</span>
            </div>
          </template>
          
          <div v-if="!selectedNode" class="empty-state">
            <el-empty description="请在左侧选择组织或用户查看详情" />
          </div>
          
          <!-- 组织详情 -->
          <div v-else-if="selectedNode.type === 'organization'" class="org-detail">
            <!-- 标签页容器 -->
            <el-tabs v-model="activeTabName" @tab-click="handleTabClick" class="org-detail-tabs">
              <!-- 基本信息标签页 -->
              <el-tab-pane label="基本信息" name="basic">
                <div class="detail-section">
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="组织名称">{{ selectedNode.name }}</el-descriptions-item>
                    <el-descriptions-item label="组织层级">{{ getLevelName(selectedNode.level) }}</el-descriptions-item>
                    <el-descriptions-item label="创建时间">{{ formatDate(selectedNode.created_at) }}</el-descriptions-item>
                    <el-descriptions-item label="更新时间">{{ formatDate(selectedNode.updated_at) }}</el-descriptions-item>
                  </el-descriptions>
                </div>

                <div class="detail-section">
                  <h4>用户统计</h4>
                  <div class="user-stats-enhanced">
                    <!-- 总用户数卡片 -->
                    <div
                      class="stat-card"
                      :class="{ 'selected': selectedStatsCard === 'total' }"
                      @click="handleStatsCardClick('total')"
                    >
                      <div class="card-icon total">
                        <el-icon><User /></el-icon>
                      </div>
                      <div class="card-content">
                        <div class="card-label">总用户数</div>
                        <div class="card-value">{{ getOrgUserCount(selectedNode.id) }}</div>
                        <div class="card-desc">当前层级及下属层级</div>
                      </div>
                      <div class="card-indicator" v-if="selectedStatsCard === 'total'">
                        <el-icon><Check /></el-icon>
                      </div>
                    </div>

                    <!-- 管理员卡片 -->
                    <div
                      class="stat-card"
                      :class="{ 'selected': selectedStatsCard === 'admin' }"
                      @click="handleStatsCardClick('admin')"
                    >
                      <div class="card-icon admin">
                        <el-icon><UserFilled /></el-icon>
                      </div>
                      <div class="card-content">
                        <div class="card-label">管理员</div>
                        <div class="card-value">{{ getOrgAdminCount(selectedNode.id) }}</div>
                        <div class="card-desc">具有管理权限</div>
                      </div>
                      <div class="card-indicator" v-if="selectedStatsCard === 'admin'">
                        <el-icon><Check /></el-icon>
                      </div>
                    </div>

                    <!-- 普通用户卡片 -->
                    <div
                      class="stat-card"
                      :class="{ 'selected': selectedStatsCard === 'normal' }"
                      @click="handleStatsCardClick('normal')"
                    >
                      <div class="card-icon normal">
                        <el-icon><Avatar /></el-icon>
                      </div>
                      <div class="card-content">
                        <div class="card-label">普通用户</div>
                        <div class="card-value">{{ getOrgNormalUserCount(selectedNode.id) }}</div>
                        <div class="card-desc">基础权限用户</div>
                      </div>
                      <div class="card-indicator" v-if="selectedStatsCard === 'normal'">
                        <el-icon><Check /></el-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 总用户列表标签页 -->
              <el-tab-pane label="总用户" name="total">
                <div class="user-list-content">
                  <div class="list-header">
                    <h4>{{ selectedNode.name }} - 总用户列表 ({{ tabUsers.length }}人)</h4>
                    <div class="list-actions">
                      <el-button size="small" @click="handleTabRefresh">
                        <el-icon><Refresh /></el-icon>
                        刷新
                      </el-button>
                      <el-button type="primary" size="small" @click="handleTabAddUser">
                        <el-icon><Plus /></el-icon>
                        添加用户
                      </el-button>
                    </div>
                  </div>
                  <el-table :data="tabUsers" style="width: 100%" stripe v-loading="tabLoading">
                    <el-table-column prop="id" label="ID" width="80" />
                    <el-table-column prop="full_name" label="姓名" min-width="120" />
                    <el-table-column prop="role_name" label="角色" min-width="100" />
                    <el-table-column prop="organization_name" label="组织" min-width="150" />
                    <el-table-column prop="email" label="邮箱" min-width="200" />
                    <el-table-column prop="phone" label="电话" min-width="120" />
                    <el-table-column label="操作" width="150">
                      <template #default="{ row }">
                        <el-button size="small" @click="handleTabEditUser(row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="handleTabDeleteUser(row)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>

              <!-- 管理员列表标签页 -->
              <el-tab-pane label="管理员" name="admin">
                <div class="user-list-content">
                  <div class="list-header">
                    <h4>{{ selectedNode.name }} - 管理员列表 ({{ tabUsers.length }}人)</h4>
                    <div class="list-actions">
                      <el-button size="small" @click="handleTabRefresh">
                        <el-icon><Refresh /></el-icon>
                        刷新
                      </el-button>
                      <el-button type="primary" size="small" @click="handleTabAddUser">
                        <el-icon><Plus /></el-icon>
                        添加用户
                      </el-button>
                    </div>
                  </div>
                  <el-table :data="tabUsers" style="width: 100%" stripe v-loading="tabLoading">
                    <el-table-column prop="id" label="ID" width="80" />
                    <el-table-column prop="full_name" label="姓名" min-width="120" />
                    <el-table-column prop="role_name" label="角色" min-width="100" />
                    <el-table-column prop="organization_name" label="组织" min-width="150" />
                    <el-table-column prop="email" label="邮箱" min-width="200" />
                    <el-table-column prop="phone" label="电话" min-width="120" />
                    <el-table-column label="操作" width="150">
                      <template #default="{ row }">
                        <el-button size="small" @click="handleTabEditUser(row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="handleTabDeleteUser(row)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>

              <!-- 普通用户列表标签页 -->
              <el-tab-pane label="普通用户" name="normal">
                <div class="user-list-content">
                  <div class="list-header">
                    <h4>{{ selectedNode.name }} - 普通用户列表 ({{ tabUsers.length }}人)</h4>
                    <div class="list-actions">
                      <el-button size="small" @click="handleTabRefresh">
                        <el-icon><Refresh /></el-icon>
                        刷新
                      </el-button>
                      <el-button type="primary" size="small" @click="handleTabAddUser">
                        <el-icon><Plus /></el-icon>
                        添加用户
                      </el-button>
                    </div>
                  </div>
                  <el-table :data="tabUsers" style="width: 100%" stripe v-loading="tabLoading">
                    <el-table-column prop="id" label="ID" width="80" />
                    <el-table-column prop="full_name" label="姓名" min-width="120" />
                    <el-table-column prop="role_name" label="角色" min-width="100" />
                    <el-table-column prop="organization_name" label="组织" min-width="150" />
                    <el-table-column prop="email" label="邮箱" min-width="200" />
                    <el-table-column prop="phone" label="电话" min-width="120" />
                    <el-table-column label="操作" width="150">
                      <template #default="{ row }">
                        <el-button size="small" @click="handleTabEditUser(row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="handleTabDeleteUser(row)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
          
          <!-- 移除用户详情显示，因为树中不再显示用户节点 -->
          <div v-else class="no-user-detail">
            <el-empty description="组织架构树仅显示组织结构，用户详情请在右侧用户构成详情中查看" />
          </div>
        </el-card>


      </div>
    </div>

    <!-- 用户编辑对话框 -->
    <UserEditDialog
      v-model="showUserEditDialog"
      :user="editingUser"
      :organizations="flatOrganizations"
      @save="handleSaveUser"
    />

    <!-- 组织编辑对话框 -->
    <OrgEditDialog
      v-model="showOrgEditDialog"
      :organization="editingOrg"
      :parent-organizations="flatOrganizations"
      @save="handleSaveOrg"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  UserFilled,
  Search,
  Edit,
  Delete,
  OfficeBuilding,
  User,
  Avatar,
  Refresh,
  View,
  Link,
  Check
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import UserEditDialog from './components/UserEditDialog.vue'
import OrgEditDialog from './components/OrgEditDialog.vue'
import { getUsers, getUserCount, deleteUser, getOrganizationsWithUsers } from '@/api/users'
import { useOrgUsersAdapter } from '@/composables/useOrgUsersAdapter.js'

// 使用OrgUsers数据适配层
const {
  loading: orgAdapterLoading,
  organizationTreeData: adapterTreeData,
  flatOrganizations: adapterFlatOrgs,
  organizationsResponse: adapterOrgsResponse,  // 🔧 修复：导入适配层的organizationsResponse
  selectedNode: adapterSelectedNode,
  searchText: adapterSearchText,
  loadOrganizationsWithUsers: adapterLoadOrgs,
  refreshData: adapterRefreshData,
  getLevelName: adapterGetLevelName,
  canManageOrganization: adapterCanManage
} = useOrgUsersAdapter({
  autoLoad: false, // 手动控制加载
  enableCache: true
})

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const filterRole = ref('')
const filterStatus = ref('')
const allExpanded = ref(false)
const selectedNode = ref(null)
const showUserEditDialog = ref(false)
const showOrgEditDialog = ref(false)
const editingUser = ref(null)
const editingOrg = ref(null)
const orgTreeRef = ref(null)

// 新增：标签页功能相关状态
const activeTabName = ref('basic') // 'basic', 'total', 'admin', 'normal'
const tabUsers = ref([])
const tabLoading = ref(false)
const selectedStatsCard = ref(null)

// 用户列表相关数据
const usersLoading = ref(false)
const userSearchKeyword = ref('')
const allUsersList = ref([])
const usersPagination = reactive({
  currentPage: 1,
  pageSize: 15,  // 默认每页15条
  total: 0
})

// 新的筛选选项数据结构 - 智能联动优化版
const filterOptions = reactive({
  displayScope: '全部用户',           // 显示范围：全部用户, 当前组织, 当前组织及子组织
  orgLevel: '按层级分组',             // 组织层级：按层级分组, 不区分层级, 指定层级
  roleType: '按角色分组',             // 角色类型：按角色分组, 不区分角色, 指定角色
  specificOrg: null,                 // 指定的组织ID
  specificRole: null                 // 指定的角色
})

// 筛选相关数据
const currentUserPermissions = ref({})
const filteredUsersList = ref([])  // 筛选后的用户列表
const availableOrganizations = ref([])  // 可选择的组织列表
const availableRoles = ref([  // 可选择的角色列表
  { value: 'super_admin', label: '超级管理员' },
  { value: 'admin', label: '管理员' },
  { value: 'normal_user', label: '普通用户' },
  { value: 'guest', label: '访客' }
])

// 组织架构数据
const organizationTreeData = ref([])
const flatOrganizations = ref([])
const organizationsResponse = ref([]) // 存储原始的嵌套组织响应
const allUsers = ref([])
const userStats = ref({
  total_users: 0,
  active_users: 0,
  inactive_users: 0,
  role_statistics: []
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 数据加载方法
const loadUserStats = async () => {
  try {
    const response = await getUserCount()
    userStats.value = response
  } catch (error) {
    console.error('加载用户统计失败:', error)
    ElMessage.error('加载用户统计失败')
  }
}

const loadUsers = async () => {
  try {
    console.log('loadUsers - 开始加载用户数据')
    const response = await getUsers({ page: 1, size: 500 }) // 加载所有用户（后端限制最大1000）
    console.log('loadUsers - API响应:', response)

    // 🔧 修复：处理API中间件包装的响应格式
    let actualData = response
    if (response && response.success && response.data) {
      // API中间件包装格式：{success: true, data: {...}, message: "", error_code: null}
      actualData = response.data
      console.log('🔧 loadUsers - 检测到API中间件包装格式，提取data字段:', actualData)
    }

    // 处理分页响应，提取用户数组
    if (actualData && actualData.users && Array.isArray(actualData.users)) {
      allUsers.value = actualData.users
      console.log('loadUsers - 设置allUsers.value (users):', allUsers.value.length, '个用户')
      console.log('loadUsers - 前5个用户示例:', allUsers.value.slice(0, 5))
    } else if (actualData && actualData.items) {
      allUsers.value = actualData.items
      console.log('loadUsers - 设置allUsers.value (items):', allUsers.value.length, '个用户')
      console.log('loadUsers - 前5个用户示例:', allUsers.value.slice(0, 5))
    } else if (Array.isArray(actualData)) {
      allUsers.value = actualData
      console.log('loadUsers - 设置allUsers.value (array):', allUsers.value.length, '个用户')
      console.log('loadUsers - 前5个用户示例:', actualData.slice(0, 5))
    } else {
      console.warn('用户数据格式异常:', response)
      allUsers.value = []
    }

    console.log('loadUsers - 最终allUsers.value总数:', allUsers.value.length)
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
    allUsers.value = []
  }
}

// 构建筛选参数 - 智能联动优化版
const buildFilterParams = () => {
  const startTime = performance.now()

  const params = {
    page: usersPagination.currentPage,
    page_size: usersPagination.pageSize
  }

  // 添加搜索关键词
  if (userSearchKeyword.value) {
    params.search = userSearchKeyword.value
  }

  // 🎯 智能组织筛选逻辑 - 新版本
  if (selectedNode.value && selectedNode.value.type === 'organization') {
    // 根据显示范围确定组织筛选策略
    if (filterOptions.displayScope === '当前组织') {
      // 当前组织：仅显示该组织的直属用户
      params.scope = 'current_only'
      params.org_id = selectedNode.value.id
      params.include_children = false
      console.log('🎯 智能筛选：当前组织模式', { org_id: selectedNode.value.id, include_children: false })
    } else if (filterOptions.displayScope === '当前组织及子组织') {
      // 当前组织及子组织：显示该组织及其所有下级组织的用户
      params.scope = 'current_and_sub'
      params.org_id = selectedNode.value.id
      params.include_children = true
      console.log('🎯 智能筛选：子组织模式', { org_id: selectedNode.value.id, include_children: true })
    } else if (filterOptions.displayScope === '全部用户') {
      // 全部用户：显示整个集团的用户
      params.scope = 'all'
      if (selectedNode.value.level > 0) {
        params.org_id = selectedNode.value.id
        params.include_children = true
        console.log('🎯 智能筛选：全部用户模式（非根节点）', { org_id: selectedNode.value.id, include_children: true })
      } else {
        console.log('🎯 智能筛选：全部用户模式（根节点）', { org_id: null, include_children: false })
      }
      // 如果是根节点，不设置organization_id，显示所有用户
    }

    // 如果是指定组织模式，使用指定的组织ID
    if (filterOptions.orgStructure === 'specific' && filterOptions.specificOrg) {
      params.organization_id = filterOptions.specificOrg
      params.include_children = filterOptions.displayScope === 'children'
      console.log('🎯 智能筛选：指定组织模式', { org_id: filterOptions.specificOrg, include_children: params.include_children })
    }
  }

  // 🔧 修复：智能角色筛选逻辑 - 修复role_filter参数问题
  if (filterOptions.roleType === '指定角色' && filterOptions.specificRole) {
    params.role_filter = filterOptions.specificRole
    console.log('🎯 智能筛选：指定角色', { role: filterOptions.specificRole })
  } else if (filterOptions.roleType === '按角色分组') {
    // 🔧 修复：按角色分组时不传递role_filter，让后端返回所有角色的用户
    // params.role_filter = 'by_role' // 删除这行，因为'by_role'不是有效的角色名称
    console.log('🎯 智能筛选：角色分组模式 - 不限制角色')
  } else if (filterOptions.roleType === '不区分角色') {
    // 🔧 修复：不区分角色时也不传递role_filter
    // params.role_filter = 'none' // 删除这行，让后端返回所有用户
    console.log('🎯 智能筛选：不区分角色 - 不限制角色')
  }

  // 智能组织层级筛选逻辑
  if (filterOptions.orgLevel === '按层级分组') {
    // 按层级分组时不传递org_filter
    // params.org_filter = 'by_level' // 等后端实现后再启用
  } else if (filterOptions.orgLevel === '不区分层级') {
    // 不区分层级时也不传递org_filter
    // params.org_filter = 'none'
  }

  // 🎯 智能筛选模式映射 - 根据显示范围、组织结构、角色筛选综合确定
  params.filter_mode = determineFilterMode()

  // 筛选参数构建完成

  return params
}

// 🎯 智能筛选模式确定方法
const determineFilterMode = () => {
  const { displayScope, orgStructure, roleFilter } = filterOptions

  // 🔧 修复：使用后端期望的小写格式
  let baseMode = 'all_with_hierarchy_with_role' // 默认模式

  if (displayScope === 'all') {
    // 全部用户
    baseMode = 'all_with_hierarchy_with_role'
  } else if (displayScope === 'current') {
    // 当前组织
    baseMode = 'all_with_hierarchy_with_role' // 🔧 修复：使用有效的模式
  } else if (displayScope === 'children') {
    // 当前组织及子组织
    baseMode = 'all_with_hierarchy_with_role'
  }

  console.log('🎯 确定筛选模式:', {
    显示范围: displayScope,
    组织结构: orgStructure,
    角色筛选: roleFilter,
    最终模式: baseMode
  })

  return baseMode || 'all_with_hierarchy_with_role' // 默认模式
}

// 加载所有用户列表（用于右侧用户列表显示）
const loadAllUsersList = async () => {
  usersLoading.value = true
  const startTime = performance.now() // 修复：在函数开始处定义startTime

  try {
    const params = buildFilterParams()
    const response = await getUsers(params)

    // 🔧 修复：处理API中间件包装的响应格式
    let actualData = response
    if (response && response.success && response.data) {
      // API中间件包装格式：{success: true, data: {...}, message: "", error_code: null}
      actualData = response.data
      console.log('🔧 检测到API中间件包装格式，提取data字段:', actualData)
    }

    // 🎯 智能数据处理和性能优化
    if (actualData && actualData.users) {
      // 使用批量更新减少响应式触发次数
      const updateStartTime = performance.now()

      allUsersList.value = actualData.users
      usersPagination.total = actualData.total
      filteredUsersList.value = actualData.users

      // 更新当前用户权限信息
      if (actualData.current_user_permissions) {
        currentUserPermissions.value = actualData.current_user_permissions
      }

      const updateEndTime = performance.now()
      console.log('✅ 用户列表数据更新完成:', {
        用户数量: allUsersList.value.length,
        总数: usersPagination.total,
        当前页: usersPagination.currentPage,
        数据更新耗时: `${(updateEndTime - updateStartTime).toFixed(2)}ms`,
        总耗时: `${(updateEndTime - startTime).toFixed(2)}ms`,
        选中组织: selectedNode.value?.name || '无'
      })

      // 🎯 性能检查：如果加载时间超过100ms，发出警告
      const totalTime = updateEndTime - startTime
      if (totalTime > 100) {
        console.warn('⚠️ 用户列表加载时间超过100ms:', `${totalTime.toFixed(2)}ms`)
      }

    } else {
      console.warn('❌ 用户列表数据格式异常:', response)
      allUsersList.value = []
      usersPagination.total = 0
    }
  } catch (error) {
    console.error('❌ 加载用户列表失败:', error)

    // 详细错误处理和用户提示
    let errorMessage = '加载用户列表失败'

    if (error.response) {
      // 服务器响应错误
      const status = error.response.status
      if (status === 403) {
        errorMessage = '没有权限访问该组织的用户数据'
      } else if (status === 404) {
        errorMessage = '请求的组织不存在'
      } else if (status === 500) {
        errorMessage = '服务器内部错误，请稍后重试'
      } else {
        errorMessage = `服务器错误 (${status}): ${error.response.data?.detail || '未知错误'}`
      }
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络连接后重试'
    } else {
      // 其他错误
      errorMessage = `请求失败: ${error.message}`
    }

    ElMessage.error(errorMessage)
    allUsersList.value = []
    usersPagination.total = 0

    // 记录错误信息
    console.error('用户列表加载失败:', error.message)
  } finally {
    usersLoading.value = false
  }
}

const loadOrganizationsWithUsers = async () => {
  loading.value = true
  try {
    console.log('loadOrganizationsWithUsers - 使用适配层加载组织架构数据')

    // 使用适配层加载数据
    await adapterLoadOrgs()

    // 同步适配层数据到本地变量
    organizationTreeData.value = adapterTreeData.value
    flatOrganizations.value = adapterFlatOrgs.value
    organizationsResponse.value = adapterOrgsResponse.value  // 🔧 修复：使用正确的适配层变量

    console.log('loadOrganizationsWithUsers - 适配层数据同步完成')
    console.log('loadOrganizationsWithUsers - flatOrganizations.value:', flatOrganizations.value)

    // 更新可选择的组织列表（仅包含组织类型的节点）
    availableOrganizations.value = flatOrganizations.value.filter(org =>
      org.type === 'organization'
    ).map(org => ({
      id: org.id,
      name: org.name,
      level: org.level
    }))

    console.log('可选择组织列表:', availableOrganizations.value)

    // 统计嵌套结构中的用户总数
    const countUsersInNestedStructure = (orgs) => {
      let count = 0
      if (!Array.isArray(orgs)) orgs = [orgs]

      orgs.forEach(org => {
        if (org.users && Array.isArray(org.users)) {
          count += org.users.length
        }
        if (org.children && Array.isArray(org.children)) {
          count += countUsersInNestedStructure(org.children)
        }
      })
      return count
    }

    // 简化调试信息
    console.log('🔍 organizationsResponse.value组织数量:', organizationsResponse.value?.length || 0)

    const totalUsersInOrgs = countUsersInNestedStructure(organizationsResponse.value)
    console.log('loadOrganizationsWithUsers - 嵌套结构中的用户总数:', totalUsersInOrgs)

    // 暂时禁用buildTreeData，使用computedTreeData中的测试数据
    buildTreeData()
  } catch (error) {
    console.error('加载组织架构失败:', error)
    ElMessage.error('加载组织架构失败')
  } finally {
    loading.value = false
  }
}

// 扁平化组织结构的辅助函数
const flattenOrganizations = (organizations) => {
  const result = []

  const flatten = (orgs) => {
    if (!Array.isArray(orgs)) {
      orgs = [orgs]
    }

    orgs.forEach(org => {
      // 添加当前组织（去除children和users字段以避免循环引用）
      const { children, users, ...orgData } = org
      result.push(orgData)

      // 递归处理子组织
      if (children && children.length > 0) {
        flatten(children)
      }
    })
  }

  flatten(organizations)
  return result
}

const loadAllData = async () => {
  console.log('=== 开始数据加载对比分析 ===')

  // 并行加载所有数据
  await Promise.all([
    loadUserStats(),
    loadUsers(),
    loadOrganizationsWithUsers(),
    loadAllUsersList()
  ])

  // 数据加载完成后进行对比分析
  console.log('=== 数据加载对比分析 ===')
  console.log('allUsers.value 用户数量:', allUsers.value.length)
  console.log('organizationsResponse.value 组织数量:', organizationsResponse.value?.length || 0)

  // 从组织结构中提取所有用户
  const extractUsersFromOrgs = (orgs) => {
    let users = []
    if (!Array.isArray(orgs)) orgs = [orgs]

    const extract = (orgList) => {
      if (!Array.isArray(orgList)) orgList = [orgList]
      orgList.forEach(org => {
        if (org.users && Array.isArray(org.users)) {
          users.push(...org.users)
        }
        if (org.children && Array.isArray(org.children)) {
          extract(org.children)
        }
      })
    }

    extract(orgs)
    return users
  }

  const orgUsers = extractUsersFromOrgs(organizationsResponse.value || [])

  // 对比两个数据源的用户ID
  const allUserIds = new Set(allUsers.value.map(u => u.id))
  const orgUserIds = new Set(orgUsers.map(u => u.id))

  // 找出差异
  const onlyInAllUsers = [...allUserIds].filter(id => !orgUserIds.has(id))
  const onlyInOrgUsers = [...orgUserIds].filter(id => !allUserIds.has(id))
  if (onlyInOrgUsers.length > 0) {
    console.log('只在 orgUsers 中的用户ID:', onlyInOrgUsers.slice(0, 10))
  }

  console.log('=== 数据加载对比分析完成 ===')
}

// 计算属性
const filteredTreeData = computed(() => {
  // 这里可以根据搜索关键词和筛选条件过滤数据
  return organizationTreeData.value
})

// 计算属性：返回真实的API数据
const computedTreeData = computed(() => {
  console.log('computedTreeData - 使用真实API数据')
  console.log('organizationTreeData.value:', organizationTreeData.value)
  return organizationTreeData.value
})

// 注意：filteredUsersList已移除，现在直接使用allUsersList
// 所有过滤、搜索、分页都在服务端处理

// 移除了getTotalUsersInMemberGroup方法，因为不再显示用户分组

// 计算属性：默认展开的节点（只展开到二级层级）
const defaultExpandedKeys = computed(() => {
  const keys = []

  const collectExpandedKeys = (nodes, currentLevel = 0) => {
    if (!Array.isArray(nodes)) return

    nodes.forEach(node => {
      // 展开集团总部（level 0）和大区（level 1）
      if (currentLevel <= 1 && node.type === 'organization') {
        keys.push(node.id)

        // 递归处理子节点
        if (node.children && node.children.length > 0) {
          collectExpandedKeys(node.children, currentLevel + 1)
        }
      }

      // 展开成员分组（让用户可见）
      if (node.type === 'member_group') {
        keys.push(node.id)
      }

      // 展开管理员和普通用户分组（让用户可见）
      if (node.type === 'admin_group' || node.type === 'normal_user_group') {
        keys.push(node.id)
      }
    })
  }

  collectExpandedKeys(organizationTreeData.value)
  console.log('defaultExpandedKeys - 默认展开的节点:', keys)
  return keys
})

// 方法
const buildTreeData = () => {
  // 直接使用API返回的嵌套结构，并添加用户数据
  try {
    const response = organizationsResponse.value
    console.log('🔍 buildTreeData - 开始构建树结构')
    console.log('🔍 buildTreeData - organizationsResponse.value:', response)
    console.log('🔍 buildTreeData - response类型:', typeof response, '是否数组:', Array.isArray(response))

    // 🔧 修复：正确检查数据是否存在
    if (!response) {
      console.log('❌ buildTreeData - 没有数据，设置为空数组')
      organizationTreeData.value = []
      return
    }

    // 🔧 修复：确保response是数组格式
    let organizationArray = Array.isArray(response) ? response : [response]
    if (organizationArray.length === 0) {
      console.log('❌ buildTreeData - 数组为空，设置为空数组')
      organizationTreeData.value = []
      return
    }

    // 🔧 修复：检查根组织的子组织
    if (organizationArray.length > 0 && organizationArray[0].children) {
      console.log('🔍 buildTreeData - 根组织子组织数量:', organizationArray[0].children.length)
      organizationArray[0].children.forEach((child, index) => {
        console.log(`🔍 buildTreeData - 根组织子组织${index + 1}: ${child.name} (ID: ${child.id}, Level: ${child.level})`)
      })
    }

    // 递归构建树结构（仅组织结构，不包含用户节点）
    const buildTree = (orgs) => {
      console.log('🔧 buildTree - 输入orgs:', orgs, '类型:', typeof orgs, '是否数组:', Array.isArray(orgs))

      if (!Array.isArray(orgs)) {
        orgs = [orgs]
      }

      return orgs.map(org => {
        console.log(`🔧 buildTree - 处理组织: ${org.name} (ID: ${org.id}, Level: ${org.level})`)

        // 统计用户数量
        const adminCount = org.users ? org.users.filter(user =>
          user.role_name && user.role_name.includes('管理员')
        ).length : 0

        const normalUserCount = org.users ? org.users.filter(user =>
          !user.role_name || !user.role_name.includes('管理员')
        ).length : 0

        // 递归统计子组织的用户数量
        let totalAdminCount = adminCount
        let totalNormalUserCount = normalUserCount

        if (org.children && org.children.length > 0) {
          org.children.forEach(child => {
            const childStats = calculateOrgStats(child)
            totalAdminCount += childStats.adminCount
            totalNormalUserCount += childStats.normalUserCount
          })
        }

        const node = {
          ...org,
          type: 'organization',
          adminCount: totalAdminCount,
          normalUserCount: totalNormalUserCount,
          totalUserCount: totalAdminCount + totalNormalUserCount,
          children: []
        }

        // 仅添加子组织，不添加用户节点
        if (org.children && org.children.length > 0) {
          console.log(`🔧 buildTree - 递归处理子组织: ${org.children.length} 个`)
          const childNodes = buildTree(org.children)
          node.children = childNodes
          console.log(`🔧 buildTree - 添加子组织节点数: ${childNodes.length}`)
        }

        console.log(`🔧 buildTree - 组织节点构建完成: ${org.name}, 管理员: ${totalAdminCount}人, 用户: ${totalNormalUserCount}人`)
        return node
      })
    }

    // 计算组织统计信息的辅助函数
    const calculateOrgStats = (org) => {
      const adminCount = org.users ? org.users.filter(user =>
        user.role_name && user.role_name.includes('管理员')
      ).length : 0

      const normalUserCount = org.users ? org.users.filter(user =>
        !user.role_name || !user.role_name.includes('管理员')
      ).length : 0

      let totalAdminCount = adminCount
      let totalNormalUserCount = normalUserCount

      if (org.children && org.children.length > 0) {
        org.children.forEach(child => {
          const childStats = calculateOrgStats(child)
          totalAdminCount += childStats.adminCount
          totalNormalUserCount += childStats.normalUserCount
        })
      }

      return {
        adminCount: totalAdminCount,
        normalUserCount: totalNormalUserCount
      }
    }

    // 清空现有数据
    organizationTreeData.value = []

    // 🔧 修复：重新构建数据，使用organizationArray
    const newTreeData = buildTree(organizationArray)
    console.log('✅ buildTreeData - 新构建的树数据:', newTreeData)

    // 使用nextTick确保响应式更新
    nextTick(() => {
      organizationTreeData.value = newTreeData
      console.log('✅ buildTreeData - 设置organizationTreeData.value:', organizationTreeData.value)
      console.log('✅ buildTreeData - 根节点数量:', organizationTreeData.value.length)

      // 详细检查根节点的子节点
      if (organizationTreeData.value.length > 0) {
        const rootNode = organizationTreeData.value[0]
        console.log('✅ buildTreeData - 根节点名称:', rootNode.name)
        console.log('✅ buildTreeData - 根节点子节点数量:', rootNode.children?.length || 0)

        // 检查每个子节点
        if (rootNode.children) {
          console.log('🔍 buildTreeData - 详细检查根节点的所有子节点:')
          rootNode.children.forEach((child, index) => {
            console.log(`🔍 buildTreeData - 子节点${index + 1}: ${child.name} (类型: ${child.type}, 级别: ${child.level}, ID: ${child.id})`)
            if (child.type === 'organization' && child.level === 1) {
              console.log(`✅ buildTreeData - 发现大区: ${child.name}`)
            }
          })

          // 统计大区数量
          const regions = rootNode.children.filter(child => child.type === 'organization' && child.level === 1)
          console.log(`📊 buildTreeData - 大区总数: ${regions.length}`)
          regions.forEach((region, index) => {
            console.log(`📊 buildTreeData - 大区${index + 1}: ${region.name} (ID: ${region.id})`)
          })
        }
      }

      // 默认选中根节点（集团总部）
      if (organizationTreeData.value.length > 0 && !selectedNode.value) {
        selectedNode.value = organizationTreeData.value[0]
        console.log('✅ buildTreeData - 默认选中根节点:', selectedNode.value)
      }
    })

  } catch (error) {
    console.error('❌ 构建树结构失败:', error)
    organizationTreeData.value = []
  }
}

// 使用适配层的getLevelName函数
const getLevelName = adapterGetLevelName

const getTabTypeLabel = (tabType) => {
  const labels = {
    'total': '全部用户',
    'admin': '管理员',
    'normal': '普通用户'
  }
  return labels[tabType] || '用户'
}

const getUserStatusType = (isActive) => {
  return isActive ? 'success' : 'danger'
}

const formatDate = (dateStr) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleString('zh-CN')
}

const getOrgPath = (orgId) => {
  const org = flatOrganizations.value.find(o => o.id === orgId)
  if (!org) return '未知'

  let path = org.name
  let current = org
  while (current.parent_id) {
    const parent = flatOrganizations.value.find(o => o.id === current.parent_id)
    if (parent) {
      path = parent.name + ' / ' + path
      current = parent
    } else {
      break
    }
  }
  return path
}

// 递归获取组织及其所有子组织的用户数量（使用组织API的嵌套数据）
const getOrgUserCount = (orgId) => {
  console.log('=== getOrgUserCount 开始计算 ===')
  console.log('getOrgUserCount - 输入orgId:', orgId)
  console.log('getOrgUserCount - organizationsResponse.value:', organizationsResponse.value?.length || 0)
  console.log('getOrgUserCount - organizationsResponse.value 完整数据:', organizationsResponse.value)

  // 在嵌套的组织结构中查找指定组织
  const findOrgInNested = (orgs, targetId) => {
    if (!Array.isArray(orgs)) orgs = [orgs]

    for (const org of orgs) {
      if (org.id === targetId) {
        return org
      }
      if (org.children && org.children.length > 0) {
        const found = findOrgInNested(org.children, targetId)
        if (found) return found
      }
    }
    return null
  }

  // 递归统计组织及其所有子组织的用户数量
  const countUsersRecursively = (org) => {
    if (!org) return 0

    let count = 0

    // 统计当前组织的用户
    if (org.users && Array.isArray(org.users)) {
      count += org.users.length
      console.log(`getOrgUserCount - 组织 ${org.name} 直接用户数: ${org.users.length}`)
    }

    // 递归统计子组织的用户
    if (org.children && Array.isArray(org.children)) {
      org.children.forEach(child => {
        const childCount = countUsersRecursively(child)
        count += childCount
        console.log(`getOrgUserCount - 子组织 ${child.name} 用户数: ${childCount}`)
      })
    }

    return count
  }

  const targetOrg = findOrgInNested(organizationsResponse.value || [], orgId)
  if (!targetOrg) {
    console.log('getOrgUserCount - 未找到目标组织:', orgId)
    return 0
  }

  const totalCount = countUsersRecursively(targetOrg)
  console.log('getOrgUserCount - 最终用户数量:', totalCount)
  console.log('=== getOrgUserCount 计算结束 ===')

  return totalCount
}

const getOrgAdminCount = (orgId) => {
  // 在嵌套的组织结构中查找指定组织
  const findOrgInNested = (orgs, targetId) => {
    if (!Array.isArray(orgs)) orgs = [orgs]

    for (const org of orgs) {
      if (org.id === targetId) {
        return org
      }
      if (org.children && org.children.length > 0) {
        const found = findOrgInNested(org.children, targetId)
        if (found) return found
      }
    }
    return null
  }

  // 递归统计组织及其所有子组织的管理员数量
  const countAdminsRecursively = (org) => {
    if (!org) return 0

    let count = 0

    // 统计当前组织的管理员
    if (org.users && Array.isArray(org.users)) {
      count += org.users.filter(user =>
        user.role_name === '管理员' || user.role_name === '超级管理员'
      ).length
    }

    // 递归统计子组织的管理员
    if (org.children && Array.isArray(org.children)) {
      org.children.forEach(child => {
        count += countAdminsRecursively(child)
      })
    }

    return count
  }

  const targetOrg = findOrgInNested(organizationsResponse.value || [], orgId)
  return targetOrg ? countAdminsRecursively(targetOrg) : 0
}

const getOrgNormalUserCount = (orgId) => {
  // 在嵌套的组织结构中查找指定组织
  const findOrgInNested = (orgs, targetId) => {
    if (!Array.isArray(orgs)) orgs = [orgs]

    for (const org of orgs) {
      if (org.id === targetId) {
        return org
      }
      if (org.children && org.children.length > 0) {
        const found = findOrgInNested(org.children, targetId)
        if (found) return found
      }
    }
    return null
  }

  // 递归统计组织及其所有子组织的普通用户数量
  const countNormalUsersRecursively = (org) => {
    if (!org) return 0

    let count = 0

    // 统计当前组织的普通用户
    if (org.users && Array.isArray(org.users)) {
      count += org.users.filter(user =>
        user.role_name === '普通用户'
      ).length
    }

    // 递归统计子组织的普通用户
    if (org.children && Array.isArray(org.children)) {
      org.children.forEach(child => {
        count += countNormalUsersRecursively(child)
      })
    }

    return count
  }

  const targetOrg = findOrgInNested(organizationsResponse.value || [], orgId)
  return targetOrg ? countNormalUsersRecursively(targetOrg) : 0
}

// 新增：统计卡片交互处理方法
const handleStatsCardClick = async (cardType) => {
  console.log('统计卡片单击切换标签页:', cardType)

  // 切换到对应的标签页
  activeTabName.value = cardType

  // 加载对应类型的用户数据
  await loadTabUsers(selectedNode.value, cardType)
}

// 新增：标签页点击处理方法
const handleTabClick = async (tab) => {
  const tabName = tab.props?.name || tab.name
  console.log('标签页切换:', tabName)

  // 如果切换到用户列表标签页，加载对应的用户数据
  if (['total', 'admin', 'normal'].includes(tabName) && selectedNode.value) {
    await loadTabUsers(selectedNode.value, tabName)
  }
}

const loadTabUsers = async (organization, tabType) => {
  if (!organization) {
    tabUsers.value = []
    return
  }

  try {
    tabLoading.value = true

    // 获取该组织及其下属层级的所有用户
    const response = await getOrganizationsWithUsers()

    // 🔧 修复：处理API中间件包装格式
    let organizationData = response
    if (response && response.success && response.data) {
      console.log('🔧 loadTabUsers - 检测到API中间件包装格式，提取data字段')
      organizationData = response.data
    }
    console.log('loadTabUsers - 处理后的组织数据:', organizationData)

    // 从嵌套结构中提取用户数据
    const extractUsersFromOrg = (org) => {
      let users = []

      // 添加当前组织的用户
      if (org.users && Array.isArray(org.users)) {
        users = users.concat(org.users)
      }

      // 递归添加子组织的用户
      if (org.children && Array.isArray(org.children)) {
        org.children.forEach(child => {
          users = users.concat(extractUsersFromOrg(child))
        })
      }

      return users
    }

    // 找到目标组织
    const findTargetOrg = (orgs, targetId) => {
      if (!Array.isArray(orgs)) orgs = [orgs]

      for (const org of orgs) {
        if (org.id === targetId) {
          return org
        }
        if (org.children && Array.isArray(org.children)) {
          const found = findTargetOrg(org.children, targetId)
          if (found) return found
        }
      }
      return null
    }

    // 🔧 修复：使用处理后的organizationData
    const targetOrg = findTargetOrg(organizationData || [], organization.id)
    if (targetOrg) {
      let allUsers = extractUsersFromOrg(targetOrg)

      // 根据标签页类型筛选用户
      if (tabType === 'admin') {
        // 筛选管理员用户（包含所有管理员级别的角色）
        tabUsers.value = allUsers.filter(user => {
          const role = user.role || user.role_name || ''
          return role === '超级管理员' ||
                 role === '管理员' ||
                 role === '全域管理员' ||
                 role.includes('管理员') ||  // 包含"管理员"字样的角色
                 role.includes('管理') ||    // 包含"管理"字样的角色
                 role === '系统管理员' ||
                 role === '华东区经理' ||
                 role === '技术部经理'
        })
      } else if (tabType === 'normal') {
        // 筛选普通用户（排除管理员级别的角色）
        tabUsers.value = allUsers.filter(user => {
          const role = user.role || user.role_name || ''
          return role === '普通用户' ||
                 role === '新用户' ||
                 (!role.includes('管理员') &&
                  !role.includes('管理') &&
                  role !== '超级管理员' &&
                  role !== '全域管理员' &&
                  role !== '系统管理员')
        })
      } else {
        // 显示所有用户
        tabUsers.value = allUsers
      }
    } else {
      tabUsers.value = []
    }

    console.log(`加载标签页用户数据完成: ${tabUsers.value.length} 个用户 (类型: ${tabType})`)

  } catch (error) {
    console.error('加载标签页用户数据失败:', error)
    ElMessage.error('加载用户数据失败')
    tabUsers.value = []
  } finally {
    tabLoading.value = false
  }
}

const handleTabRefresh = () => {
  if (selectedNode.value && ['total', 'admin', 'normal'].includes(activeTabName.value)) {
    loadTabUsers(selectedNode.value, activeTabName.value)
  }
}

const handleTabAddUser = () => {
  // 打开添加用户对话框，预设组织为当前选中的组织
  editingUser.value = {
    organization_id: selectedNode.value?.id
  }
  showUserEditDialog.value = true
}

const handleTabEditUser = (user) => {
  editingUser.value = user
  showUserEditDialog.value = true
}

const handleTabDeleteUser = async (user) => {
  try {
    await deleteUser(user.id)
    ElMessage.success('删除用户成功')

    // 刷新标签页数据
    await handleTabRefresh()

    // 同时刷新主页面数据
    await loadAllData()
  } catch (error) {
    console.error('删除用户失败:', error)
    ElMessage.error('删除用户失败')
  }
}

// 事件处理
const handleSearch = async () => {
  // 重新加载数据，应用搜索条件
  await loadAllData()
}

const resetFilters = async () => {
  // 重置所有筛选条件
  filterOptions.displayScope = 'all'
  filterOptions.orgStructure = 'none'
  filterOptions.roleFilter = 'none'
  filterOptions.specificOrg = null
  filterOptions.specificRole = null
  userSearchKeyword.value = ''
  usersPagination.currentPage = 1

  // 清除组织架构高亮
  clearOrgHighlight()

  // 重新加载用户列表
  await loadAllUsersList()
  ElMessage.success('筛选条件已重置')
}

// 防抖搜索
let searchTimeout = null
const handleUserSearch = () => {
  // 清除之前的定时器
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  // 设置新的定时器，300ms后执行搜索
  searchTimeout = setTimeout(() => {
    usersPagination.currentPage = 1
    loadAllUsersList()
  }, 300)
}

// 筛选条件变更处理
const handleFilterChange = () => {
  console.log('🔄 筛选条件变更:', {
    displayScope: filterOptions.displayScope,
    orgStructure: filterOptions.orgStructure,
    roleFilter: filterOptions.roleFilter,
    specificOrg: filterOptions.specificOrg,
    specificRole: filterOptions.specificRole
  })
  usersPagination.currentPage = 1
  loadAllUsersList()

  // 联动更新组织架构显示
  updateOrgTreeDisplay()
}

// 组织结构筛选变更处理
const handleOrgStructureChange = () => {
  // 清空指定组织选择
  if (filterOptions.orgStructure !== 'specific') {
    filterOptions.specificOrg = null
  }
  handleFilterChange()
}

// 角色筛选变更处理
const handleRoleFilterChange = () => {
  // 清空指定角色选择
  if (filterOptions.roleFilter !== 'specific') {
    filterOptions.specificRole = null
  }
  handleFilterChange()
}

// 🎯 分页处理方法
const handlePageSizeChange = (newPageSize) => {
  console.log('📄 每页显示数量变更:', newPageSize)
  usersPagination.pageSize = newPageSize
  usersPagination.currentPage = 1 // 重置到第一页
  loadAllUsersList()
}

const handleCurrentPageChange = (newPage) => {
  console.log('📄 页码变更:', newPage)
  usersPagination.currentPage = newPage
  loadAllUsersList()
}

const handlePageJump = () => {
  if (jumpToPage.value >= 1 && jumpToPage.value <= Math.ceil(usersPagination.total / usersPagination.pageSize)) {
    usersPagination.currentPage = jumpToPage.value
    loadAllUsersList()
    jumpToPage.value = null
  } else {
    ElMessage.warning('请输入有效的页码')
  }
}

// 页面跳转输入框
const jumpToPage = ref(null)

// 更新组织架构树显示状态
const updateOrgTreeDisplay = () => {
  if (!orgTreeRef.value) return

  // 根据筛选条件高亮相关组织节点
  if (filterOptions.orgStructure === 'specific' && filterOptions.specificOrg) {
    // 高亮指定组织
    highlightOrgNode(filterOptions.specificOrg)
  } else if (filterOptions.displayScope === 'current' && selectedNode.value) {
    // 高亮当前选中组织
    highlightOrgNode(selectedNode.value.id)
  } else {
    // 清除所有高亮
    clearOrgHighlight()
  }
}

// 高亮组织节点
const highlightOrgNode = (orgId) => {
  // 实现组织节点高亮逻辑
  const treeNode = orgTreeRef.value
  if (treeNode) {
    treeNode.setCurrentKey(orgId)
  }
}

// 清除组织高亮
const clearOrgHighlight = () => {
  const treeNode = orgTreeRef.value
  if (treeNode) {
    treeNode.setCurrentKey(null)
  }
}

const refreshUsersList = async () => {
  await loadAllUsersList()
  ElMessage.success('用户列表已刷新')
}

const handleUserPageSizeChange = (newSize) => {
  usersPagination.pageSize = newSize
  usersPagination.currentPage = 1
  loadAllUsersList()
}

const handleUserPageChange = (newPage) => {
  usersPagination.currentPage = newPage
  loadAllUsersList()
}

const getRoleTagType = (roleName) => {
  switch (roleName) {
    case '超级管理员':
      return 'danger'
    case '管理员':
      return 'warning'
    case '普通用户':
      return 'success'
    default:
      return 'info'
  }
}

// canEditUser函数已移除，现在直接使用后端返回的user.can_edit字段

const handleViewUser = (user) => {
  // 查看用户详情
  selectedNode.value = user
  ElMessage.info(`查看用户: ${user.full_name}`)
}

const handleEditUser = (user) => {
  // 编辑用户
  editingUser.value = user
  showUserEditDialog.value = true
}

const handleDeleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.full_name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用删除用户的API
    await deleteUser(user.id)
    ElMessage.success('用户删除成功')

    // 重新加载用户列表
    await loadAllUsersList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

// 获取组织完整路径 - 用于hover提示
const getOrganizationPath = (user) => {
  if (!user.organization_path) {
    return user.organization_name || '未知组织'
  }

  // 如果有完整路径，显示层级结构
  return user.organization_path.split(' > ').join(' → ')
}

// 检查是否有组织路径信息
const hasOrganizationPath = (user) => {
  return user.organization_path && user.organization_path !== user.organization_name
}

// 带确认对话框的删除用户函数
const handleDeleteUserWithConfirm = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.full_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        message: `
          <div style="margin: 16px 0;">
            <p style="margin-bottom: 8px;"><strong>用户信息：</strong></p>
            <p style="margin: 4px 0; color: #606266;">姓名：${user.full_name}</p>
            <p style="margin: 4px 0; color: #606266;">邮箱：${user.email || '无'}</p>
            <p style="margin: 4px 0; color: #606266;">组织：${user.organization_name || '无'}</p>
            <p style="margin-top: 12px; color: #F56C6C; font-weight: bold;">⚠️ 此操作不可恢复！</p>
          </div>
        `
      }
    )

    await deleteUser(user.id)
    ElMessage.success('用户删除成功')
    await loadAllUsersList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

const expandAll = () => {
  allExpanded.value = !allExpanded.value
  nextTick(() => {
    if (allExpanded.value) {
      orgTreeRef.value?.expandAll()
    } else {
      orgTreeRef.value?.collapseAll()
    }
  })
}

// 双击防抖变量
let clickTimer = null

// 🎯 智能联动：根据组织层级自动设置筛选条件
const applySmartFilterByLevel = (orgNode) => {
  const startTime = performance.now()

  switch (orgNode.level) {
    case 0: // 集团总部（0级）联动
      filterOptions.displayScope = '全部用户'
      filterOptions.orgLevel = '按层级分组'
      filterOptions.roleType = '按角色分组'
      break

    case 1: // 大区（1级）联动
      filterOptions.displayScope = '当前组织及子组织'
      filterOptions.orgLevel = '按层级分组'
      filterOptions.roleType = '按角色分组'
      break

    case 2: // 分公司（2级）联动
      filterOptions.displayScope = '当前组织及子组织'
      filterOptions.orgLevel = '按层级分组'
      filterOptions.roleType = '按角色分组'
      break

    case 3: // 部门（3级）联动
      filterOptions.displayScope = '当前组织及子组织'
      filterOptions.orgLevel = '按层级分组'
      filterOptions.roleType = '按角色分组'
      break

    case 4: // 小组（4级）联动
      filterOptions.displayScope = '当前组织'
      filterOptions.orgLevel = '不区分层级'
      filterOptions.roleType = '按角色分组'
      break

    default:
      // 默认设置
      filterOptions.displayScope = '全部用户'
      filterOptions.orgLevel = '按层级分组'
      filterOptions.roleType = '按角色分组'
  }

  // 设置指定组织ID
  filterOptions.specificOrg = orgNode.id

  const endTime = performance.now()
  console.log(`[智能联动] 筛选条件设置完成，耗时: ${(endTime - startTime).toFixed(2)}ms`)
}

const handleNodeClick = (data) => {
  // 清除之前的定时器，避免双击时触发单击事件
  if (clickTimer) {
    clearTimeout(clickTimer)
    clickTimer = null
    return
  }

  // 延迟执行单击事件，给双击事件留出时间
  clickTimer = setTimeout(() => {
    selectedNode.value = data
    clickTimer = null

    // 重置标签页到基本信息
    activeTabName.value = 'basic'
    tabUsers.value = []
    selectedStatsCard.value = null

    // 🎯 智能联动：如果点击的是组织节点，根据层级自动设置筛选条件
    if (data.type === 'organization') {
      console.log(`[智能联动] 选中${data.name}(${data.level}级) -> 开始设置筛选条件`)

      // 根据组织层级自动设置筛选条件
      applySmartFilterByLevel(data)

      // 输出筛选条件日志
      console.log(`[智能联动] 选中${data.name}(${data.level}级) -> 筛选条件: ${JSON.stringify(filterOptions)}`)

      // 立即刷新用户列表数据
      loadAllUsersList()

      // 更新组织架构显示状态
      updateOrgTreeDisplay()
    }
  }, 10) // 减少延迟时间到10ms，提升响应速度
}



// 双击节点处理函数
const handleNodeDoubleClick = (node, data) => {
  // 清除单击定时器，防止双击时触发单击事件
  if (clickTimer) {
    clearTimeout(clickTimer)
    clickTimer = null
  }

  // 只对可展开的节点类型处理双击事件
  const expandableTypes = ['organization', 'member_group', 'admin_group', 'normal_user_group']
  if (!expandableTypes.includes(data.type)) {
    return
  }

  console.log(`🖱️ 双击节点: ${data.name} (类型: ${data.type})`)

  // 递归切换节点及其所有子节点的展开状态
  toggleNodeAndChildren(node)
}

// 递归切换节点及其所有子节点的展开状态
const toggleNodeAndChildren = (node) => {
  if (!node || !orgTreeRef.value) {
    return
  }

  // 获取当前节点的展开状态
  const isExpanded = node.expanded
  console.log(`🔄 切换节点: ${node.data.name}, 当前状态: ${isExpanded ? '展开' : '收起'}`)

  // 如果当前节点是收起状态，则展开该节点及其所有子节点
  if (!isExpanded) {
    expandNodeAndChildren(node)
  } else {
    // 如果当前节点是展开状态，则收起该节点及其所有子节点
    collapseNodeAndChildren(node)
  }
}

// 递归展开节点及其所有子节点
const expandNodeAndChildren = (node) => {
  if (!node || !orgTreeRef.value) {
    return
  }

  // 展开当前节点
  orgTreeRef.value.store.nodesMap[node.key].expanded = true
  console.log(`📂 展开节点: ${node.data.name}`)

  // 递归展开所有子节点
  if (node.childNodes && node.childNodes.length > 0) {
    node.childNodes.forEach(childNode => {
      expandNodeAndChildren(childNode)
    })
  }
}

// 递归收起节点及其所有子节点
const collapseNodeAndChildren = (node) => {
  if (!node || !orgTreeRef.value) {
    return
  }

  // 先递归收起所有子节点
  if (node.childNodes && node.childNodes.length > 0) {
    node.childNodes.forEach(childNode => {
      collapseNodeAndChildren(childNode)
    })
  }

  // 收起当前节点
  orgTreeRef.value.store.nodesMap[node.key].expanded = false
  console.log(`📁 收起节点: ${node.data.name}`)
}

// handleEditUser函数已在上面定义，此处删除重复定义

const handleEditOrg = (org) => {
  editingOrg.value = { ...org }
  showOrgEditDialog.value = true
}

const handleAddUser = () => {
  editingUser.value = {
    username: '',
    full_name: '',
    email: '',
    phone: '',
    organization_id: selectedNode.value?.type === 'organization' ? selectedNode.value.id : null,
    roles: ['普通用户'],
    is_active: true
  }
  showUserEditDialog.value = true
}

const handleAddOrganization = () => {
  editingOrg.value = {
    name: '',
    level: selectedNode.value?.type === 'organization' ? selectedNode.value.level + 1 : 0,
    parent_id: selectedNode.value?.type === 'organization' ? selectedNode.value.id : null
  }
  showOrgEditDialog.value = true
}

const handleDeleteOrg = async (org) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除组织"${org.name}"吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    try {
      // 这里应该调用删除组织API
      // await deleteOrganization(org.id)

      ElMessage.success('删除成功')
      await loadAllData() // 重新加载数据
    } catch (error) {
      console.error('删除组织失败:', error)
      ElMessage.error('删除组织失败')
    }
  } catch {
    // 用户取消删除
  }
}

const handleSaveUser = async (userData) => {
  try {
    // 这里应该调用保存用户API
    // if (userData.id) {
    //   await updateUser(userData.id, userData)
    // } else {
    //   await createUser(userData)
    // }

    console.log('保存用户:', userData)
    ElMessage.success('用户保存成功')

    // 重新加载数据
    await loadAllData()
  } catch (error) {
    console.error('保存用户失败:', error)
    ElMessage.error('保存用户失败')
  }
}

const handleSaveOrg = async (orgData) => {
  try {
    // 这里应该调用保存组织API
    // if (orgData.id) {
    //   await updateOrganization(orgData.id, orgData)
    // } else {
    //   await createOrganization(orgData)
    // }

    console.log('保存组织:', orgData)
    ElMessage.success('组织保存成功')

    // 重新加载数据
    await loadAllData()
  } catch (error) {
    console.error('保存组织失败:', error)
    ElMessage.error('保存组织失败')
  }
}

// 拖拽相关
const allowDrag = (node) => {
  // 只允许拖拽用户，不允许拖拽组织
  return node.data.type === 'user'
}

const allowDrop = (draggingNode, dropNode, type) => {
  // 只允许拖拽到组织节点、成员分组、管理员分组或普通用户分组上
  const allowedTypes = ['organization', 'member_group', 'admin_group', 'normal_user_group']
  return allowedTypes.includes(dropNode.data.type) && type === 'inner'
}

const handleNodeDrop = async (draggingNode, dropNode, dropType) => {
  const allowedTypes = ['organization', 'member_group', 'admin_group', 'normal_user_group']
  if (!allowedTypes.includes(dropNode.data.type)) {
    ElMessage.error('只能将用户拖拽到组织节点或用户分组上')
    return
  }

  const user = draggingNode.data
  let targetOrg = dropNode.data

  // 根据不同的拖拽目标类型，获取对应的组织信息
  if (targetOrg.type === 'member_group') {
    targetOrg = {
      id: targetOrg.organization_id,
      name: targetOrg.name.replace('成员', ''),
      type: 'organization'
    }
  } else if (targetOrg.type === 'admin_group') {
    targetOrg = {
      id: targetOrg.organization_id,
      name: targetOrg.name.replace('管理员', ''),
      type: 'organization'
    }
  } else if (targetOrg.type === 'normal_user_group') {
    targetOrg = {
      id: targetOrg.organization_id,
      name: targetOrg.name.replace('普通用户', ''),
      type: 'organization'
    }
  }

  try {
    await ElMessageBox.confirm(
      `确定要将用户"${user.full_name}"调整到"${targetOrg.name}"吗？`,
      '调整确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    // 调用API更新用户的组织ID
    try {
      // 这里应该调用更新用户API
      // await updateUser(user.id, { organization_id: targetOrg.id })

      // 临时更新本地数据
      const userIndex = allUsers.value.findIndex(u => u.id === user.id)
      if (userIndex !== -1) {
        allUsers.value[userIndex].organization_id = targetOrg.id
      }

      ElMessage.success('用户组织调整成功')
      buildTreeData() // 重新构建树数据
    } catch (error) {
      console.error('更新用户组织失败:', error)
      ElMessage.error('更新用户组织失败')
      buildTreeData() // 恢复原状
    }
  } catch {
    // 用户取消，重新构建树数据以恢复原状
    buildTreeData()
  }
}











// 生命周期
onMounted(() => {
  loadAllData()
})
</script>

<style scoped>
.org-users-container {
  padding: 24px;
  min-height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  max-width: 100vw;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.page-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.header-left h2 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-desc {
  margin: 0;
  color: #64748b;
  font-size: 16px;
  line-height: 1.6;
  font-weight: 500;
}

.header-right {
  display: flex;
  gap: 16px;
}

.header-right .el-button {
  border-radius: 12px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.header-right .el-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
}

.search-card {
  margin-bottom: 24px;
  border-radius: 16px !important;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
}

.search-card :deep(.el-card__body) {
  padding: 20px 24px !important;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.search-bar .el-input {
  border-radius: 12px !important;
}

.search-bar .el-input :deep(.el-input__wrapper) {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  transition: all 0.3s ease !important;
}

.search-bar .el-input :deep(.el-input__wrapper):hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.filter-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.filter-controls .el-select,
.filter-controls .el-button {
  border-radius: 12px !important;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 0px;
  min-height: 0;
  max-width: 100%;
  overflow: visible;
}

.tree-panel {
  width: 520px;
  min-width: 520px;
  margin-right: 24px;
}

.tree-panel .el-card {
  min-height: 600px;
  max-height: 800px;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
}

.tree-panel .el-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

.tree-panel :deep(.el-card__body) {
  max-height: 720px;
  overflow-y: auto;
  padding: 20px !important;
}

.detail-panel {
  flex: 1;
  margin-left: 0;
}

.detail-panel .el-card {
  min-height: 600px;
  max-height: 800px;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
}

.detail-panel .el-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

.detail-panel :deep(.el-card__body) {
  max-height: 720px;
  overflow-y: auto;
  padding: 20px !important;
}

.detail-card {
  margin-bottom: 20px;
  border-radius: 16px !important;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
}

.users-list-card {
  height: auto;
  min-height: 520px;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  margin-left: 0;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* 筛选控件样式优化 */
.filter-controls {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-controls .el-select {
  transition: all 0.2s ease;
}

.filter-controls .el-select:hover {
  transform: translateY(-1px);
}

.filter-controls .el-input {
  transition: all 0.2s ease;
}

.filter-controls .el-input:hover {
  transform: translateY(-1px);
}

.filter-controls .el-button {
  transition: all 0.2s ease;
}

.filter-controls .el-button:hover {
  transform: translateY(-1px);
}

/* 分页信息样式 */
.pagination-info {
  font-weight: 500;
}

.pagination-container {
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

/* 表格性能优化 */
.users-table-container {
  position: relative;
}

.users-table-container .el-table {
  transition: none !important;
}

.users-table-container .el-table__body-wrapper {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.users-table-container .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.users-table-container .el-table__body-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.users-table-container .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.users-table-container .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 组织架构树联动高亮样式 */
.el-tree-node.is-current > .el-tree-node__content {
  background-color: rgba(64, 158, 255, 0.1) !important;
  border-left: 3px solid #409eff !important;
}

.el-tree-node.is-current > .el-tree-node__content .tree-node {
  color: #409eff !important;
  font-weight: 500 !important;
}

.el-tree-node.filter-highlight > .el-tree-node__content {
  background-color: rgba(103, 194, 58, 0.1) !important;
  border-left: 3px solid #67c23a !important;
}

.el-tree-node.filter-highlight > .el-tree-node__content .tree-node {
  color: #67c23a !important;
}

/* 筛选条件联动提示 */
.filter-controls .el-select.is-linked {
  border-color: #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

/* 组织架构树超快速动画优化 - 几乎瞬时响应 */
.fast-tree {
  --el-tree-node-content-height: 32px;
}

.fast-tree .el-tree-node {
  transition: none !important;
}

.fast-tree .el-tree-node__content {
  transition: background-color 0.02s ease !important;
  height: 32px !important;
  line-height: 32px !important;
}

.fast-tree .el-tree-node__expand-icon {
  transition: transform 0.01s ease !important;
}

.fast-tree .el-tree-node__children {
  transition: none !important;
  animation: none !important;
  transform: none !important;
}

.fast-tree .el-tree-node.is-expanded > .el-tree-node__children {
  display: block !important;
  opacity: 1 !important;
}

.fast-tree .el-tree-node:not(.is-expanded) > .el-tree-node__children {
  display: none !important;
  opacity: 0 !important;
}

/* 完全禁用所有可能的动画效果 */
.fast-tree * {
  transition: none !important;
  animation: none !important;
  transform: none !important;
}

/* 仅保留最基本的悬停效果，时间极短 */
.fast-tree .el-tree-node__content:hover {
  background-color: #f5f7fa !important;
  transition: background-color 0.01s ease !important;
}

/* 组织统计信息样式 */
.org-stats {
  color: #909399;
  font-size: 12px;
  font-weight: normal;
  margin-left: 8px;
}

.org-type {
  color: #606266;
  font-size: 12px;
  font-weight: normal;
}

/* 树节点优化 */
.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 8px;
  min-height: 32px;
}

.tree-node .node-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.tree-node .node-label {
  margin-left: 8px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tree-node .node-actions {
  display: flex;
  align-items: center;
  opacity: 0;
  transition: opacity 0.1s ease;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

/* 性能优化：减少重绘 */
.fast-tree * {
  will-change: auto !important;
}

.fast-tree .el-tree-node__content:hover {
  background-color: #f5f7fa !important;
}

.users-list-card :deep(.el-card__header) {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  padding: 20px 24px !important;
  border-bottom: 1px solid rgba(229, 231, 235, 0.6) !important;
  background: rgba(248, 250, 252, 0.8) !important;
  border-radius: 16px 16px 0 0 !important;
}

.users-list-card :deep(.el-card__body) {
  height: auto;
  min-height: 420px;
  overflow: visible;
  padding: 20px 24px !important;
}

/* 用户表格容器优化 - 充分利用右侧空间 */
.users-table-container {
  width: 100%;
  overflow-x: auto;
  position: relative;
}

/* 表格响应式优化 */
.users-table-container .el-table {
  min-width: 1200px;
  transition: none !important;
}

/* 表格列内容优化 */
.users-table-container .el-table .cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 8px 12px;
  line-height: 1.5;
}

/* 确保表格充分利用可用宽度 */
.users-table-container .el-table__body-wrapper {
  overflow-x: auto;
}

/* 表格头部样式优化 */
.users-table-container .el-table th {
  background-color: #fafafa;
  font-weight: 600;
  color: #606266;
  white-space: nowrap;
}

/* 表格行悬停效果 */
.users-table-container .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 表格列宽自适应优化 */
.users-table-container .el-table .el-table__cell {
  padding: 8px 12px;
}

/* 工具提示样式优化 */
.users-table-container .el-tooltip__trigger {
  width: 100%;
  text-align: inherit;
}

/* 智能联动状态指示器样式 */
.smart-link-indicator {
  margin-left: 16px;
  display: flex;
  align-items: center;
}

/* 标题区域布局优化 */
.header-title-section {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 16px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
}

/* Header中的分页控件样式 - 居中显示 */
.header-pagination-center {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.pagination-wrapper {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
  justify-content: center;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 16px;
  white-space: nowrap;
}

.pagination-text {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
}

.total-records {
  color: #909399;
  font-size: 12px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.pagination-component {
  margin: 0;
}

.pagination-jump {
  display: flex;
  align-items: center;
  gap: 8px;
}

.jump-label {
  color: #606266;
  font-size: 13px;
  white-space: nowrap;
}

.jump-input {
  width: 60px;
}

.jump-button {
  min-width: 60px;
}

/* 组织显示样式 */
.organization-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.organization-name {
  cursor: help;
  color: #303133;
  font-weight: 500;
}

.organization-level {
  color: #909399;
  font-size: 12px;
  font-weight: normal;
}

/* 操作按钮样式优化 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.action-button {
  min-width: 70px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-button .el-icon {
  font-size: 14px;
}

.action-button span {
  font-size: 13px;
}

.edit-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.view-button {
  background-color: #f4f4f5;
  border-color: #d3d4d6;
  color: #606266;
}

.view-button:hover {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.delete-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

/* 响应式布局适配 */
@media (max-width: 1200px) {
  .pagination-wrapper {
    gap: 16px;
  }

  .pagination-text {
    font-size: 12px;
  }

  .action-button {
    min-width: 60px;
    height: 28px;
  }

  .action-button span {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .header-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .pagination-wrapper {
    flex-direction: column;
    gap: 12px;
  }

  .pagination-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .pagination-controls {
    flex-direction: column;
    gap: 12px;
  }

  .pagination-text {
    font-size: 11px;
  }

  .jump-input {
    width: 50px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 6px;
  }

  .action-button {
    width: 100%;
    min-width: 80px;
  }
}

@media (max-width: 576px) {
  .header-pagination-center {
    padding: 8px 0;
  }

  .pagination-wrapper {
    gap: 8px;
  }

  .pagination-jump {
    flex-direction: column;
    gap: 6px;
    text-align: center;
  }

  .jump-input {
    width: 45px;
  }

  .smart-link-indicator {
    margin-left: 0;
    margin-top: 8px;
  }

  .organization-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 每页显示数量选择器样式 */
.page-size-selector {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.selector-label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
  white-space: nowrap;
}

.smart-link-indicator .el-tag {
  font-weight: 500;
  border-radius: 6px;
  padding: 4px 8px;
  animation: smartLinkPulse 2s ease-in-out infinite;
}

.smart-link-indicator .el-icon {
  margin-right: 4px;
  font-size: 12px;
}

/* 智能联动指示器动画 */
@keyframes smartLinkPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(103, 194, 58, 0.1);
  }
}

/* 筛选控件联动状态样式 */
.filter-controls .el-select.smart-linked {
  border-color: #67c23a !important;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2) !important;
}

.filter-controls .el-select.smart-linked .el-input__wrapper {
  border-color: #67c23a !important;
  box-shadow: 0 0 0 1px rgba(103, 194, 58, 0.2) inset !important;
}

.users-table-container {
  height: auto;
  min-height: 420px;
  padding: 0;
  overflow: visible;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pagination-container {
  border-top: 1px solid rgba(229, 231, 235, 0.6);
  padding-top: 20px;
  margin-top: 20px;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 0 0 16px 16px;
  margin-left: -24px;
  margin-right: -24px;
  padding-left: 24px;
  padding-right: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
  font-size: 18px;
}

.tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  margin: 2px 0;
  position: relative;
}

/* 可双击的节点类型添加悬停效果 */
.tree-node[data-type="organization"]:hover,
.tree-node[data-type="member_group"]:hover,
.tree-node[data-type="admin_group"]:hover,
.tree-node[data-type="normal_user_group"]:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 双击时的视觉反馈 */
.tree-node[data-type="organization"]:active,
.tree-node[data-type="member_group"]:active,
.tree-node[data-type="admin_group"]:active,
.tree-node[data-type="normal_user_group"]:active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
  transform: scale(0.98) translateX(4px);
}

.node-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.org-icon {
  color: #667eea;
  font-size: 18px;
  transition: all 0.3s ease;
}

.tree-node:hover .org-icon {
  transform: scale(1.1);
  color: #764ba2;
}

.node-label {
  font-size: 15px;
  font-weight: 600;
  transition: all 0.3s ease;
}

/* 组织节点样式 */
.tree-node[data-type="organization"] {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border-left: 4px solid #667eea;
  margin: 4px 0;
}

.tree-node[data-type="organization"] .node-label {
  font-size: 17px;
  font-weight: 700;
  color: #2c3e50;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 成员分组节点样式 */
.tree-node[data-type="member_group"] {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(100, 181, 246, 0.05) 100%);
  border-left: 3px solid #409eff;
}

.tree-node[data-type="member_group"] .node-label {
  font-size: 15px;
  font-weight: 600;
  color: #1976d2;
}

/* 管理员分组节点样式 */
.tree-node[data-type="admin_group"] {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255, 152, 0, 0.05) 100%);
  border-left: 3px solid #ffc107;
}

.tree-node[data-type="admin_group"] .node-label {
  font-size: 15px;
  font-weight: 600;
  color: #f57c00;
}

/* 普通用户分组节点样式 */
.tree-node[data-type="normal_user_group"] {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(139, 195, 74, 0.05) 100%);
  border-left: 3px solid #4caf50;
}

.tree-node[data-type="normal_user_group"] .node-label {
  font-size: 15px;
  font-weight: 600;
  color: #388e3c;
}

/* 用户节点样式 */
.tree-node[data-type="user"] {
  background: linear-gradient(135deg, rgba(158, 158, 158, 0.03) 0%, rgba(189, 189, 189, 0.03) 100%);
  border-left: 2px solid #bdbdbd;
}

.tree-node[data-type="user"] .node-label {
  font-size: 14px;
  font-weight: 500;
  color: #546e7a;
}

.tree-node[data-type="user"]:hover {
  background: linear-gradient(135deg, rgba(158, 158, 158, 0.08) 0%, rgba(189, 189, 189, 0.08) 100%);
  transform: translateX(2px);
}

.org-type {
  color: #78909c;
  font-size: 13px;
  font-weight: 500;
  background: rgba(120, 144, 156, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
}

.node-actions,
.user-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  opacity: 0;
  transition: all 0.3s ease;
}

.tree-node:hover .node-actions,
.tree-node:hover .user-actions {
  opacity: 1;
}

.node-actions .el-button,
.user-actions .el-button {
  border-radius: 8px !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  transition: all 0.3s ease !important;
}

.node-actions .el-button:hover,
.user-actions .el-button:hover {
  transform: scale(1.05) !important;
}

/* 成员分组信息样式 */
.member-group-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.member-group-info .el-tag {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
  border: 1px solid #90caf9;
  border-radius: 16px;
  padding: 4px 12px;
  font-weight: 600;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(21, 101, 192, 0.2);
}

/* 管理员分组信息样式 */
.admin-group-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.admin-group-info .el-tag {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  color: #e65100;
  border: 1px solid #ffcc02;
  border-radius: 16px;
  padding: 4px 12px;
  font-weight: 600;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(230, 81, 0, 0.2);
}

/* 普通用户分组信息样式 */
.normal-user-group-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.normal-user-group-info .el-tag {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  color: #2e7d32;
  border: 1px solid #81c784;
  border-radius: 16px;
  padding: 4px 12px;
  font-weight: 600;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(46, 125, 50, 0.2);
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 320px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-radius: 16px;
  border: 2px dashed rgba(148, 163, 184, 0.3);
}

.detail-section {
  margin-bottom: 28px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.5) 0%, rgba(241, 245, 249, 0.5) 100%);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s ease;
}

.detail-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.detail-section h4 {
  margin: 0 0 20px 0;
  color: #1e293b;
  font-size: 18px;
  font-weight: 700;
  border-bottom: 2px solid rgba(102, 126, 234, 0.2);
  padding-bottom: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.user-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 16px;
  min-width: 100px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-label {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 28px;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.role-tags {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.role-tag {
  margin-right: 0;
  border-radius: 16px !important;
  padding: 6px 16px !important;
  font-weight: 600 !important;
  font-size: 13px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.role-tag:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 树形结构优化 */
.tree-panel :deep(.el-tree-node__content) {
  height: 48px;
  padding: 0 12px;
  border-radius: 12px;
  margin: 3px 0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tree-panel :deep(.el-tree-node__content::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(102, 126, 234, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tree-panel :deep(.el-tree-node__content:hover::before) {
  opacity: 1;
}

.tree-panel :deep(.el-tree-node__content:hover) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.tree-panel :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
  border: 2px solid rgba(102, 126, 234, 0.3);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
}

/* 组织节点特殊样式 */
.tree-panel :deep(.el-tree-node__content[data-type="organization"]) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  border-left: 4px solid #667eea;
  font-weight: 700;
}

.tree-panel :deep(.el-tree-node__content[data-type="organization"]:hover) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
  transform: translateX(6px);
}

/* 成员分组节点样式 */
.tree-panel :deep(.el-tree-node__content[data-type="member_group"]) {
  margin-left: 12px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.06) 0%, rgba(100, 181, 246, 0.06) 100%);
  border-left: 3px solid #409eff;
  border-radius: 12px;
}

.tree-panel :deep(.el-tree-node__content[data-type="member_group"]:hover) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.12) 0%, rgba(100, 181, 246, 0.12) 100%);
}

/* 管理员分组节点样式 */
.tree-panel :deep(.el-tree-node__content[data-type="admin_group"]) {
  margin-left: 24px;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.06) 0%, rgba(255, 152, 0, 0.06) 100%);
  border-left: 3px solid #ffc107;
  border-radius: 12px;
}

.tree-panel :deep(.el-tree-node__content[data-type="admin_group"]:hover) {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.12) 0%, rgba(255, 152, 0, 0.12) 100%);
}

/* 普通用户分组节点样式 */
.tree-panel :deep(.el-tree-node__content[data-type="normal_user_group"]) {
  margin-left: 24px;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.06) 0%, rgba(139, 195, 74, 0.06) 100%);
  border-left: 3px solid #4caf50;
  border-radius: 12px;
}

.tree-panel :deep(.el-tree-node__content[data-type="normal_user_group"]:hover) {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.12) 0%, rgba(139, 195, 74, 0.12) 100%);
}

/* 用户节点样式 */
.tree-panel :deep(.el-tree-node__content[data-type="user"]) {
  margin-left: 36px;
  background: linear-gradient(135deg, rgba(158, 158, 158, 0.04) 0%, rgba(189, 189, 189, 0.04) 100%);
  border-left: 2px solid #bdbdbd;
  border-radius: 12px;
}

.tree-panel :deep(.el-tree-node__content[data-type="user"]:hover) {
  background: linear-gradient(135deg, rgba(158, 158, 158, 0.08) 0%, rgba(189, 189, 189, 0.08) 100%);
}

/* 展开/收起图标样式 */
.tree-panel :deep(.el-tree-node__expand-icon) {
  color: #667eea;
  font-size: 16px;
  transition: all 0.3s ease;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tree-panel :deep(.el-tree-node__expand-icon:hover) {
  color: #764ba2;
  background: rgba(102, 126, 234, 0.1);
  transform: scale(1.2);
}

.tree-panel :deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

/* 拖拽时的样式 */
.tree-panel :deep(.el-tree-node.is-drop-inner) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
  border: 2px dashed #667eea;
  border-radius: 12px;
}

/* 表格样式优化 */
.users-table-container :deep(.el-table) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.users-table-container :deep(.el-table__header) {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
}

.users-table-container :deep(.el-table__header th) {
  background: transparent;
  color: #1e293b;
  font-weight: 700;
  font-size: 14px;
  border-bottom: 2px solid rgba(102, 126, 234, 0.2);
  padding: 16px 12px;
}

.users-table-container :deep(.el-table__row) {
  transition: all 0.3s ease;
  border-radius: 8px;
}

.users-table-container :deep(.el-table__row:hover) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.users-table-container :deep(.el-table__row td) {
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  padding: 16px 12px;
  transition: all 0.3s ease;
}

/* 标签样式优化 */
.users-table-container :deep(.el-tag) {
  border-radius: 16px;
  font-weight: 600;
  font-size: 12px;
  padding: 6px 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.users-table-container :deep(.el-tag:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 按钮样式优化 */
.users-table-container :deep(.el-button) {
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 6px 12px;
  font-size: 12px;
}

.users-table-container :deep(.el-button:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 分页样式优化 */
.pagination-container :deep(.el-pagination) {
  justify-content: center;
  padding: 20px 0;
}

.pagination-container :deep(.el-pagination .el-pager li) {
  border-radius: 10px;
  margin: 0 4px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.pagination-container :deep(.el-pagination .el-pager li:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pagination-container :deep(.el-pagination .el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.pagination-container :deep(.el-pagination .btn-prev),
.pagination-container :deep(.el-pagination .btn-next) {
  border-radius: 10px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.pagination-container :deep(.el-pagination .btn-prev:hover),
.pagination-container :deep(.el-pagination .btn-next:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 分页控件布局优化 */
.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.pagination-info {
  flex: 1;
  min-width: 200px;
}

.pagination-controls {
  flex: 0 0 auto;
}

.pagination-jump {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .pagination-wrapper {
    flex-direction: column;
    align-items: stretch;
  }

  .pagination-info,
  .pagination-controls,
  .pagination-jump {
    justify-content: center;
    text-align: center;
  }
}



/* 优化后的按钮布局样式 - 修复无限扩展问题 */
.header-controls-optimized {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
  max-width: 100%;
  overflow: hidden;
}

.pagination-section {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 180px;
  max-width: 250px;
}

.filter-section {
  flex: 1 1 auto;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  align-items: center;
  padding: 0 8px;
  min-width: 0;
  max-width: 100%;
}

.action-section {
  flex: 0 0 auto;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  min-width: 200px;
  max-width: 350px;
  justify-content: flex-end;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  flex-shrink: 0;
  max-width: 100%;
}

.control-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
  flex-shrink: 0;
}

.jump-group {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 2px 6px;
  background: #f5f7fa;
  max-width: 120px;
}

.search-group {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 2px;
  background: #f5f7fa;
  max-width: 160px;
}

.button-group {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 2px;
  background: #f5f7fa;
  max-width: 120px;
}

.specific-org-selector {
  margin-top: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

/* 响应式布局优化 - 防止无限扩展 */
@media (max-width: 1400px) {
  .header-controls-optimized {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .pagination-section,
  .filter-section,
  .action-section {
    justify-content: center;
    max-width: 100%;
    min-width: 0;
  }

  .filter-section {
    padding: 0;
    gap: 6px;
  }

  .action-section {
    min-width: auto;
    max-width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .filter-section,
  .action-section {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .control-group {
    justify-content: space-between;
    max-width: 100%;
  }

  .jump-group,
  .search-group,
  .button-group {
    justify-content: center;
    max-width: 100%;
  }

  .control-group .el-select,
  .control-group .el-input {
    max-width: 100px;
  }
}

/* 用户统计卡片样式 */
.user-stats-enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.stat-card.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.admin {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.normal {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-content {
  flex: 1;
}

.card-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.card-desc {
  font-size: 12px;
  color: #94a3b8;
}

.card-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  color: #667eea;
  font-size: 18px;
}

/* 操作指南卡片样式 */
.operation-guide-card {
  border: 2px dashed #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.operation-guide {
  text-align: center;
  padding: 40px 20px;
}

.guide-icon {
  margin-bottom: 20px;
  color: #667eea;
}

.operation-guide h3 {
  margin: 0 0 24px 0;
  color: #1e293b;
  font-size: 20px;
  font-weight: 600;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.guide-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  text-align: left;
}

.guide-item .el-icon {
  color: #667eea;
  font-size: 18px;
  flex-shrink: 0;
}

.guide-item span {
  color: #475569;
  font-size: 14px;
  line-height: 1.5;
}

.guide-note {
  max-width: 600px;
  margin: 0 auto;
}

.user-details-container {
  margin-top: 16px;
}

.tab-content {
  padding: 16px 0;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tab-header h4 {
  margin: 0;
  color: #1e293b;
  font-size: 16px;
  font-weight: 600;
}

/* 组织详情标签页样式 */
.org-detail-tabs {
  margin-top: 16px;
}

.org-detail-tabs .el-tabs__header {
  margin-bottom: 20px;
}

.org-detail-tabs .el-tabs__nav-wrap::after {
  background-color: #e4e7ed;
}

.org-detail-tabs .el-tabs__active-bar {
  background-color: #409eff;
}

.org-detail-tabs .el-tabs__item {
  font-weight: 500;
  color: #606266;
}

.org-detail-tabs .el-tabs__item.is-active {
  color: #409eff;
  font-weight: 600;
}

/* 用户列表内容样式 */
.user-list-content {
  padding: 0;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #e4e7ed;
}

.list-header h4 {
  margin: 0;
  color: #1e293b;
  font-size: 16px;
  font-weight: 600;
}

.list-actions {
  display: flex;
  gap: 8px;
}

.list-actions .el-button {
  border-radius: 6px;
}

/* 响应式设计增强 */
@media (max-width: 1200px) {
  .org-users-container {
    padding: 16px;
  }

  .tree-panel {
    width: 450px;
    min-width: 450px;
  }

  .tree-panel .el-card,
  .detail-panel .el-card {
    min-height: 500px;
    max-height: 700px;
  }
}

@media (max-width: 768px) {
  .org-users-container {
    padding: 12px;
  }

  .main-content {
    flex-direction: column;
    gap: 16px;
  }

  .tree-panel {
    width: 100%;
    min-width: 100%;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .tree-panel .el-card,
  .detail-panel .el-card {
    min-height: 400px;
    max-height: 600px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    display: flex;
    gap: 12px;
    justify-content: center;
  }

  .operation-guide {
    padding: 24px 16px;
  }

  .guide-content {
    gap: 12px;
  }

  .guide-item {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
}
</style>
