#!/usr/bin/env python3
"""
临时迁移脚本：添加USB拓扑字段
"""

import os
import sys
sys.path.append('/app')
from sqlalchemy import create_engine, text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_usb_topology_fields():
    try:
        # 使用容器内的数据库URL
        database_url = os.getenv('DATABASE_URL', '*********************************************/omnilink_main')
        engine = create_engine(database_url)
        logger.info('连接数据库...')
        
        with engine.connect() as conn:
            trans = conn.begin()
            
            try:
                # 检查字段是否已存在
                check_fields_sql = """
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'slave_servers' 
                    AND column_name IN ('hub_count', 'total_ports', 'occupied_ports', 'free_ports', 'device_count')
                """
                result = conn.execute(text(check_fields_sql))
                existing_fields = [row[0] for row in result.fetchall()]
                logger.info(f'现有字段: {existing_fields}')
                
                fields_to_add = []
                
                if 'device_count' not in existing_fields:
                    fields_to_add.append('ADD COLUMN device_count INTEGER DEFAULT 0')
                
                if 'hub_count' not in existing_fields:
                    fields_to_add.append('ADD COLUMN hub_count INTEGER DEFAULT 0')
                
                if 'total_ports' not in existing_fields:
                    fields_to_add.append('ADD COLUMN total_ports INTEGER DEFAULT 0')
                
                if 'occupied_ports' not in existing_fields:
                    fields_to_add.append('ADD COLUMN occupied_ports INTEGER DEFAULT 0')
                
                if 'free_ports' not in existing_fields:
                    fields_to_add.append('ADD COLUMN free_ports INTEGER DEFAULT 0')
                
                if fields_to_add:
                    alter_sql = f"ALTER TABLE slave_servers {', '.join(fields_to_add)}"
                    logger.info(f'执行SQL: {alter_sql}')
                    conn.execute(text(alter_sql))
                    logger.info(f'已添加字段: {[f.split()[-2] for f in fields_to_add]}')
                else:
                    logger.info('所有USB拓扑字段已存在，跳过添加')
                
                # 添加注释
                comments = [
                    "COMMENT ON COLUMN slave_servers.device_count IS 'USB设备总数量'",
                    "COMMENT ON COLUMN slave_servers.hub_count IS 'USB Hub数量'",
                    "COMMENT ON COLUMN slave_servers.total_ports IS '总USB端口数量'",
                    "COMMENT ON COLUMN slave_servers.occupied_ports IS '已占用USB端口数量'",
                    "COMMENT ON COLUMN slave_servers.free_ports IS '空闲USB端口数量'"
                ]
                
                for comment_sql in comments:
                    try:
                        conn.execute(text(comment_sql))
                    except Exception as e:
                        logger.warning(f"添加注释失败: {e}")
                
                trans.commit()
                logger.info('🎉 USB拓扑字段迁移完成！')
                
                # 验证迁移结果
                result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_name = 'slave_servers' 
                    AND column_name IN ('device_count', 'hub_count', 'total_ports', 'occupied_ports', 'free_ports')
                    ORDER BY column_name
                """))
                
                logger.info('📋 迁移后的字段信息:')
                for row in result.fetchall():
                    logger.info(f'  - {row[0]}: {row[1]} (nullable: {row[2]}, default: {row[3]})')
                
                return True
                
            except Exception as e:
                trans.rollback()
                logger.error(f'迁移失败，已回滚: {e}')
                return False
                
    except Exception as e:
        logger.error(f'数据库连接失败: {e}')
        return False

if __name__ == "__main__":
    # 执行迁移
    success = add_usb_topology_fields()
    print(f'迁移结果: {success}')
