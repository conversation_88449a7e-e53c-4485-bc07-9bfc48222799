-- 创建申请处理系统表

-- 1. 创建申请类型表
CREATE TABLE IF NOT EXISTS application_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 创建申请请求表
CREATE TABLE IF NOT EXISTS application_requests (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) DEFAULT 'normal',
    status VARCHAR(20) DEFAULT 'pending',
    
    -- 申请人信息
    applicant_id INTEGER NOT NULL REFERENCES users(id),
    applicant_organization_id INTEGER REFERENCES organizations(id),
    
    -- 处理人信息
    processor_id INTEGER REFERENCES users(id),
    target_processor_id INTEGER REFERENCES users(id),
    
    -- 时间信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    
    -- 处理结果
    process_comment TEXT,
    attachments JSONB
);

-- 3. 插入默认申请类型
INSERT INTO application_types (name, code, description, sort_order) VALUES
('权限申请', 'permission_request', '申请获取特定权限或角色', 1),
('组织调整', 'organization_change', '申请调整组织归属', 2),
('设备申请', 'device_request', '申请使用或分配设备', 3),
('账户管理', 'account_management', '申请创建、修改或删除账户', 4),
('系统配置', 'system_config', '申请修改系统配置', 5),
('其他申请', 'other_request', '其他类型的申请', 99);

-- 4. 创建索引
CREATE INDEX IF NOT EXISTS idx_application_requests_applicant ON application_requests(applicant_id);
CREATE INDEX IF NOT EXISTS idx_application_requests_processor ON application_requests(processor_id);
CREATE INDEX IF NOT EXISTS idx_application_requests_target_processor ON application_requests(target_processor_id);
CREATE INDEX IF NOT EXISTS idx_application_requests_status ON application_requests(status);
CREATE INDEX IF NOT EXISTS idx_application_requests_type ON application_requests(type);
CREATE INDEX IF NOT EXISTS idx_application_requests_created_at ON application_requests(created_at);

-- 5. 添加更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_application_requests_updated_at 
    BEFORE UPDATE ON application_requests 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_application_types_updated_at 
    BEFORE UPDATE ON application_types 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
