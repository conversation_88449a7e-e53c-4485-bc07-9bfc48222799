#!/usr/bin/env python3

def fix_js_file(file_path):
    """最终修复JS文件"""
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的字符串替换
        old_str = 'throw new Error("Network security warning - request allowed")'
        new_str = 'console.warn("Network security warning - request allowed")'
        
        if old_str in content:
            content = content.replace(old_str, new_str)
            print(f'找到并替换了: {old_str}')
        else:
            print(f'未找到目标字符串: {old_str}')
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f'修复完成: {file_path}')
        return True
        
    except Exception as e:
        print(f'修复失败: {e}')
        return False

if __name__ == "__main__":
    files = [
        '/app/static/assets/js/index-BpfOHM6c.js',
        '/app/static/assets/js/index-BYI7t6F0.js',
        '/app/static/assets/js/index-CEdtHN8i.js',
        '/app/static/assets/js/index-DRBP03-n.js'
    ]
    
    for file_path in files:
        fix_js_file(file_path)
