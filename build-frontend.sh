#!/bin/bash

# ================================
# OmniLink 前端构建脚本
# 版本: 2.0
# 创建日期: 2025-01-07
# ================================

set -e

echo "🚀 开始构建 OmniLink 前端..."

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 进入前端目录
cd main_server/frontend

echo "📦 安装前端依赖..."
npm install

echo "🔨 构建前端项目..."
npm run build

# 创建静态文件目录
echo "📁 创建静态文件目录..."
mkdir -p ../backend/static

# 复制构建文件到后端静态目录
echo "📋 复制构建文件..."
cp -r dist/* ../backend/static/

echo "✅ 前端构建完成！"
echo "📍 静态文件已复制到: main_server/backend/static/"

# 返回根目录
cd ../..

echo "🎉 前端构建流程完成！"
